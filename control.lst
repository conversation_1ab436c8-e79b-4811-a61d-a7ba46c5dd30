                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_hlc1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=control.c -o gh_hlc1.o -list=control.lst C:\Users\<USER>\AppData\Local\Temp\gh_hlc1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_hlc1.s
Source File: control.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile control.c -o

                      10 ;		control.o

                      11 ;Source File:   control.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Oct 07 09:39:24 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "control.h"


                      21 ;2: 


                      22 ;3: #include "bufViewBER.h"


                      23 ;4: #include "BaseAsnTypes.h"


                      24 ;5: #include "iedmodel.h"


                      25 ;6: #include "mms_data.h"


                      26 ;7: #include "pwin_access.h"


                      27 ;8: #include "infoReport.h"


                      28 ;9: #include "mms.h"


                      29 ;10: #include "iedTree/iedEntity.h"


                      30 ;11: #include "iedTree/iedObjects.h"


                      31 ;12: 


                      32 ;13: #include <Clib.h>


                      33 ;14: #include <stdbool.h>


                      34 ;15: #include <stddef.h>


                      35 ;16: 


                      36 ;17: #define MAX_CTRL_OBJ_COUNT 40


                      37 ;18: 


                      38 ;19: static size_t ctrlObjCnt =0;


                      39 ;20: IEDEntity ctrlObjects[MAX_CTRL_OBJ_COUNT];


                      40 ;21: 


                      41 ;22: 


                      42 ;23: // Имя объекта управления (скорее всего pos) при записи


                      43 ;24: // используется для формирования CommandTermination information report


                      44 ;25: uint8_t cmdTermObjNameBuf[MAX_OBJECT_REFERENCE];


                      45 ;26: //Используются из потока отчетов


                      46 ;27: 


                      47 ;28: //Буфера для посылки CommandTermination InformationReport


                      48 ;29: //Используются из потока отчетов


                      49 ;30: static uint8_t cmdTermDataBuf[DEFAULT_REPORT_BUFFER_SIZE];


                      50 ;31: static uint8_t cmdTermMmsBuf[DEFAULT_REPORT_BUFFER_SIZE];



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_hlc1.s
                      51 ;32: static uint8_t cmdTermPresentationBuf[DEFAULT_REPORT_BUFFER_SIZE];


                      52 ;33: 


                      53 ;34: bool Control_registerCtrlObj(IEDEntity entity)


                      54 	.text

                      55 	.align	4

                      56 Control_registerCtrlObj::

                      57 ;35: {


                      58 

                      59 ;36:     if(ctrlObjCnt >= MAX_CTRL_OBJ_COUNT)


                      60 

00000000 e59f2310*    61 	ldr	r2,.L52

00000004 e5921000     62 	ldr	r1,[r2]

00000008 e3510028     63 	cmp	r1,40

                      64 ;37:     {


                      65 

                      66 ;38:         return false;


                      67 

0000000c 359f3308*    68 	ldrlo	r3,.L53

00000010 23a00000     69 	movhs	r0,0

                      70 ;39:     }


                      71 ;40:     ctrlObjects[ctrlObjCnt] = entity;


                      72 

00000014 37830101     73 	strlo	r0,[r3,r1 lsl 2]

                      74 ;41:     ctrlObjCnt++;


                      75 

00000018 32810001     76 	addlo	r0,r1,1

0000001c 35820000     77 	strlo	r0,[r2]

                      78 ;42:     return true;


                      79 

00000020 33a00001     80 	movlo	r0,1

00000024 e12fff1e*    81 	ret	

                      82 	.endf	Control_registerCtrlObj

                      83 	.align	4

                      84 

                      85 ;entity	r0	param

                      86 

                      87 	.section ".bss","awb"

                      88 .L38:

                      89 	.data

                      90 .L39:

00000000 00000000     91 ctrlObjCnt:	.data.b	0,0,0,0

                      92 	.type	ctrlObjCnt,$object

                      93 	.size	ctrlObjCnt,4

                      94 	.section ".bss","awb"

00000000 00000000     95 cmdTermDataBuf:	.space	8192

00000004 00000000 
00000008 00000000 
0000000c 00000000 
00000010 00000000 
00000014 00000000 
00000018 00000000 
0000001c 00000000 
00000020 00000000 
00000024 00000000 
00000028 00000000 
0000002c 00000000 
00000030 00000000 
00000034 00000000 
00000038 00000000 
0000003c 00000000 
00000040 00000000 

                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_hlc1.s
00000044 00000000 
00000048 00000000 
0000004c 00000000 
00000050 00000000 
00000054 00000000 
00000058 00000000 
0000005c 00000000 
00000060 00000000 
00000064 00000000 
00000068 00000000 
0000006c 00000000 
00000070 00000000 
00000074 00000000 
00000078 00000000 
0000007c 00000000 
00000080 00000000 
00000084 00000000 
00000088 00000000 
0000008c 00000000 
00000090 00000000 
00000094 00000000 
00000098 00000000 
0000009c 00000000 
000000a0 00000000 
000000a4 00000000 
000000a8 00000000 
000000ac 00000000 
000000b0 00000000 
000000b4 00000000 
000000b8 00000000 
000000bc 00000000 
000000c0 00000000 
000000c4 00000000 
000000c8 00000000 
000000cc 00000000 
000000d0 00000000 
000000d4 00000000 
000000d8 00000000 
000000dc 00000000 
000000e0 00000000 
000000e4 00000000 
000000e8 00000000 
000000ec 00000000 
000000f0 00000000 
000000f4 00000000 
000000f8 00000000 
000000fc 00000000 
00002000 00000000     96 cmdTermMmsBuf:	.space	8192

00002004 00000000 
00002008 00000000 
0000200c 00000000 
00002010 00000000 
00002014 00000000 
00002018 00000000 
0000201c 00000000 
00002020 00000000 
00002024 00000000 
00002028 00000000 
0000202c 00000000 
00002030 00000000 
00002034 00000000 

                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_hlc1.s
00002038 00000000 
0000203c 00000000 
00002040 00000000 
00002044 00000000 
00002048 00000000 
0000204c 00000000 
00002050 00000000 
00002054 00000000 
00002058 00000000 
0000205c 00000000 
00002060 00000000 
00002064 00000000 
00002068 00000000 
0000206c 00000000 
00002070 00000000 
00002074 00000000 
00002078 00000000 
0000207c 00000000 
00002080 00000000 
00002084 00000000 
00002088 00000000 
0000208c 00000000 
00002090 00000000 
00002094 00000000 
00002098 00000000 
0000209c 00000000 
000020a0 00000000 
000020a4 00000000 
000020a8 00000000 
000020ac 00000000 
000020b0 00000000 
000020b4 00000000 
000020b8 00000000 
000020bc 00000000 
000020c0 00000000 
000020c4 00000000 
000020c8 00000000 
000020cc 00000000 
000020d0 00000000 
000020d4 00000000 
000020d8 00000000 
000020dc 00000000 
000020e0 00000000 
000020e4 00000000 
000020e8 00000000 
000020ec 00000000 
000020f0 00000000 
000020f4 00000000 
000020f8 00000000 
000020fc 00000000 
00004000 00000000     97 cmdTermPresentationBuf:	.space	8192

00004004 00000000 
00004008 00000000 
0000400c 00000000 
00004010 00000000 
00004014 00000000 
00004018 00000000 
0000401c 00000000 
00004020 00000000 
00004024 00000000 
00004028 00000000 

                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_hlc1.s
0000402c 00000000 
00004030 00000000 
00004034 00000000 
00004038 00000000 
0000403c 00000000 
00004040 00000000 
00004044 00000000 
00004048 00000000 
0000404c 00000000 
00004050 00000000 
00004054 00000000 
00004058 00000000 
0000405c 00000000 
00004060 00000000 
00004064 00000000 
00004068 00000000 
0000406c 00000000 
00004070 00000000 
00004074 00000000 
00004078 00000000 
0000407c 00000000 
00004080 00000000 
00004084 00000000 
00004088 00000000 
0000408c 00000000 
00004090 00000000 
00004094 00000000 
00004098 00000000 
0000409c 00000000 
000040a0 00000000 
000040a4 00000000 
000040a8 00000000 
000040ac 00000000 
000040b0 00000000 
000040b4 00000000 
000040b8 00000000 
000040bc 00000000 
000040c0 00000000 
000040c4 00000000 
000040c8 00000000 
000040cc 00000000 
000040d0 00000000 
000040d4 00000000 
000040d8 00000000 
000040dc 00000000 
000040e0 00000000 
000040e4 00000000 
000040e8 00000000 
000040ec 00000000 
000040f0 00000000 
000040f4 00000000 
000040f8 00000000 
000040fc 00000000 
                      98 	.data

                      99 	.text

                     100 

                     101 ;43: }


                     102 

                     103 ;44: 


                     104 ;45: void Control_processCtrlObjects(void)


                     105 	.align	4


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_hlc1.s
                     106 	.align	4

                     107 Control_processCtrlObjects::

00000028 e92d4070    108 	stmfd	[sp]!,{r4-r6,lr}

0000002c e59f62e4*   109 	ldr	r6,.L52

                     110 ;46: {


                     111 

                     112 ;47:     size_t i;


                     113 ;48:     for(i = 0; i < ctrlObjCnt; i++)


                     114 

00000030 e59f52e4*   115 	ldr	r5,.L53

00000034 e5960000    116 	ldr	r0,[r6]

00000038 e3a04000    117 	mov	r4,0

0000003c e1540000    118 	cmp	r4,r0

00000040 2a000005    119 	bhs	.L54

                     120 .L58:

                     121 ;49:     {


                     122 

                     123 ;50:         IEDControlDA_checkTerminate(ctrlObjects[i]);


                     124 

00000044 e4950004    125 	ldr	r0,[r5],4

00000048 eb000000*   126 	bl	IEDControlDA_checkTerminate

0000004c e5960000    127 	ldr	r0,[r6]

00000050 e2844001    128 	add	r4,r4,1

00000054 e1540000    129 	cmp	r4,r0

00000058 3afffff9    130 	blo	.L58

                     131 .L54:

0000005c e8bd8070    132 	ldmfd	[sp]!,{r4-r6,pc}

                     133 	.endf	Control_processCtrlObjects

                     134 	.align	4

                     135 ;i	r4	local

                     136 

                     137 	.data

                     138 	.text

                     139 

                     140 ;51:     }


                     141 ;52: }


                     142 

                     143 ;53: 


                     144 ;54: void Control_disableWaitingObjects(void)


                     145 	.align	4

                     146 	.align	4

                     147 Control_disableWaitingObjects::

00000060 e92d4070    148 	stmfd	[sp]!,{r4-r6,lr}

00000064 e59f62ac*   149 	ldr	r6,.L52

                     150 ;55: {


                     151 

                     152 ;56:     size_t i;


                     153 ;57:     for(i = 0; i < ctrlObjCnt; i++)


                     154 

00000068 e59f52ac*   155 	ldr	r5,.L53

0000006c e5960000    156 	ldr	r0,[r6]

00000070 e3a04000    157 	mov	r4,0

00000074 e1540000    158 	cmp	r4,r0

00000078 2a000005    159 	bhs	.L95

                     160 .L99:

                     161 ;58:     {


                     162 

                     163 ;59:         IEDControlDA_disconnect(ctrlObjects[i]);


                     164 

0000007c e4950004    165 	ldr	r0,[r5],4

00000080 eb000000*   166 	bl	IEDControlDA_disconnect


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_hlc1.s
00000084 e5960000    167 	ldr	r0,[r6]

00000088 e2844001    168 	add	r4,r4,1

0000008c e1540000    169 	cmp	r4,r0

00000090 3afffff9    170 	blo	.L99

                     171 .L95:

00000094 e8bd8070    172 	ldmfd	[sp]!,{r4-r6,pc}

                     173 	.endf	Control_disableWaitingObjects

                     174 	.align	4

                     175 ;i	r4	local

                     176 

                     177 	.data

                     178 	.text

                     179 

                     180 ;60:     }


                     181 ;61: }


                     182 

                     183 ;62: 


                     184 ;63: bool Control_sendServiceErrorReport(IsoConnection* isoConn,


                     185 	.align	4

                     186 	.align	4

                     187 Control_sendServiceErrorReport::

00000098 e92d44f0    188 	stmfd	[sp]!,{r4-r7,r10,lr}

                     189 ;64:                                     IEDEntity controlObject, uint8_t addCause)


                     190 ;65: {


                     191 

                     192 ;66:     MmsConnection* mmsConn = &isoConn->mmsConn;


                     193 

0000009c e1a0a002    194 	mov	r10,r2

000000a0 e24dd038    195 	sub	sp,sp,56

000000a4 e1a06001    196 	mov	r6,r1

000000a8 e1a05000    197 	mov	r5,r0

000000ac e3a00f8f    198 	mov	r0,0x023c

000000b0 e2600a49    199 	rsb	r0,r0,73<<12

000000b4 e0854000    200 	add	r4,r5,r0

000000b8 e5960000    201 	ldr	r0,[r6]

000000bc e1a07004    202 	mov	r7,r4

                     203 ;67:     BufferView wrBuf;


                     204 ;68:     StringView cntrlObjName;


                     205 ;69:     BufferView cntrlObjNameBuf;


                     206 ;70:     StringView orIdent;


                     207 ;71:     int32_t orCat;


                     208 ;72: 


                     209 ;73:     //Имя объекта управления


                     210 ;74:     if(controlObject->parent == NULL)


                     211 

000000c0 e3500000    212 	cmp	r0,0

000000c4 0a000025    213 	beq	.L158

                     214 ;75:     {


                     215 

                     216 ;76:         ERROR_REPORT("Invalid parent");


                     217 ;77:         return false;


                     218 

                     219 ;78:     }


                     220 ;79: 


                     221 ;80: 


                     222 ;81:     BufferView_init(&cntrlObjNameBuf, mmsConn->wrCrtlObjNameBuf,


                     223 

000000c8 e2870c61    224 	add	r0,r7,97<<8

000000cc e2801054    225 	add	r1,r0,84

000000d0 e28d0018    226 	add	r0,sp,24

000000d4 e3a03000    227 	mov	r3,0


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_hlc1.s
000000d8 e3a02081    228 	mov	r2,129

000000dc eb000000*   229 	bl	BufferView_init

                     230 ;82:                     sizeof(mmsConn->wrCrtlObjNameBuf), 0);


                     231 ;83: 


                     232 ;84:     if(!IEDEntity_getFullName(controlObject->parent, &cntrlObjNameBuf  ))


                     233 

000000e0 e5960000    234 	ldr	r0,[r6]

000000e4 e28d1018    235 	add	r1,sp,24

000000e8 eb000000*   236 	bl	IEDEntity_getFullName

000000ec e3500000    237 	cmp	r0,0

000000f0 0a00001a    238 	beq	.L158

                     239 ;85:     {


                     240 

                     241 ;86:         return false;


                     242 

                     243 ;87:     }


                     244 ;88: 


                     245 ;89:     //Получаем имя объекта из буфера


                     246 ;90:     StringView_init(&cntrlObjName, (const char*)cntrlObjNameBuf.p, cntrlObjNameBuf.pos);


                     247 

000000f4 e59d201c    248 	ldr	r2,[sp,28]

000000f8 e59d1018    249 	ldr	r1,[sp,24]

000000fc e28d0024    250 	add	r0,sp,36

00000100 eb000000*   251 	bl	StringView_init

                     252 ;91: 


                     253 ;92:     BufferView_init(&wrBuf, mmsConn->infoReportDataBuf,


                     254 

00000104 e2871f51    255 	add	r1,r7,0x0144

00000108 e28d002c    256 	add	r0,sp,44

0000010c e3a03000    257 	mov	r3,0

00000110 e3a02d80    258 	mov	r2,1<<13

00000114 eb000000*   259 	bl	BufferView_init

                     260 ;93:                     sizeof(mmsConn->infoReportDataBuf), 0);


                     261 ;94: 


                     262 ;95:     if(!IEDControlDA_getOrIdent(controlObject, &orIdent))


                     263 

00000118 e28d1010    264 	add	r1,sp,16

0000011c e1a00006    265 	mov	r0,r6

00000120 eb000000*   266 	bl	IEDControlDA_getOrIdent

00000124 e3500000    267 	cmp	r0,0

00000128 0a00000c    268 	beq	.L158

                     269 ;96:     {


                     270 

                     271 ;97:         ERROR_REPORT("Unable to get orIdent");


                     272 ;98:         return false;


                     273 

                     274 ;99:     }


                     275 ;100: 


                     276 ;101:     if(!IEDControlDA_getOrCat(controlObject, &orCat))


                     277 

0000012c e28d100c    278 	add	r1,sp,12

00000130 e1a00006    279 	mov	r0,r6

00000134 eb000000*   280 	bl	IEDControlDA_getOrCat

00000138 e3500000    281 	cmp	r0,0

0000013c 0a000007    282 	beq	.L158

                     283 ;102:     {


                     284 

                     285 ;103:         ERROR_REPORT("Unable to get orCat");


                     286 ;104:         return false;


                     287 

                     288 ;105:     }



                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_hlc1.s
                     289 ;106: 


                     290 ;107:     if(!InfoReport_createLastApplErrorReport( &wrBuf,


                     291 

00000140 e3a02000    292 	mov	r2,0

00000144 e28d0010    293 	add	r0,sp,16

00000148 e88d0405    294 	stmea	[sp],{r0,r2,r10}

0000014c e5dd300c    295 	ldrb	r3,[sp,12]

00000150 e28d1024    296 	add	r1,sp,36

00000154 e28d002c    297 	add	r0,sp,44

00000158 eb000000*   298 	bl	InfoReport_createLastApplErrorReport

0000015c e3500000    299 	cmp	r0,0

                     300 .L158:

                     301 ;108:                                           &cntrlObjName, 0, orCat, &orIdent, 0, addCause))


                     302 ;109:     {


                     303 

                     304 ;110:         return false;


                     305 

00000160 03a00000    306 	moveq	r0,0

00000164 0a000009    307 	beq	.L143

                     308 .L157:

                     309 ;111:     }


                     310 ;112: 


                     311 ;113:     InfoReport_send(isoConn, wrBuf.p, wrBuf.pos,


                     312 

00000168 e2840c41    313 	add	r0,r4,65<<8

0000016c e2800044    314 	add	r0,r0,68

00000170 e58d0000    315 	str	r0,[sp]

00000174 e2840d84    316 	add	r0,r4,33<<8

00000178 e2803044    317 	add	r3,r0,68

0000017c e59d2030    318 	ldr	r2,[sp,48]

00000180 e59d102c    319 	ldr	r1,[sp,44]

00000184 e1a00005    320 	mov	r0,r5

00000188 eb000000*   321 	bl	InfoReport_send

                     322 ;114:                     isoConn->mmsConn.infoReportBuf,


                     323 ;115:                     isoConn->mmsConn.infoReportPresentationBuf);


                     324 ;116:     return true;


                     325 

0000018c e3a00001    326 	mov	r0,1

                     327 .L143:

00000190 e28dd038    328 	add	sp,sp,56

00000194 e8bd84f0    329 	ldmfd	[sp]!,{r4-r7,r10,pc}

                     330 	.endf	Control_sendServiceErrorReport

                     331 	.align	4

                     332 ;mmsConn	r7	local

                     333 ;wrBuf	[sp,44]	local

                     334 ;cntrlObjName	[sp,36]	local

                     335 ;cntrlObjNameBuf	[sp,24]	local

                     336 ;orIdent	[sp,16]	local

                     337 ;orCat	[sp,12]	local

                     338 

                     339 ;isoConn	r5	param

                     340 ;controlObject	r6	param

                     341 ;addCause	r10	param

                     342 

                     343 	.section ".bss","awb"

                     344 .L234:

                     345 	.data

                     346 	.text

                     347 

                     348 ;117: }


                     349 


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_hlc1.s
                     350 ;118: 


                     351 ;119: //функция вызывается из потока отчётов, поэтому использует глобальные


                     352 ;120: //буфера


                     353 ;121: bool Control_sendPositiveCmdTermReport(IsoConnection* isoConn,


                     354 	.align	4

                     355 	.align	4

                     356 Control_sendPositiveCmdTermReport::

00000198 e92d4070    357 	stmfd	[sp]!,{r4-r6,lr}

                     358 ;122:                                     IEDEntity controlObject)


                     359 ;123: {


                     360 

                     361 ;124:     StringView cntrlObjItemId;


                     362 ;125:     BufferView cntrlObjItemIdBuf;


                     363 ;126:     StringView* cntrlObjDomainId;


                     364 ;127:     BufferView wrBuf;


                     365 ;128: 


                     366 ;129: 


                     367 ;130:     //Получаем itemId


                     368 ;131:     BufferView_init(&cntrlObjItemIdBuf, cmdTermObjNameBuf,


                     369 

0000019c e59f417c*   370 	ldr	r4,.L340

000001a0 e24dd028    371 	sub	sp,sp,40

000001a4 e1a06000    372 	mov	r6,r0

000001a8 e1a05001    373 	mov	r5,r1

000001ac e59f1170*   374 	ldr	r1,.L341

000001b0 e28d0014    375 	add	r0,sp,20

000001b4 e3a03000    376 	mov	r3,0

000001b8 e3a02081    377 	mov	r2,129

000001bc eb000000*   378 	bl	BufferView_init

                     379 ;132:                     sizeof(cmdTermObjNameBuf), 0);


                     380 ;133: 


                     381 ;134:     if(!IEDEntity_getFullItemId(controlObject, &cntrlObjItemIdBuf ))


                     382 

000001c0 e28d1014    383 	add	r1,sp,20

000001c4 e1a00005    384 	mov	r0,r5

000001c8 eb000000*   385 	bl	IEDEntity_getFullItemId

000001cc e3500000    386 	cmp	r0,0

000001d0 0a000013    387 	beq	.L262

                     388 ;135:     {


                     389 

                     390 ;136:         return false;


                     391 

                     392 ;137:     }


                     393 ;138: 


                     394 ;139:     //Получаем itemId объекта из буфера


                     395 ;140:     StringView_init(&cntrlObjItemId, (const char*)cntrlObjItemIdBuf.p, cntrlObjItemIdBuf.pos);


                     396 

000001d4 e59d2018    397 	ldr	r2,[sp,24]

000001d8 e59d1014    398 	ldr	r1,[sp,20]

000001dc e28d0020    399 	add	r0,sp,32

000001e0 eb000000*   400 	bl	StringView_init

                     401 ;141: 


                     402 ;142:     //Получаем domainId


                     403 ;143:     if(!IEDEntity_getDomainId(controlObject, &cntrlObjDomainId ))


                     404 

000001e4 e28d1004    405 	add	r1,sp,4

000001e8 e1a00005    406 	mov	r0,r5

000001ec eb000000*   407 	bl	IEDEntity_getDomainId

000001f0 e3500000    408 	cmp	r0,0

000001f4 0a00000a    409 	beq	.L262

                     410 ;144:     {



                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_hlc1.s
                     411 

                     412 ;145:         return false;


                     413 

                     414 ;146:     }


                     415 ;147: 


                     416 ;148:     BufferView_init(&wrBuf, cmdTermDataBuf, sizeof(cmdTermDataBuf), 0);


                     417 

000001f8 e1a01004    418 	mov	r1,r4

000001fc e28d0008    419 	add	r0,sp,8

00000200 e3a03000    420 	mov	r3,0

00000204 e3a02d80    421 	mov	r2,1<<13

00000208 eb000000*   422 	bl	BufferView_init

                     423 ;149: 


                     424 ;150:     if(!InfoReport_createPositiveCmdTermReport(controlObject, &wrBuf, cntrlObjDomainId,


                     425 

0000020c e28d3020    426 	add	r3,sp,32

00000210 e59d2004    427 	ldr	r2,[sp,4]

00000214 e28d1008    428 	add	r1,sp,8

00000218 e1a00005    429 	mov	r0,r5

0000021c eb000000*   430 	bl	InfoReport_createPositiveCmdTermReport

00000220 e3500000    431 	cmp	r0,0

                     432 .L262:

                     433 ;151:                                                &cntrlObjItemId))


                     434 ;152:     {


                     435 

                     436 ;153:         return false;


                     437 

00000224 03a00000    438 	moveq	r0,0

00000228 0a000007    439 	beq	.L253

                     440 .L261:

                     441 ;154:     }


                     442 ;155: 


                     443 ;156:     InfoReport_send(isoConn, wrBuf.p, wrBuf.pos, cmdTermMmsBuf,


                     444 

0000022c e59f00f4*   445 	ldr	r0,.L342

00000230 e59f30f4*   446 	ldr	r3,.L343

00000234 e58d0000    447 	str	r0,[sp]

00000238 e59d200c    448 	ldr	r2,[sp,12]

0000023c e59d1008    449 	ldr	r1,[sp,8]

00000240 e1a00006    450 	mov	r0,r6

00000244 eb000000*   451 	bl	InfoReport_send

                     452 ;157:                     cmdTermPresentationBuf);


                     453 ;158:     return true;


                     454 

00000248 e3a00001    455 	mov	r0,1

                     456 .L253:

0000024c e28dd028    457 	add	sp,sp,40

00000250 e8bd8070    458 	ldmfd	[sp]!,{r4-r6,pc}

                     459 	.endf	Control_sendPositiveCmdTermReport

                     460 	.align	4

                     461 ;cntrlObjItemId	[sp,32]	local

                     462 ;cntrlObjItemIdBuf	[sp,20]	local

                     463 ;cntrlObjDomainId	[sp,4]	local

                     464 ;wrBuf	[sp,8]	local

                     465 

                     466 ;isoConn	r6	param

                     467 ;controlObject	r5	param

                     468 

                     469 	.data

                     470 	.text

                     471 


                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_hlc1.s
                     472 ;159: }


                     473 

                     474 ;160: 


                     475 ;161: //функция вызывается из потока отчётов, поэтому использует глобальные


                     476 ;162: //буфера


                     477 ;163: bool Control_sendNegativeCmdTermReport(IsoConnection* isoConn,


                     478 	.align	4

                     479 	.align	4

                     480 Control_sendNegativeCmdTermReport::

00000254 e92d40f0    481 	stmfd	[sp]!,{r4-r7,lr}

                     482 ;164:                                     IEDEntity controlObject, uint8_t addCause)


                     483 ;165: {


                     484 

                     485 ;166:     StringView cntrlObjItemId;


                     486 ;167:     BufferView cntrlObjItemIdBuf;


                     487 ;168:     StringView* cntrlObjDomainId;


                     488 ;169:     BufferView wrBuf;


                     489 ;170: 


                     490 ;171:     //Получаем itemId


                     491 ;172:     BufferView_init(&cntrlObjItemIdBuf, cmdTermObjNameBuf,


                     492 

00000258 e59f40c0*   493 	ldr	r4,.L340

0000025c e24dd028    494 	sub	sp,sp,40

00000260 e1a06000    495 	mov	r6,r0

00000264 e1a05001    496 	mov	r5,r1

00000268 e59f10b4*   497 	ldr	r1,.L341

0000026c e28d0014    498 	add	r0,sp,20

00000270 e3a03000    499 	mov	r3,0

00000274 e1a07002    500 	mov	r7,r2

00000278 e3a02081    501 	mov	r2,129

0000027c eb000000*   502 	bl	BufferView_init

                     503 ;173:                     sizeof(cmdTermObjNameBuf), 0);


                     504 ;174: 


                     505 ;175:     if(!IEDEntity_getFullItemId(controlObject, &cntrlObjItemIdBuf ))


                     506 

00000280 e28d1014    507 	add	r1,sp,20

00000284 e1a00005    508 	mov	r0,r5

00000288 eb000000*   509 	bl	IEDEntity_getFullItemId

0000028c e3500000    510 	cmp	r0,0

00000290 0a000014    511 	beq	.L353

                     512 ;176:     {


                     513 

                     514 ;177:         return false;


                     515 

                     516 ;178:     }


                     517 ;179: 


                     518 ;180:     //Получаем itemId объекта из буфера


                     519 ;181:     StringView_init(&cntrlObjItemId, (const char*)cntrlObjItemIdBuf.p, cntrlObjItemIdBuf.pos);


                     520 

00000294 e59d2018    521 	ldr	r2,[sp,24]

00000298 e59d1014    522 	ldr	r1,[sp,20]

0000029c e28d0020    523 	add	r0,sp,32

000002a0 eb000000*   524 	bl	StringView_init

                     525 ;182: 


                     526 ;183:     //Получаем domainId


                     527 ;184:     if(!IEDEntity_getDomainId(controlObject, &cntrlObjDomainId ))


                     528 

000002a4 e28d1004    529 	add	r1,sp,4

000002a8 e1a00005    530 	mov	r0,r5

000002ac eb000000*   531 	bl	IEDEntity_getDomainId

000002b0 e3500000    532 	cmp	r0,0


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_hlc1.s
000002b4 0a00000b    533 	beq	.L353

                     534 ;185:     {


                     535 

                     536 ;186:         return false;


                     537 

                     538 ;187:     }


                     539 ;188: 


                     540 ;189:     BufferView_init(&wrBuf, cmdTermDataBuf, sizeof(cmdTermDataBuf), 0);


                     541 

000002b8 e1a01004    542 	mov	r1,r4

000002bc e28d0008    543 	add	r0,sp,8

000002c0 e3a03000    544 	mov	r3,0

000002c4 e3a02d80    545 	mov	r2,1<<13

000002c8 eb000000*   546 	bl	BufferView_init

                     547 ;190: 


                     548 ;191:     if(!InfoReport_createNegativeCmdTermReport(controlObject, &wrBuf,


                     549 

000002cc e28d3020    550 	add	r3,sp,32

000002d0 e58d7000    551 	str	r7,[sp]

000002d4 e59d2004    552 	ldr	r2,[sp,4]

000002d8 e28d1008    553 	add	r1,sp,8

000002dc e1a00005    554 	mov	r0,r5

000002e0 eb000000*   555 	bl	InfoReport_createNegativeCmdTermReport

000002e4 e3500000    556 	cmp	r0,0

                     557 .L353:

                     558 ;192:                                                cntrlObjDomainId, &cntrlObjItemId, addCause))


                     559 ;193:     {


                     560 

                     561 ;194:         return false;


                     562 

000002e8 03a00000    563 	moveq	r0,0

000002ec 0a000007    564 	beq	.L344

                     565 .L352:

                     566 ;195:     }


                     567 ;196:     InfoReport_send(isoConn, wrBuf.p, wrBuf.pos, cmdTermMmsBuf,


                     568 

000002f0 e59f0030*   569 	ldr	r0,.L342

000002f4 e59f3030*   570 	ldr	r3,.L343

000002f8 e58d0000    571 	str	r0,[sp]

000002fc e59d200c    572 	ldr	r2,[sp,12]

00000300 e59d1008    573 	ldr	r1,[sp,8]

00000304 e1a00006    574 	mov	r0,r6

00000308 eb000000*   575 	bl	InfoReport_send

                     576 ;197:                     cmdTermPresentationBuf);


                     577 ;198:     return true;


                     578 

0000030c e3a00001    579 	mov	r0,1

                     580 .L344:

00000310 e28dd028    581 	add	sp,sp,40

00000314 e8bd80f0    582 	ldmfd	[sp]!,{r4-r7,pc}

                     583 	.endf	Control_sendNegativeCmdTermReport

                     584 	.align	4

                     585 ;cntrlObjItemId	[sp,32]	local

                     586 ;cntrlObjItemIdBuf	[sp,20]	local

                     587 ;cntrlObjDomainId	[sp,4]	local

                     588 ;wrBuf	[sp,8]	local

                     589 

                     590 ;isoConn	r6	param

                     591 ;controlObject	r5	param

                     592 ;addCause	r7	param

                     593 


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_hlc1.s
                     594 	.data

                     595 	.text

                     596 

                     597 ;199: }


                     598 	.align	4

                     599 .L52:

00000318 00000000*   600 	.data.w	.L39

                     601 	.type	.L52,$object

                     602 	.size	.L52,4

                     603 

                     604 .L53:

0000031c 00000000*   605 	.data.w	ctrlObjects

                     606 	.type	.L53,$object

                     607 	.size	.L53,4

                     608 

                     609 .L340:

00000320 00000000*   610 	.data.w	cmdTermDataBuf

                     611 	.type	.L340,$object

                     612 	.size	.L340,4

                     613 

                     614 .L341:

00000324 00000000*   615 	.data.w	cmdTermObjNameBuf

                     616 	.type	.L341,$object

                     617 	.size	.L341,4

                     618 

                     619 .L342:

00000328 00000000*   620 	.data.w	cmdTermPresentationBuf

                     621 	.type	.L342,$object

                     622 	.size	.L342,4

                     623 

                     624 .L343:

0000032c 00000000*   625 	.data.w	cmdTermMmsBuf

                     626 	.type	.L343,$object

                     627 	.size	.L343,4

                     628 

                     629 	.align	4

                     630 ;ctrlObjCnt	.L39	static

                     631 ;cmdTermDataBuf	cmdTermDataBuf	static

                     632 ;cmdTermMmsBuf	cmdTermMmsBuf	static

                     633 ;cmdTermPresentationBuf	cmdTermPresentationBuf	static

                     634 

                     635 	.data

                     636 	.comm	ctrlObjects,160,4

                     637 	.type	ctrlObjects,$object

                     638 	.size	ctrlObjects,160

                     639 	.comm	cmdTermObjNameBuf,132,4

                     640 	.type	cmdTermObjNameBuf,$object

                     641 	.size	cmdTermObjNameBuf,132

                     642 	.ghsnote version,6

                     643 	.ghsnote tools,3

                     644 	.ghsnote options,0

                     645 	.text

                     646 	.align	4

                     647 	.data

                     648 	.align	4

                     649 	.section ".bss","awb"

                     650 	.align	4

                     651 	.text

