#include "goose.h"
#include "bufViewBER.h"
#include "AsnEncoding.h"
#include "IEDCompile/InnerAttributeTypes.h"
#include "BaseAsnTypes.h"
#include "iedmodel.h"
#include "iedTree/iedTree.h"
#include "iedTree/DataSet.h"
#include "FinalDA.h"
#include "iedTree/iedFinalDA.h"
#include "mms_data.h"
#include "netTools.h"
#include "DataSlice.h"
#include "timers.h"
#include "platformTools.h"
#include <debug.h>
#include <string.h>
#include <stdlib.h>
#include <stdint.h>



#define  MAX_GO_CB_REF_SIZE 66
#define TIME_ALLOWED_TO_LIVE 4000
#define MAX_GOCB_COUNT 4

// Количество посылок с интервалом T1 после первой посылки изменений.
// Минимальное значение 1
#define T1_COUNT 4

//Максимальное количество GSE в одном GoCB
#define MAX_GSE_COUNT 3

//"Context specific" тэг настроек GSE VLAN
#define BER_VLAN_INFO 0xA0
//"Context specific" тэг настроек T0 и T1
#define BER_GOOSE_INTERVALS 0xA1

//Maximum transfer unit
#define MTU_SIZE 1480
#define MAX_GOOSE_DA_COUNT 600

#define ETHTYPE_GOOSE	0x88B8

typedef struct VLANInfoStruct * VLANInfo;

typedef struct {
	size_t ldPos;
	size_t lnPos;
	size_t fcPos;
	size_t goCBPos;
} GoCBPath;

//Для StNum и SqNum
typedef struct
{
	//Куда записывать
	uint8_t* p;
	uint32_t value;
} GOOSEPktNum;

struct VLANInfoStruct
{
	bool useVLAN;
	//Уточнить тип
	int id;
	//Уточнить тип
	int priority;
};


struct GSESettingsStruct
{
	//!Номер сетевого интерфейса
	uint8_t ifNum;
	void* netIf;
	uint8_t src[6];
	uint8_t dst[6];
	struct VLANInfoStruct vlan;
	uint16_t appID;	
	uint8_t* outPkt;
	size_t outPktSize;
	//Указатель и размер чтобы копировать PDU между пакетами
	uint8_t* pPDU;
	size_t pduSize;
	//Место в пакете, куда положить время
	uint8_t* pPktTime;
	//Номер пакета
	GOOSEPktNum sqNum;
	//Номер изменения состояния
	GOOSEPktNum stNum;
	uint32_t t0;
	uint32_t t1;
	uint32_t currT;
    //Счётчик количества первых посылок с интервалом t1
    uint32_t t1Counter;
	uint32_t msCounter;
};

struct GoCBStruct
{	
	bool goEna;
	StringView appID;
	StringView dataSetName;	
	IEDEntity dataSetEntity;
	DataSet* dataSet;
	bool ndsCom;
    char goCBRef[66];
	uint16_t confRev;
	uint32_t timeAllowedToLive;
	size_t gseCount;
	struct GSESettingsStruct gse[MAX_GSE_COUNT];
	size_t daCount;
	void* daList[MAX_GOOSE_DA_COUNT];
};

//Структура содержит размеры разных частей пакета GOOSE.

typedef struct  
{
	//Размер пакета без заголовка Ethernet
	uint16_t goosePktSize;
	uint16_t pduWithTLSize;
	uint16_t pduSize;
	uint16_t dataTLSize;
	uint16_t dataSize;
} GOOSESizes;

static size_t g_goCBCount = 0;
static struct GoCBStruct g_goCBs[MAX_GOCB_COUNT];

//Используется для хранения пути к GoCB при инициализации.
static GoCBPath g_goCBPath;


static void writeUlongBE(uint8_t* p, uint32_t data)
{	
	p[0] = data >> 24;
	p[1] = (data >> 16) & 0xFF;	
	p[2] = (data >> 8) & 0xFF;
	p[3] = data & 0xFF;
}

static GoCB getFreeGoCB(void)
{	
	if (g_goCBCount == MAX_GOCB_COUNT)
	{
		return NULL;
	}
	g_goCBCount++;
	return g_goCBs + g_goCBCount - 1;
}

//Пишем время в исходящий пакет GSE
static void writePktTime(GSESettings gse)
{
	uint64_t timeStamp = dataSliceGetTimeStamp();	
	MMSData_encodeTimeStamp(0x84, timeStamp, gse->pPktTime, 0);
}


static bool calcGOOSEDataSize(GoCB goCB, GOOSESizes* gSizes)
{	
	size_t daIdx;
	uint16_t totalSize = 0;
	for (daIdx = 0; daIdx < goCB->daCount; ++daIdx)
	{
		size_t daSize;
		if (!FDA_getFixedEncodedSize(goCB->daList[daIdx], &daSize))
		{
			return FALSE;
		}
		totalSize += (uint16_t)daSize;
	}
	gSizes->dataSize = totalSize;
	gSizes->dataTLSize = BerEncoder_determineFullObjectSize(gSizes->dataSize);
	return TRUE;
}

static bool calcGOOSEPDUSize(GoCB goCB, GOOSESizes* gSizes)
{
	uint16_t totalSize = 0;	
	//gocbRef, String
	totalSize += BerEncoder_determineFullObjectSize(strlen(goCB->goCBRef));
	//timeAlowedToLive, INT32U, ms
	totalSize += BerEncoder_determineFullObjectSize(
		BerEncoder_UInt32determineEncodedSize(goCB->timeAllowedToLive));
	//datSet, String
	totalSize += BerEncoder_determineFullObjectSize(goCB->dataSetName.len);
	//goID, String
	totalSize += BerEncoder_determineFullObjectSize(goCB->appID.len);
	//t, TimeStamp
	totalSize += 10;
	//stNum, INT32U
	totalSize += 6;
	//sqNum, INT32U
	totalSize += 6;
	//test, bool
	totalSize += 3;
	//confRev, INT32U
	totalSize += BerEncoder_determineFullObjectSize(
		BerEncoder_UInt32determineEncodedSize(goCB->confRev));
	//ndsCom
	totalSize += 3;
	//numDatSetEntries
	totalSize += BerEncoder_determineFullObjectSize(
		BerEncoder_UInt32determineEncodedSize(
			goCB->daCount));
	
	gSizes->pduSize = totalSize + gSizes->dataTLSize;
	gSizes->pduWithTLSize = BerEncoder_determineFullObjectSize(gSizes->pduSize);
	return TRUE;
}

static bool calcGOOSEPktSize(GoCB goCB, GSESettings gse,GOOSESizes* gSizes)
{
	uint16_t totalSize = 0;
	/*
	//Destination	
	totalSize += 6;
	//Source	
	totalSize += 6;
	//VLAN	
	if (gse->vlan.useVLAN)
	{
		totalSize += 4;
	}
	//Ethernet Type	
	totalSize += 2;
	*/
	//APP ID
	totalSize += 2;
	//Length	
	totalSize += 2;
	//Reserved1
	totalSize += 2;
	//Reserved2
	totalSize += 2;
	gSizes->goosePktSize = totalSize + gSizes->pduWithTLSize;
	return TRUE;
}

static bool calcGOOSESizes(GoCB goCB, GSESettings gse, GOOSESizes* gSizes)
{	
	if (!calcGOOSEDataSize(goCB, gSizes))
	{
		ERROR_REPORT("Unable to calculate GOOSE data size");
		return FALSE;
	}
	if (!calcGOOSEPDUSize(goCB, gSizes))
	{
		ERROR_REPORT("Unable to calculate GOOSE PDU size");
		return FALSE;
	}		
	//goosePktSize;			
	if (!calcGOOSEPktSize(goCB, gse, gSizes))
	{
		ERROR_REPORT("Unable to calculate full GOOSE packet size");
		return FALSE;
	}
	return TRUE;
}

static bool writeVLANInfo(VLANInfo vlan, BufferView* gooseBuf)
{
	if (vlan->useVLAN)
	{
		unsigned char vlanBuf[4];
		uint8_t tci1 = vlan->priority << 5;
		uint8_t tci2 = vlan->id % 256;
		tci1 += vlan->id / 256;

		// идентификатор vlan
		vlanBuf[0] = 0x81;
		vlanBuf[1] = 0x00;

		// priority + id
		vlanBuf[2] = tci1; 
		// id
		vlanBuf[3] = tci2; 

		if (BufferView_writeData(gooseBuf, vlanBuf, sizeof(vlanBuf)) < sizeof(vlanBuf))
		{
			return FALSE;
		}

		return TRUE;
	}
	return TRUE;
}

static bool writeGOOSEHeader(GSESettings gse, GOOSESizes* gooseSizes,
	BufferView* gooseBuf)
{
	//Destination
	if (BufferView_writeData(gooseBuf, gse->dst, 6) < 6)
	{
		return FALSE;
	}
	//Source
	if (BufferView_writeData(gooseBuf, gse->src, 6) < 6)
	{
		return FALSE;
	}

	//VLAN
	if (!writeVLANInfo(&gse->vlan, gooseBuf))
	{
		return FALSE;
	}

	//Ethernet Type
	if (!BufferView_writeUshortBE(gooseBuf, ETHTYPE_GOOSE))
	{
		return FALSE;
	}

	//APP ID
	if (!BufferView_writeUshortBE(gooseBuf, gse->appID))
	{
		return FALSE;
	}

	
	//Это длина всего пакета GOOSE без Ehthernet-заголовка
	if (!BufferView_writeUshortBE(gooseBuf, gooseSizes->goosePktSize))
	{
		return FALSE;
	}

	//Reserved
	if (!BufferView_writeUshortBE(gooseBuf, 0) ||
		!BufferView_writeUshortBE(gooseBuf, 0))
	{
		return FALSE;
	}
	return TRUE;
}

static bool writeDataTemplate(GoCB goCB, GOOSESizes* gooseSizes, 
	BufferView* templateBuf)
{
	size_t daIdx;
	
	if (!BufferView_encodeTL(templateBuf, 0xAB, gooseSizes->dataSize))
	{
		ERROR_REPORT("Error creating GOOSE data template");
		return FALSE;
	}

	for (daIdx = 0; daIdx < goCB->daCount; ++daIdx)
	{
		if (!FDA_encodeGOOSETemplate(goCB->daList[daIdx], templateBuf))
		{
			ERROR_REPORT("Error creating GOODE data template");
			return FALSE;
		}
	}	
	return TRUE;
}

static bool writeGOOSEPduTemplate(GoCB goCB, GSESettings gse, 
	GOOSESizes* gooseSizes, BufferView* templateBuf)
{
	uint8_t dummyTime[8] = { 0 };

	//Указатель на PDU чтобы потом копировать
	gse->pPDU = templateBuf->p + templateBuf->pos;
	gse->pduSize = gooseSizes->pduWithTLSize;

	if (!BufferView_encodeTL(templateBuf, 0x61, gooseSizes->pduSize))
	{
		return FALSE;
	}

	//gocbRef	
	if (!BufferView_encodeStr(templateBuf, 0x80, goCB->goCBRef))
	{
		return FALSE;
	}	
	//timeAllowedToLive (int)
	if (!BufferView_encodeUInt32(templateBuf, 0x81, goCB->timeAllowedToLive))
	{
		return FALSE;
	}
	//datSet
	if (!BufferView_encodeStringView(templateBuf, 0x82, &goCB->dataSetName))
	{
		return FALSE;
	}
	//goID
	if (!BufferView_encodeStringView(templateBuf, 0x83, &goCB->appID))
	{
		return FALSE;
	}
	//t
	//Запоминаем куда потом писать время
	gse->pPktTime = templateBuf->p + templateBuf->pos;
	if (!BufferView_encodeOctetString(templateBuf, 0x84, dummyTime, 8))
	{
		return FALSE;
	}	
	//stNum
	//Запоминаем позицию, куда потом вписывать stNum
	gse->stNum.p = templateBuf->p + templateBuf->pos + 2;
	if (!BufferView_encodeOctetString(templateBuf, 0x85, dummyTime, 4))
	{
		return FALSE;
	}	
	//sqNum	
	//Запоминаем позицию, куда потом вписывать sqNum
	gse->sqNum.p = templateBuf->p + templateBuf->pos + 2;
	if (!BufferView_encodeOctetString(templateBuf, 0x86, dummyTime, 4))
	{
		return FALSE;
	}
	//test
	if (!BufferView_encodeBoolean(templateBuf, 0x87, FALSE))
	{
		return FALSE;
	}
		
	//confRev
	if (!BufferView_encodeUInt32(templateBuf, 0x88, goCB->confRev))
	{
		return FALSE;
	}
	//ndsCom
	//На момент формирования шаблона ndsCom неизвестен.
	//Но поскольку GOOSE с ndsCom не посылается вообще,
	//можно смело писать false
	if (!BufferView_encodeBoolean(templateBuf, 0x89, false/*goCB->ndsCom*/))
	{
		return FALSE;
	}
	//numDatSetEntries
	if (!BufferView_encodeUInt32(templateBuf, 0x8A, goCB->daCount))
	{
		return FALSE;
	}
	//allData
	if (!writeDataTemplate(goCB, gooseSizes, templateBuf))
	{
		return FALSE;
	}
    WRITE_TO_FILE("gseData.bin", gse->pPDU, gse->pduSize);
	return TRUE;
}

// Подготавливает буфер GOOSE
// Должна вызываться при инициализации
bool prepareGOOSEbuf(GoCB goCB, GSESettings gse, BufferView* gooseBuf)
{	
	GOOSESizes gooseSizes;

	if (!calcGOOSESizes(goCB, gse, &gooseSizes))
	{
		ERROR_REPORT("Unable to calculate GOOSE sizes");
		return FALSE;
	}

	if (!writeGOOSEHeader(gse, &gooseSizes, gooseBuf))
	{
		return FALSE;
	}

	//GOOSE PDU	
	if (!writeGOOSEPduTemplate(goCB, gse, &gooseSizes, gooseBuf))
	{
		return FALSE;
	}
	
	return TRUE;
}

// Декодирует конфигурацию VLAN. Если нужный тэг не найден,
// устанавливает useVLAN = FALSE
static bool decodeVLANinfo(VLANInfo vlan, BufferView* berData)
{
	uint32_t vlanID;
	uint32_t vlanPriority;

	vlan->useVLAN = FALSE;
	if (!BufferView_checkTag(berData, BER_VLAN_INFO))
	{
		//Конфигурации VLAN нет. Это нормально.		
		return TRUE;
	}
	//Пропускаем тэг и длину информации о VLAN
	if (!BufferView_decodeTL(berData, NULL, NULL, NULL))
	{
		ERROR_REPORT("Unable to decode VLAN info tag and length");
		return FALSE;
	}
	//VLAN ID
	if (!BufferView_decodeUInt32TL(berData, ASN_INTEGER, &vlanID))
	{
		ERROR_REPORT("Unable to decode VLAN ID");
		return FALSE;
	}
	vlan->id = vlanID;

	//VLAN priority
	if (!BufferView_decodeUInt32TL(berData, ASN_INTEGER, &vlanPriority))
	{
		ERROR_REPORT("Unable to decode VLAN priority");
		return FALSE;
	}	
	vlan->priority = vlanPriority;
	vlan->useVLAN = TRUE;
	return TRUE;
}

bool GSE_init(GSESettings gse, BufferView* berData)
{
	uint8_t tag;	
	uint32_t ifNum;
	uint32_t appID;
	StringView dest;
	uint32_t t0, t1;

	//Тэг и длина GSE
	if (!BufferView_decodeTL(berData, &tag, NULL, NULL))
	{
		ERROR_REPORT("Unable to decode GSE tag and length");
		return FALSE;
	}
	if (tag != ASN_SEQUENCE)
	{
		return FALSE;
	}

	//Номер сетевого интерфейса
	if (!BufferView_decodeUInt32TL(berData, ASN_INTEGER, &ifNum))
	{
		ERROR_REPORT("Unable to decode GSE ifNum");
		return FALSE;
	}
	gse->ifNum = (uint8_t)ifNum;
	//Сетевой интерфейс
	if (!NetTools_getIf(gse->ifNum, &gse->netIf))
	{
		ERROR_REPORT("Unable to optain net interface");
		return FALSE;
	}
	//Source
	if (!NetTools_getMac(gse->netIf, gse->src))
	{
		ERROR_REPORT("Unable to optain interface MAC");
		return FALSE;
	}

	//Destination
	if (!BufferView_decodeStringViewTL(berData, ASN_OCTET_STRING, &dest)
		|| dest.len != 6)
	{
		ERROR_REPORT("Unable to decode GSE destination");
		return FALSE;
	}
	memcpy(&gse->dst, dest.p, dest.len);

	//VLAN info
	if (!decodeVLANinfo(&gse->vlan, berData))
	{
		ERROR_REPORT("Unable to decode GSE VLAN info");
		return FALSE;
	}
	//APP ID
	if (!BufferView_decodeUInt32TL(berData, ASN_INTEGER, &appID))
	{
		ERROR_REPORT("Unable to decode GSE appID");
		return FALSE;
	}
	gse->appID = (uint16_t)appID;
	//Тэг и длина T info
	if (!BufferView_decodeTL(berData, &tag, NULL, NULL) 
		|| tag != BER_GOOSE_INTERVALS)
	{
        ERROR_REPORT("Unable to decode GSE T0/T1 configuration");
		return FALSE;
	}

	//T0
	if (!BufferView_decodeUInt32TL(berData, ASN_INTEGER, &t0))
	{
		ERROR_REPORT("Unable to decode T0");
		return FALSE;
	}
	gse->t0 = t0;

	//T1
	if (!BufferView_decodeUInt32TL(berData, ASN_INTEGER, &t1))
	{
		ERROR_REPORT("Unable to decode T1");
		return FALSE;
	}
	gse->t1 = t1;

	return TRUE;
}

static bool initDatSet(size_t goCBPos, GoCB goCB)
{	
	IEDEntity dataSetEntity;

	if (!getConstDAString(goCBPos, "DatSet", &goCB->dataSetName))
	{
		return false;
	}

	//Получаем DataSet в IEDTree
	dataSetEntity = IEDTree_findDataSetBySingleName(&goCB->dataSetName);
	if(dataSetEntity == NULL)
	{
		return false;
	}
	goCB->dataSetEntity = dataSetEntity;
	goCB->dataSet = DataSet_getDataSetObj(dataSetEntity);
	if(goCB->dataSet == NULL)
	{
		ERROR_REPORT("Invalid DataSet");
		return false;
	}

	return true;
}

//Если delimiter NULL, то пишется конец стоки - 0
static bool writeObjectNameToBuf(size_t pos, char* delimiter, BufferView* buf)
{
	StringView nameView;	

	if (!getObjectName(pos, &nameView))
	{	
		return false;
	}
	if (!BufferView_writeStringView(buf, &nameView))
	{
		return false;
	}
	if (delimiter != NULL)
	{	
		// Записываем разделитель
		if (!BufferView_writeStr(buf, delimiter))
		{
			return false;
		}
		return true;
	}
	//Записываем конец строки
	if (1 != BufferView_writeData(buf, "", 1))
	{
		return false;
	}
	return true;
}

//Заполняет goCBRef правильной строкой ASCIIZ
static bool initGoCBRef(GoCB goCB)
{	
	BufferView refBuf;
    BufferView_init(&refBuf, (uint8_t*)goCB->goCBRef, MAX_GO_CB_REF_SIZE, 0);

	if (!writeObjectNameToBuf(g_goCBPath.ldPos, "/", &refBuf))
	{
		ERROR_REPORT("Unable to write LD name");
		return false;
	}

	if (!writeObjectNameToBuf(g_goCBPath.lnPos, "$", &refBuf))
	{
		ERROR_REPORT("Unable to write LN name");
		return false;
	}

	if (!writeObjectNameToBuf(g_goCBPath.fcPos, "$", &refBuf))
	{
		ERROR_REPORT("Unable to write FC name");
		return false;
	}

	if (!writeObjectNameToBuf(g_goCBPath.goCBPos, NULL, &refBuf))
	{
		ERROR_REPORT("Unable to write GoCB name");
		return false;
	}
	return true;
}

static bool initGoCBvars(GoCB goCB, size_t goCBPos)
{	
	uint32_t confRev;
	goCB->goEna = FALSE;

	//AppID
	if (!getConstDAString(goCBPos, "AppID", &goCB->appID))
	{
		ERROR_REPORT("Unable to init AppID");
		return FALSE;
	}

	//ConfRev
	if (!getConstDAULong(goCBPos, "ConfRev", &confRev))
	{
		ERROR_REPORT("Unable to init ConfRev");
		return FALSE;
	}	
	goCB->confRev = (uint16_t)confRev;

	if (!initDatSet(goCBPos, goCB))
	{
		ERROR_REPORT("Unable to init DatSet");
		return FALSE;
	}

	if (!initGoCBRef(goCB))
	{
		ERROR_REPORT("Unable to init GoCBRef");
		return FALSE;
	}

	goCB->timeAllowedToLive = TIME_ALLOWED_TO_LIVE;
	return TRUE;
}

static bool initGSEList(GoCB goCB, size_t goCBPos)
{
	size_t gseIndex;
	BufferView gseBER;
	uint8_t gseListTag;
	size_t gseListLen;
	void* pGSEList;

	//Пропускаем TL от goCB
	size_t currPos = readTL(goCBPos, NULL, NULL, NULL);
	if (currPos == 0)
	{
		ERROR_REPORT("Unable to read GoCB");
		return FALSE;
	}
	//Пропускаем имя
	currPos = skipObject(currPos);
	if (currPos == 0)
	{
		ERROR_REPORT("Unable to skip GoCB name");
		return FALSE;
	}
	//Получаем TL
    currPos = readTL(currPos, &gseListTag, (int*)&gseListLen, NULL);
	if (currPos == 0)
	{
		ERROR_REPORT("Unable to read GSE list");
		return FALSE;
	}
	//Проверяем что список GSE
	if (gseListTag != IED_GSE_LIST)
	{
		ERROR_REPORT("Invalid GSE list tag");
		return FALSE;
	}
	
	pGSEList = IEDModel_ptrFromPos(currPos);
	if (pGSEList == NULL)
	{
		ERROR_REPORT("Error getting GSE list pointer");
		return FALSE;
	}

	//Из длины и текущей позиции делаем BufferView
	BufferView_init(&gseBER, pGSEList, gseListLen, 0);

	goCB->gseCount = 0;	
	for (gseIndex = 0; gseIndex < MAX_GSE_COUNT; gseIndex++)
	{
		if (BufferView_endOfBuf(&gseBER))
		{
			return TRUE;
		}
		if (!GSE_init(goCB->gse + gseIndex, &gseBER))
		{
			return FALSE;
		}
		goCB->gseCount += 1;
	}
	return TRUE;
}

static bool initDAObjects(GoCB goCB)
{    
    IEDEntity da;	
    void* finalDAobj;

	DataSetItem* dsItem = goCB->dataSet->firstItem;
	while(dsItem != NULL)
	{
		da = dsItem->obj;

		if(da->type != IED_ENTITY_DA_TERMINAL_ITEM)
		{
			ERROR_REPORT("GOOSE: Unsupported object type");
			return false;
		}
		finalDAobj = FDA_create((enum InnerAttributeType)da->subType,
								IEDTermItemDA_getTerminalItemDescr(da));
		if (finalDAobj == NULL)
		{
			ERROR_REPORT("GOOSE: Unable to create the final DA object");
			return false;
		}
		goCB->daList[goCB->daCount] = finalDAobj;

		goCB->daCount++;
		dsItem = dsItem->next;
	}


	return true;
}

static bool initMsgTemplates(GoCB goCB)
{
	size_t gseIdx;
	BufferView gooseBufView;
	//Цикл по GSE с созданием шаблонов сообщений
	for (gseIdx = 0; gseIdx < goCB->gseCount; ++gseIdx)
	{
		//Выделяем память для пакета
		GSESettings gse = goCB->gse + gseIdx;
		uint8_t* buf = malloc(MTU_SIZE);
		if (buf == NULL)
		{
			ERROR_REPORT("Unable to allocate GOOSE buffer");
			return FALSE;
		}
		gse->outPkt = buf;
		BufferView_init(&gooseBufView, buf, MTU_SIZE, 0);
		if (!prepareGOOSEbuf(goCB, gse, &gooseBufView))
		{
			ERROR_REPORT("Unable to prepare GOOSE out buffer");
			return FALSE;
		}
		gse->outPktSize = gooseBufView.pos;
		WRITE_TO_FILE("gse.bin", gse->outPkt, gooseBufView.pos);
	}
	return TRUE;
}

static GOOSE_resetPktCounters(GoCB goCB)
{
	size_t gseIdx;
	for (gseIdx = 0; gseIdx < goCB->gseCount; ++gseIdx)
	{
		GSESettings gse = goCB->gse + gseIdx;
		gse->sqNum.value = 1;
		gse->stNum.value = 1;
	}
}

static GOOSE_resetPktTimers(GoCB goCB)
{
	size_t gseIdx;
	for (gseIdx = 0; gseIdx < goCB->gseCount; ++gseIdx)
	{
		GSESettings gse = goCB->gse + gseIdx;
		gse->currT = gse->t0;
        gse->t1Counter = 1;
        gse->msCounter = 0;
	}
}

static GOOSE_writePktCounters(GSESettings gse)
{
	writeUlongBE(gse->stNum.p, gse->stNum.value & 0x7FFFFFFF);
	writeUlongBE(gse->sqNum.p, gse->sqNum.value & 0x7FFFFFFF);
}

bool GoCB_init(GoCB goCB, size_t goCBPos)
{
	goCB->ndsCom = TRUE;

	if (!initGoCBvars(goCB, goCBPos))
	{		
		return FALSE;
	}
	if (!initGSEList(goCB, goCBPos) || goCB->gseCount == 0)
	{
		return FALSE;
	}

	//Пока передаются только Final DA эта функция вызывается здесь.
	//Когда надо будет передавать DO, возможно этот код должен будет
	//выполняться в процессе создания шаблонов сообщений
	if (!initDAObjects(goCB))
	{
		ERROR_REPORT("Unable to init dataset DA objects");
		return FALSE;
	}
	if (!initMsgTemplates(goCB))
	{
		ERROR_REPORT("Unable to init GOOSE message templates");
		return FALSE;
	}

	GOOSE_resetPktCounters(goCB);
	GOOSE_resetPktTimers(goCB);

	goCB->ndsCom = FALSE;
	return TRUE;
}

/*
static BOOL_T readDATypeInfo(BufferView* encodedFinalDA, 
	enum InnerAttributeType* outAttrType, void** accessInfo)
{
	uint32_t attrType;
	StringView encodedElemStruct;
	void* daObject;
	void* accessInfo;
	//Какое смещение использовано для выравнивания структуры описания
	uint8_t* pDescrStructAlignOffset;

	if (!BufferView_decodeTL(encodedFinalDA))
	{
		ERROR_REPORT("Unable to decode Final DA");
		return NULL;
	}
	//Получить тип элемента
	if (!BufferView_decodeUInt32TL(encodedFinalDA, ASN_INTEGER, &attrType))
	{
		ERROR_REPORT("Unable to decode Final DA type");
		return NULL;
	}
	*outAttrType = attrType;

	//Получаем структуру элемента
	if (BufferView_decodeStringViewTL(encodedFinalDA, ASN_OCTET_STRING,
		&encodedElemStruct))
	{
		ERROR_REPORT("Unable to decode Final DA struct");
		return NULL;
	}
	//Указатель непосредственно на структуру	
	pDescrStructAlignOffset = encodedElemStruct.p;
	if (*pDescrStructAlignOffset > 3)
	{
		ERROR_REPORT("Invalid alignment");
		return NULL;
	}
	*accessInfo = pDescrStructAlignOffset + *pDescrStructAlignOffset + 1;
	return TRUE;
}



//Создаёт объект FinalDA согласно описанию из информационной модели
// encodedFinalDA.
//Одновременно генерирует шаблон для кодирования исходящих данных.
//Возвращает указатель на объект FinalDA или NULL при ошибке
void* GoCB_initFinalDA(BufferView* encodedFinalDA, BufferView* templateBuf)
{
	InnerAttributeType attrType;
	void* daObject;

	if (readDATypeInfo())
	{
		return NULL;
	}
	
	//Создать элемент
	daObject = FDA_create(attrType, accessInfo);
	if (daObject == NULL)
	{
		ERROR_REPORT("Unable to create Final DA object");
		return NULL;
	}

	//Генерировать шаблон
	if (!FDA_encodeTemplate(daObject, templateBuf))
	{
		ERROR_REPORT("Unable to create Final DA template");
		return NULL;
	}

	return daObject;
}

//encodedChildren - буфер, содержащий закодированный список детей
BOOL_T GoCB_initDataSetIemChildren(BufferView* encodedChildren, 
	BufferView* templateBuf)
{
	while (encodedChildren->pos < encodedChildren->len)
	{
		if (!GoCB_initDataSetItem(encodedChildren, templateBuf, daList, pDaListPos,
			maxDaListPos))
		{
			return FALSE;
		}
	}
	return TRUE;
}


//Рекурсивно обходит элемент набора данных, создавая список FinalDA и 
//одновременно генерирует шаблон для кодирования исходящих данных.
//
//Шаблон это полноценно закодированный элемент набора данных, но на месте
//значений - зарезервированное пустое место, в которое потом можно вписать 
//фактическое занчение. Для значений переменной длины должно быть
//зарезервировано максимальное разумное значение

BOOL_T GoCB_initDataSetItem(BufferView* encodedItem, BufferView* templateBuf,
	void** daList, size_t* pDaListPos, size_t maxDaListPos)
{
	uint8_t objTag;

	if (*pDaListPos == maxDaListPos)
	{
		ERROR_REPORT("Too many DA in the dataset");
		return FALSE;
	}
	if (!BufferView_peekTag(&objTag))
	{
		ERROR_REPORT("Unable to read tag");
		return FALSE;
	}

	if (objTag == IED_DA_FINAL)
	{
		void* daObject = GoCB_initFinalDA(encodedItem, templateBuf);
		if (daObject == NULL)
		{
			return FALSE;
		}
		daList[(*pDaListPos)] = daObject;
		(*pDaListPos)++;
	}
	else
	{
		BufferView subObjects;
		//Получаем указатель на список детей и размер
		if (!getSubObjectsBufView(encodedItem, &subObjects))
		{			
			ERROR_REPORT("Unable to get subobjects");
			return FALSE;
		}
		if (!BufferView_decodeTL())
		{
			ERROR_REPORT("Unable to decode tag and length");
			return FALSE;
		}
		//Пишем Sequence и размер
		!!!
		//Цикл по детям
		if (!GoCB_initDataSetIemChildren())
		{
			return FALSE;
		}
	}
}
*/

static void registerGoCB(int goCBPos)
{		
	GoCB goCB = getFreeGoCB();
	g_goCBPath.goCBPos = goCBPos;
	TRACE("GoCB is found at 0x%04X", goCBPos);
	if (goCB == NULL)
	{
		ERROR_REPORT("Unable to register GoCB: too many GoCBs");
		return;
	}
	if (!GoCB_init(goCB, goCBPos))
	{
		ERROR_REPORT("Error initializing GoCB");
	}
}


// Регистрирует все GoCB, которые найдёт в указанном объекте FC
// если FC "GO". Если FC другой, то не делает ничего
void registerGoCBsGivenFC(int fcPos)
{
	StringView fcName;

	g_goCBPath.fcPos = fcPos;

	if (!getObjectName(fcPos, &fcName))
	{
		ERROR_REPORT("Unable to read FC name");
		return;
	}
	if (fcName.len != 2)
	{
		ERROR_REPORT("Invalid FC name");
		return;
	}
	if (memcmp("GO", fcName.p, 2) == 0)
	{		
		processSubobjects(fcPos, registerGoCB);
	}	
}

static void registerAllLogicalNodeGoCB(int lnPos)
{
	g_goCBPath.lnPos = lnPos;
	processSubobjects(lnPos, registerGoCBsGivenFC);
}

static void registerAllLogicalDeviceGoCB(int ldPos)
{
	int dataSectionPos;

	g_goCBPath.ldPos = ldPos;
	dataSectionPos = findObjectByTag(ldPos, IED_VMD_DATA_SECTION);
	if (!dataSectionPos)
	{
		ERROR_REPORT("Data section is not found");
		return;
	}

	processSubobjects(dataSectionPos, registerAllLogicalNodeGoCB);
}

static void registerAllGoCB(void)
{
	processSubobjects(0, registerAllLogicalDeviceGoCB);
}

void GOOSE_send(GSESettings gse)
{
	GOOSE_writePktCounters(gse);
	gse->sqNum.value++;
	NetTools_send(gse->netIf, gse->outPkt, gse->outPktSize);
}

static bool GOOSE_readAndDetectChange(GoCB goCB, void* dataSliceWnd)
{
	size_t daIdx;
	//Есть изменения в каком-нибудь DA
	bool changed = false;
	//Конкретный DA изменился
	bool daChanged = false;
	
	//Обязательно нужно обойти все DA, даже если изменения обнаружены
	//раньше, чтобы считать все данные для передачи.
	for (daIdx = 0; daIdx < goCB->daCount; ++daIdx)
	{
        FDA_readAndCompare(goCB->daList[daIdx], dataSliceWnd, &daChanged);
		changed = (changed || daChanged);
	}
	return changed;
}

static void GOOSE_writeDataToFirstGSEBuf(GoCB goCB)
{
	size_t daIdx;
		
	for (daIdx = 0; daIdx < goCB->daCount; ++daIdx)
	{
		FDA_encodeFixedData(goCB->daList[daIdx]);
	}
}

//Копирует PDU пакета
static void copyGSEPPDU(GSESettings gseSrc, GSESettings gseDst)
{
	memcpy(gseSrc->pPDU, gseDst->pPDU, gseSrc->pduSize);
}

void GOOSE_sendChanges(void)
{
	GSESettings gse;
	size_t goCBIdx;
	size_t gseIdx;
    void* dataSliceWnd;
    int interruptState = PTools_lockInterrupt();    
    dataSliceWnd = DataSlice_getDataSliceWnd();
	for (goCBIdx = 0; goCBIdx < g_goCBCount; ++goCBIdx)
	{
		GoCB goCB = g_goCBs + goCBIdx;
		
		if (goCB->goEna && !goCB->ndsCom)
		{
            if (GOOSE_readAndDetectChange(goCB, dataSliceWnd))
			{
				//Одинаковые данные для всех GSE
				gse = goCB->gse;
				writePktTime(gse);
				GOOSE_writeDataToFirstGSEBuf(goCB);
				gse->stNum.value++;
				gse->sqNum.value = 1;

				for (gseIdx = 0; gseIdx < goCB->gseCount; ++gseIdx)
				{
					gse = goCB->gse + gseIdx;
					
					if (gseIdx > 0)
					{						
						copyGSEPPDU(goCB->gse, gse);
					}

					gse->currT = gse->t1;
					gse->msCounter = 0;
                    gse->t1Counter = T1_COUNT;
					GOOSE_send(gse);
				}
			}
		}
	}
    PTools_unlockInterrupt(interruptState);
}

static void processGSETimer(GSESettings gse)
{	    
	if (gse->msCounter < gse->currT)
	{
		gse->msCounter++;
	}
	else
	{
		GOOSE_send(gse);                        
		gse->msCounter = 0;
        if(gse->t1Counter > 1)
        {
            gse->t1Counter--;
        }
        else
        {
            gse->currT *= 2;
            if (gse->currT > gse->t0)
            {
                gse->currT = gse->t0;
            }
        }
	}	
}

static void timerProc(void)
{
	size_t goCBIdx;
	size_t gseIdx;
    int interruptState = PTools_lockInterrupt();
	for (goCBIdx = 0; goCBIdx < g_goCBCount; ++goCBIdx)
	{
		GoCB goCB = g_goCBs + goCBIdx;

		if (goCB->goEna && !goCB->ndsCom)
		{		
			for (gseIdx = 0; gseIdx < goCB->gseCount; ++gseIdx)
			{
				processGSETimer(goCB->gse + gseIdx);
			}
		}
	}
    PTools_unlockInterrupt(interruptState);
}

bool GOOSE_getGoEna(size_t cbIndex, bool* goEna)
{
	if (cbIndex >= g_goCBCount)
	{
		ERROR_REPORT("GoCB index is too big");
		return FALSE;
	}
	*goEna = g_goCBs[cbIndex].goEna;
	return TRUE;
}

bool GOOSE_getNdsCom(size_t cbIndex, bool* ndsCom)
{
	if (cbIndex >= g_goCBCount)
	{
		ERROR_REPORT("GoCB index is too big");
		return FALSE;
	}
	*ndsCom = g_goCBs[cbIndex].ndsCom;
	return TRUE;
}

bool GOOSE_setGoEna(size_t cbIndex, bool value)
{
	GoCB goCB;
	if (cbIndex >= g_goCBCount)
	{
		ERROR_REPORT("GoCB index is too big");
		return FALSE;
	}
	goCB = g_goCBs + cbIndex;

	if (goCB->ndsCom)
	{
		return false;
	}

	if (!goCB->goEna && value)
	{
		//Если включаем
        GOOSE_resetPktCounters(goCB);
		GOOSE_resetPktTimers(goCB);
	}
	g_goCBs[cbIndex].goEna = value;
	return TRUE;
}

void GOOSE_init(void)
{
    size_t i;
    registerAllGoCB();    	            
    DataSlice_setCallBack(GOOSE_sendChanges);
    Timers_setGoose1msCallBack(timerProc);

    //Включаем все GoCB
    for(i = 0; i < g_goCBCount; i++)
    {
        if(!g_goCBs[i].ndsCom)
        {
            g_goCBs[i].goEna = true;
        }
    }
}
