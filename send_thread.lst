                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8qc1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=send_thread.c -o gh_8qc1.o -list=send_thread.lst C:\Users\<USER>\AppData\Local\Temp\gh_8qc1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_8qc1.s
Source File: send_thread.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile send_thread.c

                      10 ;		-o send_thread.o

                      11 ;Source File:   send_thread.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Oct 07 09:39:39 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "send_thread.h"


                      21 ;2: #include "out_buffers.h"


                      22 ;3: #include "Cotp.h"


                      23 ;4: #include <Clib.h>


                      24 ;5: #include <stddef.h>


                      25 ;6: 


                      26 ;7: void sendThread(IsoConnection* isoConn)


                      27 	.text

                      28 	.align	4

                      29 sendThread::

00000000 e92d4030     30 	stmfd	[sp]!,{r4-r5,lr}

                      31 ;8: {


                      32 

                      33 ;9:     isoConn->sendThreadIsRunning = true;


                      34 

00000004 e3a01bf2     35 	mov	r1,242<<10

00000008 e2811099     36 	add	r1,r1,153

0000000c e1a04000     37 	mov	r4,r0

00000010 e3a00001     38 	mov	r0,1

00000014 e7c10004     39 	strb	r0,[r1,r4]

                      40 ;10:     while(isoConn->connected)


                      41 

00000018 e3a01bf2     42 	mov	r1,242<<10

0000001c e2811098     43 	add	r1,r1,152

00000020 e7d10004     44 	ldrb	r0,[r1,r4]

00000024 e3500000     45 	cmp	r0,0

00000028 0a000017     46 	beq	.L5

                      47 .L6:

                      48 ;11:     {


                      49 

                      50 ;12:         SessionOutBuffer* buffer = OutQueue_get(&isoConn->outQueue);



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8qc1.s
                      51 

0000002c e2840bf2     52 	add	r0,r4,242<<10

00000030 e280006c     53 	add	r0,r0,108

00000034 eb000000*    54 	bl	OutQueue_get

00000038 e1b05000     55 	movs	r5,r0

                      56 ;13:         if(buffer == NULL)


                      57 

0000003c 1a000006     58 	bne	.L7

                      59 ;14:         {


                      60 

                      61 ;15:             Idle();


                      62 

00000040 e6000010     63 	.word	0xE6000010

                      64 

00000044 e3a01bf2     65 	mov	r1,242<<10

00000048 e2811098     66 	add	r1,r1,152

0000004c e7d10004     67 	ldrb	r0,[r1,r4]

00000050 e3500000     68 	cmp	r0,0

00000054 1afffff4     69 	bne	.L6

00000058 ea00000b     70 	b	.L5

                      71 .L7:

                      72 ;16:         }


                      73 ;17:         else


                      74 ;18:         {


                      75 

                      76 ;19:             //Посылаем


                      77 ;20:             cotpSendData(&isoConn->cotpConn, buffer->cotpOutBuf, buffer->byteCount);


                      78 

0000005c e5952004     79 	ldr	r2,[r5,4]

00000060 e2851008     80 	add	r1,r5,8

00000064 e2840bf2     81 	add	r0,r4,242<<10

00000068 e280009c     82 	add	r0,r0,156

0000006c eb000000*    83 	bl	cotpSendData

                      84 ;21: 			freeSessionOutBuffer(buffer);


                      85 

00000070 e1a00005     86 	mov	r0,r5

00000074 eb000000*    87 	bl	freeSessionOutBuffer

00000078 e3a01bf2     88 	mov	r1,242<<10

0000007c e2811098     89 	add	r1,r1,152

00000080 e7d10004     90 	ldrb	r0,[r1,r4]

00000084 e3500000     91 	cmp	r0,0

00000088 1affffe7     92 	bne	.L6

                      93 .L5:

                      94 ;22:         }


                      95 ;23:     }


                      96 ;24:     isoConn->sendThreadIsRunning = false;


                      97 

0000008c e3a01bf2     98 	mov	r1,242<<10

00000090 e2811099     99 	add	r1,r1,153

00000094 e7c10004    100 	strb	r0,[r1,r4]

00000098 e8bd8030    101 	ldmfd	[sp]!,{r4-r5,pc}

                     102 	.endf	sendThread

                     103 	.align	4

                     104 ;buffer	r5	local

                     105 

                     106 ;isoConn	r4	param

                     107 

                     108 	.section ".bss","awb"

                     109 .L82:

                     110 	.data

                     111 	.text


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8qc1.s
                     112 

                     113 ;25: }


                     114 	.align	4

                     115 

                     116 	.data

                     117 	.ghsnote version,6

                     118 	.ghsnote tools,1

                     119 	.ghsnote options,0

                     120 	.text

                     121 	.align	4

