
#include "RptBool.h"

#include <debug.h>

static void initValue(RptItem item)
{
    item->oldValue.boolValue = item->iedObj->boolValue;
}

static void updateChanges(RptItem item)
{        
    item->newValue.boolValue = item->iedObj->boolValue;
}


static bool encodeRead(RptItem item, BufferView* outBuf)
{			
    //!!! Тут много чего надо написать

    item->changedOld = item->changedNew;
    item->changedNew = TRGOP_NONE;
    return true;
}

static bool calcReadLen(RptItem item, size_t* pLen )
{
	*pLen = 3; // BOOL encoded size
	return true;
}

static void overwriteOld(RptItem item)
{
    item->oldValue.boolValue = item->newValue.boolValue;    
}

static struct RptItemBehavior behavior = {
    initValue,
    updateChanges,
    encodeRead,
    calcReadLen,
    overwriteOld
};


void RptBool_init(RptItem item)
{
    item->behaviour = &behavior;

}
