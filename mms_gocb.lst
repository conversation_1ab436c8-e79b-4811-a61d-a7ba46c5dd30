                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3eo1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=mms_gocb.c -o gh_3eo1.o -list=mms_gocb.lst C:\Users\<USER>\AppData\Local\Temp\gh_3eo1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_3eo1.s
Source File: mms_gocb.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile mms_gocb.c -o

                      10 ;		mms_gocb.o

                      11 ;Source File:   mms_gocb.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Oct 07 09:39:27 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "mms_gocb.h"


                      21 ;2: #include "IEDCompile/AccessInfo.h"


                      22 ;3: #include "IEDCompile/InnerAttributeTypes.h"


                      23 ;4: #include "iedmodel.h"


                      24 ;5: #include "mms_data.h"


                      25 ;6: #include "goose.h"


                      26 ;7: #include "BaseAsnTypes.h"


                      27 ;8: #include <stddef.h>


                      28 ;9: 


                      29 ;10: //Комментарий для проверки кодировки


                      30 ;11: 


                      31 ;12: int encodeAccessAttrGoCB(uint8_t* outBuf, int bufPos, int accessDataPos,


                      32 ;13: 	bool determineSize)


                      33 ;14: {


                      34 ;15: 	CBAttrAccessInfo* pAccessInfo =


                      35 ;16: 		(CBAttrAccessInfo*)getAlignedDescrStruct(accessDataPos);


                      36 ;17: 	if (pAccessInfo == NULL)


                      37 ;18: 	{


                      38 ;19: 		ERROR_REPORT("Unable to get access info struct");


                      39 ;20: 		return 0;


                      40 ;21: 	}


                      41 ;22: 


                      42 ;23: 	switch (pAccessInfo->attrCode)


                      43 ;24: 	{


                      44 ;25: 	case GoEna:	


                      45 ;26: 	case NdsCom:


                      46 ;27: 		return encodeAccessAttrBoolean(outBuf, bufPos, determineSize);	


                      47 ;28: 	default:


                      48 ;29: 		ERROR_REPORT("Invalid GoCB DA code");


                      49 ;30: 		return 0;


                      50 ;31: 	}



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3eo1.s
                      51 ;32: }


                      52 ;33: 


                      53 ;34: static int encodeReadGoEna(uint8_t* outBuf, int bufPos,


                      54 

                      55 ;42: }


                      56 

                      57 ;43: 


                      58 ;44: static int encodeReadNdsCom(uint8_t* outBuf, int bufPos,


                      59 

                      60 ;51: }


                      61 

                      62 ;52: 


                      63 ;53: int encodeReadAttrGoCB(uint8_t* outBuf, int bufPos, void* descrStruct, bool determineSize)


                      64 ;54: {


                      65 ;55: 	CBAttrAccessInfo* pAccessInfo = descrStruct;


                      66 ;56: 


                      67 ;57: 	switch (pAccessInfo->attrCode) {


                      68 ;58: 	case GoEna:


                      69 ;59: 		return encodeReadGoEna(outBuf, bufPos, pAccessInfo, determineSize);


                      70 ;60: 	case NdsCom:


                      71 ;61: 		return encodeReadNdsCom(outBuf, bufPos, pAccessInfo, determineSize);


                      72 ;62: 	default:


                      73 ;63: 		ERROR_REPORT("Invalid  GoCB DA code");


                      74 ;64: 		return 0;


                      75 ;65: 	}


                      76 ;66: }


                      77 ;67: 


                      78 ;68: static void writeGoEna(int cbIndex, uint8_t* dataToWrite)


                      79 

                      80 ;79: }


                      81 

                      82 	.text

                      83 	.align	4

                      84 encodeAccessAttrGoCB::

00000000 e92d4070     85 	stmfd	[sp]!,{r4-r6,lr}

00000004 e1a05001     86 	mov	r5,r1

00000008 e1a06003     87 	mov	r6,r3

0000000c e1a04000     88 	mov	r4,r0

00000010 e1a00002     89 	mov	r0,r2

00000014 eb000000*    90 	bl	getAlignedDescrStruct

00000018 e3500000     91 	cmp	r0,0

0000001c 0a000007     92 	beq	.L64

00000020 e5900004     93 	ldr	r0,[r0,4]

00000024 e2500001     94 	subs	r0,r0,1

00000028 e3500001     95 	cmp	r0,1

0000002c 91a02006     96 	movls	r2,r6

00000030 91a01005     97 	movls	r1,r5

00000034 91a00004     98 	movls	r0,r4

00000038 98bd4070     99 	ldmlsfd	[sp]!,{r4-r6,lr}

0000003c 9a000000*   100 	bls	encodeAccessAttrBoolean

                     101 .L64:

00000040 e3a00000    102 	mov	r0,0

00000044 e8bd8070    103 	ldmfd	[sp]!,{r4-r6,pc}

                     104 	.endf	encodeAccessAttrGoCB

                     105 	.align	4

                     106 ;pAccessInfo	r0	local

                     107 

                     108 ;outBuf	r4	param

                     109 ;bufPos	r5	param

                     110 ;accessDataPos	r2	param

                     111 ;determineSize	r6	param


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3eo1.s
                     112 

                     113 	.section ".bss","awb"

                     114 .L124:

                     115 	.data

                     116 	.text

                     117 

                     118 

                     119 	.align	4

                     120 	.align	4

                     121 encodeReadAttrGoCB::

00000048 e92d4070    122 	stmfd	[sp]!,{r4-r6,lr}

0000004c e24dd004    123 	sub	sp,sp,4

00000050 e1a05000    124 	mov	r5,r0

00000054 e5920004    125 	ldr	r0,[r2,4]

00000058 e1a06001    126 	mov	r6,r1

0000005c e2500001    127 	subs	r0,r0,1

00000060 0a000003    128 	beq	.L145

00000064 e3500001    129 	cmp	r0,1

00000068 13a00000    130 	movne	r0,0

0000006c 1a000013    131 	bne	.L141

00000070 ea000009    132 	b	.L151

                     133 .L145:

00000074 e1a04003    134 	mov	r4,r3

                     135 ;35: 	CBAttrAccessInfo* descrStruct, bool determineSize)


                     136 ;36: {


                     137 

                     138 ;37: 	size_t cbIndex = descrStruct->rcbIndex;


                     139 

00000078 e5920008    140 	ldr	r0,[r2,8]

                     141 ;38: 	bool value;


                     142 ;39: 	GOOSE_getGoEna(cbIndex, &value);


                     143 

0000007c e28d1002    144 	add	r1,sp,2

00000080 eb000000*   145 	bl	GOOSE_getGoEna

                     146 ;40: 


                     147 ;41: 	return encodeBoolValue(outBuf, bufPos, value, determineSize);


                     148 

00000084 e1a03004    149 	mov	r3,r4

00000088 e5dd2002    150 	ldrb	r2,[sp,2]

0000008c e1a01006    151 	mov	r1,r6

00000090 e1a00005    152 	mov	r0,r5

00000094 eb000000*   153 	bl	encodeBoolValue

00000098 ea000008    154 	b	.L141

                     155 .L151:

0000009c e1a04003    156 	mov	r4,r3

                     157 ;45: 	CBAttrAccessInfo* descrStruct, bool determineSize)


                     158 ;46: {


                     159 

                     160 ;47: 	size_t cbIndex = descrStruct->rcbIndex;


                     161 

000000a0 e5920008    162 	ldr	r0,[r2,8]

                     163 ;48: 	bool value;


                     164 ;49: 	GOOSE_getNdsCom(cbIndex, &value);


                     165 

000000a4 e28d1003    166 	add	r1,sp,3

000000a8 eb000000*   167 	bl	GOOSE_getNdsCom

                     168 ;50: 	return encodeBoolValue(outBuf, bufPos, value, determineSize);


                     169 

000000ac e1a03004    170 	mov	r3,r4

000000b0 e5dd2003    171 	ldrb	r2,[sp,3]

000000b4 e1a01006    172 	mov	r1,r6


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3eo1.s
000000b8 e1a00005    173 	mov	r0,r5

000000bc eb000000*   174 	bl	encodeBoolValue

                     175 .L141:

000000c0 e28dd004    176 	add	sp,sp,4

000000c4 e8bd8070    177 	ldmfd	[sp]!,{r4-r6,pc}

                     178 	.endf	encodeReadAttrGoCB

                     179 	.align	4

                     180 ;pAccessInfo	r2	local

                     181 ;determineSize	r4	local

                     182 ;value	[sp,2]	local

                     183 ;determineSize	r4	local

                     184 ;value	[sp,3]	local

                     185 

                     186 ;outBuf	r5	param

                     187 ;bufPos	r6	param

                     188 ;descrStruct	r2	param

                     189 ;determineSize	r3	param

                     190 

                     191 	.section ".bss","awb"

                     192 .L192:

                     193 	.data

                     194 	.text

                     195 

                     196 

                     197 ;80: 


                     198 ;81: void writeAttrGoCB(void* descrStruct, uint8_t* dataToWrite)


                     199 	.align	4

                     200 	.align	4

                     201 writeAttrGoCB::

                     202 ;82: {


                     203 

                     204 ;83: 	CBAttrAccessInfo* pAccessInfo = descrStruct;


                     205 

                     206 ;84: 


                     207 ;85: 	switch (pAccessInfo->attrCode)


                     208 

000000c8 e5902004    209 	ldr	r2,[r0,4]

000000cc e3520001    210 	cmp	r2,1

                     211 ;86: 	{


                     212 ;87: 	case GoEna:


                     213 ;88: 		writeGoEna(pAccessInfo->rcbIndex, dataToWrite);


                     214 

000000d0 05d12000    215 	ldreqb	r2,[r1]

000000d4 05900008    216 	ldreq	r0,[r0,8]

                     217 ;69: {


                     218 

                     219 ;70: 	bool value;


                     220 ;71: 	


                     221 ;72: 	if (dataToWrite[0] != IEC61850_BER_BOOLEAN || dataToWrite[1] != 1)


                     222 

000000d8 03520083    223 	cmpeq	r2,131

000000dc 05d12001    224 	ldreqb	r2,[r1,1]

000000e0 03520001    225 	cmpeq	r2,1

000000e4 1a000003    226 	bne	.L209

                     227 ;73: 	{


                     228 

                     229 ;74: 		return;


                     230 

                     231 ;75: 	}


                     232 ;76: 	value = dataToWrite[2] != 0;


                     233 


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3eo1.s
000000e8 e5d11002    234 	ldrb	r1,[r1,2]

000000ec e3510000    235 	cmp	r1,0

000000f0 13a01001    236 	movne	r1,1

                     237 ;77: 	


                     238 ;78: 	GOOSE_setGoEna(cbIndex, value);


                     239 

000000f4 ea000000*   240 	b	GOOSE_setGoEna

                     241 .L209:

000000f8 e12fff1e*   242 	ret	

                     243 	.endf	writeAttrGoCB

                     244 	.align	4

                     245 ;pAccessInfo	r0	local

                     246 ;cbIndex	r0	local

                     247 ;value	r1	local

                     248 

                     249 ;descrStruct	r0	param

                     250 ;dataToWrite	r1	param

                     251 

                     252 	.section ".bss","awb"

                     253 .L262:

                     254 	.data

                     255 	.text

                     256 

                     257 ;89: 		break;


                     258 ;90: 	default:


                     259 ;91: 		ERROR_REPORT("Unsupported GoCB attribute");


                     260 ;92: 	}


                     261 ;93: 


                     262 ;94: }


                     263 	.align	4

                     264 

                     265 	.data

                     266 	.ghsnote version,6

                     267 	.ghsnote tools,3

                     268 	.ghsnote options,0

                     269 	.text

                     270 	.align	4

