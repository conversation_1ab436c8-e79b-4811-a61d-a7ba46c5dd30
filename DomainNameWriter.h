#pragma once

#include "MmsConst.h"
#include <stdint.h>
#include <stdbool.h>

//! Вспомогательный "класс" для генерации списка доменных имён.
//! Непосредственно в поля этой структуры записывать нельзя - нарушение
//! инкапсуляции.

typedef struct {
    uint8_t* outBuf;
    int totalSize;
    bool bufferFull;
    int outBufSize;
    int stackDepth;
    //! Содержит стек к концами полных имён.
    int nameEndStack[10];
    uint8_t currDomainName[MAX_MMS_DOMAIN_NAME+1];
    //! Режим перемотки до нужного имени
    bool fastForward;
    //! Имя, до которого делать перемотку
    uint8_t startingName[MAX_MMS_DOMAIN_NAME + 1];
    //! Размер имени, до которого делается перемотка
    int startingNameSize;
} DomainNameWriter;

//! Конструктор
void DomainNameWriter_init(DomainNameWriter* self,
    uint8_t* outBuf, int outBufSize);

//! Должна вызываться после конструктора до всех прочих функций.
//! Устанавливает режим "перемотки" до нужного имени.
//! В этом режиме функция encode ничего не делает пока не встретит
//! указанное полное имя. После того как указанное имя встречено,
//! начиная со следующего полного имени всё работает в нормальном режиме.
//! Указанное имя сохраняется внутри объекта.
void DomainNameWriter_setStartName(DomainNameWriter* self,
    uint8_t* startName, int startNameSize);

//! Добавляет новое имя в конец полного доменного имени
void DomainNameWriter_pushName(DomainNameWriter* self,
    uint8_t* name, int nameLen);

//! Отбрасывает последнее имя в полном доменном имени
void DomainNameWriter_discardName(DomainNameWriter* self);

//! Записывает текущее полное доменное имя в исходящий буфер
//! возвращает число записываемых байт.
//! В режиме "перемотки" до нужного имени возвращает 0
int DomainNameWriter_encode(DomainNameWriter* self);
