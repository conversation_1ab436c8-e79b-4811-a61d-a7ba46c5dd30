<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE QtCreatorProject>
<!-- Written by QtCreator 4.9.1, 2025-04-28T12:11:26. -->
<qtcreator>
 <data>
  <variable>EnvironmentId</variable>
  <value type="QByteArray">{a5b6ada6-70c4-41b8-b74e-ef46e13cf18d}</value>
 </data>
 <data>
  <variable>ProjectExplorer.Project.ActiveTarget</variable>
  <value type="int">0</value>
 </data>
 <data>
  <variable>ProjectExplorer.Project.EditorSettings</variable>
  <valuemap type="QVariantMap">
   <value type="bool" key="EditorConfiguration.AutoIndent">true</value>
   <value type="bool" key="EditorConfiguration.AutoSpacesForTabs">false</value>
   <value type="bool" key="EditorConfiguration.CamelCaseNavigation">true</value>
   <valuemap type="QVariantMap" key="EditorConfiguration.CodeStyle.0">
    <value type="QString" key="language">Cpp</value>
    <valuemap type="QVariantMap" key="value">
     <value type="QByteArray" key="CurrentPreferences">CppGlobal</value>
    </valuemap>
   </valuemap>
   <valuemap type="QVariantMap" key="EditorConfiguration.CodeStyle.1">
    <value type="QString" key="language">QmlJS</value>
    <valuemap type="QVariantMap" key="value">
     <value type="QByteArray" key="CurrentPreferences">QmlJSGlobal</value>
    </valuemap>
   </valuemap>
   <value type="int" key="EditorConfiguration.CodeStyle.Count">2</value>
   <value type="QByteArray" key="EditorConfiguration.Codec">UTF-8</value>
   <value type="bool" key="EditorConfiguration.ConstrainTooltips">false</value>
   <value type="int" key="EditorConfiguration.IndentSize">4</value>
   <value type="bool" key="EditorConfiguration.KeyboardTooltips">false</value>
   <value type="int" key="EditorConfiguration.MarginColumn">80</value>
   <value type="bool" key="EditorConfiguration.MouseHiding">true</value>
   <value type="bool" key="EditorConfiguration.MouseNavigation">true</value>
   <value type="int" key="EditorConfiguration.PaddingMode">1</value>
   <value type="bool" key="EditorConfiguration.ScrollWheelZooming">true</value>
   <value type="bool" key="EditorConfiguration.ShowMargin">false</value>
   <value type="int" key="EditorConfiguration.SmartBackspaceBehavior">0</value>
   <value type="bool" key="EditorConfiguration.SmartSelectionChanging">true</value>
   <value type="bool" key="EditorConfiguration.SpacesForTabs">true</value>
   <value type="int" key="EditorConfiguration.TabKeyBehavior">0</value>
   <value type="int" key="EditorConfiguration.TabSize">8</value>
   <value type="bool" key="EditorConfiguration.UseGlobal">true</value>
   <value type="int" key="EditorConfiguration.Utf8BomBehavior">1</value>
   <value type="bool" key="EditorConfiguration.addFinalNewLine">true</value>
   <value type="bool" key="EditorConfiguration.cleanIndentation">true</value>
   <value type="bool" key="EditorConfiguration.cleanWhitespace">true</value>
   <value type="bool" key="EditorConfiguration.inEntireDocument">false</value>
  </valuemap>
 </data>
 <data>
  <variable>ProjectExplorer.Project.PluginSettings</variable>
  <valuemap type="QVariantMap">
   <valuelist type="QVariantList" key="ClangCodeModel.CustomCommandLineKey">
    <value type="QString">-fno-delayed-template-parsing</value>
   </valuelist>
   <value type="bool" key="ClangCodeModel.UseGlobalConfig">true</value>
  </valuemap>
 </data>
 <data>
  <variable>ProjectExplorer.Project.Target.0</variable>
  <valuemap type="QVariantMap">
   <value type="QString" key="ProjectExplorer.ProjectConfiguration.DefaultDisplayName">GHS</value>
   <value type="QString" key="ProjectExplorer.ProjectConfiguration.DisplayName">GHS</value>
   <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">{0a613069-8a56-46bf-9fba-37d3f69bbb51}</value>
   <value type="int" key="ProjectExplorer.Target.ActiveBuildConfiguration">0</value>
   <value type="int" key="ProjectExplorer.Target.ActiveDeployConfiguration">0</value>
   <value type="int" key="ProjectExplorer.Target.ActiveRunConfiguration">0</value>
   <valuemap type="QVariantMap" key="ProjectExplorer.Target.BuildConfiguration.0">
    <value type="QString" key="ProjectExplorer.BuildConfiguration.BuildDirectory">.</value>
    <valuemap type="QVariantMap" key="ProjectExplorer.BuildConfiguration.BuildStepList.0">
     <valuemap type="QVariantMap" key="ProjectExplorer.BuildStepList.Step.0">
      <valuelist type="QVariantList" key="GenericProjectManager.GenericMakeStep.BuildTargets">
       <value type="QString">all</value>
      </valuelist>
      <value type="bool" key="GenericProjectManager.GenericMakeStep.Clean">false</value>
      <value type="QString" key="GenericProjectManager.GenericMakeStep.MakeArguments">PLATFORM=IEDNEXUS </value>
      <value type="bool" key="GenericProjectManager.GenericMakeStep.OverrideMakeflags">false</value>
      <value type="bool" key="ProjectExplorer.BuildStep.Enabled">true</value>
      <value type="QString" key="ProjectExplorer.ProjectConfiguration.DefaultDisplayName">Make</value>
      <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">GenericProjectManager.GenericMakeStep</value>
     </valuemap>
     <value type="int" key="ProjectExplorer.BuildStepList.StepsCount">1</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.DefaultDisplayName">Build</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">ProjectExplorer.BuildSteps.Build</value>
    </valuemap>
    <valuemap type="QVariantMap" key="ProjectExplorer.BuildConfiguration.BuildStepList.1">
     <valuemap type="QVariantMap" key="ProjectExplorer.BuildStepList.Step.0">
      <valuelist type="QVariantList" key="GenericProjectManager.GenericMakeStep.BuildTargets">
       <value type="QString">clean</value>
      </valuelist>
      <value type="bool" key="GenericProjectManager.GenericMakeStep.Clean">false</value>
      <value type="bool" key="GenericProjectManager.GenericMakeStep.OverrideMakeflags">false</value>
      <value type="bool" key="ProjectExplorer.BuildStep.Enabled">true</value>
      <value type="QString" key="ProjectExplorer.ProjectConfiguration.DefaultDisplayName">Make</value>
      <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">GenericProjectManager.GenericMakeStep</value>
     </valuemap>
     <value type="int" key="ProjectExplorer.BuildStepList.StepsCount">1</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.DefaultDisplayName">Clean</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">ProjectExplorer.BuildSteps.Clean</value>
    </valuemap>
    <value type="int" key="ProjectExplorer.BuildConfiguration.BuildStepListCount">2</value>
    <value type="bool" key="ProjectExplorer.BuildConfiguration.ClearSystemEnvironment">false</value>
    <valuelist type="QVariantList" key="ProjectExplorer.BuildConfiguration.UserEnvironmentChanges"/>
    <value type="QString" key="ProjectExplorer.ProjectConfiguration.DefaultDisplayName">Default</value>
    <value type="QString" key="ProjectExplorer.ProjectConfiguration.DisplayName">Release (IEDNEXUS)</value>
    <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">GenericProjectManager.GenericBuildConfiguration</value>
   </valuemap>
   <valuemap type="QVariantMap" key="ProjectExplorer.Target.BuildConfiguration.1">
    <value type="QString" key="ProjectExplorer.BuildConfiguration.BuildDirectory">.</value>
    <valuemap type="QVariantMap" key="ProjectExplorer.BuildConfiguration.BuildStepList.0">
     <valuemap type="QVariantMap" key="ProjectExplorer.BuildStepList.Step.0">
      <valuelist type="QVariantList" key="GenericProjectManager.GenericMakeStep.BuildTargets">
       <value type="QString">all</value>
      </valuelist>
      <value type="bool" key="GenericProjectManager.GenericMakeStep.Clean">false</value>
      <value type="QString" key="GenericProjectManager.GenericMakeStep.MakeArguments">PLATFORM=DRP150 </value>
      <value type="bool" key="GenericProjectManager.GenericMakeStep.OverrideMakeflags">false</value>
      <value type="bool" key="ProjectExplorer.BuildStep.Enabled">true</value>
      <value type="QString" key="ProjectExplorer.ProjectConfiguration.DefaultDisplayName">Make</value>
      <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">GenericProjectManager.GenericMakeStep</value>
     </valuemap>
     <value type="int" key="ProjectExplorer.BuildStepList.StepsCount">1</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.DefaultDisplayName">Build</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">ProjectExplorer.BuildSteps.Build</value>
    </valuemap>
    <valuemap type="QVariantMap" key="ProjectExplorer.BuildConfiguration.BuildStepList.1">
     <valuemap type="QVariantMap" key="ProjectExplorer.BuildStepList.Step.0">
      <valuelist type="QVariantList" key="GenericProjectManager.GenericMakeStep.BuildTargets">
       <value type="QString">clean</value>
      </valuelist>
      <value type="bool" key="GenericProjectManager.GenericMakeStep.Clean">false</value>
      <value type="bool" key="GenericProjectManager.GenericMakeStep.OverrideMakeflags">false</value>
      <value type="bool" key="ProjectExplorer.BuildStep.Enabled">true</value>
      <value type="QString" key="ProjectExplorer.ProjectConfiguration.DefaultDisplayName">Make</value>
      <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">GenericProjectManager.GenericMakeStep</value>
     </valuemap>
     <value type="int" key="ProjectExplorer.BuildStepList.StepsCount">1</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.DefaultDisplayName">Clean</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">ProjectExplorer.BuildSteps.Clean</value>
    </valuemap>
    <value type="int" key="ProjectExplorer.BuildConfiguration.BuildStepListCount">2</value>
    <value type="bool" key="ProjectExplorer.BuildConfiguration.ClearSystemEnvironment">false</value>
    <valuelist type="QVariantList" key="ProjectExplorer.BuildConfiguration.UserEnvironmentChanges"/>
    <value type="QString" key="ProjectExplorer.ProjectConfiguration.DefaultDisplayName">Default</value>
    <value type="QString" key="ProjectExplorer.ProjectConfiguration.DisplayName">Release (DRP150)</value>
    <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">GenericProjectManager.GenericBuildConfiguration</value>
   </valuemap>
   <valuemap type="QVariantMap" key="ProjectExplorer.Target.BuildConfiguration.2">
    <value type="QString" key="ProjectExplorer.BuildConfiguration.BuildDirectory">.</value>
    <valuemap type="QVariantMap" key="ProjectExplorer.BuildConfiguration.BuildStepList.0">
     <valuemap type="QVariantMap" key="ProjectExplorer.BuildStepList.Step.0">
      <valuelist type="QVariantList" key="GenericProjectManager.GenericMakeStep.BuildTargets">
       <value type="QString">all</value>
      </valuelist>
      <value type="bool" key="GenericProjectManager.GenericMakeStep.Clean">false</value>
      <value type="QString" key="GenericProjectManager.GenericMakeStep.MakeArguments">PLATFORM=OTHER </value>
      <value type="bool" key="GenericProjectManager.GenericMakeStep.OverrideMakeflags">false</value>
      <value type="bool" key="ProjectExplorer.BuildStep.Enabled">true</value>
      <value type="QString" key="ProjectExplorer.ProjectConfiguration.DefaultDisplayName">Make</value>
      <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">GenericProjectManager.GenericMakeStep</value>
     </valuemap>
     <value type="int" key="ProjectExplorer.BuildStepList.StepsCount">1</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.DefaultDisplayName">Build</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">ProjectExplorer.BuildSteps.Build</value>
    </valuemap>
    <valuemap type="QVariantMap" key="ProjectExplorer.BuildConfiguration.BuildStepList.1">
     <valuemap type="QVariantMap" key="ProjectExplorer.BuildStepList.Step.0">
      <valuelist type="QVariantList" key="GenericProjectManager.GenericMakeStep.BuildTargets">
       <value type="QString">clean</value>
      </valuelist>
      <value type="bool" key="GenericProjectManager.GenericMakeStep.Clean">false</value>
      <value type="bool" key="GenericProjectManager.GenericMakeStep.OverrideMakeflags">false</value>
      <value type="bool" key="ProjectExplorer.BuildStep.Enabled">true</value>
      <value type="QString" key="ProjectExplorer.ProjectConfiguration.DefaultDisplayName">Make</value>
      <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">GenericProjectManager.GenericMakeStep</value>
     </valuemap>
     <value type="int" key="ProjectExplorer.BuildStepList.StepsCount">1</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.DefaultDisplayName">Clean</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">ProjectExplorer.BuildSteps.Clean</value>
    </valuemap>
    <value type="int" key="ProjectExplorer.BuildConfiguration.BuildStepListCount">2</value>
    <value type="bool" key="ProjectExplorer.BuildConfiguration.ClearSystemEnvironment">false</value>
    <valuelist type="QVariantList" key="ProjectExplorer.BuildConfiguration.UserEnvironmentChanges"/>
    <value type="QString" key="ProjectExplorer.ProjectConfiguration.DefaultDisplayName">Default</value>
    <value type="QString" key="ProjectExplorer.ProjectConfiguration.DisplayName">Release (OTHER)</value>
    <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">GenericProjectManager.GenericBuildConfiguration</value>
   </valuemap>
   <value type="int" key="ProjectExplorer.Target.BuildConfigurationCount">3</value>
   <valuemap type="QVariantMap" key="ProjectExplorer.Target.DeployConfiguration.0">
    <valuemap type="QVariantMap" key="ProjectExplorer.BuildConfiguration.BuildStepList.0">
     <value type="int" key="ProjectExplorer.BuildStepList.StepsCount">0</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.DefaultDisplayName">Deploy</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">ProjectExplorer.BuildSteps.Deploy</value>
    </valuemap>
    <value type="int" key="ProjectExplorer.BuildConfiguration.BuildStepListCount">1</value>
    <value type="QString" key="ProjectExplorer.ProjectConfiguration.DefaultDisplayName">Deploy Configuration</value>
    <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">ProjectExplorer.DefaultDeployConfiguration</value>
   </valuemap>
   <value type="int" key="ProjectExplorer.Target.DeployConfigurationCount">1</value>
   <valuemap type="QVariantMap" key="ProjectExplorer.Target.PluginSettings"/>
   <valuemap type="QVariantMap" key="ProjectExplorer.Target.RunConfiguration.0">
    <value type="int" key="PE.EnvironmentAspect.Base">2</value>
    <valuelist type="QVariantList" key="PE.EnvironmentAspect.Changes"/>
    <value type="QString" key="ProjectExplorer.ProjectConfiguration.DefaultDisplayName">Custom Executable</value>
    <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">ProjectExplorer.CustomExecutableRunConfiguration</value>
    <value type="uint" key="RunConfiguration.QmlDebugServerPort">3768</value>
    <value type="bool" key="RunConfiguration.UseCppDebugger">false</value>
    <value type="bool" key="RunConfiguration.UseCppDebuggerAuto">true</value>
    <value type="bool" key="RunConfiguration.UseMultiProcess">false</value>
    <value type="bool" key="RunConfiguration.UseQmlDebugger">false</value>
    <value type="bool" key="RunConfiguration.UseQmlDebuggerAuto">true</value>
   </valuemap>
   <value type="int" key="ProjectExplorer.Target.RunConfigurationCount">1</value>
  </valuemap>
 </data>
 <data>
  <variable>ProjectExplorer.Project.TargetCount</variable>
  <value type="int">1</value>
 </data>
 <data>
  <variable>ProjectExplorer.Project.Updater.FileVersion</variable>
  <value type="int">21</value>
 </data>
 <data>
  <variable>UserStickyKeys</variable>
  <valuelist type="QVariantList">
   <value type="QString">ProjectExplorer.Project.Target.0</value>
   <value type="QString">ProjectExplorer.Project.Target.0/ProjectExplorer.ProjectConfiguration.DefaultDisplayName</value>
   <value type="QString">ProjectExplorer.Project.Target.0/ProjectExplorer.ProjectConfiguration.DisplayName</value>
   <value type="QString">ProjectExplorer.Project.Target.0/ProjectExplorer.ProjectConfiguration.Id</value>
   <value type="QString">ProjectExplorer.Project.Target.0/ProjectExplorer.Target.BuildConfiguration.0</value>
   <value type="QString">ProjectExplorer.Project.Target.0/ProjectExplorer.Target.BuildConfiguration.0/ProjectExplorer.BuildConfiguration.BuildStepList.0</value>
   <value type="QString">ProjectExplorer.Project.Target.0/ProjectExplorer.Target.BuildConfiguration.0/ProjectExplorer.BuildConfiguration.BuildStepList.0/ProjectExplorer.BuildStepList.Step.0</value>
   <value type="QString">ProjectExplorer.Project.Target.0/ProjectExplorer.Target.BuildConfiguration.0/ProjectExplorer.BuildConfiguration.BuildStepList.0/ProjectExplorer.BuildStepList.Step.0/GenericProjectManager.GenericMakeStep.MakeArguments</value>
   <value type="QString">ProjectExplorer.Project.Target.0/ProjectExplorer.Target.BuildConfiguration.0/ProjectExplorer.BuildConfiguration.BuildStepList.1</value>
   <value type="QString">ProjectExplorer.Project.Target.0/ProjectExplorer.Target.BuildConfiguration.0/ProjectExplorer.BuildConfiguration.BuildStepList.1/ProjectExplorer.BuildStepList.Step.0</value>
   <value type="QString">ProjectExplorer.Project.Target.0/ProjectExplorer.Target.BuildConfiguration.0/ProjectExplorer.BuildConfiguration.BuildStepList.1/ProjectExplorer.BuildStepList.Step.0/GenericProjectManager.GenericMakeStep.Clean</value>
   <value type="QString">ProjectExplorer.Project.Target.0/ProjectExplorer.Target.BuildConfiguration.0/ProjectExplorer.ProjectConfiguration.DisplayName</value>
   <value type="QString">ProjectExplorer.Project.Target.0/ProjectExplorer.Target.BuildConfigurationCount</value>
   <value type="QString">ProjectExplorer.Project.Target.0/ProjectExplorer.Target.DeployConfiguration.0</value>
   <value type="QString">ProjectExplorer.Project.Target.0/ProjectExplorer.Target.DeployConfiguration.0/ProjectExplorer.BuildConfiguration.BuildStepList.0</value>
   <value type="QString">ProjectExplorer.Project.Target.0/ProjectExplorer.Target.DeployConfiguration.0/ProjectExplorer.BuildConfiguration.BuildStepList.0/ProjectExplorer.BuildStepList.StepsCount</value>
   <value type="QString">ProjectExplorer.Project.Target.0/ProjectExplorer.Target.DeployConfiguration.0/ProjectExplorer.ProjectConfiguration.DefaultDisplayName</value>
   <value type="QString">ProjectExplorer.Project.Target.0/ProjectExplorer.Target.DeployConfiguration.0/ProjectExplorer.ProjectConfiguration.Id</value>
   <value type="QString">ProjectExplorer.Project.Target.0/ProjectExplorer.Target.RunConfiguration.0</value>
  </valuelist>
 </data>
 <data>
  <variable>Version</variable>
  <value type="int">21</value>
 </data>
</qtcreator>
