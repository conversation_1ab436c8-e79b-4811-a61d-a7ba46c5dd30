#include "platform_socket.h"
#include <platform_socket_def.h>
#include "MmsConst.h"
#include <debug.h>

//Обёртки для самопального TCP/IP стека от Дениса

#define MMS_PORT 102

SOCKET listenSocket;
static sockaddr_in  listenAddr =  {0};

bool socketInit(void)
{
    return TRUE;
}

bool acceptConnection(SOCKET* pConnSocket, struct sockaddr* addr, int *addrlen)
{
    *pConnSocket = accept (listenSocket, addr, addrlen);
    return *pConnSocket != INVALID_SOCKET;
}


int readSocket(SERVER_SOCKET socket, void* buf, int byteCount)
{
    unsigned char* byteBuf = buf;
    while( byteCount )
    {
        int recvCount;
        recvCount = recv(socket, byteBuf, byteCount, 0);
        if (recvCount == SOCKET_ERROR || recvCount <= 0)
        {
            return 0;
        }
        byteCount -= recvCount;
        byteBuf += recvCount;
    }
    return 1;
}

int writeSocket(SERVER_SOCKET socket, void* buf, int byteCount)
{
    int bytesSent;
    bytesSent = send( socket, buf, byteCount, 0 );

    if( bytesSent == SOCKET_ERROR )
    {
        debugSendText("writeSocket ERROR");
        return -1;

    }
    return bytesSent;
}


int startListening(void)
{
    int sockResult;

    listenSocket = socket( AF_INET, SOCK_STREAM, 0 );

    if(listenSocket == INVALID_SOCKET)
    {
        return 0;
    }

    listenAddr.sin_family = AF_INET;
    listenAddr.sin_port = htons(MMS_PORT);
    listenAddr.sin_addr = INADDR_ANY;
    sockResult = bind( listenSocket, (struct sockaddr*)&listenAddr, sizeof(listenAddr) );
    if(sockResult == -1)
    {
        closesocket(listenSocket);
        return 0;
    }

    sockResult = listen( listenSocket, MAX_CONN_COUNT );
    if ( sockResult != 0 )
    {
        closesocket(listenSocket);
        return 0;
    }
    return 1;
}

