#include "rcb.h"
#include "AsnEncoding.h"
#include "IEDCompile/InnerAttributeTypes.h"
#include "iedmodel.h"
#include "iedTree/iedTree.h"
#include "reportItems/RptDataSet.h"
#include "reporter.h"
#include "reports.h"
#include <debug.h>
#include <string.h>

//Получение имени DataSet из атрибута RCB DatSet.
static bool readDatSetAttr(int datSetDAPos, StringView* dataSetFullName)
{
    int innerTypeLen;
    enum InnerAttributeType innerAttrType;
    int namePos;
    uint8_t nameTag;
    int nameLen;
    //Пропускем TL
    int pos = readTL(datSetDAPos, NULL, NULL, NULL);
    //Пропускаем имя
    pos = skipObject(pos);

    //Inner TYPE
    if (iedModel[pos++] != ASN_INTEGER)
    {
        return FALSE;
    }
    pos = BerDecoder_decodeLength(iedModel, &innerTypeLen, pos, iedModelSize);
    if (pos < 1)
    {
        return FALSE;
    }
    innerAttrType = (enum InnerAttributeType)
            BerDecoder_decodeUint32(iedModel, innerTypeLen, pos);
    pos += innerTypeLen;
    if (innerAttrType != INNER_TYPE_CONST)
    {
        return FALSE;
    }

    //Читаем имя DataSet
    //Неплохо бы сделать это через готовую функцию
    namePos = readTL(pos, &nameTag, &nameLen, NULL);

    if (!namePos || nameTag !=(BER_CONTEXT_SPECIFIC | IEC61850_VISIBLE_STRING_64))
    {
        return FALSE;
    }

    StringView_init(dataSetFullName,(const char*)iedModel + namePos, nameLen);

    return TRUE;
}


static bool registerRCBDataset(int rcbPos, PReporter report)
{
    IEDEntity dataSetEntity;

    StringView dataSetFullName;
    int dataSetPos;
    int datSetDAPos = findObjectBySimpleName(rcbPos, "DatSet", 6);
    if (!datSetDAPos)
    {
        ERROR_REPORT("DatSet attr is not found in RCB at pos %04X", rcbPos);
        return FALSE;
    }

    if (!readDatSetAttr(datSetDAPos, &dataSetFullName))
    {
        ERROR_REPORT("Unable to read DatSet attr at pos %04X", datSetDAPos);
        return FALSE;
    }

    dataSetPos = getDataSetByPath(&dataSetFullName);
    if (!dataSetPos)
    {
        ERROR_REPORT("DataSet is not found");
        return FALSE;
    }

    Reporter_setDataSetName(report, &dataSetFullName);

    //Получаем DataSet в IEDTree
    dataSetEntity = IEDTree_findDataSetBySingleName(&dataSetFullName);
    if(dataSetEntity == NULL)
    {
        return false;
    }
    report->dataSetEntity = dataSetEntity;
    report->dataSet = DataSet_getDataSetObj(dataSetEntity);

    if(report->dataSet == NULL)
    {
        ERROR_REPORT("Invalid DataSet");
        return false;
    }

    report->rptDataSet = RptDataSet_create(report);
    if(report->rptDataSet == NULL)
    {
        ERROR_REPORT("Unable to create RptDataSet");
        return false;
    }

    return true;
}

bool registerRptID(int rcbPos, RCB* report)
{
    int rptIDLen;
    int rptIDvalPos;
    int pos;
    uint8_t tag;
    int rptIDDAPos = findObjectBySimpleName(rcbPos, "RptID", 5);
    RET_IF_NOT(rptIDDAPos, "RptIDPos attr is not found in RCB at pos %04X", rcbPos);
    pos = readTL(rptIDDAPos, &tag, NULL, NULL);
    RET_IF_NOT(pos, "Error reading RptDI at %d", rptIDDAPos);
    //Skip name
    pos = skipObject(pos);
    RET_IF_NOT(pos, "Error reading RptDI at %d", rptIDDAPos);
    //Skip Inner Attribute Type
    pos = skipObject(pos);
    RET_IF_NOT(pos, "Error reading RptDI at %d", rptIDDAPos);
    //pos указывает на константу RptID
    rptIDvalPos = readTL(pos, &tag, &rptIDLen, NULL);
    RET_IF_NOT(pos, "Error reading value RptDI at %d", pos);


    StringView_init(&report->rptID,
        (const char*)iedModel + rptIDvalPos, rptIDLen);    
    return TRUE;
}

bool registerTrgOps(int rcbPos, RCB* rcb)
{
    int trgOps;
    enum InnerAttributeType attrType;
    //По 7.2 этот атрибут должен называться TrgOp, но по факту везде TrgOps
    int trgOpsPos = getDAValuePos(rcbPos, "TrgOps", 6, &attrType);
    RET_IF_NOT(trgOpsPos, "Error reading TrgOps");
    trgOps = BerDecoder_DecodeBitStringTLToInt(iedModel, trgOpsPos);
    RET_IF_NOT(trgOpsPos >= 0, "Error decoding TrgOps");
    rcb->trgOps = trgOps;
    return TRUE;
}

bool registerConfRev(int rcbPos, RCB* pRCB)
{
    uint8_t tag;
    int len;
    int confRev;
    enum InnerAttributeType attrType;
    int confRevPos = getDAValuePos(rcbPos, "ConfRev", 7, &attrType);
    RET_IF_NOT(confRevPos, "Error reading ConfRev");
    confRevPos = readTL(confRevPos, &tag, &len, NULL);
    RET_IF_NOT(confRevPos, "Error reading ConfRev");
    confRev = BerDecoder_decodeUint32(iedModel, len, confRevPos);
    pRCB->confRev = confRev;
    return TRUE;
}


bool registerOptFlds(int rcbPos, RCB* report)
{
    int optFlds;
    enum InnerAttributeType attrType;
    int optFldsPos = getDAValuePos(rcbPos, "OptFlds", 7, &attrType);
    RET_IF_NOT(optFldsPos, "Error reading OptFlds");
    optFlds = BerDecoder_DecodeBitStringTLToInt(iedModel, optFldsPos);
    RET_IF_NOT(optFldsPos >= 0, "Error decoding OptFlds");
    report->optFlds = optFlds & SUPPORTED_OPTFLDS;
    return TRUE;
}

void registerReport(int rcbPos, bool buffered)
{
    PReporter pReporter = getFreeReport();
    RCB* pRCB = &pReporter->rcb;
    if (pReporter == NULL)
    {
        ERROR_REPORT("Unable to register report: too many reports");
        return;
    }

    pReporter->rcb.buffered = buffered;
    pReporter->rcb.rptEna = FALSE;
    pReporter->rcb.resv = false;
    pReporter->sessionOutBuffer.busy = FALSE;
    pReporter->connection = NULL;
    pReporter->intgPdCounter = 0;
    pReporter->intgTimerAlam = false;
    pRCB->confRev = 0;
    pRCB->sqNum = 1;
    pRCB->entryID = 0;
    pRCB->intgPd = 0;

    if (!registerRptID(rcbPos, pRCB))
    {
        return;
    }

    if (!registerRCBDataset(rcbPos, pReporter))
    {
        return;
    }

    if (!registerConfRev(rcbPos, pRCB))
    {
        return;
    }

    if (!registerTrgOps(rcbPos, pRCB))
    {
        return;
    }

    if (!registerOptFlds(rcbPos, pRCB))
    {
        return;
    }

    if(!initReportCompareDataset(pReporter))
    {
        return;
    }
	    
    finalizeReportRegistration();
    if (buffered)
    {
        ReportQueue_init(&(pReporter->buffer));
    }
}

void registerBufferedReport(int rcbPos)
{
    TRACE("Buffered report. Pos: %04X", rcbPos);
    registerReport(rcbPos, TRUE);
}

void registerUnbufferedReport(int rcbPos)
{
    TRACE("Unbuffered report. Pos: %04X", rcbPos);
    registerReport(rcbPos, FALSE);
}

// Регистрирует все RCB, которые найдёт в указанном объекте FC
// В зависимости от имени FC регистрирует buffered, unbuffered,
// или вообще никакие RCB (если имя FC не "BR" и не "RP")
void registerRCBsGivenFC(int fcPos)
{
    StringView fcName;

    if(!getObjectName(fcPos, &fcName))
    {
        ERROR_REPORT("Unable to read FC name");
        return;
    }
    if(fcName.len != 2)
    {
        ERROR_REPORT("Invalid FC name");
        return;
    }
    if ( memcmp("RP", fcName.p, 2) == 0)
    {
        //Unbuffered reports
        processSubobjects(fcPos, registerUnbufferedReport);
    }
    else if( memcmp("BR", fcName.p, 2) == 0)
    {
        //Buffered reports
        processSubobjects(fcPos, registerBufferedReport);
    }
}

void registerAllLogicalNodeRCB(int lnPos)
{
    processSubobjects(lnPos, registerRCBsGivenFC);
}

void registerAllLogicalDeviceRCB(int ldPos)
{
    int dataSectionPos;

    dataSectionPos = findObjectByTag(ldPos, IED_VMD_DATA_SECTION);
    if(!dataSectionPos)
    {
        ERROR_REPORT("Data section is not found");
        return;
    }

    processSubobjects(dataSectionPos, registerAllLogicalNodeRCB);
}

void registerAllRCB(void)
{
    processSubobjects(0, registerAllLogicalDeviceRCB);
}

