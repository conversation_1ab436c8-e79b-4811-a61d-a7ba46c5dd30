                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2b01.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=session.c -o gh_2b01.o -list=session.lst C:\Users\<USER>\AppData\Local\Temp\gh_2b01.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_2b01.s
Source File: session.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile session.c -o

                      10 ;		session.o

                      11 ;Source File:   session.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:55:59 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include <debug.h>


                      21 ;2: #include "session.h"


                      22 ;3: 


                      23 ;4: #include <string.h>


                      24 ;5: 


                      25 ;6: #define SESSION_CONNECT_CODE	0x0d	//Соединение


                      26 ;7: #define SESSION_ACCEPT_CODE 0x0e	//Прием


                      27 ;8: #define SESSION_DATA_CODE 0x01	//Передача данных


                      28 ;9: #define SESSION_ABORT_CODE 0x19	//Прерывание активности


                      29 ;10: 


                      30 ;11: #define USER_DATA_PARAMETER 0xC1


                      31 ;12: #define PROTOCOL_OPTIONS_PARAMETER 0x13


                      32 ;13: #define VERSION_NUMBER_PARAMETER 0x16


                      33 ;14: 


                      34 ;15: static const unsigned char dataSpdu[] = { 0x01, 0x00, 0x01, 0x00 };


                      35 ;16: 


                      36 ;17: static int encodeConnectAcceptItem(unsigned char* buf, int offset)


                      37 

                      38 ;28: }


                      39 

                      40 ;29: 


                      41 ;30: 


                      42 ;31: static int encodeSessionRequirement(unsigned char* buf, int offset)


                      43 

                      44 ;38: }


                      45 

                      46 ;39: 


                      47 ;40: 


                      48 ;41: IsoSessionIndication parseSessionMessage(unsigned char* message, int inLen,


                      49 	.text

                      50 	.align	4


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2b01.s
                      51 parseSessionMessage::

00000000 e92d4030     52 	stmfd	[sp]!,{r4-r5,lr}

                      53 ;42:                                          unsigned char** pUserData, int* pUserDataSize)


                      54 ;43: {


                      55 

                      56 ;44:     unsigned char id;


                      57 ;45:     unsigned char headerLen;


                      58 ;46:     if(inLen <= 1)


                      59 

00000004 e3510001     60 	cmp	r1,1

00000008 da000017     61 	ble	.L50

                      62 ;47:     {


                      63 

                      64 ;48:         return SESSION_ERROR;


                      65 

                      66 ;49:     }


                      67 ;50:     id = message[0];


                      68 

0000000c e5d0c000     69 	ldrb	r12,[r0]

                      70 ;51:     headerLen = message[1];


                      71 

00000010 e5d05001     72 	ldrb	r5,[r0,1]

                      73 ;52: 


                      74 ;53:     switch (id) {


                      75 

00000014 e25c4001     76 	subs	r4,r12,1

00000018 0a000005     77 	beq	.L46

0000001c e354000c     78 	cmp	r4,12

00000020 1a000013     79 	bne	.L56

                      80 ;54:     case SESSION_CONNECT_CODE:


                      81 ;55:         debugSendText("Received Session connect message");


                      82 

00000024 e28f0000*    83 	adr	r0,.L179

00000028 eb000000*    84 	bl	debugSendText

                      85 ;56:         return SESSION_CONNECT;


                      86 

0000002c e3a00002     87 	mov	r0,2

00000030 ea000013     88 	b	.L37

                      89 .L46:

                      90 ;57:     case SESSION_DATA_CODE:


                      91 ;58:         if (inLen < 4)


                      92 

00000034 e3510004     93 	cmp	r1,4

00000038 ba00000b     94 	blt	.L50

                      95 ;59:         {


                      96 

                      97 ;60:             return SESSION_ERROR;


                      98 

                      99 ;61:         }


                     100 ;62:         if ((headerLen == 0) && (message[2] == 1) && (message[3] == 0))


                     101 

0000003c e3550000    102 	cmp	r5,0

00000040 05d0c002    103 	ldreqb	r12,[r0,2]

00000044 035c0001    104 	cmpeq	r12,1

00000048 05d0c003    105 	ldreqb	r12,[r0,3]

0000004c 035c0000    106 	cmpeq	r12,0

00000050 1a000005    107 	bne	.L50

                     108 ;63:         {


                     109 

                     110 ;64:             *pUserData = message + 4;


                     111 


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2b01.s
00000054 e2800004    112 	add	r0,r0,4

00000058 e5820000    113 	str	r0,[r2]

                     114 ;65:             *pUserDataSize = inLen -4;


                     115 

0000005c e2410004    116 	sub	r0,r1,4

00000060 e5830000    117 	str	r0,[r3]

                     118 ;66:             return SESSION_DATA;


                     119 

00000064 e3a00004    120 	mov	r0,4

00000068 ea000005    121 	b	.L37

                     122 .L50:

                     123 ;67:         }


                     124 ;68:         return SESSION_ERROR;        


                     125 

0000006c e3a00001    126 	mov	r0,1

00000070 ea000003    127 	b	.L37

                     128 .L56:

                     129 ;69:     default:


                     130 ;70:         debugSendUshort("Unknown session message ID:", id);


                     131 

00000074 e28f0000*   132 	adr	r0,.L180

00000078 e1a0100c    133 	mov	r1,r12

0000007c eb000000*   134 	bl	debugSendUshort

                     135 ;71:         break;


                     136 ;72:     }


                     137 ;73:     return SESSION_ERROR;


                     138 

00000080 e3a00001    139 	mov	r0,1

                     140 .L37:

00000084 e8bd8030    141 	ldmfd	[sp]!,{r4-r5,pc}

                     142 	.endf	parseSessionMessage

                     143 	.align	4

                     144 ;id	r12	local

                     145 ;headerLen	r5	local

                     146 ;.L153	.L158	static

                     147 ;.L154	.L157	static

                     148 

                     149 ;message	r0	param

                     150 ;inLen	r1	param

                     151 ;pUserData	r2	param

                     152 ;pUserDataSize	r3	param

                     153 

                     154 	.section ".bss","awb"

                     155 .L152:

                     156 	.data

                     157 	.text

                     158 

                     159 ;74: }


                     160 

                     161 ;75: 


                     162 ;76: int createAcceptSPDU( unsigned char* buf, unsigned char* userData, int userDataLen)


                     163 	.align	4

                     164 	.align	4

                     165 createAcceptSPDU::

00000088 e92d4030    166 	stmfd	[sp]!,{r4-r5,lr}

                     167 ;77: {


                     168 

                     169 ;78:     int offset = 0;


                     170 

                     171 ;79: 


                     172 ;80:     buf[offset++] = SESSION_ACCEPT_CODE;



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2b01.s
                     173 

0000008c e3a0c005    174 	mov	r12,5

00000090 e1a04000    175 	mov	r4,r0

00000094 e5c4c002    176 	strb	r12,[r4,2]

                     177 ;20:     buf[offset++] = 6;


                     178 

00000098 e3a0c006    179 	mov	r12,6

0000009c e5c4c003    180 	strb	r12,[r4,3]

                     181 ;21:     buf[offset++] = PROTOCOL_OPTIONS_PARAMETER;


                     182 

000000a0 e3a0c013    183 	mov	r12,19

000000a4 e5c4c004    184 	strb	r12,[r4,4]

                     185 ;22:     buf[offset++] = 1;


                     186 

000000a8 e3a0c001    187 	mov	r12,1

000000ac e5c4c005    188 	strb	r12,[r4,5]

                     189 ;23:     buf[offset++] = 0; //options


                     190 

000000b0 e5c4c008    191 	strb	r12,[r4,8]

                     192 ;26:     buf[offset++] = 2; /* Version = 2 */


                     193 

000000b4 e3a0c002    194 	mov	r12,2

000000b8 e5c4c009    195 	strb	r12,[r4,9]

                     196 ;27:     return offset;


                     197 

                     198 ;84: 


                     199 ;85:     offset = encodeSessionRequirement(buf, offset);


                     200 

                     201 ;32: {


                     202 

                     203 ;33:     buf[offset++] = 0x14;


                     204 

000000bc e5c4c00b    205 	strb	r12,[r4,11]

                     206 ;35:     buf[offset++] = 0;//(uint8_t) (self->sessionRequirement / 0x100);


                     207 

000000c0 e5c4c00d    208 	strb	r12,[r4,13]

                     209 ;37:     return offset;


                     210 

                     211 ;86:     //offset = encodeCalledSessionSelector(self, buf, offset);


                     212 ;87: 


                     213 ;88:     buf[offset++] = USER_DATA_PARAMETER;


                     214 

000000c4 e3a03016    215 	mov	r3,22

000000c8 e5c43007    216 	strb	r3,[r4,7]

                     217 ;25:     buf[offset++] = 1;


                     218 

000000cc e3a03014    219 	mov	r3,20

000000d0 e5c4300a    220 	strb	r3,[r4,10]

                     221 ;34:     buf[offset++] = 2;


                     222 

000000d4 e3a030c1    223 	mov	r3,193

000000d8 e5c4300e    224 	strb	r3,[r4,14]

                     225 ;89:     buf[offset++] = userDataLen;


                     226 

000000dc e5c4200f    227 	strb	r2,[r4,15]

                     228 ;90:     memcpy(buf + offset, userData, userDataLen);


                     229 

000000e0 e1a05002    230 	mov	r5,r2

000000e4 e3a0000e    231 	mov	r0,14

000000e8 e5c40000    232 	strb	r0,[r4]

                     233 ;81:     offset++;//Пропускаем длину



                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2b01.s
                     234 

                     235 ;82: 


                     236 ;83:     offset = encodeConnectAcceptItem(buf, offset);    


                     237 

                     238 ;18: {


                     239 

                     240 ;19:     buf[offset++] = 5;


                     241 

000000ec e3a00000    242 	mov	r0,0

000000f0 e5c40006    243 	strb	r0,[r4,6]

                     244 ;24:     buf[offset++] = VERSION_NUMBER_PARAMETER; /* Version Number */


                     245 

000000f4 e5c4000c    246 	strb	r0,[r4,12]

                     247 ;36:     buf[offset++] = 2;//(uint8_t) (self->sessionRequirement & 0x00ff);


                     248 

000000f8 e2840010    249 	add	r0,r4,16

000000fc eb000000*   250 	bl	memcpy

                     251 ;91:     offset += userDataLen;


                     252 

                     253 ;92: 


                     254 ;93:     buf[1] = offset - 2;//Длина


                     255 

00000100 e285000e    256 	add	r0,r5,14

00000104 e5c40001    257 	strb	r0,[r4,1]

                     258 ;94: 


                     259 ;95:     return offset;


                     260 

00000108 e2850010    261 	add	r0,r5,16

0000010c e8bd8030    262 	ldmfd	[sp]!,{r4-r5,pc}

                     263 	.endf	createAcceptSPDU

                     264 	.align	4

                     265 ;offset	r0	local

                     266 ;offset	r3	local

                     267 ;offset	r3	local

                     268 

                     269 ;buf	r4	param

                     270 ;userData	none	param

                     271 ;userDataLen	r5	param

                     272 

                     273 	.section ".bss","awb"

                     274 .L206:

                     275 	.data

                     276 	.text

                     277 

                     278 ;96: }


                     279 

                     280 ;97: 


                     281 ;98: int isoSession_createDataSpdu(unsigned char* buf, int bufSize,


                     282 	.align	4

                     283 	.align	4

                     284 	.align	4

                     285 isoSession_createDataSpdu::

00000110 e92d4070    286 	stmfd	[sp]!,{r4-r6,lr}

00000114 e1a05000    287 	mov	r5,r0

00000118 e1a04003    288 	mov	r4,r3

                     289 ;99:                                unsigned char* userData, int userDataLen)


                     290 ;100: {


                     291 

                     292 ;101: 	if (SESION_DATA_PACKET_HEADER_SIZE + userDataLen > bufSize)


                     293 

0000011c e2840004    294 	add	r0,r4,4


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2b01.s
00000120 e1500001    295 	cmp	r0,r1

                     296 ;102: 	{


                     297 

                     298 ;103: 		ERROR_REPORT("Session buffer overflow");


                     299 ;104: 		return 0;


                     300 

00000124 c3a00000    301 	movgt	r0,0

00000128 ca000009    302 	bgt	.L213

0000012c e59f1064*   303 	ldr	r1,.L261

00000130 e1a00005    304 	mov	r0,r5

00000134 e1a06002    305 	mov	r6,r2

                     306 ;105: 	}


                     307 ;106: 


                     308 ;107:     memcpy( buf, dataSpdu, SESION_DATA_PACKET_HEADER_SIZE );


                     309 

00000138 e3a02004    310 	mov	r2,4

0000013c eb000000*   311 	bl	memcpy

                     312 ;108:     buf += SESION_DATA_PACKET_HEADER_SIZE;


                     313 

00000140 e2850004    314 	add	r0,r5,4

                     315 ;109:     memcpy( buf, userData, userDataLen );


                     316 

00000144 e1a02004    317 	mov	r2,r4

00000148 e1a01006    318 	mov	r1,r6

0000014c eb000000*   319 	bl	memcpy

                     320 ;110:     return SESION_DATA_PACKET_HEADER_SIZE + userDataLen;


                     321 

00000150 e2840004    322 	add	r0,r4,4

                     323 .L213:

00000154 e8bd8070    324 	ldmfd	[sp]!,{r4-r6,pc}

                     325 	.endf	isoSession_createDataSpdu

                     326 	.align	4

                     327 

                     328 ;buf	r5	param

                     329 ;bufSize	r1	param

                     330 ;userData	r6	param

                     331 ;userDataLen	r4	param

                     332 

                     333 	.section ".bss","awb"

                     334 .L246:

                     335 	.section ".rodata","a"

                     336 .L247:

00000000 01         337 dataSpdu:	.data.b	1

00000001 00         338 	.space	1

00000002 01         339 	.data.b	1

00000003 00         340 	.space	1

                     341 	.type	dataSpdu,$object

                     342 	.size	dataSpdu,4

                     343 	.data

                     344 	.text

                     345 

                     346 ;111: }


                     347 	.align	4

                     348 .L179:

                     349 ;	"Received Session connect message\000"

00000158 65636552    350 	.data.b	82,101,99,101

0000015c 64657669    351 	.data.b	105,118,101,100

00000160 73655320    352 	.data.b	32,83,101,115

00000164 6e6f6973    353 	.data.b	115,105,111,110

00000168 6e6f6320    354 	.data.b	32,99,111,110

0000016c 7463656e    355 	.data.b	110,101,99,116


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2b01.s
00000170 73656d20    356 	.data.b	32,109,101,115

00000174 65676173    357 	.data.b	115,97,103,101

00000178 00         358 	.data.b	0

00000179 000000     359 	.align 4

                     360 

                     361 	.type	.L179,$object

                     362 	.size	.L179,4

                     363 

                     364 .L180:

                     365 ;	"Unknown session message ID:\000"

0000017c 6e6b6e55    366 	.data.b	85,110,107,110

00000180 206e776f    367 	.data.b	111,119,110,32

00000184 73736573    368 	.data.b	115,101,115,115

00000188 206e6f69    369 	.data.b	105,111,110,32

0000018c 7373656d    370 	.data.b	109,101,115,115

00000190 20656761    371 	.data.b	97,103,101,32

00000194 003a4449    372 	.data.b	73,68,58,0

                     373 	.align 4

                     374 

                     375 	.type	.L180,$object

                     376 	.size	.L180,4

                     377 

                     378 .L261:

00000198 00000000*   379 	.data.w	.L247

                     380 	.type	.L261,$object

                     381 	.size	.L261,4

                     382 

                     383 	.align	4

                     384 ;dataSpdu	.L247	static

                     385 

                     386 	.data

                     387 .L276:

                     388 	.globl	g_DataSpdu

00000000 01         389 g_DataSpdu:	.data.b	1

00000001 00         390 	.space	1

00000002 01         391 	.data.b	1

00000003 00         392 	.space	1

                     393 	.type	g_DataSpdu,$object

                     394 	.size	g_DataSpdu,4

                     395 	.ghsnote version,6

                     396 	.ghsnote tools,3

                     397 	.ghsnote options,0

                     398 	.text

                     399 	.align	4

                     400 	.data

                     401 	.align	4

                     402 	.section ".rodata","a"

                     403 	.align	4

                     404 	.text

