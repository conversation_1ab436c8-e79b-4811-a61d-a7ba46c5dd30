#pragma once

#include <stdint.h>
#include <stdbool.h>

//Проверка кодировки

//! Какую функцию вызывать каждую миллисекунду
//! Для T0/T1 GOOSE
void Timers_setGoose1msCallBack(void(*func)(void));

//! Какую функцию вызывать каждую миллисекунду
//! Для Integrity в отчётах
void Timers_setIntegrity1msCallBack(void(*func)(void));

//! Какую функцию вызывать каждую миллисекунду
//! Для проверки состояния шины
void Timers_setNetBusChek1msCallback(void(*func)(void));

void Timers_init(void);

uint32_t Timers_getTickCount(void);

bool Timers_isTimeout(uint32_t startTime, uint32_t timeout);
