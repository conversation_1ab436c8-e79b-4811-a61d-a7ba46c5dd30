#include "iedEntity.h"

#include "..\bufView.h"

#include <stdbool.h>


bool IEDControlDO_init(IEDEntity entity);

bool IEDControlDA_init(IEDEntity entity, BufferView *ctrlInfoBER);
bool IEDControlDA_isControlDA(IEDEntity entity);
bool IEDControlDA_isReady(IEDEntity controlDA);
MmsDataAccessError IEDControlDA_write(IEDEntity entity,
	IsoConnection* isoConn, BufferView* value);


bool IEDOrIdent_init(IEDEntity entity);
bool IEDOrIdent_calcReadLen(IEDEntity entity, size_t *pLen);
bool IEDOrIdent_encodeRead(IEDEntity entity, BufferView *outBuf);
MmsDataAccessError IEDOrIdent_write(IEDEntity entity,
	IsoConnection* isoConn, BufferView* value);

bool IEDOrCat_init(IEDEntity entity);
bool IEDOrCat_calcReadLen(IEDEntity entity, size_t *pLen);
bool IEDOrCat_encodeRead(IEDEntity entity, BufferView *outBuf);
MmsDataAccessError IEDOrCat_write(IEDEntity entity,
	IsoConnection* isoConn, BufferView* value);

//!!! Временно работает как констата с разрешённой бессмысленной записью
bool IEDCtlNum_init(IEDEntity entity,BufferView* ber);

