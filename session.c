#include <debug.h>
#include "session.h"

#include <string.h>

#define SESSION_CONNECT_CODE	0x0d	//Соединение
#define SESSION_ACCEPT_CODE 0x0e	//Прием
#define SESSION_DATA_CODE 0x01	//Передача данных
#define SESSION_ABORT_CODE 0x19	//Прерывание активности

#define USER_DATA_PARAMETER 0xC1
#define PROTOCOL_OPTIONS_PARAMETER 0x13
#define VERSION_NUMBER_PARAMETER 0x16

static const unsigned char dataSpdu[] = { 0x01, 0x00, 0x01, 0x00 };

static int encodeConnectAcceptItem(unsigned char* buf, int offset)
{
    buf[offset++] = 5;
    buf[offset++] = 6;
    buf[offset++] = PROTOCOL_OPTIONS_PARAMETER;
    buf[offset++] = 1;
    buf[offset++] = 0; //options
    buf[offset++] = VERSION_NUMBER_PARAMETER; /* Version Number */
    buf[offset++] = 1;
    buf[offset++] = 2; /* Version = 2 */
    return offset;
}


static int encodeSessionRequirement(unsigned char* buf, int offset)
{
    buf[offset++] = 0x14;
    buf[offset++] = 2;
    buf[offset++] = 0;//(uint8_t) (self->sessionRequirement / 0x100);
    buf[offset++] = 2;//(uint8_t) (self->sessionRequirement & 0x00ff);
    return offset;
}


IsoSessionIndication parseSessionMessage(unsigned char* message, int inLen,
                                         unsigned char** pUserData, int* pUserDataSize)
{
    unsigned char id;
    unsigned char headerLen;
    if(inLen <= 1)
    {
        return SESSION_ERROR;
    }
    id = message[0];
    headerLen = message[1];

    switch (id) {
    case SESSION_CONNECT_CODE:
        debugSendText("Received Session connect message");
        return SESSION_CONNECT;
    case SESSION_DATA_CODE:
        if (inLen < 4)
        {
            return SESSION_ERROR;
        }
        if ((headerLen == 0) && (message[2] == 1) && (message[3] == 0))
        {
            *pUserData = message + 4;
            *pUserDataSize = inLen -4;
            return SESSION_DATA;
        }
        return SESSION_ERROR;        
    default:
        debugSendUshort("Unknown session message ID:", id);
        break;
    }
    return SESSION_ERROR;
}

int createAcceptSPDU( unsigned char* buf, unsigned char* userData, int userDataLen)
{
    int offset = 0;

    buf[offset++] = SESSION_ACCEPT_CODE;
    offset++;//Пропускаем длину

    offset = encodeConnectAcceptItem(buf, offset);    

    offset = encodeSessionRequirement(buf, offset);
    //offset = encodeCalledSessionSelector(self, buf, offset);

    buf[offset++] = USER_DATA_PARAMETER;
    buf[offset++] = userDataLen;
    memcpy(buf + offset, userData, userDataLen);
    offset += userDataLen;

    buf[1] = offset - 2;//Длина

    return offset;
}

int isoSession_createDataSpdu(unsigned char* buf, int bufSize,
                               unsigned char* userData, int userDataLen)
{
	if (SESION_DATA_PACKET_HEADER_SIZE + userDataLen > bufSize)
	{
		ERROR_REPORT("Session buffer overflow");
		return 0;
	}

    memcpy( buf, dataSpdu, SESION_DATA_PACKET_HEADER_SIZE );
    buf += SESION_DATA_PACKET_HEADER_SIZE;
    memcpy( buf, userData, userDataLen );
    return SESION_DATA_PACKET_HEADER_SIZE + userDataLen;
}

#pragma alignvar (4)
unsigned char g_DataSpdu[] = { 0x01, 0x00, 0x01, 0x00 };
