#include "mms_fs.h"
#include "stringView.h"

#include "BaseAsnTypes.h"
#include "AsnEncoding.h"
#include "file_system.h"
#include "tools.h"
#include "local_types.h"
#include "bufViewBER.h"
#include <stddef.h>
#include <stdio.h>
#include "timetools.h"
//Файловые операции MMS

#define MMS_DIRECTORY_NAME 0xA0
#define MMS_FILE_NAME 0xA1

//GeneralizedTime
// год, месяц(1 - 12), число(1 - 32), часы, минуты, cекунды, точка, доли секунды, Z
// strlen("19851106210627.123Z") + 1 = 20
#define GENERZLISED_TIME_SIZE	(sizeof(GeneralizedTime_t) -1 ) // размер без '0'
typedef char GeneralizedTime_t[20];


// Информация о длине закодированных атрибутов файла
typedef struct {
    size_t timeLen;
    size_t sizeLen;
    //Полный размер закодированных атрибутов файла включая тэг и длину
    size_t fullAttrLen;//A1
}  EncodedAttrLen;

// Информация для записи заголовка ответа на файловую операцию
typedef struct {
    uint32_t invokeID;
    //Двухбайтный тэг big endian
    uint16_t extTag;
    //Размер всего ответа внутри тэга A1, включая invokeID
    size_t responseLen;
    //Размер данных внутри расширенного тэга (BF 4X)
    size_t dataLen;
} FileRespHeadInfo;



static void determineEncodedAttrLen(EncodedAttrLen* encAttrLen, size_t fileSize)
{
    encAttrLen->timeLen = 2 //тэг + длина
        + GENERZLISED_TIME_SIZE;
    encAttrLen->sizeLen = 2 //тэг + длина
        + BerEncoder_UInt32determineEncodedSize(fileSize);
    encAttrLen->fullAttrLen = BerEncoder_determineFullObjectSize(
        encAttrLen->sizeLen + encAttrLen->timeLen);
}

// Определяет размер ответа на операцию с файлом,
// включая размер данных, двухбайтный тэг, invokeID.
// Исключая корневой тэг(A1) и его размер.
static size_t determineResponseLen(uint32_t invokeID, size_t responseDataLen)
{
    return BerEncoder_UInt32determineEncodedSize(invokeID)
        + 2 //Тэг и длина InvokeID
        + BerEncoder_determineFullObjectSize(responseDataLen)
        + 1; //Ещё один байт поскольку тэг двухбайтовый (BF 4D)
}

static bool makeGeneralizedTime(FSFileAttr* attr, GeneralizedTime_t generalizedTime)
{
    struct tm tmTime;
    __time32_t t = attr->time;

    if (!TimeTools_gmtime32(&tmTime, &t))
    {
        return false;
    }

    tmTime.tm_year += 1900;
    tmTime.tm_mon += 1;
    snprintf(generalizedTime, sizeof(GeneralizedTime_t),
        "%d%02d%02d%02d%02d%02d.%03dZ", // 19851106210627.300Z
        tmTime.tm_year, tmTime.tm_mon, tmTime.tm_mday,
        tmTime.tm_hour, tmTime.tm_min, tmTime.tm_sec, attr->ms);

    return true;
}
static bool encodeFileAttr(BufferView* outBuf, EncodedAttrLen* encAttrLen,
    FSFileAttr* attr)
{
    GeneralizedTime_t generalizedTime;
    //Атрибуты
    if (!BufferView_encodeTL(outBuf, 0xA1,
        encAttrLen->sizeLen + encAttrLen->timeLen))
    {
        return FALSE;
    }
    //Размер
    if (!BufferView_encodeUInt32(outBuf, 0x80, attr->fileSize))
    {
        return FALSE;
    }

    //Время
    if (!makeGeneralizedTime(attr, generalizedTime))
    {
        return FALSE;
    }

    if (!BufferView_encodeOctetString(outBuf, 0x81, generalizedTime,
        GENERZLISED_TIME_SIZE))
    {
        return FALSE;
    }
    return TRUE;
}

static bool encodeFileInfo(FSFindData* fileInfo, BufferView* outBuf)
{
    /*
        30
            A0
                19 Имя файла
            A1
                80 Размер
                81 Время
    */

    EncodedAttrLen encAttrLen;
    size_t encodedNameStringLen;//19
    size_t encodedNameLen;//A0


    //Определяем размеры
    determineEncodedAttrLen(&encAttrLen, fileInfo->attr.fileSize);
    encodedNameStringLen = BerEncoder_determineFullObjectSize(
        fileInfo->fileName.len);
    encodedNameLen = BerEncoder_determineFullObjectSize(encodedNameStringLen);

    //Кодируем
    //Sequence
    if (!BufferView_encodeTL(
        outBuf, ASN_SEQUENCE, encodedNameLen + encAttrLen.fullAttrLen))
    {
        return FALSE;
    }
    //Имя файла

    if (!BufferView_encodeTL(outBuf, 0xA0, encodedNameStringLen))
    {
        return FALSE;
    }

    if (!BufferView_encodeStringView(outBuf, ASN_GRAPHIC_STRING,
        &fileInfo->fileName))
    {
        return FALSE;
    }

    //Атрибуты
    if (!encodeFileAttr(outBuf, &encAttrLen, &fileInfo->attr))
    {
        return FALSE;
    }
    return TRUE;
}

static bool encodeFileResponseHead(BufferView* outBuf,
    FileRespHeadInfo* headerInfo)
{
    if (!BufferView_encodeTL(outBuf, 0xA1, headerInfo->responseLen))
    {
        return FALSE;
    }
    //InvokeID
    if (!BufferView_encodeUInt32(outBuf, ASN_INTEGER, headerInfo->invokeID))
    {
        return FALSE;
    }
    if (!BufferView_encodeExtTL(outBuf, headerInfo->extTag,
        headerInfo->dataLen))
    {
        return FALSE;
    }
    return TRUE;
}

//
static bool fileList(MmsConnection* mmsConn, StringView* dirName,
    StringView* startFileName, BufferView* outBuf, bool* moreFollows)
{
    FSFindData fileInfo;
    BufferView nameBuf;
    FNameErrCode findResult;
    size_t oldOutBufPos;
    //Инициализируем буфер имени
    BufferView_init(&nameBuf, mmsConn->fileName, FILE_NAME_BUF_SIZE, 0);

    findResult = fs_findFirst(dirName, startFileName, &fileInfo, &nameBuf);
    while (findResult != FNAME_NOT_FOUND)
    {
        if (findResult == FNAME_BUF_ERROR)
        {
            ERROR_REPORT("The file name does not fit in the buffer");
            fs_findClose(&fileInfo);
            return findResult;
        }

        if (findResult == FNAME_ERROR)
        {
            ERROR_REPORT("Unknown error");
            fs_findClose(&fileInfo);
            return findResult;
        }

        //encodeFileInfo пишет инфу в буфер. Если инфа не влезла,
        //то надо откатить буфер к состоянию до вызова, и вернуть More Follows
        oldOutBufPos = outBuf->pos;
        if (!encodeFileInfo(&fileInfo, outBuf))
        {
            fs_findClose(&fileInfo);
            //Восстанавливаем позицию буфера до вызова
            outBuf->pos = oldOutBufPos;
            *moreFollows = TRUE;
            return TRUE;
        }

        //Освобождаем буфер имени
        nameBuf.pos = 0;

        findResult = fs_findNext(&fileInfo, &nameBuf);
    }

    fs_findClose(&fileInfo);
    *moreFollows = FALSE;
    return TRUE;
}

// Создаёт ответ на запрос списка файлов
static bool encodeFileDirRequest(MmsConnection* mmsConn, uint32_t invokeID,
    StringView* dirName, StringView* startFileName, BufferView* outBuf)
{
    /*
        A1
            02 InvokeID
            BF 4D
                A0
                    30
                        Список
                More follows
    */

    bool moreFollows;
    BufferView fileListBuf;
    size_t fileListSeqSize;
    FileRespHeadInfo headInfo;

    //Получаем закодированый список файлов с атрибутами
    BufferView_init(&fileListBuf, mmsConn->fileBuf, FILE_BUF_SIZE, 0);
    if (!fileList(mmsConn, dirName, startFileName, &fileListBuf, &moreFollows))
    {
        return FALSE;
    }

    //===================Определяем размеры=====================
    // Размер списка файлов с размером и тэгом SEQUENCE(0x30) для тэга 0xA0
    fileListSeqSize = BerEncoder_determineFullObjectSize(fileListBuf.pos);

    // Полный размер списка с размером и тэгом A0 и флагом More Follows
    // для тэга BF 4D
    headInfo.dataLen = BerEncoder_determineFullObjectSize(fileListSeqSize)
        + (moreFollows ? 3 : 0);

    //Размер ответа, включая InvokeID для тэга A1
    headInfo.responseLen = determineResponseLen(invokeID, headInfo.dataLen);

    //====================Кодируем================================
    headInfo.extTag = 0xBF4D;
    headInfo.invokeID = invokeID;

    if (!encodeFileResponseHead(outBuf, &headInfo))
    {
        return FALSE;
    }

    if (!BufferView_encodeTL(outBuf, 0xA0, fileListSeqSize))
    {
        return FALSE;
    }
    // Cписок файлов из временного буфера в исходящий
    if (!BufferView_encodeBufferView(outBuf, ASN_SEQUENCE,&fileListBuf))
    {
        return FALSE;
    }
    //More Follows
    if (moreFollows)
    {
        if (!BufferView_encodeBoolean(outBuf, 0x81, moreFollows))
        {
            return FALSE;
        }

    }
    return TRUE;
}

bool mms_handleFileDirRequest(MmsConnection* mmsConn, BufferView* inBuf,
    unsigned int invokeId, BufferView* outBuf)
{
    uint8_t tag;
    int length;
    StringView dir;
    StringView startFile;
    bool thereIsStartFile = FALSE;
    StringView_fromCStr(&dir, "/");

    while (inBuf->pos < inBuf->len)
    {
        bool result = BerDecoder_decodeTLFromBufferView(inBuf, &tag, &length, NULL);
        RET_IF_NOT(result, "Error reading tag and length");
        switch (tag) {
        case MMS_DIRECTORY_NAME:
            result = BerDecoder_decodeTLFromBufferView(inBuf, &tag, &length, NULL);
            RET_IF_NOT(result, "Error reading tag and length");
            RET_IF_NOT(tag == ASN_GRAPHIC_STRING, "Unexpected tag");
            StringView_init(&dir, (char*)inBuf->p + inBuf->pos, length);
            inBuf->pos += length;
            break;
        case MMS_FILE_NAME:
            result = BerDecoder_decodeTLFromBufferView(inBuf, &tag, &length, NULL);
            RET_IF_NOT(result, "Error reading tag and length");
            RET_IF_NOT(tag == ASN_GRAPHIC_STRING, "Unexpected tag");
            StringView_init(&startFile, (char*)inBuf->p + inBuf->pos, length);
            inBuf->pos += length;
            thereIsStartFile = TRUE;
            break;
        default:
            ERROR_REPORT("Unexpectded tag");
            return FALSE;
        }
    }
    RET_IF_NOT(dir.p != NULL, "Directory name is not found");

    //dir содержит путь к запрошенному директорию
    return encodeFileDirRequest(mmsConn, invokeId, &dir,
        thereIsStartFile? &startFile: NULL,
        outBuf);
}


static bool encodeFileOpenRequest(MmsConnection* mmsConn, uint32_t invokeID,
    StringView* fileName, size_t startPos, BufferView* outBuf)
{
    /*
        A1
            02 InvokeID
            BF 48
                80 FRSM ID
                A1 (содержит атрибуты)
                    80 Размер
                    81 Время
    */
    EncodedAttrLen encAttrLen;
    size_t encodedFRSMIDlen;
    FileRespHeadInfo respHeadInfo;
    FSFileAttr attr;
    uint32_t frsmID;

    if (!fs_fileOpen(fileName, startPos, &frsmID, &attr))
    {
        return FALSE;
    }
    mmsConn->isFileOpen = TRUE;
    mmsConn->frsmID = frsmID;

    //===================Определяем размеры=====================
    determineEncodedAttrLen(&encAttrLen, attr.fileSize);
    encodedFRSMIDlen = 2 //тэг + длина
        + BerEncoder_UInt32determineEncodedSize(frsmID);
    //данные для тэга BF 48
    respHeadInfo.dataLen = encAttrLen.fullAttrLen + encodedFRSMIDlen;
    respHeadInfo.responseLen = determineResponseLen(invokeID,
        respHeadInfo.dataLen);

    //===================Кодируем=====================
    respHeadInfo.extTag = 0xBF48;
    respHeadInfo.invokeID = invokeID;
    if (!encodeFileResponseHead(outBuf, &respHeadInfo))
    {
        return FALSE;
    }
    //FRSM ID
    if (!BufferView_encodeUInt32(outBuf, 0x80, frsmID))
    {
        return FALSE;
    }

    //Атрибуты
    if (!encodeFileAttr(outBuf, &encAttrLen, &attr))
    {
        return FALSE;
    }
    return TRUE;
}

bool mms_handleFileOpenRequest(MmsConnection* mmsConn, BufferView* inBuf,
    unsigned int invokeId, BufferView* outBuf)
{
    /*
    A0
        19 Имя файла
    81 Начальная позиция
    */
    uint8_t tag;
    int length;
    StringView fileName;
    size_t startPos;

    bool result = BerDecoder_decodeTLFromBufferView(inBuf, &tag, &length, NULL);
    RET_IF_NOT(result, "Error reading tag and length");
    RET_IF_NOT(tag == 0xA0, "Unexpected tag");
    //Имя файла
    result = BerDecoder_decodeTLFromBufferView(inBuf, &tag, &length, NULL);
    RET_IF_NOT(result, "Error reading file name tag and length");
    RET_IF_NOT(tag == ASN_GRAPHIC_STRING, "Unexpected tag");
    //Создаём StringView на имя файла
    result = BufferView_readStringView(inBuf, length, &fileName);
    RET_IF_NOT(result, "Error reading file Name");
    //Начальная позиция
    result = BerDecoder_decodeTLFromBufferView(inBuf, &tag, &length, NULL);
    RET_IF_NOT(result, "Error reading position tag and length");
    result = BufferView_decodeUInt32(inBuf, length, (uint32_t*)&startPos);
    RET_IF_NOT(result, "Error reading start position");

    return encodeFileOpenRequest(mmsConn, invokeId,
        &fileName, startPos, outBuf);
}


static bool encodeFileReadRequest(MmsConnection* mmsConn, uint32_t invokeID,
    uint32_t frsmID, BufferView* outBuf)
{
    /*
        A1
            02 InvokeID
            BF 49
                80 file data
                81 more follows
    */
    BufferView fileReadBuf;
    bool moreFollows;
    size_t moreFollowsLen = 3;
    size_t encodedFileDataLen;
    FileRespHeadInfo respHeadInfo;

    BufferView_init(&fileReadBuf, mmsConn->fileBuf, FILE_BUF_SIZE, 0);
    if (!fs_fileRead(frsmID, &fileReadBuf, &moreFollows))
    {
        return FALSE;
    }
    //===================Определяем размеры=====================
    encodedFileDataLen = BerEncoder_determineFullObjectSize(fileReadBuf.pos);
    respHeadInfo.dataLen = encodedFileDataLen + moreFollowsLen;
    respHeadInfo.responseLen = determineResponseLen(invokeID,
        respHeadInfo.dataLen);

    //===================Кодируем=====================
    respHeadInfo.extTag = 0xBF49;
    respHeadInfo.invokeID = invokeID;
    if (!encodeFileResponseHead(outBuf, &respHeadInfo))
    {
        return FALSE;
    }
    //Данные
    if (!BufferView_encodeBufferView(outBuf, 0x80, &fileReadBuf))
    {
        return FALSE;
    }
    //More follows
    if (!BufferView_encodeBoolean(outBuf, 0x81, moreFollows))
    {
        return FALSE;
    }
    return TRUE;
}

bool mms_handleFileReadRequest(MmsConnection* mmsConn, BufferView* inBuf,
    unsigned int invokeID, BufferView* outBuf)
{
    /*
        inBuf содержит только frsmID
    */
    uint32_t frsmID;
    bool result = BufferView_decodeUInt32(inBuf, inBuf->len, &frsmID);
    RET_IF_NOT(result, "Error reading FRSM ID");
    return encodeFileReadRequest(mmsConn, invokeID, frsmID, outBuf);
}

static bool encodeFileCloseRequest(MmsConnection* mmsConn, uint32_t invokeID,
    uint32_t frsmID, BufferView* outBuf)
{
    /*
        A1
            02 InvokeID
            9F 4A пустой
    */
    FileRespHeadInfo respHeadInfo;

    if (!fs_fileClose(frsmID))
    {
        return FALSE;
    }
    mmsConn->isFileOpen = FALSE;

    respHeadInfo.dataLen = 0;
    respHeadInfo.responseLen = determineResponseLen(invokeID,
        respHeadInfo.dataLen);
    respHeadInfo.invokeID = invokeID;
    respHeadInfo.extTag = 0x9F4A;
    return encodeFileResponseHead(outBuf, &respHeadInfo);
}

bool mms_handleFileCloseRequest(MmsConnection* mmsConn, BufferView* inBuf,
    unsigned int invokeID, BufferView* outBuf)
{
    /*
        inBuf содержит только frsmID
    */
    uint32_t frsmID;
    bool result = BufferView_decodeUInt32(inBuf, inBuf->len, &frsmID);
    RET_IF_NOT(result, "Error reading FRSM ID");
    return encodeFileCloseRequest(mmsConn, invokeID, frsmID, outBuf);
}
