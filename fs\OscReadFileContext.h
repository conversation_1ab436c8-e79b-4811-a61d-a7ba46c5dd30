#pragma once
#include "../pwin_access.h"
#include "OscInfo.h"
#include "OscDescr.h"
#include <stdbool.h>
#include "../fdzipstream/fdzipstream.h"

//! размер одного цикла в dat файле
#define DAT_PERIOD_BUFFER_SIZE	(16*1024)
//! размер файла cfg
#define CFG_BUFFER_SIZE			(32*1024)
//! размер буфера под изменения частоты
#define FREQ_CFG_BUFFER_SIZE	(32*1024)
//! размер файла hdr
#define HDR_BUFFER_SIZE			(64)

//! размер буфера потокового файла,
//! сюда может максимально записаться:
//! DAT_PERIOD_BUFFER_SIZE + CFG_BUFFER_SIZE + FREQ_CFG_BUFFER_SIZE
#define STREAM_BUFFER_SIZE		(128*1024)

//! буфер для форматирование одного значения 
#define FORMAT_BUFFER_SIZE		(1024)

//! структура одного изменения частоты (для cfg файла)
typedef struct OscFreqCfg OscFreqCfg;
struct OscFreqCfg
{
	float freq;
	unsigned int sampleNum;
};

//! контекст для чтения осцилограмм
typedef struct OscReadFileContext OscReadFileContext;
struct OscReadFileContext
{
	//! для файловых операций PWinLib
	PWFileInfo fileInfo;
	char fileName[16];
	//! под заголовок осцилограммы
	OSCInfoStruct *oscInfo;
	unsigned int curFrame;
	double tick;
	double phistoryTick;
	OscFreqCfg lastFreq;
	int freqCfgCount;
	char formatBuffer[FORMAT_BUFFER_SIZE];
	OscWriteBuffer cfgBuffer;
	OscWriteBuffer datBuffer;
	OscWriteBuffer hdrBuffer;
	OscWriteBuffer freqCfgBuffer;
	OscWriteBuffer streamBuffer;
	ZIPstream *zipStream;
	ZIPentry *zipDat;
	ZIPentry *zipCfg;
	ZIPentry *zipHdr;
};


bool OscReadFileContext_writeCfgToStream(OscReadFileContext * readFileContext);
bool OscReadFileContext_closeCfg(OscReadFileContext * readFileContext);

bool OscReadFileContext_writeDatToStream(OscReadFileContext * readFileContext);
bool OscReadFileContext_closeDat(OscReadFileContext * readFileContext);

bool OscReadFileContext_writeHdrToStream(OscReadFileContext * readFileContext);
bool OscReadFileContext_closeHdr(OscReadFileContext * readFileContext);

bool OscReadFileContext_flushAndClose(OscReadFileContext * readFileContext);



int OscReadFileContext_getPhistotyTime(OscReadFileContext * readFileContext);
int OscReadFileContext_getPhistotyTimeMS(OscReadFileContext * readFileContext);

bool OscReadFileContext_writeFreq(OscReadFileContext * readFileContext, float freq, 
	unsigned int sampleNum);

//! количество изменения частоты
int OscReadFileContext_getFreqCount(OscReadFileContext * readFileContext);
//! частота по номеру изменения
OscFreqCfg *OscReadFileContext_getFreqCfg(OscReadFileContext * readFileContext,int num);

//! создает контекст
OscReadFileContext *OscReadFileContext_create(PWFileInfo *fileInfo);
//! удаляет контекст (безопасна)
void OscReadFileContext_destroy(OscReadFileContext * readFileContext);


