#pragma once

// Этот модуль планируется как новый низкоуровневый кодер/декодер BER.

#include <stdint.h>
#include <stddef.h>

//! Функция копирует len байт из src в dst в обратном порядке.
//! Предназначена для кодирования int разной разрядности в BER.
//! Пример кодирования:
//! size_t len = BERCoder_calcIntEncodedLen(&value, sizeof(value));
//! BERCoder_reverseCopy(&value, buffer, len);
void BERCoder_reverseCopy(const void* src, uint8_t* dst, size_t len);

//! Вычисляет длину BER-кодированного int.
//! Фактически считает число значащих байт.
size_t BERCoder_calcIntEncodedLen(const void* pValue, size_t intSize);

