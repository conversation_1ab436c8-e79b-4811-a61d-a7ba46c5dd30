#include "presentation.h"
#include "AsnEncoding.h"  

#include <stddef.h>
#include <string.h>

//параметры CONNECTION PRESENTATION
#define	MODE_SELECTOR_PARAMETER 0xa0
#define MODE_PARAMETER 0x80
#define NORMAL_MODE_PARAMETERS 0xa2
#define CALLING_PRESENTATION_SELECTOR 0x81
#define CALLED_PRESENTATION_SELECTOR 0x82
#define PRESENTATION_CONTEXT_DIFINITION_LIST 0xa4
#define PRESENTATION_USER_DATA 0x61
#define RESPONDING_PRESENTATION_SELECTOR 0x83

//параметры ACCEPT PRESENTATION
#define CONTEXT_DIFINITION_RESULT_LIST 0xa5
#define PRESENTATION_CONTEXT_DIFINITION_LIST_RESULT 0x80
#define	TRANFER_SYNTAX_NAME_PARAMETER 0x81
#define PRESENTATION_USER_DATA_NEXT	 0xa0

#define	PRESENTATION_DATA_PACKET_HEADER_SIZE	9

unsigned char berId[] = { 0x51, 0x01 };
unsigned char calledPresentationSelector[] = { 0x00, 0x00, 0x00, 0x01 };

static int encodeAcceptBer( unsigned char* buf, int bufPos )
{

    bufPos = BerEncoder_encodeTL( ASN_SEQUENCE, 7, buf, bufPos );
    bufPos = BerEncoder_encodeTL( PRESENTATION_CONTEXT_DIFINITION_LIST_RESULT,
                                  1, buf, bufPos );
    buf[bufPos++] = 0;
    bufPos = BerEncoder_encodeTL( TRANFER_SYNTAX_NAME_PARAMETER, 2, buf,
                                  bufPos );
    buf[bufPos++] = berId[0];		//0x51;
    buf[bufPos++] = berId[1];		//0x01;

    return bufPos;
}


//Пишет "шапку" UserData(для ACSE) или просто определяет её размер
//encode:
//  1 - реально писать в буфер. //Возвращает новую позицию в буфере
//  0 - или только определить размер. Возвращает размер
static int encodeUserData( unsigned char* buffer, int bufPos, int userDataLength,
                    unsigned char encode, unsigned char contextId )
{
    int fullyEncodedDataLength;
    int encodedDataSetLength = 3; /* presentation-selector */

    // presentation-data
    encodedDataSetLength += userDataLength + 1;
    encodedDataSetLength += BerEncoder_determineLengthSize(userDataLength);

    fullyEncodedDataLength = encodedDataSetLength;

    fullyEncodedDataLength += BerEncoder_determineLengthSize(encodedDataSetLength) + 1;

    if (encode) {
        /* fully-encoded-data */
        bufPos = BerEncoder_encodeTL(PRESENTATION_USER_DATA,
                                     fullyEncodedDataLength, buffer, bufPos);
        bufPos = BerEncoder_encodeTL(ASN_SEQUENCE, encodedDataSetLength, buffer,
                                     bufPos);

        /* presentation-selector acse */
        bufPos = BerEncoder_encodeTL(ASN_INTEGER, 1, buffer, bufPos);
        buffer[bufPos++] = contextId;

        /* presentation-data (= acse payload) */
        bufPos = BerEncoder_encodeTL(PRESENTATION_USER_DATA_NEXT,
                                     userDataLength, buffer, bufPos);

        return bufPos;
    }
    else {
        int encodedUserDataLength = fullyEncodedDataLength + 1;
        encodedUserDataLength += BerEncoder_determineLengthSize(fullyEncodedDataLength);

        return encodedUserDataLength;
    }
}

void initPresentation(IsoPresentation* presentation)
{
    //TODO acseContextId
    presentation->presentationContextId = 3;
    presentation->acseContextId = 1;
}

int isoPresentation_createCpaMessage(IsoPresentation* presentation,
                                 unsigned char* buf, unsigned char* userData, int userDataLen)
{
    int contentLength = 0;
    int normalModeLength = 0;
    int bufPos = 0;

    // mode-selector
    contentLength += 5;
    normalModeLength += 6; // responding-presentation-selector
    normalModeLength += 20; // context-definition-result-list
    normalModeLength += encodeUserData(NULL, 0, userDataLen, 0/*encode*/,
                                       presentation->acseContextId);

    contentLength += normalModeLength;

    contentLength += BerEncoder_determineLengthSize(normalModeLength) + 1;

    bufPos = BerEncoder_encodeTL(ASN_SET, contentLength, buf, bufPos);

    /* mode-selector */
    bufPos = BerEncoder_encodeTL(MODE_SELECTOR_PARAMETER, 3, buf, bufPos);
    bufPos = BerEncoder_encodeTL(MODE_PARAMETER, 1, buf, bufPos);
    buf[bufPos++] = 1; /* 1 = normal-mode */

    /* normal-mode-parameters */
    bufPos = BerEncoder_encodeTL(NORMAL_MODE_PARAMETERS, normalModeLength, buf, bufPos);

    /* responding-presentation-selector */
    bufPos = BerEncoder_encodeTL(RESPONDING_PRESENTATION_SELECTOR, 4, buf, bufPos);
    memcpy(buf + bufPos, calledPresentationSelector, 4);
    bufPos += 4;

    /* context-definition-result-list */
    bufPos = BerEncoder_encodeTL(CONTEXT_DIFINITION_RESULT_LIST, 18, buf, bufPos);
    bufPos = encodeAcceptBer(buf, bufPos); /* accept for acse */
    bufPos = encodeAcceptBer(buf, bufPos); /* accept for mms */

    /* encode user data */
    //Пишем "шапку"
    bufPos = encodeUserData(buf, bufPos, userDataLen, 1, presentation->acseContextId);
    //Пишем сами данные
    memcpy( buf + bufPos, userData, userDataLen );
    return bufPos + userDataLen;
}


int isoPresentation_parseUserData(IsoPresentation* presentation,
                                  unsigned char* inBuf, int inLen,
                                  unsigned char** pOutUserData)
{        
    int len;//dummy?
    int userDataLength;
    int bufPos = 0;

    if (inLen < PRESENTATION_DATA_PACKET_HEADER_SIZE)
    {
        return -1;
    }

    if (inBuf[bufPos++] != PRESENTATION_USER_DATA)
    {
        return -1;
    }
    bufPos = BerDecoder_decodeLength(inBuf, &len, bufPos, inLen);

    if (inBuf[bufPos++] != ASN_SEQUENCE)
    {
        return -1;
    }

    bufPos = BerDecoder_decodeLength(inBuf, &len, bufPos, inLen);

    if (inBuf[bufPos++] != ASN_INTEGER)
    {
        return -1;
    }

    if (inBuf[bufPos++] != 0x01)
    {
        return -1;
    }

    presentation->nextContextId = inBuf[bufPos++];

    if (inBuf[bufPos++] != PRESENTATION_USER_DATA_NEXT)
    {
        return -1;
    }

    bufPos = BerDecoder_decodeLength(inBuf, &userDataLength, bufPos, inLen);

    *pOutUserData = inBuf +bufPos;

    return userDataLength;
}

int IsoPresentation_createUserData(IsoPresentation* presentation,
                                   unsigned char* buf, unsigned char* userData, int userDataLen)
{
    int bufPos = 0;

    int userDataLengthFieldSize = BerEncoder_determineLengthSize(userDataLen);

    int pdvListLength = userDataLen + (userDataLengthFieldSize + 4);

    int pdvListLengthFieldSize = BerEncoder_determineLengthSize(pdvListLength);
    int presentationLength = pdvListLength + (pdvListLengthFieldSize + 1);

    bufPos = BerEncoder_encodeTL(PRESENTATION_USER_DATA, presentationLength,
                                 buf, bufPos);

    bufPos = BerEncoder_encodeTL(ASN_SEQUENCE, pdvListLength, buf, bufPos);

    buf[bufPos++] = ASN_INTEGER;
    buf[bufPos++] = 0x01;
    buf[bufPos++] = presentation->presentationContextId;

    bufPos = BerEncoder_encodeTL(PRESENTATION_USER_DATA_NEXT, userDataLen,
                                 buf, bufPos);

    memcpy( buf + bufPos, userData, userDataLen );
    return bufPos + userDataLen;
}


#pragma alignvar (4)



unsigned char Asn_Id_Acse[] = { 0x52, 0x01, 0x00, 0x01 };


unsigned char Asn_Id_Mms[] = { 0x28, 0xca, 0x22, 0x02, 0x01 };
