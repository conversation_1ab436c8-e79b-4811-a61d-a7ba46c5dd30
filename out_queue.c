#include "out_queue.h"

#include <platform_critical_section.h>
#include <stddef.h>

void OutQueue_init(OutQueue* self)
{
    CriticalSection_Init(&self->cs);
	self->head = -1;
}

void OutQueue_done(OutQueue* self)
{
	CriticalSection_Done(&self->cs);	
}

bool OutQueue_isEmpty(OutQueue* self)
{
	return self->head == -1;
};

bool OutQueue_insert(OutQueue* self, void* item)
{
	bool result = FALSE;
    CriticalSection_Lock(&self->cs);
	if (self->head == -1)
	{
		//Empty queue
		self->tail = 0;
		self->queue[0] = item;
		self->head = 1;
		result = TRUE;
	}
	else if (self->head == self->tail)
	{
		//Overflow
		result = FALSE;
	}
	else
	{
		self->queue[self->head++] = item;
		if (self->head == OUT_QUEUE_SIZE)
		{
			self->head = 0;
		}
		result = TRUE;
	}
    CriticalSection_Unlock(&self->cs);
    return result;
}

void* OutQueue_get(OutQueue* self)
{
	void* result = NULL;
    CriticalSection_Lock(&self->cs);
	if (self->head == -1)
	{
		//Empty
		result = NULL;
	}
	else
	{
		result = self->queue[self->tail++];
		if (self->tail == OUT_QUEUE_SIZE)
		{
			self->tail = 0;
		}
		if (self->tail == self->head)
		{
			self->head = -1;
		}
	}
    CriticalSection_Unlock(&self->cs);
    return result;
}

