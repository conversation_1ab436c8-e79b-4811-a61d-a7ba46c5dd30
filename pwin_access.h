#pragma once


#include "../../../../AT91CORE/modules/pw/pwinlib/pwfiles.h"
#include <stdint.h>
#include <stddef.h>
#include <stdbool.h>
#include <time.h>

bool initPWin(void);

bool loadRomModule(uint32_t signature, void** pRomModule, uint8_t** pRomModuleData,
    size_t* pRomModuleDataSize, __time32_t *time);

uint8_t* loadIedModel(int* pSize );

int getFloatValue(short offset);

void writeTele(uint32_t offset);

int getFloatSett(unsigned short offset);
float getRealSett(unsigned short offset);
bool pwaWriteFloatSett(unsigned short offset, int value);
bool pwaWriteRealSett(unsigned short offset, float value);
int getIntSett(int offset);
bool pwaWriteIntSett(unsigned short offset, int value);

//!	список осцилограмм
//! Возвращает
//! >0 - успешно
//! == 0 - больше нет осцилограмм
//! <0 - ошибка
//! oscNum - абсолютный номер осцилограммы, с которого начинать поиск
int pwaOscFindFirst(unsigned int oscNum, PWFileInfo *fileInfo);
int pwaOscFindNext(int findResult, PWFileInfo *fileInfo);
void pwaOscFindClose(void);
//! чтение осцилограмм
//! > 0 - количество прочитанных
//! == 0 - все прочитано
//! <0 - ошибка
int pwaOscOscRead(PWFileInfo *fileInfo, unsigned int offset,
    unsigned char *data, int maxDataSize);




