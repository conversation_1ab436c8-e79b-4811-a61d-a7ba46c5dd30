#include "OscConverter.h"
#include "platform_critical_section.h"
#include <time.h>
#include <string.h>
#include <stdlib.h>
#include <stdio.h>
#include "../timetools.h"

bool  OscConverter_init(void)
{	
	return true;
}


bool  OscConverter_processPeriod(OscReadFileContext * readFileContext)
{
	char *formatBuffer = readFileContext->formatBuffer;
	OscWriteBuffer *datBuffer = &readFileContext->datBuffer;
	OSCInfoStruct *oscInfo;
	int pointPerFrame;
	int analogCount;
	int boolCount;
	int pointNum;
	int sampleNum;
	int size;
	double tick;
	int analogNum;
	int boolNum;
	float freq;

	if (!readFileContext)
	{
		return false;
	}
	oscInfo = readFileContext->oscInfo;
	if (!oscInfo)
	{
		return false;
	}

	pointPerFrame = OSCInfo_getPointPerFrameCount(oscInfo);
	analogCount = OSCInfo_getAnalogCount(oscInfo);
	boolCount = OSCInfo_getBoolCount(oscInfo);
	sampleNum = (readFileContext->curFrame) * pointPerFrame + 1;



	OscWriteBuffer_reset(datBuffer);

	for (pointNum = 0; pointNum < pointPerFrame; ++pointNum)
	{
		size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, "%d", sampleNum);
		if (!OscWriteBuffer_write(datBuffer, formatBuffer, size)) return false;

		size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, ",%lld", 
			(uint64_t)( readFileContext->tick + 0.5));
		if (!OscWriteBuffer_write(datBuffer, formatBuffer, size)) return false;
		
		if (!OSCFrame_getTick(oscInfo, &tick))
		{
			return false;
		}
		readFileContext->tick += tick;

		// если еще не вышли за предысторию, фиксируем тики
		if (readFileContext->curFrame < OSCInfo_getPrehistFrameCount(oscInfo))
		{
			readFileContext->phistoryTick = readFileContext->tick;
		}

		

		for (analogNum = 0; analogNum < analogCount; ++analogNum)
		{
			int analogValue = OSCFrame_getAnalogValue(oscInfo, pointNum, analogNum);
			size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, ",%d", analogValue);
			if (!OscWriteBuffer_write(datBuffer, formatBuffer, size)) return false;
		}

		for (boolNum = 0; boolNum < boolCount; ++boolNum)
		{
			int boolValue = OSCFrame_getBoolValue(oscInfo, pointNum, boolNum);
			size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, ",%d", boolValue);
			if (!OscWriteBuffer_write(datBuffer, formatBuffer, size)) return false;
		}


		size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, "\r\n");
		if (!OscWriteBuffer_write(datBuffer, formatBuffer, size)) return false;

		// частоту для cfg файла
		if (!OSCFrame_getFreq(oscInfo, &freq))
		{
			return false;
		}
		if (!OscReadFileContext_writeFreq(readFileContext, freq, sampleNum))
		{
			return false;
		}

		sampleNum++;
	}

	return true;
}

//! вычисление времени начала предыстории
static void getPhistTime(OscReadFileContext * readFileContext,
	__time32_t *t, int *ms)
{
	OSCInfoStruct *oscInfo = readFileContext->oscInfo;
	__time32_t oscTime = OSCInfo_getUTCDate(oscInfo);
	int oscTimeMS = OSCInfo_getDateMS(oscInfo);
	int64_t tmp;

	// время осцилограммы в миллисекундах
	tmp = oscTime;
	tmp *= 1000;
	tmp += oscTimeMS;

	// время предыстории в миллисекундах
	tmp -= OscReadFileContext_getPhistotyTimeMS(readFileContext);
	// в секундах
	*t = (__time32_t)(tmp / 1000);
	*ms = tmp % 1000;
	
}
//! время в COMTRADE 
static bool writeFormatTimeToCfgBuffer(OscReadFileContext * readFileContext,
	__time32_t t, int ms)
{
	char *formatBuffer = readFileContext->formatBuffer;
	OscWriteBuffer *cfgBuffer = &readFileContext->cfgBuffer;
	int size;
	struct tm tmTime;

	// пишется UTC, поэтому осцилограмма будет отличатся от мониторной, 
	// чтобы было все одинаково нужно вытащить часовой пояс из ЦП
	if (TimeTools_gmtime32(&tmTime, &t) == true)
	{
		// по идее тут нужно прибавить 1900, но comtrade
		// требует год в формате %02d, поэтому вычитаем 100

		// Update: для нового формата используется год в виде 4-х символов
		// поэтому прибавляем 1900 и меняем формат на %04d
		tmTime.tm_year += 1900;
		tmTime.tm_mon += 1;

		size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE,
			"%02d/%02d/%04d,%02d:%02d:%02d.%03d\r\n",
			tmTime.tm_mday,
			tmTime.tm_mon,
			tmTime.tm_year,
			tmTime.tm_hour,
			tmTime.tm_min,
			tmTime.tm_sec,
			ms);
	}
	else
	{
		size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE,
			"01/01/1980 00:00:00.%03d\r\n",ms);
	}

	return OscWriteBuffer_write(cfgBuffer, formatBuffer, size);
}

//! записывает время предыстории и осцилограммы
static bool writeOscTimeToCfg(OscReadFileContext * readFileContext)
{
	OSCInfoStruct *oscInfo = readFileContext->oscInfo;
	
	__time32_t oscTime = OSCInfo_getUTCDate(oscInfo);
	int oscTimeMS = OSCInfo_getDateMS(oscInfo);
	int phistTimeMS;
	__time32_t phistTime;

	getPhistTime(readFileContext, &phistTime, &phistTimeMS);
	if (!writeFormatTimeToCfgBuffer(readFileContext, phistTime, phistTimeMS))
	{
		return false;
	}
	if (!writeFormatTimeToCfgBuffer(readFileContext, oscTime, oscTimeMS))
	{
		return false;
	}


	return true;
}
//! описание аналоговых каналов в cfg
static bool writeAnalogToCfg(OscReadFileContext * readFileContext)
{
	OSCInfoStruct *oscInfo = readFileContext->oscInfo;
	int analogCount = OSCInfo_getAnalogCount(oscInfo);
	OscWriteBuffer *cfgBuffer = &readFileContext->cfgBuffer;
	char *formatBuffer = readFileContext->formatBuffer;
	int analogNum;
	int size;
	// аналоговые
	for (analogNum = 0; analogNum < analogCount; ++analogNum)
	{
		OscDescrAnalog *pAnalog = OSCInfo_getAnalog(oscInfo, analogNum);
		double cft = OSCInfo_getAnalogCft(oscInfo, analogNum);
		int max = OSCInfo_getAnalogMax(oscInfo, analogNum);
		int min = OSCInfo_getAnalogMin(oscInfo, analogNum);

		size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE,
			"%d,%s,%s,%s,%s,%e,%e,%d,%d,%d,%e,%e,%c\r\n",
			analogNum + 1,
			OSCDescr_analogName(pAnalog),
			"", // phase
			"", // scheme
			OSCDescr_analogUnits(pAnalog), // units
			cft, // a
			0.0, // b
			0, // skew
			min,
			max,
			1.0, //primary
			1.0, //secondary
			'S'); //primary-secondary flag
		if (!OscWriteBuffer_write(cfgBuffer, formatBuffer, size)) return false;

	}

	return true;
}

//! описание дискретных каналов в cfg
static bool writeBoolToCfg(OscReadFileContext * readFileContext)
{
	OSCInfoStruct *oscInfo = readFileContext->oscInfo;
	int boolCount = OSCInfo_getBoolCount(oscInfo);
	OscWriteBuffer *cfgBuffer = &readFileContext->cfgBuffer;
	char *formatBuffer = readFileContext->formatBuffer;
	int boolNum;
	int size;

	for (boolNum = 0; boolNum < boolCount; ++boolNum)
	{
		OscDescrBool *pBool = OSCInfo_getBool(oscInfo, boolNum);
		size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE,
			"%d,%s,%d\r\n",
			boolNum + 1,
			OSCDescr_boolName(pBool),
			1); // normal state
		if (!OscWriteBuffer_write(cfgBuffer, formatBuffer, size)) return false;
	}

	return true;
}

//! частота и список изменений частоты в cfg
static bool writeFrequencyToCfg(OscReadFileContext * readFileContext)
{
	OscWriteBuffer *cfgBuffer = &readFileContext->cfgBuffer;
	char *formatBuffer = readFileContext->formatBuffer;
	double freq;
	int freqCount;
	int freqNum;
	int size;
	// частота
	freq = OSCDescr_getFreq();
	size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, "%d\r\n", (int)freq);
	if (!OscWriteBuffer_write(cfgBuffer, formatBuffer, size)) return false;

	// количество изменений частоты
	freqCount = OscReadFileContext_getFreqCount(readFileContext);
	size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, "%d\r\n", freqCount);
	if (!OscWriteBuffer_write(cfgBuffer, formatBuffer, size)) return false;

	// изменения частоты
	for (freqNum = 0; freqNum < freqCount; ++freqNum)
	{
		OscFreqCfg *freqCfg = OscReadFileContext_getFreqCfg(readFileContext, freqNum);
		if (!freqCfg)
		{
			return false;
		}
		size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, "%f,%d\r\n", (double)freqCfg->freq,
			freqCfg->sampleNum);
		if (!OscWriteBuffer_write(cfgBuffer, formatBuffer, size)) return false;
	}
	return true;
}


bool  OscConverter_processCfg(OscReadFileContext * readFileContext)
{
	char *formatBuffer = readFileContext->formatBuffer;
	OscWriteBuffer *cfgBuffer = &readFileContext->cfgBuffer;
	OSCInfoStruct *oscInfo;
	int size;
	int analogCount;
	int boolCount;

	oscInfo = readFileContext->oscInfo;
	if (!oscInfo)
	{
		return false;
	}

	analogCount = OSCInfo_getAnalogCount(oscInfo);
	boolCount = OSCInfo_getBoolCount(oscInfo);

	// имя блока и время
	size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, "MTRA,%s,2013\r\n", OSCDescr_getTerminalName());
	if (!OscWriteBuffer_write(cfgBuffer, formatBuffer, size)) return false;

	// количество аналоговых и дискретных
	size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, "%d,%dA,%dD\r\n", analogCount + boolCount, 
		analogCount,
		boolCount);
	if (!OscWriteBuffer_write(cfgBuffer, formatBuffer, size)) return false;


	// запись каналов

	// аналоговые
	if (!writeAnalogToCfg(readFileContext))
	{
		return false;
	}

	// дискретные
	if (!writeBoolToCfg(readFileContext))
	{
		return false;
	}
	// изменение частоты
	if (!writeFrequencyToCfg(readFileContext))
	{
		return false;
	}
	// время начала предыстории и время осцилограммы
	if (!writeOscTimeToCfg(readFileContext))
	{
		return false;
	}

	// ASCII согласно формату
	size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, "ASCII\r\n");
	if (!OscWriteBuffer_write(cfgBuffer, formatBuffer, size)) return false;

	// Следующие значения вписываются напрямую
	// в дальнейшем следует переделать для большего соответствия формату

	// коэффициент умножения временной метки
	size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, "1\r\n");
	if (!OscWriteBuffer_write(cfgBuffer, formatBuffer, size)) return false;

	// Разница между локальным временем и UTC
	size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, "0,0\r\n");
	if (!OscWriteBuffer_write(cfgBuffer, formatBuffer, size)) return false;
	
	// Качество времени
	size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, "0,0\r\n");
	if (!OscWriteBuffer_write(cfgBuffer, formatBuffer, size)) return false;

	return true;
}

bool OscConverter_processHdr(OscReadFileContext * readFileContext)
{
	// сюдя можно что-нибудь дописать
	char *comment = "";
	char *text = "";

	char *formatBuffer = readFileContext->formatBuffer;
	OscWriteBuffer *hdrBuffer = &readFileContext->hdrBuffer;
	OSCInfoStruct *oscInfo;
	int size;
	oscInfo = readFileContext->oscInfo;
	if (!oscInfo)
	{
		return false;
	}

	size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, "%s\r\n",text);
	if (!OscWriteBuffer_write(hdrBuffer, formatBuffer, size)) return false;

	size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, "%s\r\n",comment);
	if (!OscWriteBuffer_write(hdrBuffer, formatBuffer, size)) return false;

	return true;
}

