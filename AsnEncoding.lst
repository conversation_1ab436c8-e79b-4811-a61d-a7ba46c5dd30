                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=AsnEncoding.c -o gh_71s1.o -list=AsnEncoding.lst C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
Source File: AsnEncoding.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile AsnEncoding.c

                      10 ;		-o AsnEncoding.o

                      11 ;Source File:   AsnEncoding.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:55:58 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "AsnEncoding.h"


                      21 ;2: 


                      22 ;3: #include "bufViewBER.h"


                      23 ;4: #include <string.h>


                      24 ;5: #include <stdlib.h>


                      25 ;6: 


                      26 ;7: 


                      27 ;8: int BerEncoder_encodeLength( unsigned int iLength, unsigned char* pBuffer, int iBufPos )


                      28 	.text

                      29 	.align	4

                      30 BerEncoder_encodeLength::

                      31 ;9: {


                      32 

                      33 ;10:     if ( iLength < 128 )


                      34 

00000000 e3500080     35 	cmp	r0,128

                      36 ;11:     {


                      37 

                      38 ;12:         pBuffer[iBufPos++] = (unsigned char)iLength;


                      39 

                      40 ;24: 


                      41 ;25:     }


                      42 ;26: 


                      43 ;27:     return iBufPos;


                      44 

00000004 3a00000a     45 	blo	.L21

                      46 ;13:     }


                      47 ;14:     else if ( iLength < 256 )


                      48 

00000008 e3500f40     49 	cmp	r0,256

                      50 ;15:     {



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
                      51 

                      52 ;16:         pBuffer[iBufPos++] = 0x81;


                      53 

0000000c 33a03081     54 	movlo	r3,129

00000010 37c13002     55 	strlob	r3,[r1,r2]

00000014 32822001     56 	addlo	r2,r2,1

                      57 ;17:         pBuffer[iBufPos++] = (unsigned char)iLength;


                      58 

                      59 ;24: 


                      60 ;25:     }


                      61 ;26: 


                      62 ;27:     return iBufPos;


                      63 

00000018 3a000005     64 	blo	.L21

                      65 ;18:     }


                      66 ;19:     else


                      67 ;20:     {


                      68 

                      69 ;21:         pBuffer[iBufPos++] = 0x82;


                      70 

0000001c e3a03082     71 	mov	r3,130

00000020 e7c13002     72 	strb	r3,[r1,r2]

00000024 e2822001     73 	add	r2,r2,1

                      74 ;22:         pBuffer[iBufPos++] = iLength / 256;


                      75 

00000028 e1a03420     76 	mov	r3,r0 lsr 8

0000002c e7c13002     77 	strb	r3,[r1,r2]

00000030 e2822001     78 	add	r2,r2,1

                      79 ;23:         pBuffer[iBufPos++] = iLength % 256;


                      80 

                      81 ;24: 


                      82 ;25:     }


                      83 ;26: 


                      84 ;27:     return iBufPos;


                      85 

                      86 .L21:

00000034 e7c10002     87 	strb	r0,[r1,r2]

00000038 e2820001     88 	add	r0,r2,1

0000003c e12fff1e*    89 	ret	

                      90 	.endf	BerEncoder_encodeLength

                      91 	.align	4

                      92 

                      93 ;iLength	r0	param

                      94 ;pBuffer	r1	param

                      95 ;iBufPos	r2	param

                      96 

                      97 	.section ".bss","awb"

                      98 .L74:

                      99 	.data

                     100 	.text

                     101 

                     102 ;28: }


                     103 

                     104 ;29: 


                     105 ;30: int BerDecoder_decodeLength(unsigned char* buffer, int* length, int bufPos, int maxBufPos)


                     106 	.align	4

                     107 	.align	4

                     108 BerDecoder_decodeLength::

00000040 e92d0070    109 	stmfd	[sp]!,{r4-r6}

                     110 ;31: {


                     111 


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
                     112 ;32:     unsigned char len1;


                     113 ;33:     int i;


                     114 ;34: 


                     115 ;35:     if (bufPos >= maxBufPos)


                     116 

00000044 e1520003    117 	cmp	r2,r3

00000048 aa000064    118 	bge	.L112

                     119 ;36:         return -1;


                     120 

                     121 ;37: 


                     122 ;38:      len1 = buffer[bufPos++];


                     123 

0000004c e7d0c002    124 	ldrb	r12,[r0,r2]

00000050 e2822001    125 	add	r2,r2,1

                     126 ;39: 


                     127 ;40:     if (len1 & 0x80) {


                     128 

00000054 e31c0080    129 	tst	r12,128

                     130 ;56:             }


                     131 ;57:         }


                     132 ;58: 


                     133 ;59:     }


                     134 ;60:     else {


                     135 

                     136 ;61:         *length = len1;


                     137 

00000058 0581c000    138 	streq	r12,[r1]

0000005c 0a00005b    139 	beq	.L110

                     140 ;41:         int lenLength = len1 & 0x7f;


                     141 

00000060 e21c407f    142 	ands	r4,r12,127

                     143 ;42: 


                     144 ;43:         if (lenLength == 0) { /* indefinite length form */


                     145 

00000064 1a000004    146 	bne	.L100

                     147 ;44:             *length = -1;


                     148 

00000068 e3f0c000    149 	mvns	r12,0

0000006c e581c000    150 	str	r12,[r1]

                     151 ;62:     }


                     152 ;63: 


                     153 ;64:     if (*length < 0)


                     154 

                     155 

                     156 

00000070 51a00002    157 	movpl	r0,r2

00000074 43e00000    158 	mvnmi	r0,0

00000078 ea000059    159 	b	.L93

                     160 .L100:

                     161 ;45:         }


                     162 ;46:         else {


                     163 

                     164 ;47:             *length = 0;


                     165 

0000007c e3a0c000    166 	mov	r12,0

00000080 e581c000    167 	str	r12,[r1]

                     168 ;48: 


                     169 ;49: 


                     170 ;50:             for (i = 0; i < lenLength; i++) {


                     171 

00000084 e3540000    172 	cmp	r4,0


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
00000088 a1a05004    173 	movge	r5,r4

0000008c b3a05000    174 	movlt	r5,0

00000090 e1b041a5    175 	movs	r4,r5 lsr 3

00000094 0a000041    176 	beq	.L178

                     177 .L179:

00000098 e1520003    178 	cmp	r2,r3

0000009c aa00004f    179 	bge	.L112

000000a0 e1a0c40c    180 	mov	r12,r12 lsl 8

000000a4 e581c000    181 	str	r12,[r1]

000000a8 e7d06002    182 	ldrb	r6,[r0,r2]

000000ac e2822001    183 	add	r2,r2,1

000000b0 e08cc006    184 	add	r12,r12,r6

000000b4 e581c000    185 	str	r12,[r1]

000000b8 e1520003    186 	cmp	r2,r3

000000bc aa000047    187 	bge	.L112

000000c0 e1a0c40c    188 	mov	r12,r12 lsl 8

000000c4 e581c000    189 	str	r12,[r1]

000000c8 e7d06002    190 	ldrb	r6,[r0,r2]

000000cc e2822001    191 	add	r2,r2,1

000000d0 e08cc006    192 	add	r12,r12,r6

000000d4 e581c000    193 	str	r12,[r1]

000000d8 e1520003    194 	cmp	r2,r3

000000dc aa00003f    195 	bge	.L112

000000e0 e1a0c40c    196 	mov	r12,r12 lsl 8

000000e4 e581c000    197 	str	r12,[r1]

000000e8 e7d06002    198 	ldrb	r6,[r0,r2]

000000ec e2822001    199 	add	r2,r2,1

000000f0 e08cc006    200 	add	r12,r12,r6

000000f4 e581c000    201 	str	r12,[r1]

000000f8 e1520003    202 	cmp	r2,r3

000000fc aa000037    203 	bge	.L112

00000100 e1a0c40c    204 	mov	r12,r12 lsl 8

00000104 e581c000    205 	str	r12,[r1]

00000108 e7d06002    206 	ldrb	r6,[r0,r2]

0000010c e2822001    207 	add	r2,r2,1

00000110 e08cc006    208 	add	r12,r12,r6

00000114 e581c000    209 	str	r12,[r1]

00000118 e1520003    210 	cmp	r2,r3

0000011c aa00002f    211 	bge	.L112

00000120 e1a0c40c    212 	mov	r12,r12 lsl 8

00000124 e581c000    213 	str	r12,[r1]

00000128 e7d06002    214 	ldrb	r6,[r0,r2]

0000012c e2822001    215 	add	r2,r2,1

00000130 e08cc006    216 	add	r12,r12,r6

00000134 e581c000    217 	str	r12,[r1]

00000138 e1520003    218 	cmp	r2,r3

0000013c aa000027    219 	bge	.L112

00000140 e1a0c40c    220 	mov	r12,r12 lsl 8

00000144 e581c000    221 	str	r12,[r1]

00000148 e7d06002    222 	ldrb	r6,[r0,r2]

0000014c e2822001    223 	add	r2,r2,1

00000150 e08cc006    224 	add	r12,r12,r6

00000154 e581c000    225 	str	r12,[r1]

00000158 e1520003    226 	cmp	r2,r3

0000015c aa00001f    227 	bge	.L112

00000160 e1a0c40c    228 	mov	r12,r12 lsl 8

00000164 e581c000    229 	str	r12,[r1]

00000168 e7d06002    230 	ldrb	r6,[r0,r2]

0000016c e2822001    231 	add	r2,r2,1

00000170 e08cc006    232 	add	r12,r12,r6

00000174 e581c000    233 	str	r12,[r1]


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
00000178 e1520003    234 	cmp	r2,r3

0000017c aa000017    235 	bge	.L112

00000180 e1a0c40c    236 	mov	r12,r12 lsl 8

00000184 e581c000    237 	str	r12,[r1]

00000188 e7d06002    238 	ldrb	r6,[r0,r2]

0000018c e2822001    239 	add	r2,r2,1

00000190 e08cc006    240 	add	r12,r12,r6

00000194 e581c000    241 	str	r12,[r1]

00000198 e2544001    242 	subs	r4,r4,1

0000019c 1affffbd    243 	bne	.L179

                     244 .L178:

000001a0 e2154007    245 	ands	r4,r5,7

000001a4 0a000009    246 	beq	.L110

                     247 .L205:

000001a8 e1520003    248 	cmp	r2,r3

000001ac aa00000b    249 	bge	.L112

000001b0 e1a0c40c    250 	mov	r12,r12 lsl 8

000001b4 e581c000    251 	str	r12,[r1]

000001b8 e7d05002    252 	ldrb	r5,[r0,r2]

000001bc e2822001    253 	add	r2,r2,1

000001c0 e08cc005    254 	add	r12,r12,r5

000001c4 e581c000    255 	str	r12,[r1]

000001c8 e2544001    256 	subs	r4,r4,1

000001cc 1afffff5    257 	bne	.L205

                     258 .L110:

                     259 ;62:     }


                     260 ;63: 


                     261 ;64:     if (*length < 0)


                     262 

                     263 

                     264 

000001d0 e35c0000    265 	cmp	r12,0

000001d4 a1a00002    266 	movge	r0,r2

000001d8 b3e00000    267 	mvnlt	r0,0

000001dc ea000000    268 	b	.L93

                     269 .L112:

                     270 ;65:         return -1;


                     271 

000001e0 e3e00000    272 	mvn	r0,0

                     273 .L93:

000001e4 e8bd0070    274 	ldmfd	[sp]!,{r4-r6}

000001e8 e12fff1e*   275 	ret	

                     276 	.endf	BerDecoder_decodeLength

                     277 	.align	4

                     278 ;len1	r12	local

                     279 ;lenLength	r4	local

                     280 

                     281 ;buffer	r0	param

                     282 ;length	r1	param

                     283 ;bufPos	r2	param

                     284 ;maxBufPos	r3	param

                     285 

                     286 	.section ".bss","awb"

                     287 .L484:

                     288 	.data

                     289 	.text

                     290 

                     291 ;74: }


                     292 

                     293 ;75: 


                     294 ;76: 



                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
                     295 ;77: 


                     296 ;78: 


                     297 ;79: bool BerDecoder_decodeTLFromBufferView(BufferView* buf, uint8_t* pTag, int* pLen, int* pFullLen)


                     298 	.align	4

                     299 	.align	4

                     300 BerDecoder_decodeTLFromBufferView::

000001ec e92d4cf0    301 	stmfd	[sp]!,{r4-r7,r10-fp,lr}

000001f0 e24dd004    302 	sub	sp,sp,4

000001f4 e1a04000    303 	mov	r4,r0

000001f8 e1a06001    304 	mov	r6,r1

000001fc e1a07002    305 	mov	r7,r2

00000200 e1a0a003    306 	mov	r10,r3

                     307 ;80: {


                     308 

                     309 ;81:     uint8_t tag;


                     310 ;82:     int len;


                     311 ;83:     int objPos = buf->pos;


                     312 

00000204 e8941021    313 	ldmfd	[r4],{r0,r5,r12}

00000208 e2852001    314 	add	r2,r5,1

0000020c e1a0300c    315 	mov	r3,r12

                     316 ;84: 


                     317 ;85:     tag = BufferView_readTag(buf);


                     318 

00000210 e5842004    319 	str	r2,[r4,4]

00000214 e7d0b005    320 	ldrb	fp,[r0,r5]

                     321 ;86:     buf->pos = BerDecoder_decodeLength(buf->p, &len, buf->pos, buf->len);


                     322 

00000218 e1a0100d    323 	mov	r1,sp

0000021c ebffff87*   324 	bl	BerDecoder_decodeLength

00000220 e5840004    325 	str	r0,[r4,4]

                     326 ;87:     if (buf->pos <= 0)


                     327 

00000224 e3500000    328 	cmp	r0,0

                     329 ;88:     {


                     330 

                     331 ;89:         return FALSE;


                     332 

00000228 020000ff    333 	andeq	r0,r0,255

0000022c 0a00000b    334 	beq	.L553

                     335 ;90:     }


                     336 ;91: 


                     337 ;92:     if (pTag != NULL)


                     338 

00000230 e3560000    339 	cmp	r6,0

                     340 ;93:     {


                     341 

                     342 ;94:         *pTag = tag;


                     343 

00000234 15c6b000    344 	strneb	fp,[r6]

                     345 ;95:     }


                     346 ;96: 


                     347 ;97:     if (pLen != NULL)


                     348 

00000238 e3570000    349 	cmp	r7,0

                     350 ;98:     {


                     351 

                     352 ;99:         *pLen = len;


                     353 

0000023c 159d0000    354 	ldrne	r0,[sp]

00000240 15870000    355 	strne	r0,[r7]


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
                     356 ;100:     }


                     357 ;101: 


                     358 ;102:     if (pFullLen != NULL)


                     359 

00000244 e35a0000    360 	cmp	r10,0

                     361 ;103:     {


                     362 

                     363 ;104:         *pFullLen = buf->pos - objPos + len;


                     364 

00000248 15940004    365 	ldrne	r0,[r4,4]

0000024c 159d1000    366 	ldrne	r1,[sp]

00000250 10400005    367 	subne	r0,r0,r5

00000254 10810000    368 	addne	r0,r1,r0

00000258 158a0000    369 	strne	r0,[r10]

                     370 ;105:     }


                     371 ;106: 


                     372 ;107:     return TRUE;


                     373 

0000025c e3a00001    374 	mov	r0,1

                     375 .L553:

00000260 e28dd004    376 	add	sp,sp,4

00000264 e8bd8cf0    377 	ldmfd	[sp]!,{r4-r7,r10-fp,pc}

                     378 	.endf	BerDecoder_decodeTLFromBufferView

                     379 	.align	4

                     380 ;tag	fp	local

                     381 ;len	[sp]	local

                     382 ;objPos	r5	local

                     383 

                     384 ;buf	r4	param

                     385 ;pTag	r6	param

                     386 ;pLen	r7	param

                     387 ;pFullLen	r10	param

                     388 

                     389 	.section ".bss","awb"

                     390 .L650:

                     391 	.data

                     392 	.text

                     393 

                     394 ;108: }


                     395 

                     396 ;109: 


                     397 ;110: int32_t BerDecoder_decodeInt32(uint8_t* buffer, int intlen, int bufPos)


                     398 	.align	4

                     399 	.align	4

                     400 BerDecoder_decodeInt32::

00000268 e92d0070    401 	stmfd	[sp]!,{r4-r6}

0000026c e1a03000    402 	mov	r3,r0

                     403 ;111: {


                     404 

                     405 ;112:     int32_t value;


                     406 ;113:     int i;


                     407 ;114: 


                     408 ;115:     bool isNegative = ((buffer[bufPos] & 0x80) == 0x80);


                     409 

00000270 e7d30002    410 	ldrb	r0,[r3,r2]

00000274 e3a04000    411 	mov	r4,0

00000278 e1a00c00    412 	mov	r0,r0 lsl 24

0000027c e1b00fa0    413 	movs	r0,r0 lsr 31

                     414 ;116: 


                     415 ;117:     if (isNegative)


                     416 


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
                     417 ;118:         value = -1;


                     418 

00000280 13e00000    419 	mvnne	r0,0

                     420 ;121: 


                     421 ;122:     for (i = 0; i < intlen; i++) {


                     422 

                     423 ;119:     else


                     424 ;120:         value = 0;


                     425 

                     426 ;121: 


                     427 ;122:     for (i = 0; i < intlen; i++) {


                     428 

00000284 e3510000    429 	cmp	r1,0

00000288 a1a05001    430 	movge	r5,r1

0000028c b3a05000    431 	movlt	r5,0

00000290 e1b0c1a5    432 	movs	r12,r5 lsr 3

00000294 0a000013    433 	beq	.L726

00000298 e0821003    434 	add	r1,r2,r3

0000029c e1a0418c    435 	mov	r4,r12 lsl 3

                     436 .L742:

000002a0 e4d16001    437 	ldrb	r6,[r1],1

000002a4 e0860400    438 	add	r0,r6,r0 lsl 8

000002a8 e4d16001    439 	ldrb	r6,[r1],1

000002ac e0860400    440 	add	r0,r6,r0 lsl 8

000002b0 e4d16001    441 	ldrb	r6,[r1],1

000002b4 e0860400    442 	add	r0,r6,r0 lsl 8

000002b8 e4d16001    443 	ldrb	r6,[r1],1

000002bc e0860400    444 	add	r0,r6,r0 lsl 8

000002c0 e4d16001    445 	ldrb	r6,[r1],1

000002c4 e0860400    446 	add	r0,r6,r0 lsl 8

000002c8 e4d16001    447 	ldrb	r6,[r1],1

000002cc e0860400    448 	add	r0,r6,r0 lsl 8

000002d0 e4d16001    449 	ldrb	r6,[r1],1

000002d4 e0860400    450 	add	r0,r6,r0 lsl 8

000002d8 e4d16001    451 	ldrb	r6,[r1],1

000002dc e25cc001    452 	subs	r12,r12,1

000002e0 e0860400    453 	add	r0,r6,r0 lsl 8

000002e4 1affffed    454 	bne	.L742

                     455 .L726:

000002e8 e215c007    456 	ands	r12,r5,7

000002ec 10831002    457 	addne	r1,r3,r2

000002f0 10841001    458 	addne	r1,r4,r1

                     459 .L746:

000002f4 14d12001    460 	ldrneb	r2,[r1],1

000002f8 10820400    461 	addne	r0,r2,r0 lsl 8

000002fc 125cc001    462 	subnes	r12,r12,1

00000300 1afffffb    463 	bne	.L746

                     464 .L681:

                     465 ;125:     }


                     466 ;126: 


                     467 ;127:     return value;


                     468 

00000304 e8bd0070    469 	ldmfd	[sp]!,{r4-r6}

00000308 e12fff1e*   470 	ret	

                     471 	.endf	BerDecoder_decodeInt32

                     472 	.align	4

                     473 ;value	r0	local

                     474 ;i	r4	local

                     475 ;isNegative	r0	local

                     476 

                     477 ;buffer	r3	param


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
                     478 ;intlen	r1	param

                     479 ;bufPos	r2	param

                     480 

                     481 	.section ".bss","awb"

                     482 .L923:

                     483 	.data

                     484 	.text

                     485 

                     486 ;128: }


                     487 

                     488 ;129: 


                     489 ;130: unsigned int BerDecoder_decodeUint32(unsigned char* buffer, int intLen, int bufPos)


                     490 	.align	4

                     491 	.align	4

                     492 BerDecoder_decodeUint32::

0000030c e92d0070    493 	stmfd	[sp]!,{r4-r6}

                     494 ;131: {


                     495 

00000310 e3a03000    496 	mov	r3,0

                     497 ;132:     unsigned int value = 0;


                     498 

                     499 ;133: 


                     500 ;134:     int i;


                     501 ;135:     for (i = 0; i < intLen; i++) {


                     502 

00000314 e1a04003    503 	mov	r4,r3

00000318 e3510000    504 	cmp	r1,0

0000031c a1a05001    505 	movge	r5,r1

00000320 b3a05000    506 	movlt	r5,0

00000324 e1b0c1a5    507 	movs	r12,r5 lsr 3

00000328 0a000013    508 	beq	.L974

0000032c e0821000    509 	add	r1,r2,r0

00000330 e1a0418c    510 	mov	r4,r12 lsl 3

                     511 .L990:

00000334 e4d16001    512 	ldrb	r6,[r1],1

00000338 e0863403    513 	add	r3,r6,r3 lsl 8

0000033c e4d16001    514 	ldrb	r6,[r1],1

00000340 e0863403    515 	add	r3,r6,r3 lsl 8

00000344 e4d16001    516 	ldrb	r6,[r1],1

00000348 e0863403    517 	add	r3,r6,r3 lsl 8

0000034c e4d16001    518 	ldrb	r6,[r1],1

00000350 e0863403    519 	add	r3,r6,r3 lsl 8

00000354 e4d16001    520 	ldrb	r6,[r1],1

00000358 e0863403    521 	add	r3,r6,r3 lsl 8

0000035c e4d16001    522 	ldrb	r6,[r1],1

00000360 e0863403    523 	add	r3,r6,r3 lsl 8

00000364 e4d16001    524 	ldrb	r6,[r1],1

00000368 e0863403    525 	add	r3,r6,r3 lsl 8

0000036c e4d16001    526 	ldrb	r6,[r1],1

00000370 e25cc001    527 	subs	r12,r12,1

00000374 e0863403    528 	add	r3,r6,r3 lsl 8

00000378 1affffed    529 	bne	.L990

                     530 .L974:

0000037c e215c007    531 	ands	r12,r5,7

00000380 10800002    532 	addne	r0,r0,r2

00000384 10840000    533 	addne	r0,r4,r0

                     534 .L994:

00000388 14d01001    535 	ldrneb	r1,[r0],1

0000038c 10813403    536 	addne	r3,r1,r3 lsl 8

00000390 125cc001    537 	subnes	r12,r12,1

00000394 1afffffb    538 	bne	.L994


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
                     539 .L954:

                     540 ;138:     }


                     541 ;139: 


                     542 ;140:     return value;


                     543 

00000398 e1a00003    544 	mov	r0,r3

0000039c e8bd0070    545 	ldmfd	[sp]!,{r4-r6}

000003a0 e12fff1e*   546 	ret	

                     547 	.endf	BerDecoder_decodeUint32

                     548 	.align	4

                     549 ;value	r3	local

                     550 ;i	r4	local

                     551 

                     552 ;buffer	r0	param

                     553 ;intLen	r1	param

                     554 ;bufPos	r2	param

                     555 

                     556 	.section ".bss","awb"

                     557 .L1153:

                     558 	.data

                     559 	.text

                     560 

                     561 ;141: }


                     562 

                     563 ;142: 


                     564 ;143: int BerEncoder_encodeTL( unsigned char ucTag, unsigned int iLength,


                     565 	.align	4

                     566 	.align	4

                     567 BerEncoder_encodeTL::

000003a4 e1a0c001    568 	mov	r12,r1

000003a8 e1a01002    569 	mov	r1,r2

                     570 ;144:                          unsigned char* pBuffer, int iBufPos )


                     571 ;145: {


                     572 

                     573 ;146:     pBuffer[iBufPos++] = ucTag;


                     574 

000003ac e2832001    575 	add	r2,r3,1

000003b0 e7c10003    576 	strb	r0,[r1,r3]

                     577 ;147:     iBufPos = BerEncoder_encodeLength( iLength, pBuffer, iBufPos );


                     578 

000003b4 e1a0000c    579 	mov	r0,r12

000003b8 eaffff10*   580 	b	BerEncoder_encodeLength

                     581 	.endf	BerEncoder_encodeTL

                     582 	.align	4

                     583 

                     584 ;ucTag	r0	param

                     585 ;iLength	r4	param

                     586 ;pBuffer	r12	param

                     587 ;iBufPos	none	param

                     588 

                     589 	.section ".bss","awb"

                     590 .L1198:

                     591 	.data

                     592 	.text

                     593 

                     594 ;149: }


                     595 

                     596 ;150: 


                     597 ;151: int BerDecoder_decodeString(uint8_t* buf, int bufPos, int maxPos, uint8_t** str, int* strLen)


                     598 	.align	4

                     599 	.align	4


                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
                     600 BerDecoder_decodeString::

000003bc e92d4070    601 	stmfd	[sp]!,{r4-r6,lr}

                     602 ;152: {


                     603 

                     604 ;153:     uint8_t tag = buf[bufPos++];


                     605 

000003c0 e1a05003    606 	mov	r5,r3

000003c4 e1a0c002    607 	mov	r12,r2

000003c8 e1a0300c    608 	mov	r3,r12

000003cc e59d4010    609 	ldr	r4,[sp,16]

000003d0 e2812001    610 	add	r2,r1,1

                     611 ;154:     bufPos = BerDecoder_decodeLength(buf, strLen, bufPos,  maxPos);


                     612 

000003d4 e1a01004    613 	mov	r1,r4

000003d8 e1a06000    614 	mov	r6,r0

000003dc ebffff17*   615 	bl	BerDecoder_decodeLength

000003e0 e1a01000    616 	mov	r1,r0

                     617 ;155:     if(bufPos == -1)


                     618 

000003e4 e3710001    619 	cmn	r1,1

                     620 ;156:     {


                     621 

                     622 ;157:         return -1;


                     623 

                     624 ;158:     }


                     625 ;159:     *str = buf + bufPos;


                     626 

000003e8 10810006    627 	addne	r0,r1,r6

000003ec 15850000    628 	strne	r0,[r5]

                     629 ;160:     return bufPos + *strLen;


                     630 

000003f0 15940000    631 	ldrne	r0,[r4]

000003f4 10800001    632 	addne	r0,r0,r1

000003f8 e8bd8070    633 	ldmfd	[sp]!,{r4-r6,pc}

                     634 	.endf	BerDecoder_decodeString

                     635 	.align	4

                     636 

                     637 ;buf	r6	param

                     638 ;bufPos	r1	param

                     639 ;maxPos	r12	param

                     640 ;str	r5	param

                     641 ;strLen	r4	param

                     642 

                     643 	.section ".bss","awb"

                     644 .L1253:

                     645 	.data

                     646 	.text

                     647 

                     648 ;161: }


                     649 

                     650 ;162: 


                     651 ;163: int EncodeBOOLEAN( unsigned char* pBuffer, unsigned char bValue )


                     652 	.align	4

                     653 	.align	4

                     654 EncodeBOOLEAN::

                     655 ;164: {


                     656 

                     657 ;165:     pBuffer[0] = ASN_BOOLEAN;	//идентификатор


                     658 

000003fc e3a02001    659 	mov	r2,1

00000400 e5c02000    660 	strb	r2,[r0]


                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
                     661 ;166:     pBuffer[1] = 0x01;			//длина


                     662 

00000404 e5c02001    663 	strb	r2,[r0,1]

                     664 ;167:     pBuffer[2] = 0x00;			//значение


                     665 

00000408 e3a02000    666 	mov	r2,0

0000040c e5c02002    667 	strb	r2,[r0,2]

                     668 ;168:     if( bValue > 0 )


                     669 

00000410 e3510000    670 	cmp	r1,0

                     671 ;169:     {


                     672 

                     673 ;170:         pBuffer[2] = 0x01;


                     674 

00000414 13a01001    675 	movne	r1,1

00000418 15c01002    676 	strneb	r1,[r0,2]

                     677 ;171:     }


                     678 ;172:     return ASN_BOOLEANTYPE_SIZE;


                     679 

0000041c e3a00003    680 	mov	r0,3

00000420 e12fff1e*   681 	ret	

                     682 	.endf	EncodeBOOLEAN

                     683 	.align	4

                     684 

                     685 ;pBuffer	r0	param

                     686 ;bValue	r1	param

                     687 

                     688 	.section ".bss","awb"

                     689 .L1309:

                     690 	.data

                     691 	.text

                     692 

                     693 ;173: }


                     694 

                     695 ;174: 


                     696 ;175: int DecodeBOOLEAN( unsigned char* pBuffer, int iBufPos, unsigned char* pValueOut )


                     697 	.align	4

                     698 	.align	4

                     699 DecodeBOOLEAN::

                     700 ;176: {


                     701 

                     702 ;177: 


                     703 ;178:     if( pBuffer[iBufPos] != ASN_BOOLEAN )


                     704 

00000424 e7d03001    705 	ldrb	r3,[r0,r1]

00000428 e3530001    706 	cmp	r3,1

                     707 ;179:     {//не тип BOOLEAN


                     708 

                     709 ;180:         return -1;


                     710 

                     711 ;181:     }


                     712 ;182:     if( pBuffer[iBufPos+1] != 0x01 )


                     713 

0000042c 00803001    714 	addeq	r3,r0,r1

00000430 05d33001    715 	ldreqb	r3,[r3,1]

00000434 03530001    716 	cmpeq	r3,1

                     717 ;185:     }


                     718 ;186: 


                     719 ;187: 


                     720 ;188:     *pValueOut = pBuffer[iBufPos+2];


                     721 


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
00000438 00800001    722 	addeq	r0,r0,r1

0000043c 05d00002    723 	ldreqb	r0,[r0,2]

00000440 05c20000    724 	streqb	r0,[r2]

                     725 ;189: 


                     726 ;190:     return iBufPos+3;


                     727 

00000444 02810003    728 	addeq	r0,r1,3

                     729 ;183:     {//размер не 0x01


                     730 

                     731 ;184:         return -1;


                     732 

00000448 13e00000    733 	mvnne	r0,0

0000044c e12fff1e*   734 	ret	

                     735 	.endf	DecodeBOOLEAN

                     736 	.align	4

                     737 

                     738 ;pBuffer	r0	param

                     739 ;iBufPos	r1	param

                     740 ;pValueOut	r2	param

                     741 

                     742 	.section ".bss","awb"

                     743 .L1388:

                     744 	.data

                     745 	.text

                     746 

                     747 ;191: }


                     748 

                     749 ;192: 


                     750 ;193: int EncodeBIT_STRINGPrimitive( unsigned char* pBuffer, unsigned char* pBitString, int iLengthInBit )


                     751 	.align	4

                     752 	.align	4

                     753 EncodeBIT_STRINGPrimitive::

00000450 e92d4010    754 	stmfd	[sp]!,{r4,lr}

                     755 ;194: {


                     756 

00000454 e3a03000    757 	mov	r3,0

                     758 ;195:     unsigned char ucRemn = (unsigned char)( iLengthInBit & 0x07 );	//остаток от деления на 8


                     759 

00000458 e212c007    760 	ands	r12,r2,7

                     761 ;196:     unsigned char ucUnusedBitsQuant = 0;							//количество незначащих бит в конце


                     762 

                     763 ;197:     int			  iSize;											//длина всей посылки ASN


                     764 ;198: 


                     765 ;199:     if( ucRemn > 0 )


                     766 

                     767 ;200:     {


                     768 

                     769 ;201:         ucUnusedBitsQuant = 8 - ucRemn;


                     770 

0000045c 126c3008    771 	rsbne	r3,r12,8

00000460 120330ff    772 	andne	r3,r3,255

                     773 ;202:     }


                     774 ;203: 


                     775 ;204:     iSize = ( iLengthInBit + ucUnusedBitsQuant ) / 8 + 1;	//общая длина в байтах ( +1 это головной байт для незначащих бит )


                     776 

00000464 e0832002    777 	add	r2,r3,r2

00000468 e1a0cfc2    778 	mov	r12,r2 asr 31

0000046c e0822eac    779 	add	r2,r2,r12 lsr 29

00000470 e1a021c2    780 	mov	r2,r2 asr 3

00000474 e2824001    781 	add	r4,r2,1

                     782 ;205: 



                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
                     783 ;206: 


                     784 ;207:     pBuffer[0] = ASN_BIT_STRING;			//идентификатор BIT_STRING


                     785 

00000478 e3a02003    786 	mov	r2,3

0000047c e5c02000    787 	strb	r2,[r0]

                     788 ;208:     if( iSize > 127 )


                     789 

00000480 e354007f    790 	cmp	r4,127

                     791 ;209:     {


                     792 

                     793 ;210:      //BIT_STRING такого размера не используются


                     794 ;211:         return 0;


                     795 

00000484 c3a00000    796 	movgt	r0,0

00000488 ca000005    797 	bgt	.L1402

                     798 ;212:     }


                     799 ;213:     else


                     800 ;214:     {


                     801 

                     802 ;215:         pBuffer[1] = (unsigned char)iSize;				//длина


                     803 

0000048c e2442001    804 	sub	r2,r4,1

00000490 e5c04001    805 	strb	r4,[r0,1]

                     806 ;216:         pBuffer[2] = ucUnusedBitsQuant;					//количество незначащих бит в конце посылки


                     807 

00000494 e5c03002    808 	strb	r3,[r0,2]

                     809 ;217:         memcpy( &pBuffer[3], pBitString, iSize - 1 );	//битовая строка


                     810 

00000498 e2800003    811 	add	r0,r0,3

0000049c eb000000*   812 	bl	memcpy

                     813 ;218:         return iSize + 2;


                     814 

000004a0 e2840002    815 	add	r0,r4,2

                     816 .L1402:

000004a4 e8bd8010    817 	ldmfd	[sp]!,{r4,pc}

                     818 	.endf	EncodeBIT_STRINGPrimitive

                     819 	.align	4

                     820 ;ucRemn	r12	local

                     821 ;ucUnusedBitsQuant	r3	local

                     822 ;iSize	r4	local

                     823 

                     824 ;pBuffer	r0	param

                     825 ;pBitString	r1	param

                     826 ;iLengthInBit	r2	param

                     827 

                     828 	.section ".bss","awb"

                     829 .L1463:

                     830 	.data

                     831 	.text

                     832 

                     833 ;219:     }


                     834 ;220: }


                     835 

                     836 ;221: int DecodeBIT_STRINGPrimitive( unsigned char* pBuffer, unsigned char* pBitStringOut,


                     837 	.align	4

                     838 	.align	4

                     839 DecodeBIT_STRINGPrimitive::

000004a8 e92d4010    840 	stmfd	[sp]!,{r4,lr}

000004ac e1a0c001    841 	mov	r12,r1

                     842 ;222:                                unsigned char* pBitStringSizeOut, unsigned char* pUnusedBitsQuantOut )


                     843 ;223: {



                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
                     844 

                     845 ;224: 


                     846 ;225:     if( pBuffer[0] != ASN_BIT_STRING )


                     847 

000004b0 e5d01000    848 	ldrb	r1,[r0]

000004b4 e3510003    849 	cmp	r1,3

000004b8 1a000002    850 	bne	.L1487

                     851 ;226:     {//не тип BIT_STRING


                     852 

                     853 ;227:         return -1;


                     854 

                     855 ;228:     }


                     856 ;229: 


                     857 ;230:     if( pBuffer[1] > 127 )


                     858 

000004bc e5d01001    859 	ldrb	r1,[r0,1]

000004c0 e351007f    860 	cmp	r1,127

000004c4 9a000001    861 	bls	.L1486

                     862 .L1487:

                     863 ;231:     {//неверный размер


                     864 

                     865 ;232:         return -1;


                     866 

000004c8 e3e04000    867 	mvn	r4,0

000004cc ea00000b    868 	b	.L1481

                     869 .L1486:

                     870 ;233:     }


                     871 ;234: 


                     872 ;235:     if( pBuffer[1] == 1 )


                     873 

000004d0 e3a04000    874 	mov	r4,0

000004d4 e3510001    875 	cmp	r1,1

                     876 ;236:     {//пустая битовая строка


                     877 

                     878 ;237:         *pBitStringSizeOut = 0;


                     879 

000004d8 05c24000    880 	streqb	r4,[r2]

                     881 ;238:         return 0;


                     882 

000004dc 0a000007    883 	beq	.L1481

                     884 ;239:     }


                     885 ;240: 


                     886 ;241:     *pBitStringSizeOut = pBuffer[1] - 1;


                     887 

000004e0 e2411001    888 	sub	r1,r1,1

000004e4 e5c21000    889 	strb	r1,[r2]

                     890 ;242:     *pUnusedBitsQuantOut = pBuffer[2];


                     891 

000004e8 e5d01002    892 	ldrb	r1,[r0,2]

000004ec e5c31000    893 	strb	r1,[r3]

                     894 ;243:     memcpy( pBitStringOut, &pBuffer[3], (int)(*pBitStringSizeOut) );


                     895 

000004f0 e5d22000    896 	ldrb	r2,[r2]

000004f4 e2801003    897 	add	r1,r0,3

000004f8 e1a0000c    898 	mov	r0,r12

000004fc eb000000*   899 	bl	memcpy

                     900 ;244:     return 0;


                     901 

                     902 .L1481:

00000500 e1a00004    903 	mov	r0,r4

00000504 e8bd8010    904 	ldmfd	[sp]!,{r4,pc}


                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
                     905 	.endf	DecodeBIT_STRINGPrimitive

                     906 	.align	4

                     907 

                     908 ;pBuffer	r0	param

                     909 ;pBitStringOut	r12	param

                     910 ;pBitStringSizeOut	r2	param

                     911 ;pUnusedBitsQuantOut	r3	param

                     912 

                     913 	.section ".bss","awb"

                     914 .L1560:

                     915 	.data

                     916 	.text

                     917 

                     918 ;245: }


                     919 

                     920 ;246: 


                     921 ;247: int BerDecoder_DecodeBitStringTLToInt(uint8_t* inBuf, int inBufPos)


                     922 	.align	4

                     923 	.align	4

                     924 BerDecoder_DecodeBitStringTLToInt::

00000508 e92d0030    925 	stmfd	[sp]!,{r4-r5}

                     926 ;248: {


                     927 

0000050c e2811001    928 	add	r1,r1,1

                     929 ;255:     len = inBuf[inBufPos++];


                     930 

00000510 e7d03001    931 	ldrb	r3,[r0,r1]

00000514 e3a02000    932 	mov	r2,0

                     933 ;249:     int result = 0;


                     934 

                     935 ;250:     int len;


                     936 ;251:     int padding;


                     937 ;252:     int i;


                     938 ;253:     //Skip tag


                     939 ;254:     inBufPos++;


                     940 

00000518 e3530005    941 	cmp	r3,5

                     942 ;257:     {


                     943 

                     944 ;258:         return -1;


                     945 

                     946 ;259:     }


                     947 ;260:     len--;//padding не считаем


                     948 

0000051c d2433001    949 	suble	r3,r3,1

                     950 ;261:     padding = inBuf[inBufPos++];


                     951 

00000520 e2811001    952 	add	r1,r1,1

                     953 ;256:     if (len > 5)


                     954 

00000524 d7d0c001    955 	ldrleb	r12,[r0,r1]

00000528 d2811001    956 	addle	r1,r1,1

                     957 ;262:     if (padding > 7)


                     958 

0000052c d35c0007    959 	cmple	r12,7

                     960 ;263:     {


                     961 

                     962 ;264:         return -1;


                     963 

00000530 c3e00000    964 	mvngt	r0,0

00000534 ca000026    965 	bgt	.L1583


                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
                     966 ;265:     }


                     967 ;266:     //Байт который лежит последним, должен быть младшим


                     968 ;267:     for (i = 0; i < len; ++i)


                     969 

00000538 e3530000    970 	cmp	r3,0

0000053c a1a04003    971 	movge	r4,r3

00000540 b3a04000    972 	movlt	r4,0

00000544 e1b031a4    973 	movs	r3,r4 lsr 3

00000548 0a000019    974 	beq	.L1637

                     975 .L1653:

0000054c e7d05001    976 	ldrb	r5,[r0,r1]

00000550 e2811001    977 	add	r1,r1,1

00000554 e1852402    978 	orr	r2,r5,r2 lsl 8

00000558 e7d05001    979 	ldrb	r5,[r0,r1]

0000055c e2811001    980 	add	r1,r1,1

00000560 e1852402    981 	orr	r2,r5,r2 lsl 8

00000564 e7d05001    982 	ldrb	r5,[r0,r1]

00000568 e2811001    983 	add	r1,r1,1

0000056c e1852402    984 	orr	r2,r5,r2 lsl 8

00000570 e7d05001    985 	ldrb	r5,[r0,r1]

00000574 e2811001    986 	add	r1,r1,1

00000578 e1852402    987 	orr	r2,r5,r2 lsl 8

0000057c e7d05001    988 	ldrb	r5,[r0,r1]

00000580 e2811001    989 	add	r1,r1,1

00000584 e1852402    990 	orr	r2,r5,r2 lsl 8

00000588 e7d05001    991 	ldrb	r5,[r0,r1]

0000058c e2811001    992 	add	r1,r1,1

00000590 e1852402    993 	orr	r2,r5,r2 lsl 8

00000594 e7d05001    994 	ldrb	r5,[r0,r1]

00000598 e2811001    995 	add	r1,r1,1

0000059c e1852402    996 	orr	r2,r5,r2 lsl 8

000005a0 e7d05001    997 	ldrb	r5,[r0,r1]

000005a4 e2811001    998 	add	r1,r1,1

000005a8 e1852402    999 	orr	r2,r5,r2 lsl 8

000005ac e2533001   1000 	subs	r3,r3,1

000005b0 1affffe5   1001 	bne	.L1653

                    1002 .L1637:

000005b4 e2143007   1003 	ands	r3,r4,7

                    1004 .L1657:

000005b8 17d04001   1005 	ldrneb	r4,[r0,r1]

000005bc 12811001   1006 	addne	r1,r1,1

000005c0 11842402   1007 	orrne	r2,r4,r2 lsl 8

000005c4 12533001   1008 	subnes	r3,r3,1

000005c8 1afffffa   1009 	bne	.L1657

                    1010 .L1595:

000005cc e1b00c52   1011 	movs	r0,r2 asr r12

000005d0 43e00000   1012 	mvnmi	r0,0

                    1013 ;271:     }


                    1014 ;272:     result >>= padding;


                    1015 

                    1016 ;273:     if (result < 0)


                    1017 

                    1018 

                    1019 

                    1020 ;276:     }


                    1021 ;277:     return result;


                    1022 

                    1023 .L1583:

000005d4 e8bd0030   1024 	ldmfd	[sp]!,{r4-r5}

000005d8 e12fff1e*  1025 	ret	

                    1026 	.endf	BerDecoder_DecodeBitStringTLToInt


                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
                    1027 	.align	4

                    1028 ;result	r2	local

                    1029 ;len	r3	local

                    1030 ;padding	r12	local

                    1031 

                    1032 ;inBuf	r0	param

                    1033 ;inBufPos	r1	param

                    1034 

                    1035 	.section ".bss","awb"

                    1036 .L1846:

                    1037 	.data

                    1038 	.text

                    1039 

                    1040 ;278: }


                    1041 

                    1042 ;279: 


                    1043 ;280: int BerDecoder_DecodeLengthOld(unsigned char* pBuffer, int iBufPos, int iMaxBufPos, int* pLength)


                    1044 	.align	4

                    1045 	.align	4

                    1046 BerDecoder_DecodeLengthOld::

000005dc e92d0070   1047 	stmfd	[sp]!,{r4-r6}

                    1048 ;281: {


                    1049 

                    1050 ;282: 


                    1051 ;283:     unsigned char	ucLength;


                    1052 ;284:     int				iLenLength;


                    1053 ;285: 


                    1054 ;286: 


                    1055 ;287:     if( iBufPos >= iMaxBufPos )


                    1056 

000005e0 e1510002   1057 	cmp	r1,r2

                    1058 .L1881:

                    1059 ;288:     {


                    1060 

                    1061 ;289: 


                    1062 ;290:         return -1;


                    1063 

000005e4 a3e00000   1064 	mvnge	r0,0

000005e8 aa00005f   1065 	bge	.L1878

                    1066 .L1880:

                    1067 ;291: 


                    1068 ;292:     }//if( iBufPos > iMaxBufPos )


                    1069 ;293: 


                    1070 ;294: 


                    1071 ;295:     ucLength = pBuffer[iBufPos++];


                    1072 

000005ec e7d0c001   1073 	ldrb	r12,[r0,r1]

000005f0 e2811001   1074 	add	r1,r1,1

                    1075 ;296: 


                    1076 ;297:     if ( ucLength & 0x80 )


                    1077 

000005f4 e31c0080   1078 	tst	r12,128

                    1079 ;322: 


                    1080 ;323:             }//for( i = 0; i < iLenLength; i++ )


                    1081 ;324: 


                    1082 ;325:         }//else if( iLenLength == 0 )


                    1083 ;326: 


                    1084 ;327:     }//if ( ucLength & 0x80 )


                    1085 ;328:     else


                    1086 ;329:     {


                    1087 


                                                                      Page 19
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
                    1088 ;330: 


                    1089 ;331:         *pLength = ucLength;


                    1090 

000005f8 0583c000   1091 	streq	r12,[r3]

000005fc 0a000059   1092 	beq	.L1895

                    1093 ;298:     {//кодирование длины занимает больше одного байта


                    1094 

                    1095 ;299: 


                    1096 ;300:         iLenLength = ucLength & 0x7f;


                    1097 

00000600 e21cc07f   1098 	ands	r12,r12,127

                    1099 ;301:         if( iLenLength == 0 )


                    1100 

                    1101 ;302:         {//неопределенная форма длины


                    1102 

                    1103 ;303: 


                    1104 ;304:             *pLength = -1;


                    1105 

00000604 03e0c000   1106 	mvneq	r12,0

00000608 0583c000   1107 	streq	r12,[r3]

                    1108 ;332: 


                    1109 ;333:     }//else if ( ucLength & 0x80 )


                    1110 ;334: 


                    1111 ;335:     return iBufPos;


                    1112 

0000060c 01a00001   1113 	moveq	r0,r1

00000610 0a000055   1114 	beq	.L1878

                    1115 ;305: 


                    1116 ;306:         }//if( iLenLength == 0 )


                    1117 ;307:         else


                    1118 ;308:         {


                    1119 

                    1120 ;309:             int i;


                    1121 ;310:             *pLength = 0;


                    1122 

00000614 e3a05000   1123 	mov	r5,0

00000618 e5835000   1124 	str	r5,[r3]

                    1125 ;311:             for( i = 0; i < iLenLength; i++ )


                    1126 

0000061c e35c0000   1127 	cmp	r12,0

00000620 a1a0400c   1128 	movge	r4,r12

00000624 b3a04000   1129 	movlt	r4,0

00000628 e1b0c1a4   1130 	movs	r12,r4 lsr 3

0000062c 0a000041   1131 	beq	.L1949

                    1132 .L1950:

00000630 e1510002   1133 	cmp	r1,r2

00000634 aaffffea   1134 	bge	.L1881

00000638 e1a05405   1135 	mov	r5,r5 lsl 8

0000063c e5835000   1136 	str	r5,[r3]

00000640 e7d06001   1137 	ldrb	r6,[r0,r1]

00000644 e2811001   1138 	add	r1,r1,1

00000648 e0855006   1139 	add	r5,r5,r6

0000064c e5835000   1140 	str	r5,[r3]

00000650 e1510002   1141 	cmp	r1,r2

00000654 aaffffe2   1142 	bge	.L1881

00000658 e1a05405   1143 	mov	r5,r5 lsl 8

0000065c e5835000   1144 	str	r5,[r3]

00000660 e7d06001   1145 	ldrb	r6,[r0,r1]

00000664 e2811001   1146 	add	r1,r1,1

00000668 e0855006   1147 	add	r5,r5,r6

0000066c e5835000   1148 	str	r5,[r3]


                                                                      Page 20
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
00000670 e1510002   1149 	cmp	r1,r2

00000674 aaffffda   1150 	bge	.L1881

00000678 e1a05405   1151 	mov	r5,r5 lsl 8

0000067c e5835000   1152 	str	r5,[r3]

00000680 e7d06001   1153 	ldrb	r6,[r0,r1]

00000684 e2811001   1154 	add	r1,r1,1

00000688 e0855006   1155 	add	r5,r5,r6

0000068c e5835000   1156 	str	r5,[r3]

00000690 e1510002   1157 	cmp	r1,r2

00000694 aaffffd2   1158 	bge	.L1881

00000698 e1a05405   1159 	mov	r5,r5 lsl 8

0000069c e5835000   1160 	str	r5,[r3]

000006a0 e7d06001   1161 	ldrb	r6,[r0,r1]

000006a4 e2811001   1162 	add	r1,r1,1

000006a8 e0855006   1163 	add	r5,r5,r6

000006ac e5835000   1164 	str	r5,[r3]

000006b0 e1510002   1165 	cmp	r1,r2

000006b4 aaffffca   1166 	bge	.L1881

000006b8 e1a05405   1167 	mov	r5,r5 lsl 8

000006bc e5835000   1168 	str	r5,[r3]

000006c0 e7d06001   1169 	ldrb	r6,[r0,r1]

000006c4 e2811001   1170 	add	r1,r1,1

000006c8 e0855006   1171 	add	r5,r5,r6

000006cc e5835000   1172 	str	r5,[r3]

000006d0 e1510002   1173 	cmp	r1,r2

000006d4 aaffffc2   1174 	bge	.L1881

000006d8 e1a05405   1175 	mov	r5,r5 lsl 8

000006dc e5835000   1176 	str	r5,[r3]

000006e0 e7d06001   1177 	ldrb	r6,[r0,r1]

000006e4 e2811001   1178 	add	r1,r1,1

000006e8 e0855006   1179 	add	r5,r5,r6

000006ec e5835000   1180 	str	r5,[r3]

000006f0 e1510002   1181 	cmp	r1,r2

000006f4 aaffffba   1182 	bge	.L1881

000006f8 e1a05405   1183 	mov	r5,r5 lsl 8

000006fc e5835000   1184 	str	r5,[r3]

00000700 e7d06001   1185 	ldrb	r6,[r0,r1]

00000704 e2811001   1186 	add	r1,r1,1

00000708 e0855006   1187 	add	r5,r5,r6

0000070c e5835000   1188 	str	r5,[r3]

00000710 e1510002   1189 	cmp	r1,r2

00000714 aaffffb2   1190 	bge	.L1881

00000718 e1a05405   1191 	mov	r5,r5 lsl 8

0000071c e5835000   1192 	str	r5,[r3]

00000720 e7d06001   1193 	ldrb	r6,[r0,r1]

00000724 e2811001   1194 	add	r1,r1,1

00000728 e0855006   1195 	add	r5,r5,r6

0000072c e5835000   1196 	str	r5,[r3]

00000730 e25cc001   1197 	subs	r12,r12,1

00000734 1affffbd   1198 	bne	.L1950

                    1199 .L1949:

00000738 e214c007   1200 	ands	r12,r4,7

0000073c 0a000009   1201 	beq	.L1895

                    1202 .L1976:

00000740 e1510002   1203 	cmp	r1,r2

00000744 aaffffa6   1204 	bge	.L1881

00000748 e1a05405   1205 	mov	r5,r5 lsl 8

0000074c e5835000   1206 	str	r5,[r3]

00000750 e7d04001   1207 	ldrb	r4,[r0,r1]

00000754 e2811001   1208 	add	r1,r1,1

00000758 e0855004   1209 	add	r5,r5,r4


                                                                      Page 21
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
0000075c e5835000   1210 	str	r5,[r3]

00000760 e25cc001   1211 	subs	r12,r12,1

00000764 1afffff5   1212 	bne	.L1976

                    1213 .L1895:

                    1214 ;332: 


                    1215 ;333:     }//else if ( ucLength & 0x80 )


                    1216 ;334: 


                    1217 ;335:     return iBufPos;


                    1218 

00000768 e1a00001   1219 	mov	r0,r1

                    1220 .L1878:

0000076c e8bd0070   1221 	ldmfd	[sp]!,{r4-r6}

00000770 e12fff1e*  1222 	ret	

                    1223 	.endf	BerDecoder_DecodeLengthOld

                    1224 	.align	4

                    1225 ;ucLength	r12	local

                    1226 ;iLenLength	r12	local

                    1227 

                    1228 ;pBuffer	r0	param

                    1229 ;iBufPos	r1	param

                    1230 ;iMaxBufPos	r2	param

                    1231 ;pLength	r3	param

                    1232 

                    1233 	.section ".bss","awb"

                    1234 .L2253:

                    1235 	.data

                    1236 	.text

                    1237 

                    1238 ;336: }


                    1239 

                    1240 ;337: 


                    1241 ;338: unsigned int BerDecoder_DecodeUint32Old( unsigned char* pBuffer, int iBufPos, int iLength )


                    1242 	.align	4

                    1243 	.align	4

                    1244 BerDecoder_DecodeUint32Old::

00000774 e92d0070   1245 	stmfd	[sp]!,{r4-r6}

                    1246 ;339: {


                    1247 

00000778 e3a03000   1248 	mov	r3,0

                    1249 ;340: 


                    1250 ;341:     unsigned int Value = 0;


                    1251 

                    1252 ;342:     int i;


                    1253 ;343: 


                    1254 ;344:     for( i = 0; i < iLength; i++ )


                    1255 

0000077c e1a04003   1256 	mov	r4,r3

00000780 e3520000   1257 	cmp	r2,0

00000784 a1a05002   1258 	movge	r5,r2

00000788 b3a05000   1259 	movlt	r5,0

0000078c e1b0c1a5   1260 	movs	r12,r5 lsr 3

00000790 0a000013   1261 	beq	.L2350

00000794 e0812000   1262 	add	r2,r1,r0

00000798 e1a0418c   1263 	mov	r4,r12 lsl 3

                    1264 .L2366:

0000079c e4d26001   1265 	ldrb	r6,[r2],1

000007a0 e0863403   1266 	add	r3,r6,r3 lsl 8

000007a4 e4d26001   1267 	ldrb	r6,[r2],1

000007a8 e0863403   1268 	add	r3,r6,r3 lsl 8

000007ac e4d26001   1269 	ldrb	r6,[r2],1

000007b0 e0863403   1270 	add	r3,r6,r3 lsl 8


                                                                      Page 22
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
000007b4 e4d26001   1271 	ldrb	r6,[r2],1

000007b8 e0863403   1272 	add	r3,r6,r3 lsl 8

000007bc e4d26001   1273 	ldrb	r6,[r2],1

000007c0 e0863403   1274 	add	r3,r6,r3 lsl 8

000007c4 e4d26001   1275 	ldrb	r6,[r2],1

000007c8 e0863403   1276 	add	r3,r6,r3 lsl 8

000007cc e4d26001   1277 	ldrb	r6,[r2],1

000007d0 e0863403   1278 	add	r3,r6,r3 lsl 8

000007d4 e4d26001   1279 	ldrb	r6,[r2],1

000007d8 e25cc001   1280 	subs	r12,r12,1

000007dc e0863403   1281 	add	r3,r6,r3 lsl 8

000007e0 1affffed   1282 	bne	.L2366

                    1283 .L2350:

000007e4 e215c007   1284 	ands	r12,r5,7

000007e8 10800001   1285 	addne	r0,r0,r1

000007ec 10840000   1286 	addne	r0,r4,r0

                    1287 .L2370:

000007f0 14d01001   1288 	ldrneb	r1,[r0],1

000007f4 10813403   1289 	addne	r3,r1,r3 lsl 8

000007f8 125cc001   1290 	subnes	r12,r12,1

000007fc 1afffffb   1291 	bne	.L2370

                    1292 .L2323:

                    1293 ;349: 


                    1294 ;350:     }//for( i = 0; i < iLength; i++ )


                    1295 ;351: 


                    1296 ;352:     return Value;


                    1297 

00000800 e1a00003   1298 	mov	r0,r3

00000804 e8bd0070   1299 	ldmfd	[sp]!,{r4-r6}

00000808 e12fff1e*  1300 	ret	

                    1301 	.endf	BerDecoder_DecodeUint32Old

                    1302 	.align	4

                    1303 ;Value	r3	local

                    1304 ;i	r4	local

                    1305 

                    1306 ;pBuffer	r0	param

                    1307 ;iBufPos	r1	param

                    1308 ;iLength	r2	param

                    1309 

                    1310 	.section ".bss","awb"

                    1311 .L2529:

                    1312 	.data

                    1313 	.text

                    1314 

                    1315 ;353: }


                    1316 

                    1317 ;354: 


                    1318 ;355: int BerDecoder_DecodeObjectName(unsigned char* pBuffer, int bufPos, int iTotalLength,


                    1319 	.align	4

                    1320 	.align	4

                    1321 BerDecoder_DecodeObjectName::

0000080c e92d4cf0   1322 	stmfd	[sp]!,{r4-r7,r10-fp,lr}

                    1323 ;356:     unsigned char** pItemId, int* itemIdLen, unsigned char** pDomainId, int* domainLen)


                    1324 ;357: {


                    1325 

                    1326 ;358:     unsigned char   tag=0xff;


                    1327 

                    1328 ;359:     int             totalNameLength;


                    1329 ;360: 


                    1330 ;361:     tag = pBuffer[bufPos++];


                    1331 


                                                                      Page 23
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
00000810 e24dd00c   1332 	sub	sp,sp,12

00000814 e59d6028   1333 	ldr	r6,[sp,40]

00000818 e59d702c   1334 	ldr	r7,[sp,44]

0000081c e59da030   1335 	ldr	r10,[sp,48]

00000820 e1a0b003   1336 	mov	fp,r3

00000824 e1a04002   1337 	mov	r4,r2

00000828 e2812001   1338 	add	r2,r1,1

0000082c e7d01001   1339 	ldrb	r1,[r0,r1]

00000830 e1a03004   1340 	mov	r3,r4

00000834 e5cd1007   1341 	strb	r1,[sp,7]

                    1342 ;362:     bufPos = BerDecoder_decodeLength(pBuffer, &totalNameLength, bufPos, iTotalLength);


                    1343 

00000838 e28d1008   1344 	add	r1,sp,8

0000083c e1a05000   1345 	mov	r5,r0

00000840 ebfffdfe*  1346 	bl	BerDecoder_decodeLength

00000844 e1a01000   1347 	mov	r1,r0

                    1348 ;363: 


                    1349 ;364:     if (bufPos == -1)


                    1350 

00000848 e3710001   1351 	cmn	r1,1

0000084c 0a000012   1352 	beq	.L2565

                    1353 ;365:     {


                    1354 

                    1355 ;366:         return -1;


                    1356 

                    1357 ;367:     }


                    1358 ;368: 


                    1359 ;369:     switch(tag)


                    1360 

00000850 e5dd0007   1361 	ldrb	r0,[sp,7]

00000854 e35000a1   1362 	cmp	r0,161

00000858 1a00000f   1363 	bne	.L2565

                    1364 ;370:     {


                    1365 ;371:     case ASN_OBJECT_NAME_VMD_SPECIFIC:


                    1366 ;372:         break;


                    1367 ;373:     case ASN_OBJECT_NAME_DOMAIN_SPECIFIC:


                    1368 ;374:         //Домен


                    1369 ;375:         bufPos = BerDecoder_decodeString(pBuffer, bufPos, iTotalLength, pDomainId, domainLen);


                    1370 

0000085c e58da000   1371 	str	r10,[sp]

00000860 e1a03007   1372 	mov	r3,r7

00000864 e1a02004   1373 	mov	r2,r4

00000868 e1a00005   1374 	mov	r0,r5

0000086c ebfffed2*  1375 	bl	BerDecoder_decodeString

00000870 e1a01000   1376 	mov	r1,r0

                    1377 ;376:         if (bufPos == -1)


                    1378 

00000874 e3710001   1379 	cmn	r1,1

00000878 0a000007   1380 	beq	.L2565

                    1381 ;377:         {


                    1382 

                    1383 ;378:             break;


                    1384 

                    1385 ;379:         }


                    1386 ;380:         bufPos = BerDecoder_decodeString(pBuffer, bufPos, iTotalLength, pItemId, itemIdLen);


                    1387 

0000087c e58d6000   1388 	str	r6,[sp]

00000880 e1a0300b   1389 	mov	r3,fp

00000884 e1a02004   1390 	mov	r2,r4

00000888 e1a00005   1391 	mov	r0,r5

0000088c ebfffeca*  1392 	bl	BerDecoder_decodeString


                                                                      Page 24
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
                    1393 ;381:         if (bufPos == -1)


                    1394 

                    1395 

                    1396 

                    1397 

                    1398 

00000890 e3700001   1399 	cmn	r0,1

00000894 03e00000   1400 	mvneq	r0,0

00000898 ea000000   1401 	b	.L2554

                    1402 .L2565:

                    1403 ;386:     case ASN_OBJECT_NAME_AA_SPECIFIC:


                    1404 ;387:         break;


                    1405 ;388:     default:


                    1406 ;389:         break;


                    1407 ;390:     }


                    1408 ;391:     return -1;


                    1409 

0000089c e3e00000   1410 	mvn	r0,0

                    1411 .L2554:

000008a0 e28dd00c   1412 	add	sp,sp,12

000008a4 e8bd8cf0   1413 	ldmfd	[sp]!,{r4-r7,r10-fp,pc}

                    1414 	.endf	BerDecoder_DecodeObjectName

                    1415 	.align	4

                    1416 ;tag	[sp,7]	local

                    1417 ;totalNameLength	[sp,8]	local

                    1418 

                    1419 ;pBuffer	r5	param

                    1420 ;bufPos	r1	param

                    1421 ;iTotalLength	r4	param

                    1422 ;pItemId	fp	param

                    1423 ;itemIdLen	r6	param

                    1424 ;pDomainId	r7	param

                    1425 ;domainLen	r10	param

                    1426 

                    1427 	.section ".bss","awb"

                    1428 .L2640:

                    1429 	.data

                    1430 	.text

                    1431 

                    1432 ;392: }


                    1433 

                    1434 ;393: 


                    1435 ;394: int BerDecoder_DecodeObjectNameToStringView(unsigned char* pBuffer, int bufPos,


                    1436 	.align	4

                    1437 	.align	4

                    1438 BerDecoder_DecodeObjectNameToStringView::

000008a8 e92d4000   1439 	stmfd	[sp]!,{lr}

                    1440 ;395:     int iTotalLength, StringView* domainId, StringView* itemId)


                    1441 ;396: {


                    1442 

                    1443 ;397:     return BerDecoder_DecodeObjectName(pBuffer, bufPos, iTotalLength,


                    1444 

000008ac e24dd00c   1445 	sub	sp,sp,12

000008b0 e59dc010   1446 	ldr	r12,[sp,16]

000008b4 e1a0e003   1447 	mov	lr,r3

000008b8 e2833004   1448 	add	r3,r3,4

000008bc e98d4008   1449 	stmfa	[sp],{r3,lr}

000008c0 e58dc000   1450 	str	r12,[sp]

000008c4 e28c3004   1451 	add	r3,r12,4

000008c8 ebffffcf*  1452 	bl	BerDecoder_DecodeObjectName

000008cc e28dd00c   1453 	add	sp,sp,12


                                                                      Page 25
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
000008d0 e8bd8000   1454 	ldmfd	[sp]!,{pc}

                    1455 	.endf	BerDecoder_DecodeObjectNameToStringView

                    1456 	.align	4

                    1457 

                    1458 ;pBuffer	none	param

                    1459 ;bufPos	none	param

                    1460 ;iTotalLength	none	param

                    1461 ;domainId	r3	param

                    1462 ;itemId	r12	param

                    1463 

                    1464 	.section ".bss","awb"

                    1465 .L2689:

                    1466 	.data

                    1467 	.text

                    1468 

                    1469 ;398:         (uint8_t**)&itemId->p, (int*)&itemId->len,


                    1470 ;399:         (uint8_t**)&domainId->p, (int*)&domainId->len);


                    1471 ;400: }


                    1472 

                    1473 ;401: 


                    1474 ;402: 


                    1475 ;403: int BerEncoder_determineLengthSize( unsigned int uLength )


                    1476 	.align	4

                    1477 	.align	4

                    1478 BerEncoder_determineLengthSize::

                    1479 ;404: {


                    1480 

                    1481 ;405:     if( uLength < 128 )


                    1482 

000008d4 e3500080   1483 	cmp	r0,128

                    1484 ;406:     {


                    1485 

                    1486 ;407:         return 1;


                    1487 

000008d8 33a00001   1488 	movlo	r0,1

000008dc 3a000002   1489 	blo	.L2696

                    1490 ;408:     }


                    1491 ;409: 


                    1492 ;410:     if( uLength < 256 )


                    1493 

                    1494 

                    1495 

                    1496 

                    1497 

000008e0 e3500f40   1498 	cmp	r0,256

000008e4 23a00003   1499 	movhs	r0,3

000008e8 33a00002   1500 	movlo	r0,2

                    1501 .L2696:

000008ec e12fff1e*  1502 	ret	

                    1503 	.endf	BerEncoder_determineLengthSize

                    1504 	.align	4

                    1505 

                    1506 ;uLength	r0	param

                    1507 

                    1508 	.section ".bss","awb"

                    1509 .L2748:

                    1510 	.data

                    1511 	.text

                    1512 

                    1513 ;417:     }


                    1514 ;418: }



                                                                      Page 26
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
                    1515 

                    1516 ;419: 


                    1517 ;420: int BerEncoder_determineFullObjectSize(unsigned int uLength)


                    1518 	.align	4

                    1519 	.align	4

                    1520 BerEncoder_determineFullObjectSize::

000008f0 e92d4010   1521 	stmfd	[sp]!,{r4,lr}

                    1522 ;421: {


                    1523 

                    1524 ;422:     return 1 + BerEncoder_determineLengthSize(uLength)


                    1525 

000008f4 e1a04000   1526 	mov	r4,r0

000008f8 ebfffff5*  1527 	bl	BerEncoder_determineLengthSize

000008fc e2800001   1528 	add	r0,r0,1

00000900 e0840000   1529 	add	r0,r4,r0

00000904 e8bd8010   1530 	ldmfd	[sp]!,{r4,pc}

                    1531 	.endf	BerEncoder_determineFullObjectSize

                    1532 	.align	4

                    1533 

                    1534 ;uLength	r4	param

                    1535 

                    1536 	.section ".bss","awb"

                    1537 .L2782:

                    1538 	.data

                    1539 	.text

                    1540 

                    1541 ;423:         + uLength;


                    1542 ;424: }


                    1543 

                    1544 ;425: 


                    1545 ;426: 


                    1546 ;427: int BerEncoder_UInt32determineEncodedSize( unsigned int iValue )


                    1547 	.align	4

                    1548 	.align	4

                    1549 BerEncoder_UInt32determineEncodedSize::

00000908 e92d4000   1550 	stmfd	[sp]!,{lr}

0000090c e24dd00c   1551 	sub	sp,sp,12

00000910 e58d0000   1552 	str	r0,[sp]

00000914 e3a01000   1553 	mov	r1,0

00000918 e5cd1004   1554 	strb	r1,[sp,4]

0000091c e5dd1001   1555 	ldrb	r1,[sp,1]

00000920 e5cd1006   1556 	strb	r1,[sp,6]

00000924 e5dd1002   1557 	ldrb	r1,[sp,2]

00000928 e5cd0005   1558 	strb	r0,[sp,5]

0000092c e5cd1007   1559 	strb	r1,[sp,7]

00000930 e5dd1003   1560 	ldrb	r1,[sp,3]

00000934 e28d0005   1561 	add	r0,sp,5

00000938 e5cd1008   1562 	strb	r1,[sp,8]

                    1563 ;428: {


                    1564 

                    1565 ;429: 


                    1566 ;430:     unsigned char* pValueArray = (unsigned char*)&iValue;


                    1567 

                    1568 ;431:     unsigned char ValueBuffer[5];


                    1569 ;432: 


                    1570 ;433:     int i;


                    1571 ;434:     int iSize;


                    1572 ;435: 


                    1573 ;436:     ValueBuffer[0] = 0;


                    1574 

                    1575 ;437: 



                                                                      Page 27
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
                    1576 ;438: 


                    1577 ;439:     for( i = 0; i < 4; i++ )


                    1578 

                    1579 ;443: 


                    1580 ;444:     }//for( i = 0; i < 4; i++ )


                    1581 ;445: 


                    1582 ;446:     //если ORDER_LITTLE_ENDIAN


                    1583 ;447:     BerEncoder_RevertByteOrder( ValueBuffer + 1, 4 );


                    1584 

0000093c e3a01004   1585 	mov	r1,4

00000940 eb000319*  1586 	bl	BerEncoder_RevertByteOrder

                    1587 ;448:     //если ORDER_LITTLE_ENDIAN


                    1588 ;449:     iSize = BerEncoder_CompressInteger( ValueBuffer, 5 );


                    1589 

00000944 e28d0004   1590 	add	r0,sp,4

00000948 e3a01005   1591 	mov	r1,5

0000094c eb000017*  1592 	bl	BerEncoder_CompressInteger

                    1593 ;450:     return iSize;


                    1594 

00000950 e28dd00c   1595 	add	sp,sp,12

00000954 e8bd8000   1596 	ldmfd	[sp]!,{pc}

                    1597 	.endf	BerEncoder_UInt32determineEncodedSize

                    1598 	.align	4

                    1599 ;ValueBuffer	[sp,4]	local

                    1600 

                    1601 ;iValue	[sp]	param

                    1602 

                    1603 	.section ".bss","awb"

                    1604 .L2840:

                    1605 	.data

                    1606 	.text

                    1607 

                    1608 ;451: }


                    1609 

                    1610 ;452: size_t BerEncoder_uint32determineEncodedSizeTL(uint32_t value)


                    1611 	.align	4

                    1612 	.align	4

                    1613 BerEncoder_uint32determineEncodedSizeTL::

00000958 e92d4000   1614 	stmfd	[sp]!,{lr}

                    1615 ;453: {


                    1616 

                    1617 ;454:     return BerEncoder_UInt32determineEncodedSize(value) + 2;//+ размер и тэг


                    1618 

0000095c ebffffe9*  1619 	bl	BerEncoder_UInt32determineEncodedSize

00000960 e2800002   1620 	add	r0,r0,2

00000964 e8bd8000   1621 	ldmfd	[sp]!,{pc}

                    1622 	.endf	BerEncoder_uint32determineEncodedSizeTL

                    1623 	.align	4

                    1624 

                    1625 ;value	none	param

                    1626 

                    1627 	.section ".bss","awb"

                    1628 .L2878:

                    1629 	.data

                    1630 	.text

                    1631 

                    1632 ;455: }


                    1633 

                    1634 ;456: 


                    1635 ;457: 


                    1636 ;458: int BerEncoder_Int32DetermineEncodedSize(int iValue)



                                                                      Page 28
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
                    1637 	.align	4

                    1638 	.align	4

                    1639 BerEncoder_Int32DetermineEncodedSize::

00000968 e92d4000   1640 	stmfd	[sp]!,{lr}

0000096c e24dd008   1641 	sub	sp,sp,8

00000970 e58d0004   1642 	str	r0,[sp,4]

00000974 e5dd1005   1643 	ldrb	r1,[sp,5]

00000978 e5cd1001   1644 	strb	r1,[sp,1]

0000097c e5dd1006   1645 	ldrb	r1,[sp,6]

00000980 e5cd0000   1646 	strb	r0,[sp]

00000984 e5cd1002   1647 	strb	r1,[sp,2]

00000988 e5dd1007   1648 	ldrb	r1,[sp,7]

0000098c e1a0000d   1649 	mov	r0,sp

00000990 e5cd1003   1650 	strb	r1,[sp,3]

                    1651 ;459: {


                    1652 

                    1653 ;460:     unsigned char* pValueArray = (unsigned char*)&iValue;


                    1654 

                    1655 ;461:     unsigned char ValueBuffer[4];


                    1656 ;462: 


                    1657 ;463:     int i;


                    1658 ;464:     int iSize;


                    1659 ;465: 


                    1660 ;466: 


                    1661 ;467:     for (i = 0; i < 4; i++)


                    1662 

                    1663 ;470:     }


                    1664 ;471:      //если ORDER_LITTLE_ENDIAN


                    1665 ;472:     BerEncoder_RevertByteOrder(ValueBuffer, 4);


                    1666 

00000994 e3a01004   1667 	mov	r1,4

00000998 eb000303*  1668 	bl	BerEncoder_RevertByteOrder

                    1669 ;473:     //если ORDER_LITTLE_ENDIAN


                    1670 ;474:     iSize = BerEncoder_CompressInteger(ValueBuffer, 4);


                    1671 

0000099c e1a0000d   1672 	mov	r0,sp

000009a0 e3a01004   1673 	mov	r1,4

000009a4 eb000001*  1674 	bl	BerEncoder_CompressInteger

                    1675 ;475:     return iSize;


                    1676 

000009a8 e28dd008   1677 	add	sp,sp,8

000009ac e8bd8000   1678 	ldmfd	[sp]!,{pc}

                    1679 	.endf	BerEncoder_Int32DetermineEncodedSize

                    1680 	.align	4

                    1681 ;ValueBuffer	[sp]	local

                    1682 

                    1683 ;iValue	[sp,4]	param

                    1684 

                    1685 	.section ".bss","awb"

                    1686 .L2943:

                    1687 	.data

                    1688 	.text

                    1689 

                    1690 ;476: }


                    1691 

                    1692 ;477: 


                    1693 ;478: int BerEncoder_CompressInteger( unsigned char* pInteger, int iOriginalSize )


                    1694 	.align	4

                    1695 	.align	4

                    1696 BerEncoder_CompressInteger::

000009b0 e92d0010   1697 	stmfd	[sp]!,{r4}


                                                                      Page 29
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
000009b4 e1a02000   1698 	mov	r2,r0

                    1699 ;479: {


                    1700 

                    1701 ;480: 


                    1702 ;481:     unsigned char* pIntegerEnd = pInteger + iOriginalSize - 1;


                    1703 

000009b8 e1a03002   1704 	mov	r3,r2

000009bc e0810002   1705 	add	r0,r1,r2

000009c0 e2400001   1706 	sub	r0,r0,1

                    1707 ;482:     unsigned char* pBytePosition;


                    1708 ;483: 


                    1709 ;484:     int iBytesToDelete;


                    1710 ;485:     int iNewSize;


                    1711 ;486: 


                    1712 ;487:     for( pBytePosition = pInteger; pBytePosition < pIntegerEnd; pBytePosition++ )


                    1713 

000009c4 e050c002   1714 	subs	r12,r0,r2

000009c8 43a0c000   1715 	movmi	r12,0

000009cc e1b001ac   1716 	movs	r0,r12 lsr 3

000009d0 0a000062   1717 	beq	.L3006

                    1718 .L3007:

000009d4 e5d34000   1719 	ldrb	r4,[r3]

000009d8 e3540000   1720 	cmp	r4,0

000009dc 1a000003   1721 	bne	.L3009

000009e0 e5d34001   1722 	ldrb	r4,[r3,1]

000009e4 e3140080   1723 	tst	r4,128

000009e8 1a00006d   1724 	bne	.L2968

000009ec ea000004   1725 	b	.L3013

                    1726 .L3009:

000009f0 e35400ff   1727 	cmp	r4,255

000009f4 1a00006a   1728 	bne	.L2968

000009f8 e5d34001   1729 	ldrb	r4,[r3,1]

000009fc e3140080   1730 	tst	r4,128

00000a00 0a000067   1731 	beq	.L2968

                    1732 .L3013:

00000a04 e5f34001   1733 	ldrb	r4,[r3,1]!

00000a08 e3540000   1734 	cmp	r4,0

00000a0c 1a000003   1735 	bne	.L3015

00000a10 e5d34001   1736 	ldrb	r4,[r3,1]

00000a14 e3140080   1737 	tst	r4,128

00000a18 1a000061   1738 	bne	.L2968

00000a1c ea000004   1739 	b	.L3019

                    1740 .L3015:

00000a20 e35400ff   1741 	cmp	r4,255

00000a24 1a00005e   1742 	bne	.L2968

00000a28 e5d34001   1743 	ldrb	r4,[r3,1]

00000a2c e3140080   1744 	tst	r4,128

00000a30 0a00005b   1745 	beq	.L2968

                    1746 .L3019:

00000a34 e5f34001   1747 	ldrb	r4,[r3,1]!

00000a38 e3540000   1748 	cmp	r4,0

00000a3c 1a000003   1749 	bne	.L3021

00000a40 e5d34001   1750 	ldrb	r4,[r3,1]

00000a44 e3140080   1751 	tst	r4,128

00000a48 1a000055   1752 	bne	.L2968

00000a4c ea000004   1753 	b	.L3025

                    1754 .L3021:

00000a50 e35400ff   1755 	cmp	r4,255

00000a54 1a000052   1756 	bne	.L2968

00000a58 e5d34001   1757 	ldrb	r4,[r3,1]

00000a5c e3140080   1758 	tst	r4,128


                                                                      Page 30
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
00000a60 0a00004f   1759 	beq	.L2968

                    1760 .L3025:

00000a64 e5f34001   1761 	ldrb	r4,[r3,1]!

00000a68 e3540000   1762 	cmp	r4,0

00000a6c 1a000003   1763 	bne	.L3027

00000a70 e5d34001   1764 	ldrb	r4,[r3,1]

00000a74 e3140080   1765 	tst	r4,128

00000a78 1a000049   1766 	bne	.L2968

00000a7c ea000004   1767 	b	.L3031

                    1768 .L3027:

00000a80 e35400ff   1769 	cmp	r4,255

00000a84 1a000046   1770 	bne	.L2968

00000a88 e5d34001   1771 	ldrb	r4,[r3,1]

00000a8c e3140080   1772 	tst	r4,128

00000a90 0a000043   1773 	beq	.L2968

                    1774 .L3031:

00000a94 e5f34001   1775 	ldrb	r4,[r3,1]!

00000a98 e3540000   1776 	cmp	r4,0

00000a9c 1a000003   1777 	bne	.L3033

00000aa0 e5d34001   1778 	ldrb	r4,[r3,1]

00000aa4 e3140080   1779 	tst	r4,128

00000aa8 1a00003d   1780 	bne	.L2968

00000aac ea000004   1781 	b	.L3037

                    1782 .L3033:

00000ab0 e35400ff   1783 	cmp	r4,255

00000ab4 1a00003a   1784 	bne	.L2968

00000ab8 e5d34001   1785 	ldrb	r4,[r3,1]

00000abc e3140080   1786 	tst	r4,128

00000ac0 0a000037   1787 	beq	.L2968

                    1788 .L3037:

00000ac4 e5f34001   1789 	ldrb	r4,[r3,1]!

00000ac8 e3540000   1790 	cmp	r4,0

00000acc 1a000003   1791 	bne	.L3039

00000ad0 e5d34001   1792 	ldrb	r4,[r3,1]

00000ad4 e3140080   1793 	tst	r4,128

00000ad8 1a000031   1794 	bne	.L2968

00000adc ea000004   1795 	b	.L3043

                    1796 .L3039:

00000ae0 e35400ff   1797 	cmp	r4,255

00000ae4 1a00002e   1798 	bne	.L2968

00000ae8 e5d34001   1799 	ldrb	r4,[r3,1]

00000aec e3140080   1800 	tst	r4,128

00000af0 0a00002b   1801 	beq	.L2968

                    1802 .L3043:

00000af4 e5f34001   1803 	ldrb	r4,[r3,1]!

00000af8 e3540000   1804 	cmp	r4,0

00000afc 1a000003   1805 	bne	.L3045

00000b00 e5d34001   1806 	ldrb	r4,[r3,1]

00000b04 e3140080   1807 	tst	r4,128

00000b08 1a000025   1808 	bne	.L2968

00000b0c ea000004   1809 	b	.L3049

                    1810 .L3045:

00000b10 e35400ff   1811 	cmp	r4,255

00000b14 1a000022   1812 	bne	.L2968

00000b18 e5d34001   1813 	ldrb	r4,[r3,1]

00000b1c e3140080   1814 	tst	r4,128

00000b20 0a00001f   1815 	beq	.L2968

                    1816 .L3049:

00000b24 e5f34001   1817 	ldrb	r4,[r3,1]!

00000b28 e3540000   1818 	cmp	r4,0

00000b2c 1a000003   1819 	bne	.L3051


                                                                      Page 31
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
00000b30 e5d34001   1820 	ldrb	r4,[r3,1]

00000b34 e3140080   1821 	tst	r4,128

00000b38 1a000019   1822 	bne	.L2968

00000b3c ea000004   1823 	b	.L3054

                    1824 .L3051:

00000b40 e35400ff   1825 	cmp	r4,255

00000b44 1a000016   1826 	bne	.L2968

00000b48 e5d34001   1827 	ldrb	r4,[r3,1]

00000b4c e3140080   1828 	tst	r4,128

00000b50 0a000013   1829 	beq	.L2968

                    1830 .L3054:

00000b54 e2833001   1831 	add	r3,r3,1

00000b58 e2500001   1832 	subs	r0,r0,1

00000b5c 1affff9c   1833 	bne	.L3007

                    1834 .L3006:

00000b60 e21c0007   1835 	ands	r0,r12,7

00000b64 0a00000e   1836 	beq	.L2968

                    1837 .L3057:

00000b68 e5d34000   1838 	ldrb	r4,[r3]

00000b6c e3540000   1839 	cmp	r4,0

00000b70 1a000003   1840 	bne	.L3059

00000b74 e5d3c001   1841 	ldrb	r12,[r3,1]

00000b78 e31c0080   1842 	tst	r12,128

00000b7c 1a000008   1843 	bne	.L2968

00000b80 ea000004   1844 	b	.L3062

                    1845 .L3059:

00000b84 e35400ff   1846 	cmp	r4,255

00000b88 1a000005   1847 	bne	.L2968

00000b8c e5d3c001   1848 	ldrb	r12,[r3,1]

00000b90 e31c0080   1849 	tst	r12,128

00000b94 0a000002   1850 	beq	.L2968

                    1851 .L3062:

00000b98 e2833001   1852 	add	r3,r3,1

00000b9c e2500001   1853 	subs	r0,r0,1

00000ba0 1afffff0   1854 	bne	.L3057

                    1855 .L2968:

                    1856 ;510: 


                    1857 ;511:     }


                    1858 ;512: 


                    1859 ;513:     iBytesToDelete = pBytePosition - pInteger;


                    1860 

00000ba4 e053c002   1861 	subs	r12,r3,r2

                    1862 ;514:     iNewSize = iOriginalSize;


                    1863 

00000ba8 e1a00001   1864 	mov	r0,r1

                    1865 ;515: 


                    1866 ;516: 


                    1867 ;517:     if( iBytesToDelete )


                    1868 

00000bac 0a00001d   1869 	beq	.L2950

                    1870 ;518:     {


                    1871 

                    1872 ;519:         unsigned char* pNewEnd;


                    1873 ;520:         unsigned char* pNewBytePosition;


                    1874 ;521:         iNewSize -= iBytesToDelete;


                    1875 

00000bb0 e1a01002   1876 	mov	r1,r2

00000bb4 e040000c   1877 	sub	r0,r0,r12

                    1878 ;522:         pNewEnd = pInteger + iNewSize;


                    1879 

00000bb8 e080c002   1880 	add	r12,r0,r2


                                                                      Page 32
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
                    1881 ;523: 


                    1882 ;524:         for( pNewBytePosition = pInteger; pNewBytePosition < pNewEnd; pNewBytePosition++ )


                    1883 

00000bbc e05cc002   1884 	subs	r12,r12,r2

00000bc0 43a0c000   1885 	movmi	r12,0

00000bc4 e1b021ac   1886 	movs	r2,r12 lsr 3

00000bc8 0a000011   1887 	beq	.L3065

                    1888 .L3081:

00000bcc e4d34001   1889 	ldrb	r4,[r3],1

00000bd0 e4c14001   1890 	strb	r4,[r1],1

00000bd4 e4d34001   1891 	ldrb	r4,[r3],1

00000bd8 e4c14001   1892 	strb	r4,[r1],1

00000bdc e4d34001   1893 	ldrb	r4,[r3],1

00000be0 e4c14001   1894 	strb	r4,[r1],1

00000be4 e4d34001   1895 	ldrb	r4,[r3],1

00000be8 e4c14001   1896 	strb	r4,[r1],1

00000bec e4d34001   1897 	ldrb	r4,[r3],1

00000bf0 e4c14001   1898 	strb	r4,[r1],1

00000bf4 e4d34001   1899 	ldrb	r4,[r3],1

00000bf8 e4c14001   1900 	strb	r4,[r1],1

00000bfc e4d34001   1901 	ldrb	r4,[r3],1

00000c00 e4c14001   1902 	strb	r4,[r1],1

00000c04 e4d34001   1903 	ldrb	r4,[r3],1

00000c08 e2522001   1904 	subs	r2,r2,1

00000c0c e4c14001   1905 	strb	r4,[r1],1

00000c10 1affffed   1906 	bne	.L3081

                    1907 .L3065:

00000c14 e21c2007   1908 	ands	r2,r12,7

                    1909 .L3085:

00000c18 14d3c001   1910 	ldrneb	r12,[r3],1

00000c1c 14c1c001   1911 	strneb	r12,[r1],1

00000c20 12522001   1912 	subnes	r2,r2,1

00000c24 1afffffb   1913 	bne	.L3085

                    1914 .L2970:

                    1915 ;529: 


                    1916 ;530:         }//for( pNewBytePosition = pInteger; pNewBytePosition < pNewEnd; pNewBytePosition++ )


                    1917 ;531: 


                    1918 ;532: 


                    1919 ;533:     }//if( iBytesToDelete )


                    1920 ;534: 


                    1921 ;535:     return iNewSize;


                    1922 

                    1923 .L2950:

00000c28 e8bd0010   1924 	ldmfd	[sp]!,{r4}

00000c2c e12fff1e*  1925 	ret	

                    1926 	.endf	BerEncoder_CompressInteger

                    1927 	.align	4

                    1928 ;pIntegerEnd	r0	local

                    1929 ;pBytePosition	r3	local

                    1930 ;iBytesToDelete	r12	local

                    1931 ;iNewSize	r0	local

                    1932 ;pNewEnd	r12	local

                    1933 ;pNewBytePosition	r1	local

                    1934 

                    1935 ;pInteger	r2	param

                    1936 ;iOriginalSize	r1	param

                    1937 

                    1938 	.section ".bss","awb"

                    1939 .L3692:

                    1940 	.data

                    1941 	.text


                                                                      Page 33
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
                    1942 

                    1943 ;536: 


                    1944 ;537: }


                    1945 

                    1946 ;538: 


                    1947 ;539: int BerEncoder_encodeUInt32( unsigned int iValue, unsigned char* pBuffer, int iBufPos )


                    1948 	.align	4

                    1949 	.align	4

                    1950 BerEncoder_encodeUInt32::

00000c30 e92d4070   1951 	stmfd	[sp]!,{r4-r6,lr}

00000c34 e1a04002   1952 	mov	r4,r2

00000c38 e3a06000   1953 	mov	r6,0

00000c3c e24dd00c   1954 	sub	sp,sp,12

00000c40 e58d0000   1955 	str	r0,[sp]

00000c44 e1a05001   1956 	mov	r5,r1

00000c48 e5dd1001   1957 	ldrb	r1,[sp,1]

00000c4c e5cd6004   1958 	strb	r6,[sp,4]

00000c50 e5cd1006   1959 	strb	r1,[sp,6]

00000c54 e5dd1002   1960 	ldrb	r1,[sp,2]

00000c58 e5cd0005   1961 	strb	r0,[sp,5]

00000c5c e5cd1007   1962 	strb	r1,[sp,7]

00000c60 e5dd1003   1963 	ldrb	r1,[sp,3]

00000c64 e28d0005   1964 	add	r0,sp,5

00000c68 e5cd1008   1965 	strb	r1,[sp,8]

                    1966 ;540: {


                    1967 

                    1968 ;541: 


                    1969 ;542:     unsigned char* pValueArray = (unsigned char*)&iValue;


                    1970 

                    1971 ;543:     unsigned char ValueBuffer[5];


                    1972 ;544: 


                    1973 ;545:     int i;


                    1974 ;546:     int iSize;


                    1975 ;547: 


                    1976 ;548:     ValueBuffer[0] = 0;


                    1977 

                    1978 ;549: 


                    1979 ;550: 


                    1980 ;551:     for( i = 0; i < 4; i++ )


                    1981 

                    1982 ;555: 


                    1983 ;556:     }//for( i = 0; i < 4; i++ )


                    1984 ;557: 


                    1985 ;558:     //если ORDER_LITTLE_ENDIAN


                    1986 ;559:     BerEncoder_RevertByteOrder( ValueBuffer + 1, 4 );


                    1987 

00000c6c e3a01004   1988 	mov	r1,4

00000c70 eb00024d*  1989 	bl	BerEncoder_RevertByteOrder

                    1990 ;560:     //если ORDER_LITTLE_ENDIAN


                    1991 ;561: 


                    1992 ;562:     iSize = BerEncoder_CompressInteger( ValueBuffer, 5 );


                    1993 

00000c74 e28d0004   1994 	add	r0,sp,4

00000c78 e3a01005   1995 	mov	r1,5

00000c7c ebffff4b*  1996 	bl	BerEncoder_CompressInteger

                    1997 ;563: 


                    1998 ;564:     for( i = 0; i < iSize; i++ )


                    1999 

00000c80 e3500000   2000 	cmp	r0,0

00000c84 a1a03000   2001 	movge	r3,r0

00000c88 b3a03000   2002 	movlt	r3,0


                                                                      Page 34
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
00000c8c e1b021a3   2003 	movs	r2,r3 lsr 3

00000c90 0a00001b   2004 	beq	.L3874

00000c94 e28d1004   2005 	add	r1,sp,4

00000c98 e1a06182   2006 	mov	r6,r2 lsl 3

                    2007 .L3890:

00000c9c e4d1c001   2008 	ldrb	r12,[r1],1

00000ca0 e2522001   2009 	subs	r2,r2,1

00000ca4 e7c5c004   2010 	strb	r12,[r5,r4]

00000ca8 e4d1c001   2011 	ldrb	r12,[r1],1

00000cac e2844001   2012 	add	r4,r4,1

00000cb0 e7c5c004   2013 	strb	r12,[r5,r4]

00000cb4 e4d1c001   2014 	ldrb	r12,[r1],1

00000cb8 e2844001   2015 	add	r4,r4,1

00000cbc e7c5c004   2016 	strb	r12,[r5,r4]

00000cc0 e4d1c001   2017 	ldrb	r12,[r1],1

00000cc4 e2844001   2018 	add	r4,r4,1

00000cc8 e7c5c004   2019 	strb	r12,[r5,r4]

00000ccc e4d1c001   2020 	ldrb	r12,[r1],1

00000cd0 e2844001   2021 	add	r4,r4,1

00000cd4 e7c5c004   2022 	strb	r12,[r5,r4]

00000cd8 e4d1c001   2023 	ldrb	r12,[r1],1

00000cdc e2844001   2024 	add	r4,r4,1

00000ce0 e7c5c004   2025 	strb	r12,[r5,r4]

00000ce4 e4d1c001   2026 	ldrb	r12,[r1],1

00000ce8 e2844001   2027 	add	r4,r4,1

00000cec e7c5c004   2028 	strb	r12,[r5,r4]

00000cf0 e4d1c001   2029 	ldrb	r12,[r1],1

00000cf4 e2844001   2030 	add	r4,r4,1

00000cf8 e7c5c004   2031 	strb	r12,[r5,r4]

00000cfc e2844001   2032 	add	r4,r4,1

00000d00 1affffe5   2033 	bne	.L3890

                    2034 .L3874:

00000d04 e2132007   2035 	ands	r2,r3,7

00000d08 108d1006   2036 	addne	r1,sp,r6

00000d0c 12811004   2037 	addne	r1,r1,4

                    2038 .L3894:

00000d10 14d13001   2039 	ldrneb	r3,[r1],1

00000d14 17c53004   2040 	strneb	r3,[r5,r4]

00000d18 12844001   2041 	addne	r4,r4,1

00000d1c 12522001   2042 	subnes	r2,r2,1

00000d20 1afffffa   2043 	bne	.L3894

                    2044 .L3849:

                    2045 ;568: 


                    2046 ;569:     }


                    2047 ;570:     return iBufPos;


                    2048 

00000d24 e1a00004   2049 	mov	r0,r4

00000d28 e28dd00c   2050 	add	sp,sp,12

00000d2c e8bd8070   2051 	ldmfd	[sp]!,{r4-r6,pc}

                    2052 	.endf	BerEncoder_encodeUInt32

                    2053 	.align	4

                    2054 ;ValueBuffer	[sp,4]	local

                    2055 ;i	r6	local

                    2056 

                    2057 ;iValue	[sp]	param

                    2058 ;pBuffer	r5	param

                    2059 ;iBufPos	r4	param

                    2060 

                    2061 	.section ".bss","awb"

                    2062 .L4087:

                    2063 	.data


                                                                      Page 35
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
                    2064 	.text

                    2065 

                    2066 ;571: }


                    2067 

                    2068 ;572: 


                    2069 ;573: int BerEncoder_EncodeInt32(int iValue, unsigned char* pBuffer, int iBufPos)


                    2070 	.align	4

                    2071 	.align	4

                    2072 BerEncoder_EncodeInt32::

00000d30 e92d4030   2073 	stmfd	[sp]!,{r4-r5,lr}

00000d34 e24dd008   2074 	sub	sp,sp,8

00000d38 e58d0004   2075 	str	r0,[sp,4]

00000d3c e1a05001   2076 	mov	r5,r1

00000d40 e5dd1005   2077 	ldrb	r1,[sp,5]

00000d44 e1a04002   2078 	mov	r4,r2

00000d48 e5cd1001   2079 	strb	r1,[sp,1]

00000d4c e5dd1006   2080 	ldrb	r1,[sp,6]

00000d50 e5cd0000   2081 	strb	r0,[sp]

00000d54 e5cd1002   2082 	strb	r1,[sp,2]

00000d58 e5dd1007   2083 	ldrb	r1,[sp,7]

00000d5c e1a0000d   2084 	mov	r0,sp

00000d60 e5cd1003   2085 	strb	r1,[sp,3]

                    2086 ;574: {


                    2087 

                    2088 ;575:     unsigned char* pValueArray = (unsigned char*)&iValue;


                    2089 

                    2090 ;576:     unsigned char ValueBuffer[4];


                    2091 ;577: 


                    2092 ;578:     int i;


                    2093 ;579:     int iSize;


                    2094 ;580: 


                    2095 ;581:     for (i = 0; i < 4; i++)


                    2096 

                    2097 ;584:     }


                    2098 ;585:      //если ORDER_LITTLE_ENDIAN


                    2099 ;586:     BerEncoder_RevertByteOrder(ValueBuffer, 4);


                    2100 

00000d64 e3a01004   2101 	mov	r1,4

00000d68 eb00020f*  2102 	bl	BerEncoder_RevertByteOrder

                    2103 ;587:     //если ORDER_LITTLE_ENDIAN


                    2104 ;588:     iSize = BerEncoder_CompressInteger(ValueBuffer, 4);


                    2105 

00000d6c e1a0000d   2106 	mov	r0,sp

00000d70 e3a01004   2107 	mov	r1,4

00000d74 ebffff0d*  2108 	bl	BerEncoder_CompressInteger

                    2109 ;589:     for (i = 0; i < iSize; i++)


                    2110 

00000d78 e3a03000   2111 	mov	r3,0

00000d7c e3500000   2112 	cmp	r0,0

00000d80 a1a0c000   2113 	movge	r12,r0

00000d84 b3a0c000   2114 	movlt	r12,0

00000d88 e1b021ac   2115 	movs	r2,r12 lsr 3

00000d8c 0a00001b   2116 	beq	.L4146

00000d90 e1a0100d   2117 	mov	r1,sp

00000d94 e1a03182   2118 	mov	r3,r2 lsl 3

                    2119 .L4162:

00000d98 e4d10001   2120 	ldrb	r0,[r1],1

00000d9c e2522001   2121 	subs	r2,r2,1

00000da0 e7c50004   2122 	strb	r0,[r5,r4]

00000da4 e4d10001   2123 	ldrb	r0,[r1],1

00000da8 e2844001   2124 	add	r4,r4,1


                                                                      Page 36
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
00000dac e7c50004   2125 	strb	r0,[r5,r4]

00000db0 e4d10001   2126 	ldrb	r0,[r1],1

00000db4 e2844001   2127 	add	r4,r4,1

00000db8 e7c50004   2128 	strb	r0,[r5,r4]

00000dbc e4d10001   2129 	ldrb	r0,[r1],1

00000dc0 e2844001   2130 	add	r4,r4,1

00000dc4 e7c50004   2131 	strb	r0,[r5,r4]

00000dc8 e4d10001   2132 	ldrb	r0,[r1],1

00000dcc e2844001   2133 	add	r4,r4,1

00000dd0 e7c50004   2134 	strb	r0,[r5,r4]

00000dd4 e4d10001   2135 	ldrb	r0,[r1],1

00000dd8 e2844001   2136 	add	r4,r4,1

00000ddc e7c50004   2137 	strb	r0,[r5,r4]

00000de0 e4d10001   2138 	ldrb	r0,[r1],1

00000de4 e2844001   2139 	add	r4,r4,1

00000de8 e7c50004   2140 	strb	r0,[r5,r4]

00000dec e4d10001   2141 	ldrb	r0,[r1],1

00000df0 e2844001   2142 	add	r4,r4,1

00000df4 e7c50004   2143 	strb	r0,[r5,r4]

00000df8 e2844001   2144 	add	r4,r4,1

00000dfc 1affffe5   2145 	bne	.L4162

                    2146 .L4146:

00000e00 e21c2007   2147 	ands	r2,r12,7

00000e04 1083100d   2148 	addne	r1,r3,sp

                    2149 .L4166:

00000e08 14d13001   2150 	ldrneb	r3,[r1],1

00000e0c 17c53004   2151 	strneb	r3,[r5,r4]

00000e10 12844001   2152 	addne	r4,r4,1

00000e14 12522001   2153 	subnes	r2,r2,1

00000e18 1afffffa   2154 	bne	.L4166

                    2155 .L4117:

                    2156 ;592:     }


                    2157 ;593:     return iBufPos;


                    2158 

00000e1c e1a00004   2159 	mov	r0,r4

00000e20 e28dd008   2160 	add	sp,sp,8

00000e24 e8bd8030   2161 	ldmfd	[sp]!,{r4-r5,pc}

                    2162 	.endf	BerEncoder_EncodeInt32

                    2163 	.align	4

                    2164 ;ValueBuffer	[sp]	local

                    2165 ;i	r3	local

                    2166 

                    2167 ;iValue	[sp,4]	param

                    2168 ;pBuffer	r5	param

                    2169 ;iBufPos	r4	param

                    2170 

                    2171 	.section ".bss","awb"

                    2172 .L4357:

                    2173 	.data

                    2174 	.text

                    2175 

                    2176 ;594: }


                    2177 

                    2178 ;595: 


                    2179 ;596: int BerEncoder_EncodeFloatWithTL(unsigned char ucTag, float Value,


                    2180 	.align	4

                    2181 	.align	4

                    2182 BerEncoder_EncodeFloatWithTL::

00000e28 e92d4cf2   2183 	stmfd	[sp]!,{r1,r4-r7,r10-fp,lr}

                    2184 ;597:     unsigned char formatWidth, unsigned char exponentWidth,


                    2185 ;598:     unsigned char* pBuffer, int iBufPos)



                                                                      Page 37
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
                    2186 ;599: {


                    2187 

                    2188 ;600:     unsigned char* pValueArray = (unsigned char*)&Value;


                    2189 

                    2190 ;601:     unsigned char valueBuffer[9];


                    2191 ;602:     int i;


                    2192 ;603: 


                    2193 ;604:     int byteSize = formatWidth / 8;


                    2194 

00000e2c e24dd00c   2195 	sub	sp,sp,12

00000e30 e58d100c   2196 	str	r1,[sp,12]

00000e34 e59d502c   2197 	ldr	r5,[sp,44]

00000e38 e59d4030   2198 	ldr	r4,[sp,48]

00000e3c e5cd3000   2199 	strb	r3,[sp]

                    2200 ;607: 


                    2201 ;608:     for (i = 0; i < byteSize; i++) {


                    2202 

00000e40 e1a06000   2203 	mov	r6,r0

00000e44 e28d0001   2204 	add	r0,sp,1

00000e48 e1b071a2   2205 	movs	r7,r2 lsr 3

                    2206 ;605: 


                    2207 ;606:     valueBuffer[0] = exponentWidth;


                    2208 

00000e4c 51a0b007   2209 	movpl	fp,r7

00000e50 43a0b000   2210 	movmi	fp,0

00000e54 e1b0a1ab   2211 	movs	r10,fp lsr 3

00000e58 11a03000   2212 	movne	r3,r0

00000e5c 128dc00c   2213 	addne	r12,sp,12

00000e60 11a0218a   2214 	movne	r2,r10 lsl 3

00000e64 1a000003   2215 	bne	.L4436

00000e68 e3a02000   2216 	mov	r2,0

00000e6c e21ba007   2217 	ands	r10,fp,7

00000e70 1a000014   2218 	bne	.L4441

00000e74 ea00001a   2219 	b	.L4384

                    2220 .L4436:

00000e78 e4dc1001   2221 	ldrb	r1,[r12],1

00000e7c e4c31001   2222 	strb	r1,[r3],1

00000e80 e4dc1001   2223 	ldrb	r1,[r12],1

00000e84 e4c31001   2224 	strb	r1,[r3],1

00000e88 e4dc1001   2225 	ldrb	r1,[r12],1

00000e8c e4c31001   2226 	strb	r1,[r3],1

00000e90 e4dc1001   2227 	ldrb	r1,[r12],1

00000e94 e4c31001   2228 	strb	r1,[r3],1

00000e98 e4dc1001   2229 	ldrb	r1,[r12],1

00000e9c e4c31001   2230 	strb	r1,[r3],1

00000ea0 e4dc1001   2231 	ldrb	r1,[r12],1

00000ea4 e4c31001   2232 	strb	r1,[r3],1

00000ea8 e4dc1001   2233 	ldrb	r1,[r12],1

00000eac e4c31001   2234 	strb	r1,[r3],1

00000eb0 e4dc1001   2235 	ldrb	r1,[r12],1

00000eb4 e25aa001   2236 	subs	r10,r10,1

00000eb8 e4c31001   2237 	strb	r1,[r3],1

00000ebc 1affffed   2238 	bne	.L4436

00000ec0 e21ba007   2239 	ands	r10,fp,7

00000ec4 0a000006   2240 	beq	.L4384

                    2241 .L4441:

00000ec8 e0823000   2242 	add	r3,r2,r0

00000ecc e08dc002   2243 	add	r12,sp,r2

00000ed0 e28c200c   2244 	add	r2,r12,12

                    2245 .L4440:

00000ed4 e4d2c001   2246 	ldrb	r12,[r2],1


                                                                      Page 38
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
00000ed8 e25aa001   2247 	subs	r10,r10,1

00000edc e4c3c001   2248 	strb	r12,[r3],1

00000ee0 1afffffb   2249 	bne	.L4440

                    2250 .L4384:

                    2251 ;610:     }


                    2252 ;611: 


                    2253 ;612:     //если ORDER_LITTLE_ENDIAN


                    2254 ;613:     BerEncoder_RevertByteOrder(valueBuffer + 1, byteSize);


                    2255 

00000ee4 e1a01007   2256 	mov	r1,r7

00000ee8 eb0001af*  2257 	bl	BerEncoder_RevertByteOrder

                    2258 ;614:     //если ORDER_LITTLE_ENDIAN


                    2259 ;615: 


                    2260 ;616:     pBuffer[iBufPos++] = ucTag;


                    2261 

00000eec e7c56004   2262 	strb	r6,[r5,r4]

00000ef0 e2844001   2263 	add	r4,r4,1

                    2264 ;617:     pBuffer[iBufPos++] = (unsigned char)byteSize+1;


                    2265 

00000ef4 e2970001   2266 	adds	r0,r7,1

00000ef8 e7c50004   2267 	strb	r0,[r5,r4]

00000efc e2844001   2268 	add	r4,r4,1

                    2269 ;618: 


                    2270 ;619:     for (i = 0; i < byteSize + 1; i++) {


                    2271 

00000f00 e3a02000   2272 	mov	r2,0

00000f04 51a0c000   2273 	movpl	r12,r0

00000f08 43a0c000   2274 	movmi	r12,0

00000f0c e1b031ac   2275 	movs	r3,r12 lsr 3

00000f10 0a00001b   2276 	beq	.L4443

00000f14 e1a0000d   2277 	mov	r0,sp

00000f18 e1a02183   2278 	mov	r2,r3 lsl 3

                    2279 .L4459:

00000f1c e4d01001   2280 	ldrb	r1,[r0],1

00000f20 e2533001   2281 	subs	r3,r3,1

00000f24 e7c51004   2282 	strb	r1,[r5,r4]

00000f28 e4d01001   2283 	ldrb	r1,[r0],1

00000f2c e2844001   2284 	add	r4,r4,1

00000f30 e7c51004   2285 	strb	r1,[r5,r4]

00000f34 e4d01001   2286 	ldrb	r1,[r0],1

00000f38 e2844001   2287 	add	r4,r4,1

00000f3c e7c51004   2288 	strb	r1,[r5,r4]

00000f40 e4d01001   2289 	ldrb	r1,[r0],1

00000f44 e2844001   2290 	add	r4,r4,1

00000f48 e7c51004   2291 	strb	r1,[r5,r4]

00000f4c e4d01001   2292 	ldrb	r1,[r0],1

00000f50 e2844001   2293 	add	r4,r4,1

00000f54 e7c51004   2294 	strb	r1,[r5,r4]

00000f58 e4d01001   2295 	ldrb	r1,[r0],1

00000f5c e2844001   2296 	add	r4,r4,1

00000f60 e7c51004   2297 	strb	r1,[r5,r4]

00000f64 e4d01001   2298 	ldrb	r1,[r0],1

00000f68 e2844001   2299 	add	r4,r4,1

00000f6c e7c51004   2300 	strb	r1,[r5,r4]

00000f70 e4d01001   2301 	ldrb	r1,[r0],1

00000f74 e2844001   2302 	add	r4,r4,1

00000f78 e7c51004   2303 	strb	r1,[r5,r4]

00000f7c e2844001   2304 	add	r4,r4,1

00000f80 1affffe5   2305 	bne	.L4459

                    2306 .L4443:

00000f84 e21c3007   2307 	ands	r3,r12,7


                                                                      Page 39
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
00000f88 1082000d   2308 	addne	r0,r2,sp

                    2309 .L4463:

00000f8c 14d02001   2310 	ldrneb	r2,[r0],1

00000f90 17c52004   2311 	strneb	r2,[r5,r4]

00000f94 12844001   2312 	addne	r4,r4,1

00000f98 12533001   2313 	subnes	r3,r3,1

00000f9c 1afffffa   2314 	bne	.L4463

                    2315 .L4388:

                    2316 ;621:     }


                    2317 ;622: 


                    2318 ;623:     return iBufPos;


                    2319 

00000fa0 e1a00004   2320 	mov	r0,r4

00000fa4 e28dd00c   2321 	add	sp,sp,12

00000fa8 e8bd8cf2   2322 	ldmfd	[sp]!,{r1,r4-r7,r10-fp,pc}

                    2323 	.endf	BerEncoder_EncodeFloatWithTL

                    2324 	.align	4

                    2325 ;valueBuffer	[sp]	local

                    2326 ;i	r2	local

                    2327 ;byteSize	r7	local

                    2328 

                    2329 ;ucTag	r6	param

                    2330 ;Value	[sp,12]	param

                    2331 ;formatWidth	r2	param

                    2332 ;exponentWidth	r3	param

                    2333 ;pBuffer	r5	param

                    2334 ;iBufPos	r4	param

                    2335 

                    2336 	.section ".bss","awb"

                    2337 .L4763:

                    2338 	.data

                    2339 	.text

                    2340 

                    2341 ;624: }


                    2342 

                    2343 ;625: 


                    2344 ;626: int BerEncoder_encodeUInt32WithTL(unsigned char ucTag, unsigned int iValue,


                    2345 	.align	4

                    2346 	.align	4

                    2347 BerEncoder_encodeUInt32WithTL::

00000fac e92d40f2   2348 	stmfd	[sp]!,{r1,r4-r7,lr}

00000fb0 e1a05002   2349 	mov	r5,r2

00000fb4 e1a07000   2350 	mov	r7,r0

00000fb8 e24dd008   2351 	sub	sp,sp,8

00000fbc e58d1008   2352 	str	r1,[sp,8]

00000fc0 e5dd0009   2353 	ldrb	r0,[sp,9]

00000fc4 e1a04003   2354 	mov	r4,r3

00000fc8 e5cd0002   2355 	strb	r0,[sp,2]

00000fcc e5dd000a   2356 	ldrb	r0,[sp,10]

00000fd0 e3a06000   2357 	mov	r6,0

00000fd4 e5cd0003   2358 	strb	r0,[sp,3]

00000fd8 e5dd000b   2359 	ldrb	r0,[sp,11]

00000fdc e5cd6000   2360 	strb	r6,[sp]

00000fe0 e5cd0004   2361 	strb	r0,[sp,4]

                    2362 ;627:         unsigned char* pBuffer, int iBufPos)


                    2363 ;628: {


                    2364 

                    2365 ;629: 


                    2366 ;630:     unsigned char* pValueArray = (unsigned char*)&iValue;


                    2367 

                    2368 ;631:     unsigned char ValueBuffer[5];



                                                                      Page 40
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
                    2369 ;632: 


                    2370 ;633:     int i;


                    2371 ;634:     int iSize;


                    2372 ;635: 


                    2373 ;636:     ValueBuffer[0] = 0;


                    2374 

                    2375 ;637: 


                    2376 ;638: 


                    2377 ;639:     for( i = 0; i < 4; i++ )


                    2378 

                    2379 ;643: 


                    2380 ;644:     }//for( i = 0; i < 4; i++ )


                    2381 ;645: 


                    2382 ;646:     //если ORDER_LITTLE_ENDIAN


                    2383 ;647:     BerEncoder_RevertByteOrder( ValueBuffer + 1, 4 );


                    2384 

00000fe4 e28d0001   2385 	add	r0,sp,1

00000fe8 e5cd1001   2386 	strb	r1,[sp,1]

00000fec e3a01004   2387 	mov	r1,4

00000ff0 eb00016d*  2388 	bl	BerEncoder_RevertByteOrder

                    2389 ;648:     //если ORDER_LITTLE_ENDIAN


                    2390 ;649: 


                    2391 ;650:     iSize = BerEncoder_CompressInteger( ValueBuffer, 5 );


                    2392 

00000ff4 e1a0000d   2393 	mov	r0,sp

00000ff8 e3a01005   2394 	mov	r1,5

00000ffc ebfffe6b*  2395 	bl	BerEncoder_CompressInteger

                    2396 ;651: 


                    2397 ;652:     pBuffer[iBufPos++] = ucTag;


                    2398 

00001000 e7c57004   2399 	strb	r7,[r5,r4]

00001004 e2844001   2400 	add	r4,r4,1

                    2401 ;653:     pBuffer[iBufPos++] = (unsigned char)iSize;


                    2402 

00001008 e7c50004   2403 	strb	r0,[r5,r4]

0000100c e2844001   2404 	add	r4,r4,1

                    2405 ;654: 


                    2406 ;655:     for( i = 0; i < iSize; i++ )


                    2407 

00001010 e3500000   2408 	cmp	r0,0

00001014 a1a0c000   2409 	movge	r12,r0

00001018 b3a0c000   2410 	movlt	r12,0

0000101c e1b031ac   2411 	movs	r3,r12 lsr 3

00001020 0a00001b   2412 	beq	.L4850

00001024 e1a0200d   2413 	mov	r2,sp

00001028 e1a06183   2414 	mov	r6,r3 lsl 3

                    2415 .L4866:

0000102c e4d20001   2416 	ldrb	r0,[r2],1

00001030 e2533001   2417 	subs	r3,r3,1

00001034 e7c50004   2418 	strb	r0,[r5,r4]

00001038 e4d20001   2419 	ldrb	r0,[r2],1

0000103c e2844001   2420 	add	r4,r4,1

00001040 e7c50004   2421 	strb	r0,[r5,r4]

00001044 e4d20001   2422 	ldrb	r0,[r2],1

00001048 e2844001   2423 	add	r4,r4,1

0000104c e7c50004   2424 	strb	r0,[r5,r4]

00001050 e4d20001   2425 	ldrb	r0,[r2],1

00001054 e2844001   2426 	add	r4,r4,1

00001058 e7c50004   2427 	strb	r0,[r5,r4]

0000105c e4d20001   2428 	ldrb	r0,[r2],1

00001060 e2844001   2429 	add	r4,r4,1


                                                                      Page 41
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
00001064 e7c50004   2430 	strb	r0,[r5,r4]

00001068 e4d20001   2431 	ldrb	r0,[r2],1

0000106c e2844001   2432 	add	r4,r4,1

00001070 e7c50004   2433 	strb	r0,[r5,r4]

00001074 e4d20001   2434 	ldrb	r0,[r2],1

00001078 e2844001   2435 	add	r4,r4,1

0000107c e7c50004   2436 	strb	r0,[r5,r4]

00001080 e4d20001   2437 	ldrb	r0,[r2],1

00001084 e2844001   2438 	add	r4,r4,1

00001088 e7c50004   2439 	strb	r0,[r5,r4]

0000108c e2844001   2440 	add	r4,r4,1

00001090 1affffe5   2441 	bne	.L4866

                    2442 .L4850:

00001094 e21c3007   2443 	ands	r3,r12,7

00001098 1086000d   2444 	addne	r0,r6,sp

                    2445 .L4870:

0000109c 14d02001   2446 	ldrneb	r2,[r0],1

000010a0 17c52004   2447 	strneb	r2,[r5,r4]

000010a4 12844001   2448 	addne	r4,r4,1

000010a8 12533001   2449 	subnes	r3,r3,1

000010ac 1afffffa   2450 	bne	.L4870

                    2451 .L4813:

                    2452 ;659: 


                    2453 ;660:     }//    for( i = 0; i < iSize; i++ )


                    2454 ;661: 


                    2455 ;662:     return iBufPos;


                    2456 

000010b0 e1a00004   2457 	mov	r0,r4

000010b4 e28dd008   2458 	add	sp,sp,8

000010b8 e8bd80f2   2459 	ldmfd	[sp]!,{r1,r4-r7,pc}

                    2460 	.endf	BerEncoder_encodeUInt32WithTL

                    2461 	.align	4

                    2462 ;ValueBuffer	[sp]	local

                    2463 ;i	r6	local

                    2464 

                    2465 ;ucTag	r7	param

                    2466 ;iValue	[sp,8]	param

                    2467 ;pBuffer	r5	param

                    2468 ;iBufPos	r4	param

                    2469 

                    2470 	.section ".bss","awb"

                    2471 .L5063:

                    2472 	.data

                    2473 	.text

                    2474 

                    2475 ;663: 


                    2476 ;664: }


                    2477 

                    2478 ;665: 


                    2479 ;666: int BerEncoder_EncodeInt32WithTL(unsigned char ucTag, int iValue,


                    2480 	.align	4

                    2481 	.align	4

                    2482 BerEncoder_EncodeInt32WithTL::

000010bc e92d4076   2483 	stmfd	[sp]!,{r1-r2,r4-r6,lr}

000010c0 e1a06000   2484 	mov	r6,r0

000010c4 e5dd0000   2485 	ldrb	r0,[sp]

000010c8 e5cd0004   2486 	strb	r0,[sp,4]

000010cc e5dd0001   2487 	ldrb	r0,[sp,1]

000010d0 e1a05002   2488 	mov	r5,r2

000010d4 e5cd0005   2489 	strb	r0,[sp,5]

000010d8 e5dd0002   2490 	ldrb	r0,[sp,2]


                                                                      Page 42
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
000010dc e1a04003   2491 	mov	r4,r3

000010e0 e5cd0006   2492 	strb	r0,[sp,6]

000010e4 e5dd0003   2493 	ldrb	r0,[sp,3]

000010e8 e3a01004   2494 	mov	r1,4

000010ec e5cd0007   2495 	strb	r0,[sp,7]

                    2496 ;667:         unsigned char* pBuffer, int iBufPos)


                    2497 ;668: {


                    2498 

                    2499 ;669:     unsigned char* pValueArray = (unsigned char*)&iValue;


                    2500 

                    2501 ;670:     unsigned char ValueBuffer[4];


                    2502 ;671: 


                    2503 ;672:     int i;


                    2504 ;673:     int iSize;


                    2505 ;674: 


                    2506 ;675:     for (i = 0; i < 4; i++)


                    2507 

                    2508 ;679: 


                    2509 ;680:     }//for( i = 0; i < 4; i++ )


                    2510 ;681: 


                    2511 ;682:      //если ORDER_LITTLE_ENDIAN


                    2512 ;683:     BerEncoder_RevertByteOrder(ValueBuffer, 4);


                    2513 

000010f0 e28d0004   2514 	add	r0,sp,4

000010f4 eb00012c*  2515 	bl	BerEncoder_RevertByteOrder

                    2516 ;684:     //если ORDER_LITTLE_ENDIAN


                    2517 ;685: 


                    2518 ;686:     iSize = BerEncoder_CompressInteger(ValueBuffer, 4);


                    2519 

000010f8 e28d0004   2520 	add	r0,sp,4

000010fc e3a01004   2521 	mov	r1,4

00001100 ebfffe2a*  2522 	bl	BerEncoder_CompressInteger

                    2523 ;687: 


                    2524 ;688:     pBuffer[iBufPos++] = ucTag;


                    2525 

00001104 e7c56004   2526 	strb	r6,[r5,r4]

00001108 e2844001   2527 	add	r4,r4,1

                    2528 ;689:     pBuffer[iBufPos++] = (unsigned char)iSize;


                    2529 

0000110c e7c50004   2530 	strb	r0,[r5,r4]

00001110 e2844001   2531 	add	r4,r4,1

                    2532 ;690: 


                    2533 ;691:     for (i = 0; i < iSize; i++)


                    2534 

00001114 e3a0c000   2535 	mov	r12,0

00001118 e3500000   2536 	cmp	r0,0

0000111c a1a06000   2537 	movge	r6,r0

00001120 b3a06000   2538 	movlt	r6,0

00001124 e1b031a6   2539 	movs	r3,r6 lsr 3

00001128 0a00001b   2540 	beq	.L5122

0000112c e28d2004   2541 	add	r2,sp,4

00001130 e1a0c183   2542 	mov	r12,r3 lsl 3

                    2543 .L5138:

00001134 e4d20001   2544 	ldrb	r0,[r2],1

00001138 e2533001   2545 	subs	r3,r3,1

0000113c e7c50004   2546 	strb	r0,[r5,r4]

00001140 e4d20001   2547 	ldrb	r0,[r2],1

00001144 e2844001   2548 	add	r4,r4,1

00001148 e7c50004   2549 	strb	r0,[r5,r4]

0000114c e4d20001   2550 	ldrb	r0,[r2],1

00001150 e2844001   2551 	add	r4,r4,1


                                                                      Page 43
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
00001154 e7c50004   2552 	strb	r0,[r5,r4]

00001158 e4d20001   2553 	ldrb	r0,[r2],1

0000115c e2844001   2554 	add	r4,r4,1

00001160 e7c50004   2555 	strb	r0,[r5,r4]

00001164 e4d20001   2556 	ldrb	r0,[r2],1

00001168 e2844001   2557 	add	r4,r4,1

0000116c e7c50004   2558 	strb	r0,[r5,r4]

00001170 e4d20001   2559 	ldrb	r0,[r2],1

00001174 e2844001   2560 	add	r4,r4,1

00001178 e7c50004   2561 	strb	r0,[r5,r4]

0000117c e4d20001   2562 	ldrb	r0,[r2],1

00001180 e2844001   2563 	add	r4,r4,1

00001184 e7c50004   2564 	strb	r0,[r5,r4]

00001188 e4d20001   2565 	ldrb	r0,[r2],1

0000118c e2844001   2566 	add	r4,r4,1

00001190 e7c50004   2567 	strb	r0,[r5,r4]

00001194 e2844001   2568 	add	r4,r4,1

00001198 1affffe5   2569 	bne	.L5138

                    2570 .L5122:

0000119c e2163007   2571 	ands	r3,r6,7

000011a0 108d000c   2572 	addne	r0,sp,r12

000011a4 12800004   2573 	addne	r0,r0,4

                    2574 .L5142:

000011a8 14d02001   2575 	ldrneb	r2,[r0],1

000011ac 17c52004   2576 	strneb	r2,[r5,r4]

000011b0 12844001   2577 	addne	r4,r4,1

000011b4 12533001   2578 	subnes	r3,r3,1

000011b8 1afffffa   2579 	bne	.L5142

                    2580 .L5093:

                    2581 ;695: 


                    2582 ;696:     }//    for( i = 0; i < iSize; i++ )


                    2583 ;697: 


                    2584 ;698:     return iBufPos;


                    2585 

000011bc e1a00004   2586 	mov	r0,r4

000011c0 e8bd8076   2587 	ldmfd	[sp]!,{r1-r2,r4-r6,pc}

                    2588 	.endf	BerEncoder_EncodeInt32WithTL

                    2589 	.align	4

                    2590 ;ValueBuffer	[sp,4]	local

                    2591 ;i	r12	local

                    2592 

                    2593 ;ucTag	r6	param

                    2594 ;iValue	[sp]	param

                    2595 ;pBuffer	r5	param

                    2596 ;iBufPos	r4	param

                    2597 

                    2598 	.section ".bss","awb"

                    2599 .L5333:

                    2600 	.data

                    2601 	.text

                    2602 

                    2603 ;699: 


                    2604 ;700: }


                    2605 

                    2606 ;701: 


                    2607 ;702: int BerEncoder_encodeOctetString(uint8_t tag, const uint8_t *octetString, uint32_t octetStringSize,


                    2608 	.align	4

                    2609 	.align	4

                    2610 BerEncoder_encodeOctetString::

000011c4 e92d40f0   2611 	stmfd	[sp]!,{r4-r7,lr}

000011c8 e1a07001   2612 	mov	r7,r1


                                                                      Page 44
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
000011cc e59d1014   2613 	ldr	r1,[sp,20]

                    2614 ;703:                                  uint8_t *buffer, int bufPos)


                    2615 ;704: {


                    2616 

                    2617 ;705:     buffer[bufPos++] = tag;


                    2618 

000011d0 e1a05002   2619 	mov	r5,r2

000011d4 e2812001   2620 	add	r2,r1,1

                    2621 ;706: 


                    2622 ;707:     bufPos = BerEncoder_encodeLength(octetStringSize, buffer, bufPos);


                    2623 

000011d8 e1a06003   2624 	mov	r6,r3

000011dc e7c60001   2625 	strb	r0,[r6,r1]

000011e0 e1a01006   2626 	mov	r1,r6

000011e4 e1a00005   2627 	mov	r0,r5

000011e8 ebfffb84*  2628 	bl	BerEncoder_encodeLength

000011ec e1a02005   2629 	mov	r2,r5

000011f0 e1a01007   2630 	mov	r1,r7

000011f4 e1a04000   2631 	mov	r4,r0

                    2632 ;708: 


                    2633 ;709:     memcpy(buffer + bufPos, octetString, octetStringSize);


                    2634 

000011f8 e0840006   2635 	add	r0,r4,r6

000011fc eb000000*  2636 	bl	memcpy

                    2637 ;710:     bufPos += octetStringSize;


                    2638 

                    2639 ;711: 


                    2640 ;712:     return bufPos;


                    2641 

00001200 e0840005   2642 	add	r0,r4,r5

00001204 e8bd80f0   2643 	ldmfd	[sp]!,{r4-r7,pc}

                    2644 	.endf	BerEncoder_encodeOctetString

                    2645 	.align	4

                    2646 

                    2647 ;tag	r0	param

                    2648 ;octetString	r7	param

                    2649 ;octetStringSize	r5	param

                    2650 ;buffer	r6	param

                    2651 ;bufPos	r4	param

                    2652 

                    2653 	.section ".bss","awb"

                    2654 .L5374:

                    2655 	.data

                    2656 	.text

                    2657 

                    2658 ;713: }


                    2659 

                    2660 ;714: 


                    2661 ;715: int BerEncoder_encodeBitString( unsigned char ucTag, int iBitStringSize, unsigned char* pBitString,


                    2662 	.align	4

                    2663 	.align	4

                    2664 BerEncoder_encodeBitString::

00001208 e92d44f0   2665 	stmfd	[sp]!,{r4-r7,r10,lr}

                    2666 ;716:                                 unsigned char* pBuffer, int iBufPos )


                    2667 ;717: {


                    2668 

0000120c e3a04000   2669 	mov	r4,0

                    2670 ;718:     int iByteSize = iBitStringSize / 8;


                    2671 

00001210 e1a06002   2672 	mov	r6,r2

00001214 e1a02fc1   2673 	mov	r2,r1 asr 31


                                                                      Page 45
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
00001218 e1a05003   2674 	mov	r5,r3

0000121c e59dc018   2675 	ldr	r12,[sp,24]

00001220 e0813ea2   2676 	add	r3,r1,r2 lsr 29

00001224 e7c5000c   2677 	strb	r0,[r5,r12]

                    2678 ;724: 


                    2679 ;725:     if ( iBitStringSize % 8 )


                    2680 

00001228 e2020007   2681 	and	r0,r2,7

0000122c e28c2001   2682 	add	r2,r12,1

00001230 e1a071c3   2683 	mov	r7,r3 asr 3

                    2684 ;719:     int i;


                    2685 ;720:     int iPadding;


                    2686 ;721:     unsigned char ucPaddingMask = 0;


                    2687 

                    2688 ;722: 


                    2689 ;723:     pBuffer[iBufPos++] = ucTag;


                    2690 

00001234 e0810000   2691 	add	r0,r1,r0

00001238 e3c00007   2692 	bic	r0,r0,7

0000123c e1510000   2693 	cmp	r1,r0

                    2694 ;726:     {


                    2695 

                    2696 ;727: 


                    2697 ;728:         iByteSize++;


                    2698 

00001240 12877001   2699 	addne	r7,r7,1

                    2700 ;729: 


                    2701 ;730:     }//if ( iBitStringSize % 8 )


                    2702 ;731: 


                    2703 ;732:     iPadding = ( iByteSize * 8 ) - iBitStringSize;


                    2704 

00001244 e061a187   2705 	rsb	r10,r1,r7 lsl 3

                    2706 ;733: 


                    2707 ;734:     iBufPos = BerEncoder_encodeLength( iByteSize + 1, pBuffer, iBufPos );


                    2708 

00001248 e1a01005   2709 	mov	r1,r5

0000124c e2870001   2710 	add	r0,r7,1

00001250 ebfffb6a*  2711 	bl	BerEncoder_encodeLength

                    2712 ;735: 


                    2713 ;736:     pBuffer[iBufPos++] = iPadding;


                    2714 

00001254 e280c001   2715 	add	r12,r0,1

00001258 e7c5a000   2716 	strb	r10,[r5,r0]

                    2717 ;737: 


                    2718 ;738: 


                    2719 ;739: 


                    2720 ;740:     for ( i = 0; i < iByteSize; i++ )


                    2721 

0000125c e1a00004   2722 	mov	r0,r4

00001260 e3570000   2723 	cmp	r7,0

00001264 a1a03007   2724 	movge	r3,r7

00001268 b3a03000   2725 	movlt	r3,0

0000126c e1b021a3   2726 	movs	r2,r3 lsr 3

00001270 0a00001b   2727 	beq	.L5432

00001274 e2861001   2728 	add	r1,r6,1

                    2729 .L5448:

00001278 e7d67000   2730 	ldrb	r7,[r6,r0]

0000127c e2800008   2731 	add	r0,r0,8

00001280 e7c5700c   2732 	strb	r7,[r5,r12]

00001284 e4d17008   2733 	ldrb	r7,[r1],8

00001288 e28cc001   2734 	add	r12,r12,1


                                                                      Page 46
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
0000128c e7c5700c   2735 	strb	r7,[r5,r12]

00001290 e5517007   2736 	ldrb	r7,[r1,-7]

00001294 e28cc001   2737 	add	r12,r12,1

00001298 e7c5700c   2738 	strb	r7,[r5,r12]

0000129c e5517006   2739 	ldrb	r7,[r1,-6]

000012a0 e28cc001   2740 	add	r12,r12,1

000012a4 e7c5700c   2741 	strb	r7,[r5,r12]

000012a8 e5517005   2742 	ldrb	r7,[r1,-5]

000012ac e28cc001   2743 	add	r12,r12,1

000012b0 e7c5700c   2744 	strb	r7,[r5,r12]

000012b4 e5517004   2745 	ldrb	r7,[r1,-4]

000012b8 e28cc001   2746 	add	r12,r12,1

000012bc e7c5700c   2747 	strb	r7,[r5,r12]

000012c0 e5517003   2748 	ldrb	r7,[r1,-3]

000012c4 e28cc001   2749 	add	r12,r12,1

000012c8 e7c5700c   2750 	strb	r7,[r5,r12]

000012cc e5517002   2751 	ldrb	r7,[r1,-2]

000012d0 e28cc001   2752 	add	r12,r12,1

000012d4 e7c5700c   2753 	strb	r7,[r5,r12]

000012d8 e28cc001   2754 	add	r12,r12,1

000012dc e2522001   2755 	subs	r2,r2,1

000012e0 1affffe4   2756 	bne	.L5448

                    2757 .L5432:

000012e4 e2132007   2758 	ands	r2,r3,7

000012e8 0a000005   2759 	beq	.L5385

                    2760 .L5452:

000012ec e7d61000   2761 	ldrb	r1,[r6,r0]

000012f0 e2800001   2762 	add	r0,r0,1

000012f4 e7c5100c   2763 	strb	r1,[r5,r12]

000012f8 e28cc001   2764 	add	r12,r12,1

000012fc e2522001   2765 	subs	r2,r2,1

00001300 1afffff9   2766 	bne	.L5452

                    2767 .L5385:

                    2768 ;744: 


                    2769 ;745:     }//for ( i = 0; i < iByteSize; i++ )


                    2770 ;746: 


                    2771 ;747: 


                    2772 ;748: 


                    2773 ;749:     for( i = 0; i < iPadding; i++ )


                    2774 

00001304 e3a00000   2775 	mov	r0,0

00001308 e35a0000   2776 	cmp	r10,0

0000130c a1a0300a   2777 	movge	r3,r10

00001310 b3a03000   2778 	movlt	r3,0

00001314 e1b021a3   2779 	movs	r2,r3 lsr 3

00001318 0a000013   2780 	beq	.L5455

0000131c e3a01001   2781 	mov	r1,1

                    2782 .L5471:

00001320 e0844011   2783 	add	r4,r4,r1 lsl r0

00001324 e2806001   2784 	add	r6,r0,1

00001328 e0844611   2785 	add	r4,r4,r1 lsl r6

0000132c e2806002   2786 	add	r6,r0,2

00001330 e0844611   2787 	add	r4,r4,r1 lsl r6

00001334 e2806003   2788 	add	r6,r0,3

00001338 e0844611   2789 	add	r4,r4,r1 lsl r6

0000133c e2806004   2790 	add	r6,r0,4

00001340 e0844611   2791 	add	r4,r4,r1 lsl r6

00001344 e2806005   2792 	add	r6,r0,5

00001348 e0844611   2793 	add	r4,r4,r1 lsl r6

0000134c e2806006   2794 	add	r6,r0,6

00001350 e0844611   2795 	add	r4,r4,r1 lsl r6


                                                                      Page 47
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
00001354 e2806007   2796 	add	r6,r0,7

00001358 e0844611   2797 	add	r4,r4,r1 lsl r6

0000135c e20440ff   2798 	and	r4,r4,255

00001360 e2800008   2799 	add	r0,r0,8

00001364 e2522001   2800 	subs	r2,r2,1

00001368 1affffec   2801 	bne	.L5471

                    2802 .L5455:

0000136c e2132007   2803 	ands	r2,r3,7

00001370 13a03001   2804 	movne	r3,1

                    2805 .L5475:

00001374 10844013   2806 	addne	r4,r4,r3 lsl r0

00001378 120440ff   2807 	andne	r4,r4,255

0000137c 12800001   2808 	addne	r0,r0,1

00001380 12522001   2809 	subnes	r2,r2,1

00001384 1afffffa   2810 	bne	.L5475

                    2811 .L5389:

                    2812 ;753: 


                    2813 ;754:     }//for( i = 0; i < iPadding; i++ )


                    2814 ;755: 


                    2815 ;756:     ucPaddingMask = ~ucPaddingMask;


                    2816 

                    2817 ;757: 


                    2818 ;758:     pBuffer[iBufPos -1] = pBuffer[iBufPos -1] & ucPaddingMask;


                    2819 

00001388 e08c0005   2820 	add	r0,r12,r5

0000138c e5501001   2821 	ldrb	r1,[r0,-1]

00001390 e1c11004   2822 	bic	r1,r1,r4

00001394 e5401001   2823 	strb	r1,[r0,-1]

                    2824 ;759: 


                    2825 ;760:     return iBufPos;


                    2826 

00001398 e1a0000c   2827 	mov	r0,r12

0000139c e8bd84f0   2828 	ldmfd	[sp]!,{r4-r7,r10,pc}

                    2829 	.endf	BerEncoder_encodeBitString

                    2830 	.align	4

                    2831 ;iByteSize	r7	local

                    2832 ;i	r0	local

                    2833 ;iPadding	r10	local

                    2834 ;ucPaddingMask	r4	local

                    2835 

                    2836 ;ucTag	r0	param

                    2837 ;iBitStringSize	r1	param

                    2838 ;pBitString	r6	param

                    2839 ;pBuffer	r5	param

                    2840 ;iBufPos	r12	param

                    2841 

                    2842 	.section ".bss","awb"

                    2843 .L5786:

                    2844 	.data

                    2845 	.text

                    2846 

                    2847 ;761: }


                    2848 

                    2849 ;762: 


                    2850 ;763: int BerEncoder_encodeBitStringUshortBuf(uint8_t tag, int bitCount, uint16_t data,


                    2851 	.align	4

                    2852 	.align	4

                    2853 BerEncoder_encodeBitStringUshortBuf::

000013a0 e59dc000   2854 	ldr	r12,[sp]

                    2855 ;764:     uint8_t* outBuf, int outBufPos)


                    2856 ;765: {



                                                                      Page 48
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
                    2857 

                    2858 ;766:     uint8_t padding = sizeof(uint16_t) * 8 - bitCount;


                    2859 

000013a4 e2611010   2860 	rsb	r1,r1,16

                    2861 ;767:     outBuf[outBufPos++] = tag;


                    2862 

000013a8 e7c3000c   2863 	strb	r0,[r3,r12]

000013ac e28cc001   2864 	add	r12,r12,1

                    2865 ;768:     outBuf[outBufPos++] = 3;//length


                    2866 

000013b0 e3a00003   2867 	mov	r0,3

000013b4 e7c3000c   2868 	strb	r0,[r3,r12]

000013b8 e28cc001   2869 	add	r12,r12,1

                    2870 ;769:     outBuf[outBufPos++] = padding;


                    2871 

000013bc e7c3100c   2872 	strb	r1,[r3,r12]

000013c0 e28cc001   2873 	add	r12,r12,1

                    2874 ;770:     outBuf[outBufPos++] = data & 0xFF;


                    2875 

000013c4 e7c3200c   2876 	strb	r2,[r3,r12]

000013c8 e28cc001   2877 	add	r12,r12,1

                    2878 ;771:     outBuf[outBufPos++] = data >> 8;


                    2879 

000013cc e1a00442   2880 	mov	r0,r2 asr 8

000013d0 e7c3000c   2881 	strb	r0,[r3,r12]

                    2882 ;772:     return outBufPos;


                    2883 

000013d4 e28c0001   2884 	add	r0,r12,1

000013d8 e12fff1e*  2885 	ret	

                    2886 	.endf	BerEncoder_encodeBitStringUshortBuf

                    2887 	.align	4

                    2888 

                    2889 ;tag	r0	param

                    2890 ;bitCount	r1	param

                    2891 ;data	r2	param

                    2892 ;outBuf	r3	param

                    2893 ;outBufPos	r12	param

                    2894 

                    2895 	.section ".bss","awb"

                    2896 .L5854:

                    2897 	.data

                    2898 	.text

                    2899 

                    2900 ;773: }


                    2901 

                    2902 ;774: 


                    2903 ;775: int BerEncoder_encodeUshortBitString(uint8_t tag, int bitCount, uint16_t data,


                    2904 	.align	4

                    2905 	.align	4

                    2906 BerEncoder_encodeUshortBitString::

000013dc e92d0010   2907 	stmfd	[sp]!,{r4}

000013e0 e59d4004   2908 	ldr	r4,[sp,4]

                    2909 ;776:     uint8_t* outBuf, int outBufPos)


                    2910 ;777: {


                    2911 

                    2912 ;778:     uint8_t padding = sizeof(uint16_t) * 8 - bitCount;


                    2913 

000013e4 e261c010   2914 	rsb	r12,r1,16

                    2915 ;779:     outBuf[outBufPos++] = tag;


                    2916 

000013e8 e7c30004   2917 	strb	r0,[r3,r4]


                                                                      Page 49
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
000013ec e2844001   2918 	add	r4,r4,1

                    2919 ;780:     outBuf[outBufPos++] = 3;//length


                    2920 

000013f0 e3a00003   2921 	mov	r0,3

000013f4 e7c30004   2922 	strb	r0,[r3,r4]

000013f8 e2844001   2923 	add	r4,r4,1

                    2924 ;781:     outBuf[outBufPos++] = padding;


                    2925 

000013fc e7c3c004   2926 	strb	r12,[r3,r4]

00001400 e2844001   2927 	add	r4,r4,1

                    2928 ;782:     //Сдвигаем чтобы неиспользуемые биты были младшими


                    2929 ;783:     data <<= padding;


                    2930 

00001404 e20cc0ff   2931 	and	r12,r12,255

00001408 e1a02c12   2932 	mov	r2,r2 lsl r12

0000140c e1a02802   2933 	mov	r2,r2 lsl 16

00001410 e1a02822   2934 	mov	r2,r2 lsr 16

                    2935 ;784:     outBuf[outBufPos++] = data >> 8;


                    2936 

00001414 e1a00442   2937 	mov	r0,r2 asr 8

00001418 e7c30004   2938 	strb	r0,[r3,r4]

0000141c e2844001   2939 	add	r4,r4,1

                    2940 ;785:     outBuf[outBufPos++] = data & 0xFF;


                    2941 

00001420 e7c32004   2942 	strb	r2,[r3,r4]

                    2943 ;786:     return outBufPos;


                    2944 

00001424 e2840001   2945 	add	r0,r4,1

00001428 e8bd0010   2946 	ldmfd	[sp]!,{r4}

0000142c e12fff1e*  2947 	ret	

                    2948 	.endf	BerEncoder_encodeUshortBitString

                    2949 	.align	4

                    2950 ;padding	r12	local

                    2951 

                    2952 ;tag	r0	param

                    2953 ;bitCount	r1	param

                    2954 ;data	r2	param

                    2955 ;outBuf	r3	param

                    2956 ;outBufPos	r4	param

                    2957 

                    2958 	.section ".bss","awb"

                    2959 .L5886:

                    2960 	.data

                    2961 	.text

                    2962 

                    2963 ;787: }


                    2964 

                    2965 ;788: 


                    2966 ;789: int BerEncoder_encodeUcharBitString(uint8_t tag, int bitCount, uint8_t data,


                    2967 	.align	4

                    2968 	.align	4

                    2969 BerEncoder_encodeUcharBitString::

00001430 e92d0010   2970 	stmfd	[sp]!,{r4}

00001434 e59d4004   2971 	ldr	r4,[sp,4]

                    2972 ;790:     uint8_t* outBuf, int outBufPos)


                    2973 ;791: {


                    2974 

                    2975 ;792:     uint8_t padding = sizeof(uint8_t) * 8 - bitCount;


                    2976 

00001438 e261c008   2977 	rsb	r12,r1,8

                    2978 ;793:     outBuf[outBufPos++] = tag;



                                                                      Page 50
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
                    2979 

0000143c e7c30004   2980 	strb	r0,[r3,r4]

00001440 e2844001   2981 	add	r4,r4,1

                    2982 ;794:     outBuf[outBufPos++] = 2;//length


                    2983 

00001444 e3a00002   2984 	mov	r0,2

00001448 e7c30004   2985 	strb	r0,[r3,r4]

0000144c e2844001   2986 	add	r4,r4,1

                    2987 ;795:     outBuf[outBufPos++] = padding;


                    2988 

00001450 e7c3c004   2989 	strb	r12,[r3,r4]

00001454 e2844001   2990 	add	r4,r4,1

                    2991 ;796:     //Сдвигаем чтобы неиспользуемые биты были младшими


                    2992 ;797:     data <<= padding;


                    2993 

00001458 e20cc0ff   2994 	and	r12,r12,255

0000145c e1a02c12   2995 	mov	r2,r2 lsl r12

                    2996 ;798:     outBuf[outBufPos++] = data;


                    2997 

00001460 e7c32004   2998 	strb	r2,[r3,r4]

                    2999 ;799:     return outBufPos;


                    3000 

00001464 e2840001   3001 	add	r0,r4,1

00001468 e8bd0010   3002 	ldmfd	[sp]!,{r4}

0000146c e12fff1e*  3003 	ret	

                    3004 	.endf	BerEncoder_encodeUcharBitString

                    3005 	.align	4

                    3006 ;padding	r12	local

                    3007 

                    3008 ;tag	r0	param

                    3009 ;bitCount	r1	param

                    3010 ;data	r2	param

                    3011 ;outBuf	r3	param

                    3012 ;outBufPos	r4	param

                    3013 

                    3014 	.section ".bss","awb"

                    3015 .L5918:

                    3016 	.data

                    3017 	.text

                    3018 

                    3019 ;800: }


                    3020 

                    3021 ;801: 


                    3022 ;802: 


                    3023 ;803: int BerEncoder_determineEncodedStringSize( const char* String )


                    3024 	.align	4

                    3025 	.align	4

                    3026 BerEncoder_determineEncodedStringSize::

00001470 e92d4010   3027 	stmfd	[sp]!,{r4,lr}

                    3028 ;804: {


                    3029 

                    3030 ;805: 


                    3031 ;806:      int iSize;


                    3032 ;807:      int iLength;


                    3033 ;808: 


                    3034 ;809:      if( String != NULL )


                    3035 

00001474 e3500000   3036 	cmp	r0,0

                    3037 ;820: 


                    3038 ;821:     }//if( String != NULL )


                    3039 ;822:     else



                                                                      Page 51
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
                    3040 ;823:     {


                    3041 

                    3042 ;824:         return 2;


                    3043 

00001478 03a00002   3044 	moveq	r0,2

0000147c 0a000004   3045 	beq	.L5925

                    3046 ;810:      {


                    3047 

                    3048 ;811:         iSize = 1; //размер тэга


                    3049 

                    3050 ;812: 


                    3051 ;813:         iLength = strlen( String );


                    3052 

00001480 eb000000*  3053 	bl	strlen

                    3054 ;814: 


                    3055 ;815:         iSize += BerEncoder_determineLengthSize( iLength );


                    3056 

00001484 e1a04000   3057 	mov	r4,r0

00001488 ebfffd11*  3058 	bl	BerEncoder_determineLengthSize

0000148c e0800004   3059 	add	r0,r0,r4

                    3060 ;816: 


                    3061 ;817:         iSize += iLength;


                    3062 

00001490 e2800001   3063 	add	r0,r0,1

                    3064 ;818: 


                    3065 ;819:         return iSize;


                    3066 

                    3067 .L5925:

00001494 e8bd8010   3068 	ldmfd	[sp]!,{r4,pc}

                    3069 	.endf	BerEncoder_determineEncodedStringSize

                    3070 	.align	4

                    3071 ;iSize	r0	local

                    3072 ;iLength	r4	local

                    3073 

                    3074 ;String	r0	param

                    3075 

                    3076 	.section ".bss","awb"

                    3077 .L5958:

                    3078 	.data

                    3079 	.text

                    3080 

                    3081 ;825: 


                    3082 ;826:      }


                    3083 ;827: 


                    3084 ;828: }


                    3085 

                    3086 ;829: 


                    3087 ;830: int BerEncoder_encodeStringWithTL( unsigned char ucTag, const char* pString,


                    3088 	.align	4

                    3089 	.align	4

                    3090 BerEncoder_encodeStringWithTL::

00001498 e92d40f0   3091 	stmfd	[sp]!,{r4-r7,lr}

                    3092 ;831:                                     unsigned char* pBuffer, int iBufPos )


                    3093 ;832: {


                    3094 

                    3095 ;833: 


                    3096 ;834:     int iLength;


                    3097 ;835:     int i;


                    3098 ;836: 


                    3099 ;837:     pBuffer[iBufPos++] = ucTag;


                    3100 


                                                                      Page 52
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
0000149c e1b06001   3101 	movs	r6,r1

000014a0 e2834001   3102 	add	r4,r3,1

000014a4 e1a05002   3103 	mov	r5,r2

000014a8 e7c50003   3104 	strb	r0,[r5,r3]

                    3105 ;838: 


                    3106 ;839: 


                    3107 ;840:     if( pString != NULL )


                    3108 

                    3109 ;852: 


                    3110 ;853:         }//for( i = 0; i < iLength; i++ )


                    3111 ;854: 


                    3112 ;855:     }//if( pString != NULL )


                    3113 ;856:     else


                    3114 ;857:     {


                    3115 

                    3116 ;858: 


                    3117 ;859:         pBuffer[iBufPos++] = 0;


                    3118 

000014ac 07c56004   3119 	streqb	r6,[r5,r4]

000014b0 02844001   3120 	addeq	r4,r4,1

000014b4 0a000030   3121 	beq	.L5980

                    3122 ;841:     {


                    3123 

                    3124 ;842: 


                    3125 ;843:         iLength = strlen( pString );


                    3126 

000014b8 e1a00006   3127 	mov	r0,r6

000014bc eb000000*  3128 	bl	strlen

                    3129 ;844: 


                    3130 ;845:         iBufPos = BerEncoder_encodeLength( iLength, pBuffer, iBufPos );


                    3131 

000014c0 e1a02004   3132 	mov	r2,r4

000014c4 e1a01005   3133 	mov	r1,r5

000014c8 e1a07000   3134 	mov	r7,r0

000014cc ebfffacb*  3135 	bl	BerEncoder_encodeLength

000014d0 e1a04000   3136 	mov	r4,r0

                    3137 ;846: 


                    3138 ;847: 


                    3139 ;848:         for( i = 0; i < iLength; i++ )


                    3140 

000014d4 e3a02000   3141 	mov	r2,0

000014d8 e3570000   3142 	cmp	r7,0

000014dc a1a03007   3143 	movge	r3,r7

000014e0 b3a03000   3144 	movlt	r3,0

000014e4 e1b011a3   3145 	movs	r1,r3 lsr 3

000014e8 0a00001b   3146 	beq	.L6018

000014ec e2860001   3147 	add	r0,r6,1

                    3148 .L6034:

000014f0 e7d6c002   3149 	ldrb	r12,[r6,r2]

000014f4 e2822008   3150 	add	r2,r2,8

000014f8 e7c5c004   3151 	strb	r12,[r5,r4]

000014fc e4d0c008   3152 	ldrb	r12,[r0],8

00001500 e2844001   3153 	add	r4,r4,1

00001504 e7c5c004   3154 	strb	r12,[r5,r4]

00001508 e550c007   3155 	ldrb	r12,[r0,-7]

0000150c e2844001   3156 	add	r4,r4,1

00001510 e7c5c004   3157 	strb	r12,[r5,r4]

00001514 e550c006   3158 	ldrb	r12,[r0,-6]

00001518 e2844001   3159 	add	r4,r4,1

0000151c e7c5c004   3160 	strb	r12,[r5,r4]

00001520 e550c005   3161 	ldrb	r12,[r0,-5]


                                                                      Page 53
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
00001524 e2844001   3162 	add	r4,r4,1

00001528 e7c5c004   3163 	strb	r12,[r5,r4]

0000152c e550c004   3164 	ldrb	r12,[r0,-4]

00001530 e2844001   3165 	add	r4,r4,1

00001534 e7c5c004   3166 	strb	r12,[r5,r4]

00001538 e550c003   3167 	ldrb	r12,[r0,-3]

0000153c e2844001   3168 	add	r4,r4,1

00001540 e7c5c004   3169 	strb	r12,[r5,r4]

00001544 e550c002   3170 	ldrb	r12,[r0,-2]

00001548 e2844001   3171 	add	r4,r4,1

0000154c e7c5c004   3172 	strb	r12,[r5,r4]

00001550 e2844001   3173 	add	r4,r4,1

00001554 e2511001   3174 	subs	r1,r1,1

00001558 1affffe4   3175 	bne	.L6034

                    3176 .L6018:

0000155c e2131007   3177 	ands	r1,r3,7

00001560 0a000005   3178 	beq	.L5980

                    3179 .L6038:

00001564 e7d60002   3180 	ldrb	r0,[r6,r2]

00001568 e2822001   3181 	add	r2,r2,1

0000156c e7c50004   3182 	strb	r0,[r5,r4]

00001570 e2844001   3183 	add	r4,r4,1

00001574 e2511001   3184 	subs	r1,r1,1

00001578 1afffff9   3185 	bne	.L6038

                    3186 .L5980:

                    3187 ;860: 


                    3188 ;861:     }//else if( pString != NULL )


                    3189 ;862: 


                    3190 ;863:     return iBufPos;


                    3191 

0000157c e1a00004   3192 	mov	r0,r4

00001580 e8bd80f0   3193 	ldmfd	[sp]!,{r4-r7,pc}

                    3194 	.endf	BerEncoder_encodeStringWithTL

                    3195 	.align	4

                    3196 ;iLength	r7	local

                    3197 ;i	r2	local

                    3198 

                    3199 ;ucTag	r0	param

                    3200 ;pString	r6	param

                    3201 ;pBuffer	r5	param

                    3202 ;iBufPos	r4	param

                    3203 

                    3204 	.section ".bss","awb"

                    3205 .L6209:

                    3206 	.data

                    3207 	.text

                    3208 

                    3209 ;864: 


                    3210 ;865: }


                    3211 

                    3212 ;866: 


                    3213 ;867: int BerEncoder_encodeBoolean( unsigned char ucTag, unsigned char ucValue,


                    3214 	.align	4

                    3215 	.align	4

                    3216 BerEncoder_encodeBoolean::

00001584 e283c001   3217 	add	r12,r3,1

00001588 e7c20003   3218 	strb	r0,[r2,r3]

0000158c e3a00001   3219 	mov	r0,1

00001590 e7c2000c   3220 	strb	r0,[r2,r12]

00001594 e28cc001   3221 	add	r12,r12,1

00001598 e1b00001   3222 	movs	r0,r1


                                                                      Page 54
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
0000159c 13a00001   3223 	movne	r0,1

                    3224 ;868:                               unsigned char* pBuffer, int iBufPos )


                    3225 ;869: {


                    3226 

                    3227 ;870: 


                    3228 ;871:     pBuffer[iBufPos++] = ucTag;


                    3229 

                    3230 ;872:     pBuffer[iBufPos++] = 1;


                    3231 

                    3232 ;873: 


                    3233 ;874:     if( ucValue )


                    3234 

                    3235 ;875:     {


                    3236 

                    3237 ;876:         pBuffer[iBufPos++] = 0x01;


                    3238 

                    3239 ;877:     }


                    3240 ;878:     else


                    3241 ;879:     {


                    3242 

                    3243 ;880:         pBuffer[iBufPos++] = 0x00;


                    3244 

                    3245 ;881:     }


                    3246 ;882:     return iBufPos;


                    3247 

000015a0 e7c2000c   3248 	strb	r0,[r2,r12]

000015a4 e28c0001   3249 	add	r0,r12,1

000015a8 e12fff1e*  3250 	ret	

                    3251 	.endf	BerEncoder_encodeBoolean

                    3252 	.align	4

                    3253 

                    3254 ;ucTag	r0	param

                    3255 ;ucValue	r1	param

                    3256 ;pBuffer	r2	param

                    3257 ;iBufPos	r12	param

                    3258 

                    3259 	.section ".bss","awb"

                    3260 .L6288:

                    3261 	.data

                    3262 	.text

                    3263 

                    3264 ;883: }


                    3265 

                    3266 ;884: 


                    3267 ;885: void BerEncoder_RevertByteOrder( unsigned char* pOctets, const int iSize )


                    3268 	.align	4

                    3269 	.align	4

                    3270 BerEncoder_RevertByteOrder::

000015ac e92d00f0   3271 	stmfd	[sp]!,{r4-r7}

                    3272 ;886: {


                    3273 

                    3274 ;887:     int i;


                    3275 ;888:     unsigned char ucTemp;


                    3276 ;889: 


                    3277 ;890:     for( i = 0; i < iSize / 2; i++ )


                    3278 

000015b0 e3a04000   3279 	mov	r4,0

000015b4 e0812fa1   3280 	add	r2,r1,r1 lsr 31

000015b8 e1b060c2   3281 	movs	r6,r2 asr 1

000015bc 43a06000   3282 	movmi	r6,0

000015c0 e1b051a6   3283 	movs	r5,r6 lsr 3


                                                                      Page 55
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
000015c4 0a000024   3284 	beq	.L6316

000015c8 e0802001   3285 	add	r2,r0,r1

000015cc e2422008   3286 	sub	r2,r2,8

000015d0 e280c001   3287 	add	r12,r0,1

                    3288 .L6332:

000015d4 e5d27007   3289 	ldrb	r7,[r2,7]

000015d8 e7d03004   3290 	ldrb	r3,[r0,r4]

000015dc e7c07004   3291 	strb	r7,[r0,r4]

000015e0 e5d27006   3292 	ldrb	r7,[r2,6]

000015e4 e5c23007   3293 	strb	r3,[r2,7]

000015e8 e14c3097   3294 	swpb	r3,r7,[r12]

000015ec e2844008   3295 	add	r4,r4,8

000015f0 e5d27005   3296 	ldrb	r7,[r2,5]

000015f4 e5c23006   3297 	strb	r3,[r2,6]

000015f8 e5dc3001   3298 	ldrb	r3,[r12,1]

000015fc e5cc7001   3299 	strb	r7,[r12,1]

00001600 e5c23005   3300 	strb	r3,[r2,5]

00001604 e5d27004   3301 	ldrb	r7,[r2,4]

00001608 e5dc3002   3302 	ldrb	r3,[r12,2]

0000160c e5cc7002   3303 	strb	r7,[r12,2]

00001610 e5c23004   3304 	strb	r3,[r2,4]

00001614 e5d27003   3305 	ldrb	r7,[r2,3]

00001618 e5dc3003   3306 	ldrb	r3,[r12,3]

0000161c e5cc7003   3307 	strb	r7,[r12,3]

00001620 e5c23003   3308 	strb	r3,[r2,3]

00001624 e5d27002   3309 	ldrb	r7,[r2,2]

00001628 e5dc3004   3310 	ldrb	r3,[r12,4]

0000162c e5cc7004   3311 	strb	r7,[r12,4]

00001630 e5c23002   3312 	strb	r3,[r2,2]

00001634 e5d27001   3313 	ldrb	r7,[r2,1]

00001638 e5dc3005   3314 	ldrb	r3,[r12,5]

0000163c e5cc7005   3315 	strb	r7,[r12,5]

00001640 e5c23001   3316 	strb	r3,[r2,1]

00001644 e5d27000   3317 	ldrb	r7,[r2]

00001648 e5fc3006   3318 	ldrb	r3,[r12,6]!

0000164c e4cc7002   3319 	strb	r7,[r12],2

00001650 e4423008   3320 	strb	r3,[r2],-8

00001654 e2555001   3321 	subs	r5,r5,1

00001658 1affffdd   3322 	bne	.L6332

                    3323 .L6316:

0000165c e2165007   3324 	ands	r5,r6,7

00001660 0a000009   3325 	beq	.L6295

00001664 e0411004   3326 	sub	r1,r1,r4

00001668 e0811000   3327 	add	r1,r1,r0

0000166c e2411001   3328 	sub	r1,r1,1

                    3329 .L6336:

00001670 e5d12000   3330 	ldrb	r2,[r1]

00001674 e7d03004   3331 	ldrb	r3,[r0,r4]

00001678 e7c02004   3332 	strb	r2,[r0,r4]

0000167c e4413001   3333 	strb	r3,[r1],-1

00001680 e2844001   3334 	add	r4,r4,1

00001684 e2555001   3335 	subs	r5,r5,1

00001688 1afffff8   3336 	bne	.L6336

                    3337 .L6295:

0000168c e8bd00f0   3338 	ldmfd	[sp]!,{r4-r7}

00001690 e12fff1e*  3339 	ret	

                    3340 	.endf	BerEncoder_RevertByteOrder

                    3341 	.align	4

                    3342 ;i	r4	local

                    3343 ;ucTemp	r3	local

                    3344 


                                                                      Page 56
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
                    3345 ;pOctets	r0	param

                    3346 ;iSize	r1	param

                    3347 

                    3348 	.section ".bss","awb"

                    3349 .L6489:

                    3350 	.data

                    3351 	.text

                    3352 

                    3353 ;895: 


                    3354 ;896:     }


                    3355 ;897: }


                    3356 

                    3357 ;898: 


                    3358 ;899: int IsIncluded(unsigned char* str1, unsigned char* str2)


                    3359 	.align	4

                    3360 	.align	4

                    3361 IsIncluded::

00001694 e92d40f0   3362 	stmfd	[sp]!,{r4-r7,lr}

                    3363 ;900: {


                    3364 

                    3365 ;901:     int min_len;


                    3366 ;902:     int len1, len2;


                    3367 ;903:     int i;


                    3368 ;904: 


                    3369 ;905:     len1 = strlen((char*)str1);


                    3370 

00001698 e1a04001   3371 	mov	r4,r1

0000169c e1a05000   3372 	mov	r5,r0

000016a0 eb000000*  3373 	bl	strlen

000016a4 e1a06000   3374 	mov	r6,r0

                    3375 ;906:     len2 = strlen((char*)str2);


                    3376 

000016a8 e1a00004   3377 	mov	r0,r4

000016ac eb000000*  3378 	bl	strlen

                    3379 ;907: 


                    3380 ;908:     min_len = len1 < len2 ? len1 : len2;


                    3381 

000016b0 e3a02000   3382 	mov	r2,0

000016b4 e1500006   3383 	cmp	r0,r6

000016b8 c1a00006   3384 	movgt	r0,r6

                    3385 ;909: 


                    3386 ;910:     for ( i = 0; i < min_len; i++) {


                    3387 

000016bc e3500000   3388 	cmp	r0,0

000016c0 a1a0c000   3389 	movge	r12,r0

000016c4 b3a0c000   3390 	movlt	r12,0

000016c8 e1b031ac   3391 	movs	r3,r12 lsr 3

000016cc 0a00001f   3392 	beq	.L6546

000016d0 e2840001   3393 	add	r0,r4,1

000016d4 e2851001   3394 	add	r1,r5,1

                    3395 .L6547:

000016d8 e7d47002   3396 	ldrb	r7,[r4,r2]

000016dc e7d56002   3397 	ldrb	r6,[r5,r2]

000016e0 e1560007   3398 	cmp	r6,r7

000016e4 05d07000   3399 	ldreqb	r7,[r0]

000016e8 05d16000   3400 	ldreqb	r6,[r1]

000016ec 01560007   3401 	cmpeq	r6,r7

000016f0 05d07001   3402 	ldreqb	r7,[r0,1]

000016f4 05d16001   3403 	ldreqb	r6,[r1,1]

000016f8 01560007   3404 	cmpeq	r6,r7

000016fc 05d07002   3405 	ldreqb	r7,[r0,2]


                                                                      Page 57
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
00001700 05d16002   3406 	ldreqb	r6,[r1,2]

00001704 01560007   3407 	cmpeq	r6,r7

00001708 05d07003   3408 	ldreqb	r7,[r0,3]

0000170c 05d16003   3409 	ldreqb	r6,[r1,3]

00001710 01560007   3410 	cmpeq	r6,r7

00001714 05d07004   3411 	ldreqb	r7,[r0,4]

00001718 05d16004   3412 	ldreqb	r6,[r1,4]

0000171c 01560007   3413 	cmpeq	r6,r7

00001720 05d07005   3414 	ldreqb	r7,[r0,5]

00001724 05d16005   3415 	ldreqb	r6,[r1,5]

00001728 01560007   3416 	cmpeq	r6,r7

0000172c 05d07006   3417 	ldreqb	r7,[r0,6]

00001730 05d16006   3418 	ldreqb	r6,[r1,6]

00001734 01560007   3419 	cmpeq	r6,r7

00001738 1a000009   3420 	bne	.L6582

0000173c e2800008   3421 	add	r0,r0,8

00001740 e2811008   3422 	add	r1,r1,8

00001744 e2822008   3423 	add	r2,r2,8

00001748 e2533001   3424 	subs	r3,r3,1

0000174c 1affffe1   3425 	bne	.L6547

                    3426 .L6546:

00001750 e21c3007   3427 	ands	r3,r12,7

00001754 0a000007   3428 	beq	.L6515

                    3429 .L6581:

00001758 e7d41002   3430 	ldrb	r1,[r4,r2]

0000175c e7d50002   3431 	ldrb	r0,[r5,r2]

00001760 e1500001   3432 	cmp	r0,r1

                    3433 .L6582:

00001764 13a00000   3434 	movne	r0,0

00001768 1a000003   3435 	bne	.L6513

                    3436 .L6584:

0000176c e2822001   3437 	add	r2,r2,1

00001770 e2533001   3438 	subs	r3,r3,1

00001774 1afffff7   3439 	bne	.L6581

                    3440 .L6515:

                    3441 ;913:         }


                    3442 ;914:     }


                    3443 ;915:     return 1;


                    3444 

00001778 e3a00001   3445 	mov	r0,1

                    3446 .L6513:

0000177c e8bd80f0   3447 	ldmfd	[sp]!,{r4-r7,pc}

                    3448 	.endf	IsIncluded

                    3449 	.align	4

                    3450 ;min_len	r0	local

                    3451 ;len1	r6	local

                    3452 ;i	r2	local

                    3453 

                    3454 ;str1	r5	param

                    3455 ;str2	r4	param

                    3456 

                    3457 	.section ".bss","awb"

                    3458 .L6817:

                    3459 	.data

                    3460 	.text

                    3461 

                    3462 ;916: }


                    3463 

                    3464 ;917: 


                    3465 ;918: float BerDecoder_decodeFloat(unsigned char* buffer, int bufPos)


                    3466 	.align	4


                                                                      Page 58
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_71s1.s
                    3467 	.align	4

                    3468 BerDecoder_decodeFloat::

00001780 e24dd004   3469 	sub	sp,sp,4

00001784 e2811001   3470 	add	r1,r1,1

00001788 e7d02001   3471 	ldrb	r2,[r0,r1]

0000178c e2811001   3472 	add	r1,r1,1

00001790 e5cd2003   3473 	strb	r2,[sp,3]

00001794 e7d02001   3474 	ldrb	r2,[r0,r1]

00001798 e2811001   3475 	add	r1,r1,1

0000179c e5cd2002   3476 	strb	r2,[sp,2]

000017a0 e7d02001   3477 	ldrb	r2,[r0,r1]

000017a4 e2811001   3478 	add	r1,r1,1

000017a8 e5cd2001   3479 	strb	r2,[sp,1]

000017ac e7d00001   3480 	ldrb	r0,[r0,r1]

000017b0 e5cd0000   3481 	strb	r0,[sp]

                    3482 ;919: {


                    3483 

                    3484 ;920:     float value;


                    3485 ;921:     unsigned char* valueBuf = (unsigned char*)&value;


                    3486 

                    3487 ;922: 


                    3488 ;923:     int i;


                    3489 ;924: 


                    3490 ;925:     bufPos += 1; /* skip exponentWidth field */


                    3491 

                    3492 ;926: 


                    3493 ;927:     for (i = 3; i >= 0; i--) {


                    3494 

                    3495 ;929:     }


                    3496 ;930: 


                    3497 ;931:     return value;


                    3498 

000017b4 e59d0000   3499 	ldr	r0,[sp]

000017b8 e28dd004   3500 	add	sp,sp,4

000017bc e12fff1e*  3501 	ret	

                    3502 	.endf	BerDecoder_decodeFloat

                    3503 	.align	4

                    3504 ;value	[sp]	local

                    3505 

                    3506 ;buffer	r0	param

                    3507 ;bufPos	r1	param

                    3508 

                    3509 	.section ".bss","awb"

                    3510 .L6904:

                    3511 	.data

                    3512 	.text

                    3513 

                    3514 ;932: }


                    3515 	.align	4

                    3516 

                    3517 	.data

                    3518 	.ghsnote version,6

                    3519 	.ghsnote tools,3

                    3520 	.ghsnote options,0

                    3521 	.text

                    3522 	.align	4

