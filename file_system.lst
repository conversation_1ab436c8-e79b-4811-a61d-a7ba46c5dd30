                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_crg1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=file_system.c -o gh_crg1.o -list=file_system.lst C:\Users\<USER>\AppData\Local\Temp\gh_crg1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_crg1.s
Source File: file_system.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile file_system.c

                      10 ;		-o file_system.o

                      11 ;Source File:   file_system.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Oct 07 09:39:29 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "file_system.h"


                      21 ;2: 


                      22 ;3: #include "BaseAsnTypes.h"


                      23 ;4: #include "local_types.h"


                      24 ;5: #include "types.h"


                      25 ;6: #include "bufView.h"


                      26 ;7: #include "frsm.h"


                      27 ;8: #include "FS/ConfigFiles.h"


                      28 ;9: #include "FS/OscFiles.h"


                      29 ;10: #include <stddef.h>


                      30 ;11: 


                      31 ;12: #define CFG_DIR_NAME "config"


                      32 ;13: #define OSC_DIR_NAME "osc"


                      33 ;14: 


                      34 ;15: inline bool isDelimiter(char c)


                      35 

                      36 ;18: }


                      37 

                      38 ;19: 


                      39 ;20: // Ищет первый разделитель от конца строки к началу


                      40 ;21: // Если не найден, возвращает FALSE;


                      41 ;22: static bool findDelimiter(const StringView* fullName, size_t* delimiterPos )


                      42 

                      43 ;35: }


                      44 

                      45 ;36: 


                      46 ;37: // Удаляет разделитель директориев слева, если есть.


                      47 ;38: static void stripLeftDelim(StringView* name)


                      48 ;39: {


                      49 ;40:     //Срезаем лидирующий разделитель, если есть


                      50 ;41:     if (name->len > 0 && isDelimiter(name->p[0]))



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_crg1.s
                      51 ;42:     {


                      52 ;43:         StringView_init(name, name->p + 1, name->len - 1);


                      53 ;44:     }


                      54 ;45: }


                      55 ;46: 


                      56 ;47: //Разделяет полное имя файла на собственно имя файла и имя директория


                      57 ;48: //Имена всегда возвращаются без лидирущих разделителей.


                      58 ;49: static void splitFullFileName(const StringView* fullName,


                      59 

                      60 ;71:     }


                      61 ;72: }


                      62 

                      63 ;73: 


                      64 ;74: 


                      65 ;75: bool fs_init(void)


                      66 ;76: {


                      67 ;77:     return frsm_init() && CFGFS_init() && OSCFS_init();


                      68 ;78: }


                      69 ;79: 


                      70 ;80: 


                      71 ;81: static FNameErrCode oscFindFirst(StringView* startFileName,


                      72 ;82:     FSFindData* fileInfo, BufferView* bufForName)


                      73 ;83: {


                      74 ;84:     fileInfo->subsystem = FS_SUB_OSC;


                      75 ;85:     BufferView_writeStr(bufForName, "/" OSC_DIR_NAME "/");


                      76 ;86:     return OSCFS_findFirst(startFileName, fileInfo, bufForName);


                      77 ;87: }


                      78 ;88: 


                      79 ;89: FNameErrCode fs_findFirst(StringView* dirName, StringView* startFileName,


                      80 ;90:     FSFindData* fileInfo, BufferView* bufForName)


                      81 ;91: {


                      82 ;92:     FNameErrCode result;


                      83 ;93: 


                      84 ;94:     //dirName без лидирующего разделителя


                      85 ;95:     StringView strippedDirName;


                      86 ;96:     StringView_fromStringView(&strippedDirName, dirName);


                      87 ;97:     stripLeftDelim(&strippedDirName);


                      88 ;98: 


                      89 ;99:     //Если пустое имя директория, значит нужен список корневого директория


                      90 ;100:     fileInfo->rootDir = (strippedDirName.len == 0);


                      91 ;101:     if (fileInfo->rootDir


                      92 ;102:         || StringView_cmpCStr(&strippedDirName, CFG_DIR_NAME) == 0)


                      93 ;103:     {


                      94 ;104:         //Поиск в конфигурационных файлах


                      95 ;105:         //Сохраняем позицию буфера имени для восстановления, если


                      96 ;106:         //конфигурационного файла не найдено


                      97 ;107:         size_t oldNamePos = bufForName->pos;


                      98 ;108:         fileInfo->subsystem = FS_SUB_CFG;


                      99 ;109:         BufferView_writeStr(bufForName, "/" CFG_DIR_NAME "/");


                     100 ;110:         result = CFGFS_findFirst(startFileName, fileInfo, bufForName);


                     101 ;111:         //Если обходим корневоей директорий и ничего не нашли в конфигах,


                     102 ;112:         //переходим к поиску осциллограмм


                     103 ;113:         if (result == FNAME_NOT_FOUND && fileInfo->rootDir)


                     104 ;114:         {


                     105 ;115:             //восстанавливаем позицию буфера имени


                     106 ;116:             bufForName->pos = oldNamePos;


                     107 ;117:             result = oscFindFirst(startFileName, fileInfo, bufForName);


                     108 ;118:         }


                     109 ;119:     }


                     110 ;120:     else if(StringView_cmpCStr(&strippedDirName, OSC_DIR_NAME) == 0)


                     111 ;121:     {



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_crg1.s
                     112 ;122:         //Поиск в осциллограммах


                     113 ;123:         result = oscFindFirst(startFileName, fileInfo, bufForName);


                     114 ;124:     }


                     115 ;125:     else


                     116 ;126:     {


                     117 ;127:         result = FNAME_NOT_FOUND;


                     118 ;128:     }


                     119 ;129:     if (result == FNAME_OK)


                     120 ;130:     {


                     121 ;131:         StringView_init(&fileInfo->fileName, (char*)bufForName->p, bufForName->pos);


                     122 ;132:     }


                     123 ;133:     return result;


                     124 ;134: }


                     125 ;135: 


                     126 ;136: FNameErrCode fs_findNext(FSFindData* fileInfo, BufferView* bufForName)


                     127 ;137: {


                     128 ;138:     FNameErrCode result;


                     129 ;139: 


                     130 ;140:     if (fileInfo->subsystem == FS_SUB_CFG)


                     131 ;141:     {


                     132 ;142:         //Поиск в конфигурационных файлах


                     133 ;143:         //Сохраняем позицию буфера имени для восстановления, если


                     134 ;144:         //конфигурационного файла не найдено


                     135 ;145:         size_t oldNamePos = bufForName->pos;


                     136 ;146:         BufferView_writeStr(bufForName, "/" CFG_DIR_NAME "/");


                     137 ;147:         result = CFGFS_findNext(fileInfo, bufForName);


                     138 ;148:         //Если обходим корневоей директорий и ничего не нашли в конфигах,


                     139 ;149:         //переходим к поиску осциллограмм


                     140 ;150:         if (result == FNAME_NOT_FOUND && fileInfo->rootDir)


                     141 ;151:         {


                     142 ;152:             // закрытие поиска, может здесь должен быть вызов CFGFS_findClose(fileInfo)?


                     143 ;153:             fs_findClose(fileInfo);


                     144 ;154:             //восстанавливаем позицию буфера имени


                     145 ;155:             bufForName->pos = oldNamePos;


                     146 ;156:             result = oscFindFirst(NULL, fileInfo, bufForName);


                     147 ;157:         }


                     148 ;158:     }


                     149 ;159:     else if (fileInfo->subsystem == FS_SUB_OSC)


                     150 ;160:     {


                     151 ;161:         BufferView_writeStr(bufForName, "/" OSC_DIR_NAME "/");


                     152 ;162:         result = OSCFS_findNext(fileInfo, bufForName);


                     153 ;163:     }


                     154 ;164:     else


                     155 ;165:     {


                     156 ;166:         result = FNAME_NOT_FOUND;


                     157 ;167:     }


                     158 ;168:     if (result == FNAME_OK)


                     159 ;169:     {


                     160 ;170:         StringView_init(&fileInfo->fileName, (char*)bufForName->p, bufForName->pos);


                     161 ;171:     }


                     162 ;172:     return result;


                     163 ;173: }


                     164 ;174: void fs_findClose(FSFindData* fileInfo)


                     165 ;175: {


                     166 ;176:     if (fileInfo->subsystem == FS_SUB_CFG)


                     167 ;177:     {


                     168 ;178:         CFGFS_findClose(fileInfo);


                     169 ;179:     }


                     170 ;180:     else if (fileInfo->subsystem == FS_SUB_OSC)


                     171 ;181:     {


                     172 ;182:         OSCFS_findClose(fileInfo);



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_crg1.s
                     173 ;183:     }


                     174 ;184: }


                     175 ;185: static bool fileOpen(StringView* dirName, StringView* fileName, FRSM* frsm,


                     176 

                     177 ;199: }


                     178 

                     179 	.text

                     180 	.align	4

                     181 stripLeftDelim:

00000000 e92d4000    182 	stmfd	[sp]!,{lr}

00000004 e5902000    183 	ldr	r2,[r0]

00000008 e3520000    184 	cmp	r2,0

0000000c 0a000006    185 	beq	.L121

00000010 e5903004    186 	ldr	r3,[r0,4]

00000014 e5d31000    187 	ldrb	r1,[r3]

                     188 ;16: {


                     189 

                     190 ;17:     return c == '\\' || c == '/';


                     191 

00000018 e351005c    192 	cmp	r1,92

0000001c 1351002f    193 	cmpne	r1,47

00000020 02422001    194 	subeq	r2,r2,1

00000024 02831001    195 	addeq	r1,r3,1

00000028 0b000000*   196 	bleq	StringView_init

                     197 .L121:

0000002c e8bd4000    198 	ldmfd	[sp]!,{lr}

00000030 e12fff1e*   199 	ret	

                     200 	.endf	stripLeftDelim

                     201 	.align	4

                     202 ;c	r1	local

                     203 

                     204 ;name	r0	param

                     205 

                     206 	.section ".bss","awb"

                     207 .L226:

                     208 	.data

                     209 	.text

                     210 

                     211 

                     212 	.align	4

                     213 	.align	4

                     214 fs_init::

00000034 e92d4010    215 	stmfd	[sp]!,{r4,lr}

00000038 e3a04000    216 	mov	r4,0

0000003c eb000000*   217 	bl	frsm_init

00000040 e3500000    218 	cmp	r0,0

00000044 0a000005    219 	beq	.L244

00000048 eb000000*   220 	bl	CFGFS_init

0000004c e3500000    221 	cmp	r0,0

00000050 0a000002    222 	beq	.L244

00000054 eb000000*   223 	bl	OSCFS_init

00000058 e3500000    224 	cmp	r0,0

0000005c 13a04001    225 	movne	r4,1

                     226 .L244:

00000060 e20400ff    227 	and	r0,r4,255

00000064 e8bd8010    228 	ldmfd	[sp]!,{r4,pc}

                     229 	.endf	fs_init

                     230 	.align	4

                     231 

                     232 	.section ".bss","awb"

                     233 .L306:


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_crg1.s
                     234 	.data

                     235 	.text

                     236 

                     237 

                     238 	.align	4

                     239 	.align	4

                     240 oscFindFirst:

00000068 e92d4070    241 	stmfd	[sp]!,{r4-r6,lr}

0000006c e1a05002    242 	mov	r5,r2

00000070 e1a06000    243 	mov	r6,r0

00000074 e3a00001    244 	mov	r0,1

00000078 e1a04001    245 	mov	r4,r1

0000007c e5840008    246 	str	r0,[r4,8]

00000080 e28f1000*   247 	adr	r1,.L359

00000084 e1a00005    248 	mov	r0,r5

00000088 eb000000*   249 	bl	BufferView_writeStr

0000008c e1a02005    250 	mov	r2,r5

00000090 e1a01004    251 	mov	r1,r4

00000094 e1a00006    252 	mov	r0,r6

00000098 eb000000*   253 	bl	OSCFS_findFirst

0000009c e8bd4070    254 	ldmfd	[sp]!,{r4-r6,lr}

000000a0 e12fff1e*   255 	ret	

                     256 	.endf	oscFindFirst

                     257 	.align	4

                     258 ;.L351	.L354	static

                     259 

                     260 ;startFileName	r6	param

                     261 ;fileInfo	r4	param

                     262 ;bufForName	r5	param

                     263 

                     264 	.section ".bss","awb"

                     265 .L350:

                     266 	.data

                     267 	.text

                     268 

                     269 

                     270 	.align	4

                     271 	.align	4

                     272 fs_findFirst::

000000a4 e92d44f0    273 	stmfd	[sp]!,{r4-r7,r10,lr}

000000a8 e1a06002    274 	mov	r6,r2

000000ac e1a05003    275 	mov	r5,r3

000000b0 e24dd008    276 	sub	sp,sp,8

000000b4 e1a07001    277 	mov	r7,r1

000000b8 e1a01000    278 	mov	r1,r0

000000bc e1a0000d    279 	mov	r0,sp

000000c0 eb000000*   280 	bl	StringView_fromStringView

000000c4 e1a0000d    281 	mov	r0,sp

000000c8 ebffffcc*   282 	bl	stripLeftDelim

000000cc e59d0000    283 	ldr	r0,[sp]

000000d0 e3500000    284 	cmp	r0,0

000000d4 03a00001    285 	moveq	r0,1

000000d8 13a00000    286 	movne	r0,0

000000dc e5c60000    287 	strb	r0,[r6]

000000e0 e3500000    288 	cmp	r0,0

000000e4 1a000004    289 	bne	.L363

000000e8 e28f1000*   290 	adr	r1,.L534

000000ec e1a0000d    291 	mov	r0,sp

000000f0 eb000000*   292 	bl	StringView_cmpCStr

000000f4 e3500000    293 	cmp	r0,0

000000f8 1a000017    294 	bne	.L362


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_crg1.s
                     295 .L363:

000000fc e595a004    296 	ldr	r10,[r5,4]

00000100 e3a00000    297 	mov	r0,0

00000104 e5860008    298 	str	r0,[r6,8]

00000108 e28f1000*   299 	adr	r1,.L535

0000010c e1a00005    300 	mov	r0,r5

00000110 eb000000*   301 	bl	BufferView_writeStr

00000114 e1a02005    302 	mov	r2,r5

00000118 e1a01006    303 	mov	r1,r6

0000011c e1a00007    304 	mov	r0,r7

00000120 eb000000*   305 	bl	CFGFS_findFirst

00000124 e1a04000    306 	mov	r4,r0

00000128 e3540001    307 	cmp	r4,1

0000012c 1a000015    308 	bne	.L368

00000130 e5d60000    309 	ldrb	r0,[r6]

00000134 e3500000    310 	cmp	r0,0

00000138 0a000012    311 	beq	.L368

0000013c e585a004    312 	str	r10,[r5,4]

00000140 e1a02005    313 	mov	r2,r5

00000144 e1a01006    314 	mov	r1,r6

00000148 e1a00007    315 	mov	r0,r7

0000014c ebffffc5*   316 	bl	oscFindFirst

00000150 e1b04000    317 	movs	r4,r0

00000154 1a000010    318 	bne	.L372

00000158 ea00000c    319 	b	.L373

                     320 .L362:

0000015c e28f1000*   321 	adr	r1,.L536

00000160 e1a0000d    322 	mov	r0,sp

00000164 eb000000*   323 	bl	StringView_cmpCStr

00000168 e3500000    324 	cmp	r0,0

0000016c 13a00001    325 	movne	r0,1

00000170 1a00000a    326 	bne	.L360

00000174 e1a02005    327 	mov	r2,r5

00000178 e1a01006    328 	mov	r1,r6

0000017c e1a00007    329 	mov	r0,r7

00000180 ebffffb8*   330 	bl	oscFindFirst

00000184 e1a04000    331 	mov	r4,r0

                     332 .L368:

00000188 e3540000    333 	cmp	r4,0

0000018c 1a000002    334 	bne	.L372

                     335 .L373:

00000190 e8950006    336 	ldmfd	[r5],{r1-r2}

00000194 e286000c    337 	add	r0,r6,12

00000198 eb000000*   338 	bl	StringView_init

                     339 .L372:

0000019c e1a00004    340 	mov	r0,r4

                     341 .L360:

000001a0 e28dd008    342 	add	sp,sp,8

000001a4 e8bd84f0    343 	ldmfd	[sp]!,{r4-r7,r10,pc}

                     344 	.endf	fs_findFirst

                     345 	.align	4

                     346 ;result	r4	local

                     347 ;strippedDirName	[sp]	local

                     348 ;.L491	.L497	static

                     349 ;oldNamePos	r10	local

                     350 ;.L492	.L498	static

                     351 ;.L493	.L496	static

                     352 

                     353 ;dirName	r0	param

                     354 ;startFileName	r7	param

                     355 ;fileInfo	r6	param


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_crg1.s
                     356 ;bufForName	r5	param

                     357 

                     358 	.section ".bss","awb"

                     359 .L490:

                     360 	.data

                     361 	.text

                     362 

                     363 

                     364 	.align	4

                     365 	.align	4

                     366 fs_findNext::

000001a8 e92d40f0    367 	stmfd	[sp]!,{r4-r7,lr}

000001ac e1a06000    368 	mov	r6,r0

000001b0 e5960008    369 	ldr	r0,[r6,8]

000001b4 e1a05001    370 	mov	r5,r1

000001b8 e3500000    371 	cmp	r0,0

000001bc 1a000016    372 	bne	.L539

000001c0 e5957004    373 	ldr	r7,[r5,4]

000001c4 e28f1000*   374 	adr	r1,.L535

000001c8 e1a00005    375 	mov	r0,r5

000001cc eb000000*   376 	bl	BufferView_writeStr

000001d0 e1a01005    377 	mov	r1,r5

000001d4 e1a00006    378 	mov	r0,r6

000001d8 eb000000*   379 	bl	CFGFS_findNext

000001dc e1a04000    380 	mov	r4,r0

000001e0 e3540001    381 	cmp	r4,1

000001e4 1a000018    382 	bne	.L544

000001e8 e5d60000    383 	ldrb	r0,[r6]

000001ec e3500000    384 	cmp	r0,0

000001f0 0a000015    385 	beq	.L544

000001f4 e1a00006    386 	mov	r0,r6

000001f8 eb000022*   387 	bl	fs_findClose

000001fc e5857004    388 	str	r7,[r5,4]

00000200 e1a02005    389 	mov	r2,r5

00000204 e1a01006    390 	mov	r1,r6

00000208 e3a00000    391 	mov	r0,0

0000020c ebffff95*   392 	bl	oscFindFirst

00000210 e1b04000    393 	movs	r4,r0

00000214 1a000011    394 	bne	.L548

00000218 ea00000d    395 	b	.L549

                     396 .L539:

0000021c e3500001    397 	cmp	r0,1

00000220 13a00001    398 	movne	r0,1

00000224 1a00000e    399 	bne	.L537

00000228 e28f1000*   400 	adr	r1,.L359

0000022c e1a00005    401 	mov	r0,r5

00000230 eb000000*   402 	bl	BufferView_writeStr

00000234 e1a01005    403 	mov	r1,r5

00000238 e1a00006    404 	mov	r0,r6

0000023c eb000000*   405 	bl	OSCFS_findNext

00000240 e1b04000    406 	movs	r4,r0

00000244 1a000005    407 	bne	.L548

00000248 ea000001    408 	b	.L549

                     409 .L544:

0000024c e3540000    410 	cmp	r4,0

00000250 1a000002    411 	bne	.L548

                     412 .L549:

00000254 e8950006    413 	ldmfd	[r5],{r1-r2}

00000258 e286000c    414 	add	r0,r6,12

0000025c eb000000*   415 	bl	StringView_init

                     416 .L548:


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_crg1.s
00000260 e1a00004    417 	mov	r0,r4

                     418 .L537:

00000264 e8bd80f0    419 	ldmfd	[sp]!,{r4-r7,pc}

                     420 	.endf	fs_findNext

                     421 	.align	4

                     422 .L359:

                     423 ;	"/osc/\000"

00000268 63736f2f    424 	.data.b	47,111,115,99

0000026c 002f       425 	.data.b	47,0

0000026e 0000       426 	.align 4

                     427 

                     428 	.type	.L359,$object

                     429 	.size	.L359,4

                     430 

                     431 .L534:

                     432 ;	"config\000"

00000270 666e6f63    433 	.data.b	99,111,110,102

00000274 6769       434 	.data.b	105,103

00000276 00         435 	.data.b	0

00000277 00         436 	.align 4

                     437 

                     438 	.type	.L534,$object

                     439 	.size	.L534,4

                     440 

                     441 .L535:

                     442 ;	"/config/\000"

00000278 6e6f632f    443 	.data.b	47,99,111,110

0000027c 2f676966    444 	.data.b	102,105,103,47

00000280 00         445 	.data.b	0

00000281 000000     446 	.align 4

                     447 

                     448 	.type	.L535,$object

                     449 	.size	.L535,4

                     450 

                     451 .L536:

                     452 ;	"osc\000"

00000284 0063736f    453 	.data.b	111,115,99,0

                     454 	.align 4

                     455 

                     456 	.type	.L536,$object

                     457 	.size	.L536,4

                     458 

                     459 	.align	4

                     460 ;result	r4	local

                     461 ;oldNamePos	r7	local

                     462 ;.L641	.L646	static

                     463 ;.L642	.L645	static

                     464 

                     465 ;fileInfo	r6	param

                     466 ;bufForName	r5	param

                     467 

                     468 	.section ".bss","awb"

                     469 .L640:

                     470 	.data

                     471 	.text

                     472 

                     473 

                     474 	.align	4

                     475 	.align	4

                     476 fs_findClose::

00000288 e5901008    477 	ldr	r1,[r0,8]


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_crg1.s
0000028c e3510000    478 	cmp	r1,0

00000290 0a000000*   479 	beq	CFGFS_findClose

00000294 e3510001    480 	cmp	r1,1

00000298 0a000000*   481 	beq	OSCFS_findClose

0000029c e12fff1e*   482 	ret	

                     483 	.endf	fs_findClose

                     484 	.align	4

                     485 

                     486 ;fileInfo	r0	param

                     487 

                     488 	.section ".bss","awb"

                     489 .L722:

                     490 	.data

                     491 	.text

                     492 

                     493 

                     494 ;200: 


                     495 ;201: bool fs_fileOpen(StringView* fullFileName, size_t startPos, uint32_t* frsmID,


                     496 	.align	4

                     497 	.align	4

                     498 	.align	4

                     499 fs_fileOpen::

000002a0 e92d40f0    500 	stmfd	[sp]!,{r4-r7,lr}

000002a4 e24dd014    501 	sub	sp,sp,20

000002a8 e1a07000    502 	mov	r7,r0

000002ac e1a04002    503 	mov	r4,r2

000002b0 e1a05003    504 	mov	r5,r3

                     505 ;202:     FSFileAttr* attr)


                     506 ;203: {


                     507 

                     508 ;204:     StringView dirName;


                     509 ;205:     StringView fileName;


                     510 ;206:     FRSM* frsm;


                     511 ;207: 


                     512 ;208:     splitFullFileName(fullFileName, &dirName, &fileName);


                     513 

                     514 ;50:     StringView* dirName, StringView* fileName)


                     515 ;51: {


                     516 

                     517 ;52:     size_t delimiterPos;


                     518 ;53:     if (findDelimiter(fullName, &delimiterPos))


                     519 

                     520 ;23: {


                     521 

                     522 ;24:     size_t pos = fullName->len;


                     523 

000002b4 e8970003    524 	ldmfd	[r7],{r0-r1}

000002b8 e1a03000    525 	mov	r3,r0

000002bc e1b06003    526 	movs	r6,r3

                     527 ;25:     while (pos > 0)


                     528 

000002c0 e0860001    529 	add	r0,r6,r1

                     530 ;29:         {


                     531 

                     532 ;30:             *delimiterPos = pos;


                     533 

                     534 ;31:             return TRUE;


                     535 

000002c4 0a000017    536 	beq	.L752

                     537 .L743:

                     538 ;26:     {



                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_crg1.s
                     539 

                     540 ;27:         --pos;


                     541 

000002c8 e5702001    542 	ldrb	r2,[r0,-1]!

                     543 ;16: {


                     544 

                     545 ;17:     return c == '\\' || c == '/';


                     546 

000002cc e2466001    547 	sub	r6,r6,1

                     548 ;28:         if (isDelimiter(fullName->p[pos]))


                     549 

000002d0 e352005c    550 	cmp	r2,92

000002d4 1352002f    551 	cmpne	r2,47

000002d8 0a000002    552 	beq	.L751

                     553 ;29:         {


                     554 

                     555 ;30:             *delimiterPos = pos;


                     556 

                     557 ;31:             return TRUE;


                     558 

000002dc e3560000    559 	cmp	r6,0

000002e0 1afffff8    560 	bne	.L743

000002e4 ea00000f    561 	b	.L752

                     562 .L751:

                     563 ;32:         }


                     564 ;33:     }


                     565 ;34:     return FALSE;


                     566 

                     567 ;54:     {


                     568 

                     569 ;55:         //Разделитель найден


                     570 ;56:         //Имя файла


                     571 ;57:         size_t fileNameStart = delimiterPos + 1;


                     572 

000002e8 e2860001    573 	add	r0,r6,1

                     574 ;58:         StringView_init(fileName, fullName->p + fileNameStart,


                     575 

000002ec e0432000    576 	sub	r2,r3,r0

000002f0 e0801001    577 	add	r1,r0,r1

000002f4 e28d0004    578 	add	r0,sp,4

000002f8 eb000000*   579 	bl	StringView_init

                     580 ;59:             fullName->len - fileNameStart);


                     581 ;60:         //Директорий


                     582 ;61:         StringView_init(dirName, fullName->p, delimiterPos);


                     583 

000002fc e1a02006    584 	mov	r2,r6

00000300 e5971004    585 	ldr	r1,[r7,4]

00000304 e28d000c    586 	add	r0,sp,12

00000308 eb000000*   587 	bl	StringView_init

                     588 ;62:         //Срезаем лидирующий разделитель, если есть


                     589 ;63:         stripLeftDelim(dirName);


                     590 

0000030c e28d000c    591 	add	r0,sp,12

00000310 ebffff3a*   592 	bl	stripLeftDelim

00000314 e1a00004    593 	mov	r0,r4

00000318 eb000000*   594 	bl	frsm_alloc

                     595 ;209:     if( ! frsm_alloc(frsmID))


                     596 

0000031c e3500000    597 	cmp	r0,0

00000320 0a00000a    598 	beq	.L755

00000324 ea00000a    599 	b	.L754


                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_crg1.s
                     600 .L752:

                     601 ;64:     }


                     602 ;65:     else


                     603 ;66:     {


                     604 

                     605 ;67:         //Разделитель не найден.


                     606 ;68:         //Только имя файла. Директорий пустой.


                     607 ;69:         StringView_init(dirName, fullName->p, 0);


                     608 

00000328 e28d000c    609 	add	r0,sp,12

0000032c e3a02000    610 	mov	r2,0

00000330 eb000000*   611 	bl	StringView_init

                     612 ;70:         StringView_init(fileName, fullName->p, fullName->len);


                     613 

00000334 e8970003    614 	ldmfd	[r7],{r0-r1}

00000338 e1a02000    615 	mov	r2,r0

0000033c e28d0004    616 	add	r0,sp,4

00000340 eb000000*   617 	bl	StringView_init

00000344 e1a00004    618 	mov	r0,r4

00000348 eb000000*   619 	bl	frsm_alloc

                     620 ;209:     if( ! frsm_alloc(frsmID))


                     621 

0000034c e3500000    622 	cmp	r0,0

                     623 .L755:

                     624 ;210:     {


                     625 

                     626 ;211:         return FALSE;


                     627 

00000350 0a000021    628 	beq	.L738

                     629 .L754:

                     630 ;212:     }


                     631 ;213:     frsm_getById(*frsmID, &frsm);


                     632 

00000354 e5940000    633 	ldr	r0,[r4]

00000358 e1a0100d    634 	mov	r1,sp

0000035c eb000000*   635 	bl	frsm_getById

00000360 e59f113c*   636 	ldr	r1,.L1061

00000364 e28d000c    637 	add	r0,sp,12

00000368 eb000000*   638 	bl	StringView_cmpCStr

                     639 ;214:     if(!fileOpen(&dirName, &fileName, frsm, attr))


                     640 

                     641 ;186:                        FSFileAttr* attr)


                     642 ;187: {


                     643 

                     644 ;188:         if(StringView_cmpCStr(dirName, CFG_DIR_NAME) == 0 )


                     645 

0000036c e3500000    646 	cmp	r0,0

00000370 1a000007    647 	bne	.L763

                     648 ;189:         {


                     649 

                     650 ;190:             frsm->subsystem = FS_SUB_CFG;


                     651 

00000374 e59d1000    652 	ldr	r1,[sp]

00000378 e1a02005    653 	mov	r2,r5

0000037c e5810004    654 	str	r0,[r1,4]

                     655 ;191:             return CFGFS_openFile(fileName, frsm, attr);


                     656 

00000380 e28d0004    657 	add	r0,sp,4

00000384 eb000000*   658 	bl	CFGFS_openFile

00000388 e3500000    659 	cmp	r0,0

0000038c 0a00000d    660 	beq	.L758


                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_crg1.s
00000390 ea000010    661 	b	.L757

                     662 .L763:

                     663 ;192:         }


                     664 ;193:         else if(StringView_cmpCStr(dirName, OSC_DIR_NAME) == 0 )


                     665 

00000394 e59f110c*   666 	ldr	r1,.L1062

00000398 e28d000c    667 	add	r0,sp,12

0000039c eb000000*   668 	bl	StringView_cmpCStr

000003a0 e3500000    669 	cmp	r0,0

                     670 ;197:         }


                     671 ;198:         return FALSE;


                     672 

000003a4 1a000007    673 	bne	.L758

                     674 ;194:         {


                     675 

                     676 ;195:             frsm->subsystem = FS_SUB_OSC;


                     677 

000003a8 e1a02005    678 	mov	r2,r5

000003ac e59d1000    679 	ldr	r1,[sp]

000003b0 e3a00001    680 	mov	r0,1

000003b4 e5810004    681 	str	r0,[r1,4]

                     682 ;196:             return OSCFS_openFile(fileName, frsm, attr);


                     683 

000003b8 e28d0004    684 	add	r0,sp,4

000003bc eb000000*   685 	bl	OSCFS_openFile

000003c0 e3500000    686 	cmp	r0,0

000003c4 1a000003    687 	bne	.L757

                     688 .L758:

                     689 ;215:     {


                     690 

                     691 ;216:         frsm_free(*frsmID);


                     692 

000003c8 e5940000    693 	ldr	r0,[r4]

000003cc eb000000*   694 	bl	frsm_free

                     695 ;217:         return FALSE;


                     696 

000003d0 e3a00000    697 	mov	r0,0

000003d4 ea000000    698 	b	.L738

                     699 .L757:

                     700 ;218:     }


                     701 ;219:     return TRUE;


                     702 

000003d8 e3a00001    703 	mov	r0,1

                     704 .L738:

000003dc e28dd014    705 	add	sp,sp,20

000003e0 e8bd80f0    706 	ldmfd	[sp]!,{r4-r7,pc}

                     707 	.endf	fs_fileOpen

                     708 	.align	4

                     709 ;dirName	[sp,12]	local

                     710 ;fileName	[sp,4]	local

                     711 ;frsm	[sp]	local

                     712 ;pos	r6	local

                     713 ;c	r2	local

                     714 ;fileNameStart	r0	local

                     715 

                     716 ;fullFileName	r7	param

                     717 ;startPos	none	param

                     718 ;frsmID	r4	param

                     719 ;attr	r5	param

                     720 

                     721 	.section ".bss","awb"


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_crg1.s
                     722 .L1014:

                     723 	.section ".rodata","a"

                     724 .L1015:

                     725 __UNNAMED_2_static_in_fileOpen:;	"osc\000"

00000000 0063736f    726 	.data.b	111,115,99,0

                     727 	.type	__UNNAMED_2_static_in_fileOpen,$object

                     728 	.size	__UNNAMED_2_static_in_fileOpen,4

                     729 .L1016:

                     730 __UNNAMED_1_static_in_fileOpen:;	"config\000"

00000004 666e6f63    731 	.data.b	99,111,110,102

00000008 6769       732 	.data.b	105,103

0000000a 00         733 	.data.b	0

0000000b 00         734 	.space	1

                     735 	.type	__UNNAMED_1_static_in_fileOpen,$object

                     736 	.size	__UNNAMED_1_static_in_fileOpen,8

                     737 	.data

                     738 	.text

                     739 

                     740 ;220: }


                     741 

                     742 ;221: 


                     743 ;222: bool fs_fileClose(uint32_t frsmID)


                     744 	.align	4

                     745 	.align	4

                     746 fs_fileClose::

000003e4 e92d4030    747 	stmfd	[sp]!,{r4-r5,lr}

000003e8 e24dd004    748 	sub	sp,sp,4

000003ec e1a0100d    749 	mov	r1,sp

000003f0 e1a05000    750 	mov	r5,r0

000003f4 eb000000*   751 	bl	frsm_getById

                     752 ;223: {


                     753 

                     754 ;224:     FRSM* frsm;


                     755 ;225:     bool result;


                     756 ;226:     if (!frsm_getById(frsmID, &frsm))


                     757 

000003f8 e3500000    758 	cmp	r0,0

                     759 ;227:     {


                     760 

                     761 ;228:         return FALSE;


                     762 

000003fc 0a000010    763 	beq	.L1063

                     764 ;229:     }


                     765 ;230:     switch (frsm->subsystem)


                     766 

00000400 e59d0000    767 	ldr	r0,[sp]

00000404 e5901004    768 	ldr	r1,[r0,4]

00000408 e3510001    769 	cmp	r1,1

0000040c 3a000002    770 	blo	.L1070

                     771 ;237:         break;


                     772 ;238:     default:


                     773 ;239:         result = FALSE;


                     774 

00000410 13a00000    775 	movne	r0,0

                     776 ;244:     }


                     777 ;245:     return result;


                     778 

00000414 1a00000a    779 	bne	.L1063

00000418 ea000003    780 	b	.L1071

                     781 .L1070:

                     782 ;231:     {



                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_crg1.s
                     783 ;232:     case FS_SUB_CFG:


                     784 ;233:         result = CFGFS_closeFile(frsm);


                     785 

0000041c eb000000*   786 	bl	CFGFS_closeFile

00000420 e1b04000    787 	movs	r4,r0

                     788 ;240:     }


                     789 ;241:     if (result)


                     790 

00000424 0a000005    791 	beq	.L1073

00000428 ea000002    792 	b	.L1074

                     793 .L1071:

                     794 ;234:         break;


                     795 ;235:     case FS_SUB_OSC:


                     796 ;236:         result = OSCFS_closeFile(frsm);


                     797 

0000042c eb000000*   798 	bl	OSCFS_closeFile

00000430 e1b04000    799 	movs	r4,r0

                     800 ;240:     }


                     801 ;241:     if (result)


                     802 

00000434 0a000001    803 	beq	.L1073

                     804 .L1074:

                     805 ;242:     {


                     806 

                     807 ;243:         frsm_free(frsmID);


                     808 

00000438 e1a00005    809 	mov	r0,r5

0000043c eb000000*   810 	bl	frsm_free

                     811 .L1073:

                     812 ;244:     }


                     813 ;245:     return result;


                     814 

00000440 e1a00004    815 	mov	r0,r4

                     816 .L1063:

00000444 e28dd004    817 	add	sp,sp,4

00000448 e8bd8030    818 	ldmfd	[sp]!,{r4-r5,pc}

                     819 	.endf	fs_fileClose

                     820 	.align	4

                     821 ;frsm	[sp]	local

                     822 ;result	r4	local

                     823 

                     824 ;frsmID	r5	param

                     825 

                     826 	.section ".bss","awb"

                     827 .L1191:

                     828 	.data

                     829 	.text

                     830 

                     831 ;246: }


                     832 

                     833 ;247: 


                     834 ;248: bool fs_fileRead(uint32_t frsmID, BufferView* fileReadBuf, bool *moreFollows)


                     835 	.align	4

                     836 	.align	4

                     837 fs_fileRead::

0000044c e92d4030    838 	stmfd	[sp]!,{r4-r5,lr}

00000450 e1a05002    839 	mov	r5,r2

00000454 e24dd004    840 	sub	sp,sp,4

00000458 e1a04001    841 	mov	r4,r1

0000045c e1a0100d    842 	mov	r1,sp

00000460 eb000000*   843 	bl	frsm_getById


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_crg1.s
                     844 ;249: {


                     845 

                     846 ;250:     FRSM* frsm;


                     847 ;251:     bool result;


                     848 ;252:     if (!frsm_getById(frsmID, &frsm))


                     849 

00000464 e3500000    850 	cmp	r0,0

                     851 ;253:     {


                     852 

                     853 ;254:         return FALSE;


                     854 

00000468 0a00000b    855 	beq	.L1219

                     856 ;255:     }


                     857 ;256:     switch (frsm->subsystem)


                     858 

0000046c e59d0000    859 	ldr	r0,[sp]

00000470 e5901004    860 	ldr	r1,[r0,4]

00000474 e3510001    861 	cmp	r1,1

00000478 3a000004    862 	blo	.L1226

                     863 ;263:         break;


                     864 ;264:     default:


                     865 ;265:         result = FALSE;


                     866 

0000047c 13a00000    867 	movne	r0,0

                     868 ;266:     }


                     869 ;267:     return result;


                     870 

                     871 ;260:         break;


                     872 ;261:     case FS_SUB_OSC:


                     873 ;262:         result = OSCFS_readFile(frsm, fileReadBuf, moreFollows);


                     874 

00000480 01a02005    875 	moveq	r2,r5

00000484 01a01004    876 	moveq	r1,r4

00000488 0b000000*   877 	bleq	OSCFS_readFile

                     878 ;266:     }


                     879 ;267:     return result;


                     880 

0000048c ea000002    881 	b	.L1219

                     882 .L1226:

                     883 ;257:     {


                     884 ;258:     case FS_SUB_CFG:


                     885 ;259:         result = CFGFS_readFile(frsm, fileReadBuf, moreFollows);


                     886 

00000490 e1a02005    887 	mov	r2,r5

00000494 e1a01004    888 	mov	r1,r4

00000498 eb000000*   889 	bl	CFGFS_readFile

                     890 ;266:     }


                     891 ;267:     return result;


                     892 

                     893 .L1219:

0000049c e28dd004    894 	add	sp,sp,4

000004a0 e8bd8030    895 	ldmfd	[sp]!,{r4-r5,pc}

                     896 	.endf	fs_fileRead

                     897 	.align	4

                     898 ;frsm	[sp]	local

                     899 

                     900 ;frsmID	none	param

                     901 ;fileReadBuf	r4	param

                     902 ;moreFollows	r5	param

                     903 

                     904 	.section ".bss","awb"


                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_crg1.s
                     905 .L1306:

                     906 	.data

                     907 	.text

                     908 

                     909 ;268: }


                     910 	.align	4

                     911 .L1061:

000004a4 00000000*   912 	.data.w	.L1016

                     913 	.type	.L1061,$object

                     914 	.size	.L1061,4

                     915 

                     916 .L1062:

000004a8 00000000*   917 	.data.w	.L1015

                     918 	.type	.L1062,$object

                     919 	.size	.L1062,4

                     920 

                     921 	.align	4

                     922 ;__UNNAMED_1_static_in_fileOpen	.L1016	static

                     923 ;__UNNAMED_2_static_in_fileOpen	.L1015	static

                     924 

                     925 	.data

                     926 	.ghsnote version,6

                     927 	.ghsnote tools,3

                     928 	.ghsnote options,0

                     929 	.text

                     930 	.align	4

                     931 	.section ".rodata","a"

                     932 	.align	4

                     933 	.text

