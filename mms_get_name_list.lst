                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bhg1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=mms_get_name_list.c -o gh_bhg1.o -list=mms_get_name_list.lst C:\Users\<USER>\AppData\Local\Temp\gh_bhg1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_bhg1.s
Source File: mms_get_name_list.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      10 ;		mms_get_name_list.c -o mms_get_name_list.o

                      11 ;Source File:   mms_get_name_list.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:56:01 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "mms_get_name_list.h"


                      21 ;2: 


                      22 ;3: #include "AsnEncoding.h"


                      23 ;4: #include "MmsConst.h"


                      24 ;5: #include "mms_error.h"


                      25 ;6: #include "mmsconnection.h"


                      26 ;7: #include "iedmodel.h"


                      27 ;8: #include <debug.h>


                      28 ;9: 


                      29 ;10: #include <types.h>


                      30 ;11: #include <stddef.h>


                      31 ;12: 


                      32 ;13: 


                      33 ;14: #define TAG_CONTINUE_AFTER 0x82


                      34 ;15: 


                      35 ;16: 


                      36 ;17: #define OBJECT_SCOPE_VMD					0


                      37 ;18: #define OBJECT_SCOPE_DOMAIN					1


                      38 ;19: #define OBJECT_SCOPE_ASSOCIATION			2


                      39 ;20: 


                      40 ;21: #define OBJECT_CLASS_NAMED_VARIABLE			0


                      41 ;22: #define OBJECT_CLASS_NAMED_VARIABLE_LIST	2


                      42 ;23: #define OBJECT_CLASS_JOURNAL				8


                      43 ;24: #define OBJECT_CLASS_DOMAIN					9


                      44 ;25: 


                      45 ;26: // 5 - максимуальная шапка списка (тэг + размер)


                      46 ;27: // 3 - "more follows"


                      47 ;28: // 6 - Invoke ID


                      48 ;29: // 5 - тэг и размер всего ответа


                      49 ;30: #define MAX_NAME_LIST_HEADER (5 + 3 + 6 + 5)


                      50 ;31: 



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bhg1.s
                      51 ;32: /*


                      52 ;33: int mms_createEmptyNameListResponse( unsigned int invokeId, unsigned char* response)


                      53 ;34: {


                      54 ;35:     int bufPos = 0;


                      55 ;36:     unsigned int identifierListSize;


                      56 ;37:     unsigned int  invokeIdSize;


                      57 ;38:     unsigned int listOfIdentifierSize;


                      58 ;39:     unsigned int getNameListSize;


                      59 ;40:     unsigned int confirmedServiceResponseSize;


                      60 ;41:     unsigned int confirmedResponsePDUSize;


                      61 ;42:     int moreFollows;


                      62 ;43: 


                      63 ;44: 


                      64 ;45:     //debugSendText("mms_createNameListResponse");


                      65 ;46: 


                      66 ;47:     //============== Определяем размеры =================


                      67 ;48:     //Размер списка идентификаторов


                      68 ;49:     //Настраиваем DomainNameWriter на определение размера


                      69 ;50: 


                      70 ;51:     identifierListSize = 0;


                      71 ;52: 


                      72 ;53:     moreFollows = FALSE;


                      73 ;54: 


                      74 ;55:     //Размер ответа на список имён


                      75 ;56:     listOfIdentifierSize = 1 + BerEncoder_determineLengthSize(identifierListSize)


                      76 ;57:             + identifierListSize;


                      77 ;58: 


                      78 ;59: 


                      79 ;60:     getNameListSize = listOfIdentifierSize;


                      80 ;61:     if (moreFollows == FALSE)


                      81 ;62:     {


                      82 ;63:         //3 байта


                      83 ;64:         getNameListSize += 3;


                      84 ;65:     }


                      85 ;66: 


                      86 ;67:     //Размер Invoke ID


                      87 ;68:     invokeIdSize = BerEncoder_UInt32determineEncodedSize(invokeId) + 2;


                      88 ;69: 


                      89 ;70:     //Размер всего ответа


                      90 ;71:     confirmedServiceResponseSize = 1 + BerEncoder_determineLengthSize(getNameListSize)


                      91 ;72:             + getNameListSize;


                      92 ;73:     confirmedResponsePDUSize = confirmedServiceResponseSize + invokeIdSize;


                      93 ;74: 


                      94 ;75:     //=================== Кодируем ответ ==================


                      95 ;76: 


                      96 ;77:     bufPos = BerEncoder_encodeTL(0xa1, confirmedResponsePDUSize, response, bufPos);


                      97 ;78: 


                      98 ;79:     bufPos = BerEncoder_encodeTL(ASN_INTEGER, invokeIdSize - 2, response, bufPos);


                      99 ;80:     bufPos = BerEncoder_encodeUInt32(invokeId, response, bufPos);


                     100 ;81: 


                     101 ;82:     bufPos = BerEncoder_encodeTL(0xa1, getNameListSize, response, bufPos);


                     102 ;83:     bufPos = BerEncoder_encodeTL(0xa0, identifierListSize, response, bufPos);


                     103 ;84: 


                     104 ;85: 


                     105 ;86:     identifierListSize =  0;


                     106 ;87:     bufPos += identifierListSize;


                     107 ;88: 


                     108 ;89:     if (!moreFollows)


                     109 ;90:     {        


                     110 ;91:         bufPos = BerEncoder_encodeBoolean(0x81, moreFollows, response, bufPos);


                     111 ;92:     }



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bhg1.s
                     112 ;93:     return bufPos;


                     113 ;94: }


                     114 ;95: */


                     115 ;96: 


                     116 ;97: int mms_createNameListResponse(MmsConnection* mmsConn,


                     117 	.text

                     118 	.align	4

                     119 mms_createNameListResponse::

00000000 e92d4ff8    120 	stmfd	[sp]!,{r3-fp,lr}

                     121 ;98:         unsigned int invokeId, unsigned char* response, int rootObjPos,


                     122 ;99:                                uint8_t* continueAfter, int continueAfterLen,


                     123 ;100:                                bool recursive, int objectsTagTowrite)


                     124 ;101: {


                     125 

                     126 ;102:     int bufPos = 0;


                     127 

                     128 ;103:     unsigned int identifierListSize;


                     129 ;104:     unsigned int  invokeIdSize;


                     130 ;105:     unsigned int listOfIdentifierSize;


                     131 ;106:     unsigned int getNameListSize;


                     132 ;107:     unsigned int confirmedServiceResponseSize;


                     133 ;108:     unsigned int confirmedResponsePDUSize;    


                     134 ;109:     int moreFollows;


                     135 ;110:     DomainNameWriter* nameWriter = &mmsConn->nameWriter;


                     136 

                     137 ;111: 


                     138 ;112:     //debugSendText("mms_createNameListResponse");


                     139 ;113: 


                     140 ;114:     //============== Определяем размеры =================


                     141 ;115:     //Размер списка идентификаторов


                     142 ;116:     //Настраиваем DomainNameWriter на определение размера


                     143 ;117:     DomainNameWriter_init(nameWriter,NULL,


                     144 

00000004 e24dd008    145 	sub	sp,sp,8

00000008 e58d3008    146 	str	r3,[sp,8]

0000000c e59d7030    147 	ldr	r7,[sp,48]

00000010 e1a05002    148 	mov	r5,r2

00000014 e3a02ef0    149 	mov	r2,15<<8

00000018 e28220ed    150 	add	r2,r2,237

0000001c e1a04000    151 	mov	r4,r0

00000020 e1a0b001    152 	mov	fp,r1

00000024 e3a01000    153 	mov	r1,0

00000028 eb000000*   154 	bl	DomainNameWriter_init

                     155 ;118:                           MAX_MMS_RESPONSE_SIZE - MAX_NAME_LIST_HEADER);


                     156 ;119:     if(continueAfter != NULL)


                     157 

0000002c e3570000    158 	cmp	r7,0

                     159 ;120:     {


                     160 

                     161 ;121:         DomainNameWriter_setStartName(nameWriter,continueAfter, continueAfterLen);


                     162 

00000030 159d2034    163 	ldrne	r2,[sp,52]

00000034 11a01007    164 	movne	r1,r7

00000038 11a00004    165 	movne	r0,r4

0000003c 1b000000*   166 	blne	DomainNameWriter_setStartName

                     167 ;122:     }


                     168 ;123: 


                     169 ;124:     writeChildrenNames(rootObjPos, nameWriter, recursive, objectsTagTowrite);


                     170 

00000040 e59d303c    171 	ldr	r3,[sp,60]

00000044 e5dd2038    172 	ldrb	r2,[sp,56]


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bhg1.s
00000048 e59d0008    173 	ldr	r0,[sp,8]

0000004c e1a01004    174 	mov	r1,r4

00000050 eb000000*   175 	bl	writeChildrenNames

                     176 ;125:     identifierListSize = nameWriter->totalSize;


                     177 

00000054 e5946004    178 	ldr	r6,[r4,4]

                     179 ;126: 


                     180 ;127:     moreFollows = nameWriter->bufferFull;    


                     181 

00000058 e5d49008    182 	ldrb	r9,[r4,8]

                     183 ;128: 


                     184 ;129:     //Размер ответа на список имён


                     185 ;130:     listOfIdentifierSize = 1 + BerEncoder_determineLengthSize(identifierListSize)


                     186 

0000005c e1a00006    187 	mov	r0,r6

00000060 eb000000*   188 	bl	BerEncoder_determineLengthSize

00000064 e0800006    189 	add	r0,r0,r6

00000068 e280a004    190 	add	r10,r0,4

                     191 ;131:             + identifierListSize;


                     192 ;132: 


                     193 ;133: 


                     194 ;134:     getNameListSize = listOfIdentifierSize;


                     195 

                     196 ;135:     


                     197 ;136:     


                     198 ;137:     //размер moreFollows


                     199 ;138:     getNameListSize += 3;


                     200 

                     201 ;139:     


                     202 ;140: 


                     203 ;141:     //Размер Invoke ID


                     204 ;142:     invokeIdSize = BerEncoder_UInt32determineEncodedSize(invokeId) + 2;


                     205 

0000006c e1a0000b    206 	mov	r0,fp

00000070 eb000000*   207 	bl	BerEncoder_UInt32determineEncodedSize

00000074 e2808002    208 	add	r8,r0,2

                     209 ;143: 


                     210 ;144:     //Размер всего ответа


                     211 ;145:     confirmedServiceResponseSize = 1 + BerEncoder_determineLengthSize(getNameListSize)


                     212 

00000078 e1a0000a    213 	mov	r0,r10

0000007c eb000000*   214 	bl	BerEncoder_determineLengthSize

00000080 e080100a    215 	add	r1,r0,r10

00000084 e0811008    216 	add	r1,r1,r8

                     217 ;146:             + getNameListSize;


                     218 ;147:     confirmedResponsePDUSize = confirmedServiceResponseSize + invokeIdSize;


                     219 

00000088 e2811001    220 	add	r1,r1,1

                     221 ;148: 


                     222 ;149:     //=================== Кодируем ответ ==================


                     223 ;150: 


                     224 ;151:     bufPos = BerEncoder_encodeTL(0xa1, confirmedResponsePDUSize, response, bufPos);


                     225 

0000008c e1a02005    226 	mov	r2,r5

00000090 e3a03000    227 	mov	r3,0

00000094 e3a000a1    228 	mov	r0,161

00000098 eb000000*   229 	bl	BerEncoder_encodeTL

                     230 ;152: 


                     231 ;153:     bufPos = BerEncoder_encodeTL(ASN_INTEGER, invokeIdSize - 2, response, bufPos);


                     232 

0000009c e1a02005    233 	mov	r2,r5


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bhg1.s
000000a0 e2481002    234 	sub	r1,r8,2

000000a4 e1a03000    235 	mov	r3,r0

000000a8 e3a00002    236 	mov	r0,2

000000ac eb000000*   237 	bl	BerEncoder_encodeTL

                     238 ;154:     bufPos = BerEncoder_encodeUInt32(invokeId, response, bufPos);


                     239 

000000b0 e1a01005    240 	mov	r1,r5

000000b4 e1a02000    241 	mov	r2,r0

000000b8 e1a0000b    242 	mov	r0,fp

000000bc eb000000*   243 	bl	BerEncoder_encodeUInt32

                     244 ;155: 


                     245 ;156:     bufPos = BerEncoder_encodeTL(0xa1, getNameListSize, response, bufPos);


                     246 

000000c0 e1a02005    247 	mov	r2,r5

000000c4 e1a0100a    248 	mov	r1,r10

000000c8 e1a03000    249 	mov	r3,r0

000000cc e3a000a1    250 	mov	r0,161

000000d0 eb000000*   251 	bl	BerEncoder_encodeTL

                     252 ;157:     bufPos = BerEncoder_encodeTL(0xa0, identifierListSize, response, bufPos);


                     253 

000000d4 e1a02005    254 	mov	r2,r5

000000d8 e1a01006    255 	mov	r1,r6

000000dc e1a03000    256 	mov	r3,r0

000000e0 e3a000a0    257 	mov	r0,160

000000e4 eb000000*   258 	bl	BerEncoder_encodeTL

000000e8 e3a02ef0    259 	mov	r2,15<<8

000000ec e28220ed    260 	add	r2,r2,237

000000f0 e1a0a000    261 	mov	r10,r0

                     262 ;158: 


                     263 ;159:     //Настраиваем DomainNameWriter на фактическую запись


                     264 ;160:     DomainNameWriter_init(nameWriter, response + bufPos,


                     265 

000000f4 e08a1005    266 	add	r1,r10,r5

000000f8 e1a00004    267 	mov	r0,r4

000000fc eb000000*   268 	bl	DomainNameWriter_init

                     269 ;161:                           MAX_MMS_RESPONSE_SIZE - MAX_NAME_LIST_HEADER);


                     270 ;162:     if(continueAfter != NULL)


                     271 

00000100 e3570000    272 	cmp	r7,0

                     273 ;163:     {


                     274 

                     275 ;164:         DomainNameWriter_setStartName(nameWriter,continueAfter, continueAfterLen);


                     276 

00000104 159d2034    277 	ldrne	r2,[sp,52]

00000108 11a01007    278 	movne	r1,r7

0000010c 11a00004    279 	movne	r0,r4

00000110 1b000000*   280 	blne	DomainNameWriter_setStartName

                     281 ;165:     }


                     282 ;166: 


                     283 ;167:     writeChildrenNames(rootObjPos, nameWriter, recursive, objectsTagTowrite);


                     284 

00000114 e59d303c    285 	ldr	r3,[sp,60]

00000118 e5dd2038    286 	ldrb	r2,[sp,56]

0000011c e59d0008    287 	ldr	r0,[sp,8]

00000120 e1a01004    288 	mov	r1,r4

00000124 eb000000*   289 	bl	writeChildrenNames

                     290 ;168: 


                     291 ;169:     identifierListSize =  nameWriter->totalSize;


                     292 

00000128 e1a02005    293 	mov	r2,r5

0000012c e5940004    294 	ldr	r0,[r4,4]


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bhg1.s
                     295 ;170:     //debugSendUshort("nameWriter returned:", identifierListSize);


                     296 ;171: 


                     297 ;172:     bufPos += identifierListSize;


                     298 

00000130 e1a01009    299 	mov	r1,r9

00000134 e08a3000    300 	add	r3,r10,r0

                     301 ;173:     bufPos = BerEncoder_encodeBoolean(0x81, moreFollows, response, bufPos);


                     302 

00000138 e3a00081    303 	mov	r0,129

0000013c eb000000*   304 	bl	BerEncoder_encodeBoolean

                     305 ;174: 


                     306 ;175:     return bufPos;


                     307 

00000140 e28dd008    308 	add	sp,sp,8

00000144 e8bd8ff8    309 	ldmfd	[sp]!,{r3-fp,pc}

                     310 	.endf	mms_createNameListResponse

                     311 	.align	4

                     312 ;bufPos	r10	local

                     313 ;identifierListSize	r6	local

                     314 ;invokeIdSize	r8	local

                     315 ;listOfIdentifierSize	r0	local

                     316 ;getNameListSize	r10	local

                     317 ;confirmedServiceResponseSize	r1	local

                     318 ;moreFollows	r9	local

                     319 ;nameWriter	r4	local

                     320 

                     321 ;mmsConn	r0	param

                     322 ;invokeId	fp	param

                     323 ;response	r5	param

                     324 ;rootObjPos	[sp,8]	param

                     325 ;continueAfter	r7	param

                     326 ;continueAfterLen	[sp,52]	param

                     327 ;recursive	[sp,56]	param

                     328 ;objectsTagTowrite	[sp,60]	param

                     329 

                     330 	.section ".bss","awb"

                     331 .L64:

                     332 	.data

                     333 	.text

                     334 

                     335 ;176: }


                     336 

                     337 ;177: 


                     338 ;178: int mms_handleGetNameListRequest(MmsConnection* mmsConn,


                     339 	.align	4

                     340 	.align	4

                     341 mms_handleGetNameListRequest::

00000148 e92d4ff0    342 	stmfd	[sp]!,{r4-fp,lr}

                     343 ;179:                                  unsigned char* inBuf, int bufPos, int maxBufPos,


                     344 ;180:                                   unsigned int invokeId, unsigned char* response)


                     345 ;181: {


                     346 

0000014c e1a04002    347 	mov	r4,r2

00000150 e1a06003    348 	mov	r6,r3

00000154 e24dd02c    349 	sub	sp,sp,44

00000158 e59da054    350 	ldr	r10,[sp,84]

0000015c e3e0b000    351 	mvn	fp,0

00000160 e58d0028    352 	str	r0,[sp,40]

00000164 e3a00000    353 	mov	r0,0

00000168 e1a0c000    354 	mov	r12,r0

0000016c e1a0e000    355 	mov	lr,r0


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bhg1.s
00000170 e1a05001    356 	mov	r5,r1

00000174 e28d1014    357 	add	r1,sp,20

00000178 e8815801    358 	stmea	[r1],{r0,fp-r12,lr}

                     359 ;182:     int responseSize = 0;


                     360 

                     361 ;183:     int rootObj;


                     362 ;184:     int objectClass = -1;


                     363 

                     364 ;185: 


                     365 ;186:     int objectScope = -1;


                     366 

                     367 ;187: 


                     368 ;188:     uint8_t* domainId = NULL;


                     369 

                     370 ;189:     int domainIdLength;


                     371 ;190: 


                     372 ;191:     uint8_t* continueAfter = NULL;


                     373 

                     374 ;192:     int continueAfterLength = 0;


                     375 

                     376 ;193: 


                     377 ;194:     //debugSendDump("\tdata:", inBuf + bufPos, maxBufPos - bufPos);


                     378 ;195: 


                     379 ;196:     while (bufPos < maxBufPos) {


                     380 

0000017c e1540006    381 	cmp	r4,r6

00000180 aa00004d    382 	bge	.L84

                     383 .L85:

                     384 ;197:         unsigned char tag = inBuf[bufPos++];


                     385 

00000184 e7d57004    386 	ldrb	r7,[r5,r4]

00000188 e2842001    387 	add	r2,r4,1

                     388 ;198:         int length;


                     389 ;199: 


                     390 ;200:         bufPos = BerDecoder_decodeLength(inBuf, &length, bufPos, maxBufPos);


                     391 

0000018c e1a03006    392 	mov	r3,r6

00000190 e28d1010    393 	add	r1,sp,16

00000194 e1a00005    394 	mov	r0,r5

00000198 eb000000*   395 	bl	BerDecoder_decodeLength

0000019c e1b04000    396 	movs	r4,r0

                     397 ;201: 


                     398 ;202:         if (bufPos < 0)  {


                     399 

000001a0 4a000074    400 	bmi	.L120

                     401 ;203:             //mmsMsg_createMmsRejectPdu(&invokeId, MMS_ERROR_REJECT_INVALID_PDU, response);			


                     402 ;204:             return 0;


                     403 

                     404 ;205:         }


                     405 ;206: 


                     406 ;207:         //debugSendUshort("Name List request tag:", tag);


                     407 ;208: 


                     408 ;209: 


                     409 ;210:         switch (tag) {


                     410 

000001a4 e2571082    411 	subs	r1,r7,130

000001a8 0a00003c    412 	beq	.L101

000001ac e251101e    413 	subs	r1,r1,30

000001b0 0a000002    414 	beq	.L91

000001b4 e3510001    415 	cmp	r1,1

000001b8 0a00000d    416 	beq	.L92


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bhg1.s
000001bc ea00006d    417 	b	.L120

                     418 .L91:

                     419 ;211:         case 0xa0: // objectClass


                     420 ;212:             bufPos++;


                     421 

000001c0 e2844001    422 	add	r4,r4,1

                     423 ;213:             length = inBuf[bufPos++];


                     424 

000001c4 e7d51004    425 	ldrb	r1,[r5,r4]

000001c8 e2844001    426 	add	r4,r4,1

000001cc e58d1010    427 	str	r1,[sp,16]

                     428 ;214:             objectClass = BerDecoder_decodeUint32(inBuf, length, bufPos);            


                     429 

000001d0 e1a02004    430 	mov	r2,r4

000001d4 e1a00005    431 	mov	r0,r5

000001d8 eb000000*   432 	bl	BerDecoder_decodeUint32

000001dc e59d1010    433 	ldr	r1,[sp,16]

                     434 ;250:             break;


                     435 ;251:         default:        


                     436 ;252:             return 0;


                     437 

                     438 ;253:             /*


                     439 ;254:             return CreateMmsConfirmedErrorPdu( invokeId, response,


                     440 ;255:                                                           MMS_ERROR_ACCESS_OBJECT_ACCESS_UNSUPPORTED );


                     441 ;256:                                                           */


                     442 ;257:         }


                     443 ;258: 


                     444 ;259:         bufPos += length;


                     445 

000001e0 e58d0018    446 	str	r0,[sp,24]

000001e4 e0844001    447 	add	r4,r4,r1

000001e8 e1540006    448 	cmp	r4,r6

000001ec baffffe4    449 	blt	.L85

000001f0 ea000031    450 	b	.L84

                     451 .L92:

                     452 ;215:             break;


                     453 ;216: 


                     454 ;217:         case 0xa1: // objectScope


                     455 ;218:             {


                     456 

                     457 ;219: 


                     458 ;220:                 unsigned char objectScopeTag = inBuf[bufPos++];


                     459 

000001f4 e7d5b004    460 	ldrb	fp,[r5,r4]

000001f8 e2842001    461 	add	r2,r4,1

                     462 ;221:                 bufPos = BerDecoder_decodeLength(inBuf, &length, bufPos, maxBufPos);


                     463 

000001fc e1a03006    464 	mov	r3,r6

00000200 e28d1010    465 	add	r1,sp,16

00000204 e1a00005    466 	mov	r0,r5

00000208 eb000000*   467 	bl	BerDecoder_decodeLength

0000020c e1a04000    468 	mov	r4,r0

                     469 ;222: 


                     470 ;223:                 //debugSendUshort("\tobjectScopeTag:", objectScopeTag);


                     471 ;224:                 switch (objectScopeTag) {


                     472 

00000210 e25bb080    473 	subs	fp,fp,128

00000214 3a000018    474 	blo	.L99

00000218 0a000003    475 	beq	.L95

0000021c e35b0002    476 	cmp	fp,2

00000220 3a000007    477 	blo	.L96


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bhg1.s
00000224 0a00000f    478 	beq	.L97

00000228 ea000013    479 	b	.L99

                     480 .L95:

                     481 ;225:                 case 0x80: // vmd-specific


                     482 ;226:                     objectScope = OBJECT_SCOPE_VMD;


                     483 

0000022c e59d1010    484 	ldr	r1,[sp,16]

                     485 ;250:             break;


                     486 ;251:         default:        


                     487 ;252:             return 0;


                     488 

                     489 ;253:             /*


                     490 ;254:             return CreateMmsConfirmedErrorPdu( invokeId, response,


                     491 ;255:                                                           MMS_ERROR_ACCESS_OBJECT_ACCESS_UNSUPPORTED );


                     492 ;256:                                                           */


                     493 ;257:         }


                     494 ;258: 


                     495 ;259:         bufPos += length;


                     496 

00000230 e3a0b000    497 	mov	fp,0

00000234 e0844001    498 	add	r4,r4,r1

00000238 e1540006    499 	cmp	r4,r6

0000023c baffffd0    500 	blt	.L85

00000240 ea00001d    501 	b	.L84

                     502 .L96:

                     503 ;227:                     break;


                     504 ;228:                 case 0x81: // domain-specific


                     505 ;229:                     domainIdLength = length;


                     506 

00000244 e59d1010    507 	ldr	r1,[sp,16]

00000248 e0840005    508 	add	r0,r4,r5

0000024c e58d1024    509 	str	r1,[sp,36]

                     510 ;230:                     domainId = inBuf + bufPos;


                     511 

00000250 e58d0014    512 	str	r0,[sp,20]

                     513 ;231:                     //debugSendUshort("\tdomainIdLength:", domainIdLength);


                     514 ;232:                     //debugSendStrL("\tdomainId:", domainId, domainIdLength);


                     515 ;233:                     objectScope = OBJECT_SCOPE_DOMAIN;


                     516 

00000254 e3a0b001    517 	mov	fp,1

                     518 ;250:             break;


                     519 ;251:         default:        


                     520 ;252:             return 0;


                     521 

                     522 ;253:             /*


                     523 ;254:             return CreateMmsConfirmedErrorPdu( invokeId, response,


                     524 ;255:                                                           MMS_ERROR_ACCESS_OBJECT_ACCESS_UNSUPPORTED );


                     525 ;256:                                                           */


                     526 ;257:         }


                     527 ;258: 


                     528 ;259:         bufPos += length;


                     529 

00000258 e0844001    530 	add	r4,r4,r1

0000025c e1540006    531 	cmp	r4,r6

00000260 baffffc7    532 	blt	.L85

00000264 ea000014    533 	b	.L84

                     534 .L97:

                     535 ;234:                     break;


                     536 ;235:                 case 0x82: // association-specific


                     537 ;236:                     objectScope = OBJECT_SCOPE_ASSOCIATION;                    


                     538 


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bhg1.s
                     539 ;237:                     return CreateMmsConfirmedErrorPdu( invokeId, response,


                     540 

00000268 e1a0100a    541 	mov	r1,r10

0000026c e59d0050    542 	ldr	r0,[sp,80]

00000270 e3a02052    543 	mov	r2,82

00000274 eb000000*   544 	bl	CreateMmsConfirmedErrorPdu

00000278 ea00003f    545 	b	.L81

                     546 .L99:

                     547 ;238:                                                    MMS_ERROR_ACCESS_OBJECT_ACCESS_UNSUPPORTED );


                     548 ;239:                 default:


                     549 ;240:                     debugSendUshort("!!!!! Unsupported objectScope:",tag);


                     550 

0000027c e28f0000*   551 	adr	r0,.L336

00000280 e1a01007    552 	mov	r1,r7

00000284 eb000000*   553 	bl	debugSendUshort

                     554 ;241:                     mms_createMmsRejectPdu(&invokeId, MMS_ERROR_REJECT_UNRECOGNIZED_MODIFIER, response);


                     555 

00000288 e1a0200a    556 	mov	r2,r10

0000028c e28d0050    557 	add	r0,sp,80

00000290 e3a01068    558 	mov	r1,104

00000294 eb000000*   559 	bl	mms_createMmsRejectPdu

                     560 ;242:                     return 0;


                     561 

00000298 e3a00000    562 	mov	r0,0

0000029c ea000036    563 	b	.L81

                     564 .L101:

                     565 ;243:                 }                


                     566 ;244:             }


                     567 ;245:             break;


                     568 ;246: 


                     569 ;247:         case TAG_CONTINUE_AFTER:


                     570 ;248:             continueAfter = inBuf + bufPos;


                     571 

000002a0 e0840005    572 	add	r0,r4,r5

000002a4 e59d1010    573 	ldr	r1,[sp,16]

000002a8 e58d001c    574 	str	r0,[sp,28]

                     575 ;249:             continueAfterLength = length;


                     576 

000002ac e58d1020    577 	str	r1,[sp,32]

                     578 ;250:             break;


                     579 ;251:         default:        


                     580 ;252:             return 0;


                     581 

                     582 ;253:             /*


                     583 ;254:             return CreateMmsConfirmedErrorPdu( invokeId, response,


                     584 ;255:                                                           MMS_ERROR_ACCESS_OBJECT_ACCESS_UNSUPPORTED );


                     585 ;256:                                                           */


                     586 ;257:         }


                     587 ;258: 


                     588 ;259:         bufPos += length;


                     589 

000002b0 e0844001    590 	add	r4,r4,r1

000002b4 e1540006    591 	cmp	r4,r6

000002b8 baffffb1    592 	blt	.L85

                     593 .L84:

                     594 ;260:     }           


                     595 ;261: 


                     596 ;262:     switch(objectScope)


                     597 

000002bc e35b0001    598 	cmp	fp,1

000002c0 0a00000a    599 	beq	.L108


                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bhg1.s
000002c4 2a00002b    600 	bhs	.L120

                     601 ;263:     {


                     602 ;264:     case OBJECT_SCOPE_VMD:


                     603 ;265:         //debugSendText("Process OBJECT_SCOPE_VMD");


                     604 ;266:         responseSize =


                     605 

000002c8 e3e04000    606 	mvn	r4,0

000002cc e59d001c    607 	ldr	r0,[sp,28]

000002d0 e59d1020    608 	ldr	r1,[sp,32]

000002d4 e3a03000    609 	mov	r3,0

000002d8 e88d001b    610 	stmea	[sp],{r0-r1,r3-r4}

000002dc e59d0028    611 	ldr	r0,[sp,40]

000002e0 e59d1050    612 	ldr	r1,[sp,80]

000002e4 e1a0200a    613 	mov	r2,r10

000002e8 ebffff44*   614 	bl	mms_createNameListResponse

                     615 ;267:                 mms_createNameListResponse(mmsConn, invokeId,  response, 0,


                     616 ;268: 					continueAfter, continueAfterLength, FALSE, IED_ANY_TAG);


                     617 ;269:         return responseSize;


                     618 

000002ec ea000022    619 	b	.L81

                     620 .L108:

                     621 ;270:     case OBJECT_SCOPE_DOMAIN:


                     622 ;271: 		{


                     623 

                     624 ;272: 			int ldSection;


                     625 ;273: 			int objectsTagToWrite;


                     626 ;274: 			if (objectClass == OBJECT_CLASS_NAMED_VARIABLE_LIST)


                     627 

000002f0 e59d0018    628 	ldr	r0,[sp,24]

000002f4 e3500002    629 	cmp	r0,2

000002f8 1a000007    630 	bne	.L109

                     631 ;275: 			{


                     632 

                     633 ;276: 				//Для Data sets


                     634 ;277: 				ldSection = IED_VMD_DATA_SET_SECTION;


                     635 

000002fc e3a040e7    636 	mov	r4,231

                     637 ;291: 			}


                     638 ;292: 			


                     639 ;293: 			rootObj = findDomainSection(ldSection,


                     640 

00000300 e59d1014    641 	ldr	r1,[sp,20]

00000304 e59d2024    642 	ldr	r2,[sp,36]

00000308 e3a000ee    643 	mov	r0,238

                     644 ;278: 				objectsTagToWrite = IED_DATA_SET;


                     645 

0000030c eb000000*   646 	bl	findDomainSection

00000310 e1b03000    647 	movs	r3,r0

                     648 ;294: 				domainId, domainIdLength);


                     649 ;295: 			if (rootObj == 0)


                     650 

00000314 1a00000e    651 	bne	.L116

00000318 ea000008    652 	b	.L117

                     653 .L109:

                     654 ;279: 			}


                     655 ;280:             else if (objectClass == OBJECT_CLASS_JOURNAL)


                     656 

0000031c e3500008    657 	cmp	r0,8

00000320 0a000006    658 	beq	.L117

                     659 ;281: 			{


                     660 


                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bhg1.s
                     661 ;282: 				//Journal не поддерживается


                     662 ;283:                 return CreateMmsConfirmedErrorPdu( invokeId, response,


                     663 

                     664 ;284: 					MMS_ERROR_ACCESS_OBJECT_NON_EXISTENT );				


                     665 ;285: 			}


                     666 ;286: 			else


                     667 ;287: 			{


                     668 

                     669 ;288:                 //Для обычных данных


                     670 ;289: 				ldSection = IED_VMD_DATA_SECTION;


                     671 

00000324 e3e04000    672 	mvn	r4,0

                     673 ;291: 			}


                     674 ;292: 			


                     675 ;293: 			rootObj = findDomainSection(ldSection,


                     676 

00000328 e59d1014    677 	ldr	r1,[sp,20]

0000032c e59d2024    678 	ldr	r2,[sp,36]

00000330 e3a000ec    679 	mov	r0,236

                     680 ;290: 				objectsTagToWrite = IED_ANY_TAG;


                     681 

00000334 eb000000*   682 	bl	findDomainSection

00000338 e1b03000    683 	movs	r3,r0

                     684 ;294: 				domainId, domainIdLength);


                     685 ;295: 			if (rootObj == 0)


                     686 

0000033c 1a000004    687 	bne	.L116

                     688 .L117:

                     689 ;296: 			{


                     690 

                     691 ;297: 				ERROR_REPORT("Unable to find domain cection");


                     692 ;298: 				return CreateMmsConfirmedErrorPdu(invokeId, response,


                     693 

00000340 e1a0100a    694 	mov	r1,r10

00000344 e59d0050    695 	ldr	r0,[sp,80]

00000348 e3a02051    696 	mov	r2,81

0000034c eb000000*   697 	bl	CreateMmsConfirmedErrorPdu

00000350 ea000009    698 	b	.L81

                     699 .L116:

                     700 ;299: 					MMS_ERROR_ACCESS_OBJECT_NON_EXISTENT);


                     701 ;300: 			}


                     702 ;301: 			return mms_createNameListResponse(mmsConn, invokeId, response,


                     703 

00000354 e59d001c    704 	ldr	r0,[sp,28]

00000358 e59d1020    705 	ldr	r1,[sp,32]

0000035c e3a02001    706 	mov	r2,1

00000360 e88d0017    707 	stmea	[sp],{r0-r2,r4}

00000364 e59d0028    708 	ldr	r0,[sp,40]

00000368 e59d1050    709 	ldr	r1,[sp,80]

0000036c e1a0200a    710 	mov	r2,r10

00000370 ebffff22*   711 	bl	mms_createNameListResponse

00000374 ea000000    712 	b	.L81

                     713 .L120:

                     714 ;302: 				rootObj, continueAfter, continueAfterLength, TRUE,


                     715 ;303: 				objectsTagToWrite);


                     716 ;304: 		}                


                     717 ;305:     default:


                     718 ;306:         return 0;


                     719 

00000378 e3a00000    720 	mov	r0,0

                     721 .L81:


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bhg1.s
0000037c e28dd02c    722 	add	sp,sp,44

00000380 e8bd8ff0    723 	ldmfd	[sp]!,{r4-fp,pc}

                     724 	.endf	mms_handleGetNameListRequest

                     725 	.align	4

                     726 ;rootObj	r3	local

                     727 ;objectClass	[sp,24]	local

                     728 ;objectScope	fp	local

                     729 ;domainId	[sp,20]	local

                     730 ;domainIdLength	[sp,36]	local

                     731 ;continueAfter	[sp,28]	local

                     732 ;continueAfterLength	[sp,32]	local

                     733 ;tag	r7	local

                     734 ;length	[sp,16]	local

                     735 ;objectScopeTag	fp	local

                     736 ;.L291	.L294	static

                     737 ;objectsTagToWrite	r4	local

                     738 

                     739 ;mmsConn	[sp,40]	param

                     740 ;inBuf	r5	param

                     741 ;bufPos	r4	param

                     742 ;maxBufPos	r6	param

                     743 ;invokeId	[sp,80]	param

                     744 ;response	r10	param

                     745 

                     746 	.section ".bss","awb"

                     747 .L290:

                     748 	.data

                     749 	.text

                     750 

                     751 ;307:     }


                     752 ;308: 


                     753 ;309: }


                     754 	.align	4

                     755 .L336:

                     756 ;	"!!!!! Unsupported objectScope:\000"

00000384 21212121    757 	.data.b	33,33,33,33

00000388 6e552021    758 	.data.b	33,32,85,110

0000038c 70707573    759 	.data.b	115,117,112,112

00000390 6574726f    760 	.data.b	111,114,116,101

00000394 626f2064    761 	.data.b	100,32,111,98

00000398 7463656a    762 	.data.b	106,101,99,116

0000039c 706f6353    763 	.data.b	83,99,111,112

000003a0 3a65       764 	.data.b	101,58

000003a2 00         765 	.data.b	0

000003a3 00         766 	.align 4

                     767 

                     768 	.type	.L336,$object

                     769 	.size	.L336,4

                     770 

                     771 	.align	4

                     772 

                     773 	.data

                     774 	.ghsnote version,6

                     775 	.ghsnote tools,3

                     776 	.ghsnote options,0

                     777 	.text

                     778 	.align	4

