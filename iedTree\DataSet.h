#pragma once

//DataSet в контексте IEDTree

#include "iedEntity.h"

#include <stddef.h>
#include <stdbool.h>

typedef struct DataSetItem{
	//! Следующий элемент в списке
	struct DataSetItem* next;
	StringView domainID;
	StringView itemID;
	//Объект, на который ссылается элемент набра данных
	IEDEntity obj;
} DataSetItem;

typedef struct {
	DataSetItem* firstItem;
	size_t itemCount;
} DataSet;

bool DataSet_init(IEDEntity entity);
bool DataSet_postCreate(IEDEntity entity);

DataSet* DataSet_getDataSetObj(IEDEntity entity);


DataSetItem* DataSet_getFirstItem(IEDEntity entity);

//! Для чтения вычисляет общий размер всех элементов DataSet
//! dsEntity - собственно, DataSet
bool DataSet_calcReadLen(IEDEntity dsEntity, size_t *pLen);

//! dsEntity - собственно, DataSet
bool DataSet_encodeRead(IEDEntity dsEntity, BufferView* outBuf);
