#pragma once

#include "local_types.h"
#include <stddef.h>
#include <stdint.h>
#include <stdbool.h>


//Комментарий для проверки кодировки
bool NetTools_init(void);
bool NetTools_getIf(uint8_t ifNum, void** pNetIf);

//! 6 байт MAC записываются по указанному адресу
bool NetTools_getMac(void* netIf, uint8_t mac[6]);

bool NetTools_send(void* netIf, void* data, size_t byteCount);

bool NetTools_busOK(void);
