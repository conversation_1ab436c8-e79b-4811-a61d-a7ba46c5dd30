                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_i901.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=platform_thread.c -o gh_i901.o -list=platform_thread.lst C:\Users\<USER>\AppData\Local\Temp\gh_i901.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_i901.s
Source File: platform_thread.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      10 ;		platform_thread.c -o platform_thread.o

                      11 ;Source File:   platform_thread.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Oct 07 09:39:29 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "platform_thread.h"


                      21 ;2: 


                      22 ;3: #include <process.h>


                      23 ;4: 


                      24 ;5: void createThread(void *pProcAddr, void* pParam)


                      25 	.text

                      26 	.align	4

                      27 createThread::

                      28 ;6: {


                      29 

                      30 ;7:     _CreateThread( pProcAddr, pParam, 0x1000,


                      31 

00000000 e3a03f40     32 	mov	r3,256

00000004 e2833001     33 	add	r3,r3,1

00000008 e3a02d40     34 	mov	r2,1<<12

0000000c ea000000*    35 	b	_CreateThread

                      36 	.endf	createThread

                      37 	.align	4

                      38 

                      39 ;pProcAddr	none	param

                      40 ;pParam	none	param

                      41 

                      42 	.section ".bss","awb"

                      43 .L30:

                      44 	.data

                      45 	.text

                      46 

                      47 ;8:                                 THREAD_PRIORITY_NORMAL | THREAD_SELF_TERMINATED);


                      48 ;9: }


                      49 	.align	4

                      50 


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_i901.s
                      51 	.data

                      52 	.ghsnote version,6

                      53 	.ghsnote tools,3

                      54 	.ghsnote options,0

                      55 	.text

                      56 	.align	4

