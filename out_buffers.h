#pragma once
#include "MmsConst.h"
#include <platform_critical_section.h>
#include <types.h>
#include <stdbool.h>

//Управление буферами для отправки COTP в отдельном потоке.
//Эти буфера являются общими для всех соединений
//При отправке эти буфера режутся на TPKT пакеты

#define SESSION_OUT_BUF_SIZE DEFAULT_BUFFER_SIZE
#define COTP_OUT_BUFFERS_COUNT 10

typedef struct {
    bool busy;
    int byteCount;
    unsigned char cotpOutBuf[SESSION_OUT_BUF_SIZE];
} SessionOutBuffer;

typedef struct {
    CriticalSection cs;
    SessionOutBuffer buffers[COTP_OUT_BUFFERS_COUNT];
} SessionBuffers;

void initSessionOutBuffers(SessionBuffers* buffers);
void SessionBuffers_done(SessionBuffers* buffers);
SessionOutBuffer* allocSessionOutBuffer(SessionBuffers* buffers, int size);
void freeSessionOutBuffer(SessionOutBuffer* buf);


