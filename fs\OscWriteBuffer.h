#pragma once
#include <stdbool.h>
#include "../bufView.h"

typedef struct OscWriteBuffer OscWriteBuffer;
struct OscWriteBuffer
{
	unsigned char *data;
	unsigned char *p;
	size_t dataPos;
	size_t dataSize;
	size_t size;
};
//! выделяет память и присоединяет ее к буферу, поля wb должны быть заполнены 0
bool OscWriteBuffer_create(OscWriteBuffer *wb, size_t size);
//! присоединяет выделенный буфер к wb
bool OscWriteBuffer_attach(OscWriteBuffer *wb, unsigned char *p, size_t size);
//! удаляет все данные из буфера
void OscWriteBuffer_reset(OscWriteBuffer *wb);

//! запись, если не лезет - память перевыделяется
bool OscWriteBuffer_write(OscWriteBuffer *wb, void *data, size_t size);
//! перенос из одного буфера в другой, если не лезет dst увеличивается
bool OscWriteBuffer_toWriteBuffer(OscWriteBuffer *dst, OscWriteBuffer *src);

bool OscWriteBuffer_resize(OscWriteBuffer *wb, int len);
bool  OscWriteBuffer_empty(OscWriteBuffer *wb);

void *OscWriteBuffer_data(OscWriteBuffer *wb);
size_t OscWriteBuffer_dataLen(OscWriteBuffer *wb);
size_t OscWriteBuffer_size(OscWriteBuffer *wb);

void OscWriteBuffer_destroy(OscWriteBuffer *wb);

size_t OscWriteBuffer_toBufferView(BufferView *bv, OscWriteBuffer *wb);
