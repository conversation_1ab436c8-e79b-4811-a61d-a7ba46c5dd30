#include "bufViewMMS.h"

#include "AsnEncoding.h"



bool BufView_decodeObjectName(BufferView *bv, StringView *domainId,
                              StringView *itemId)
{
    int nameLen = BerDecoder_DecodeObjectName(bv->p, bv->pos, bv->len,
        (uint8_t**)&itemId->p, (int*)&itemId->len,
        (uint8_t**)&domainId->p, (int*)&domainId->len);

    if(nameLen < 0)
    {
        return false;
    }

    bv->pos = nameLen;
    return true;
}
