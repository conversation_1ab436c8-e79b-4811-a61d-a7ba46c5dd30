#include "iedFC.h"


enum IEDFCType
{    
    IED_FC_TYPE_ST = 0,
    IED_FC_TYPE_MX,
    IED_FC_TYPE_SP,
    IED_FC_TYPE_SV,
    IED_FC_TYPE_CF,
    IED_FC_TYPE_DC,
    IED_FC_TYPE_SG,
    IED_FC_TYPE_SE,
    IED_FC_TYPE_SR,
    IED_FC_TYPE_OR,
    IED_FC_TYPE_BL,
    IED_FC_TYPE_EX,

    // Не упоминаются в 7.3 Annex B
    IED_FC_TYPE_CO,
    IED_FC_TYPE_US,
    IED_FC_TYPE_MS,
    IED_FC_TYPE_RP,
    IED_FC_TYPE_BR,
    IED_FC_TYPE_GO,

    IED_FC_TYPE_COUNT,
    IED_FC_TYPE_UNKNOWN = 0xFF,
};

const char* IEDFCTypeNames[IED_FC_TYPE_COUNT] = {
    "ST", 
    "MX", 
    "SP",
    "SV",
    "CF",
    "DC",
    "SG",
    "SE",
    "SR",
    "OR",
    "BL",
    "EX",    
    "CO",
    "US",
    "MS",
    "RP",
    "BR",
    "GO"
};

static enum IEDFCType getFCType(StringView* fcName)
{    
    int i;
    if(fcName->len != 2)
    {
        return IED_FC_TYPE_UNKNOWN;
    }
    for(i = 0; i < IED_FC_TYPE_COUNT; i++)
    {
        if(0 == StringView_cmpCStr(fcName, IEDFCTypeNames[i]))
        {
            return (enum IEDFCType)i;
        }
    }
    return IED_FC_TYPE_UNKNOWN;
}

static bool isReadOnlyFC(enum IEDFCType fcType)
{
    switch(fcType)
    {
    case IED_FC_TYPE_ST:
    case IED_FC_TYPE_MX:
    case IED_FC_TYPE_SV:    
    case IED_FC_TYPE_DC:// У нас пока description ReadOnly        
    case IED_FC_TYPE_BL:
    case IED_FC_TYPE_EX:
        return true;
    default:
        return false;
    }
}


bool IEDFC_init(IEDEntity entity)
{
    enum IEDFCType fcType;
    entity->type = IED_ENTITY_FC;
    fcType = getFCType(&entity->name);
    entity->subType = fcType;
    if(isReadOnlyFC(fcType))
    {
        IEDEntity_setReadOnlyRecursive(entity, true);
    }
    return true;
}
