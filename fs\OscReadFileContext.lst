                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ec81.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=OscReadFileContext.c -o fs\gh_ec81.o -list=fs/OscReadFileContext.lst C:\Users\<USER>\AppData\Local\Temp\gh_ec81.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_ec81.s
Source File: OscReadFileContext.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      10 ;		fs/OscReadFileContext.c -o fs/OscReadFileContext.o

                      11 ;Source File:   fs/OscReadFileContext.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:55:57 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include <stdbool.h>


                      21 ;2: #include <stdint.h>


                      22 ;3: #include <stdio.h>


                      23 ;4: #include <string.h>


                      24 ;5: #include "OscReadFileContext.h"


                      25 ;6: #include "OscInfo.h"


                      26 ;7: #include "OscFiles.h"


                      27 ;8: 


                      28 ;9: int zs_user_writeToStream(int fd, void *data, int size)


                      29 	.text

                      30 	.align	4

                      31 zs_user_writeToStream::

00000000 e92d4010     32 	stmfd	[sp]!,{r4,lr}

                      33 ;10: {


                      34 

                      35 ;11: 	OscReadFileContext *readFileContext = (OscReadFileContext*)fd;


                      36 

00000004 e1a04002     37 	mov	r4,r2

00000008 e2800e4a     38 	add	r0,r0,0x04a0

0000000c eb000000*    39 	bl	OscWriteBuffer_write

                      40 ;12: 	if (OscWriteBuffer_write(&readFileContext->streamBuffer, data, size))


                      41 

00000010 e3500000     42 	cmp	r0,0

                      43 ;13: 	{


                      44 

                      45 ;14: 		return size;


                      46 

00000014 11a00004     47 	movne	r0,r4

                      48 ;15: 	}


                      49 ;16: 	else


                      50 ;17: 	{



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ec81.s
                      51 

                      52 ;18: 		return 0;


                      53 

00000018 e8bd8010     54 	ldmfd	[sp]!,{r4,pc}

                      55 	.endf	zs_user_writeToStream

                      56 	.align	4

                      57 

                      58 ;fd	r0	param

                      59 ;data	none	param

                      60 ;size	r4	param

                      61 

                      62 	.section ".bss","awb"

                      63 .L53:

                      64 	.data

                      65 	.text

                      66 

                      67 ;19: 	}


                      68 ;20: }


                      69 

                      70 ;21: static ZIPentry *initZipEntry(OscReadFileContext *readFileContext)


                      71 	.align	4

                      72 	.align	4

                      73 initZipEntry:

0000001c e92d4000     74 	stmfd	[sp]!,{lr}

00000020 e24dd00c     75 	sub	sp,sp,12

                      76 ;22: {


                      77 

                      78 ;23: 	int64_t writeStatus;


                      79 ;24: 	return zs_entrybegin(readFileContext->zipStream,


                      80 

00000024 e28d1004     81 	add	r1,sp,4

00000028 e58d1000     82 	str	r1,[sp]

0000002c e5902008     83 	ldr	r2,[r0,8]

00000030 e280101c     84 	add	r1,r0,28

00000034 e59004b4     85 	ldr	r0,[r0,1204]

00000038 e3a03000     86 	mov	r3,0

0000003c eb000000*    87 	bl	zs_entrybegin

00000040 e28dd00c     88 	add	sp,sp,12

00000044 e8bd4000     89 	ldmfd	[sp]!,{lr}

00000048 e12fff1e*    90 	ret	

                      91 	.endf	initZipEntry

                      92 	.align	4

                      93 ;writeStatus	[sp,4]	local

                      94 

                      95 ;readFileContext	r0	param

                      96 

                      97 	.section ".bss","awb"

                      98 .L94:

                      99 	.data

                     100 	.text

                     101 

                     102 ;25: 		readFileContext->fileName,


                     103 ;26: 		readFileContext->fileInfo.t,


                     104 ;27: 		ZS_STORE, 


                     105 ;28: 		&writeStatus);


                     106 ;29: }


                     107 

                     108 ;30: static bool writeZipEntry(OscReadFileContext *readFileContext, ZIPentry *zipEntry,


                     109 	.align	4

                     110 	.align	4

                     111 writeZipEntry:


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ec81.s
0000004c e92d40f0    112 	stmfd	[sp]!,{r4-r7,lr}

                     113 ;31: 	OscWriteBuffer *wb)


                     114 ;32: {


                     115 

                     116 ;33: 	unsigned char *data = OscWriteBuffer_data(wb);


                     117 

00000050 e24dd010    118 	sub	sp,sp,16

00000054 e1a05001    119 	mov	r5,r1

00000058 e1a07000    120 	mov	r7,r0

0000005c e1a04002    121 	mov	r4,r2

00000060 e1a00004    122 	mov	r0,r4

00000064 eb000000*   123 	bl	OscWriteBuffer_data

00000068 e1a06000    124 	mov	r6,r0

                     125 ;34: 	size_t dataLen = OscWriteBuffer_dataLen(wb);


                     126 

0000006c e1a00004    127 	mov	r0,r4

00000070 eb000000*   128 	bl	OscWriteBuffer_dataLen

00000074 e3a02000    129 	mov	r2,0

00000078 e28d1008    130 	add	r1,sp,8

0000007c e52d0004    131 	str	r0,[sp,-4]!

00000080 e1a00002    132 	mov	r0,r2

00000084 e98d0003    133 	stmfa	[sp],{r0-r1}

00000088 e8bd0008    134 	ldmfd	[sp]!,{r3}

0000008c e1a02006    135 	mov	r2,r6

00000090 e59704b4    136 	ldr	r0,[r7,1204]

00000094 e1a01005    137 	mov	r1,r5

00000098 eb000000*   138 	bl	zs_entrydata

                     139 ;35: 	int64_t writeStatus;


                     140 ;36: 	if (!zs_entrydata(readFileContext->zipStream, zipEntry, data, dataLen, &writeStatus))


                     141 

0000009c e3500000    142 	cmp	r0,0

                     143 ;37: 	{


                     144 

                     145 ;38: 		return false;


                     146 

000000a0 020000ff    147 	andeq	r0,r0,255

000000a4 0a000002    148 	beq	.L101

                     149 ;39: 	}


                     150 ;40: 	OscWriteBuffer_reset(wb);


                     151 

000000a8 e1a00004    152 	mov	r0,r4

000000ac eb000000*   153 	bl	OscWriteBuffer_reset

                     154 ;41: 	return true;


                     155 

000000b0 e3a00001    156 	mov	r0,1

                     157 .L101:

000000b4 e28dd010    158 	add	sp,sp,16

000000b8 e8bd40f0    159 	ldmfd	[sp]!,{r4-r7,lr}

000000bc e12fff1e*   160 	ret	

                     161 	.endf	writeZipEntry

                     162 	.align	4

                     163 ;data	r6	local

                     164 ;writeStatus	[sp,8]	local

                     165 

                     166 ;readFileContext	r7	param

                     167 ;zipEntry	r5	param

                     168 ;wb	r4	param

                     169 

                     170 	.section ".bss","awb"

                     171 .L149:

                     172 	.data


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ec81.s
                     173 	.text

                     174 

                     175 ;42: }


                     176 

                     177 ;43: 


                     178 ;44: static bool flushZipEntry(OscReadFileContext *readFileContext, ZIPentry *zipEntry)


                     179 	.align	4

                     180 	.align	4

                     181 flushZipEntry:

000000c0 e92d4000    182 	stmfd	[sp]!,{lr}

000000c4 e24dd008    183 	sub	sp,sp,8

                     184 ;45: {


                     185 

                     186 ;46: 	int64_t writeStatus;


                     187 ;47: 	return zs_entryend(readFileContext->zipStream, zipEntry, &writeStatus) != NULL;


                     188 

000000c8 e59004b4    189 	ldr	r0,[r0,1204]

000000cc e1a0200d    190 	mov	r2,sp

000000d0 eb000000*   191 	bl	zs_entryend

000000d4 e3500000    192 	cmp	r0,0

000000d8 13a00001    193 	movne	r0,1

000000dc e28dd008    194 	add	sp,sp,8

000000e0 e8bd4000    195 	ldmfd	[sp]!,{lr}

000000e4 e12fff1e*   196 	ret	

                     197 	.endf	flushZipEntry

                     198 	.align	4

                     199 ;writeStatus	[sp]	local

                     200 

                     201 ;readFileContext	r0	param

                     202 ;zipEntry	none	param

                     203 

                     204 	.section ".bss","awb"

                     205 .L193:

                     206 	.data

                     207 	.text

                     208 

                     209 ;48: }	


                     210 

                     211 ;49: 


                     212 ;50: OscReadFileContext * OscReadFileContext_create(PWFileInfo *fileInfo)


                     213 	.align	4

                     214 	.align	4

                     215 OscReadFileContext_create::

000000e8 e92d4030    216 	stmfd	[sp]!,{r4-r5,lr}

000000ec e1a05000    217 	mov	r5,r0

                     218 ;51: {	


                     219 

                     220 ;52: 	


                     221 ;53: 	OscReadFileContext *readFileContext =  OscFiles_malloc(sizeof(OscReadFileContext));


                     222 

000000f0 e3a00e40    223 	mov	r0,1<<10

000000f4 e28000c4    224 	add	r0,r0,196

000000f8 eb000000*   225 	bl	OscFiles_malloc

000000fc e1b04000    226 	movs	r4,r0

                     227 ;54: 	if (!readFileContext)


                     228 

00000100 0a00002f    229 	beq	.L220

                     230 ;55: 	{


                     231 

                     232 ;56: 		return NULL;


                     233 


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ec81.s
                     234 ;57: 	}


                     235 ;58: 	memset(readFileContext, 0, sizeof(OscReadFileContext));


                     236 

00000104 e3a02e40    237 	mov	r2,1<<10

00000108 e28220c4    238 	add	r2,r2,196

0000010c e3a01000    239 	mov	r1,0

00000110 eb000000*   240 	bl	memset

                     241 ;59: 	memcpy(&readFileContext->fileInfo, fileInfo, sizeof(PWFileInfo));


                     242 

00000114 e1a01005    243 	mov	r1,r5

00000118 e1a00004    244 	mov	r0,r4

0000011c e3a0201c    245 	mov	r2,28

00000120 eb000000*   246 	bl	memcpy

                     247 ;60: 


                     248 ;61: 	readFileContext->freqCfgCount = ~0;


                     249 

00000124 e3e00000    250 	mvn	r0,0

00000128 e584004c    251 	str	r0,[r4,76]

                     252 ;62: 


                     253 ;63: 	// zip архив


                     254 ;64: 	readFileContext->zipStream = zs_init((int)readFileContext, NULL);


                     255 

0000012c e1a00004    256 	mov	r0,r4

00000130 e3a01000    257 	mov	r1,0

00000134 eb000000*   258 	bl	zs_init

00000138 e58404b4    259 	str	r0,[r4,1204]

                     260 ;65: 	if (!readFileContext->zipStream)


                     261 

0000013c e3500000    262 	cmp	r0,0

00000140 0a00001b    263 	beq	.L221

                     264 ;66: 	{


                     265 

                     266 ;67: 		OscReadFileContext_destroy(readFileContext);


                     267 

                     268 ;68: 		return NULL;


                     269 

                     270 ;69: 	}


                     271 ;70: 


                     272 ;71: 


                     273 ;72: 	if (!OscWriteBuffer_create(&readFileContext->cfgBuffer, CFG_BUFFER_SIZE))


                     274 

00000144 e2840e45    275 	add	r0,r4,0x0450

00000148 e3a01c80    276 	mov	r1,1<<15

0000014c eb000000*   277 	bl	OscWriteBuffer_create

00000150 e3500000    278 	cmp	r0,0

00000154 0a000016    279 	beq	.L221

                     280 ;73: 	{


                     281 

                     282 ;74: 		OscReadFileContext_destroy(readFileContext);


                     283 

                     284 ;75: 		return NULL;


                     285 

                     286 ;76: 	}


                     287 ;77: 	


                     288 ;78: 	if (!OscWriteBuffer_create(&readFileContext->freqCfgBuffer, FREQ_CFG_BUFFER_SIZE))


                     289 

00000158 e2840e40    290 	add	r0,r4,1<<10

0000015c e280008c    291 	add	r0,r0,140

00000160 e3a01c80    292 	mov	r1,1<<15

00000164 eb000000*   293 	bl	OscWriteBuffer_create

00000168 e3500000    294 	cmp	r0,0


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ec81.s
0000016c 0a000010    295 	beq	.L221

                     296 ;79: 	{


                     297 

                     298 ;80: 		OscReadFileContext_destroy(readFileContext);


                     299 

                     300 ;81: 		return NULL;


                     301 

                     302 ;82: 	}


                     303 ;83: 


                     304 ;84: 	if (!OscWriteBuffer_create(&readFileContext->datBuffer, DAT_PERIOD_BUFFER_SIZE))


                     305 

00000170 e2840e40    306 	add	r0,r4,1<<10

00000174 e2800064    307 	add	r0,r0,100

00000178 e3a01c40    308 	mov	r1,1<<14

0000017c eb000000*   309 	bl	OscWriteBuffer_create

00000180 e3500000    310 	cmp	r0,0

00000184 0a00000a    311 	beq	.L221

                     312 ;85: 	{


                     313 

                     314 ;86: 		OscReadFileContext_destroy(readFileContext);


                     315 

                     316 ;87: 		return NULL;


                     317 

                     318 ;88: 	}


                     319 ;89: 	


                     320 ;90: 


                     321 ;91: 	if (!OscWriteBuffer_create(&readFileContext->hdrBuffer, HDR_BUFFER_SIZE))


                     322 

00000188 e2840e40    323 	add	r0,r4,1<<10

0000018c e2800078    324 	add	r0,r0,120

00000190 e3a01040    325 	mov	r1,64

00000194 eb000000*   326 	bl	OscWriteBuffer_create

00000198 e3500000    327 	cmp	r0,0

0000019c 0a000004    328 	beq	.L221

                     329 ;92: 	{


                     330 

                     331 ;93: 		OscReadFileContext_destroy(readFileContext);


                     332 

                     333 ;94: 		return NULL;


                     334 

                     335 ;95: 	}


                     336 ;96: 	


                     337 ;97: 


                     338 ;98: 	if (!OscWriteBuffer_create(&readFileContext->streamBuffer, STREAM_BUFFER_SIZE))


                     339 

000001a0 e2840e4a    340 	add	r0,r4,0x04a0

000001a4 e3a01b80    341 	mov	r1,1<<17

000001a8 eb000000*   342 	bl	OscWriteBuffer_create

000001ac e3500000    343 	cmp	r0,0

000001b0 1a000003    344 	bne	.L220

                     345 .L221:

                     346 ;99: 	{


                     347 

                     348 ;100: 		OscReadFileContext_destroy(readFileContext);


                     349 

000001b4 e1a00004    350 	mov	r0,r4

000001b8 eb000003*   351 	bl	OscReadFileContext_destroy

                     352 ;101: 		return NULL;


                     353 

000001bc e3a00000    354 	mov	r0,0

000001c0 ea000000    355 	b	.L200


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ec81.s
                     356 .L220:

                     357 ;102: 	}


                     358 ;103: 


                     359 ;104: 	return readFileContext;


                     360 

000001c4 e1a00004    361 	mov	r0,r4

                     362 .L200:

000001c8 e8bd8030    363 	ldmfd	[sp]!,{r4-r5,pc}

                     364 	.endf	OscReadFileContext_create

                     365 	.align	4

                     366 ;readFileContext	r4	local

                     367 

                     368 ;fileInfo	r5	param

                     369 

                     370 	.section ".bss","awb"

                     371 .L343:

                     372 	.data

                     373 	.text

                     374 

                     375 ;105: }


                     376 

                     377 ;106: 


                     378 ;107: void OscReadFileContext_destroy(OscReadFileContext * readFileContext)


                     379 	.align	4

                     380 	.align	4

                     381 OscReadFileContext_destroy::

000001cc e92d4010    382 	stmfd	[sp]!,{r4,lr}

000001d0 e1a04000    383 	mov	r4,r0

                     384 ;108: {


                     385 

                     386 ;109: 	zs_free(readFileContext->zipStream);


                     387 

000001d4 e59404b4    388 	ldr	r0,[r4,1204]

000001d8 eb000000*   389 	bl	zs_free

                     390 ;110: 


                     391 ;111: 	OscWriteBuffer_destroy(&readFileContext->cfgBuffer);


                     392 

000001dc e2840e45    393 	add	r0,r4,0x0450

000001e0 eb000000*   394 	bl	OscWriteBuffer_destroy

                     395 ;112: 	OscWriteBuffer_destroy(&readFileContext->freqCfgBuffer);


                     396 

000001e4 e2840e40    397 	add	r0,r4,1<<10

000001e8 e280008c    398 	add	r0,r0,140

000001ec eb000000*   399 	bl	OscWriteBuffer_destroy

                     400 ;113: 	OscWriteBuffer_destroy(&readFileContext->datBuffer);


                     401 

000001f0 e2840e40    402 	add	r0,r4,1<<10

000001f4 e2800064    403 	add	r0,r0,100

000001f8 eb000000*   404 	bl	OscWriteBuffer_destroy

                     405 ;114: 	OscWriteBuffer_destroy(&readFileContext->streamBuffer);


                     406 

000001fc e2840e4a    407 	add	r0,r4,0x04a0

00000200 eb000000*   408 	bl	OscWriteBuffer_destroy

                     409 ;115: 	OscWriteBuffer_destroy(&readFileContext->hdrBuffer);


                     410 

00000204 e2840e40    411 	add	r0,r4,1<<10

00000208 e2800078    412 	add	r0,r0,120

0000020c eb000000*   413 	bl	OscWriteBuffer_destroy

                     414 ;116: 	OscFiles_free(readFileContext);


                     415 

00000210 e1a00004    416 	mov	r0,r4


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ec81.s
00000214 e8bd4010    417 	ldmfd	[sp]!,{r4,lr}

00000218 ea000000*   418 	b	OscFiles_free

                     419 	.endf	OscReadFileContext_destroy

                     420 	.align	4

                     421 

                     422 ;readFileContext	r4	param

                     423 

                     424 	.section ".bss","awb"

                     425 .L382:

                     426 	.data

                     427 	.text

                     428 

                     429 ;117: }


                     430 

                     431 ;118: 


                     432 ;119: 


                     433 ;120: bool OscReadFileContext_writeCfgToStream(OscReadFileContext * readFileContext)


                     434 	.align	4

                     435 	.align	4

                     436 OscReadFileContext_writeCfgToStream::

0000021c e92d4010    437 	stmfd	[sp]!,{r4,lr}

00000220 e1a04000    438 	mov	r4,r0

                     439 ;121: {


                     440 

                     441 ;122: 


                     442 ;123: 	if (!readFileContext->zipCfg)


                     443 

00000224 e59414bc    444 	ldr	r1,[r4,1212]

00000228 e3510000    445 	cmp	r1,0

0000022c 1a00000a    446 	bne	.L391

                     447 ;124: 	{


                     448 

                     449 ;125: 		snprintf(readFileContext->fileName, sizeof(readFileContext->fileName),


                     450 

00000230 e5943000    451 	ldr	r3,[r4]

00000234 e28f2000*   452 	adr	r2,.L485

00000238 e284001c    453 	add	r0,r4,28

0000023c e3a01010    454 	mov	r1,16

00000240 eb000000*   455 	bl	snprintf

                     456 ;126: 			"%d.cfg", readFileContext->fileInfo.name);


                     457 ;127: 		readFileContext->zipCfg = initZipEntry(readFileContext);


                     458 

00000244 e1a00004    459 	mov	r0,r4

00000248 ebffff73*   460 	bl	initZipEntry

0000024c e58404bc    461 	str	r0,[r4,1212]

00000250 e1a01000    462 	mov	r1,r0

                     463 ;128: 		if (!readFileContext->zipCfg)


                     464 

00000254 e3500000    465 	cmp	r0,0

                     466 ;129: 		{


                     467 

                     468 ;130: 			return false;


                     469 

00000258 020000ff    470 	andeq	r0,r0,255

                     471 .L391:

                     472 ;131: 		}


                     473 ;132: 	}


                     474 ;133: 


                     475 ;134: 	return writeZipEntry(readFileContext, readFileContext->zipCfg, &readFileContext->cfgBuffer);


                     476 

0000025c 12842e45    477 	addne	r2,r4,0x0450


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ec81.s
00000260 11a00004    478 	movne	r0,r4

00000264 18bd4010    479 	ldmnefd	[sp]!,{r4,lr}

00000268 1affff77*   480 	bne	writeZipEntry

                     481 .L389:

0000026c e8bd8010    482 	ldmfd	[sp]!,{r4,pc}

                     483 	.endf	OscReadFileContext_writeCfgToStream

                     484 	.align	4

                     485 ;.L467	.L470	static

                     486 

                     487 ;readFileContext	r4	param

                     488 

                     489 	.section ".bss","awb"

                     490 .L466:

                     491 	.data

                     492 	.text

                     493 

                     494 ;135: 	


                     495 ;136: }


                     496 

                     497 ;137: 


                     498 ;138: bool OscReadFileContext_closeCfg(OscReadFileContext * readFileContext)


                     499 	.align	4

                     500 	.align	4

                     501 OscReadFileContext_closeCfg::

                     502 ;139: {


                     503 

                     504 ;140: 	return flushZipEntry(readFileContext, readFileContext->zipCfg);


                     505 

00000270 e59014bc    506 	ldr	r1,[r0,1212]

00000274 eaffff91*   507 	b	flushZipEntry

                     508 	.endf	OscReadFileContext_closeCfg

                     509 	.align	4

                     510 

                     511 ;readFileContext	none	param

                     512 

                     513 	.section ".bss","awb"

                     514 .L513:

                     515 	.data

                     516 	.text

                     517 

                     518 ;141: }


                     519 

                     520 ;142: 


                     521 ;143: bool OscReadFileContext_writeDatToStream(OscReadFileContext * readFileContext)


                     522 	.align	4

                     523 	.align	4

                     524 OscReadFileContext_writeDatToStream::

00000278 e92d4010    525 	stmfd	[sp]!,{r4,lr}

0000027c e1a04000    526 	mov	r4,r0

                     527 ;144: {


                     528 

                     529 ;145: 	if (!readFileContext->zipDat)


                     530 

00000280 e59414b8    531 	ldr	r1,[r4,1208]

00000284 e3510000    532 	cmp	r1,0

00000288 1a00000a    533 	bne	.L522

                     534 ;146: 	{


                     535 

                     536 ;147: 		snprintf(readFileContext->fileName, sizeof(readFileContext->fileName),


                     537 

0000028c e5943000    538 	ldr	r3,[r4]


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ec81.s
00000290 e28f2000*   539 	adr	r2,.L613

00000294 e284001c    540 	add	r0,r4,28

00000298 e3a01010    541 	mov	r1,16

0000029c eb000000*   542 	bl	snprintf

                     543 ;148: 			"%d.dat", readFileContext->fileInfo.name);


                     544 ;149: 		readFileContext->zipDat = initZipEntry(readFileContext);


                     545 

000002a0 e1a00004    546 	mov	r0,r4

000002a4 ebffff5c*   547 	bl	initZipEntry

000002a8 e58404b8    548 	str	r0,[r4,1208]

000002ac e1a01000    549 	mov	r1,r0

                     550 ;150: 		if (!readFileContext->zipDat)


                     551 

000002b0 e3500000    552 	cmp	r0,0

                     553 ;151: 		{


                     554 

                     555 ;152: 			return false;


                     556 

000002b4 020000ff    557 	andeq	r0,r0,255

                     558 .L522:

                     559 ;153: 		}


                     560 ;154: 	}


                     561 ;155: 	return writeZipEntry(readFileContext, readFileContext->zipDat, &readFileContext->datBuffer);


                     562 

000002b8 12840e40    563 	addne	r0,r4,1<<10

000002bc 12802064    564 	addne	r2,r0,100

000002c0 11a00004    565 	movne	r0,r4

000002c4 18bd4010    566 	ldmnefd	[sp]!,{r4,lr}

000002c8 1affff5f*   567 	bne	writeZipEntry

                     568 .L520:

000002cc e8bd8010    569 	ldmfd	[sp]!,{r4,pc}

                     570 	.endf	OscReadFileContext_writeDatToStream

                     571 	.align	4

                     572 ;.L595	.L598	static

                     573 

                     574 ;readFileContext	r4	param

                     575 

                     576 	.section ".bss","awb"

                     577 .L594:

                     578 	.data

                     579 	.text

                     580 

                     581 ;156: }


                     582 

                     583 ;157: bool OscReadFileContext_closeDat(OscReadFileContext * readFileContext)


                     584 	.align	4

                     585 	.align	4

                     586 OscReadFileContext_closeDat::

                     587 ;158: {


                     588 

                     589 ;159: 	return flushZipEntry(readFileContext, readFileContext->zipDat);


                     590 

000002d0 e59014b8    591 	ldr	r1,[r0,1208]

000002d4 eaffff79*   592 	b	flushZipEntry

                     593 	.endf	OscReadFileContext_closeDat

                     594 	.align	4

                     595 

                     596 ;readFileContext	none	param

                     597 

                     598 	.section ".bss","awb"

                     599 .L641:


                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ec81.s
                     600 	.data

                     601 	.text

                     602 

                     603 ;160: }


                     604 

                     605 ;161: 


                     606 ;162: bool  OscReadFileContext_writeHdrToStream(OscReadFileContext * readFileContext)


                     607 	.align	4

                     608 	.align	4

                     609 OscReadFileContext_writeHdrToStream::

000002d8 e92d4010    610 	stmfd	[sp]!,{r4,lr}

000002dc e1a04000    611 	mov	r4,r0

                     612 ;163: {


                     613 

                     614 ;164: 	if (!readFileContext->zipHdr)


                     615 

000002e0 e59414c0    616 	ldr	r1,[r4,1216]

000002e4 e3510000    617 	cmp	r1,0

000002e8 1a00000a    618 	bne	.L650

                     619 ;165: 	{


                     620 

                     621 ;166: 		snprintf(readFileContext->fileName, sizeof(readFileContext->fileName),


                     622 

000002ec e5943000    623 	ldr	r3,[r4]

000002f0 e28f2000*   624 	adr	r2,.L741

000002f4 e284001c    625 	add	r0,r4,28

000002f8 e3a01010    626 	mov	r1,16

000002fc eb000000*   627 	bl	snprintf

                     628 ;167: 			"%d.hdr", readFileContext->fileInfo.name);


                     629 ;168: 		readFileContext->zipHdr = initZipEntry(readFileContext);


                     630 

00000300 e1a00004    631 	mov	r0,r4

00000304 ebffff44*   632 	bl	initZipEntry

00000308 e58404c0    633 	str	r0,[r4,1216]

0000030c e1a01000    634 	mov	r1,r0

                     635 ;169: 		if (!readFileContext->zipHdr)


                     636 

00000310 e3500000    637 	cmp	r0,0

                     638 ;170: 		{


                     639 

                     640 ;171: 			return false;


                     641 

00000314 020000ff    642 	andeq	r0,r0,255

                     643 .L650:

                     644 ;172: 		}


                     645 ;173: 	}


                     646 ;174: 


                     647 ;175: 	return writeZipEntry(readFileContext, readFileContext->zipHdr, &readFileContext->hdrBuffer);


                     648 

00000318 12840e40    649 	addne	r0,r4,1<<10

0000031c 12802078    650 	addne	r2,r0,120

00000320 11a00004    651 	movne	r0,r4

00000324 18bd4010    652 	ldmnefd	[sp]!,{r4,lr}

00000328 1affff47*   653 	bne	writeZipEntry

                     654 .L648:

0000032c e8bd8010    655 	ldmfd	[sp]!,{r4,pc}

                     656 	.endf	OscReadFileContext_writeHdrToStream

                     657 	.align	4

                     658 ;.L723	.L726	static

                     659 

                     660 ;readFileContext	r4	param


                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ec81.s
                     661 

                     662 	.section ".bss","awb"

                     663 .L722:

                     664 	.data

                     665 	.text

                     666 

                     667 ;176: }


                     668 

                     669 ;177: 


                     670 ;178: bool OscReadFileContext_closeHdr(OscReadFileContext * readFileContext)


                     671 	.align	4

                     672 	.align	4

                     673 OscReadFileContext_closeHdr::

                     674 ;179: {


                     675 

                     676 ;180: 	return flushZipEntry(readFileContext, readFileContext->zipHdr);


                     677 

00000330 e59014c0    678 	ldr	r1,[r0,1216]

00000334 eaffff61*   679 	b	flushZipEntry

                     680 	.endf	OscReadFileContext_closeHdr

                     681 	.align	4

                     682 

                     683 ;readFileContext	none	param

                     684 

                     685 	.section ".bss","awb"

                     686 .L769:

                     687 	.data

                     688 	.text

                     689 

                     690 ;181: }


                     691 

                     692 ;182: 


                     693 ;183: bool OscReadFileContext_flushAndClose(OscReadFileContext * readFileContext)


                     694 	.align	4

                     695 	.align	4

                     696 OscReadFileContext_flushAndClose::

00000338 e92d4000    697 	stmfd	[sp]!,{lr}

0000033c e24dd008    698 	sub	sp,sp,8

                     699 ;184: {


                     700 

                     701 ;185: 	int64_t writestatus;


                     702 ;186: 	return zs_finish(readFileContext->zipStream, &writestatus) == 0;


                     703 

00000340 e59004b4    704 	ldr	r0,[r0,1204]

00000344 e1a0100d    705 	mov	r1,sp

00000348 eb000000*   706 	bl	zs_finish

0000034c e3500000    707 	cmp	r0,0

00000350 03a00001    708 	moveq	r0,1

00000354 13a00000    709 	movne	r0,0

00000358 e28dd008    710 	add	sp,sp,8

0000035c e8bd8000    711 	ldmfd	[sp]!,{pc}

                     712 	.endf	OscReadFileContext_flushAndClose

                     713 	.align	4

                     714 ;writestatus	[sp]	local

                     715 

                     716 ;readFileContext	r0	param

                     717 

                     718 	.section ".bss","awb"

                     719 .L801:

                     720 	.data

                     721 	.text


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ec81.s
                     722 

                     723 ;187: }


                     724 

                     725 ;188: 


                     726 ;189: int OscReadFileContext_getPhistotyTime(OscReadFileContext * readFileContext)


                     727 	.align	4

                     728 	.align	4

                     729 OscReadFileContext_getPhistotyTime::

00000360 e92d4000    730 	stmfd	[sp]!,{lr}

                     731 ;190: {


                     732 

                     733 ;191: 	return (int)((readFileContext->phistoryTick + 0.5) / 1000000);


                     734 

00000364 e5901040    735 	ldr	r1,[r0,64]

00000368 e590003c    736 	ldr	r0,[r0,60]

0000036c e3a02000    737 	mov	r2,0

00000370 e3a036fe    738 	mov	r3,254<<20

00000374 e28335c0    739 	add	r3,r3,3<<28

00000378 eb000000*   740 	bl	__dadd

0000037c e59f3054*   741 	ldr	r3,.L837

00000380 e3a02000    742 	mov	r2,0

00000384 eb000000*   743 	bl	__ddiv

00000388 e8bd4000    744 	ldmfd	[sp]!,{lr}

0000038c ea000000*   745 	b	__dtoi

                     746 	.endf	OscReadFileContext_getPhistotyTime

                     747 	.align	4

                     748 

                     749 ;readFileContext	r2	param

                     750 

                     751 	.section ".bss","awb"

                     752 .L830:

                     753 	.data

                     754 	.text

                     755 

                     756 ;192: }


                     757 

                     758 ;193: 


                     759 ;194: int OscReadFileContext_getPhistotyTimeMS(OscReadFileContext * readFileContext)


                     760 	.align	4

                     761 	.align	4

                     762 OscReadFileContext_getPhistotyTimeMS::

00000390 e92d4000    763 	stmfd	[sp]!,{lr}

                     764 ;195: {


                     765 

                     766 ;196: 	return (int)((readFileContext->phistoryTick + 0.5) / 1000);


                     767 

00000394 e5901040    768 	ldr	r1,[r0,64]

00000398 e590003c    769 	ldr	r0,[r0,60]

0000039c e3a02000    770 	mov	r2,0

000003a0 e3a036fe    771 	mov	r3,254<<20

000003a4 e28335c0    772 	add	r3,r3,3<<28

000003a8 eb000000*   773 	bl	__dadd

000003ac e59f3028*   774 	ldr	r3,.L869

000003b0 e3a02000    775 	mov	r2,0

000003b4 eb000000*   776 	bl	__ddiv

000003b8 e8bd4000    777 	ldmfd	[sp]!,{lr}

000003bc ea000000*   778 	b	__dtoi

                     779 	.endf	OscReadFileContext_getPhistotyTimeMS

                     780 	.align	4

                     781 .L485:

                     782 ;	"%d.cfg\000"


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ec81.s
000003c0 632e6425    783 	.data.b	37,100,46,99

000003c4 6766       784 	.data.b	102,103

000003c6 00         785 	.data.b	0

000003c7 00         786 	.align 4

                     787 

                     788 	.type	.L485,$object

                     789 	.size	.L485,4

                     790 

                     791 .L613:

                     792 ;	"%d.dat\000"

000003c8 642e6425    793 	.data.b	37,100,46,100

000003cc 7461       794 	.data.b	97,116

000003ce 00         795 	.data.b	0

000003cf 00         796 	.align 4

                     797 

                     798 	.type	.L613,$object

                     799 	.size	.L613,4

                     800 

                     801 .L741:

                     802 ;	"%d.hdr\000"

000003d0 682e6425    803 	.data.b	37,100,46,104

000003d4 7264       804 	.data.b	100,114

000003d6 00         805 	.data.b	0

000003d7 00         806 	.align 4

                     807 

                     808 	.type	.L741,$object

                     809 	.size	.L741,4

                     810 

                     811 .L837:

000003d8 412e8480    812 	.data.w	0x412e8480

                     813 	.type	.L837,$object

                     814 	.size	.L837,4

                     815 

                     816 .L869:

000003dc 408f4000    817 	.data.w	0x408f4000

                     818 	.type	.L869,$object

                     819 	.size	.L869,4

                     820 

                     821 	.align	4

                     822 

                     823 ;readFileContext	r2	param

                     824 

                     825 	.section ".bss","awb"

                     826 .L862:

                     827 	.data

                     828 	.text

                     829 

                     830 ;197: }


                     831 

                     832 ;198: 


                     833 ;199: bool OscReadFileContext_writeFreq(OscReadFileContext * readFileContext, float freq,


                     834 	.align	4

                     835 	.align	4

                     836 OscReadFileContext_writeFreq::

000003e0 e92d40f0    837 	stmfd	[sp]!,{r4-r7,lr}

                     838 ;200: 	unsigned int sampleNum)


                     839 ;201: {


                     840 

                     841 ;202: 	


                     842 ;203: 	// ни одной частоты не записано


                     843 ;204: 	if (readFileContext->freqCfgCount == ~0)



                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ec81.s
                     844 

000003e4 e1a05001    845 	mov	r5,r1

000003e8 e24dd008    846 	sub	sp,sp,8

000003ec e1a04000    847 	mov	r4,r0

000003f0 e594704c    848 	ldr	r7,[r4,76]

000003f4 e1a06002    849 	mov	r6,r2

000003f8 e3770001    850 	cmn	r7,1

                     851 ;205: 	{


                     852 

                     853 ;206: 		readFileContext->lastFreq.sampleNum = sampleNum;


                     854 

000003fc 02840044    855 	addeq	r0,r4,68

                     856 ;207: 		readFileContext->lastFreq.freq = freq;


                     857 

                     858 ;208: 		readFileContext->freqCfgCount = 0;


                     859 

00000400 03a07000    860 	moveq	r7,0

00000404 088000e0    861 	stmeqea	[r0],{r5-r7}

                     862 ;209: 		return TRUE;


                     863 

00000408 03a00001    864 	moveq	r0,1

0000040c 0a000011    865 	beq	.L870

                     866 ;210: 	}


                     867 ;211: 	


                     868 ;212: 	// записывается только если частота изменилась 


                     869 ;213: 	if (readFileContext->lastFreq.freq != freq) 


                     870 

00000410 e5940044    871 	ldr	r0,[r4,68]

00000414 eb000000*   872 	bl	__fcmp

00000418 e3500000    873 	cmp	r0,0

                     874 ;222: 			&freqCfg, sizeof(OscFreqCfg));


                     875 ;223: 	}


                     876 ;224: 	else


                     877 ;225: 	{


                     878 

                     879 ;226: 		readFileContext->lastFreq.sampleNum = sampleNum;


                     880 

0000041c 05846048    881 	streq	r6,[r4,72]

                     882 ;227: 		return TRUE;


                     883 

00000420 03a00001    884 	moveq	r0,1

00000424 0a00000b    885 	beq	.L870

                     886 ;214: 	{


                     887 

                     888 ;215: 		OscFreqCfg freqCfg;


                     889 ;216: 		freqCfg = readFileContext->lastFreq;


                     890 

00000428 e5942044    891 	ldr	r2,[r4,68]

0000042c e1a0100d    892 	mov	r1,sp

00000430 e5812000    893 	str	r2,[r1]

00000434 e5940048    894 	ldr	r0,[r4,72]

00000438 e2877001    895 	add	r7,r7,1

0000043c e5810004    896 	str	r0,[r1,4]

                     897 ;217: 		readFileContext->lastFreq.freq = freq;


                     898 

                     899 ;218: 		readFileContext->lastFreq.sampleNum = sampleNum;


                     900 

                     901 ;219: 		readFileContext->freqCfgCount++;


                     902 

00000440 e2840044    903 	add	r0,r4,68

00000444 e88000e0    904 	stmea	[r0],{r5-r7}


                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ec81.s
                     905 ;220: 


                     906 ;221: 		return OscWriteBuffer_write(&readFileContext->freqCfgBuffer,


                     907 

00000448 e2840e40    908 	add	r0,r4,1<<10

0000044c e280008c    909 	add	r0,r0,140

00000450 e3a02008    910 	mov	r2,8

00000454 eb000000*   911 	bl	OscWriteBuffer_write

                     912 .L870:

00000458 e28dd008    913 	add	sp,sp,8

0000045c e8bd80f0    914 	ldmfd	[sp]!,{r4-r7,pc}

                     915 	.endf	OscReadFileContext_writeFreq

                     916 	.align	4

                     917 ;freqCfg	[sp]	local

                     918 

                     919 ;readFileContext	r4	param

                     920 ;freq	r5	param

                     921 ;sampleNum	r6	param

                     922 

                     923 	.section ".bss","awb"

                     924 .L924:

                     925 	.data

                     926 	.text

                     927 

                     928 ;228: 	}


                     929 ;229: }


                     930 

                     931 ;230: 


                     932 ;231: int OscReadFileContext_getFreqCount(OscReadFileContext * readFileContext)


                     933 	.align	4

                     934 	.align	4

                     935 OscReadFileContext_getFreqCount::

                     936 ;232: {


                     937 

                     938 ;233: 	return readFileContext->freqCfgCount + 1;


                     939 

00000460 e590004c    940 	ldr	r0,[r0,76]

00000464 e2800001    941 	add	r0,r0,1

00000468 e12fff1e*   942 	ret	

                     943 	.endf	OscReadFileContext_getFreqCount

                     944 	.align	4

                     945 

                     946 ;readFileContext	r0	param

                     947 

                     948 	.section ".bss","awb"

                     949 .L974:

                     950 	.data

                     951 	.text

                     952 

                     953 ;234: }


                     954 

                     955 ;235: 


                     956 ;236: OscFreqCfg * OscReadFileContext_getFreqCfg(OscReadFileContext * readFileContext, int num)


                     957 	.align	4

                     958 	.align	4

                     959 OscReadFileContext_getFreqCfg::

0000046c e92d4010    960 	stmfd	[sp]!,{r4,lr}

                     961 ;237: {


                     962 

                     963 ;238: 	OscFreqCfg *result;


                     964 ;239: 	


                     965 ;240: 	// если не было изменений частоты или последняя частота



                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ec81.s
                     966 ;241: 	if (readFileContext->freqCfgCount == 0 || 


                     967 

00000470 e590204c    968 	ldr	r2,[r0,76]

00000474 e1a04001    969 	mov	r4,r1

00000478 e3520000    970 	cmp	r2,0

0000047c 11540002    971 	cmpne	r4,r2

                     972 ;242: 		num == readFileContext->freqCfgCount)


                     973 ;243: 	{


                     974 

                     975 ;244: 		return &readFileContext->lastFreq;


                     976 

00000480 02800044    977 	addeq	r0,r0,68

00000484 0a000007    978 	beq	.L981

                     979 ;245: 	}


                     980 ;246: 


                     981 ;247: 


                     982 ;248: 	if (num >= readFileContext->freqCfgCount + 1)


                     983 

00000488 e2821001    984 	add	r1,r2,1

0000048c e1540001    985 	cmp	r4,r1

                     986 ;249: 	{


                     987 

                     988 ;250: 		return NULL;


                     989 

00000490 a3a00000    990 	movge	r0,0

00000494 aa000003    991 	bge	.L981

                     992 ;251: 	}


                     993 ;252: 


                     994 ;253: 	result = OscWriteBuffer_data(&readFileContext->freqCfgBuffer);


                     995 

00000498 e2801e40    996 	add	r1,r0,1<<10

0000049c e281008c    997 	add	r0,r1,140

000004a0 eb000000*   998 	bl	OscWriteBuffer_data

                     999 ;254: 	return &result[num];


                    1000 

000004a4 e0800184   1001 	add	r0,r0,r4 lsl 3

                    1002 .L981:

000004a8 e8bd8010   1003 	ldmfd	[sp]!,{r4,pc}

                    1004 	.endf	OscReadFileContext_getFreqCfg

                    1005 	.align	4

                    1006 

                    1007 ;readFileContext	r0	param

                    1008 ;num	r4	param

                    1009 

                    1010 	.section ".bss","awb"

                    1011 .L1038:

                    1012 	.data

                    1013 	.text

                    1014 

                    1015 ;255: }


                    1016 	.align	4

                    1017 

                    1018 	.data

                    1019 	.ghsnote version,6

                    1020 	.ghsnote tools,3

                    1021 	.ghsnote options,0

                    1022 	.text

                    1023 	.align	4

