                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4ag1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=ReportQueue.c -o gh_4ag1.o -list=ReportQueue.lst C:\Users\<USER>\AppData\Local\Temp\gh_4ag1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_4ag1.s
Source File: ReportQueue.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile ReportQueue.c

                      10 ;		-o ReportQueue.o

                      11 ;Source File:   ReportQueue.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:56:06 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "ReportQueue.h"


                      21 ;2: #include <debug.h>


                      22 ;3: #include <string.h>


                      23 ;4: 


                      24 ;5: 


                      25 ;6: 


                      26 ;7: static void lockQueue(ReportQueue *queue)


                      27 ;8: {


                      28 ;9: 	CriticalSection_Lock(&(queue->criticalSection));


                      29 ;10: }


                      30 ;11: static void unlockQueue(ReportQueue *queue)


                      31 ;12: {


                      32 ;13: 	CriticalSection_Unlock(&(queue->criticalSection));


                      33 ;14: }


                      34 ;15: 	


                      35 ;16: 


                      36 ;17: void ReportQueue_init(ReportQueue *queue)


                      37 ;18: {


                      38 ;19: 	queue->chunkCount = REPORT_MEM_SIZE / REPORT_CHUNK_SIZE;


                      39 ;20: 	queue->criticalErrorCount = 0;


                      40 ;21: 	queue->lastOverflowCount = queue->overflowCount = 0;


                      41 ;22: 	queue->head = 0;


                      42 ;23: 	queue->tail = 0;


                      43 ;24: 	CriticalSection_Init(&queue->criticalSection);


                      44 ;25: }


                      45 ;26: 


                      46 ;27: static int getFreeChunkCount(ReportQueue *queue)


                      47 ;28: {


                      48 ;29: 	int freeChunkCount;


                      49 ;30: 	// очередь переполнена


                      50 ;31: 	if (queue->head == -1)



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4ag1.s
                      51 ;32: 	{


                      52 ;33: 		return 0;


                      53 ;34: 	}


                      54 ;35: 	// очередь пустая


                      55 ;36: 	if (queue->head == queue->tail)


                      56 ;37: 	{


                      57 ;38: 		return queue->chunkCount;


                      58 ;39: 	}


                      59 ;40: 	// смотрим сколько осталось


                      60 ;41: 	if (queue->head > queue->tail)


                      61 ;42: 	{


                      62 ;43: 		


                      63 ;44: 		freeChunkCount = (queue->chunkCount - queue->head) + queue->tail;


                      64 ;45: 	}


                      65 ;46: 	else


                      66 ;47: 	{	


                      67 ;48: 		freeChunkCount = queue->tail - queue->head;


                      68 ;49: 	}


                      69 ;50: 


                      70 ;51: 	return freeChunkCount;


                      71 ;52: }


                      72 ;53: 


                      73 ;54: // пустое чтение, для осбождения памяти


                      74 ;55: static void dummyRead(ReportQueue *queue)


                      75 

                      76 ;89: }


                      77 

                      78 ;90: 


                      79 ;91: static void freeChunk(ReportQueue *queue, int chunkCount)


                      80 

                      81 ;96: 	} while (getFreeChunkCount(queue) < chunkCount);


                      82 ;97: }


                      83 

                      84 	.text

                      85 	.align	4

                      86 lockQueue:

00000000 e92d4000     87 	stmfd	[sp]!,{lr}

00000004 e2801b42     88 	add	r1,r0,66<<10

00000008 e2810018     89 	add	r0,r1,24

0000000c eb000000*    90 	bl	CriticalSection_Lock

00000010 e8bd4000     91 	ldmfd	[sp]!,{lr}

00000014 e12fff1e*    92 	ret	

                      93 	.endf	lockQueue

                      94 	.align	4

                      95 

                      96 ;queue	r0	param

                      97 

                      98 	.section ".bss","awb"

                      99 .L126:

                     100 	.data

                     101 	.text

                     102 

                     103 

                     104 	.align	4

                     105 	.align	4

                     106 unlockQueue:

00000018 e92d4000    107 	stmfd	[sp]!,{lr}

0000001c e2801b42    108 	add	r1,r0,66<<10

00000020 e2810018    109 	add	r0,r1,24

00000024 eb000000*   110 	bl	CriticalSection_Unlock

00000028 e8bd4000    111 	ldmfd	[sp]!,{lr}


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4ag1.s
0000002c e12fff1e*   112 	ret	

                     113 	.endf	unlockQueue

                     114 	.align	4

                     115 

                     116 ;queue	r0	param

                     117 

                     118 	.section ".bss","awb"

                     119 .L158:

                     120 	.data

                     121 	.text

                     122 

                     123 

                     124 	.align	4

                     125 	.align	4

                     126 ReportQueue_init::

00000030 e3a01b42    127 	mov	r1,66<<10

00000034 e2811008    128 	add	r1,r1,8

00000038 e3a02f80    129 	mov	r2,1<<9

0000003c e7812000    130 	str	r2,[r1,r0]

00000040 e2802b42    131 	add	r2,r0,66<<10

00000044 e3a01000    132 	mov	r1,0

00000048 e5821014    133 	str	r1,[r2,20]

0000004c e3a02b42    134 	mov	r2,66<<10

00000050 e282200c    135 	add	r2,r2,12

00000054 e7821000    136 	str	r1,[r2,r0]

00000058 e3a02b42    137 	mov	r2,66<<10

0000005c e2822010    138 	add	r2,r2,16

00000060 e7821000    139 	str	r1,[r2,r0]

00000064 e2802b42    140 	add	r2,r0,66<<10

00000068 e5821000    141 	str	r1,[r2]

0000006c e3a02b42    142 	mov	r2,66<<10

00000070 e2822004    143 	add	r2,r2,4

00000074 e7821000    144 	str	r1,[r2,r0]

00000078 e2801b42    145 	add	r1,r0,66<<10

0000007c e2810018    146 	add	r0,r1,24

00000080 ea000000*   147 	b	CriticalSection_Init

                     148 	.endf	ReportQueue_init

                     149 	.align	4

                     150 

                     151 ;queue	r0	param

                     152 

                     153 	.section ".bss","awb"

                     154 .L190:

                     155 	.data

                     156 	.text

                     157 

                     158 

                     159 	.align	4

                     160 	.align	4

                     161 getFreeChunkCount:

00000084 e2801b42    162 	add	r1,r0,66<<10

00000088 e5911000    163 	ldr	r1,[r1]

0000008c e3710001    164 	cmn	r1,1

00000090 03a00000    165 	moveq	r0,0

00000094 0a00000e    166 	beq	.L197

00000098 e3a02b42    167 	mov	r2,66<<10

0000009c e2822004    168 	add	r2,r2,4

000000a0 e7922000    169 	ldr	r2,[r2,r0]

000000a4 e1510002    170 	cmp	r1,r2

000000a8 03a01b42    171 	moveq	r1,66<<10

000000ac 02811008    172 	addeq	r1,r1,8


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4ag1.s
000000b0 07900001    173 	ldreq	r0,[r0,r1]

000000b4 0a000006    174 	beq	.L197

000000b8 e1510002    175 	cmp	r1,r2

000000bc c3a03b42    176 	movgt	r3,66<<10

000000c0 c2833008    177 	addgt	r3,r3,8

000000c4 c7900003    178 	ldrgt	r0,[r0,r3]

000000c8 c0400001    179 	subgt	r0,r0,r1

000000cc c0820000    180 	addgt	r0,r2,r0

000000d0 d0420001    181 	suble	r0,r2,r1

                     182 .L197:

000000d4 e12fff1e*   183 	ret	

                     184 	.endf	getFreeChunkCount

                     185 	.align	4

                     186 

                     187 ;queue	r0	param

                     188 

                     189 	.section ".bss","awb"

                     190 .L250:

                     191 	.data

                     192 	.text

                     193 

                     194 

                     195 ;98: 


                     196 ;99: int	 ReportQueue_write(ReportQueue *queue, unsigned char *data, int size)


                     197 	.align	4

                     198 	.align	4

                     199 ReportQueue_write::

000000d8 e92d4ff6    200 	stmfd	[sp]!,{r1-r2,r4-fp,lr}

                     201 ;100: {


                     202 

                     203 ;101: 	ReportChunk *chunk = NULL;


                     204 

                     205 ;102: 	int takeChunkCount;


                     206 ;103: 	int freeChunkCount;


                     207 ;104: 	int head;


                     208 ;105: 	int copyCount;


                     209 ;106: 	int sizeToWrite = size;


                     210 

                     211 ;107: 			


                     212 ;108: 	// 


                     213 ;109: 	if (size <= 0)


                     214 

000000dc e24dd008    215 	sub	sp,sp,8

000000e0 e58d1008    216 	str	r1,[sp,8]

000000e4 e58d200c    217 	str	r2,[sp,12]

000000e8 e3520000    218 	cmp	r2,0

                     219 ;110: 	{


                     220 

                     221 ;111: 		return 0;


                     222 

000000ec d3a00000    223 	movle	r0,0

000000f0 da000068    224 	ble	.L273

000000f4 e3a0b000    225 	mov	fp,0

000000f8 e1a06000    226 	mov	r6,r0

000000fc e2867b42    227 	add	r7,r6,66<<10

00000100 e1a0a002    228 	mov	r10,r2

                     229 ;112: 	}


                     230 ;113: 


                     231 ;114: 	// количество занимаемых кусков


                     232 ;115: 	takeChunkCount = (size / REPORT_CHUNK_SIZE) + (size % REPORT_CHUNK_SIZE != 0);


                     233 


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4ag1.s
00000104 e1a00fc2    234 	mov	r0,r2 asr 31

00000108 e200307f    235 	and	r3,r0,127

0000010c e0823003    236 	add	r3,r2,r3

00000110 e3c3307f    237 	bic	r3,r3,127

00000114 e1520003    238 	cmp	r2,r3

00000118 e0820ca0    239 	add	r0,r2,r0 lsr 25

0000011c e1a003c0    240 	mov	r0,r0 asr 7

00000120 12802001    241 	addne	r2,r0,1

00000124 11a08002    242 	movne	r8,r2

00000128 01a08000    243 	moveq	r8,r0

                     244 ;116: 	// пытаемся положить данных больше, чем общая память


                     245 ;117: 	if (takeChunkCount > queue->chunkCount)


                     246 

0000012c e5970008    247 	ldr	r0,[r7,8]

00000130 e1a01008    248 	mov	r1,r8

00000134 e1510000    249 	cmp	r1,r0

00000138 da000007    250 	ble	.L278

                     251 ;118: 	{


                     252 

                     253 ;119: 		queue->criticalErrorCount++;


                     254 

0000013c e5970014    255 	ldr	r0,[r7,20]

00000140 e2800001    256 	add	r0,r0,1

00000144 e5870014    257 	str	r0,[r7,20]

                     258 ;120: 		queue->overflowCount++;


                     259 

00000148 e597000c    260 	ldr	r0,[r7,12]

0000014c e2800001    261 	add	r0,r0,1

00000150 e587000c    262 	str	r0,[r7,12]

                     263 ;121: 		return 0;


                     264 

00000154 e3a00000    265 	mov	r0,0

00000158 ea00004e    266 	b	.L273

                     267 .L278:

                     268 ;122: 	}


                     269 ;123: 


                     270 ;124: 	// блокировка, т.к. может вызваться purge


                     271 ;125: 	lockQueue(queue);


                     272 

0000015c e1a00006    273 	mov	r0,r6

00000160 ebffffa6*   274 	bl	lockQueue

                     275 ;126: 	freeChunkCount = getFreeChunkCount(queue);


                     276 

00000164 e1a00006    277 	mov	r0,r6

00000168 ebffffc5*   278 	bl	getFreeChunkCount

                     279 ;127: 	if (freeChunkCount < takeChunkCount)


                     280 

0000016c e1500008    281 	cmp	r0,r8

00000170 ba000003    282 	blt	.L282

00000174 e5974000    283 	ldr	r4,[r7]

                     284 ;132: 	}


                     285 ;133: 


                     286 ;134: 	head = queue->head;


                     287 

                     288 ;135: 


                     289 ;136: 	while (sizeToWrite > 0)


                     290 

00000178 e35a0000    291 	cmp	r10,0

0000017c da00003b    292 	ble	.L305

00000180 ea000024    293 	b	.L302

                     294 .L282:


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4ag1.s
                     295 ;128: 	{


                     296 

                     297 ;129: 		queue->overflowCount++;


                     298 

00000184 e8970234    299 	ldmfd	[r7],{r2,r4-r5,r9}

00000188 e2890001    300 	add	r0,r9,1

0000018c e587000c    301 	str	r0,[r7,12]

                     302 ;130: 		// освобождается нужное количество


                     303 ;131: 		freeChunk(queue, takeChunkCount);


                     304 

00000190 e1a09005    305 	mov	r9,r5

00000194 e1a05002    306 	mov	r5,r2

                     307 .L286:

                     308 ;92: {	


                     309 

                     310 ;93: 	do


                     311 

                     312 ;94: 	{


                     313 

                     314 ;95: 		dummyRead(queue);


                     315 

                     316 ;56: {


                     317 

                     318 ;57: 	ReportChunk *chunk;


                     319 ;58: 	int tail;


                     320 ;59: 	


                     321 ;60: 	if (queue->head == -1)


                     322 

00000198 e3750001    323 	cmn	r5,1

                     324 ;61: 	{


                     325 

                     326 ;62: 		queue->head = queue->tail;


                     327 

0000019c 01a05004    328 	moveq	r5,r4

000001a0 05875000    329 	streq	r5,[r7]

                     330 ;63: 	}


                     331 ;64: 


                     332 ;65: 	tail = queue->tail;


                     333 

                     334 ;66: 	while (1)


                     335 

000001a4 e1a0c009    336 	mov	r12,r9

000001a8 e1a0e005    337 	mov	lr,r5

000001ac e3a02000    338 	mov	r2,0

                     339 .L291:

000001b0 e0840284    340 	add	r0,r4,r4 lsl 5

000001b4 e2844001    341 	add	r4,r4,1

000001b8 e154000c    342 	cmp	r4,r12

000001bc e0860100    343 	add	r0,r6,r0 lsl 2

000001c0 e5d03002    344 	ldrb	r3,[r0,2]

000001c4 a1a04002    345 	movge	r4,r2

                     346 ;67: 	{


                     347 

                     348 ;68: 		chunk = &queue->chunk[tail];


                     349 

                     350 ;69: 		tail++;


                     351 

                     352 ;70: 		if (tail >= queue->chunkCount)


                     353 

                     354 

                     355 


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4ag1.s
                     356 ;73: 		}


                     357 ;74: 


                     358 ;75: 		if (chunk->lastFlag)


                     359 

000001c8 e3530000    360 	cmp	r3,0

000001cc 0a000007    361 	beq	.L293

                     362 ;76: 		{


                     363 

                     364 ;77: 			chunk->lastFlag = 0;


                     365 

000001d0 e3a03000    366 	mov	r3,0

000001d4 e5c03002    367 	strb	r3,[r0,2]

                     368 ;78: 			break;


                     369 

                     370 ;83: 		{


                     371 

                     372 ;84: 			break;


                     373 

                     374 ;85: 		}


                     375 ;86: 	}


                     376 ;87: 	


                     377 ;88: 	queue->tail = tail;


                     378 

000001d8 e5874004    379 	str	r4,[r7,4]

000001dc e1a00006    380 	mov	r0,r6

000001e0 ebffffa7*   381 	bl	getFreeChunkCount

000001e4 e1500008    382 	cmp	r0,r8

000001e8 baffffea    383 	blt	.L286

000001ec ea000006    384 	b	.L281

                     385 .L293:

                     386 ;79: 		}


                     387 ;80: 


                     388 ;81: 		// очередь полностью освободилась


                     389 ;82: 		if (tail == queue->head)


                     390 

000001f0 e154000e    391 	cmp	r4,lr

000001f4 1affffed    392 	bne	.L291

                     393 ;83: 		{


                     394 

                     395 ;84: 			break;


                     396 

                     397 ;85: 		}


                     398 ;86: 	}


                     399 ;87: 	


                     400 ;88: 	queue->tail = tail;


                     401 

000001f8 e5874004    402 	str	r4,[r7,4]

000001fc e1a00006    403 	mov	r0,r6

00000200 ebffff9f*   404 	bl	getFreeChunkCount

00000204 e1500008    405 	cmp	r0,r8

00000208 baffffe2    406 	blt	.L286

                     407 .L281:

                     408 ;132: 	}


                     409 ;133: 


                     410 ;134: 	head = queue->head;


                     411 

0000020c e1a04005    412 	mov	r4,r5

                     413 ;135: 


                     414 ;136: 	while (sizeToWrite > 0)


                     415 

00000210 e35a0000    416 	cmp	r10,0


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4ag1.s
00000214 da000015    417 	ble	.L305

                     418 .L302:

00000218 e35a0080    419 	cmp	r10,128

0000021c d1a0500a    420 	movle	r5,r10

00000220 c3a05080    421 	movgt	r5,128

00000224 e1a02005    422 	mov	r2,r5

00000228 e0840284    423 	add	r0,r4,r4 lsl 5

0000022c e086b100    424 	add	fp,r6,r0 lsl 2

00000230 e59d1008    425 	ldr	r1,[sp,8]

00000234 e28b0004    426 	add	r0,fp,4

00000238 eb000000*   427 	bl	memcpy

                     428 ;137: 	{


                     429 

                     430 ;138: 		copyCount = sizeToWrite > REPORT_CHUNK_SIZE ? REPORT_CHUNK_SIZE : sizeToWrite;


                     431 

                     432 ;139: 		chunk = &queue->chunk[head];


                     433 

                     434 ;140: 


                     435 ;141: 		if (copyCount == REPORT_CHUNK_SIZE)


                     436 

                     437 ;142: 		{


                     438 

                     439 ;143: 			//Закомментировано потому что выравнивание на 4 не гарантировано


                     440 ;144: 			//copyChunk(chunk->payload, data);


                     441 ;145: 			memcpy(chunk->payload, data, copyCount);


                     442 

                     443 ;146: 		}


                     444 ;147: 		else


                     445 ;148: 		{


                     446 

                     447 ;149: 			memcpy(chunk->payload, data, copyCount);


                     448 

                     449 ;150: 		}


                     450 ;151: 


                     451 ;152: 		sizeToWrite -= copyCount;


                     452 

0000023c e1cb50b0    453 	strh	r5,[fp]

                     454 ;154: 		chunk->lastFlag = 0;


                     455 

00000240 e3a00000    456 	mov	r0,0

00000244 e5cb0002    457 	strb	r0,[fp,2]

                     458 ;155: 		data += copyCount;


                     459 

00000248 e59d0008    460 	ldr	r0,[sp,8]

0000024c e04aa005    461 	sub	r10,r10,r5

                     462 ;153: 		chunk->len = copyCount;


                     463 

00000250 e0800005    464 	add	r0,r0,r5

00000254 e58d0008    465 	str	r0,[sp,8]

                     466 ;156: 		head++;


                     467 

00000258 e5970008    468 	ldr	r0,[r7,8]

0000025c e2844001    469 	add	r4,r4,1

                     470 ;157: 		if (head >= queue->chunkCount)


                     471 

                     472 

                     473 

00000260 e1540000    474 	cmp	r4,r0

00000264 a3a04000    475 	movge	r4,0

00000268 e35a0000    476 	cmp	r10,0

0000026c caffffe9    477 	bgt	.L302


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4ag1.s
                     478 .L305:

00000270 e3a00001    479 	mov	r0,1

00000274 e5cb0002    480 	strb	r0,[fp,2]

00000278 e5970004    481 	ldr	r0,[r7,4]

0000027c e1500004    482 	cmp	r0,r4

00000280 11a00004    483 	movne	r0,r4

00000284 03e00000    484 	mvneq	r0,0

                     485 ;160: 		}


                     486 ;161: 	}


                     487 ;162: 	


                     488 ;163: 	// тут chunk уже не может быть NULL


                     489 ;164: 	chunk->lastFlag = 1;


                     490 

                     491 ;165: 


                     492 ;166: 	// последний элемент


                     493 ;167: 	if (head == queue->tail) // переполнение


                     494 

                     495 

                     496 

                     497 ;170: 	}


                     498 ;171: 


                     499 ;172: 	queue->head = head;


                     500 

00000288 e5870000    501 	str	r0,[r7]

                     502 ;173: 	unlockQueue(queue);


                     503 

0000028c e1a00006    504 	mov	r0,r6

00000290 ebffff60*   505 	bl	unlockQueue

                     506 ;174: 	return size;


                     507 

00000294 e59d000c    508 	ldr	r0,[sp,12]

                     509 .L273:

00000298 e28dd008    510 	add	sp,sp,8

0000029c e8bd8ff6    511 	ldmfd	[sp]!,{r1-r2,r4-fp,pc}

                     512 	.endf	ReportQueue_write

                     513 	.align	4

                     514 ;chunk	fp	local

                     515 ;takeChunkCount	r8	local

                     516 ;head	r4	local

                     517 ;copyCount	r5	local

                     518 ;sizeToWrite	r10	local

                     519 ;chunk	r0	local

                     520 ;tail	r4	local

                     521 

                     522 ;queue	r6	param

                     523 ;data	[sp,8]	param

                     524 ;size	[sp,12]	param

                     525 

                     526 	.section ".bss","awb"

                     527 .L550:

                     528 	.data

                     529 	.text

                     530 

                     531 ;175: }


                     532 

                     533 ;176: 


                     534 ;177: int ReportQueue_read(ReportQueue *queue, unsigned char *data, int maxDataSize)


                     535 	.align	4

                     536 	.align	4

                     537 ReportQueue_read::

000002a0 e92d4ff0    538 	stmfd	[sp]!,{r4-fp,lr}


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4ag1.s
                     539 ;178: {


                     540 

                     541 ;179: 	int copyCount = 0;


                     542 

                     543 ;180: 	int tail;


                     544 ;181: 	ReportChunk *chunk;


                     545 ;182: 


                     546 ;183: 	if (ReportQueue_isEmpty(queue))


                     547 

000002a4 e24dd008    548 	sub	sp,sp,8

000002a8 e1a06001    549 	mov	r6,r1

000002ac e1a07002    550 	mov	r7,r2

000002b0 e1a0a000    551 	mov	r10,r0

000002b4 eb000041*   552 	bl	ReportQueue_isEmpty

000002b8 e3500000    553 	cmp	r0,0

                     554 ;184: 	{


                     555 

                     556 ;185: 		return 0;


                     557 

000002bc 13a00000    558 	movne	r0,0

000002c0 1a00002c    559 	bne	.L605

000002c4 e3a0b000    560 	mov	fp,0

                     561 ;186: 	}


                     562 ;187: 	


                     563 ;188: 	lockQueue(queue);


                     564 

000002c8 e1a0000a    565 	mov	r0,r10

000002cc ebffff4b*   566 	bl	lockQueue

                     567 ;189: 


                     568 ;190: 	if (queue->head == -1)


                     569 

000002d0 e28a0b42    570 	add	r0,r10,66<<10

000002d4 e5900000    571 	ldr	r0,[r0]

000002d8 e3a05b42    572 	mov	r5,66<<10

000002dc e2855004    573 	add	r5,r5,4

000002e0 e79a4005    574 	ldr	r4,[r10,r5]

000002e4 e3700001    575 	cmn	r0,1

                     576 ;191: 	{


                     577 

                     578 ;192: 		queue->head = queue->tail;


                     579 

000002e8 028a0b42    580 	addeq	r0,r10,66<<10

000002ec 05804000    581 	streq	r4,[r0]

                     582 ;193: 	}


                     583 ;194: 


                     584 ;195: 	tail = queue->tail;


                     585 

                     586 ;196: 	while (1)


                     587 

000002f0 e08a9005    588 	add	r9,r10,r5

000002f4 e2898004    589 	add	r8,r9,4

                     590 .L614:

                     591 ;197: 	{


                     592 

                     593 ;198: 		chunk = &queue->chunk[tail];


                     594 

000002f8 e0840284    595 	add	r0,r4,r4 lsl 5

000002fc e1a05100    596 	mov	r5,r0 lsl 2

                     597 ;199: 		if (maxDataSize > chunk->len)


                     598 

00000300 e1b520ba    599 	ldrh	r2,[r5,r10]!


                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4ag1.s
00000304 e1570002    600 	cmp	r7,r2

00000308 da000005    601 	ble	.L620

0000030c e2851004    602 	add	r1,r5,4

00000310 e1a00006    603 	mov	r0,r6

00000314 eb000000*   604 	bl	memcpy

                     605 ;200: 		{


                     606 

                     607 ;201: 			if (chunk->len == REPORT_CHUNK_SIZE)


                     608 

                     609 ;202: 			{


                     610 

                     611 ;203: 				//Закомментировано потому что выравнивание на 4 не гарантировано


                     612 ;204: 				//copyChunk(data, chunk->payload);


                     613 ;205: 				memcpy(data, chunk->payload, chunk->len);


                     614 

                     615 ;206: 			}


                     616 ;207: 			else


                     617 ;208: 			{


                     618 

                     619 ;209: 				memcpy(data, chunk->payload, chunk->len);


                     620 

                     621 ;210: 			}


                     622 ;211: 			copyCount += chunk->len;


                     623 

00000318 e1d520b0    624 	ldrh	r2,[r5]

0000031c e0866002    625 	add	r6,r6,r2

00000320 e08bb002    626 	add	fp,fp,r2

                     627 ;212: 			data += chunk->len;


                     628 

                     629 .L620:

00000324 e0477002    630 	sub	r7,r7,r2

00000328 e1a01008    631 	mov	r1,r8

0000032c e5910000    632 	ldr	r0,[r1]

00000330 e2844001    633 	add	r4,r4,1

00000334 e1540000    634 	cmp	r4,r0

00000338 e5d50002    635 	ldrb	r0,[r5,2]

0000033c a3a04000    636 	movge	r4,0

                     637 ;213: 		}


                     638 ;214: 


                     639 ;215: 		maxDataSize -= chunk->len;


                     640 

                     641 ;216: 		tail++;


                     642 

                     643 ;217: 		if (tail >= queue->chunkCount)


                     644 

                     645 

                     646 

                     647 ;220: 		}


                     648 ;221: 		if (chunk->lastFlag)


                     649 

00000340 e3500000    650 	cmp	r0,0

00000344 0affffeb    651 	beq	.L614

                     652 ;222: 		{


                     653 

                     654 ;223: 			chunk->lastFlag = 0;


                     655 

00000348 e3a00000    656 	mov	r0,0

0000034c e5c50002    657 	strb	r0,[r5,2]

                     658 ;224: 			break;


                     659 

                     660 ;225: 		}



                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4ag1.s
                     661 ;226: 		


                     662 ;227: 	}


                     663 ;228: 	queue->lastOverflowCount = queue->overflowCount;


                     664 

00000350 e3a00b42    665 	mov	r0,66<<10

00000354 e3a01b42    666 	mov	r1,66<<10

00000358 e281100c    667 	add	r1,r1,12

0000035c e791100a    668 	ldr	r1,[r1,r10]

00000360 e2800010    669 	add	r0,r0,16

00000364 e780100a    670 	str	r1,[r0,r10]

                     671 ;229: 	queue->tail = tail;


                     672 

00000368 e5894000    673 	str	r4,[r9]

                     674 ;230: 	unlockQueue(queue);


                     675 

0000036c e1a0000a    676 	mov	r0,r10

00000370 ebffff28*   677 	bl	unlockQueue

                     678 ;231: 	return copyCount;


                     679 

00000374 e1a0000b    680 	mov	r0,fp

                     681 .L605:

00000378 e28dd008    682 	add	sp,sp,8

0000037c e8bd8ff0    683 	ldmfd	[sp]!,{r4-fp,pc}

                     684 	.endf	ReportQueue_read

                     685 	.align	4

                     686 ;copyCount	fp	local

                     687 ;tail	r4	local

                     688 ;chunk	r5	local

                     689 

                     690 ;queue	r10	param

                     691 ;data	r6	param

                     692 ;maxDataSize	r7	param

                     693 

                     694 	.section ".bss","awb"

                     695 .L775:

                     696 	.data

                     697 	.text

                     698 

                     699 ;232: }


                     700 

                     701 ;233: 


                     702 ;234: void ReportQueue_purge(ReportQueue *queue)


                     703 	.align	4

                     704 	.align	4

                     705 ReportQueue_purge::

00000380 e92d4010    706 	stmfd	[sp]!,{r4,lr}

                     707 ;235: {


                     708 

                     709 ;236: 	lockQueue(queue);


                     710 

00000384 e1a04000    711 	mov	r4,r0

00000388 ebffff1c*   712 	bl	lockQueue

                     713 ;237: 	queue->head = queue->tail = 0;


                     714 

0000038c e2841b42    715 	add	r1,r4,66<<10

00000390 e3a00000    716 	mov	r0,0

00000394 e1a02000    717 	mov	r2,r0

00000398 e8810005    718 	stmea	[r1],{r0,r2}

                     719 ;238: 	queue->lastOverflowCount = queue->overflowCount = 0;


                     720 

0000039c e3a01b42    721 	mov	r1,66<<10


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4ag1.s
000003a0 e281100c    722 	add	r1,r1,12

000003a4 e7810004    723 	str	r0,[r1,r4]

000003a8 e3a01b42    724 	mov	r1,66<<10

000003ac e2811010    725 	add	r1,r1,16

000003b0 e7810004    726 	str	r0,[r1,r4]

                     727 ;239: 	unlockQueue(queue);


                     728 

000003b4 e1a00004    729 	mov	r0,r4

000003b8 e8bd4010    730 	ldmfd	[sp]!,{r4,lr}

000003bc eaffff15*   731 	b	unlockQueue

                     732 	.endf	ReportQueue_purge

                     733 	.align	4

                     734 

                     735 ;queue	r4	param

                     736 

                     737 	.section ".bss","awb"

                     738 .L830:

                     739 	.data

                     740 	.text

                     741 

                     742 ;240: }


                     743 

                     744 ;241: 


                     745 ;242: int ReportQueue_isEmpty(ReportQueue *queue)


                     746 	.align	4

                     747 	.align	4

                     748 ReportQueue_isEmpty::

                     749 ;243: {


                     750 

                     751 ;244: 	return queue->head == queue->tail;


                     752 

000003c0 e3a02b42    753 	mov	r2,66<<10

000003c4 e2822004    754 	add	r2,r2,4

000003c8 e2801b42    755 	add	r1,r0,66<<10

000003cc e7900002    756 	ldr	r0,[r0,r2]

000003d0 e5911000    757 	ldr	r1,[r1]

000003d4 e1500001    758 	cmp	r0,r1

000003d8 03a00001    759 	moveq	r0,1

000003dc 13a00000    760 	movne	r0,0

000003e0 e12fff1e*   761 	ret	

                     762 	.endf	ReportQueue_isEmpty

                     763 	.align	4

                     764 

                     765 ;queue	r0	param

                     766 

                     767 	.section ".bss","awb"

                     768 .L862:

                     769 	.data

                     770 	.text

                     771 

                     772 ;245: }


                     773 

                     774 ;246: 


                     775 ;247: int ReportQueue_isOverflow(ReportQueue *queue)


                     776 	.align	4

                     777 	.align	4

                     778 ReportQueue_isOverflow::

                     779 ;248: {


                     780 

                     781 ;249: 	return queue->lastOverflowCount != queue->overflowCount;


                     782 


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4ag1.s
000003e4 e2800b42    783 	add	r0,r0,66<<10

000003e8 e5901010    784 	ldr	r1,[r0,16]

000003ec e590000c    785 	ldr	r0,[r0,12]

000003f0 e0500001    786 	subs	r0,r0,r1

000003f4 13a00001    787 	movne	r0,1

000003f8 e12fff1e*   788 	ret	

                     789 	.endf	ReportQueue_isOverflow

                     790 	.align	4

                     791 

                     792 ;queue	r0	param

                     793 

                     794 	.section ".bss","awb"

                     795 .L894:

                     796 	.data

                     797 	.text

                     798 

                     799 ;250: }


                     800 	.align	4

                     801 

                     802 	.data

                     803 	.ghsnote version,6

                     804 	.ghsnote tools,3

                     805 	.ghsnote options,0

                     806 	.text

                     807 	.align	4

