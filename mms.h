#ifndef MMS_H_
#define MMS_H_

#include "out_queue.h"
#include "out_buffers.h"
#include "presentation.h"
#include "acse.h"
#include "IsoConnectionForward.h"
#include "mmsconnection.h"

#include <stdbool.h>

#define COTP_IN_BUF_SIZE 2048

//Параметры Ассоциации MMS
//!!! Это значение от былды - затычка
#define CONFIG_MMS_MAXIMUM_PDU_SIZE 65536

#define DEFAULT_MAX_SERV_OUTSTANDING_CALLING 5
#define DEFAULT_MAX_SERV_OUTSTANDING_CALLED 5
#define DEFAULT_DATA_STRUCTURE_NESTING_LEVEL 10

typedef enum
{
    MMS_ERROR,
    MMS_INITIATE,
    MMS_CONFIRMED_REQUEST,
    MMS_OK,
    MMS_CONCLUDE
} MmsIndication;



struct IsoConnection {
    int maxServOutstandingCalling;
    int maxServOutstandingCalled;
    int dataStructureNestingLevel;
    unsigned int maxPduSize;
    unsigned int lastInvokeId;

    unsigned char cotpInBuf[COTP_IN_BUF_SIZE];
    SessionOutBuffer* pCurrCotpOutBuf;
    SessionBuffers outBuffers;
    OutQueue outQueue;

    volatile bool connected;
    volatile bool sendThreadIsRunning;
    COTPConnection cotpConn;
    IsoPresentation presentation;
    AcseConnection acse;
    MmsConnection mmsConn;
    unsigned char isoOutBuf[DEFAULT_BUFFER_SIZE];

    int iedModelSize;
};

#ifndef MMS_STATUS_SERVICE
#define MMS_STATUS_SERVICE 1
#endif

#ifndef MMS_IDENTIFY_SERVICE
#define MMS_IDENTIFY_SERVICE 1
#endif

#ifndef MMS_FILE_SERVICE
#define MMS_FILE_SERVICE 1
#endif


void handleMMSConnection(SERVER_SOCKET socket);

#endif	//MMS_H_
