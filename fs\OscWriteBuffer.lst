                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8uo1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=OscWriteBuffer.c -o fs\gh_8uo1.o -list=fs/OscWriteBuffer.lst C:\Users\<USER>\AppData\Local\Temp\gh_8uo1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_8uo1.s
Source File: OscWriteBuffer.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      10 ;		fs/OscWriteBuffer.c -o fs/OscWriteBuffer.o

                      11 ;Source File:   fs/OscWriteBuffer.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:55:57 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include <stdlib.h>


                      21 ;2: #include <string.h>


                      22 ;3: #include <types.h>


                      23 ;4: #include "../local_types.h"


                      24 ;5: #include "OscWriteBuffer.h"


                      25 ;6: #include "OscFiles.h"


                      26 ;7: 


                      27 ;8: bool OscWriteBuffer_create(OscWriteBuffer *wb, size_t size)


                      28 ;9: {


                      29 ;10: 	void *p;


                      30 ;11: 


                      31 ;12: 	if (wb->p) // p должно быть NULL для корректного удаления


                      32 ;13: 	{


                      33 ;14: 		return false;


                      34 ;15: 	}


                      35 ;16: 


                      36 ;17: 	p = OscFiles_malloc(size);


                      37 ;18: 	if (!p)


                      38 ;19: 	{


                      39 ;20: 		return false;


                      40 ;21: 	}


                      41 ;22: 


                      42 ;23: 	wb->p = p;


                      43 ;24: 	wb->data = p;


                      44 ;25: 	wb->size = size;


                      45 ;26: 	wb->dataSize = 0;


                      46 ;27: 	wb->dataPos = 0;


                      47 ;28: 	return true;


                      48 ;29: }


                      49 ;30: 


                      50 ;31: bool OscWriteBuffer_attach(OscWriteBuffer *wb, unsigned char *p, size_t size)



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8uo1.s
                      51 ;32: {


                      52 ;33: 	wb->p = NULL;


                      53 ;34: 	wb->data = p;


                      54 ;35: 	wb->size = size;


                      55 ;36: 	wb->dataSize = 0;


                      56 ;37: 	wb->dataPos = 0;


                      57 ;38: 	return true;


                      58 ;39: }


                      59 ;40: 


                      60 ;41: void OscWriteBuffer_reset(OscWriteBuffer *wb)


                      61 ;42: {


                      62 ;43: 	wb->dataPos = 0;


                      63 ;44: 	wb->dataSize = 0;


                      64 ;45: }


                      65 ;46: 


                      66 ;47: static size_t getFreeSize(OscWriteBuffer *wb)


                      67 

                      68 ;50: }


                      69 

                      70 	.text

                      71 	.align	4

                      72 OscWriteBuffer_create::

00000000 e92d4030     73 	stmfd	[sp]!,{r4-r5,lr}

00000004 e1a04000     74 	mov	r4,r0

00000008 e5940004     75 	ldr	r0,[r4,4]

0000000c e1a05001     76 	mov	r5,r1

00000010 e3500000     77 	cmp	r0,0

00000014 1a000003     78 	bne	.L27

00000018 e1a00005     79 	mov	r0,r5

0000001c eb000000*    80 	bl	OscFiles_malloc

00000020 e3500000     81 	cmp	r0,0

00000024 1a000001     82 	bne	.L26

                      83 .L27:

00000028 e3a00000     84 	mov	r0,0

0000002c ea000005     85 	b	.L21

                      86 .L26:

00000030 e1a02000     87 	mov	r2,r0

00000034 e1a0c005     88 	mov	r12,r5

00000038 e3a05000     89 	mov	r5,0

0000003c e1a03005     90 	mov	r3,r5

00000040 e884102d     91 	stmea	[r4],{r0,r2-r3,r5,r12}

00000044 e3a00001     92 	mov	r0,1

                      93 .L21:

00000048 e8bd8030     94 	ldmfd	[sp]!,{r4-r5,pc}

                      95 	.endf	OscWriteBuffer_create

                      96 	.align	4

                      97 ;p	r0	local

                      98 

                      99 ;wb	r4	param

                     100 ;size	r5	param

                     101 

                     102 	.section ".bss","awb"

                     103 .L76:

                     104 	.data

                     105 	.text

                     106 

                     107 

                     108 	.align	4

                     109 	.align	4

                     110 OscWriteBuffer_attach::

0000004c e3a03000    111 	mov	r3,0


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8uo1.s
00000050 e1a0c003    112 	mov	r12,r3

00000054 e8a0100a    113 	stmea	[r0]!,{r1,r3,r12}

00000058 e1a01003    114 	mov	r1,r3

0000005c e8800006    115 	stmea	[r0],{r1-r2}

00000060 e3a00001    116 	mov	r0,1

00000064 e12fff1e*   117 	ret	

                     118 	.endf	OscWriteBuffer_attach

                     119 	.align	4

                     120 

                     121 ;wb	r0	param

                     122 ;p	r1	param

                     123 ;size	r2	param

                     124 

                     125 	.section ".bss","awb"

                     126 .L110:

                     127 	.data

                     128 	.text

                     129 

                     130 

                     131 	.align	4

                     132 	.align	4

                     133 OscWriteBuffer_reset::

00000068 e3a01000    134 	mov	r1,0

0000006c e5801008    135 	str	r1,[r0,8]

00000070 e580100c    136 	str	r1,[r0,12]

00000074 e12fff1e*   137 	ret	

                     138 	.endf	OscWriteBuffer_reset

                     139 	.align	4

                     140 

                     141 ;wb	r0	param

                     142 

                     143 	.section ".bss","awb"

                     144 .L142:

                     145 	.data

                     146 	.text

                     147 

                     148 

                     149 ;51: bool OscWriteBuffer_write(OscWriteBuffer *wb, void *data, size_t size)


                     150 	.align	4

                     151 	.align	4

                     152 OscWriteBuffer_write::

00000078 e92d40f0    153 	stmfd	[sp]!,{r4-r7,lr}

0000007c e2804008    154 	add	r4,r0,8

00000080 e1a07001    155 	mov	r7,r1

00000084 e1a05002    156 	mov	r5,r2

                     157 ;52: {


                     158 

                     159 ;53: 	size_t freeSize = getFreeSize(wb);


                     160 

                     161 ;48: {


                     162 

                     163 ;49: 	return wb->size - wb->dataSize - wb->dataPos;


                     164 

00000088 e8940049    165 	ldmfd	[r4],{r0,r3,r6}

0000008c e2444008    166 	sub	r4,r4,8

00000090 e1a01006    167 	mov	r1,r6

00000094 e1a0c000    168 	mov	r12,r0

00000098 e0410003    169 	sub	r0,r1,r3

0000009c e040000c    170 	sub	r0,r0,r12

                     171 ;54: 	// не хватает места


                     172 ;55: 	if (freeSize < size)



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8uo1.s
                     173 

000000a0 e1500005    174 	cmp	r0,r5

000000a4 25940000    175 	ldrhs	r0,[r4]

000000a8 2a00000d    176 	bhs	.L155

                     177 ;56: 	{


                     178 

                     179 ;57: 		void *p = wb->p;


                     180 

000000ac e5940004    181 	ldr	r0,[r4,4]

                     182 ;58: 		int newSize = wb->size * 2; // увеличивается в 2 раза


                     183 

000000b0 e1a06081    184 	mov	r6,r1 lsl 1

                     185 ;59: 		if (p == NULL) // статический буфер, нельзя перевыделить


                     186 

000000b4 e3500000    187 	cmp	r0,0

000000b8 0a000002    188 	beq	.L161

                     189 ;60: 		{


                     190 

                     191 ;61: 			return false;


                     192 

                     193 ;62: 		}


                     194 ;63: 	


                     195 ;64: 		p = OscFiles_realloc(p, newSize);


                     196 

000000bc e1a01006    197 	mov	r1,r6

000000c0 eb000000*   198 	bl	OscFiles_realloc

                     199 ;65: 		if (!p)


                     200 

000000c4 e3500000    201 	cmp	r0,0

                     202 .L161:

                     203 ;66: 		{


                     204 

                     205 ;67: 			return false;


                     206 

000000c8 03a00000    207 	moveq	r0,0

000000cc 0a00000d    208 	beq	.L149

                     209 .L160:

                     210 ;68: 		}


                     211 ;69: 


                     212 ;70: 		wb->p = p;


                     213 

000000d0 e1a01000    214 	mov	r1,r0

000000d4 e8840003    215 	stmea	[r4],{r0-r1}

                     216 ;71: 		wb->data = wb->p;


                     217 

                     218 ;72: 		wb->size = newSize;


                     219 

000000d8 e5846010    220 	str	r6,[r4,16]

000000dc e594300c    221 	ldr	r3,[r4,12]

000000e0 e594c008    222 	ldr	r12,[r4,8]

                     223 .L155:

                     224 ;73: 	}


                     225 ;74: 


                     226 ;75: 


                     227 ;76: 	memcpy(wb->data + wb->dataPos + wb->dataSize, data, size);


                     228 

000000e4 e1a02005    229 	mov	r2,r5

000000e8 e1a01007    230 	mov	r1,r7

000000ec e08c0000    231 	add	r0,r12,r0

000000f0 e0830000    232 	add	r0,r3,r0

000000f4 eb000000*   233 	bl	memcpy


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8uo1.s
                     234 ;77: 	wb->dataSize += size;


                     235 

000000f8 e594000c    236 	ldr	r0,[r4,12]

000000fc e0800005    237 	add	r0,r0,r5

00000100 e584000c    238 	str	r0,[r4,12]

                     239 ;78: 	return true;


                     240 

00000104 e3a00001    241 	mov	r0,1

                     242 .L149:

00000108 e8bd80f0    243 	ldmfd	[sp]!,{r4-r7,pc}

                     244 	.endf	OscWriteBuffer_write

                     245 	.align	4

                     246 ;p	r0	local

                     247 ;newSize	r6	local

                     248 

                     249 ;wb	r4	param

                     250 ;data	r7	param

                     251 ;size	r5	param

                     252 

                     253 	.section ".bss","awb"

                     254 .L235:

                     255 	.data

                     256 	.text

                     257 

                     258 ;79: }


                     259 

                     260 ;80: 


                     261 ;81: bool OscWriteBuffer_toWriteBuffer(OscWriteBuffer *dst, OscWriteBuffer *src)


                     262 	.align	4

                     263 	.align	4

                     264 OscWriteBuffer_toWriteBuffer::

0000010c e92d4070    265 	stmfd	[sp]!,{r4-r6,lr}

00000110 e1a06000    266 	mov	r6,r0

00000114 e1a05001    267 	mov	r5,r1

                     268 ;82: {


                     269 

                     270 ;83: 	unsigned char *data = OscWriteBuffer_data(src);


                     271 

00000118 e1a00005    272 	mov	r0,r5

0000011c eb00001a*   273 	bl	OscWriteBuffer_data

00000120 e1a04000    274 	mov	r4,r0

                     275 ;84: 	size_t size = OscWriteBuffer_dataLen(src);


                     276 

00000124 e1a00005    277 	mov	r0,r5

00000128 eb00001b*   278 	bl	OscWriteBuffer_dataLen

                     279 ;85: 	bool result =  OscWriteBuffer_write(dst,data, size);


                     280 

0000012c e1a01004    281 	mov	r1,r4

00000130 e1a02000    282 	mov	r2,r0

00000134 e1a00006    283 	mov	r0,r6

00000138 ebffffce*   284 	bl	OscWriteBuffer_write

0000013c e1b04000    285 	movs	r4,r0

                     286 ;86: 	if (result)


                     287 

                     288 ;87: 	{


                     289 

                     290 ;88: 		OscWriteBuffer_reset(src);


                     291 

00000140 11a00005    292 	movne	r0,r5

00000144 1bffffc7*   293 	blne	OscWriteBuffer_reset

                     294 ;89: 	}



                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8uo1.s
                     295 ;90: 


                     296 ;91: 	return result;


                     297 

00000148 e1a00004    298 	mov	r0,r4

0000014c e8bd8070    299 	ldmfd	[sp]!,{r4-r6,pc}

                     300 	.endf	OscWriteBuffer_toWriteBuffer

                     301 	.align	4

                     302 ;data	r4	local

                     303 ;result	r4	local

                     304 

                     305 ;dst	r6	param

                     306 ;src	r5	param

                     307 

                     308 	.section ".bss","awb"

                     309 .L306:

                     310 	.data

                     311 	.text

                     312 

                     313 ;92: }


                     314 

                     315 ;93: 


                     316 ;94: bool OscWriteBuffer_resize(OscWriteBuffer *wb, int len)


                     317 	.align	4

                     318 	.align	4

                     319 OscWriteBuffer_resize::

                     320 ;95: {


                     321 

                     322 ;96: 	size_t newLen = wb->dataSize + len;


                     323 

00000150 e590200c    324 	ldr	r2,[r0,12]

00000154 e0811002    325 	add	r1,r1,r2

                     326 ;97: 	if (newLen + wb->dataPos > wb->size)


                     327 

00000158 e5902008    328 	ldr	r2,[r0,8]

0000015c e5903010    329 	ldr	r3,[r0,16]

00000160 e0812002    330 	add	r2,r1,r2

00000164 e1520003    331 	cmp	r2,r3

                     332 ;98: 	{


                     333 

                     334 ;99: 		return false;


                     335 

00000168 83a00000    336 	movhi	r0,0

                     337 ;100: 	}


                     338 ;101: 	wb->dataSize += len;


                     339 

0000016c 9580100c    340 	strls	r1,[r0,12]

                     341 ;102: 	return true;


                     342 

00000170 93a00001    343 	movls	r0,1

00000174 e12fff1e*   344 	ret	

                     345 	.endf	OscWriteBuffer_resize

                     346 	.align	4

                     347 

                     348 ;wb	r0	param

                     349 ;len	r1	param

                     350 

                     351 	.section ".bss","awb"

                     352 .L358:

                     353 	.data

                     354 	.text

                     355 


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8uo1.s
                     356 ;103: }


                     357 

                     358 ;104: 


                     359 ;105: bool OscWriteBuffer_empty(OscWriteBuffer *wb) 


                     360 	.align	4

                     361 	.align	4

                     362 OscWriteBuffer_empty::

                     363 ;106: {


                     364 

                     365 ;107: 	return wb->dataSize == 0;


                     366 

00000178 e590000c    367 	ldr	r0,[r0,12]

0000017c e3500000    368 	cmp	r0,0

00000180 03a00001    369 	moveq	r0,1

00000184 13a00000    370 	movne	r0,0

00000188 e12fff1e*   371 	ret	

                     372 	.endf	OscWriteBuffer_empty

                     373 	.align	4

                     374 

                     375 ;wb	r0	param

                     376 

                     377 	.section ".bss","awb"

                     378 .L401:

                     379 	.data

                     380 	.text

                     381 

                     382 ;108: }


                     383 

                     384 ;109: 


                     385 ;110: void * OscWriteBuffer_data(OscWriteBuffer *wb)


                     386 	.align	4

                     387 	.align	4

                     388 OscWriteBuffer_data::

                     389 ;111: {


                     390 

                     391 ;112: 	return wb->data + wb->dataPos;


                     392 

0000018c e5901008    393 	ldr	r1,[r0,8]

00000190 e5902000    394 	ldr	r2,[r0]

00000194 e0810002    395 	add	r0,r1,r2

00000198 e12fff1e*   396 	ret	

                     397 	.endf	OscWriteBuffer_data

                     398 	.align	4

                     399 

                     400 ;wb	r0	param

                     401 

                     402 	.section ".bss","awb"

                     403 .L430:

                     404 	.data

                     405 	.text

                     406 

                     407 ;113: }


                     408 

                     409 ;114: 


                     410 ;115: size_t OscWriteBuffer_dataLen(OscWriteBuffer *wb)


                     411 	.align	4

                     412 	.align	4

                     413 OscWriteBuffer_dataLen::

                     414 ;116: {


                     415 

                     416 ;117: 	return wb->dataSize;



                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8uo1.s
                     417 

0000019c e590000c    418 	ldr	r0,[r0,12]

000001a0 e12fff1e*   419 	ret	

                     420 	.endf	OscWriteBuffer_dataLen

                     421 	.align	4

                     422 

                     423 ;wb	r0	param

                     424 

                     425 	.section ".bss","awb"

                     426 .L462:

                     427 	.data

                     428 	.text

                     429 

                     430 ;118: }


                     431 

                     432 ;119: 


                     433 ;120: size_t OscWriteBuffer_size(OscWriteBuffer *wb)


                     434 	.align	4

                     435 	.align	4

                     436 OscWriteBuffer_size::

                     437 ;121: {


                     438 

                     439 ;122: 	return wb->size - wb->dataSize;


                     440 

000001a4 e5901010    441 	ldr	r1,[r0,16]

000001a8 e590000c    442 	ldr	r0,[r0,12]

000001ac e0410000    443 	sub	r0,r1,r0

000001b0 e12fff1e*   444 	ret	

                     445 	.endf	OscWriteBuffer_size

                     446 	.align	4

                     447 

                     448 ;wb	r0	param

                     449 

                     450 	.section ".bss","awb"

                     451 .L494:

                     452 	.data

                     453 	.text

                     454 

                     455 ;123: }


                     456 

                     457 ;124: 


                     458 ;125: void OscWriteBuffer_destroy(OscWriteBuffer *wb)


                     459 	.align	4

                     460 	.align	4

                     461 OscWriteBuffer_destroy::

                     462 ;126: {


                     463 

                     464 ;127: 	// буфер создан через выделение памяти


                     465 ;128: 	if (wb->p)


                     466 

000001b4 e5900004    467 	ldr	r0,[r0,4]

000001b8 e3500000    468 	cmp	r0,0

                     469 ;129: 	{


                     470 

                     471 ;130: 		OscFiles_free(wb->p);


                     472 

000001bc 1a000000*   473 	bne	OscFiles_free

000001c0 e12fff1e*   474 	ret	

                     475 	.endf	OscWriteBuffer_destroy

                     476 	.align	4

                     477 


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8uo1.s
                     478 ;wb	r0	param

                     479 

                     480 	.section ".bss","awb"

                     481 .L536:

                     482 	.data

                     483 	.text

                     484 

                     485 ;132: 	}


                     486 ;133: 	// буфер создан OscWriteBuffer_attach


                     487 ;134: }


                     488 

                     489 ;135: 


                     490 ;136: size_t OscWriteBuffer_toBufferView(BufferView *bv, OscWriteBuffer *wb)


                     491 	.align	4

                     492 	.align	4

                     493 OscWriteBuffer_toBufferView::

000001c4 e92d4070    494 	stmfd	[sp]!,{r4-r6,lr}

000001c8 e1a04000    495 	mov	r4,r0

000001cc e1a05001    496 	mov	r5,r1

                     497 ;137: {


                     498 

                     499 ;138: 	int writenSize;


                     500 ;139: 


                     501 ;140: 	if (OscWriteBuffer_empty(wb))


                     502 

000001d0 e1a00005    503 	mov	r0,r5

000001d4 ebffffe7*   504 	bl	OscWriteBuffer_empty

000001d8 e3500000    505 	cmp	r0,0

                     506 ;141: 	{


                     507 

                     508 ;142: 		return 0;


                     509 

000001dc 13a00000    510 	movne	r0,0

000001e0 1a000012    511 	bne	.L547

                     512 ;143: 	}


                     513 ;144: 


                     514 ;145: 	writenSize = BufferView_writeData(bv, OscWriteBuffer_data(wb),


                     515 

000001e4 e1a00005    516 	mov	r0,r5

000001e8 ebffffe7*   517 	bl	OscWriteBuffer_data

000001ec e1a06000    518 	mov	r6,r0

000001f0 e1a00005    519 	mov	r0,r5

000001f4 ebffffe8*   520 	bl	OscWriteBuffer_dataLen

000001f8 e1a01006    521 	mov	r1,r6

000001fc e1a02000    522 	mov	r2,r0

00000200 e1a00004    523 	mov	r0,r4

00000204 eb000000*   524 	bl	BufferView_writeData

00000208 e5951008    525 	ldr	r1,[r5,8]

0000020c e1a04000    526 	mov	r4,r0

                     527 ;146: 		OscWriteBuffer_dataLen(wb));


                     528 ;147: 	wb->dataPos += writenSize;


                     529 

00000210 e0811004    530 	add	r1,r1,r4

00000214 e5851008    531 	str	r1,[r5,8]

                     532 ;148: 	wb->dataSize -= writenSize;


                     533 

00000218 e595100c    534 	ldr	r1,[r5,12]

0000021c e0511004    535 	subs	r1,r1,r4

00000220 e585100c    536 	str	r1,[r5,12]

                     537 ;149: 


                     538 ;150: 	// все записали



                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8uo1.s
                     539 ;151: 	if (wb->dataSize == 0)


                     540 

                     541 ;152: 	{


                     542 

                     543 ;153: 		OscWriteBuffer_reset(wb);


                     544 

00000224 01a00005    545 	moveq	r0,r5

00000228 0bffff8e*   546 	bleq	OscWriteBuffer_reset

                     547 ;154: 	}


                     548 ;155: 	return writenSize;


                     549 

0000022c e1a00004    550 	mov	r0,r4

                     551 .L547:

00000230 e8bd8070    552 	ldmfd	[sp]!,{r4-r6,pc}

                     553 	.endf	OscWriteBuffer_toBufferView

                     554 	.align	4

                     555 ;writenSize	r4	local

                     556 

                     557 ;bv	r4	param

                     558 ;wb	r5	param

                     559 

                     560 	.section ".bss","awb"

                     561 .L599:

                     562 	.data

                     563 	.text

                     564 

                     565 ;156: }


                     566 	.align	4

                     567 

                     568 	.data

                     569 	.ghsnote version,6

                     570 	.ghsnote tools,3

                     571 	.ghsnote options,0

                     572 	.text

                     573 	.align	4

