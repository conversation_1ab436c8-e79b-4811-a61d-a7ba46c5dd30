#pragma once
#include "stringView.h"
#include "bufView.h"
#include "local_types.h"
#include "FS/FSTypes.h"

bool fs_init(void);

//! Заполняет fileInfo информацией о первом найденном файле
//! Буфер для имени должен быть пустым.
FNameErrCode fs_findFirst(StringView* dirName, StringView* startFileName,
	FSFindData* fileInfo, BufferView* bufForName);

//! Заполняет fileInfo информацией об очередном найденном файле
//! Буфер для имени должен быть пустым.
//! Индекс файла увеличсивается на 1. Если последний файл в директрии, 
//! то индекс файла становится 0, а индекс директория увеличивается на 1.
//! Структура fileInfo должна быть заполнена путём вызова fs_findFirst
//! или fs_findNext
//! После вызова fileInfo содержит информацию о файле
FNameErrCode fs_findNext(FSFindData* fileInfo, BufferView* bufForName);

//! закрытие поиска (обязательно должна быть парой к fs_findFirst в независимости от результата)
void fs_findClose(FSFindData* fileInfo);

bool fs_fileOpen(StringView* fullFileName, size_t startPos, uint32_t* frsmID, FSFileAttr* attr);
bool fs_fileClose(uint32_t frsmID);
bool fs_fileRead(uint32_t frsmID, BufferView* fileReadBuf, bool *moreFollows);

