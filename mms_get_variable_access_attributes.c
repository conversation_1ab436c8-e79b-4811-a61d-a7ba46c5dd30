#include "mms_get_variable_access_attributes.h"
#include "AsnEncoding.h"  
#include "MmsConst.h"
#include "mmsservices.h"
#include "iedmodel.h"
#include "mms_error.h"
#include <debug.h>

#include <stddef.h>
#include <string.h>

static int encodeGetVariableAccessAttrResponse(unsigned int invokeId, int objectPos,
                              unsigned char* outBuf)
{
    int bufPos = 0;
    unsigned int accessResultSize;
    unsigned int invokeIdSize;
    unsigned int confirmedResponseContentSize;
    unsigned int fullConfirmedResponseSize;

    //==============determine BER encoded message sizes==============
    //Общий размер всех значений
    accessResultSize = encodeObjectAccessAttrs(NULL, bufPos, objectPos, TRUE, TRUE);

    //Вместе с тэгом 0xA6 (тэг + размер размера данных + размер данных)
    confirmedResponseContentSize = 1
            + BerEncoder_determineLengthSize(accessResultSize)
            + accessResultSize;

    invokeIdSize = BerEncoder_UInt32determineEncodedSize(invokeId) + 2;


    //Для тэга 0xA1
    fullConfirmedResponseSize = invokeIdSize
            + confirmedResponseContentSize;


    //================ encode message ============================
    bufPos = 0;

    // confirmed response PDU
    bufPos = BerEncoder_encodeTL(0xa1,  fullConfirmedResponseSize, outBuf, bufPos);

    // invoke id
    bufPos = BerEncoder_encodeTL(ASN_INTEGER, invokeIdSize - 2, outBuf, bufPos);
    bufPos = BerEncoder_encodeUInt32(invokeId, outBuf, bufPos);

    // confirmed-service-response getVariableAccessAttributes
    bufPos = BerEncoder_encodeTL(0xa6, accessResultSize,
                                 outBuf, bufPos);

    // encode access results
    bufPos = encodeObjectAccessAttrs(outBuf, bufPos, objectPos, FALSE, TRUE);


    return bufPos;
}

static int getVariableAccessAttr(unsigned int invokeId,
                             uint8_t* domainId, int domainIdLen,
                             uint8_t* itemId, int itemIdLen,
                             unsigned char* outBuf)
{
    //TODO выделить функцию получения объекта по именам
    int ldPos;
    int objectPos;

    ldPos = findDomainSection(IED_VMD_DATA_SECTION, domainId,domainIdLen);
    debugSendUshort("ldPos:", ldPos);
    if(ldPos == 0)
    {
        return 0;
    }
    objectPos = findObjectByPath(ldPos,itemId,itemIdLen);
    debugSendUshort("objectPos:", objectPos);
    if(objectPos == 0)
    {
        return 0;
    }
    return encodeGetVariableAccessAttrResponse(invokeId, objectPos, outBuf);
}

int mms_handleGetVariableAccessAttr(MmsConnection* mmsConn,
                                 unsigned char* inBuf, int bufPos, int maxBufPos,
                                  unsigned int invokeId, unsigned char* response)
{
    /*
    return mms_createMmsRejectPdu(invokeId,
                           MMS_ERROR_REJECT_UNRECOGNIZED_SERVICE, response);
                           */

    uint8_t* domainIdStr = NULL;
    uint8_t* itemIdStr = NULL;
    int domainIdLen;
    int itemIdLen;
    uint8_t tag;
    int length;
	int result;

    while (bufPos < maxBufPos)
    {
        tag = inBuf[bufPos++];

        bufPos = BerDecoder_decodeLength(inBuf, &length, bufPos, maxBufPos);

        if (bufPos < 0)
        {
            return 0;
        }

        if (ASN_VARIABLE_SPECIFICATION_OBJECT_NAME == tag)
        {
            bufPos = BerDecoder_DecodeObjectName(inBuf, bufPos, maxBufPos,
                &itemIdStr, &itemIdLen, &domainIdStr, &domainIdLen);
            if (bufPos < 0)
            {
                debugSendText("!!!!!Object name error");
                return -1;
            }            
        }
        else {
            debugSendText("!!!!!Unknown tag");
            return -1;
        }
    }		

	result = getVariableAccessAttr(invokeId,
		domainIdStr, domainIdLen, itemIdStr, itemIdLen,
		response);
	if (result < 1)
	{
		return CreateMmsConfirmedErrorPdu(invokeId, response,
			MMS_ERROR_ACCESS_OBJECT_NON_EXISTENT);
	}

	//TODO Обработать случай когда domainIdStr и itemIdStr неинициализированы
	return result;
}
