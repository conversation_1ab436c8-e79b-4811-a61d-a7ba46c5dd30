                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_d801.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=netTools.c -o gh_d801.o -list=netTools.lst C:\Users\<USER>\AppData\Local\Temp\gh_d801.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_d801.s
Source File: netTools.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile netTools.c -o

                      10 ;		netTools.o

                      11 ;Source File:   netTools.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Oct 07 09:39:26 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "netTools.h"


                      21 ;2: 


                      22 ;3: #include <nw.h>


                      23 ;4: #include <string.h>


                      24 ;5: #include <Clib.h> // Idle


                      25 ;6: #include <ethbuslib.h>


                      26 ;7: #include <process.h>


                      27 ;8: #include <system/System.h>


                      28 ;9: #include "debug.h"


                      29 ;10: #include <stdbool.h>


                      30 ;11: 


                      31 ;12: // в зависимости от режима работы инициализируются указатели


                      32 ;13: static bool (*_getIfFuncPtr)(unsigned char ifNum, void **pNetIf);


                      33 ;14: static bool (*_getMacFuncPtr)(void* netIf, uint8_t mac[6]);


                      34 ;15: static bool (*_sendFuncPtr)(void* netIf, void* data, size_t byteCount);


                      35 ;16: 


                      36 ;17: // для межпроцессорной шины


                      37 ;18: static EthBusInterface *_ethbus;


                      38 ;19: static int _gooseReceiverSlotNum;


                      39 ;20: static int _gooseReceiverDescr = -1;


                      40 ;21: 


                      41 ;22: //Контроллер присоединения или что-то другое


                      42 ;23: static bool _isBC;


                      43 ;24: 


                      44 ;25: // =========== интерфейс межпроцессорной шины =================================


                      45 ;26: static bool ethBusGetIf(unsigned char ifNum, void **pNetIf)


                      46 	.text

                      47 	.align	4

                      48 ethBusGetIf:

00000000 e92d4010     49 	stmfd	[sp]!,{r4,lr}

                      50 ;27: {



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_d801.s
                      51 

                      52 ;28: 	


                      53 ;29: 	// интерфейс уже проинициализирован


                      54 ;30: 	if (_gooseReceiverDescr >= 0)


                      55 

00000004 e59f40b8*    56 	ldr	r4,.L64

00000008 e5940000     57 	ldr	r0,[r4]

0000000c e3500000     58 	cmp	r0,0

                      59 ;43: 	}


                      60 ;44: 	


                      61 ;45: 	return TRUE;


                      62 

00000010 a3a00001     63 	movge	r0,1

00000014 aa00000b     64 	bge	.L2

                      65 ;31: 	{


                      66 

                      67 ;32: 		return TRUE;


                      68 

                      69 ;33: 	}


                      70 ;34: 	


                      71 ;35: 	// TODO: здесь нужно получить номер слота модуля связи на шине процесса


                      72 ;36: 	// пока отправляется по широковещательному


                      73 ;37: 	_gooseReceiverSlotNum = BA_GOOSE_REPEATER;


                      74 

00000018 e59fc0a8*    75 	ldr	r12,.L65

0000001c e3a00085     76 	mov	r0,133

00000020 e58c0000     77 	str	r0,[r12]

                      78 ;38: 	


                      79 ;39: 	_gooseReceiverDescr = _ethbus->open(PROTO_GOOSE_REPEATER);


                      80 

00000024 e59fc0a0*    81 	ldr	r12,.L66

00000028 e59c0000     82 	ldr	r0,[r12]

0000002c e590c028     83 	ldr	r12,[r0,40]

00000030 e3a0000d     84 	mov	r0,13

00000034 e1a0e00f     85 	mov	lr,pc

00000038 e12fff1c*    86 	bx	r12

0000003c e5840000     87 	str	r0,[r4]

                      88 ;40: 	if (_gooseReceiverDescr < 0)


                      89 

                      90 

                      91 

                      92 

00000040 e1a00fa0     93 	mov	r0,r0 lsr 31

00000044 e2200001     94 	eor	r0,r0,1

                      95 .L2:

00000048 e8bd4010     96 	ldmfd	[sp]!,{r4,lr}

0000004c e12fff1e*    97 	ret	

                      98 	.endf	ethBusGetIf

                      99 	.align	4

                     100 

                     101 ;ifNum	none	param

                     102 ;pNetIf	none	param

                     103 

                     104 	.data

                     105 .L49:

                     106 	.section ".bss","awb"

00000000 00000000    107 _getIfFuncPtr:	.space	4

00000004 00000000    108 _getMacFuncPtr:	.space	4

00000008 00000000    109 _sendFuncPtr:	.space	4

0000000c 00000000    110 _ethbus:	.space	4

00000010 00000000    111 _gooseReceiverSlotNum:	.space	4


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_d801.s
                     112 	.data

                     113 .L50:

00000000 ffffffff    114 _gooseReceiverDescr:	.data.b	255,255,255,255

                     115 	.type	_gooseReceiverDescr,$object

                     116 	.size	_gooseReceiverDescr,4

                     117 	.section ".bss","awb"

00000014 00         118 _isBC:	.space	1

                     119 	.data

                     120 	.text

                     121 

                     122 ;46: }


                     123 

                     124 ;47: static bool ethBusGetMac(void* netIf, uint8_t mac[6])


                     125 	.align	4

                     126 	.align	4

                     127 ethBusGetMac:

00000050 e92d4000    128 	stmfd	[sp]!,{lr}

                     129 ;48: {


                     130 

                     131 ;49: 	memset(mac,0,6);


                     132 

00000054 e3a02006    133 	mov	r2,6

00000058 e1a00001    134 	mov	r0,r1

0000005c e3a01000    135 	mov	r1,0

00000060 eb000000*   136 	bl	memset

                     137 ;50: 	return TRUE;	


                     138 

00000064 e3a00001    139 	mov	r0,1

00000068 e8bd4000    140 	ldmfd	[sp]!,{lr}

0000006c e12fff1e*   141 	ret	

                     142 	.endf	ethBusGetMac

                     143 	.align	4

                     144 

                     145 ;netIf	none	param

                     146 ;mac	r1	param

                     147 

                     148 	.section ".bss","awb"

                     149 .L94:

                     150 	.data

                     151 	.text

                     152 

                     153 ;51: }


                     154 

                     155 ;52: 


                     156 ;53: static bool ethBusSend(void* netIf, void* data, size_t byteCount)


                     157 	.align	4

                     158 	.align	4

                     159 ethBusSend:

00000070 e92d4010    160 	stmfd	[sp]!,{r4,lr}

                     161 ;54: {


                     162 

                     163 ;55: 	_ethbus->sendTo(_gooseReceiverDescr, _gooseReceiverSlotNum,data,byteCount);


                     164 

00000074 e59fc050*   165 	ldr	r12,.L66

00000078 e89c4001    166 	ldmfd	[r12],{r0,lr}

0000007c e1a03002    167 	mov	r3,r2

00000080 e1a02001    168 	mov	r2,r1

00000084 e59fc044*   169 	ldr	r12,.L132

00000088 e5904030    170 	ldr	r4,[r0,48]

0000008c e59c0000    171 	ldr	r0,[r12]

00000090 e1a0100e    172 	mov	r1,lr


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_d801.s
00000094 e1a0e00f    173 	mov	lr,pc

00000098 e12fff14*   174 	bx	r4

                     175 ;56: 	return TRUE;


                     176 

0000009c e3a00001    177 	mov	r0,1

000000a0 e8bd4010    178 	ldmfd	[sp]!,{r4,lr}

000000a4 e12fff1e*   179 	ret	

                     180 	.endf	ethBusSend

                     181 	.align	4

                     182 

                     183 ;netIf	none	param

                     184 ;data	r1	param

                     185 ;byteCount	r2	param

                     186 

                     187 	.data

                     188 	.text

                     189 

                     190 ;57: }


                     191 

                     192 ;58: 


                     193 ;59: // ============================================================================


                     194 ;60: 


                     195 ;61: 


                     196 ;62: 


                     197 ;63: 


                     198 ;64: // =========== сетевой интерфейс ==============================================


                     199 ;65: static bool networkGetIf(unsigned char ifNum, void **pNetIf)


                     200 	.align	4

                     201 	.align	4

                     202 networkGetIf:

000000a8 e92d4030    203 	stmfd	[sp]!,{r4-r5,lr}

000000ac e1a04000    204 	mov	r4,r0

000000b0 e1a05001    205 	mov	r5,r1

                     206 ;66: {


                     207 

                     208 ;67: 	NetifT*  netIf;


                     209 ;68:     // сетевые интерфейсы добавляются "пачкой" в критической секции,


                     210 ;69:     // поэтому ждем пока добавится интерфейс по-умолчанию и соответственно


                     211 ;70:     // добавятся остальные


                     212 ;71:     while(nwGetDefaultNetif() == NULL) Idle();


                     213 

000000b4 eb000000*   214 	bl	nwGetDefaultNetif

000000b8 e3500000    215 	cmp	r0,0

000000bc 1a000008    216 	bne	.L136

                     217 .L137:

000000c0 ea000003    218 	b	.L222

                     219 	.align	4

                     220 .L64:

000000c4 00000000*   221 	.data.w	.L49

                     222 	.type	.L64,$object

                     223 	.size	.L64,4

                     224 

                     225 .L65:

000000c8 00000000*   226 	.data.w	_gooseReceiverSlotNum

                     227 	.type	.L65,$object

                     228 	.size	.L65,4

                     229 

                     230 .L66:

000000cc 00000000*   231 	.data.w	_ethbus

                     232 	.type	.L66,$object

                     233 	.size	.L66,4


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_d801.s
                     234 

                     235 .L132:

000000d0 00000000*   236 	.data.w	.L50

                     237 	.type	.L132,$object

                     238 	.size	.L132,4

                     239 

                     240 .L222:

                     241 

000000d4 e6000010    242 	.word	0xE6000010

                     243 

000000d8 eb000000*   244 	bl	nwGetDefaultNetif

000000dc e3500000    245 	cmp	r0,0

000000e0 0afffff6    246 	beq	.L137

                     247 .L136:

                     248 ;72: 


                     249 ;73:     // здесь уже все возможные интерфейсы должны быть добавлены


                     250 ;74:     netIf = nwGetNetif(ifNum);


                     251 

000000e4 e1a00004    252 	mov	r0,r4

000000e8 eb000000*   253 	bl	nwGetNetif

                     254 ;75:     if(netIf == NULL)


                     255 

000000ec e3500000    256 	cmp	r0,0

                     257 ;76:     {


                     258 

                     259 ;77:         return FALSE;


                     260 

000000f0 020000ff    261 	andeq	r0,r0,255

                     262 ;78:     }


                     263 ;79:     *pNetIf = netIf;


                     264 

000000f4 15850000    265 	strne	r0,[r5]

                     266 ;80:     return TRUE;


                     267 

000000f8 13a00001    268 	movne	r0,1

000000fc e8bd4030    269 	ldmfd	[sp]!,{r4-r5,lr}

00000100 e12fff1e*   270 	ret	

                     271 	.endf	networkGetIf

                     272 	.align	4

                     273 ;netIf	r0	local

                     274 

                     275 ;ifNum	r4	param

                     276 ;pNetIf	r5	param

                     277 

                     278 	.section ".bss","awb"

                     279 .L202:

                     280 	.data

                     281 	.text

                     282 

                     283 ;81: }


                     284 

                     285 ;82: 


                     286 ;83: static bool networkGetMac(void* netIf, uint8_t mac[6])


                     287 	.align	4

                     288 	.align	4

                     289 networkGetMac:

00000104 e92d4010    290 	stmfd	[sp]!,{r4,lr}

00000108 e1a04001    291 	mov	r4,r1

                     292 ;84: {


                     293 

                     294 ;85: 	//unsigned char *nwGetMac(NetifT *netif);



                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_d801.s
                     295 ;86:     uint8_t* result  = nwGetMac(netIf);


                     296 

0000010c eb000000*   297 	bl	nwGetMac

                     298 ;87:     memcpy(mac, result, 6);


                     299 

00000110 e1a01000    300 	mov	r1,r0

00000114 e1a00004    301 	mov	r0,r4

00000118 e3a02006    302 	mov	r2,6

0000011c eb000000*   303 	bl	memcpy

                     304 ;88:     return TRUE;	


                     305 

00000120 e3a00001    306 	mov	r0,1

00000124 e8bd4010    307 	ldmfd	[sp]!,{r4,lr}

00000128 e12fff1e*   308 	ret	

                     309 	.endf	networkGetMac

                     310 	.align	4

                     311 

                     312 ;netIf	none	param

                     313 ;mac	r4	param

                     314 

                     315 	.section ".bss","awb"

                     316 .L254:

                     317 	.data

                     318 	.text

                     319 

                     320 ;89: }


                     321 

                     322 ;90: 


                     323 ;91: static bool networkSend(void* netIf, void* data, size_t byteCount)


                     324 	.align	4

                     325 	.align	4

                     326 networkSend:

0000012c e92d4000    327 	stmfd	[sp]!,{lr}

                     328 ;92: {


                     329 

                     330 ;93: 	 return 0 == nwEthSend(netIf, data, byteCount);


                     331 

00000130 eb000000*   332 	bl	nwEthSend

00000134 e3500000    333 	cmp	r0,0

00000138 03a00001    334 	moveq	r0,1

0000013c 13a00000    335 	movne	r0,0

00000140 e8bd4000    336 	ldmfd	[sp]!,{lr}

00000144 e12fff1e*   337 	ret	

                     338 	.endf	networkSend

                     339 	.align	4

                     340 

                     341 ;netIf	none	param

                     342 ;data	none	param

                     343 ;byteCount	none	param

                     344 

                     345 	.section ".bss","awb"

                     346 .L289:

                     347 	.data

                     348 	.text

                     349 

                     350 ;94: }


                     351 

                     352 ;95: // ============================================================================


                     353 ;96: 


                     354 ;97: //Комментарий для проверки кодировки


                     355 ;98: bool NetTools_init(void)



                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_d801.s
                     356 	.align	4

                     357 	.align	4

                     358 NetTools_init::

00000148 e92d4030    359 	stmfd	[sp]!,{r4-r5,lr}

                     360 ;99: {


                     361 

                     362 ;100: 	_HANDLE hModule = _GetModuleHandle("SYSTEM");


                     363 

0000014c e28f0000*   364 	adr	r0,.L418

00000150 e59f4110*   365 	ldr	r4,.L419

00000154 eb000000*   366 	bl	_GetModuleHandle

                     367 ;101: 	_FindRomModule findRomModule; 


                     368 ;102: 	if (!hModule)


                     369 

00000158 e3500000    370 	cmp	r0,0

0000015c 0a000002    371 	beq	.L302

                     372 ;103: 	{


                     373 

                     374 ;104: 		TRACE("NetTools_init: SYSTEM not found");


                     375 ;105: 		return FALSE;


                     376 

                     377 ;106: 	}


                     378 ;107: 	


                     379 ;108: 	findRomModule = (_FindRomModule)_GetProcAddress(hModule,(char*)SYS_FRM);


                     380 

00000160 e3a0118a    381 	mov	r1,0x80000022

00000164 eb000000*   382 	bl	_GetProcAddress

00000168 e1b05000    383 	movs	r5,r0

                     384 ;109: 	if (!findRomModule)


                     385 

                     386 .L302:

                     387 ;110: 	{


                     388 

                     389 ;111: 		TRACE("NetTools_init: findRomModule not found");


                     390 ;112: 		return FALSE;


                     391 

0000016c 03a00000    392 	moveq	r0,0

00000170 0a000017    393 	beq	.L296

                     394 .L304:

00000174 eb000000*   395 	bl	ethBusGetInterface

00000178 e584000c    396 	str	r0,[r4,12]

0000017c e3500000    397 	cmp	r0,0

00000180 13a00001    398 	movne	r0,1

00000184 e5c40014    399 	strb	r0,[r4,20]

                     400 ;113: 	}


                     401 ;114: 	


                     402 ;115: 	// ethbus инициализируется всегда


                     403 ;116: 	_ethbus = 	ethBusGetInterface();


                     404 

                     405 ;117: 	_isBC = _ethbus != NULL;


                     406 

                     407 ;118: 	if (!_ethbus)


                     408 

                     409 ;119: 	{


                     410 

                     411 ;120: 		TRACE("NetTools_init: ethbus not found");


                     412 ;121: 		


                     413 ;122: 	}


                     414 ;123: 	// в зависимости от того, в каком модуле связи находится MMServer,


                     415 ;124: 	// по-разному инициализируются интерфейсы посылки


                     416 ;125: 	



                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_d801.s
                     417 ;126: 	// модуль находится в "дополнительной плате" (только для КП)


                     418 ;127: 	if (_isBC && findRomModule(NULL,'GFCN'))


                     419 

00000188 e3500000    420 	cmp	r0,0

0000018c 0a00000b    421 	beq	.L306

00000190 e59f10d4*   422 	ldr	r1,.L420

00000194 e3a00000    423 	mov	r0,0

00000198 e1a0e00f    424 	mov	lr,pc

0000019c e12fff15*   425 	bx	r5

000001a0 e3500000    426 	cmp	r0,0

000001a4 0a000005    427 	beq	.L306

000001a8 e59f20c0*   428 	ldr	r2,.L421

                     429 ;128: 	{


                     430 

                     431 ;129: 		_getIfFuncPtr = ethBusGetIf;


                     432 

                     433 ;130: 		_getMacFuncPtr = ethBusGetMac;


                     434 

                     435 ;131: 		_sendFuncPtr = ethBusSend;


                     436 

000001ac e59f10c0*   437 	ldr	r1,.L422

000001b0 e59f00c0*   438 	ldr	r0,.L423

000001b4 e8840007    439 	stmea	[r4],{r0-r2}

                     440 ;138: 	}


                     441 ;139: 	


                     442 ;140: 	return TRUE;


                     443 

000001b8 e3a00001    444 	mov	r0,1

000001bc ea000004    445 	b	.L296

                     446 .L306:

                     447 ;132: 	}


                     448 ;133: 	else // модуль находится в плате с шиной процесса или УСО


                     449 ;134: 	{


                     450 

                     451 ;135: 		_getIfFuncPtr = networkGetIf;


                     452 

000001c0 e59f20b4*   453 	ldr	r2,.L424

000001c4 e59f10b4*   454 	ldr	r1,.L425

                     455 ;137: 		_sendFuncPtr = networkSend;


                     456 

000001c8 e59f00b4*   457 	ldr	r0,.L426

                     458 ;136: 		_getMacFuncPtr = networkGetMac;


                     459 

000001cc e8840007    460 	stmea	[r4],{r0-r2}

                     461 ;138: 	}


                     462 ;139: 	


                     463 ;140: 	return TRUE;


                     464 

000001d0 e3a00001    465 	mov	r0,1

                     466 .L296:

000001d4 e8bd8030    467 	ldmfd	[sp]!,{r4-r5,pc}

                     468 	.endf	NetTools_init

                     469 	.align	4

                     470 ;hModule	r1	local

                     471 ;findRomModule	r5	local

                     472 ;.L398	.L401	static

                     473 

                     474 	.data

                     475 	.text

                     476 

                     477 ;141: }



                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_d801.s
                     478 

                     479 ;142: 


                     480 ;143: bool NetTools_getIf(uint8_t ifNum, void** pNetIf)


                     481 	.align	4

                     482 	.align	4

                     483 NetTools_getIf::

000001d8 e92d4100    484 	stmfd	[sp]!,{r8,lr}

                     485 ;144: {	


                     486 

                     487 ;145:    return _getIfFuncPtr(ifNum, pNetIf);


                     488 

000001dc e59f8084*   489 	ldr	r8,.L419

000001e0 e598c000    490 	ldr	r12,[r8]

000001e4 e1a0e00f    491 	mov	lr,pc

000001e8 e12fff1c*   492 	bx	r12

000001ec e8bd8100    493 	ldmfd	[sp]!,{r8,pc}

                     494 	.endf	NetTools_getIf

                     495 	.align	4

                     496 

                     497 ;ifNum	none	param

                     498 ;pNetIf	none	param

                     499 

                     500 	.data

                     501 	.text

                     502 

                     503 ;146: }


                     504 

                     505 ;147: 


                     506 ;148: bool NetTools_getMac(void* netIf, uint8_t mac[6])


                     507 	.align	4

                     508 	.align	4

                     509 NetTools_getMac::

000001f0 e92d4100    510 	stmfd	[sp]!,{r8,lr}

                     511 ;149: {


                     512 

                     513 ;150:     return _getMacFuncPtr(netIf,mac);


                     514 

000001f4 e59f808c*   515 	ldr	r8,.L487

000001f8 e598c000    516 	ldr	r12,[r8]

000001fc e1a0e00f    517 	mov	lr,pc

00000200 e12fff1c*   518 	bx	r12

00000204 e8bd8100    519 	ldmfd	[sp]!,{r8,pc}

                     520 	.endf	NetTools_getMac

                     521 	.align	4

                     522 

                     523 ;netIf	none	param

                     524 ;mac	none	param

                     525 

                     526 	.data

                     527 	.text

                     528 

                     529 ;151: }


                     530 

                     531 ;152: 


                     532 ;153: bool NetTools_send(void* netIf, void* data, size_t byteCount)


                     533 	.align	4

                     534 	.align	4

                     535 NetTools_send::

00000208 e92d4100    536 	stmfd	[sp]!,{r8,lr}

                     537 ;154: {


                     538 


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_d801.s
                     539 ;155:    return _sendFuncPtr(netIf,data,byteCount);


                     540 

0000020c e59f8078*   541 	ldr	r8,.L519

00000210 e598c000    542 	ldr	r12,[r8]

00000214 e1a0e00f    543 	mov	lr,pc

00000218 e12fff1c*   544 	bx	r12

0000021c e8bd8100    545 	ldmfd	[sp]!,{r8,pc}

                     546 	.endf	NetTools_send

                     547 	.align	4

                     548 

                     549 ;netIf	none	param

                     550 ;data	none	param

                     551 ;byteCount	none	param

                     552 

                     553 	.data

                     554 	.text

                     555 

                     556 ;156: }


                     557 

                     558 ;157: 


                     559 ;158: 


                     560 ;159: bool NetTools_busOK(void)


                     561 	.align	4

                     562 	.align	4

                     563 NetTools_busOK::

00000220 e92d4000    564 	stmfd	[sp]!,{lr}

                     565 ;160: {


                     566 

                     567 ;161: 	EthBusModuleState *moduleState;


                     568 ;162: 


                     569 ;163: 	if(!_isBC)


                     570 

00000224 e51fc160*   571 	ldr	r12,.L66

00000228 e5dc0008    572 	ldrb	r0,[r12,8]

0000022c e3500000    573 	cmp	r0,0

                     574 ;164: 	{


                     575 

                     576 ;165: 		return true;


                     577 

00000230 03a00001    578 	moveq	r0,1

00000234 0a000008    579 	beq	.L520

00000238 e59cc000    580 	ldr	r12,[r12]

0000023c e1b0000c    581 	movs	r0,r12

                     582 ;166: 	}


                     583 ;167: 


                     584 ;168: 	// шина еще не инициализирована


                     585 ;169: 	if (!_ethbus)


                     586 

                     587 ;170: 	{


                     588 

                     589 ;171: 		return false;


                     590 

00000240 020000ff    591 	andeq	r0,r0,255

00000244 0a000004    592 	beq	.L520

                     593 ;172: 	}


                     594 ;173: 


                     595 ;174: 	// не может быть NULL


                     596 ;175: 	moduleState = _ethbus->getModuleState(-1);


                     597 

00000248 e59cc014    598 	ldr	r12,[r12,20]

0000024c e3e00000    599 	mvn	r0,0


                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_d801.s
00000250 e1a0e00f    600 	mov	lr,pc

00000254 e12fff1c*   601 	bx	r12

                     602 ;176: 	return moduleState->readyFlag;


                     603 

00000258 e5d00005    604 	ldrb	r0,[r0,5]

                     605 .L520:

0000025c e8bd8000    606 	ldmfd	[sp]!,{pc}

                     607 	.endf	NetTools_busOK

                     608 	.align	4

                     609 

                     610 	.data

                     611 	.text

                     612 

                     613 ;177: }


                     614 	.align	4

                     615 .L418:

                     616 ;	"SYSTEM\000"

00000260 54535953    617 	.data.b	83,89,83,84

00000264 4d45       618 	.data.b	69,77

00000266 00         619 	.data.b	0

00000267 00         620 	.align 4

                     621 

                     622 	.type	.L418,$object

                     623 	.size	.L418,4

                     624 

                     625 .L419:

00000268 00000000*   626 	.data.w	_getIfFuncPtr

                     627 	.type	.L419,$object

                     628 	.size	.L419,4

                     629 

                     630 .L420:

0000026c 4746434e    631 	.data.w	0x4746434e

                     632 	.type	.L420,$object

                     633 	.size	.L420,4

                     634 

                     635 .L421:

00000270 00000000*   636 	.data.w	ethBusSend

                     637 	.type	.L421,$object

                     638 	.size	.L421,4

                     639 

                     640 .L422:

00000274 00000000*   641 	.data.w	ethBusGetMac

                     642 	.type	.L422,$object

                     643 	.size	.L422,4

                     644 

                     645 .L423:

00000278 00000000*   646 	.data.w	ethBusGetIf

                     647 	.type	.L423,$object

                     648 	.size	.L423,4

                     649 

                     650 .L424:

0000027c 00000000*   651 	.data.w	networkSend

                     652 	.type	.L424,$object

                     653 	.size	.L424,4

                     654 

                     655 .L425:

00000280 00000000*   656 	.data.w	networkGetMac

                     657 	.type	.L425,$object

                     658 	.size	.L425,4

                     659 

                     660 .L426:


                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_d801.s
00000284 00000000*   661 	.data.w	networkGetIf

                     662 	.type	.L426,$object

                     663 	.size	.L426,4

                     664 

                     665 .L487:

00000288 00000000*   666 	.data.w	_getMacFuncPtr

                     667 	.type	.L487,$object

                     668 	.size	.L487,4

                     669 

                     670 .L519:

0000028c 00000000*   671 	.data.w	_sendFuncPtr

                     672 	.type	.L519,$object

                     673 	.size	.L519,4

                     674 

                     675 	.align	4

                     676 ;_getIfFuncPtr	_getIfFuncPtr	static

                     677 ;_getMacFuncPtr	_getMacFuncPtr	static

                     678 ;_sendFuncPtr	_sendFuncPtr	static

                     679 ;_ethbus	_ethbus	static

                     680 ;_gooseReceiverSlotNum	_gooseReceiverSlotNum	static

                     681 ;_gooseReceiverDescr	.L50	static

                     682 ;_isBC	_isBC	static

                     683 

                     684 	.data

                     685 	.ghsnote version,6

                     686 	.ghsnote tools,1

                     687 	.ghsnote options,0

                     688 	.text

                     689 	.align	4

                     690 	.data

                     691 	.align	4

                     692 	.section ".bss","awb"

00000015 000000     693 	.align	4

                     694 	.text

