#include "iedTree.h"

#include "iedEntity.h"
#include "iedObjects.h"
#include "iedNoEntity.h"

#include <platform_critical_section.h>

#include <stddef.h>
#include <debug.h>

static IEDEntity iedTreeRoot = NULL;

static CriticalSection iedTreeCS;

// Список элементов, которые нужно проверать на изменения
// при получении нового DataSlice
// Xоть следующие переменные используются локально, здесь нельзя писать static, т.к.
// GHS генерирует неверный код в функции IEDTree_addToCmpList
IEDEntity dataSliceUpdateList = NULL;
IEDEntity* dataSliceUpdateListTail = &dataSliceUpdateList;

bool IEDTree_init(uint8_t *iedModel, size_t modelSize)
{
    IEDEntity root;
    BufferView ber;


    CriticalSection_Init(&iedTreeCS);

    BufferView_init(&ber, iedModel, modelSize, 0);

    if(!IEDEntity_createFromBER(&root, &ber , NULL))
    {
        return false;
    }
    iedTreeRoot = root;

    //Дополнительная инициализация для элементов,
    //требующих готового дерева
    if(!IEDEntity_postCreate(root))
    {
        return false;
    }

    IEDNoEntity_init();

    return true;
}

void IEDTree_lock(void)
{
    CriticalSection_Lock(&iedTreeCS);
}

void IEDTree_unlock(void)
{
    CriticalSection_Unlock(&iedTreeCS);
}

IEDEntity IEDTree_findDataByFullName(StringView* ldName,
                                     StringView* dataName)
{
    IEDEntity dataSection;
    IEDEntity ld = IEDEntity_getChildByName(iedTreeRoot, ldName);
    if(ld == NULL)
    {
        return NULL;
    }
    dataSection = IEDLD_getDataSection(ld);
    if(dataSection == NULL)
    {
        return NULL;
    }
    return IEDEntity_getChildByFullName(dataSection, dataName);
}

IEDEntity IEDTree_findDataSetByFullName(StringView* ldName,
                                     StringView* dataSetName)
{
    IEDEntity dataSetSection;
    IEDEntity ld = IEDEntity_getChildByName(iedTreeRoot, ldName);
    if(ld == NULL)
    {
        return NULL;
    }
    dataSetSection = IEDLD_getDataSetSection(ld);
    if(dataSetSection == NULL)
    {
        return NULL;
    }
    return IEDEntity_getChildByFullName(dataSetSection, dataSetName);
}

IEDEntity IEDTree_findDataSetBySingleName(StringView* dataSetName)
{
    StringView ldName;
    StringView remainingName;
    if(!StringView_splitChar(dataSetName, '/', &ldName, &remainingName))
    {
        return NULL;
    }
    return IEDTree_findDataSetByFullName(&ldName, &remainingName);
}

MmsDataAccessError IEDTree_write(StringView* ldName,
                                 StringView* itemName, IsoConnection* isoConn,
                                   BufferView* value)
{
    IEDEntity entity = IEDTree_findDataByFullName(ldName, itemName);
    if(entity == NULL)
    {
        ERROR_REPORT("Item is not found");
        return DATA_ACCESS_ERROR_OBJECT_NONE_EXISTENT;
    }
    return IEDEntity_write(entity, isoConn, value);
}

void IEDTree_updateFromDataSlice(void)
{
    IEDEntity currEntity = dataSliceUpdateList;

    while(currEntity != NULL)
    {
        currEntity->updateFromDataSlice(currEntity);
        currEntity = currEntity->nextCompare;
    }
}

void IEDTree_addToCmpList(IEDEntity da)
{
    *dataSliceUpdateListTail = da;
    dataSliceUpdateListTail = &da->nextCompare;
}
