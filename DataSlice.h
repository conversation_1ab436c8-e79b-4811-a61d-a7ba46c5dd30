#pragma once

#include "local_types.h"
#include <types.h>
#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>
#include "IEDCompile/AccessInfo.h"

bool dataSliceInit(void);
void dataSliceCapture(void);
void dataSliceRelease(void);
//! Возвращает текущий timeStamp в формате 32.32
unsigned long long getCurrentDataSliceTime(void);

//! Возвращает timeStamp в формате 32.32
unsigned long long dataSliceGetTimeStamp(void);

//! Возвращает расчётное значение или значение предварительной обработки
//! в формате 16.16 или 32.0
long dataSliceGetFloatValue(uint16_t offset);

//! Возвращает расчётное значение или значение предварительной обработки
//! в формате real(настоящий float)
float dataSliceGetRealValue(uint16_t offset);

bool dataSliceGetBoolValue(uint16_t offset);
int dataSliceGetIntValue(uint16_t offset);

bool DataSlice_getBoolFast(void* dataSliceWnd, uint16_t offset);

//! Получает bool из текущего "захваченного" (скопированного) DataSlice.
bool DataSlice_getBoolFastCurrDS(uint16_t offset);

//! Получает int(значение "накопителя") из текущего "захваченного" (скопированного)
//! DataSlice. в виде INT32
int DataSlice_getInt32FastCurrDS(uint16_t offset);

//! Получает int(значение "накопителя") из текущего "захваченного" (скопированного)
//! DataSlice в виде UINT32
uint32_t DataSlice_getUInt32FastCurrDS(uint16_t offset);

//! Получает real из текущего "захваченного" (скопированного) DataSlice
//! без домножения на коэффициенты
float DataSlice_getRealFastCurrDS(uint16_t offset);

//! Получает fixed(16.16 или 32.0) из текущего "захваченного" (скопированного)
//! DataSlice без домножения на коэффициенты
int DataSlice_getFixedFastCurrDS(uint16_t offset);

//! Возвращает void* чтобы не тащить datasliceif.h в PC версию.
void* DataSlice_getDataSliceWnd(void);

//! Возвращает смещение в окне DataSlice по смещению в устройстве
//! При ошибке возвращает -1.
//! offset может быть -1, тогда тоже возвращает -1
int DataSlice_getBoolOffset(int offset);

//! Для Int (обычно это накопители) возвращает смещение в окне DataSlice
//! по смещению в устройстве.
//! При ошибке возвращает -1.
int DataSlice_getIntOffset(int offset);

//! Для FLOAT и REAL возвращает смещение в окне DataSlice по смещению
//! в устройстве.
//! При ошибке возвращает -1.
int DataSlice_getAnalogOffset(int offset);

void DataSlice_setCallBack(void (*func)(void));
