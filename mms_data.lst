                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g881.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=mms_data.c -o gh_g881.o -list=mms_data.lst C:\Users\<USER>\AppData\Local\Temp\gh_g881.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_g881.s
Source File: mms_data.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile mms_data.c -o

                      10 ;		mms_data.o

                      11 ;Source File:   mms_data.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Oct 07 09:39:36 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "mms_data.h"


                      21 ;2: 


                      22 ;3: #include "pwin_access.h"


                      23 ;4: #include "DataSlice.h"


                      24 ;5: #include "AsnEncoding.h"


                      25 ;6: #include "debug.h"


                      26 ;7: #include "iedmodel.h"


                      27 ;8: 


                      28 ;9: #include "IEDCompile/InnerAttributeTypes.h"


                      29 ;10: #include <types.h>


                      30 ;11: #include <string.h>


                      31 ;12: #include <stdint.h>


                      32 ;13: 


                      33 ;14: //Получение битов quality через сохранённые смещения в DataSlice


                      34 ;15: #define SET_QUALITY_BIT_FAST(offsName, bitNum) \


                      35 ;16:     if((accessInfo->offsName != -1)  \


                      36 ;17:         && DataSlice_getBoolFast(dataSliceWnd, accessInfo->offsName)) \


                      37 ;18:     { quality |= (1 << bitNum);}


                      38 ;19: 


                      39 ;20: uint16_t qualityFromBitsFast(void* dataSliceWnd, QualityAccsessInfo* accessInfo)


                      40 ;21: {


                      41 ;22:     uint16_t quality = 0;


                      42 ;23: 


                      43 ;24: 	SET_QUALITY_BIT_FAST(goodInvalidOffset, 7);


                      44 ;25: 	SET_QUALITY_BIT_FAST(reservedQuestionableOffset, 6);    


                      45 ;26:     SET_QUALITY_BIT_FAST(overflowOffset, 5);


                      46 ;27:     SET_QUALITY_BIT_FAST(outOfRangeOffset, 4);


                      47 ;28:     SET_QUALITY_BIT_FAST(badReferenceOffset, 3);


                      48 ;29:     SET_QUALITY_BIT_FAST(oscillatoryOffset, 2);


                      49 ;30:     SET_QUALITY_BIT_FAST(failureOffset, 1);


                      50 ;31:     SET_QUALITY_BIT_FAST(oldDataOffset, 0);



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g881.s
                      51 ;32:     SET_QUALITY_BIT_FAST(inconsistentOffset,15);


                      52 ;33:     SET_QUALITY_BIT_FAST(inaccurateOffset, 14);


                      53 ;34:     SET_QUALITY_BIT_FAST(processSubstitutedOffset, 13);


                      54 ;35:     SET_QUALITY_BIT_FAST(testOffset, 12);


                      55 ;36:     SET_QUALITY_BIT_FAST(operatorBlockedOffset, 11);


                      56 ;37:     return quality;


                      57 ;38: }


                      58 ;39: 


                      59 ;40: float readFloatValue(FloatAccsessInfo* accessInfo)


                      60 ;41: {


                      61 ;42: 	return dataSliceGetFloatValue(accessInfo->valueOffset)  * accessInfo->multiplier;


                      62 ;43: }


                      63 ;44: 


                      64 ;45: 


                      65 ;46: //! проверяет на NAN


                      66 ;47: static __inline int isfnan(float value)


                      67 

                      68 ;51: }


                      69 

                      70 	.text

                      71 	.align	4

                      72 qualityFromBitsFast::

00000000 e92d4070     73 	stmfd	[sp]!,{r4-r6,lr}

00000004 e1a05000     74 	mov	r5,r0

00000008 e1a06001     75 	mov	r6,r1

0000000c e5961004     76 	ldr	r1,[r6,4]

00000010 e3a04000     77 	mov	r4,0

00000014 e3710001     78 	cmn	r1,1

00000018 0a000004     79 	beq	.L23

0000001c e1a01801     80 	mov	r1,r1 lsl 16

00000020 e1a01821     81 	mov	r1,r1 lsr 16

00000024 eb000000*    82 	bl	DataSlice_getBoolFast

00000028 e3500000     83 	cmp	r0,0

0000002c 13a04080     84 	movne	r4,128

                      85 .L23:

00000030 e5961008     86 	ldr	r1,[r6,8]

00000034 e3710001     87 	cmn	r1,1

00000038 0a000005     88 	beq	.L26

0000003c e1a01801     89 	mov	r1,r1 lsl 16

00000040 e1a01821     90 	mov	r1,r1 lsr 16

00000044 e1a00005     91 	mov	r0,r5

00000048 eb000000*    92 	bl	DataSlice_getBoolFast

0000004c e3500000     93 	cmp	r0,0

00000050 13844040     94 	orrne	r4,r4,64

                      95 .L26:

00000054 e596100c     96 	ldr	r1,[r6,12]

00000058 e3710001     97 	cmn	r1,1

0000005c 0a000005     98 	beq	.L29

00000060 e1a01801     99 	mov	r1,r1 lsl 16

00000064 e1a01821    100 	mov	r1,r1 lsr 16

00000068 e1a00005    101 	mov	r0,r5

0000006c eb000000*   102 	bl	DataSlice_getBoolFast

00000070 e3500000    103 	cmp	r0,0

00000074 13844020    104 	orrne	r4,r4,32

                     105 .L29:

00000078 e5961010    106 	ldr	r1,[r6,16]

0000007c e3710001    107 	cmn	r1,1

00000080 0a000005    108 	beq	.L32

00000084 e1a01801    109 	mov	r1,r1 lsl 16

00000088 e1a01821    110 	mov	r1,r1 lsr 16

0000008c e1a00005    111 	mov	r0,r5


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g881.s
00000090 eb000000*   112 	bl	DataSlice_getBoolFast

00000094 e3500000    113 	cmp	r0,0

00000098 13844010    114 	orrne	r4,r4,16

                     115 .L32:

0000009c e5961014    116 	ldr	r1,[r6,20]

000000a0 e3710001    117 	cmn	r1,1

000000a4 0a000005    118 	beq	.L35

000000a8 e1a01801    119 	mov	r1,r1 lsl 16

000000ac e1a01821    120 	mov	r1,r1 lsr 16

000000b0 e1a00005    121 	mov	r0,r5

000000b4 eb000000*   122 	bl	DataSlice_getBoolFast

000000b8 e3500000    123 	cmp	r0,0

000000bc 13844008    124 	orrne	r4,r4,8

                     125 .L35:

000000c0 e5961018    126 	ldr	r1,[r6,24]

000000c4 e3710001    127 	cmn	r1,1

000000c8 0a000005    128 	beq	.L38

000000cc e1a01801    129 	mov	r1,r1 lsl 16

000000d0 e1a01821    130 	mov	r1,r1 lsr 16

000000d4 e1a00005    131 	mov	r0,r5

000000d8 eb000000*   132 	bl	DataSlice_getBoolFast

000000dc e3500000    133 	cmp	r0,0

000000e0 13844004    134 	orrne	r4,r4,4

                     135 .L38:

000000e4 e596101c    136 	ldr	r1,[r6,28]

000000e8 e3710001    137 	cmn	r1,1

000000ec 0a000005    138 	beq	.L41

000000f0 e1a01801    139 	mov	r1,r1 lsl 16

000000f4 e1a01821    140 	mov	r1,r1 lsr 16

000000f8 e1a00005    141 	mov	r0,r5

000000fc eb000000*   142 	bl	DataSlice_getBoolFast

00000100 e3500000    143 	cmp	r0,0

00000104 13844002    144 	orrne	r4,r4,2

                     145 .L41:

00000108 e5961020    146 	ldr	r1,[r6,32]

0000010c e3710001    147 	cmn	r1,1

00000110 0a000005    148 	beq	.L44

00000114 e1a01801    149 	mov	r1,r1 lsl 16

00000118 e1a01821    150 	mov	r1,r1 lsr 16

0000011c e1a00005    151 	mov	r0,r5

00000120 eb000000*   152 	bl	DataSlice_getBoolFast

00000124 e3500000    153 	cmp	r0,0

00000128 13844001    154 	orrne	r4,r4,1

                     155 .L44:

0000012c e5961024    156 	ldr	r1,[r6,36]

00000130 e3710001    157 	cmn	r1,1

00000134 0a000005    158 	beq	.L47

00000138 e1a01801    159 	mov	r1,r1 lsl 16

0000013c e1a01821    160 	mov	r1,r1 lsr 16

00000140 e1a00005    161 	mov	r0,r5

00000144 eb000000*   162 	bl	DataSlice_getBoolFast

00000148 e3500000    163 	cmp	r0,0

0000014c 13844c80    164 	orrne	r4,r4,1<<15

                     165 .L47:

00000150 e5961028    166 	ldr	r1,[r6,40]

00000154 e3710001    167 	cmn	r1,1

00000158 0a000005    168 	beq	.L50

0000015c e1a01801    169 	mov	r1,r1 lsl 16

00000160 e1a01821    170 	mov	r1,r1 lsr 16

00000164 e1a00005    171 	mov	r0,r5

00000168 eb000000*   172 	bl	DataSlice_getBoolFast


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g881.s
0000016c e3500000    173 	cmp	r0,0

00000170 13844c40    174 	orrne	r4,r4,1<<14

                     175 .L50:

00000174 e596102c    176 	ldr	r1,[r6,44]

00000178 e3710001    177 	cmn	r1,1

0000017c 0a000005    178 	beq	.L53

00000180 e1a01801    179 	mov	r1,r1 lsl 16

00000184 e1a01821    180 	mov	r1,r1 lsr 16

00000188 e1a00005    181 	mov	r0,r5

0000018c eb000000*   182 	bl	DataSlice_getBoolFast

00000190 e3500000    183 	cmp	r0,0

00000194 13844d80    184 	orrne	r4,r4,1<<13

                     185 .L53:

00000198 e5961030    186 	ldr	r1,[r6,48]

0000019c e3710001    187 	cmn	r1,1

000001a0 0a000005    188 	beq	.L56

000001a4 e1a01801    189 	mov	r1,r1 lsl 16

000001a8 e1a01821    190 	mov	r1,r1 lsr 16

000001ac e1a00005    191 	mov	r0,r5

000001b0 eb000000*   192 	bl	DataSlice_getBoolFast

000001b4 e3500000    193 	cmp	r0,0

000001b8 13844d40    194 	orrne	r4,r4,1<<12

                     195 .L56:

000001bc e5961034    196 	ldr	r1,[r6,52]

000001c0 e3710001    197 	cmn	r1,1

000001c4 0a000005    198 	beq	.L59

000001c8 e1a01801    199 	mov	r1,r1 lsl 16

000001cc e1a01821    200 	mov	r1,r1 lsr 16

000001d0 e1a00005    201 	mov	r0,r5

000001d4 eb000000*   202 	bl	DataSlice_getBoolFast

000001d8 e3500000    203 	cmp	r0,0

000001dc 13844e80    204 	orrne	r4,r4,1<<11

                     205 .L59:

000001e0 e1a00004    206 	mov	r0,r4

000001e4 e8bd8070    207 	ldmfd	[sp]!,{r4-r6,pc}

                     208 	.endf	qualityFromBitsFast

                     209 	.align	4

                     210 ;quality	r4	local

                     211 

                     212 ;dataSliceWnd	r5	param

                     213 ;accessInfo	r6	param

                     214 

                     215 	.section ".bss","awb"

                     216 .L370:

                     217 	.data

                     218 	.text

                     219 

                     220 

                     221 	.align	4

                     222 	.align	4

                     223 readFloatValue::

000001e8 e92d4010    224 	stmfd	[sp]!,{r4,lr}

000001ec e1a04000    225 	mov	r4,r0

000001f0 e1d400b4    226 	ldrh	r0,[r4,4]

000001f4 eb000000*   227 	bl	dataSliceGetFloatValue

000001f8 eb000000*   228 	bl	__itof

000001fc e5941008    229 	ldr	r1,[r4,8]

00000200 e8bd4010    230 	ldmfd	[sp]!,{r4,lr}

00000204 ea000000*   231 	b	__fmul

                     232 	.endf	readFloatValue

                     233 	.align	4


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g881.s
                     234 

                     235 ;accessInfo	r4	param

                     236 

                     237 	.section ".bss","awb"

                     238 .L510:

                     239 	.data

                     240 	.text

                     241 

                     242 

                     243 ;52: 


                     244 ;53: float readRealValue(FloatAccsessInfo* accessInfo)


                     245 	.align	4

                     246 	.align	4

                     247 readRealValue::

00000208 e92d4010    248 	stmfd	[sp]!,{r4,lr}

0000020c e24dd004    249 	sub	sp,sp,4

00000210 e1a04000    250 	mov	r4,r0

                     251 ;54: {


                     252 

                     253 ;55:     float result = dataSliceGetRealValue(accessInfo->valueOffset);


                     254 

00000214 e1d400b4    255 	ldrh	r0,[r4,4]

00000218 eb000000*   256 	bl	dataSliceGetRealValue

0000021c e1a01000    257 	mov	r1,r0

                     258 ;56: 


                     259 ;57:     if(isfnan(result))


                     260 

00000220 e58d1000    261 	str	r1,[sp]

                     262 ;48: {


                     263 

                     264 ;49:   unsigned int inan = *(unsigned int*)(void*)&value;


                     265 

                     266 ;50:   return (inan & 0x7F800000) == 0x7F800000;


                     267 

00000224 e3a025fe    268 	mov	r2,254<<22

00000228 e2822440    269 	add	r2,r2,1<<30

0000022c e0013002    270 	and	r3,r1,r2

00000230 e1530002    271 	cmp	r3,r2

                     272 ;58:     {


                     273 

                     274 ;59:         return result;


                     275 

                     276 ;60:     }


                     277 ;61: 


                     278 ;62:     return result  * accessInfo->multiplier;


                     279 

00000234 15941008    280 	ldrne	r1,[r4,8]

00000238 1b000000*   281 	blne	__fmul

0000023c e28dd004    282 	add	sp,sp,4

00000240 e8bd8010    283 	ldmfd	[sp]!,{r4,pc}

                     284 	.endf	readRealValue

                     285 	.align	4

                     286 ;result	r1	local

                     287 ;value	[sp]	local

                     288 

                     289 ;accessInfo	r4	param

                     290 

                     291 	.section ".bss","awb"

                     292 .L555:

                     293 	.data

                     294 	.text


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g881.s
                     295 

                     296 ;63: }


                     297 

                     298 ;64: 


                     299 ;65: int encodeReadFloat(uint8_t* outBuf, int bufPos, void* descrStruct, bool determineSize)


                     300 	.align	4

                     301 	.align	4

                     302 encodeReadFloat::

00000244 e92d4030    303 	stmfd	[sp]!,{r4-r5,lr}

                     304 ;66: {


                     305 

                     306 ;67:     float value;    


                     307 ;68: 


                     308 ;69:     if(determineSize)


                     309 

00000248 e24dd008    310 	sub	sp,sp,8

0000024c e3530000    311 	cmp	r3,0

                     312 ;70:     {


                     313 

                     314 ;71:         int floatSize = 5;


                     315 

                     316 ;72:         return floatSize + 2; // 2 for tag and length


                     317 

00000250 13a00007    318 	movne	r0,7

00000254 1a000009    319 	bne	.L568

00000258 e1a05001    320 	mov	r5,r1

                     321 ;73:     }


                     322 ;74:    


                     323 ;75:     value = readFloatValue(descrStruct);


                     324 

0000025c e1a04000    325 	mov	r4,r0

00000260 e1a00002    326 	mov	r0,r2

00000264 ebffffdf*   327 	bl	readFloatValue

                     328 ;76: 


                     329 ;77:     return BerEncoder_EncodeFloatWithTL(0x87, value, 32, 8, outBuf, bufPos);


                     330 

00000268 e88d0030    331 	stmea	[sp],{r4-r5}

0000026c e3a03008    332 	mov	r3,8

00000270 e3a02020    333 	mov	r2,32

00000274 e1a01000    334 	mov	r1,r0

00000278 e3a00087    335 	mov	r0,135

0000027c eb000000*   336 	bl	BerEncoder_EncodeFloatWithTL

                     337 .L568:

00000280 e28dd008    338 	add	sp,sp,8

00000284 e8bd8030    339 	ldmfd	[sp]!,{r4-r5,pc}

                     340 	.endf	encodeReadFloat

                     341 	.align	4

                     342 

                     343 ;outBuf	r4	param

                     344 ;bufPos	r5	param

                     345 ;descrStruct	r2	param

                     346 ;determineSize	r3	param

                     347 

                     348 	.section ".bss","awb"

                     349 .L598:

                     350 	.data

                     351 	.text

                     352 

                     353 ;78: }


                     354 

                     355 ;79: 



                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g881.s
                     356 ;80: int encodeReadReal(uint8_t* outBuf, int bufPos, void* descrStruct,


                     357 	.align	4

                     358 	.align	4

                     359 encodeReadReal::

00000288 e92d4030    360 	stmfd	[sp]!,{r4-r5,lr}

                     361 ;81:                    bool determineSize)


                     362 ;82: {


                     363 

                     364 ;83:     float value;


                     365 ;84: 


                     366 ;85:     if(determineSize)


                     367 

0000028c e24dd008    368 	sub	sp,sp,8

00000290 e3530000    369 	cmp	r3,0

                     370 ;86:     {


                     371 

                     372 ;87:         int floatSize = 5;


                     373 

                     374 ;88:         return floatSize + 2; // 2 for tag and length


                     375 

00000294 13a00007    376 	movne	r0,7

00000298 1a000009    377 	bne	.L612

0000029c e1a05001    378 	mov	r5,r1

                     379 ;89:     }


                     380 ;90: 


                     381 ;91:     value = readRealValue(descrStruct);


                     382 

000002a0 e1a04000    383 	mov	r4,r0

000002a4 e1a00002    384 	mov	r0,r2

000002a8 ebffffd6*   385 	bl	readRealValue

                     386 ;92: 


                     387 ;93:     return BerEncoder_EncodeFloatWithTL(0x87, value, 32, 8, outBuf, bufPos);


                     388 

000002ac e88d0030    389 	stmea	[sp],{r4-r5}

000002b0 e3a03008    390 	mov	r3,8

000002b4 e3a02020    391 	mov	r2,32

000002b8 e1a01000    392 	mov	r1,r0

000002bc e3a00087    393 	mov	r0,135

000002c0 eb000000*   394 	bl	BerEncoder_EncodeFloatWithTL

                     395 .L612:

000002c4 e28dd008    396 	add	sp,sp,8

000002c8 e8bd8030    397 	ldmfd	[sp]!,{r4-r5,pc}

                     398 	.endf	encodeReadReal

                     399 	.align	4

                     400 

                     401 ;outBuf	r4	param

                     402 ;bufPos	r5	param

                     403 ;descrStruct	r2	param

                     404 ;determineSize	r3	param

                     405 

                     406 	.section ".bss","awb"

                     407 .L646:

                     408 	.data

                     409 	.text

                     410 

                     411 ;94: }


                     412 

                     413 ;95: 


                     414 ;96: int encodeReadFloatSett(uint8_t* outBuf, int bufPos, void* descrStruct, bool determineSize)


                     415 	.align	4

                     416 	.align	4


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g881.s
                     417 encodeReadFloatSett::

000002cc e92d4070    418 	stmfd	[sp]!,{r4-r6,lr}

                     419 ;97: {


                     420 

                     421 ;98:     float value;


                     422 ;99:     FloatAccsessInfo* accessInfo = descrStruct;


                     423 

                     424 ;100: 


                     425 ;101:     if(determineSize)


                     426 

000002d0 e24dd008    427 	sub	sp,sp,8

000002d4 e3530000    428 	cmp	r3,0

                     429 ;102:     {


                     430 

                     431 ;103:         int floatSize = 5;


                     432 

                     433 ;104:         return floatSize + 2; // 2 for tag and length


                     434 

000002d8 13a00007    435 	movne	r0,7

000002dc 1a00000d    436 	bne	.L660

000002e0 e1a05000    437 	mov	r5,r0

000002e4 e1a04002    438 	mov	r4,r2

                     439 ;105:     }


                     440 ;106: 


                     441 ;107:     value = getFloatSett(accessInfo->valueOffset) * accessInfo->multiplier;


                     442 

000002e8 e1d400b4    443 	ldrh	r0,[r4,4]

000002ec e1a06001    444 	mov	r6,r1

000002f0 eb000000*   445 	bl	getFloatSett

000002f4 eb000000*   446 	bl	__itof

000002f8 e5941008    447 	ldr	r1,[r4,8]

000002fc eb000000*   448 	bl	__fmul

                     449 ;108: 


                     450 ;109:     return BerEncoder_EncodeFloatWithTL(0x87, value, 32, 8, outBuf, bufPos);


                     451 

00000300 e88d0060    452 	stmea	[sp],{r5-r6}

00000304 e3a03008    453 	mov	r3,8

00000308 e3a02020    454 	mov	r2,32

0000030c e1a01000    455 	mov	r1,r0

00000310 e3a00087    456 	mov	r0,135

00000314 eb000000*   457 	bl	BerEncoder_EncodeFloatWithTL

                     458 .L660:

00000318 e28dd008    459 	add	sp,sp,8

0000031c e8bd8070    460 	ldmfd	[sp]!,{r4-r6,pc}

                     461 	.endf	encodeReadFloatSett

                     462 	.align	4

                     463 ;accessInfo	r4	local

                     464 

                     465 ;outBuf	r5	param

                     466 ;bufPos	r6	param

                     467 ;descrStruct	r2	param

                     468 ;determineSize	r3	param

                     469 

                     470 	.section ".bss","awb"

                     471 .L694:

                     472 	.data

                     473 	.text

                     474 

                     475 ;110: }


                     476 

                     477 ;111: 



                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g881.s
                     478 ;112: int encodeReadRealSett(uint8_t* outBuf, int bufPos, void* descrStruct, bool determineSize)


                     479 	.align	4

                     480 	.align	4

                     481 encodeReadRealSett::

00000320 e92d4070    482 	stmfd	[sp]!,{r4-r6,lr}

                     483 ;113: {


                     484 

                     485 ;114:     float value;


                     486 ;115:     FloatAccsessInfo* accessInfo = descrStruct;


                     487 

                     488 ;116: 


                     489 ;117:     if(determineSize)


                     490 

00000324 e24dd008    491 	sub	sp,sp,8

00000328 e3530000    492 	cmp	r3,0

                     493 ;118:     {


                     494 

                     495 ;119:         int floatSize = 5;


                     496 

                     497 ;120:         return floatSize + 2; // 2 for tag and length


                     498 

0000032c 13a00007    499 	movne	r0,7

00000330 1a00000c    500 	bne	.L708

00000334 e1a05000    501 	mov	r5,r0

00000338 e1a04002    502 	mov	r4,r2

                     503 ;121:     }


                     504 ;122: 


                     505 ;123:     value = getRealSett(accessInfo->valueOffset) * accessInfo->multiplier;


                     506 

0000033c e1d400b4    507 	ldrh	r0,[r4,4]

00000340 e1a06001    508 	mov	r6,r1

00000344 eb000000*   509 	bl	getRealSett

00000348 e5941008    510 	ldr	r1,[r4,8]

0000034c eb000000*   511 	bl	__fmul

                     512 ;124: 


                     513 ;125:     return BerEncoder_EncodeFloatWithTL(0x87, value, 32, 8, outBuf, bufPos);


                     514 

00000350 e88d0060    515 	stmea	[sp],{r5-r6}

00000354 e3a03008    516 	mov	r3,8

00000358 e3a02020    517 	mov	r2,32

0000035c e1a01000    518 	mov	r1,r0

00000360 e3a00087    519 	mov	r0,135

00000364 eb000000*   520 	bl	BerEncoder_EncodeFloatWithTL

                     521 .L708:

00000368 e28dd008    522 	add	sp,sp,8

0000036c e8bd8070    523 	ldmfd	[sp]!,{r4-r6,pc}

                     524 	.endf	encodeReadRealSett

                     525 	.align	4

                     526 ;accessInfo	r4	local

                     527 

                     528 ;outBuf	r5	param

                     529 ;bufPos	r6	param

                     530 ;descrStruct	r2	param

                     531 ;determineSize	r3	param

                     532 

                     533 	.section ".bss","awb"

                     534 .L742:

                     535 	.data

                     536 	.text

                     537 

                     538 ;126: }



                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g881.s
                     539 

                     540 ;127: 


                     541 ;128: int MMSData_encodeTimeStamp(uint8_t tag, uint64_t timeStamp, uint8_t* outBuf,


                     542 	.align	4

                     543 	.align	4

                     544 MMSData_encodeTimeStamp::

00000370 e92d4010    545 	stmfd	[sp]!,{r4,lr}

00000374 e24dd014    546 	sub	sp,sp,20

00000378 e98d0006    547 	stmfa	[sp],{r1-r2}

0000037c e59f47c4*   548 	ldr	r4,.L833

00000380 e5942000    549 	ldr	r2,[r4]

00000384 e59dc01c    550 	ldr	r12,[sp,28]

00000388 e58d200c    551 	str	r2,[sp,12]

0000038c e5944004    552 	ldr	r4,[r4,4]

00000390 e58d4010    553 	str	r4,[sp,16]

00000394 e5dd400b    554 	ldrb	r4,[sp,11]

00000398 e5cd400c    555 	strb	r4,[sp,12]

0000039c e5dd400a    556 	ldrb	r4,[sp,10]

000003a0 e5cd400d    557 	strb	r4,[sp,13]

000003a4 e5dd4009    558 	ldrb	r4,[sp,9]

000003a8 e5cd400e    559 	strb	r4,[sp,14]

000003ac e5dd4008    560 	ldrb	r4,[sp,8]

000003b0 e5cd400f    561 	strb	r4,[sp,15]

000003b4 e5dd4007    562 	ldrb	r4,[sp,7]

000003b8 e5cd4010    563 	strb	r4,[sp,16]

000003bc e5dd4006    564 	ldrb	r4,[sp,6]

000003c0 e5cd4011    565 	strb	r4,[sp,17]

000003c4 e5dd4005    566 	ldrb	r4,[sp,5]

000003c8 e28d100c    567 	add	r1,sp,12

000003cc e5cd4012    568 	strb	r4,[sp,18]

000003d0 e5dd4004    569 	ldrb	r4,[sp,4]

000003d4 e3a02008    570 	mov	r2,8

000003d8 e5cd4013    571 	strb	r4,[sp,19]

                     572 ;129: 	int bufPos)


                     573 ;130: {


                     574 

                     575 ;131: 	int i;


                     576 ;132: 	uint8_t timeStampBuf[8] = { 0 };	


                     577 

                     578 ;133: 	uint8_t* pTime = (uint8_t*)&timeStamp;


                     579 

                     580 ;134: 	pTime += 7; //На старший байт времени


                     581 

                     582 ;135: 


                     583 ;136: 	// в dataslice  Timequality передается вместе со временев в формате iec61850, 


                     584 ;137: 	// поэтому копируется целиком


                     585 ;138: 	for (i = 0; i < 8; ++i)


                     586 

                     587 ;142: 	}


                     588 ;143: 	return BerEncoder_encodeOctetString(tag, timeStampBuf, 8, outBuf, bufPos);


                     589 

000003dc e58dc000    590 	str	r12,[sp]

000003e0 eb000000*   591 	bl	BerEncoder_encodeOctetString

000003e4 e28dd014    592 	add	sp,sp,20

000003e8 e8bd8010    593 	ldmfd	[sp]!,{r4,pc}

                     594 	.endf	MMSData_encodeTimeStamp

                     595 	.align	4

                     596 ;timeStampBuf	[sp,12]	local

                     597 ;.L825	.L828	static

                     598 

                     599 ;tag	none	param


                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g881.s
                     600 ;timeStamp	[sp,4]	param

                     601 ;outBuf	none	param

                     602 ;bufPos	r12	param

                     603 

                     604 	.section ".bss","awb"

                     605 .L824:

                     606 	.section ".rodata","a"

00000000 00         607 .L828:	.space	1

00000001 00000000    608 	.space	7

00000005 000000 
                     609 	.type	.L828,$object

                     610 	.size	.L828,8

                     611 	.data

                     612 	.text

                     613 

                     614 ;144: }


                     615 

                     616 ;145: 


                     617 ;146: int encodeReadTimeStamp(uint8_t* outBuf, int bufPos, bool determineSize)


                     618 	.align	4

                     619 	.align	4

                     620 encodeReadTimeStamp::

000003ec e92d4030    621 	stmfd	[sp]!,{r4-r5,lr}

                     622 ;147: {


                     623 

                     624 ;148: 	/*


                     625 ;149: 	int i;


                     626 ;150:     uint8_t timeStampBuf[8] = {0};


                     627 ;151:     unsigned long long timeStamp = dataSliceGetTimeStamp();


                     628 ;152: 	uint8_t* pTime = (uint8_t*)&timeStamp;


                     629 ;153: 	pTime += 7; //На старший байт времени


                     630 ;154: 


                     631 ;155: 	//Разворачиваем для big endian. Последний байт оставляем для TimeQuality


                     632 ;156: 	for (i = 0; i < 7; ++i)


                     633 ;157: 	{


                     634 ;158: 		timeStampBuf[i] = *pTime;


                     635 ;159: 		pTime--;


                     636 ;160: 	}


                     637 ;161: 	*/


                     638 ;162: 	uint64_t timeStamp;


                     639 ;163: 


                     640 ;164:     if(determineSize)


                     641 

000003f0 e24dd004    642 	sub	sp,sp,4

000003f4 e3520000    643 	cmp	r2,0

                     644 ;165:     {


                     645 

                     646 ;166:         return 10;


                     647 

000003f8 13a0000a    648 	movne	r0,10

000003fc 1a000008    649 	bne	.L834

00000400 e1a04000    650 	mov	r4,r0

00000404 e1a05001    651 	mov	r5,r1

                     652 ;167:     }


                     653 ;168: 	timeStamp = dataSliceGetTimeStamp();


                     654 

00000408 eb000000*   655 	bl	dataSliceGetTimeStamp

                     656 ;169: 


                     657 ;170:     //return BerEncoder_encodeOctetString(0x91, timeStampBuf, 8, outBuf, bufPos);


                     658 ;171: 	return MMSData_encodeTimeStamp(0x91, timeStamp, outBuf, bufPos);


                     659 


                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g881.s
0000040c e58d5000    660 	str	r5,[sp]

00000410 e1a03004    661 	mov	r3,r4

00000414 e1a02001    662 	mov	r2,r1

00000418 e1a01000    663 	mov	r1,r0

0000041c e3a00091    664 	mov	r0,145

00000420 ebffffd2*   665 	bl	MMSData_encodeTimeStamp

                     666 .L834:

00000424 e28dd004    667 	add	sp,sp,4

00000428 e8bd8030    668 	ldmfd	[sp]!,{r4-r5,pc}

                     669 	.endf	encodeReadTimeStamp

                     670 	.align	4

                     671 

                     672 ;outBuf	r4	param

                     673 ;bufPos	r5	param

                     674 ;determineSize	r2	param

                     675 

                     676 	.section ".bss","awb"

                     677 .L870:

                     678 	.data

                     679 	.text

                     680 

                     681 ;172: }


                     682 

                     683 ;173: 


                     684 ;174: //По значению dataValue возвращает соответствующее ему значение типа enumerator


                     685 ;175: int getEnumValue(int dataValue, EnumTableRecord* table, size_t tableSize)


                     686 	.align	4

                     687 	.align	4

                     688 getEnumValue::

0000042c e92d0070    689 	stmfd	[sp]!,{r4-r6}

                     690 ;176: {


                     691 

                     692 ;177: 	size_t i;


                     693 ;178: 	for (i = 0; i < tableSize; ++i)


                     694 

00000430 e3a04000    695 	mov	r4,0

00000434 e3520000    696 	cmp	r2,0

00000438 a1a05002    697 	movge	r5,r2

0000043c b3a05000    698 	movlt	r5,0

00000440 e1b0c1a5    699 	movs	r12,r5 lsr 3

00000444 0a00001d    700 	beq	.L914

00000448 e2812038    701 	add	r2,r1,56

                     702 .L915:

0000044c e5126038    703 	ldr	r6,[r2,-56]

00000450 e2423038    704 	sub	r3,r2,56

00000454 e1500006    705 	cmp	r0,r6

00000458 15126030    706 	ldrne	r6,[r2,-48]

0000045c 12423030    707 	subne	r3,r2,48

00000460 11500006    708 	cmpne	r0,r6

00000464 15126028    709 	ldrne	r6,[r2,-40]

00000468 12423028    710 	subne	r3,r2,40

0000046c 11500006    711 	cmpne	r0,r6

00000470 15126020    712 	ldrne	r6,[r2,-32]

00000474 12423020    713 	subne	r3,r2,32

00000478 11500006    714 	cmpne	r0,r6

0000047c 15126018    715 	ldrne	r6,[r2,-24]

00000480 12423018    716 	subne	r3,r2,24

00000484 11500006    717 	cmpne	r0,r6

00000488 15126010    718 	ldrne	r6,[r2,-16]

0000048c 12423010    719 	subne	r3,r2,16

00000490 11500006    720 	cmpne	r0,r6


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g881.s
00000494 15126008    721 	ldrne	r6,[r2,-8]

00000498 12423008    722 	subne	r3,r2,8

0000049c 11500006    723 	cmpne	r0,r6

000004a0 11a03002    724 	movne	r3,r2

000004a4 15936000    725 	ldrne	r6,[r3]

000004a8 11500006    726 	cmpne	r0,r6

000004ac 0a000009    727 	beq	.L950

000004b0 e2822040    728 	add	r2,r2,64

000004b4 e2844008    729 	add	r4,r4,8

000004b8 e25cc001    730 	subs	r12,r12,1

000004bc 1affffe2    731 	bne	.L915

                     732 .L914:

000004c0 e215c007    733 	ands	r12,r5,7

000004c4 0a000009    734 	beq	.L884

000004c8 e0811184    735 	add	r1,r1,r4 lsl 3

                     736 .L949:

000004cc e1a03001    737 	mov	r3,r1

000004d0 e5932000    738 	ldr	r2,[r3]

000004d4 e1500002    739 	cmp	r0,r2

                     740 .L950:

000004d8 05930004    741 	ldreq	r0,[r3,4]

000004dc 0a000003    742 	beq	.L884

                     743 .L952:

000004e0 e2811008    744 	add	r1,r1,8

000004e4 e2844001    745 	add	r4,r4,1

000004e8 e25cc001    746 	subs	r12,r12,1

000004ec 1afffff6    747 	bne	.L949

                     748 ;184: 		}


                     749 ;185: 	}


                     750 ;186: 	//Если в таблице нет такого значения, возвращаем его напрямую


                     751 ;187: 	return dataValue;


                     752 

                     753 .L884:

000004f0 e8bd0070    754 	ldmfd	[sp]!,{r4-r6}

000004f4 e12fff1e*   755 	ret	

                     756 	.endf	getEnumValue

                     757 	.align	4

                     758 ;i	r4	local

                     759 ;pair	r3	local

                     760 

                     761 ;dataValue	r0	param

                     762 ;table	r1	param

                     763 ;tableSize	r2	param

                     764 

                     765 	.section ".bss","awb"

                     766 .L1185:

                     767 	.data

                     768 	.text

                     769 

                     770 ;188: }


                     771 

                     772 ;189: 


                     773 ;190: int getEnumDataValue(int enumValue, EnumTableRecord* table, size_t tableSize)


                     774 	.align	4

                     775 	.align	4

                     776 getEnumDataValue::

000004f8 e92d0070    777 	stmfd	[sp]!,{r4-r6}

                     778 ;191: {


                     779 

                     780 ;192: 	size_t i;


                     781 ;193: 	for (i = 0; i < tableSize; ++i)



                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g881.s
                     782 

000004fc e3a04000    783 	mov	r4,0

00000500 e3520000    784 	cmp	r2,0

00000504 a1a05002    785 	movge	r5,r2

00000508 b3a05000    786 	movlt	r5,0

0000050c e1b0c1a5    787 	movs	r12,r5 lsr 3

00000510 0a00001d    788 	beq	.L1266

00000514 e2812038    789 	add	r2,r1,56

                     790 .L1267:

00000518 e5126034    791 	ldr	r6,[r2,-52]

0000051c e2423038    792 	sub	r3,r2,56

00000520 e1500006    793 	cmp	r0,r6

00000524 1512602c    794 	ldrne	r6,[r2,-44]

00000528 12423030    795 	subne	r3,r2,48

0000052c 11500006    796 	cmpne	r0,r6

00000530 15126024    797 	ldrne	r6,[r2,-36]

00000534 12423028    798 	subne	r3,r2,40

00000538 11500006    799 	cmpne	r0,r6

0000053c 1512601c    800 	ldrne	r6,[r2,-28]

00000540 12423020    801 	subne	r3,r2,32

00000544 11500006    802 	cmpne	r0,r6

00000548 15126014    803 	ldrne	r6,[r2,-20]

0000054c 12423018    804 	subne	r3,r2,24

00000550 11500006    805 	cmpne	r0,r6

00000554 1512600c    806 	ldrne	r6,[r2,-12]

00000558 12423010    807 	subne	r3,r2,16

0000055c 11500006    808 	cmpne	r0,r6

00000560 15126004    809 	ldrne	r6,[r2,-4]

00000564 12423008    810 	subne	r3,r2,8

00000568 11500006    811 	cmpne	r0,r6

0000056c 11a03002    812 	movne	r3,r2

00000570 15936004    813 	ldrne	r6,[r3,4]

00000574 11500006    814 	cmpne	r0,r6

00000578 0a000009    815 	beq	.L1302

0000057c e2822040    816 	add	r2,r2,64

00000580 e2844008    817 	add	r4,r4,8

00000584 e25cc001    818 	subs	r12,r12,1

00000588 1affffe2    819 	bne	.L1267

                     820 .L1266:

0000058c e215c007    821 	ands	r12,r5,7

00000590 0a000009    822 	beq	.L1226

00000594 e0811184    823 	add	r1,r1,r4 lsl 3

                     824 .L1301:

00000598 e1a03001    825 	mov	r3,r1

0000059c e5932004    826 	ldr	r2,[r3,4]

000005a0 e1500002    827 	cmp	r0,r2

                     828 .L1302:

000005a4 05930000    829 	ldreq	r0,[r3]

000005a8 0a000003    830 	beq	.L1226

                     831 .L1304:

000005ac e2811008    832 	add	r1,r1,8

000005b0 e2844001    833 	add	r4,r4,1

000005b4 e25cc001    834 	subs	r12,r12,1

000005b8 1afffff6    835 	bne	.L1301

                     836 ;199: 		}


                     837 ;200: 	}


                     838 ;201: 	//Если в таблице нет такого значения, возвращаем его напрямую


                     839 ;202: 	return enumValue;


                     840 

                     841 .L1226:

000005bc e8bd0070    842 	ldmfd	[sp]!,{r4-r6}


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g881.s
000005c0 e12fff1e*   843 	ret	

                     844 	.endf	getEnumDataValue

                     845 	.align	4

                     846 ;i	r4	local

                     847 ;pair	r3	local

                     848 

                     849 ;enumValue	r0	param

                     850 ;table	r1	param

                     851 ;tableSize	r2	param

                     852 

                     853 	.section ".bss","awb"

                     854 .L1537:

                     855 	.data

                     856 	.text

                     857 

                     858 ;203: }


                     859 

                     860 ;204: 


                     861 ;205: int readIntSettValue(IntBoolAccessInfo* accessInfo)


                     862 	.align	4

                     863 	.align	4

                     864 readIntSettValue::

000005c4 e92d4010    865 	stmfd	[sp]!,{r4,lr}

000005c8 e1a04000    866 	mov	r4,r0

                     867 ;206: {


                     868 

                     869 ;207: 	int dataValue = getIntSett(accessInfo->valueOffset);


                     870 

000005cc e5940004    871 	ldr	r0,[r4,4]

000005d0 eb000000*   872 	bl	getIntSett

                     873 ;208: 	if (accessInfo->enumTableSize == 0)


                     874 

000005d4 e5942008    875 	ldr	r2,[r4,8]

000005d8 e3520000    876 	cmp	r2,0

                     877 ;211: 	}


                     878 ;212: 	return getEnumValue(dataValue, accessInfo->enumTable,


                     879 

000005dc 1284100c    880 	addne	r1,r4,12

000005e0 18bd4010    881 	ldmnefd	[sp]!,{r4,lr}

000005e4 1affff90*   882 	bne	getEnumValue

                     883 ;209: 	{


                     884 

                     885 ;210: 		return dataValue;


                     886 

000005e8 e8bd8010    887 	ldmfd	[sp]!,{r4,pc}

                     888 	.endf	readIntSettValue

                     889 	.align	4

                     890 ;dataValue	r1	local

                     891 

                     892 ;accessInfo	r4	param

                     893 

                     894 	.section ".bss","awb"

                     895 .L1606:

                     896 	.data

                     897 	.text

                     898 

                     899 ;213: 		accessInfo->enumTableSize);


                     900 ;214: }


                     901 

                     902 ;215: 


                     903 ;216: int readIntValue(IntBoolAccessInfo* accessInfo)



                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g881.s
                     904 	.align	4

                     905 	.align	4

                     906 readIntValue::

000005ec e92d4010    907 	stmfd	[sp]!,{r4,lr}

000005f0 e1a04000    908 	mov	r4,r0

                     909 ;217: {


                     910 

                     911 ;218: 	int dataValue = dataSliceGetIntValue(accessInfo->valueOffset);


                     912 

000005f4 e1d400b4    913 	ldrh	r0,[r4,4]

000005f8 eb000000*   914 	bl	dataSliceGetIntValue

                     915 ;219: 	if (accessInfo->enumTableSize == 0)


                     916 

000005fc e5942008    917 	ldr	r2,[r4,8]

00000600 e3520000    918 	cmp	r2,0

                     919 ;222: 	}


                     920 ;223: 	return getEnumValue(dataValue, accessInfo->enumTable, 


                     921 

00000604 1284100c    922 	addne	r1,r4,12

00000608 18bd4010    923 	ldmnefd	[sp]!,{r4,lr}

0000060c 1affff86*   924 	bne	getEnumValue

                     925 ;220: 	{


                     926 

                     927 ;221: 		return dataValue;


                     928 

00000610 e8bd8010    929 	ldmfd	[sp]!,{r4,pc}

                     930 	.endf	readIntValue

                     931 	.align	4

                     932 ;dataValue	r1	local

                     933 

                     934 ;accessInfo	r4	param

                     935 

                     936 	.section ".bss","awb"

                     937 .L1654:

                     938 	.data

                     939 	.text

                     940 

                     941 ;224: 		accessInfo->enumTableSize);


                     942 ;225: }


                     943 

                     944 ;226: 


                     945 ;227: int encodeUInt32Value(uint8_t* outBuf, int bufPos, uint32_t value, 


                     946 	.align	4

                     947 	.align	4

                     948 encodeUInt32Value::

00000614 e92d4000    949 	stmfd	[sp]!,{lr}

                     950 ;228: 	bool determineSize)


                     951 ;229: {


                     952 

                     953 ;230: 	if (determineSize)


                     954 

00000618 e1a0c002    955 	mov	r12,r2

0000061c e3530000    956 	cmp	r3,0

00000620 0a000003    957 	beq	.L1670

                     958 ;231: 	{


                     959 

                     960 ;232: 		int intSize = BerEncoder_UInt32determineEncodedSize(value);


                     961 

00000624 e1a0000c    962 	mov	r0,r12

00000628 eb000000*   963 	bl	BerEncoder_UInt32determineEncodedSize

                     964 ;233: 		return intSize + 2; // 2 for tag and length



                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g881.s
                     965 

0000062c e2800002    966 	add	r0,r0,2

00000630 ea000005    967 	b	.L1668

                     968 .L1670:

                     969 ;234: 	}


                     970 ;235: 


                     971 ;236: 	return BerEncoder_encodeUInt32WithTL(IEC61850_BER_UNSIGNED_INTEGER,


                     972 

00000634 e1a03001    973 	mov	r3,r1

00000638 e1a0100c    974 	mov	r1,r12

0000063c e1a02000    975 	mov	r2,r0

00000640 e3a00086    976 	mov	r0,134

00000644 e8bd4000    977 	ldmfd	[sp]!,{lr}

00000648 ea000000*   978 	b	BerEncoder_encodeUInt32WithTL

                     979 .L1668:

0000064c e8bd8000    980 	ldmfd	[sp]!,{pc}

                     981 	.endf	encodeUInt32Value

                     982 	.align	4

                     983 

                     984 ;outBuf	r0	param

                     985 ;bufPos	r1	param

                     986 ;value	r12	param

                     987 ;determineSize	r3	param

                     988 

                     989 	.section ".bss","awb"

                     990 .L1702:

                     991 	.data

                     992 	.text

                     993 

                     994 ;237: 		value, outBuf, bufPos);


                     995 ;238: }


                     996 

                     997 ;239: 


                     998 ;240: int encodeBoolValue(uint8_t* outBuf, int bufPos, bool value,


                     999 	.align	4

                    1000 	.align	4

                    1001 encodeBoolValue::

                    1002 ;241: 	bool determineSize)


                    1003 ;242: {


                    1004 

                    1005 ;243: 	if (determineSize)


                    1006 

00000650 e3530000   1007 	cmp	r3,0

                    1008 ;244: 	{


                    1009 

                    1010 ;245: 		return 3;


                    1011 

00000654 13a00003   1012 	movne	r0,3

00000658 1a000005   1013 	bne	.L1713

0000065c e1a03001   1014 	mov	r3,r1

00000660 e1a0c002   1015 	mov	r12,r2

                    1016 ;246: 	}


                    1017 ;247: 


                    1018 ;248: 	return BerEncoder_encodeBoolean(IEC61850_BER_BOOLEAN, value, outBuf,


                    1019 

00000664 e1a0100c   1020 	mov	r1,r12

00000668 e1a02000   1021 	mov	r2,r0

0000066c e3a00083   1022 	mov	r0,131

00000670 ea000000*  1023 	b	BerEncoder_encodeBoolean

                    1024 .L1713:

00000674 e12fff1e*  1025 	ret	


                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g881.s
                    1026 	.endf	encodeBoolValue

                    1027 	.align	4

                    1028 

                    1029 ;outBuf	r0	param

                    1030 ;bufPos	r1	param

                    1031 ;value	r12	param

                    1032 ;determineSize	r3	param

                    1033 

                    1034 	.section ".bss","awb"

                    1035 .L1750:

                    1036 	.data

                    1037 	.text

                    1038 

                    1039 ;249: 		bufPos);


                    1040 ;250: }


                    1041 

                    1042 ;251: 


                    1043 ;252: int readCodedEnum(CodedEnumAccessInfo* accessInfo)


                    1044 	.align	4

                    1045 	.align	4

                    1046 readCodedEnum::

00000678 e92d44f0   1047 	stmfd	[sp]!,{r4-r7,r10,lr}

0000067c e1a07000   1048 	mov	r7,r0

                    1049 ;253: {


                    1050 

00000680 e2876008   1051 	add	r6,r7,8

00000684 e3e0a000   1052 	mvn	r10,0

00000688 e3a04000   1053 	mov	r4,0

                    1054 ;254: 	uint8_t value = 0;


                    1055 

                    1056 ;255: 	int valIdx;


                    1057 ;256: 	for (valIdx = 0; valIdx < accessInfo->bitCount; ++valIdx)


                    1058 

0000068c e5971004   1059 	ldr	r1,[r7,4]

00000690 e1a05004   1060 	mov	r5,r4

00000694 e1550001   1061 	cmp	r5,r1

00000698 aa00000c   1062 	bge	.L1766

                    1063 .L1768:

                    1064 ;257: 	{


                    1065 

                    1066 ;258: 		int offset = accessInfo->valueOffsets[valIdx];


                    1067 

0000069c e1a04084   1068 	mov	r4,r4 lsl 1

000006a0 e4960004   1069 	ldr	r0,[r6],4

                    1070 ;259: 		value <<= 1;


                    1071 

000006a4 e20440ff   1072 	and	r4,r4,255

                    1073 ;260: 


                    1074 ;261: 		if (offset != -1)


                    1075 

000006a8 e150000a   1076 	cmp	r0,r10

000006ac 0a000004   1077 	beq	.L1767

                    1078 ;262: 		{


                    1079 

                    1080 ;263: 			value |= dataSliceGetBoolValue(offset);


                    1081 

000006b0 e1a00800   1082 	mov	r0,r0 lsl 16

000006b4 e1a00820   1083 	mov	r0,r0 lsr 16

000006b8 eb000000*  1084 	bl	dataSliceGetBoolValue

000006bc e1844000   1085 	orr	r4,r4,r0

000006c0 e5971004   1086 	ldr	r1,[r7,4]


                                                                      Page 19
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g881.s
                    1087 .L1767:

000006c4 e2855001   1088 	add	r5,r5,1

000006c8 e1550001   1089 	cmp	r5,r1

000006cc bafffff2   1090 	blt	.L1768

                    1091 .L1766:

                    1092 ;264: 


                    1093 ;265: 		}


                    1094 ;266: 	}


                    1095 ;267: 	value <<= (8 - accessInfo->bitCount);


                    1096 

000006d0 e2610008   1097 	rsb	r0,r1,8

000006d4 e1a01014   1098 	mov	r1,r4 lsl r0

000006d8 e20100ff   1099 	and	r0,r1,255

                    1100 ;268: 	return value;


                    1101 

000006dc e8bd84f0   1102 	ldmfd	[sp]!,{r4-r7,r10,pc}

                    1103 	.endf	readCodedEnum

                    1104 	.align	4

                    1105 ;value	r4	local

                    1106 ;valIdx	r5	local

                    1107 ;offset	r0	local

                    1108 

                    1109 ;accessInfo	r7	param

                    1110 

                    1111 	.section ".bss","awb"

                    1112 .L1828:

                    1113 	.data

                    1114 	.text

                    1115 

                    1116 ;269: }


                    1117 

                    1118 ;270: 


                    1119 ;271: int encodeReadCodedEnum(uint8_t* outBuf, int bufPos, void* descrStruct,


                    1120 	.align	4

                    1121 	.align	4

                    1122 encodeReadCodedEnum::

000006e0 e92d4070   1123 	stmfd	[sp]!,{r4-r6,lr}

                    1124 ;272: 	bool determineSize)


                    1125 ;273: {	


                    1126 

                    1127 ;274: 	//Поддерживается не более 8 бит		


                    1128 ;275: 	uint8_t value;


                    1129 ;276: 	CodedEnumAccessInfo* accessInfo = descrStruct;


                    1130 

                    1131 ;277: 


                    1132 ;278: 	if (determineSize)


                    1133 

000006e4 e24dd008   1134 	sub	sp,sp,8

000006e8 e3530000   1135 	cmp	r3,0

                    1136 ;279: 	{


                    1137 

                    1138 ;280: 		return 4;


                    1139 

000006ec 13a00004   1140 	movne	r0,4

000006f0 1a00000b   1141 	bne	.L1844

000006f4 e1a06001   1142 	mov	r6,r1

000006f8 e1a05000   1143 	mov	r5,r0

000006fc e1a04002   1144 	mov	r4,r2

                    1145 ;281: 	}


                    1146 ;282: 


                    1147 ;283: 	if (accessInfo->bitCount > 8)



                                                                      Page 20
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g881.s
                    1148 

                    1149 ;284: 	{


                    1150 

                    1151 ;285: 		ERROR_REPORT("More than 8 bits is not supported");


                    1152 ;286: 	}	


                    1153 ;287: 


                    1154 ;288: 	if (accessInfo->bitCount < 1)


                    1155 

                    1156 ;289: 	{


                    1157 

                    1158 ;290: 		ERROR_REPORT("Invalid coded enum: bitCount < 1");


                    1159 ;291: 	}


                    1160 ;292: 	


                    1161 ;293: 	value = readCodedEnum(accessInfo);


                    1162 

00000700 e1a00004   1163 	mov	r0,r4

00000704 ebffffdb*  1164 	bl	readCodedEnum

00000708 e1a03005   1165 	mov	r3,r5

0000070c e28d2007   1166 	add	r2,sp,7

00000710 e5cd0007   1167 	strb	r0,[sp,7]

                    1168 ;294: 


                    1169 ;295: 	return BerEncoder_encodeBitString(ASN_TYPEDESCRIPTION_BIT_STRING,


                    1170 

00000714 e58d6000   1171 	str	r6,[sp]

00000718 e5941004   1172 	ldr	r1,[r4,4]

0000071c e3a00084   1173 	mov	r0,132

00000720 eb000000*  1174 	bl	BerEncoder_encodeBitString

                    1175 .L1844:

00000724 e28dd008   1176 	add	sp,sp,8

00000728 e8bd8070   1177 	ldmfd	[sp]!,{r4-r6,pc}

                    1178 	.endf	encodeReadCodedEnum

                    1179 	.align	4

                    1180 ;value	[sp,7]	local

                    1181 ;accessInfo	r4	local

                    1182 

                    1183 ;outBuf	r5	param

                    1184 ;bufPos	r6	param

                    1185 ;descrStruct	r2	param

                    1186 ;determineSize	r3	param

                    1187 

                    1188 	.section ".bss","awb"

                    1189 .L1887:

                    1190 	.data

                    1191 	.text

                    1192 

                    1193 ;296: 		accessInfo->bitCount, &value, outBuf, bufPos);	


                    1194 ;297: }


                    1195 

                    1196 ;298: 


                    1197 ;299: int encodeOctetString8Value(uint8_t* outBuf, int bufPos, void* pValue,


                    1198 	.align	4

                    1199 	.align	4

                    1200 encodeOctetString8Value::

0000072c e92d4000   1201 	stmfd	[sp]!,{lr}

                    1202 ;300: 	bool determineSize)


                    1203 ;301: {


                    1204 

                    1205 ;302: 	if (determineSize)


                    1206 

00000730 e24dd004   1207 	sub	sp,sp,4

00000734 e3530000   1208 	cmp	r3,0


                                                                      Page 21
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g881.s
                    1209 ;303: 	{


                    1210 

                    1211 ;304: 		return 10;


                    1212 

00000738 13a0000a   1213 	movne	r0,10

0000073c 1a000005   1214 	bne	.L1901

                    1215 ;305: 	}


                    1216 ;306: 


                    1217 ;307: 	return BerEncoder_encodeOctetString(IEC61850_BER_OCTET_STRING, pValue, 8,


                    1218 

00000740 e58d1000   1219 	str	r1,[sp]

00000744 e1a01002   1220 	mov	r1,r2

00000748 e3a02008   1221 	mov	r2,8

0000074c e1a03000   1222 	mov	r3,r0

00000750 e3a00089   1223 	mov	r0,137

00000754 eb000000*  1224 	bl	BerEncoder_encodeOctetString

                    1225 .L1901:

00000758 e28dd004   1226 	add	sp,sp,4

0000075c e8bd8000   1227 	ldmfd	[sp]!,{pc}

                    1228 	.endf	encodeOctetString8Value

                    1229 	.align	4

                    1230 

                    1231 ;outBuf	r0	param

                    1232 ;bufPos	r1	param

                    1233 ;pValue	r2	param

                    1234 ;determineSize	r3	param

                    1235 

                    1236 	.section ".bss","awb"

                    1237 .L1942:

                    1238 	.data

                    1239 	.text

                    1240 

                    1241 ;308: 		outBuf, bufPos);


                    1242 ;309: }


                    1243 

                    1244 ;310: 


                    1245 ;311: int encodeReadInt32(uint8_t* outBuf, int bufPos, void* descrStruct, bool determineSize)


                    1246 	.align	4

                    1247 	.align	4

                    1248 encodeReadInt32::

00000760 e92d4070   1249 	stmfd	[sp]!,{r4-r6,lr}

                    1250 ;312: {


                    1251 

                    1252 ;313: 	int value;


                    1253 ;314: 	IntBoolAccessInfo* accessInfo = descrStruct;


                    1254 

                    1255 ;315: 


                    1256 ;316:     value = readIntValue(accessInfo);


                    1257 

00000764 e1a05001   1258 	mov	r5,r1

00000768 e1a06003   1259 	mov	r6,r3

0000076c e1a04000   1260 	mov	r4,r0

00000770 e1a00002   1261 	mov	r0,r2

00000774 ebffff9c*  1262 	bl	readIntValue

                    1263 ;317: 


                    1264 ;318:     if(determineSize)


                    1265 

00000778 e3560000   1266 	cmp	r6,0

0000077c 0a000002   1267 	beq	.L1958

                    1268 ;319:     {


                    1269 


                                                                      Page 22
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g881.s
                    1270 ;320:         int intSize = BerEncoder_Int32DetermineEncodedSize(value);


                    1271 

00000780 eb000000*  1272 	bl	BerEncoder_Int32DetermineEncodedSize

                    1273 ;321:         return intSize + 2; // 2 for tag and length


                    1274 

00000784 e2800002   1275 	add	r0,r0,2

00000788 ea000005   1276 	b	.L1956

                    1277 .L1958:

0000078c e1a03005   1278 	mov	r3,r5

00000790 e1a02004   1279 	mov	r2,r4

00000794 e1a01000   1280 	mov	r1,r0

                    1281 ;322:     }


                    1282 ;323:     


                    1283 ;324:     return BerEncoder_EncodeInt32WithTL(0x85, value, outBuf, bufPos);


                    1284 

00000798 e3a00085   1285 	mov	r0,133

0000079c e8bd4070   1286 	ldmfd	[sp]!,{r4-r6,lr}

000007a0 ea000000*  1287 	b	BerEncoder_EncodeInt32WithTL

                    1288 .L1956:

000007a4 e8bd8070   1289 	ldmfd	[sp]!,{r4-r6,pc}

                    1290 	.endf	encodeReadInt32

                    1291 	.align	4

                    1292 ;value	r1	local

                    1293 

                    1294 ;outBuf	r4	param

                    1295 ;bufPos	r5	param

                    1296 ;descrStruct	r2	param

                    1297 ;determineSize	r6	param

                    1298 

                    1299 	.section ".bss","awb"

                    1300 .L1990:

                    1301 	.data

                    1302 	.text

                    1303 

                    1304 ;325: }


                    1305 

                    1306 ;326: 


                    1307 ;327: int encodeReadInt32U(uint8_t* outBuf, int bufPos, void* descrStruct, bool determineSize)


                    1308 	.align	4

                    1309 	.align	4

                    1310 encodeReadInt32U::

000007a8 e92d4070   1311 	stmfd	[sp]!,{r4-r6,lr}

                    1312 ;328: {


                    1313 

                    1314 ;329: 	uint32_t value;


                    1315 ;330: 	


                    1316 ;331: 	IntBoolAccessInfo* accessInfo = descrStruct;


                    1317 

                    1318 ;332: 


                    1319 ;333: 	value = readIntValue(accessInfo);


                    1320 

000007ac e1a05001   1321 	mov	r5,r1

000007b0 e1a06003   1322 	mov	r6,r3

000007b4 e1a04000   1323 	mov	r4,r0

000007b8 e1a00002   1324 	mov	r0,r2

000007bc ebffff8a*  1325 	bl	readIntValue

                    1326 ;334: 


                    1327 ;335: 	return encodeUInt32Value(outBuf, bufPos, value, determineSize);


                    1328 

000007c0 e1a03006   1329 	mov	r3,r6

000007c4 e1a01005   1330 	mov	r1,r5


                                                                      Page 23
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g881.s
000007c8 e1a02000   1331 	mov	r2,r0

000007cc e1a00004   1332 	mov	r0,r4

000007d0 e8bd4070   1333 	ldmfd	[sp]!,{r4-r6,lr}

000007d4 eaffff8e*  1334 	b	encodeUInt32Value

                    1335 	.endf	encodeReadInt32U

                    1336 	.align	4

                    1337 

                    1338 ;outBuf	r4	param

                    1339 ;bufPos	r5	param

                    1340 ;descrStruct	r2	param

                    1341 ;determineSize	r6	param

                    1342 

                    1343 	.section ".bss","awb"

                    1344 .L2030:

                    1345 	.data

                    1346 	.text

                    1347 

                    1348 ;336: }


                    1349 

                    1350 ;337: 


                    1351 ;338: int encodeReadInt32Sett(uint8_t* outBuf, int bufPos, void* descrStruct, bool determineSize)


                    1352 	.align	4

                    1353 	.align	4

                    1354 encodeReadInt32Sett::

000007d8 e92d4070   1355 	stmfd	[sp]!,{r4-r6,lr}

                    1356 ;339: {


                    1357 

                    1358 ;340: 	int value;


                    1359 ;341: 


                    1360 ;342: 	IntBoolAccessInfo* accessInfo = descrStruct;


                    1361 

                    1362 ;343: 


                    1363 ;344: 	value = readIntSettValue(accessInfo);


                    1364 

000007dc e1a05001   1365 	mov	r5,r1

000007e0 e1a06003   1366 	mov	r6,r3

000007e4 e1a04000   1367 	mov	r4,r0

000007e8 e1a00002   1368 	mov	r0,r2

000007ec ebffff74*  1369 	bl	readIntSettValue

                    1370 ;345: 


                    1371 ;346: 	if (determineSize)


                    1372 

000007f0 e3560000   1373 	cmp	r6,0

000007f4 0a000002   1374 	beq	.L2039

                    1375 ;347: 	{


                    1376 

                    1377 ;348: 		int intSize = BerEncoder_Int32DetermineEncodedSize(value);


                    1378 

000007f8 eb000000*  1379 	bl	BerEncoder_Int32DetermineEncodedSize

                    1380 ;349: 		return intSize + 2; // 2 for tag and length


                    1381 

000007fc e2800002   1382 	add	r0,r0,2

00000800 ea000005   1383 	b	.L2037

                    1384 .L2039:

00000804 e1a03005   1385 	mov	r3,r5

00000808 e1a02004   1386 	mov	r2,r4

0000080c e1a01000   1387 	mov	r1,r0

                    1388 ;350: 	}


                    1389 ;351: 


                    1390 ;352: 	return BerEncoder_EncodeInt32WithTL(0x85, value, outBuf, bufPos);


                    1391 


                                                                      Page 24
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g881.s
00000810 e3a00085   1392 	mov	r0,133

00000814 e8bd4070   1393 	ldmfd	[sp]!,{r4-r6,lr}

00000818 ea000000*  1394 	b	BerEncoder_EncodeInt32WithTL

                    1395 .L2037:

0000081c e8bd8070   1396 	ldmfd	[sp]!,{r4-r6,pc}

                    1397 	.endf	encodeReadInt32Sett

                    1398 	.align	4

                    1399 ;value	r1	local

                    1400 

                    1401 ;outBuf	r4	param

                    1402 ;bufPos	r5	param

                    1403 ;descrStruct	r2	param

                    1404 ;determineSize	r6	param

                    1405 

                    1406 	.section ".bss","awb"

                    1407 .L2070:

                    1408 	.data

                    1409 	.text

                    1410 

                    1411 ;353: }


                    1412 

                    1413 ;354: 


                    1414 ;355: int encodeReadInt32USett(uint8_t* outBuf, int bufPos, void* descrStruct, bool determineSize)


                    1415 	.align	4

                    1416 	.align	4

                    1417 encodeReadInt32USett::

00000820 e92d4070   1418 	stmfd	[sp]!,{r4-r6,lr}

                    1419 ;356: {


                    1420 

                    1421 ;357:     uint32_t value;


                    1422 ;358: 


                    1423 ;359:     IntBoolAccessInfo* accessInfo = descrStruct;


                    1424 

                    1425 ;360: 


                    1426 ;361:     value = readIntSettValue(accessInfo);


                    1427 

00000824 e1a05001   1428 	mov	r5,r1

00000828 e1a06003   1429 	mov	r6,r3

0000082c e1a04000   1430 	mov	r4,r0

00000830 e1a00002   1431 	mov	r0,r2

00000834 ebffff62*  1432 	bl	readIntSettValue

                    1433 ;362: 


                    1434 ;363: 	return encodeUInt32Value(outBuf, bufPos, value, determineSize);


                    1435 

00000838 e1a03006   1436 	mov	r3,r6

0000083c e1a01005   1437 	mov	r1,r5

00000840 e1a02000   1438 	mov	r2,r0

00000844 e1a00004   1439 	mov	r0,r4

00000848 e8bd4070   1440 	ldmfd	[sp]!,{r4-r6,lr}

0000084c eaffff70*  1441 	b	encodeUInt32Value

                    1442 	.endf	encodeReadInt32USett

                    1443 	.align	4

                    1444 

                    1445 ;outBuf	r4	param

                    1446 ;bufPos	r5	param

                    1447 ;descrStruct	r2	param

                    1448 ;determineSize	r6	param

                    1449 

                    1450 	.section ".bss","awb"

                    1451 .L2110:

                    1452 	.data


                                                                      Page 25
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g881.s
                    1453 	.text

                    1454 

                    1455 ;364: }


                    1456 

                    1457 ;365: 


                    1458 ;366: 


                    1459 ;367: int readBoolValue(IntBoolAccessInfo* accessInfo)


                    1460 	.align	4

                    1461 	.align	4

                    1462 readBoolValue::

                    1463 ;368: {


                    1464 

                    1465 ;369: 	return dataSliceGetBoolValue(accessInfo->valueOffset);


                    1466 

00000850 e1d000b4   1467 	ldrh	r0,[r0,4]

00000854 ea000000*  1468 	b	dataSliceGetBoolValue

                    1469 	.endf	readBoolValue

                    1470 	.align	4

                    1471 

                    1472 ;accessInfo	r0	param

                    1473 

                    1474 	.section ".bss","awb"

                    1475 .L2142:

                    1476 	.data

                    1477 	.text

                    1478 

                    1479 ;370: }


                    1480 

                    1481 ;371: 


                    1482 ;372: int encodeReadBoolean(uint8_t* outBuf, int bufPos, void* descrStruct, 


                    1483 	.align	4

                    1484 	.align	4

                    1485 encodeReadBoolean::

00000858 e92d4070   1486 	stmfd	[sp]!,{r4-r6,lr}

                    1487 ;373: 	bool determineSize)


                    1488 ;374: {


                    1489 

                    1490 ;375: 	int value = FALSE;


                    1491 

                    1492 ;376: 


                    1493 ;377: 	IntBoolAccessInfo* accessInfo = descrStruct;


                    1494 

                    1495 ;378: 


                    1496 ;379: 	value = readBoolValue(accessInfo);


                    1497 

0000085c e1a05001   1498 	mov	r5,r1

00000860 e1a06003   1499 	mov	r6,r3

00000864 e1a04000   1500 	mov	r4,r0

00000868 e1a00002   1501 	mov	r0,r2

0000086c ebfffff7*  1502 	bl	readBoolValue

                    1503 ;380: 


                    1504 ;381: 	return encodeBoolValue(outBuf, bufPos, value, determineSize);		


                    1505 

00000870 e1a03006   1506 	mov	r3,r6

00000874 e1a01005   1507 	mov	r1,r5

00000878 e20020ff   1508 	and	r2,r0,255

0000087c e1a00004   1509 	mov	r0,r4

00000880 e8bd4070   1510 	ldmfd	[sp]!,{r4-r6,lr}

00000884 eaffff71*  1511 	b	encodeBoolValue

                    1512 	.endf	encodeReadBoolean

                    1513 	.align	4


                                                                      Page 26
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g881.s
                    1514 

                    1515 ;outBuf	r4	param

                    1516 ;bufPos	r5	param

                    1517 ;descrStruct	r2	param

                    1518 ;determineSize	r6	param

                    1519 

                    1520 	.section ".bss","awb"

                    1521 .L2174:

                    1522 	.data

                    1523 	.text

                    1524 

                    1525 ;382: }


                    1526 

                    1527 ;383: 


                    1528 ;384: int encodeAccessAttrQuality(uint8_t* outBuf, int bufPos, bool determineSize)


                    1529 	.align	4

                    1530 	.align	4

                    1531 encodeAccessAttrQuality::

                    1532 ;385: {


                    1533 

                    1534 ;386:     //<Unknown len="1" tag="0x84" value="0xf3" valueStr="'\xf3'"/>


                    1535 ;387:     if(determineSize)


                    1536 

00000888 e3520000   1537 	cmp	r2,0

                    1538 ;390:     }


                    1539 ;391: 


                    1540 ;392:     return BerEncoder_EncodeInt32WithTL(ASN_TYPEDESCRIPTION_BIT_STRING,


                    1541 

0000088c 01a03001   1542 	moveq	r3,r1

00000890 03e0100c   1543 	mvneq	r1,12

00000894 01a02000   1544 	moveq	r2,r0

00000898 03a00084   1545 	moveq	r0,132

0000089c 0a000000*  1546 	beq	BerEncoder_EncodeInt32WithTL

                    1547 ;388:     {


                    1548 

                    1549 ;389:         return 3;


                    1550 

000008a0 e3a00003   1551 	mov	r0,3

000008a4 e12fff1e*  1552 	ret	

                    1553 	.endf	encodeAccessAttrQuality

                    1554 	.align	4

                    1555 

                    1556 ;outBuf	r0	param

                    1557 ;bufPos	r1	param

                    1558 ;determineSize	r2	param

                    1559 

                    1560 	.section ".bss","awb"

                    1561 .L2214:

                    1562 	.data

                    1563 	.text

                    1564 

                    1565 ;393:                                               -13, outBuf, bufPos);


                    1566 ;394: 


                    1567 ;395: }


                    1568 

                    1569 ;396: 


                    1570 ;397: int encodeAccessAttrBitString(int bitCount, uint8_t* outBuf, int bufPos, 


                    1571 	.align	4

                    1572 	.align	4

                    1573 encodeAccessAttrBitString::

                    1574 ;398: 	bool determineSize)



                                                                      Page 27
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g881.s
                    1575 ;399: {	


                    1576 

                    1577 ;400: 	//<Unknown len="1" tag="0x84" value="0xf3" valueStr="'\xf3'"/>


                    1578 ;401: 	if (determineSize)


                    1579 

000008a8 e3530000   1580 	cmp	r3,0

                    1581 ;404: 	}


                    1582 ;405: 


                    1583 ;406: 	return BerEncoder_EncodeInt32WithTL(ASN_TYPEDESCRIPTION_BIT_STRING,


                    1584 

000008ac 01a03002   1585 	moveq	r3,r2

000008b0 01a02001   1586 	moveq	r2,r1

000008b4 02601000   1587 	rsbeq	r1,r0,0

000008b8 03a00084   1588 	moveq	r0,132

000008bc 0a000000*  1589 	beq	BerEncoder_EncodeInt32WithTL

                    1590 ;402: 	{


                    1591 

                    1592 ;403: 		return 3;


                    1593 

000008c0 e3a00003   1594 	mov	r0,3

000008c4 e12fff1e*  1595 	ret	

                    1596 	.endf	encodeAccessAttrBitString

                    1597 	.align	4

                    1598 

                    1599 ;bitCount	r0	param

                    1600 ;outBuf	r1	param

                    1601 ;bufPos	r2	param

                    1602 ;determineSize	r3	param

                    1603 

                    1604 	.section ".bss","awb"

                    1605 .L2262:

                    1606 	.data

                    1607 	.text

                    1608 

                    1609 ;407: 		-bitCount, outBuf, bufPos);


                    1610 ;408: 


                    1611 ;409: }


                    1612 

                    1613 ;410: 


                    1614 ;411: int encodeAccessAttrBitStringConst(int constPos, uint8_t* outBuf, int bufPos,


                    1615 	.align	4

                    1616 	.align	4

                    1617 	.align	4

                    1618 encodeAccessAttrBitStringConst::

                    1619 ;412: 	bool determineSize)


                    1620 ;413: {


                    1621 

                    1622 ;414: 	int bitCount;


                    1623 ;415: 	int padding;


                    1624 ;416: 	int len;


                    1625 ;417: 	if (determineSize)


                    1626 

000008c8 e3530000   1627 	cmp	r3,0

                    1628 ;418: 	{


                    1629 

                    1630 ;419: 		return 3;


                    1631 

000008cc 13a00003   1632 	movne	r0,3

000008d0 1a00000d   1633 	bne	.L2276

                    1634 ;420: 	}


                    1635 ;421: 	len = iedModel[constPos + 1];



                                                                      Page 28
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g881.s
                    1636 

000008d4 e59fc270*  1637 	ldr	r12,.L2368

000008d8 e59c3000   1638 	ldr	r3,[r12]

000008dc e0833000   1639 	add	r3,r3,r0

000008e0 e5d3c001   1640 	ldrb	r12,[r3,1]

                    1641 ;422: 	RET_IF_NOT(len >= 2 && len < 127, "Invalid bitstring constant length");


                    1642 

000008e4 e24c0002   1643 	sub	r0,r12,2

000008e8 e350007d   1644 	cmp	r0,125

000008ec 2a000005   1645 	bhs	.L2286

                    1646 ;423: 	padding = iedModel[constPos + 2];


                    1647 

000008f0 e5d30002   1648 	ldrb	r0,[r3,2]

                    1649 ;424: 	RET_IF_NOT(padding < 8, "Invalid bitstring constant length");


                    1650 

000008f4 e3500008   1651 	cmp	r0,8

                    1652 ;425: 	bitCount = (len - 1) * 8 - padding;


                    1653 

000008f8 b060018c   1654 	rsblt	r0,r0,r12 lsl 3

000008fc b2400008   1655 	sublt	r0,r0,8

                    1656 ;426: 	return encodeAccessAttrBitString(bitCount, outBuf, bufPos, FALSE);


                    1657 

00000900 b3a03000   1658 	movlt	r3,0

00000904 baffffe7*  1659 	blt	encodeAccessAttrBitString

                    1660 .L2286:

00000908 e3a00000   1661 	mov	r0,0

                    1662 .L2276:

0000090c e12fff1e*  1663 	ret	

                    1664 	.endf	encodeAccessAttrBitStringConst

                    1665 	.align	4

                    1666 ;padding	r0	local

                    1667 ;len	r12	local

                    1668 

                    1669 ;constPos	r0	param

                    1670 ;outBuf	r1	param

                    1671 ;bufPos	r2	param

                    1672 ;determineSize	r3	param

                    1673 

                    1674 	.section ".bss","awb"

                    1675 .L2346:

                    1676 	.data

                    1677 	.text

                    1678 

                    1679 ;427: }


                    1680 

                    1681 ;428: 


                    1682 ;429: int encodeAccessAttrTimeStamp(uint8_t* outBuf, int bufPos, bool determineSize)


                    1683 	.align	4

                    1684 	.align	4

                    1685 encodeAccessAttrTimeStamp::

                    1686 ;430: {


                    1687 

                    1688 ;431:     //<Unknown len="0" tag="0x91" value="" valueStr="''"/>


                    1689 ;432:     if(determineSize)


                    1690 

00000910 e3520000   1691 	cmp	r2,0

                    1692 ;433:     {


                    1693 

                    1694 ;434:         return 2;


                    1695 

00000914 13a00002   1696 	movne	r0,2


                                                                      Page 29
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g881.s
00000918 1a000005   1697 	bne	.L2369

                    1698 ;435:     }


                    1699 ;436: 


                    1700 ;437:     outBuf[bufPos++] = 0x91;


                    1701 

0000091c e3a02091   1702 	mov	r2,145

00000920 e7c02001   1703 	strb	r2,[r0,r1]

00000924 e2811001   1704 	add	r1,r1,1

                    1705 ;438:     outBuf[bufPos++] = 0;//Длина


                    1706 

00000928 e3a02000   1707 	mov	r2,0

0000092c e7c02001   1708 	strb	r2,[r0,r1]

00000930 e2810001   1709 	add	r0,r1,1

                    1710 ;439:     return bufPos;


                    1711 

                    1712 .L2369:

00000934 e12fff1e*  1713 	ret	

                    1714 	.endf	encodeAccessAttrTimeStamp

                    1715 	.align	4

                    1716 

                    1717 ;outBuf	r0	param

                    1718 ;bufPos	r1	param

                    1719 ;determineSize	r2	param

                    1720 

                    1721 	.section ".bss","awb"

                    1722 .L2406:

                    1723 	.data

                    1724 	.text

                    1725 

                    1726 ;440: }


                    1727 

                    1728 ;441: 


                    1729 ;442: int encodeAccessAttrFloat(uint8_t* outBuf, int bufPos, bool determineSize)


                    1730 	.align	4

                    1731 	.align	4

                    1732 encodeAccessAttrFloat::

00000938 e92d4010   1733 	stmfd	[sp]!,{r4,lr}

                    1734 ;443: {


                    1735 

                    1736 ;444:     if(determineSize)


                    1737 

0000093c e3520000   1738 	cmp	r2,0

                    1739 ;445:     {


                    1740 

                    1741 ;446:         return 8;


                    1742 

00000940 13a00008   1743 	movne	r0,8

00000944 1a000010   1744 	bne	.L2420

00000948 e1a04000   1745 	mov	r4,r0

                    1746 ;447:     }    


                    1747 ;448: 


                    1748 ;449:     // Описание типа FLOAT32


                    1749 ;450:     bufPos = BerEncoder_encodeTL(ASN_TYPEDESCRIPTION_FLOAT, 6,


                    1750 

0000094c e1a02004   1751 	mov	r2,r4

00000950 e1a03001   1752 	mov	r3,r1

00000954 e3a01006   1753 	mov	r1,6

00000958 e3a000a7   1754 	mov	r0,167

0000095c eb000000*  1755 	bl	BerEncoder_encodeTL

                    1756 ;451:         outBuf, bufPos);


                    1757 ;452: 



                                                                      Page 30
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g881.s
                    1758 ;453:     //Ширина формата


                    1759 ;454:     bufPos = BerEncoder_encodeUInt32WithTL(ASN_INTEGER, 32,


                    1760 

00000960 e1a02004   1761 	mov	r2,r4

00000964 e3a01020   1762 	mov	r1,32

00000968 e1a03000   1763 	mov	r3,r0

0000096c e3a00002   1764 	mov	r0,2

00000970 eb000000*  1765 	bl	BerEncoder_encodeUInt32WithTL

                    1766 ;455:         outBuf, bufPos);


                    1767 ;456: 


                    1768 ;457:     //Ширина экспоненты


                    1769 ;458:     bufPos = BerEncoder_encodeUInt32WithTL(ASN_INTEGER, 8,


                    1770 

00000974 e1a02004   1771 	mov	r2,r4

00000978 e3a01008   1772 	mov	r1,8

0000097c e1a03000   1773 	mov	r3,r0

00000980 e3a00002   1774 	mov	r0,2

00000984 e8bd4010   1775 	ldmfd	[sp]!,{r4,lr}

00000988 ea000000*  1776 	b	BerEncoder_encodeUInt32WithTL

                    1777 .L2420:

0000098c e8bd8010   1778 	ldmfd	[sp]!,{r4,pc}

                    1779 	.endf	encodeAccessAttrFloat

                    1780 	.align	4

                    1781 

                    1782 ;outBuf	r4	param

                    1783 ;bufPos	r1	param

                    1784 ;determineSize	r2	param

                    1785 

                    1786 	.section ".bss","awb"

                    1787 .L2454:

                    1788 	.data

                    1789 	.text

                    1790 

                    1791 ;461: }


                    1792 

                    1793 ;462: 


                    1794 ;463: int encodeAccessAttrString(uint8_t* outBuf, int bufPos, uint8_t tag, int size, 


                    1795 	.align	4

                    1796 	.align	4

                    1797 encodeAccessAttrString::

00000990 e92d4010   1798 	stmfd	[sp]!,{r4,lr}

                    1799 ;464: 	bool determineSize)


                    1800 ;465: {


                    1801 

                    1802 ;466: 	if (determineSize)


                    1803 

00000994 e1a04001   1804 	mov	r4,r1

00000998 e1a0c002   1805 	mov	r12,r2

0000099c e5dd2008   1806 	ldrb	r2,[sp,8]

000009a0 e2631000   1807 	rsb	r1,r3,0

000009a4 e3520000   1808 	cmp	r2,0

                    1809 ;469: 		//return 4;


                    1810 ;470: 	}


                    1811 ;471: 


                    1812 ;472: 	// Описание типа 


                    1813 ;473: 	bufPos = BerEncoder_EncodeInt32WithTL(tag, -size, outBuf, bufPos);


                    1814 

000009a8 01a03004   1815 	moveq	r3,r4

000009ac 01a02000   1816 	moveq	r2,r0

000009b0 01a0000c   1817 	moveq	r0,r12

000009b4 08bd4010   1818 	ldmeqfd	[sp]!,{r4,lr}


                                                                      Page 31
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g881.s
000009b8 0a000000*  1819 	beq	BerEncoder_EncodeInt32WithTL

                    1820 ;467: 	{		


                    1821 

                    1822 ;468: 		return BerEncoder_Int32DetermineEncodedSize(-size) + 2;


                    1823 

000009bc e1a00001   1824 	mov	r0,r1

000009c0 eb000000*  1825 	bl	BerEncoder_Int32DetermineEncodedSize

000009c4 e2800002   1826 	add	r0,r0,2

000009c8 e8bd8010   1827 	ldmfd	[sp]!,{r4,pc}

                    1828 	.endf	encodeAccessAttrString

                    1829 	.align	4

                    1830 

                    1831 ;outBuf	r0	param

                    1832 ;bufPos	r4	param

                    1833 ;tag	r12	param

                    1834 ;size	r3	param

                    1835 ;determineSize	r5	param

                    1836 

                    1837 	.section ".bss","awb"

                    1838 .L2502:

                    1839 	.data

                    1840 	.text

                    1841 

                    1842 ;476: }


                    1843 

                    1844 ;477: 


                    1845 ;478: int encodeAccessAttrInt(uint8_t* outBuf, int bufPos, uint8_t bitCount, 


                    1846 	.align	4

                    1847 	.align	4

                    1848 encodeAccessAttrInt::

000009cc e92d4000   1849 	stmfd	[sp]!,{lr}

                    1850 ;479: 	bool determineSize)


                    1851 ;480: {


                    1852 

                    1853 ;481:     //<Unknown len="1" tag="0x85" value="0x20" valueStr="' '"/>


                    1854 ;482:     uint8_t description = bitCount;


                    1855 

000009d0 e24dd008   1856 	sub	sp,sp,8

000009d4 e5cd2007   1857 	strb	r2,[sp,7]

                    1858 ;483: 


                    1859 ;484:     if(determineSize)


                    1860 

000009d8 e3530000   1861 	cmp	r3,0

                    1862 ;485:     {


                    1863 

                    1864 ;486:         return 3;


                    1865 

000009dc 13a00003   1866 	movne	r0,3

000009e0 1a000005   1867 	bne	.L2516

                    1868 ;487:     }


                    1869 ;488: 


                    1870 ;489:     // Описание типа INT


                    1871 ;490:     return BerEncoder_encodeOctetString(IEC61850_BER_INTEGER, &description, 1,


                    1872 

000009e4 e58d1000   1873 	str	r1,[sp]

000009e8 e28d1007   1874 	add	r1,sp,7

000009ec e3a02001   1875 	mov	r2,1

000009f0 e1a03000   1876 	mov	r3,r0

000009f4 e3a00085   1877 	mov	r0,133

000009f8 eb000000*  1878 	bl	BerEncoder_encodeOctetString

                    1879 .L2516:


                                                                      Page 32
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g881.s
000009fc e28dd008   1880 	add	sp,sp,8

00000a00 e8bd8000   1881 	ldmfd	[sp]!,{pc}

                    1882 	.endf	encodeAccessAttrInt

                    1883 	.align	4

                    1884 ;description	[sp,7]	local

                    1885 

                    1886 ;outBuf	r0	param

                    1887 ;bufPos	r1	param

                    1888 ;bitCount	r2	param

                    1889 ;determineSize	r3	param

                    1890 

                    1891 	.section ".bss","awb"

                    1892 .L2550:

                    1893 	.data

                    1894 	.text

                    1895 

                    1896 ;491: 		outBuf, bufPos);


                    1897 ;492: }


                    1898 

                    1899 ;493: 


                    1900 ;494: int encodeAccessAttrUInt(uint8_t* outBuf, int bufPos, uint8_t bitCount, 


                    1901 	.align	4

                    1902 	.align	4

                    1903 encodeAccessAttrUInt::

00000a04 e92d4000   1904 	stmfd	[sp]!,{lr}

                    1905 ;495: 	bool determineSize)


                    1906 ;496: {	


                    1907 

                    1908 ;497: 	uint8_t description = bitCount;	


                    1909 

00000a08 e24dd008   1910 	sub	sp,sp,8

00000a0c e5cd2007   1911 	strb	r2,[sp,7]

                    1912 ;498: 


                    1913 ;499: 	if (determineSize)


                    1914 

00000a10 e3530000   1915 	cmp	r3,0

                    1916 ;500: 	{


                    1917 

                    1918 ;501: 		return 3;


                    1919 

00000a14 13a00003   1920 	movne	r0,3

00000a18 1a000005   1921 	bne	.L2564

                    1922 ;502: 	}


                    1923 ;503: 


                    1924 ;504: 	// Описание типа INT


                    1925 ;505: 	return BerEncoder_encodeOctetString(IEC61850_BER_UNSIGNED_INTEGER, 


                    1926 

00000a1c e58d1000   1927 	str	r1,[sp]

00000a20 e28d1007   1928 	add	r1,sp,7

00000a24 e3a02001   1929 	mov	r2,1

00000a28 e1a03000   1930 	mov	r3,r0

00000a2c e3a00086   1931 	mov	r0,134

00000a30 eb000000*  1932 	bl	BerEncoder_encodeOctetString

                    1933 .L2564:

00000a34 e28dd008   1934 	add	sp,sp,8

00000a38 e8bd8000   1935 	ldmfd	[sp]!,{pc}

                    1936 	.endf	encodeAccessAttrUInt

                    1937 	.align	4

                    1938 ;description	[sp,7]	local

                    1939 

                    1940 ;outBuf	r0	param


                                                                      Page 33
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g881.s
                    1941 ;bufPos	r1	param

                    1942 ;bitCount	r2	param

                    1943 ;determineSize	r3	param

                    1944 

                    1945 	.section ".bss","awb"

                    1946 .L2598:

                    1947 	.data

                    1948 	.text

                    1949 

                    1950 ;506: 		&description, 1, outBuf, bufPos);


                    1951 ;507: }


                    1952 

                    1953 ;508: 


                    1954 ;509: int encodeAccessAttrInt128(uint8_t* outBuf, int bufPos, bool determineSize)


                    1955 	.align	4

                    1956 	.align	4

                    1957 encodeAccessAttrInt128::

00000a3c e92d4000   1958 	stmfd	[sp]!,{lr}

                    1959 ;510: {	


                    1960 

00000a40 e59f3108*  1961 	ldr	r3,.L2662

00000a44 e24dd008   1962 	sub	sp,sp,8

00000a48 e5d3c000   1963 	ldrb	r12,[r3]

00000a4c e3520000   1964 	cmp	r2,0

                    1965 ;514: 	{


                    1966 

                    1967 ;515: 		return 4;


                    1968 

00000a50 e5cdc006   1969 	strb	r12,[sp,6]

00000a54 e5d33001   1970 	ldrb	r3,[r3,1]

00000a58 13a00004   1971 	movne	r0,4

00000a5c e5cd3007   1972 	strb	r3,[sp,7]

                    1973 ;511: 	uint8_t description[2] = { 0x00, 0x20 };


                    1974 

                    1975 ;512: 


                    1976 ;513: 	if (determineSize)


                    1977 

00000a60 1a000005   1978 	bne	.L2612

                    1979 ;516: 	}


                    1980 ;517: 


                    1981 ;518: 	// Описание типа INT


                    1982 ;519: 	return BerEncoder_encodeOctetString(IEC61850_BER_INTEGER, description, 2,


                    1983 

00000a64 e58d1000   1984 	str	r1,[sp]

00000a68 e28d1006   1985 	add	r1,sp,6

00000a6c e3a02002   1986 	mov	r2,2

00000a70 e1a03000   1987 	mov	r3,r0

00000a74 e3a00085   1988 	mov	r0,133

00000a78 eb000000*  1989 	bl	BerEncoder_encodeOctetString

                    1990 .L2612:

00000a7c e28dd008   1991 	add	sp,sp,8

00000a80 e8bd8000   1992 	ldmfd	[sp]!,{pc}

                    1993 	.endf	encodeAccessAttrInt128

                    1994 	.align	4

                    1995 ;description	[sp,6]	local

                    1996 ;.L2647	.L2650	static

                    1997 

                    1998 ;outBuf	r0	param

                    1999 ;bufPos	r1	param

                    2000 ;determineSize	r2	param

                    2001 


                                                                      Page 34
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g881.s
                    2002 	.section ".bss","awb"

                    2003 .L2646:

                    2004 	.section ".rodata","a"

00000008 00        2005 .L2650:	.space	1

00000009 20        2006 	.data.b	32

                    2007 	.type	.L2650,$object

                    2008 	.size	.L2650,2

                    2009 	.data

                    2010 	.text

                    2011 

                    2012 ;520: 		outBuf, bufPos);


                    2013 ;521: }


                    2014 

                    2015 ;522: 


                    2016 ;523: int encodeAccessAttrBoolean(uint8_t* outBuf, int bufPos, bool determineSize)


                    2017 	.align	4

                    2018 	.align	4

                    2019 encodeAccessAttrBoolean::

                    2020 ;524: {


                    2021 

                    2022 ;525:     //<Unknown len="0" tag="0x83" value="" valueStr="''"/>


                    2023 ;526:     if(determineSize)


                    2024 

00000a84 e3520000   2025 	cmp	r2,0

                    2026 ;529:     }


                    2027 ;530: 


                    2028 ;531:     // Описание типа BOOLEAN (только тэг и длина. Данных никаких нет)


                    2029 ;532:     return BerEncoder_encodeTL(IEC61850_BER_BOOLEAN, 0, outBuf, bufPos);


                    2030 

00000a88 01a03001   2031 	moveq	r3,r1

00000a8c 03a01000   2032 	moveq	r1,0

00000a90 01a02000   2033 	moveq	r2,r0

00000a94 03a00083   2034 	moveq	r0,131

00000a98 0a000000*  2035 	beq	BerEncoder_encodeTL

                    2036 ;527:     {


                    2037 

                    2038 ;528:         return 2;


                    2039 

00000a9c e3a00002   2040 	mov	r0,2

00000aa0 e12fff1e*  2041 	ret	

                    2042 	.endf	encodeAccessAttrBoolean

                    2043 	.align	4

                    2044 

                    2045 ;outBuf	r0	param

                    2046 ;bufPos	r1	param

                    2047 ;determineSize	r2	param

                    2048 

                    2049 	.section ".bss","awb"

                    2050 .L2694:

                    2051 	.data

                    2052 	.text

                    2053 

                    2054 ;533: }


                    2055 

                    2056 ;534: 


                    2057 ;535: int encodeAccessAttrCodedEnum(uint8_t* outBuf, int bufPos, int accessDataPos,


                    2058 	.align	4

                    2059 	.align	4

                    2060 encodeAccessAttrCodedEnum::

00000aa4 e92d4030   2061 	stmfd	[sp]!,{r4-r5,lr}

                    2062 ;536: 	bool determineSize)



                                                                      Page 35
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g881.s
                    2063 ;537: {	


                    2064 

                    2065 ;538: 	CodedEnumAccessInfo* pAccessInfo;


                    2066 ;539: 	if (determineSize)


                    2067 

00000aa8 e3530000   2068 	cmp	r3,0

                    2069 ;540: 	{	


                    2070 

                    2071 ;541: 		return 3;


                    2072 

00000aac 13a00003   2073 	movne	r0,3

00000ab0 1a00000c   2074 	bne	.L2708

00000ab4 e1a05001   2075 	mov	r5,r1

                    2076 ;542: 	}


                    2077 ;543: 	pAccessInfo = (CodedEnumAccessInfo*)getAlignedDescrStruct(accessDataPos);


                    2078 

00000ab8 e1a04000   2079 	mov	r4,r0

00000abc e1a00002   2080 	mov	r0,r2

00000ac0 eb000000*  2081 	bl	getAlignedDescrStruct

                    2082 ;544: 	if (pAccessInfo == NULL)


                    2083 

00000ac4 e3500000   2084 	cmp	r0,0

                    2085 ;545: 	{


                    2086 

                    2087 ;546: 		ERROR_REPORT("Unable to get access to the info struct");


                    2088 ;547: 		return 0;


                    2089 

00000ac8 0a000006   2090 	beq	.L2708

                    2091 ;548: 	}


                    2092 ;549: 


                    2093 ;550: 	return BerEncoder_EncodeInt32WithTL(ASN_TYPEDESCRIPTION_BIT_STRING,


                    2094 

00000acc e1a03005   2095 	mov	r3,r5

00000ad0 e5900004   2096 	ldr	r0,[r0,4]

00000ad4 e1a02004   2097 	mov	r2,r4

00000ad8 e2601000   2098 	rsb	r1,r0,0

00000adc e3a00084   2099 	mov	r0,132

00000ae0 e8bd4030   2100 	ldmfd	[sp]!,{r4-r5,lr}

00000ae4 ea000000*  2101 	b	BerEncoder_EncodeInt32WithTL

                    2102 .L2708:

00000ae8 e8bd8030   2103 	ldmfd	[sp]!,{r4-r5,pc}

                    2104 	.endf	encodeAccessAttrCodedEnum

                    2105 	.align	4

                    2106 ;pAccessInfo	r0	local

                    2107 

                    2108 ;outBuf	r4	param

                    2109 ;bufPos	r5	param

                    2110 ;accessDataPos	r2	param

                    2111 ;determineSize	r3	param

                    2112 

                    2113 	.section ".bss","awb"

                    2114 .L2771:

                    2115 	.data

                    2116 	.text

                    2117 

                    2118 ;551: 		-pAccessInfo->bitCount, outBuf, bufPos);


                    2119 ;552: }


                    2120 

                    2121 ;553: 


                    2122 ;554: int encodeAccessAttrEntryTime(uint8_t* outBuf, int bufPos, bool determineSize)


                    2123 	.align	4


                                                                      Page 36
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g881.s
                    2124 	.align	4

                    2125 encodeAccessAttrEntryTime::

                    2126 ;555: {


                    2127 

                    2128 ;556:     if (determineSize)


                    2129 

00000aec e3520000   2130 	cmp	r2,0

                    2131 ;559:     }


                    2132 ;560: 


                    2133 ;561:     // Описание типа


                    2134 ;562:     bufPos = BerEncoder_EncodeInt32WithTL(IEC61850_BER_BINARY_TIME,


                    2135 

00000af0 01a03001   2136 	moveq	r3,r1

00000af4 03a01001   2137 	moveq	r1,1

00000af8 01a02000   2138 	moveq	r2,r0

00000afc 03a0008c   2139 	moveq	r0,140

00000b00 0a000000*  2140 	beq	BerEncoder_EncodeInt32WithTL

                    2141 ;557:     {


                    2142 

                    2143 ;558:         return 3;


                    2144 

00000b04 e3a00003   2145 	mov	r0,3

00000b08 e12fff1e*  2146 	ret	

                    2147 	.endf	encodeAccessAttrEntryTime

                    2148 	.align	4

                    2149 

                    2150 ;outBuf	r0	param

                    2151 ;bufPos	r1	param

                    2152 ;determineSize	r2	param

                    2153 

                    2154 	.section ".bss","awb"

                    2155 .L2822:

                    2156 	.data

                    2157 	.text

                    2158 

                    2159 ;566: }


                    2160 

                    2161 ;567: 


                    2162 ;568: 


                    2163 ;569: int encodeAccessAttrConst(uint8_t* outBuf, int bufPos, int constPos, bool determineSize)


                    2164 	.align	4

                    2165 	.align	4

                    2166 encodeAccessAttrConst::

00000b0c e92d4010   2167 	stmfd	[sp]!,{r4,lr}

                    2168 ;570: {


                    2169 

                    2170 ;571: 	uint8_t constTag = iedModel[constPos];


                    2171 

00000b10 e24dd004   2172 	sub	sp,sp,4

00000b14 e59fc030*  2173 	ldr	r12,.L2368

00000b18 e1a04002   2174 	mov	r4,r2

00000b1c e59c2000   2175 	ldr	r2,[r12]

00000b20 e7d2c004   2176 	ldrb	r12,[r2,r4]

                    2177 ;572: 	if ((constTag & BER_TAG_CLASS_MASK) != BER_CONTEXT_SPECIFIC)


                    2178 

00000b24 e20c20c0   2179 	and	r2,r12,192

00000b28 e3520080   2180 	cmp	r2,128

                    2181 ;573: 	{


                    2182 

                    2183 ;574: 		ERROR_REPORT("Invalid const tag %02X", constTag);


                    2184 ;575: 		return 0;



                                                                      Page 37
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g881.s
                    2185 

00000b2c 13a00000   2186 	movne	r0,0

00000b30 1a000066   2187 	bne	.L2836

00000b34 e59f2018*  2188 	ldr	r2,.L3077

                    2189 ;576: 	}


                    2190 ;577: 	constTag &= ~BER_TAG_CLASS_MASK;


                    2191 

00000b38 e20cc03f   2192 	and	r12,r12,63

                    2193 ;578: 


                    2194 ;579:     switch (constTag) {		


                    2195 

00000b3c e35c001c   2196 	cmp	r12,28

00000b40 8a00005e   2197 	bhi	.L2879

00000b44 ea000003   2198 	b	.L3078

                    2199 	.align	4

                    2200 .L833:

00000b48 00000000*  2201 	.data.w	.L828

                    2202 	.type	.L833,$object

                    2203 	.size	.L833,4

                    2204 

                    2205 .L2368:

00000b4c 00000000*  2206 	.data.w	iedModel

                    2207 	.type	.L2368,$object

                    2208 	.size	.L2368,4

                    2209 

                    2210 .L2662:

00000b50 00000000*  2211 	.data.w	.L2650

                    2212 	.type	.L2662,$object

                    2213 	.size	.L2662,4

                    2214 

                    2215 .L3077:

00000b54 00000000*  2216 	.data.w	encodeAccessAttrInt

                    2217 	.type	.L3077,$object

                    2218 	.size	.L3077,4

                    2219 

                    2220 .L3078:

                    2221 

00000b58 e08ff10c   2222 	add	pc,pc,r12 lsl 2

                    2223 .L3015:

                    2224 

00000b5c e1a00000   2225 	nop	

00000b60 ea00001b   2226 	b	.L2843

00000b64 ea000055   2227 	b	.L2879

00000b68 ea000054   2228 	b	.L2879

00000b6c ea00002a   2229 	b	.L2857

00000b70 ea00001a   2230 	b	.L2847

00000b74 ea000051   2231 	b	.L2879

00000b78 ea00001b   2232 	b	.L2849

00000b7c ea00001d   2233 	b	.L2851

00000b80 ea00004e   2234 	b	.L2879

00000b84 ea00001e   2235 	b	.L2853

00000b88 ea000020   2236 	b	.L2855

00000b8c ea00004b   2237 	b	.L2879

00000b90 ea000021   2238 	b	.L2857

00000b94 ea000028   2239 	b	.L2861

00000b98 ea000022   2240 	b	.L2859

00000b9c ea000047   2241 	b	.L2879

00000ba0 ea000046   2242 	b	.L2879

00000ba4 ea000029   2243 	b	.L2865

00000ba8 ea00002d   2244 	b	.L2867

00000bac ea000031   2245 	b	.L2869


                                                                      Page 38
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g881.s
00000bb0 ea000042   2246 	b	.L2879

00000bb4 ea000034   2247 	b	.L2873

00000bb8 ea000040   2248 	b	.L2879

00000bbc ea000037   2249 	b	.L2875

00000bc0 ea00003e   2250 	b	.L2879

00000bc4 ea00003d   2251 	b	.L2879

00000bc8 ea000034   2252 	b	.L2875

00000bcc ea00003b   2253 	b	.L2879

00000bd0 ea000037   2254 	b	.L2877

                    2255 .L2843:

                    2256 ;580:     case IEC61850_BOOLEAN:


                    2257 ;581:         return encodeAccessAttrBoolean(outBuf, bufPos, determineSize);


                    2258 

00000bd4 e1a02003   2259 	mov	r2,r3

00000bd8 ebffffa9*  2260 	bl	encodeAccessAttrBoolean

00000bdc ea00003b   2261 	b	.L2836

                    2262 .L2847:

                    2263 ;582: 	case IEC61850_INT32:


                    2264 ;583: 		return encodeAccessAttrInt(outBuf, bufPos, 32, determineSize);


                    2265 

                    2266 ;584: 	case IEC61850_INT64:


                    2267 ;585: 		return encodeAccessAttrInt(outBuf, bufPos, 64, determineSize);


                    2268 

00000be0 e3a02040   2269 	mov	r2,64

00000be4 ebffff78*  2270 	bl	encodeAccessAttrInt

00000be8 ea000038   2271 	b	.L2836

                    2272 .L2849:

                    2273 ;586: 	case IEC61850_INT8U:


                    2274 ;587: 		return encodeAccessAttrUInt(outBuf, bufPos, 8, determineSize);


                    2275 

00000bec e3a02008   2276 	mov	r2,8

00000bf0 ebffff83*  2277 	bl	encodeAccessAttrUInt

00000bf4 ea000035   2278 	b	.L2836

                    2279 .L2851:

                    2280 ;588: 	case IEC61850_INT16U:


                    2281 ;589: 		return encodeAccessAttrUInt(outBuf, bufPos, 16, determineSize);


                    2282 

00000bf8 e3a02010   2283 	mov	r2,16

00000bfc ebffff80*  2284 	bl	encodeAccessAttrUInt

00000c00 ea000032   2285 	b	.L2836

                    2286 .L2853:

                    2287 ;590: 	case IEC61850_INT32U:


                    2288 ;591: 		return encodeAccessAttrUInt(outBuf, bufPos, 32, determineSize);


                    2289 

00000c04 e3a02020   2290 	mov	r2,32

00000c08 ebffff7d*  2291 	bl	encodeAccessAttrUInt

00000c0c ea00002f   2292 	b	.L2836

                    2293 .L2855:

                    2294 ;592: 	case IEC61850_FLOAT32:


                    2295 ;593: 		return encodeAccessAttrFloat(outBuf, bufPos, determineSize);


                    2296 

00000c10 e1a02003   2297 	mov	r2,r3

00000c14 ebffff47*  2298 	bl	encodeAccessAttrFloat

00000c18 ea00002c   2299 	b	.L2836

                    2300 .L2857:

                    2301 ;594: 	case IEC61850_ENUMERATED:


                    2302 ;595: 		return encodeAccessAttrInt(outBuf, bufPos, 32, determineSize);


                    2303 

00000c1c e3a02020   2304 	mov	r2,32

00000c20 ebffff69*  2305 	bl	encodeAccessAttrInt

00000c24 ea000029   2306 	b	.L2836


                                                                      Page 39
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g881.s
                    2307 .L2859:

                    2308 ;596: 	case IEC61850_OCTET_STRING_6:


                    2309 ;597: 		return encodeAccessAttrString(outBuf, bufPos,


                    2310 

00000c28 e58d3000   2311 	str	r3,[sp]

00000c2c e3a03006   2312 	mov	r3,6

00000c30 e3a02089   2313 	mov	r2,137

00000c34 ebffff55*  2314 	bl	encodeAccessAttrString

00000c38 ea000024   2315 	b	.L2836

                    2316 .L2861:

                    2317 ;598: 			IEC61850_BER_OCTET_STRING, 6, determineSize);


                    2318 ;599: 	case IEC61850_OCTET_STRING_64:


                    2319 ;600: 		return encodeAccessAttrString(outBuf, bufPos,


                    2320 

00000c3c e58d3000   2321 	str	r3,[sp]

00000c40 e3a03040   2322 	mov	r3,64

00000c44 e3a02089   2323 	mov	r2,137

00000c48 ebffff50*  2324 	bl	encodeAccessAttrString

00000c4c ea00001f   2325 	b	.L2836

                    2326 .L2865:

                    2327 ;601: 			IEC61850_BER_OCTET_STRING, 64, determineSize);


                    2328 ;602:     case IEC61850_VISIBLE_STRING_32:


                    2329 ;603:         return encodeAccessAttrString(outBuf, bufPos,


                    2330 

                    2331 ;604:             IEC61850_BER_VISIBLE_STRING, 255, determineSize);


                    2332 ;605:     case IEC61850_VISIBLE_STRING_64:


                    2333 ;606:         return encodeAccessAttrString(outBuf, bufPos,


                    2334 

00000c50 e58d3000   2335 	str	r3,[sp]

00000c54 e3a03040   2336 	mov	r3,64

00000c58 e3a0208a   2337 	mov	r2,138

00000c5c ebffff4b*  2338 	bl	encodeAccessAttrString

00000c60 ea00001a   2339 	b	.L2836

                    2340 .L2867:

                    2341 ;607:             IEC61850_BER_VISIBLE_STRING, 64, determineSize);


                    2342 ;608:     case IEC61850_VISIBLE_STRING_65:


                    2343 ;609:         return encodeAccessAttrString(outBuf, bufPos,


                    2344 

00000c64 e58d3000   2345 	str	r3,[sp]

00000c68 e3a03041   2346 	mov	r3,65

00000c6c e3a0208a   2347 	mov	r2,138

00000c70 ebffff46*  2348 	bl	encodeAccessAttrString

00000c74 ea000015   2349 	b	.L2836

                    2350 .L2869:

                    2351 ;610:             IEC61850_BER_VISIBLE_STRING, 65, determineSize);


                    2352 ;611:     case IEC61850_VISIBLE_STRING_129:


                    2353 ;612:         return encodeAccessAttrString(outBuf, bufPos,


                    2354 

00000c78 e58d3000   2355 	str	r3,[sp]

00000c7c e3a03081   2356 	mov	r3,129

00000c80 e3a0208a   2357 	mov	r2,138

00000c84 ebffff41*  2358 	bl	encodeAccessAttrString

00000c88 ea000010   2359 	b	.L2836

                    2360 .L2873:

                    2361 ;613:             IEC61850_BER_VISIBLE_STRING, 129, determineSize);


                    2362 ;614:     case IEC61850_VISIBLE_STRING_255:


                    2363 ;615: 		return encodeAccessAttrString(outBuf, bufPos, 


                    2364 

                    2365 ;616: 			IEC61850_BER_VISIBLE_STRING, 255, determineSize);


                    2366 ;617: 	case IEC61850_UNICODE_STRING_255:


                    2367 ;618: 		return encodeAccessAttrString(outBuf, bufPos,



                                                                      Page 40
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g881.s
                    2368 

00000c8c e58d3000   2369 	str	r3,[sp]

00000c90 e3a030ff   2370 	mov	r3,255

00000c94 e3a02090   2371 	mov	r2,144

00000c98 ebffff3c*  2372 	bl	encodeAccessAttrString

00000c9c ea00000b   2373 	b	.L2836

                    2374 .L2875:

                    2375 ;619: 			IEC61850_BER_MMS_STRING, 255, determineSize);


                    2376 ;620:     case IEC61850_GENERIC_BITSTRING:


                    2377 ;621: 	case IEC61850_QUALITY:


                    2378 ;622: 		return encodeAccessAttrBitStringConst(constPos, outBuf, bufPos, determineSize);    


                    2379 

00000ca0 e1a02001   2380 	mov	r2,r1

00000ca4 e1a01000   2381 	mov	r1,r0

00000ca8 e1a00004   2382 	mov	r0,r4

00000cac ebffff05*  2383 	bl	encodeAccessAttrBitStringConst

00000cb0 ea000006   2384 	b	.L2836

                    2385 .L2877:

                    2386 ;623:     case IEC61850_ENTRY_TIME:


                    2387 ;624:         return encodeAccessAttrEntryTime(outBuf, bufPos, determineSize);


                    2388 

00000cb4 e1a02003   2389 	mov	r2,r3

00000cb8 ebffff8b*  2390 	bl	encodeAccessAttrEntryTime

00000cbc ea000003   2391 	b	.L2836

                    2392 .L2879:

                    2393 ;625:     default:


                    2394 ;626: 		ERROR_REPORT("Invalid const tag %02X", constTag);


                    2395 ;627: 		return encodeAccessAttrString(outBuf, bufPos,


                    2396 

00000cc0 e58d3000   2397 	str	r3,[sp]

00000cc4 e3a030ff   2398 	mov	r3,255

00000cc8 e3a0208a   2399 	mov	r2,138

00000ccc ebffff2f*  2400 	bl	encodeAccessAttrString

                    2401 .L2836:

00000cd0 e28dd004   2402 	add	sp,sp,4

00000cd4 e8bd8010   2403 	ldmfd	[sp]!,{r4,pc}

                    2404 	.endf	encodeAccessAttrConst

                    2405 	.align	4

                    2406 ;constTag	r12	local

                    2407 

                    2408 ;outBuf	r0	param

                    2409 ;bufPos	r1	param

                    2410 ;constPos	r4	param

                    2411 ;determineSize	r3	param

                    2412 

                    2413 	.section ".bss","awb"

                    2414 .L3014:

                    2415 	.data

                    2416 	.ghsnote jtable,5,.L3015,.L3015,.L3015,30

                    2417 	.text

                    2418 

                    2419 ;628: 			IEC61850_BER_VISIBLE_STRING, 255, determineSize);        


                    2420 ;629:     }


                    2421 ;630: }


                    2422 	.align	4

                    2423 ;iedModel	iedModel	import

                    2424 

                    2425 	.data

                    2426 	.ghsnote version,6

                    2427 	.ghsnote tools,3

                    2428 	.ghsnote options,0


                                                                      Page 41
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g881.s
                    2429 	.text

                    2430 	.align	4

                    2431 	.section ".rodata","a"

0000000a 0000      2432 	.align	4

                    2433 	.text

