                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_7h41.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=iedObjects.c -o iedTree\gh_7h41.o -list=iedTree/iedObjects.lst C:\Users\<USER>\AppData\Local\Temp\gh_7h41.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_7h41.s
Source File: iedObjects.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      10 ;		iedTree/iedObjects.c -o iedTree/iedObjects.o

                      11 ;Source File:   iedTree/iedObjects.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Oct 07 09:39:19 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "iedObjects.h"


                      21 ;2: 


                      22 ;3: #include "iedFinalDA.h"


                      23 ;4: #include "iedControlModel.h"


                      24 ;5: 


                      25 ;6: #include "../iedmodel.h"


                      26 ;7: #include "../BaseAsnTypes.h"


                      27 ;8: #include "../AsnEncoding.h"


                      28 ;9: #include "IEDCompile/InnerAttributeTypes.h"


                      29 ;10: #include "IEDCompile/AccessInfo.h"


                      30 ;11: #include "../mms_gocb.h"


                      31 ;12: #include "../mms_rcb.h"


                      32 ;13: #include "../pwin_access.h"


                      33 ;14: #include "../control.h"


                      34 ;15: #include "../timers.h"


                      35 ;16: #include <Clib.h>


                      36 ;17: #include <string.h>


                      37 ;18: 


                      38 ;19: 


                      39 ;20: typedef struct {


                      40 ;21:     IEDEntity dataSection;


                      41 ;22:     IEDEntity dataSetSection;


                      42 ;23: } IEDLD;


                      43 ;24: 


                      44 ;25: bool IEDLD_init(IEDEntity entity)


                      45 	.text

                      46 	.align	4

                      47 IEDLD_init::

00000000 e92d4070     48 	stmfd	[sp]!,{r4-r6,lr}

00000004 e1a06000     49 	mov	r6,r0

                      50 ;26: {



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_7h41.s
                      51 

                      52 ;27:     IEDLD* extInfo = IEDEntity_alloc(sizeof(IEDLD));


                      53 

00000008 e3a00008     54 	mov	r0,8

0000000c eb000000*    55 	bl	IEDEntity_alloc

00000010 e1b05000     56 	movs	r5,r0

                      57 ;28:     if(extInfo == NULL)


                      58 

                      59 ;29:     {


                      60 

                      61 ;30:         return false;


                      62 

00000014 020500ff     63 	andeq	r0,r5,255

00000018 0a00000b     64 	beq	.L21

                      65 ;31:     }


                      66 ;32:     entity->extInfo = extInfo;


                      67 

0000001c e5865058     68 	str	r5,[r6,88]

                      69 ;33:     entity->type = IED_ENTITY_LD;


                      70 

00000020 e3a04001     71 	mov	r4,1

00000024 e5864050     72 	str	r4,[r6,80]

                      73 ;34:     extInfo->dataSection = IEDEntity_getChildByTag(entity, IED_VMD_DATA_SECTION);


                      74 

00000028 e1a00006     75 	mov	r0,r6

0000002c e3a010ec     76 	mov	r1,236

00000030 eb000000*    77 	bl	IEDEntity_getChildByTag

00000034 e5850000     78 	str	r0,[r5]

                      79 ;35:     extInfo->dataSetSection = IEDEntity_getChildByTag(entity, IED_VMD_DATA_SET_SECTION);


                      80 

00000038 e1a00006     81 	mov	r0,r6

0000003c e3a010ee     82 	mov	r1,238

00000040 eb000000*    83 	bl	IEDEntity_getChildByTag

00000044 e5850004     84 	str	r0,[r5,4]

                      85 ;36:     return true;


                      86 

00000048 e1a00004     87 	mov	r0,r4

                      88 .L21:

0000004c e8bd8070     89 	ldmfd	[sp]!,{r4-r6,pc}

                      90 	.endf	IEDLD_init

                      91 	.align	4

                      92 ;extInfo	r5	local

                      93 

                      94 ;entity	r6	param

                      95 

                      96 	.section ".bss","awb"

                      97 .L69:

                      98 	.data

                      99 	.text

                     100 

                     101 ;37: }


                     102 

                     103 ;38: 


                     104 ;39: IEDEntity IEDLD_getDataSection(IEDEntity ld)


                     105 	.align	4

                     106 	.align	4

                     107 IEDLD_getDataSection::

                     108 ;40: {


                     109 

                     110 ;41:     IEDLD* extInfo = ld->extInfo;


                     111 


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_7h41.s
00000050 e5900058    112 	ldr	r0,[r0,88]

                     113 ;42:     if(extInfo == NULL)


                     114 

00000054 e3500000    115 	cmp	r0,0

                     116 ;43:     {


                     117 

                     118 ;44:         return NULL;


                     119 

                     120 ;45:     }


                     121 ;46:     return extInfo->dataSection;


                     122 

00000058 15900000    123 	ldrne	r0,[r0]

0000005c e12fff1e*   124 	ret	

                     125 	.endf	IEDLD_getDataSection

                     126 	.align	4

                     127 ;extInfo	r0	local

                     128 

                     129 ;ld	r0	param

                     130 

                     131 	.section ".bss","awb"

                     132 .L133:

                     133 	.data

                     134 	.text

                     135 

                     136 ;47: }


                     137 

                     138 ;48: 


                     139 ;49: IEDEntity IEDLD_getDataSetSection(IEDEntity ld)


                     140 	.align	4

                     141 	.align	4

                     142 IEDLD_getDataSetSection::

                     143 ;50: {


                     144 

                     145 ;51:     IEDLD* extInfo = ld->extInfo;


                     146 

00000060 e5900058    147 	ldr	r0,[r0,88]

                     148 ;52:     if(extInfo == NULL)


                     149 

00000064 e3500000    150 	cmp	r0,0

                     151 ;53:     {


                     152 

                     153 ;54:         return NULL;


                     154 

                     155 ;55:     }


                     156 ;56:     return extInfo->dataSetSection;


                     157 

00000068 15900004    158 	ldrne	r0,[r0,4]

0000006c e12fff1e*   159 	ret	

                     160 	.endf	IEDLD_getDataSetSection

                     161 	.align	4

                     162 ;extInfo	r0	local

                     163 

                     164 ;ld	r0	param

                     165 

                     166 	.section ".bss","awb"

                     167 .L197:

                     168 	.data

                     169 	.text

                     170 

                     171 ;57: }


                     172 


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_7h41.s
                     173 ;58: 


                     174 ;59: bool IEDComplexObj_init(IEDEntity entity)


                     175 	.align	4

                     176 	.align	4

                     177 IEDComplexObj_init::

                     178 ;60: {


                     179 

                     180 ;61: 	switch(entity->tag)


                     181 

00000070 e5d01020    182 	ldrb	r1,[r0,32]

00000074 e25110e0    183 	subs	r1,r1,224

                     184 ;68: 		break;


                     185 ;69: 	case IED_FC:


                     186 ;70: 		entity->type = IED_ENTITY_FC;


                     187 

00000078 03a01004    188 	moveq	r1,4

                     189 ;75: 	}


                     190 ;76: 	return true;


                     191 

0000007c 05801050    192 	streq	r1,[r0,80]

00000080 03a00001    193 	moveq	r0,1

00000084 0a000009    194 	beq	.L210

00000088 e2511004    195 	subs	r1,r1,4

                     196 ;65: 		break;


                     197 ;66: 	case IED_LN:


                     198 ;67: 		entity->type = IED_ENTITY_LN;		


                     199 

0000008c 03a01003    200 	moveq	r1,3

                     201 ;75: 	}


                     202 ;76: 	return true;


                     203 

00000090 05801050    204 	streq	r1,[r0,80]

00000094 03a00001    205 	moveq	r0,1

00000098 0a000004    206 	beq	.L210

0000009c e3510008    207 	cmp	r1,8

                     208 ;71: 		break;


                     209 ;72: 	default:


                     210 ;73: 		ERROR_REPORT("Invalid object tag");


                     211 ;74: 		return false;


                     212 

000000a0 13a00000    213 	movne	r0,0

                     214 ;62: 	{


                     215 ;63: 	case IED_VMD_DATA_SECTION:


                     216 ;64: 		entity->type = IED_ENTITY_DATA_SECTION;


                     217 

000000a4 03a01002    218 	moveq	r1,2

                     219 ;75: 	}


                     220 ;76: 	return true;


                     221 

000000a8 05801050    222 	streq	r1,[r0,80]

000000ac 03a00001    223 	moveq	r0,1

                     224 .L210:

000000b0 e12fff1e*   225 	ret	

                     226 	.endf	IEDComplexObj_init

                     227 	.align	4

                     228 

                     229 ;entity	r0	param

                     230 

                     231 	.section ".bss","awb"

                     232 .L266:

                     233 	.data


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_7h41.s
                     234 	.text

                     235 

                     236 ;77: }


                     237 

                     238 ;78: 


                     239 ;79: bool IEDDO_init(IEDEntity entity)


                     240 	.align	4

                     241 	.align	4

                     242 IEDDO_init::

000000b4 e92d4010    243 	stmfd	[sp]!,{r4,lr}

                     244 ;80: {


                     245 

                     246 ;81: 	IEDEntity timeStampDA;


                     247 ;82:     IEDEntity oper;


                     248 ;83:     entity->type = IED_ENTITY_DO;


                     249 

000000b8 e3a01005    250 	mov	r1,5

000000bc e5801050    251 	str	r1,[r0,80]

                     252 ;84: 


                     253 ;85: 	// Если у этого DO есть t, то для него и всех его


                     254 ;86: 	// детей устанавлмваем указатель на объект timeStamp


                     255 ;87: 	timeStampDA = IEDDO_getTimeStampDA(entity);


                     256 

000000c0 e1a04000    257 	mov	r4,r0

000000c4 eb00000f*   258 	bl	IEDDO_getTimeStampDA

                     259 ;88: 	if (timeStampDA != NULL)


                     260 

000000c8 e3500000    261 	cmp	r0,0

                     262 ;89: 	{


                     263 

                     264 ;90: 		TimeStamp* pTimeStamp = timeStampDA->extInfo;


                     265 

000000cc 15901058    266 	ldrne	r1,[r0,88]

                     267 ;91: 		IEDEntity_attachTimeStamp(entity, pTimeStamp);


                     268 

000000d0 11a00004    269 	movne	r0,r4

000000d4 1b000000*   270 	blne	IEDEntity_attachTimeStamp

                     271 ;92: 	}


                     272 ;93: 


                     273 ;94:     oper = IEDEntity_getChildByCStrName(entity, "Oper");


                     274 

000000d8 e28f1000*   275 	adr	r1,.L383

000000dc e1a00004    276 	mov	r0,r4

000000e0 eb000000*   277 	bl	IEDEntity_getChildByCStrName

                     278 ;95: 	if(oper != NULL && IEDControlDA_isControlDA(oper))


                     279 

000000e4 e3500000    280 	cmp	r0,0

000000e8 0a000004    281 	beq	.L287

000000ec eb000000*   282 	bl	IEDControlDA_isControlDA

000000f0 e3500000    283 	cmp	r0,0

                     284 ;96:     {


                     285 

                     286 ;97:         return IEDControlDO_init(entity);


                     287 

000000f4 11a00004    288 	movne	r0,r4

000000f8 18bd4010    289 	ldmnefd	[sp]!,{r4,lr}

000000fc 1a000000*   290 	bne	IEDControlDO_init

                     291 .L287:

                     292 ;98:     }


                     293 ;99:     return true;


                     294 


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_7h41.s
00000100 e3a00001    295 	mov	r0,1

00000104 e8bd8010    296 	ldmfd	[sp]!,{r4,pc}

                     297 	.endf	IEDDO_init

                     298 	.align	4

                     299 ;timeStampDA	r0	local

                     300 ;oper	r1	local

                     301 ;.L361	.L364	static

                     302 

                     303 ;entity	r4	param

                     304 

                     305 	.section ".bss","awb"

                     306 .L360:

                     307 	.data

                     308 	.text

                     309 

                     310 ;100: }


                     311 

                     312 ;101: 


                     313 ;102: IEDEntity IEDDO_getTimeStampDA(IEDEntity entity)


                     314 	.align	4

                     315 	.align	4

                     316 IEDDO_getTimeStampDA::

00000108 e92d4010    317 	stmfd	[sp]!,{r4,lr}

                     318 ;103: {


                     319 

                     320 ;104: 	IEDEntity child = entity->firstChild;


                     321 

0000010c e5904004    322 	ldr	r4,[r0,4]

                     323 ;105: 	while(child != NULL)


                     324 

00000110 e3540000    325 	cmp	r4,0

00000114 0a000009    326 	beq	.L387

                     327 .L388:

                     328 ;106: 	{


                     329 

                     330 ;107: 		if(child->type == IED_ENTITY_DA_TIMESTAMP


                     331 

00000118 e5940050    332 	ldr	r0,[r4,80]

0000011c e3500009    333 	cmp	r0,9

00000120 1a000003    334 	bne	.L389

00000124 e28f1000*   335 	adr	r1,.L487

00000128 e2840048    336 	add	r0,r4,72

0000012c eb000000*   337 	bl	StringView_cmpCStr

00000130 e3500000    338 	cmp	r0,0

                     339 .L389:

                     340 ;108: 		   //Timestamp бывает не только t, а нас интересует только t


                     341 ;109: 		   && StringView_cmpCStr(&child->name, "t") == 0)


                     342 ;110: 		{


                     343 

                     344 ;111: 			return child;


                     345 

                     346 ;112: 		}


                     347 ;113: 		child = child->next;


                     348 

00000134 1594400c    349 	ldrne	r4,[r4,12]

00000138 13540000    350 	cmpne	r4,0

0000013c 1afffff5    351 	bne	.L388

                     352 .L387:

                     353 ;114: 	}


                     354 ;115: 	return NULL;


                     355 


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_7h41.s
00000140 e1a00004    356 	mov	r0,r4

00000144 e8bd8010    357 	ldmfd	[sp]!,{r4,pc}

                     358 	.endf	IEDDO_getTimeStampDA

                     359 	.align	4

                     360 ;child	r4	local

                     361 ;.L470	.L473	static

                     362 

                     363 ;entity	r0	param

                     364 

                     365 	.section ".bss","awb"

                     366 .L469:

                     367 	.data

                     368 	.text

                     369 

                     370 ;116: }


                     371 

                     372 ;117: 


                     373 ;118: bool IEDDA_init(IEDEntity entity)


                     374 	.align	4

                     375 	.align	4

                     376 IEDDA_init::

00000148 e92d4010    377 	stmfd	[sp]!,{r4,lr}

                     378 ;119: {


                     379 

                     380 ;120:     uint8_t tag;


                     381 ;121:     BufferView ber = entity->ber;


                     382 

0000014c e24dd010    383 	sub	sp,sp,16

00000150 e28d3004    384 	add	r3,sp,4

00000154 e1a04000    385 	mov	r4,r0

00000158 e2840014    386 	add	r0,r4,20

0000015c e8900007    387 	ldmfd	[r0],{r0-r2}

00000160 e8830007    388 	stmea	[r3],{r0-r2}

                     389 ;122:     entity->type = IED_ENTITY_DA;


                     390 

00000164 e3a00006    391 	mov	r0,6

00000168 e5840050    392 	str	r0,[r4,80]

                     393 ;123: 


                     394 ;124:     //Проверяем наличие информации о сервисе Control


                     395 ;125:     if(entity->name.p == NULL)


                     396 

0000016c e594004c    397 	ldr	r0,[r4,76]

00000170 e3500000    398 	cmp	r0,0

00000174 0a000016    399 	beq	.L504

                     400 ;126:     {


                     401 

                     402 ;127:         //Если нет имени, то и дополнительной информации нет


                     403 ;128:         return true;


                     404 

                     405 ;129:     }


                     406 ;130: 


                     407 ;131:     //Пропустить тэг и длину


                     408 ;132:     BufferView_decodeTL(&ber, NULL, NULL, NULL);


                     409 

00000178 e1a00003    410 	mov	r0,r3

0000017c e3a03000    411 	mov	r3,0

00000180 e1a02003    412 	mov	r2,r3

00000184 e1a01003    413 	mov	r1,r3

00000188 eb000000*   414 	bl	BufferView_decodeTL

                     415 ;133: 


                     416 ;134:     if(BufferView_endOfBuf(&ber))



                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_7h41.s
                     417 

0000018c e59d100c    418 	ldr	r1,[sp,12]

00000190 e59d0008    419 	ldr	r0,[sp,8]

00000194 e1500001    420 	cmp	r0,r1

00000198 0a00000d    421 	beq	.L504

                     422 ;135:     {


                     423 

                     424 ;136:         return true;


                     425 

0000019c e28d0004    426 	add	r0,sp,4

000001a0 e3a02000    427 	mov	r2,0

000001a4 e3a0101a    428 	mov	r1,26

000001a8 eb000000*   429 	bl	BufferView_skipObject

                     430 ;137:     }


                     431 ;138: 


                     432 ;139:     //Пропустить имя


                     433 ;140:     if(!BufferView_skipObject(&ber, ASN_VISIBLE_STRING, false))


                     434 

000001ac e3500000    435 	cmp	r0,0

                     436 ;141:     {


                     437 

                     438 ;142:         ERROR_REPORT("Name is not found");


                     439 ;143:         return false;


                     440 

000001b0 0a00000c    441 	beq	.L488

                     442 ;144:     }


                     443 ;145: 


                     444 ;146:     if(!BufferView_peekTag(&ber, &tag))


                     445 

000001b4 e28d1003    446 	add	r1,sp,3

000001b8 e28d0004    447 	add	r0,sp,4

000001bc eb000000*   448 	bl	BufferView_peekTag

000001c0 e3500000    449 	cmp	r0,0

000001c4 0a000002    450 	beq	.L504

                     451 ;147:     {


                     452 

                     453 ;148:         return true;


                     454 

                     455 ;149:     }


                     456 ;150: 


                     457 ;151:     if(tag != IED_CONTROL_INFO)


                     458 

000001c8 e5dd0003    459 	ldrb	r0,[sp,3]

000001cc e35000f1    460 	cmp	r0,241

000001d0 0a000001    461 	beq	.L503

                     462 .L504:

                     463 ;152:     {


                     464 

                     465 ;153:         return true;


                     466 

000001d4 e3a00001    467 	mov	r0,1

000001d8 ea000002    468 	b	.L488

                     469 .L503:

                     470 ;154:     }


                     471 ;155: 


                     472 ;156:     return IEDControlDA_init(entity, &ber);


                     473 

000001dc e28d1004    474 	add	r1,sp,4

000001e0 e1a00004    475 	mov	r0,r4

000001e4 eb000000*   476 	bl	IEDControlDA_init

                     477 .L488:


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_7h41.s
000001e8 e28dd010    478 	add	sp,sp,16

000001ec e8bd8010    479 	ldmfd	[sp]!,{r4,pc}

                     480 	.endf	IEDDA_init

                     481 	.align	4

                     482 ;tag	[sp,3]	local

                     483 ;ber	[sp,4]	local

                     484 

                     485 ;entity	r4	param

                     486 

                     487 	.section ".bss","awb"

                     488 .L618:

                     489 	.data

                     490 	.text

                     491 

                     492 ;157: }


                     493 

                     494 ;158: 


                     495 ;159: bool IEDConstDA_calcReadLen(IEDEntity entity, size_t *pLen)


                     496 	.align	4

                     497 	.align	4

                     498 IEDConstDA_calcReadLen::

000001f0 e92d4010    499 	stmfd	[sp]!,{r4,lr}

000001f4 e1a04001    500 	mov	r4,r1

                     501 ;160: {


                     502 

                     503 ;161:     int dataLen;


                     504 ;162:     int constPos;


                     505 ;163: 


                     506 ;164:     if(entity->type != IED_ENTITY_DA_CONST)


                     507 

000001f8 e5901050    508 	ldr	r1,[r0,80]

000001fc e3510007    509 	cmp	r1,7

00000200 1a000008    510 	bne	.L653

                     511 ;165:     {


                     512 

                     513 ;166:         ERROR_REPORT("Invalid terminal item DA");


                     514 ;167:         return false;


                     515 

                     516 ;168:     }


                     517 ;169: 


                     518 ;170:     //Получаем позицию в модели для вызова старой функции


                     519 ;171:     constPos = (int)entity->extInfo;


                     520 

00000204 e3a03001    521 	mov	r3,1

00000208 e5902058    522 	ldr	r2,[r0,88]

                     523 ;172: 


                     524 ;173:     dataLen = encodeReadConst(0, 0, constPos, true);


                     525 

0000020c e3a01000    526 	mov	r1,0

00000210 e1a00001    527 	mov	r0,r1

00000214 eb000000*   528 	bl	encodeReadConst

                     529 ;174:     if(dataLen <=0)


                     530 

00000218 e3500000    531 	cmp	r0,0

                     532 ;178:     }


                     533 ;179: 	*pLen = dataLen;


                     534 

0000021c c5840000    535 	strgt	r0,[r4]

                     536 ;180: 


                     537 ;181:     return true;


                     538 


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_7h41.s
00000220 c3a00001    539 	movgt	r0,1

00000224 ca000000    540 	bgt	.L647

                     541 .L653:

                     542 ;175:     {


                     543 

                     544 ;176:         ERROR_REPORT("Invalid read length");


                     545 ;177:         return false;


                     546 

00000228 e3a00000    547 	mov	r0,0

                     548 .L647:

0000022c e8bd8010    549 	ldmfd	[sp]!,{r4,pc}

                     550 	.endf	IEDConstDA_calcReadLen

                     551 	.align	4

                     552 ;dataLen	r0	local

                     553 

                     554 ;entity	r0	param

                     555 ;pLen	r4	param

                     556 

                     557 	.section ".bss","awb"

                     558 .L700:

                     559 	.data

                     560 	.text

                     561 

                     562 ;182: }


                     563 

                     564 ;183: 


                     565 ;184: bool IEDConstDA_encodeRead(IEDEntity entity, BufferView *outBufView)


                     566 	.align	4

                     567 	.align	4

                     568 IEDConstDA_encodeRead::

00000230 e92d4010    569 	stmfd	[sp]!,{r4,lr}

00000234 e1a04001    570 	mov	r4,r1

                     571 ;185: {


                     572 

                     573 ;186:     int dataLen;


                     574 ;187:     int constPos;


                     575 ;188:     uint8_t *writeBuf;


                     576 ;189: 


                     577 ;190:     if(entity->type != IED_ENTITY_DA_CONST)


                     578 

00000238 e5901050    579 	ldr	r1,[r0,80]

0000023c e3510007    580 	cmp	r1,7

00000240 1a00000c    581 	bne	.L726

                     582 ;191:     {


                     583 

                     584 ;192:         ERROR_REPORT("Invalid terminal item DA");


                     585 ;193:         return false;


                     586 

                     587 ;194:     }


                     588 ;195: 


                     589 ;196:     //Получаем позицию в модели для вызова старой функции


                     590 ;197:     constPos = (int)entity->extInfo;


                     591 

00000244 e5902058    592 	ldr	r2,[r0,88]

                     593 ;198: 


                     594 ;199:     writeBuf = outBufView->p + outBufView->pos;


                     595 

00000248 e894000a    596 	ldmfd	[r4],{r1,r3}

0000024c e0830001    597 	add	r0,r3,r1

                     598 ;200: 


                     599 ;201:     dataLen = encodeReadConst(writeBuf, 0, constPos, false);



                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_7h41.s
                     600 

00000250 e3a03000    601 	mov	r3,0

00000254 e1a01003    602 	mov	r1,r3

00000258 eb000000*   603 	bl	encodeReadConst

0000025c e2501000    604 	subs	r1,r0,0

                     605 ;202:     if(dataLen <=0)


                     606 

00000260 da000004    607 	ble	.L726

                     608 ;203:     {


                     609 

                     610 ;204:         ERROR_REPORT("Invalid read length");


                     611 ;205:         return false;


                     612 

                     613 ;206:     }


                     614 ;207: 


                     615 ;208:     if(!BufferView_advance(outBufView, dataLen))


                     616 

00000264 e1a00004    617 	mov	r0,r4

00000268 eb000000*   618 	bl	BufferView_advance

0000026c e3500000    619 	cmp	r0,0

                     620 ;212:     }


                     621 ;213:     return true;


                     622 

00000270 13a00001    623 	movne	r0,1

00000274 1a000000    624 	bne	.L717

                     625 .L726:

                     626 ;209:     {


                     627 

                     628 ;210:         ERROR_REPORT("Buffer overflow");


                     629 ;211:         return false;


                     630 

00000278 e3a00000    631 	mov	r0,0

                     632 .L717:

0000027c e8bd8010    633 	ldmfd	[sp]!,{r4,pc}

                     634 	.endf	IEDConstDA_encodeRead

                     635 	.align	4

                     636 ;dataLen	r1	local

                     637 ;writeBuf	r0	local

                     638 

                     639 ;entity	r0	param

                     640 ;outBufView	r4	param

                     641 

                     642 	.section ".bss","awb"

                     643 .L790:

                     644 	.data

                     645 	.text

                     646 

                     647 ;214: }


                     648 

                     649 ;215: 


                     650 ;216: 


                     651 ;217: 


                     652 ;218: MmsDataAccessError IEDComplexObj_write(IEDEntity entity,


                     653 	.align	4

                     654 	.align	4

                     655 IEDComplexObj_write::

00000280 e92d44f0    656 	stmfd	[sp]!,{r4-r7,r10,lr}

                     657 ;219:                                            IsoConnection* isoConn, BufferView* value)


                     658 ;220: {


                     659 

                     660 ;221:     uint8_t tag;



                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_7h41.s
                     661 ;222:     size_t len;


                     662 ;223:     IEDEntity nextChild;


                     663 ;224:     BufferView controlValue;


                     664 ;225: 


                     665 ;226:     if(entity->type != IED_ENTITY_DA  && entity->type != IED_ENTITY_DO)


                     666 

00000284 e1a07001    667 	mov	r7,r1

00000288 e24dd014    668 	sub	sp,sp,20

0000028c e1a06000    669 	mov	r6,r0

00000290 e5960050    670 	ldr	r0,[r6,80]

00000294 e1a05002    671 	mov	r5,r2

00000298 e3500006    672 	cmp	r0,6

0000029c 13500005    673 	cmpne	r0,5

                     674 ;227:     {


                     675 

                     676 ;228:         ERROR_REPORT("Invalid object to write");


                     677 ;229:         return DATA_ACCESS_ERROR_TYPE_INCONSISTENT;


                     678 

000002a0 13a00007    679 	movne	r0,7

000002a4 1a00002f    680 	bne	.L810

                     681 ;230:     }


                     682 ;231: 


                     683 ;232:     if(!BufferView_decodeTL(value, &tag, &len, NULL))


                     684 

000002a8 e28d2004    685 	add	r2,sp,4

000002ac e28d1003    686 	add	r1,sp,3

000002b0 e1a00005    687 	mov	r0,r5

000002b4 e3a03000    688 	mov	r3,0

000002b8 eb000000*   689 	bl	BufferView_decodeTL

000002bc e3500000    690 	cmp	r0,0

000002c0 0a000002    691 	beq	.L820

                     692 ;233:     {


                     693 

                     694 ;234:         ERROR_REPORT("Invalid write value");


                     695 ;235:         return DATA_ACCESS_ERROR_OBJECT_VALUE_INVALID;


                     696 

                     697 ;236:     }


                     698 ;237:     if(tag != 0xA2)


                     699 

000002c4 e5dd0003    700 	ldrb	r0,[sp,3]

000002c8 e35000a2    701 	cmp	r0,162

000002cc 0a000001    702 	beq	.L819

                     703 .L820:

                     704 ;238:     {


                     705 

                     706 ;239:         ERROR_REPORT("Structure tag expected");


                     707 ;240:         return DATA_ACCESS_ERROR_OBJECT_VALUE_INVALID;


                     708 

000002d0 e3a0000b    709 	mov	r0,11

000002d4 ea000023    710 	b	.L810

                     711 .L819:

                     712 ;241:     }


                     713 ;242: 


                     714 ;243: 	if(IEDControlDA_isControlDA(entity))


                     715 

000002d8 e1a00006    716 	mov	r0,r6

000002dc eb000000*   717 	bl	IEDControlDA_isControlDA

000002e0 e3500000    718 	cmp	r0,0

000002e4 0a00000a    719 	beq	.L822

                     720 ;244:     {


                     721 


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_7h41.s
                     722 ;245:         if(IEDControlDA_isReady(entity))


                     723 

000002e8 e1a00006    724 	mov	r0,r6

000002ec eb000000*   725 	bl	IEDControlDA_isReady

000002f0 e3500000    726 	cmp	r0,0

                     727 ;246:         {


                     728 

                     729 ;247:             return DATA_ACCESS_ERROR_TEMPORARILY_UNAVAILABLE;


                     730 

000002f4 13a00002    731 	movne	r0,2

000002f8 1a00001a    732 	bne	.L810

                     733 ;248:         }


                     734 ;249:         //Сохраняем значение для control объектов


                     735 ;250:         BufferView_init(&controlValue, value->p + value->pos, len, 0);


                     736 

000002fc e59d2004    737 	ldr	r2,[sp,4]

00000300 e895000a    738 	ldmfd	[r5],{r1,r3}

00000304 e28d0008    739 	add	r0,sp,8

00000308 e0831001    740 	add	r1,r3,r1

0000030c e3a03000    741 	mov	r3,0

00000310 eb000000*   742 	bl	BufferView_init

                     743 .L822:

                     744 ;251:     }


                     745 ;252: 


                     746 ;253: 


                     747 ;254:     nextChild = entity->firstChild;


                     748 

00000314 e5964004    749 	ldr	r4,[r6,4]

                     750 ;255:     while(nextChild != NULL)


                     751 

00000318 e3e0a000    752 	mvn	r10,0

0000031c e3540000    753 	cmp	r4,0

00000320 0a000008    754 	beq	.L828

                     755 .L829:

                     756 ;256:     {


                     757 

                     758 ;257:         MmsDataAccessError result =


                     759 

00000324 e1a02005    760 	mov	r2,r5

00000328 e1a01007    761 	mov	r1,r7

0000032c e1a00004    762 	mov	r0,r4

00000330 eb000000*   763 	bl	IEDEntity_write

                     764 ;258:                 IEDEntity_write(nextChild, isoConn, value);


                     765 ;259:         if(result != DATA_ACCESS_ERROR_SUCCESS)


                     766 

00000334 e150000a    767 	cmp	r0,r10

                     768 ;260:         {


                     769 

                     770 ;261:             return result;


                     771 

00000338 1a00000a    772 	bne	.L810

                     773 ;262:         }


                     774 ;263:         nextChild = nextChild->next;


                     775 

0000033c e594400c    776 	ldr	r4,[r4,12]

00000340 e3540000    777 	cmp	r4,0

00000344 1afffff6    778 	bne	.L829

                     779 .L828:

                     780 ;264:     }


                     781 ;265: 


                     782 ;266: 	if(IEDControlDA_isControlDA(entity))



                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_7h41.s
                     783 

00000348 e1a00006    784 	mov	r0,r6

0000034c eb000000*   785 	bl	IEDControlDA_isControlDA

00000350 e3500000    786 	cmp	r0,0

                     787 ;269:     }


                     788 ;270:     else


                     789 ;271:     {


                     790 

                     791 ;272:         return DATA_ACCESS_ERROR_SUCCESS;


                     792 

00000354 128d2008    793 	addne	r2,sp,8

00000358 11a01007    794 	movne	r1,r7

0000035c 03e00000    795 	mvneq	r0,0

                     796 ;267:     {


                     797 

                     798 ;268: 		return IEDControlDA_write(entity, isoConn, &controlValue);


                     799 

00000360 11a00006    800 	movne	r0,r6

00000364 1b000000*   801 	blne	IEDControlDA_write

                     802 .L810:

00000368 e28dd014    803 	add	sp,sp,20

0000036c e8bd84f0    804 	ldmfd	[sp]!,{r4-r7,r10,pc}

                     805 	.endf	IEDComplexObj_write

                     806 	.align	4

                     807 .L383:

                     808 ;	"Oper\000"

00000370 7265704f    809 	.data.b	79,112,101,114

00000374 00         810 	.data.b	0

00000375 000000     811 	.align 4

                     812 

                     813 	.type	.L383,$object

                     814 	.size	.L383,4

                     815 

                     816 .L487:

                     817 ;	"t\000"

00000378 0074       818 	.data.b	116,0

0000037a 0000       819 	.align 4

                     820 

                     821 	.type	.L487,$object

                     822 	.size	.L487,4

                     823 

                     824 	.align	4

                     825 ;tag	[sp,3]	local

                     826 ;len	[sp,4]	local

                     827 ;nextChild	r4	local

                     828 ;controlValue	[sp,8]	local

                     829 ;result	r0	local

                     830 

                     831 ;entity	r6	param

                     832 ;isoConn	r7	param

                     833 ;value	r5	param

                     834 

                     835 	.section ".bss","awb"

                     836 .L982:

                     837 	.data

                     838 	.text

                     839 

                     840 ;273:     }


                     841 ;274: 


                     842 ;275: }


                     843 


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_7h41.s
                     844 ;276: 


                     845 ;277: static bool calcChildrenReadLen(IEDEntity entity, size_t* pLen )


                     846 	.align	4

                     847 	.align	4

                     848 calcChildrenReadLen:

0000037c e92d40f0    849 	stmfd	[sp]!,{r4-r7,lr}

                     850 ;278: {


                     851 

00000380 e1a05001    852 	mov	r5,r1

00000384 e3a04000    853 	mov	r4,0

                     854 ;279:     IEDEntity nextChild;


                     855 ;280:     size_t childrenLen = 0;


                     856 

                     857 ;281: 


                     858 ;282:     nextChild = entity->firstChild;


                     859 

00000388 e24dd004    860 	sub	sp,sp,4

0000038c e5906004    861 	ldr	r6,[r0,4]

                     862 ;283:     while(nextChild != NULL)


                     863 

00000390 e1a0700d    864 	mov	r7,sp

00000394 e3560000    865 	cmp	r6,0

00000398 0a00000b    866 	beq	.L1036

                     867 .L1037:

                     868 ;284:     {


                     869 

                     870 ;285:         size_t childLen;


                     871 ;286: 		bool result = nextChild->calcReadLen(nextChild, &childLen);


                     872 

0000039c e596c060    873 	ldr	r12,[r6,96]

000003a0 e1a01007    874 	mov	r1,r7

000003a4 e1a00006    875 	mov	r0,r6

000003a8 e1a0e00f    876 	mov	lr,pc

000003ac e12fff1c*   877 	bx	r12

                     878 ;287:         if(!result)


                     879 

000003b0 e3500000    880 	cmp	r0,0

                     881 ;288:         {


                     882 

                     883 ;289:             return false;


                     884 

000003b4 0a000006    885 	beq	.L1033

                     886 ;290:         }


                     887 ;291:         childrenLen += childLen;


                     888 

000003b8 e59d0000    889 	ldr	r0,[sp]

000003bc e596600c    890 	ldr	r6,[r6,12]

000003c0 e0844000    891 	add	r4,r4,r0

                     892 ;292:         nextChild = nextChild->next;


                     893 

000003c4 e3560000    894 	cmp	r6,0

000003c8 1afffff3    895 	bne	.L1037

                     896 .L1036:

                     897 ;293:     }


                     898 ;294:     *pLen = childrenLen;


                     899 

000003cc e5854000    900 	str	r4,[r5]

                     901 ;295:     return true;


                     902 

000003d0 e3a00001    903 	mov	r0,1

                     904 .L1033:


                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_7h41.s
000003d4 e28dd004    905 	add	sp,sp,4

000003d8 e8bd40f0    906 	ldmfd	[sp]!,{r4-r7,lr}

000003dc e12fff1e*   907 	ret	

                     908 	.endf	calcChildrenReadLen

                     909 	.align	4

                     910 ;nextChild	r6	local

                     911 ;childrenLen	r4	local

                     912 ;childLen	[sp]	local

                     913 ;result	r0	local

                     914 

                     915 ;entity	r0	param

                     916 ;pLen	r5	param

                     917 

                     918 	.section ".bss","awb"

                     919 .L1114:

                     920 	.data

                     921 	.text

                     922 

                     923 ;296: }


                     924 

                     925 ;297: 


                     926 ;298: bool IEDComplexObj_calcReadLen(IEDEntity entity, size_t* pLen)


                     927 	.align	4

                     928 	.align	4

                     929 IEDComplexObj_calcReadLen::

000003e0 e92d4010    930 	stmfd	[sp]!,{r4,lr}

000003e4 e24dd004    931 	sub	sp,sp,4

000003e8 e1a04001    932 	mov	r4,r1

000003ec e1a0100d    933 	mov	r1,sp

000003f0 ebffffe1*   934 	bl	calcChildrenReadLen

                     935 ;299: {


                     936 

                     937 ;300:     size_t childrenLen;


                     938 ;301: 


                     939 ;302:     if(!calcChildrenReadLen(entity, &childrenLen))


                     940 

000003f4 e3500000    941 	cmp	r0,0

                     942 ;303:     {


                     943 

                     944 ;304:         ERROR_REPORT("calcChildrenReadLen error");


                     945 ;305:         return false;


                     946 

000003f8 0a000006    947 	beq	.L1135

                     948 ;306:     }


                     949 ;307: 


                     950 ;308:     *pLen = 1 + BerEncoder_determineLengthSize(childrenLen) + childrenLen;


                     951 

000003fc e59d0000    952 	ldr	r0,[sp]

00000400 eb000000*   953 	bl	BerEncoder_determineLengthSize

00000404 e59d1000    954 	ldr	r1,[sp]

00000408 e2800001    955 	add	r0,r0,1

0000040c e0810000    956 	add	r0,r1,r0

00000410 e5840000    957 	str	r0,[r4]

                     958 ;309:     return true;


                     959 

00000414 e3a00001    960 	mov	r0,1

                     961 .L1135:

00000418 e28dd004    962 	add	sp,sp,4

0000041c e8bd8010    963 	ldmfd	[sp]!,{r4,pc}

                     964 	.endf	IEDComplexObj_calcReadLen

                     965 	.align	4


                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_7h41.s
                     966 ;childrenLen	[sp]	local

                     967 

                     968 ;entity	none	param

                     969 ;pLen	r4	param

                     970 

                     971 	.section ".bss","awb"

                     972 .L1189:

                     973 	.data

                     974 	.text

                     975 

                     976 ;310: }


                     977 

                     978 ;311: 


                     979 ;312: bool IEDComplexObj_encodeRead(IEDEntity entity, BufferView* outBuf)


                     980 	.align	4

                     981 	.align	4

                     982 IEDComplexObj_encodeRead::

00000420 e92d4030    983 	stmfd	[sp]!,{r4-r5,lr}

00000424 e24dd004    984 	sub	sp,sp,4

00000428 e1a04000    985 	mov	r4,r0

0000042c e1a05001    986 	mov	r5,r1

                     987 ;313: {


                     988 

                     989 ;314:     IEDEntity nextChild;


                     990 ;315:     size_t childrenReadLen;


                     991 ;316: 


                     992 ;317:     if(entity->type != IED_ENTITY_LN


                     993 

00000430 e5941050    994 	ldr	r1,[r4,80]

00000434 e3510003    995 	cmp	r1,3

00000438 13510004    996 	cmpne	r1,4

0000043c 13510005    997 	cmpne	r1,5

00000440 13510006    998 	cmpne	r1,6

00000444 1a000009    999 	bne	.L1215

                    1000 ;318:             && entity->type != IED_ENTITY_FC


                    1001 ;319:             && entity->type != IED_ENTITY_DO


                    1002 ;320:             && entity->type != IED_ENTITY_DA)


                    1003 ;321:     {


                    1004 

                    1005 ;322:         ERROR_REPORT("Invalid complex object");


                    1006 ;323:         return false;


                    1007 

                    1008 ;324:     }


                    1009 ;325: 


                    1010 ;326:     if(!calcChildrenReadLen(entity, &childrenReadLen))


                    1011 

00000448 e1a0100d   1012 	mov	r1,sp

0000044c ebffffca*  1013 	bl	calcChildrenReadLen

00000450 e3500000   1014 	cmp	r0,0

00000454 0a000005   1015 	beq	.L1215

                    1016 ;327:     {


                    1017 

                    1018 ;328:         ERROR_REPORT("calcChildrenReadLen error");


                    1019 ;329:         return false;


                    1020 

                    1021 ;330:     }


                    1022 ;331: 


                    1023 ;332:     //Тэг и размер структуры


                    1024 ;333:     if(!BufferView_encodeTL(outBuf, 0xA2, childrenReadLen))


                    1025 

00000458 e59d2000   1026 	ldr	r2,[sp]


                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_7h41.s
0000045c e1a00005   1027 	mov	r0,r5

00000460 e3a010a2   1028 	mov	r1,162

00000464 eb000000*  1029 	bl	BufferView_encodeTL

00000468 e3500000   1030 	cmp	r0,0

0000046c 1a000001   1031 	bne	.L1214

                    1032 .L1215:

                    1033 ;334:     {


                    1034 

                    1035 ;335:         ERROR_REPORT("Children TL encoding error");


                    1036 ;336:         return false;


                    1037 

00000470 e3a00000   1038 	mov	r0,0

00000474 ea00000d   1039 	b	.L1203

                    1040 .L1214:

                    1041 ;337:     }


                    1042 ;338: 


                    1043 ;339:     nextChild = entity->firstChild;


                    1044 

00000478 e5944004   1045 	ldr	r4,[r4,4]

                    1046 ;340:     while(nextChild != NULL)


                    1047 

0000047c e3540000   1048 	cmp	r4,0

00000480 0a000009   1049 	beq	.L1218

                    1050 .L1219:

                    1051 ;341:     {


                    1052 

                    1053 ;342: 		bool result = nextChild->encodeRead(nextChild, outBuf);


                    1054 

00000484 e594c05c   1055 	ldr	r12,[r4,92]

00000488 e1a01005   1056 	mov	r1,r5

0000048c e1a00004   1057 	mov	r0,r4

00000490 e1a0e00f   1058 	mov	lr,pc

00000494 e12fff1c*  1059 	bx	r12

                    1060 ;343:         if(!result)


                    1061 

00000498 e3500000   1062 	cmp	r0,0

0000049c 0afffff3   1063 	beq	.L1215

                    1064 ;344:         {


                    1065 

                    1066 ;345:             ERROR_REPORT("Error reading child");


                    1067 ;346:             return false;


                    1068 

                    1069 ;347:         }


                    1070 ;348:         nextChild = nextChild->next;


                    1071 

000004a0 e594400c   1072 	ldr	r4,[r4,12]

000004a4 e3540000   1073 	cmp	r4,0

000004a8 1afffff5   1074 	bne	.L1219

                    1075 .L1218:

                    1076 ;349:     }


                    1077 ;350:     return true;


                    1078 

000004ac e3a00001   1079 	mov	r0,1

                    1080 .L1203:

000004b0 e28dd004   1081 	add	sp,sp,4

000004b4 e8bd8030   1082 	ldmfd	[sp]!,{r4-r5,pc}

                    1083 	.endf	IEDComplexObj_encodeRead

                    1084 	.align	4

                    1085 ;nextChild	r4	local

                    1086 ;childrenReadLen	[sp]	local

                    1087 


                                                                      Page 19
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_7h41.s
                    1088 ;entity	r4	param

                    1089 ;outBuf	r5	param

                    1090 

                    1091 	.section ".bss","awb"

                    1092 .L1340:

                    1093 	.data

                    1094 	.text

                    1095 

                    1096 ;351: }


                    1097 	.align	4

                    1098 

                    1099 	.data

                    1100 	.ghsnote version,6

                    1101 	.ghsnote tools,3

                    1102 	.ghsnote options,0

                    1103 	.text

                    1104 	.align	4

