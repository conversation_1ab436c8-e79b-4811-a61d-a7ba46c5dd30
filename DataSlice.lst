                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b081.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=DataSlice.c -o gh_b081.o -list=DataSlice.lst C:\Users\<USER>\AppData\Local\Temp\gh_b081.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_b081.s
Source File: DataSlice.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile DataSlice.c -o

                      10 ;		DataSlice.o

                      11 ;Source File:   DataSlice.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Oct 07 09:39:38 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "DataSlice.h"


                      21 ;2: #include <debug.h>


                      22 ;3: #include "../../../../DataSlice/DataSliceClient/datasliceif.h"


                      23 ;4: #include "fast_memcpy.h"


                      24 ;5: #include <stdint.h>


                      25 ;6: 


                      26 ;7: #define MAX_DATA_SLICE 16384


                      27 ;8: 


                      28 ;9: //! TODO: переименовать, добавив префикс lists (напр.listsDataSlice, listsCurrentDataSlice и тд)


                      29 ;10: //Интерфейс DataSlice


                      30 ;11: DataSliceIf* dataSliceIf;


                      31 ;12: 


                      32 ;13: DataSliceWnd* currentDataSlice;


                      33 ;14: 


                      34 ;15: #pragma alignvar (4)


                      35 ;16: uint8_t  dataSliceCopy[MAX_DATA_SLICE];


                      36 ;17: size_t dataSliceSize;


                      37 ;18: 


                      38 ;19: static OnUpdateDataSliceFunc g_oldCallBack;


                      39 ;20: static OnUpdateDataSliceFunc g_newCallBack;


                      40 ;21: 


                      41 ;22: //! dataslice для уставок


                      42 ;23: DataSliceSetts* settsDataSlice;


                      43 ;24: size_t settsDataSliceSize;


                      44 ;25: 


                      45 ;26: //! возвращает интерфейс для dataslice расчетных значений или уставок


                      46 ;27: static void *getDataSliceInterface(const char *moduleId)


                      47 ;28: {


                      48 ;29: 	 GetDataSliceIf getDataSlice;


                      49 ;30: 	 _HANDLE hModule = _GetModuleHandle((char*)moduleId);


                      50 ;31: 	if (!hModule) return NULL;



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b081.s
                      51 ;32: 	


                      52 ;33: 	getDataSlice = (GetDataSliceIf)_GetProcAddress(hModule,GET_DATASLICEIF_PREFIX);


                      53 ;34: 	if (!getDataSlice) return NULL;


                      54 ;35: 	


                      55 ;36: 	return getDataSlice();


                      56 ;37: 	


                      57 ;38: }


                      58 ;39: 


                      59 ;40: bool dataSliceInit(void)


                      60 ;41: {


                      61 ;42:    


                      62 ;43: 	// расчетные значения


                      63 ;44:     dataSliceIf = getDataSliceInterface(DATA_SLICE_LISTS_MODULE_ID);


                      64 ;45: 	if (!dataSliceIf) 


                      65 ;46: 		return false;


                      66 ;47: 	// getDataSliceWndSize не возвращает 0


                      67 ;48: 	dataSliceSize = dataSliceIf->getDataSliceWndSize();


                      68 ;49: 	


                      69 ;50: 	// уставки


                      70 ;51: 	settsDataSlice = getDataSliceInterface(DATA_SLICE_SETTS_MODULE_ID);


                      71 ;52: 	if (!settsDataSlice)


                      72 ;53: 		return false;


                      73 ;54: 	


                      74 ;55: 	settsDataSliceSize = settsDataSlice->getDataSliceWndSize();


                      75 ;56: 	


                      76 ;57: 	


                      77 ;58:     return true;


                      78 ;59: }


                      79 ;60: 


                      80 ;61: void dataSliceCapture(void)


                      81 ;62: {


                      82 ;63: 	//currentDataSlice = dataSliceIf->getDataSliceWnd();


                      83 ;64: 


                      84 ;65: 	fast_memcpy(dataSliceCopy, dataSliceIf->getDataSliceWnd(), dataSliceSize);


                      85 ;66: 


                      86 ;67: 	currentDataSlice = (DataSliceWnd*)dataSliceCopy;


                      87 ;68: }


                      88 ;69: 


                      89 ;70: void dataSliceRelease(void)


                      90 

                      91 ;72: 


                      92 ;73: }


                      93 

                      94 	.text

                      95 	.align	4

                      96 getDataSliceInterface:

00000000 e92d4000     97 	stmfd	[sp]!,{lr}

00000004 eb000000*    98 	bl	_GetModuleHandle

00000008 e3500000     99 	cmp	r0,0

0000000c 0a000003    100 	beq	.L27

00000010 e3e01360    101 	mvn	r1,0x80000001

00000014 e2811c80    102 	add	r1,r1,1<<15

00000018 eb000000*   103 	bl	_GetProcAddress

0000001c e1b0c000    104 	movs	r12,r0

                     105 .L27:

00000020 03a00000    106 	moveq	r0,0

00000024 0a000001    107 	beq	.L21

                     108 .L26:

00000028 e1a0e00f    109 	mov	lr,pc

0000002c e12fff1c*   110 	bx	r12

                     111 .L21:


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b081.s
00000030 e8bd4000    112 	ldmfd	[sp]!,{lr}

00000034 e12fff1e*   113 	ret	

                     114 	.endf	getDataSliceInterface

                     115 	.align	4

                     116 ;getDataSlice	r12	local

                     117 ;hModule	r1	local

                     118 

                     119 ;moduleId	none	param

                     120 

                     121 	.section ".bss","awb"

                     122 .L76:

                     123 	.data

                     124 	.text

                     125 

                     126 

                     127 	.align	4

                     128 	.align	4

                     129 dataSliceInit::

00000038 e92d4100    130 	stmfd	[sp]!,{r8,lr}

0000003c e28f0000*   131 	adr	r0,.L173

00000040 ebffffee*   132 	bl	getDataSliceInterface

00000044 e59f817c*   133 	ldr	r8,.L174

00000048 e1b0c000    134 	movs	r12,r0

0000004c e588c000    135 	str	r12,[r8]

00000050 0a000009    136 	beq	.L95

00000054 e59cc020    137 	ldr	r12,[r12,32]

00000058 e1a0e00f    138 	mov	lr,pc

0000005c e12fff1c*   139 	bx	r12

00000060 e59f1164*   140 	ldr	r1,.L175

00000064 e5810000    141 	str	r0,[r1]

00000068 e28f0000*   142 	adr	r0,.L176

0000006c ebffffe3*   143 	bl	getDataSliceInterface

00000070 e59f8168*   144 	ldr	r8,.L177

00000074 e1b0c000    145 	movs	r12,r0

00000078 e588c000    146 	str	r12,[r8]

                     147 .L95:

0000007c 03a00000    148 	moveq	r0,0

00000080 0a000005    149 	beq	.L89

                     150 .L94:

00000084 e59cc008    151 	ldr	r12,[r12,8]

00000088 e1a0e00f    152 	mov	lr,pc

0000008c e12fff1c*   153 	bx	r12

00000090 e59f114c*   154 	ldr	r1,.L178

00000094 e5810000    155 	str	r0,[r1]

00000098 e3a00001    156 	mov	r0,1

                     157 .L89:

0000009c e8bd8100    158 	ldmfd	[sp]!,{r8,pc}

                     159 	.endf	dataSliceInit

                     160 	.align	4

                     161 ;.L157	.L161	static

                     162 ;.L158	.L162	static

                     163 

                     164 	.section ".bss","awb"

                     165 .L156:

                     166 	.data

                     167 	.text

                     168 

                     169 

                     170 	.align	4

                     171 	.align	4

                     172 dataSliceCapture::


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b081.s
000000a0 e92d4100    173 	stmfd	[sp]!,{r8,lr}

000000a4 e59f811c*   174 	ldr	r8,.L174

000000a8 e598c000    175 	ldr	r12,[r8]

000000ac e59cc004    176 	ldr	r12,[r12,4]

000000b0 e1a0e00f    177 	mov	lr,pc

000000b4 e12fff1c*   178 	bx	r12

000000b8 e59f810c*   179 	ldr	r8,.L175

000000bc e1a01000    180 	mov	r1,r0

000000c0 e5982000    181 	ldr	r2,[r8]

000000c4 e59f011c*   182 	ldr	r0,.L213

000000c8 eb000000*   183 	bl	fast_memcpy

000000cc e59f0114*   184 	ldr	r0,.L213

000000d0 e59f8114*   185 	ldr	r8,.L214

000000d4 e5880000    186 	str	r0,[r8]

000000d8 e8bd8100    187 	ldmfd	[sp]!,{r8,pc}

                     188 	.endf	dataSliceCapture

                     189 	.align	4

                     190 

                     191 	.section ".bss","awb"

                     192 .L206:

                     193 	.data

                     194 	.text

                     195 

                     196 

                     197 ;74: 


                     198 ;75: unsigned long long getCurrentDataSliceTime(void)


                     199 	.align	4

                     200 	.align	4

                     201 getCurrentDataSliceTime::

000000dc e92d4100    202 	stmfd	[sp]!,{r8,lr}

                     203 ;76: {


                     204 

                     205 ;77:     return dataSliceIf->getDataSliceWnd()->time;


                     206 

000000e0 e59f80e0*   207 	ldr	r8,.L174

000000e4 e598c000    208 	ldr	r12,[r8]

000000e8 e59cc004    209 	ldr	r12,[r12,4]

000000ec e1a0e00f    210 	mov	lr,pc

000000f0 e12fff1c*   211 	bx	r12

000000f4 e8900006    212 	ldmfd	[r0],{r1-r2}

000000f8 e1a00001    213 	mov	r0,r1

000000fc e1a01002    214 	mov	r1,r2

00000100 e8bd8100    215 	ldmfd	[sp]!,{r8,pc}

                     216 	.endf	getCurrentDataSliceTime

                     217 	.align	4

                     218 

                     219 	.section ".bss","awb"

                     220 .L238:

                     221 	.data

                     222 	.text

                     223 

                     224 ;78: }


                     225 

                     226 ;79: 


                     227 ;80: unsigned long long dataSliceGetTimeStamp(void)


                     228 	.align	4

                     229 	.align	4

                     230 dataSliceGetTimeStamp::

                     231 ;81: {


                     232 

                     233 ;82:     return currentDataSlice->time;



                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b081.s
                     234 

00000104 e59f00e0*   235 	ldr	r0,.L214

00000108 e5902000    236 	ldr	r2,[r0]

0000010c e8920003    237 	ldmfd	[r2],{r0-r1}

00000110 e12fff1e*   238 	ret	

                     239 	.endf	dataSliceGetTimeStamp

                     240 	.align	4

                     241 

                     242 	.section ".bss","awb"

                     243 .L270:

                     244 	.data

                     245 	.text

                     246 

                     247 ;83: }


                     248 

                     249 ;84: 


                     250 ;85: long dataSliceGetFloatValue(uint16_t offset)


                     251 	.align	4

                     252 	.align	4

                     253 dataSliceGetFloatValue::

00000114 e92d4000    254 	stmfd	[sp]!,{lr}

00000118 e59fc0a8*   255 	ldr	r12,.L174

0000011c e24dd004    256 	sub	sp,sp,4

00000120 e59c1000    257 	ldr	r1,[r12]

00000124 e591c008    258 	ldr	r12,[r1,8]

00000128 e1a0100d    259 	mov	r1,sp

0000012c e1a0e00f    260 	mov	lr,pc

00000130 e12fff1c*   261 	bx	r12

                     262 ;86: {


                     263 

                     264 ;87:     long value;    


                     265 ;88:     if(!dataSliceIf->getAnalogValue(offset, &value))


                     266 

00000134 e3500000    267 	cmp	r0,0

00000138 159d0000    268 	ldrne	r0,[sp]

                     269 ;91:         ERROR_REPORT("DataSlice analog value error");


                     270 ;92:         TRACE("Offset = %d", offset);


                     271 ;93:     }


                     272 ;94:     return value;


                     273 

                     274 ;89:     {


                     275 

                     276 ;90:         value = 0;


                     277 

0000013c 058d0000    278 	streq	r0,[sp]

                     279 ;91:         ERROR_REPORT("DataSlice analog value error");


                     280 ;92:         TRACE("Offset = %d", offset);


                     281 ;93:     }


                     282 ;94:     return value;


                     283 

00000140 e28dd004    284 	add	sp,sp,4

00000144 e8bd8000    285 	ldmfd	[sp]!,{pc}

                     286 	.endf	dataSliceGetFloatValue

                     287 	.align	4

                     288 ;value	[sp]	local

                     289 

                     290 ;offset	none	param

                     291 

                     292 	.section ".bss","awb"

                     293 .L328:

                     294 	.data


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b081.s
                     295 	.text

                     296 

                     297 ;95: }


                     298 

                     299 ;96: 


                     300 ;97: float dataSliceGetRealValue(uint16_t offset)


                     301 	.align	4

                     302 	.align	4

                     303 dataSliceGetRealValue::

00000148 e92d4000    304 	stmfd	[sp]!,{lr}

                     305 ;98: {


                     306 

                     307 ;99:     float value;


                     308 ;100:     if(!dataSliceIf->getAnalogValue(offset, (void*)&value))


                     309 

0000014c e59fc074*   310 	ldr	r12,.L174

00000150 e24dd004    311 	sub	sp,sp,4

00000154 e59c1000    312 	ldr	r1,[r12]

00000158 e591c008    313 	ldr	r12,[r1,8]

0000015c e1a0100d    314 	mov	r1,sp

00000160 e1a0e00f    315 	mov	lr,pc

00000164 e12fff1c*   316 	bx	r12

00000168 e3500000    317 	cmp	r0,0

0000016c 159d0000    318 	ldrne	r0,[sp]

                     319 ;103:         ERROR_REPORT("DataSlice analog value error");


                     320 ;104:         TRACE("Offset = %d", offset);


                     321 ;105:     }


                     322 ;106: 


                     323 ;107:     return value;


                     324 

                     325 ;101:     {


                     326 

                     327 ;102:         value = 0;


                     328 

00000170 058d0000    329 	streq	r0,[sp]

                     330 ;103:         ERROR_REPORT("DataSlice analog value error");


                     331 ;104:         TRACE("Offset = %d", offset);


                     332 ;105:     }


                     333 ;106: 


                     334 ;107:     return value;


                     335 

00000174 e28dd004    336 	add	sp,sp,4

00000178 e8bd8000    337 	ldmfd	[sp]!,{pc}

                     338 	.endf	dataSliceGetRealValue

                     339 	.align	4

                     340 ;value	[sp]	local

                     341 

                     342 ;offset	none	param

                     343 

                     344 	.section ".bss","awb"

                     345 .L387:

                     346 	.data

                     347 	.text

                     348 

                     349 ;108: }


                     350 

                     351 ;109: 


                     352 ;110: bool dataSliceGetBoolValue(uint16_t offset)


                     353 	.align	4

                     354 	.align	4

                     355 dataSliceGetBoolValue::


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b081.s
0000017c e92d4000    356 	stmfd	[sp]!,{lr}

00000180 e59fc040*   357 	ldr	r12,.L174

00000184 e24dd004    358 	sub	sp,sp,4

00000188 e59c1000    359 	ldr	r1,[r12]

0000018c e591c00c    360 	ldr	r12,[r1,12]

00000190 e28d1003    361 	add	r1,sp,3

00000194 e1a0e00f    362 	mov	lr,pc

00000198 e12fff1c*   363 	bx	r12

                     364 ;111: {


                     365 

                     366 ;112:     bool value;    


                     367 ;113: 


                     368 ;114:     if(!dataSliceIf->getBoolValue(offset, &value))


                     369 

0000019c e3500000    370 	cmp	r0,0

000001a0 15dd0003    371 	ldrneb	r0,[sp,3]

                     372 ;117:         ERROR_REPORT("DataSlice bool value error");


                     373 ;118:         TRACE("Offset = %d", offset);


                     374 ;119:     }


                     375 ;120: 


                     376 ;121:     return value != 0;


                     377 

                     378 ;115:     {


                     379 

                     380 ;116:         value = 0;


                     381 

000001a4 05cd0003    382 	streqb	r0,[sp,3]

                     383 ;117:         ERROR_REPORT("DataSlice bool value error");


                     384 ;118:         TRACE("Offset = %d", offset);


                     385 ;119:     }


                     386 ;120: 


                     387 ;121:     return value != 0;


                     388 

000001a8 e3500000    389 	cmp	r0,0

000001ac 13a00001    390 	movne	r0,1

000001b0 e28dd004    391 	add	sp,sp,4

000001b4 e8bd8000    392 	ldmfd	[sp]!,{pc}

                     393 	.endf	dataSliceGetBoolValue

                     394 	.align	4

                     395 .L173:

                     396 ;	"DataSliceClient\000"

000001b8 61746144    397 	.data.b	68,97,116,97

000001bc 63696c53    398 	.data.b	83,108,105,99

000001c0 696c4365    399 	.data.b	101,67,108,105

000001c4 00746e65    400 	.data.b	101,110,116,0

                     401 	.align 4

                     402 

                     403 	.type	.L173,$object

                     404 	.size	.L173,4

                     405 

                     406 .L174:

000001c8 00000000*   407 	.data.w	dataSliceIf

                     408 	.type	.L174,$object

                     409 	.size	.L174,4

                     410 

                     411 .L175:

000001cc 00000000*   412 	.data.w	dataSliceSize

                     413 	.type	.L175,$object

                     414 	.size	.L175,4

                     415 

                     416 .L176:


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b081.s
                     417 ;	"DataSliceSetts\000"

000001d0 61746144    418 	.data.b	68,97,116,97

000001d4 63696c53    419 	.data.b	83,108,105,99

000001d8 74655365    420 	.data.b	101,83,101,116

000001dc 7374       421 	.data.b	116,115

000001de 00         422 	.data.b	0

000001df 00         423 	.align 4

                     424 

                     425 	.type	.L176,$object

                     426 	.size	.L176,4

                     427 

                     428 .L177:

000001e0 00000000*   429 	.data.w	settsDataSlice

                     430 	.type	.L177,$object

                     431 	.size	.L177,4

                     432 

                     433 .L178:

000001e4 00000000*   434 	.data.w	settsDataSliceSize

                     435 	.type	.L178,$object

                     436 	.size	.L178,4

                     437 

                     438 .L213:

000001e8 00000000*   439 	.data.w	dataSliceCopy

                     440 	.type	.L213,$object

                     441 	.size	.L213,4

                     442 

                     443 .L214:

000001ec 00000000*   444 	.data.w	currentDataSlice

                     445 	.type	.L214,$object

                     446 	.size	.L214,4

                     447 

                     448 	.align	4

                     449 ;value	[sp,3]	local

                     450 

                     451 ;offset	none	param

                     452 

                     453 	.section ".bss","awb"

                     454 .L461:

                     455 	.data

                     456 	.text

                     457 

                     458 ;122: }


                     459 

                     460 ;123: 


                     461 ;124: int dataSliceGetIntValue(uint16_t offset)


                     462 	.align	4

                     463 	.align	4

                     464 dataSliceGetIntValue::

000001f0 e92d4000    465 	stmfd	[sp]!,{lr}

000001f4 e51fc034*   466 	ldr	r12,.L174

000001f8 e24dd004    467 	sub	sp,sp,4

000001fc e59c1000    468 	ldr	r1,[r12]

00000200 e591c010    469 	ldr	r12,[r1,16]

00000204 e1a0100d    470 	mov	r1,sp

00000208 e1a0e00f    471 	mov	lr,pc

0000020c e12fff1c*   472 	bx	r12

                     473 ;125: {


                     474 

                     475 ;126:     long value;    


                     476 ;127:     if(!dataSliceIf->getIntValue(offset, &value))


                     477 


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b081.s
00000210 e3500000    478 	cmp	r0,0

00000214 159d0000    479 	ldrne	r0,[sp]

                     480 ;130:         ERROR_REPORT("DataSlice int value error");


                     481 ;131:         TRACE("Offset = %d", offset);


                     482 ;132:     }


                     483 ;133:     return value;


                     484 

                     485 ;128:     {


                     486 

                     487 ;129:         value = 0;


                     488 

00000218 058d0000    489 	streq	r0,[sp]

                     490 ;130:         ERROR_REPORT("DataSlice int value error");


                     491 ;131:         TRACE("Offset = %d", offset);


                     492 ;132:     }


                     493 ;133:     return value;


                     494 

0000021c e28dd004    495 	add	sp,sp,4

00000220 e8bd8000    496 	ldmfd	[sp]!,{pc}

                     497 	.endf	dataSliceGetIntValue

                     498 	.align	4

                     499 ;value	[sp]	local

                     500 

                     501 ;offset	none	param

                     502 

                     503 	.section ".bss","awb"

                     504 .L520:

                     505 	.data

                     506 	.text

                     507 

                     508 ;134: }


                     509 

                     510 ;135: 


                     511 ;136: bool DataSlice_getBoolFast(void* dataSliceWnd, uint16_t offset)


                     512 	.align	4

                     513 	.align	4

                     514 DataSlice_getBoolFast::

                     515 ;137: {


                     516 

                     517 ;138:     uint8_t* pWnd = dataSliceWnd;


                     518 

                     519 ;139:     return pWnd[offset];


                     520 

00000224 e7d00001    521 	ldrb	r0,[r0,r1]

00000228 e12fff1e*   522 	ret	

                     523 	.endf	DataSlice_getBoolFast

                     524 	.align	4

                     525 

                     526 ;dataSliceWnd	r0	param

                     527 ;offset	r1	param

                     528 

                     529 	.section ".bss","awb"

                     530 .L561:

                     531 	.data

                     532 	.text

                     533 

                     534 ;140: }


                     535 

                     536 ;141: 


                     537 ;142: bool DataSlice_getBoolFastCurrDS(uint16_t offset)


                     538 	.align	4


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b081.s
                     539 	.align	4

                     540 DataSlice_getBoolFastCurrDS::

                     541 ;143: {


                     542 

                     543 ;144: 	uint8_t* pWnd = (uint8_t*)currentDataSlice;


                     544 

0000022c e51f2048*   545 	ldr	r2,.L214

00000230 e5921000    546 	ldr	r1,[r2]

                     547 ;145: 	return pWnd[offset];


                     548 

00000234 e7d10000    549 	ldrb	r0,[r1,r0]

00000238 e12fff1e*   550 	ret	

                     551 	.endf	DataSlice_getBoolFastCurrDS

                     552 	.align	4

                     553 ;pWnd	r1	local

                     554 

                     555 ;offset	r0	param

                     556 

                     557 	.section ".bss","awb"

                     558 .L593:

                     559 	.data

                     560 	.text

                     561 

                     562 ;146: }


                     563 

                     564 ;147: 


                     565 ;148: int DataSlice_getInt32FastCurrDS(uint16_t offset)


                     566 	.align	4

                     567 	.align	4

                     568 DataSlice_getInt32FastCurrDS::

                     569 ;149: {


                     570 

                     571 ;150: 	uint8_t* pWnd = (uint8_t*)currentDataSlice;


                     572 

0000023c e51f2058*   573 	ldr	r2,.L214

00000240 e5921000    574 	ldr	r1,[r2]

                     575 ;151: 	return *(int*)(pWnd+offset);


                     576 

00000244 e7910000    577 	ldr	r0,[r1,r0]

00000248 e12fff1e*   578 	ret	

                     579 	.endf	DataSlice_getInt32FastCurrDS

                     580 	.align	4

                     581 ;pWnd	r1	local

                     582 

                     583 ;offset	r0	param

                     584 

                     585 	.section ".bss","awb"

                     586 .L622:

                     587 	.data

                     588 	.text

                     589 

                     590 ;152: }


                     591 

                     592 ;153: 


                     593 ;154: uint32_t DataSlice_getUInt32FastCurrDS(uint16_t offset)


                     594 	.align	4

                     595 	.align	4

                     596 DataSlice_getUInt32FastCurrDS::

                     597 ;155: {


                     598 

                     599 ;156:     uint8_t* pWnd = (uint8_t*)currentDataSlice;



                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b081.s
                     600 

0000024c e51f2068*   601 	ldr	r2,.L214

00000250 e5921000    602 	ldr	r1,[r2]

                     603 ;157:     return *(uint32_t*)(pWnd+offset);


                     604 

00000254 e7910000    605 	ldr	r0,[r1,r0]

00000258 e12fff1e*   606 	ret	

                     607 	.endf	DataSlice_getUInt32FastCurrDS

                     608 	.align	4

                     609 ;pWnd	r1	local

                     610 

                     611 ;offset	r0	param

                     612 

                     613 	.section ".bss","awb"

                     614 .L654:

                     615 	.data

                     616 	.text

                     617 

                     618 ;158: }


                     619 

                     620 ;159: 


                     621 ;160: float DataSlice_getRealFastCurrDS(uint16_t offset)


                     622 	.align	4

                     623 	.align	4

                     624 DataSlice_getRealFastCurrDS::

                     625 ;161: {


                     626 

                     627 ;162: 	uint8_t* pWnd = (uint8_t*)currentDataSlice;


                     628 

0000025c e51f2078*   629 	ldr	r2,.L214

00000260 e5921000    630 	ldr	r1,[r2]

                     631 ;163: 	return *(float*)(pWnd+offset);


                     632 

00000264 e7910000    633 	ldr	r0,[r1,r0]

00000268 e12fff1e*   634 	ret	

                     635 	.endf	DataSlice_getRealFastCurrDS

                     636 	.align	4

                     637 ;pWnd	r1	local

                     638 

                     639 ;offset	r0	param

                     640 

                     641 	.section ".bss","awb"

                     642 .L686:

                     643 	.data

                     644 	.text

                     645 

                     646 ;164: }


                     647 

                     648 ;165: 


                     649 ;166: int DataSlice_getFixedFastCurrDS(uint16_t offset)


                     650 	.align	4

                     651 	.align	4

                     652 DataSlice_getFixedFastCurrDS::

                     653 ;167: {


                     654 

                     655 ;168: 	uint8_t* pWnd = (uint8_t*)currentDataSlice;


                     656 

0000026c e51f2088*   657 	ldr	r2,.L214

00000270 e5921000    658 	ldr	r1,[r2]

                     659 ;169: 	return *(int*)(pWnd+offset);


                     660 


                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b081.s
00000274 e7910000    661 	ldr	r0,[r1,r0]

00000278 e12fff1e*   662 	ret	

                     663 	.endf	DataSlice_getFixedFastCurrDS

                     664 	.align	4

                     665 ;pWnd	r1	local

                     666 

                     667 ;offset	r0	param

                     668 

                     669 	.section ".bss","awb"

                     670 .L718:

                     671 	.data

                     672 	.text

                     673 

                     674 ;170: }


                     675 

                     676 ;171: 


                     677 ;172: 


                     678 ;173: void* DataSlice_getDataSliceWnd(void)


                     679 	.align	4

                     680 	.align	4

                     681 DataSlice_getDataSliceWnd::

0000027c e92d4100    682 	stmfd	[sp]!,{r8,lr}

                     683 ;174: {


                     684 

                     685 ;175:     return dataSliceIf->getDataSliceWnd();


                     686 

00000280 e51f80c0*   687 	ldr	r8,.L174

00000284 e598c000    688 	ldr	r12,[r8]

00000288 e59cc004    689 	ldr	r12,[r12,4]

0000028c e1a0e00f    690 	mov	lr,pc

00000290 e12fff1c*   691 	bx	r12

00000294 e8bd8100    692 	ldmfd	[sp]!,{r8,pc}

                     693 	.endf	DataSlice_getDataSliceWnd

                     694 	.align	4

                     695 

                     696 	.section ".bss","awb"

                     697 .L750:

                     698 	.data

                     699 	.text

                     700 

                     701 ;176: }


                     702 

                     703 ;177: 


                     704 ;178: int DataSlice_getBoolOffset(int offset)


                     705 	.align	4

                     706 	.align	4

                     707 DataSlice_getBoolOffset::

00000298 e92d4100    708 	stmfd	[sp]!,{r8,lr}

                     709 ;179: {


                     710 

                     711 ;180:     uint16_t dsOffset;


                     712 ;181: 


                     713 ;182:     if(offset == -1)


                     714 

0000029c e3700001    715 	cmn	r0,1

000002a0 0a000009    716 	beq	.L763

                     717 ;183:     {


                     718 

                     719 ;184:         return -1;


                     720 

                     721 ;185:     }



                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b081.s
                     722 ;186:     dsOffset = dataSliceIf->getBoolOffset(offset);


                     723 

000002a4 e51f80e4*   724 	ldr	r8,.L174

000002a8 e598c000    725 	ldr	r12,[r8]

000002ac e1a00800    726 	mov	r0,r0 lsl 16

000002b0 e59cc01c    727 	ldr	r12,[r12,28]

000002b4 e1a00820    728 	mov	r0,r0 lsr 16

000002b8 e1a0e00f    729 	mov	lr,pc

000002bc e12fff1c*   730 	bx	r12

                     731 ;187:     if(dsOffset == 0xFFFF)


                     732 

000002c0 e3a01cff    733 	mov	r1,255<<8

000002c4 e28110ff    734 	add	r1,r1,255

000002c8 e1500001    735 	cmp	r0,r1

                     736 ;192:     }


                     737 ;193:     return dsOffset;


                     738 

                     739 .L763:

                     740 ;188:     {


                     741 

                     742 ;189: 		TRACE("DataSlice bool offset  %x is not found", offset);


                     743 ;190:         ERROR_REPORT("DataSlice bool offset  %d is not found", offset);


                     744 ;191:         return -1;


                     745 

000002cc 03e00000    746 	mvneq	r0,0

                     747 .L757:

000002d0 e8bd8100    748 	ldmfd	[sp]!,{r8,pc}

                     749 	.endf	DataSlice_getBoolOffset

                     750 	.align	4

                     751 ;dsOffset	r0	local

                     752 

                     753 ;offset	r0	param

                     754 

                     755 	.section ".bss","awb"

                     756 .L812:

                     757 	.data

                     758 	.text

                     759 

                     760 ;194: }


                     761 

                     762 ;195: 


                     763 ;196: int DataSlice_getIntOffset(int offset)


                     764 	.align	4

                     765 	.align	4

                     766 DataSlice_getIntOffset::

000002d4 e92d4100    767 	stmfd	[sp]!,{r8,lr}

                     768 ;197: {


                     769 

                     770 ;198: 	uint16_t dsOffset;


                     771 ;199: 


                     772 ;200: 	dsOffset = dataSliceIf->getIntOffset(offset);


                     773 

000002d8 e51f8118*   774 	ldr	r8,.L174

000002dc e598c000    775 	ldr	r12,[r8]

000002e0 e1a00800    776 	mov	r0,r0 lsl 16

000002e4 e59cc018    777 	ldr	r12,[r12,24]

000002e8 e1a00820    778 	mov	r0,r0 lsr 16

000002ec e1a0e00f    779 	mov	lr,pc

000002f0 e12fff1c*   780 	bx	r12

                     781 ;201: 	if(dsOffset == 0xFFFF)


                     782 


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b081.s
000002f4 e3a01cff    783 	mov	r1,255<<8

000002f8 e28110ff    784 	add	r1,r1,255

000002fc e1500001    785 	cmp	r0,r1

                     786 ;202: 	{


                     787 

                     788 ;203: 		ERROR_REPORT("DataSlice int offset  %d is not found", offset);


                     789 ;204: 		return -1;


                     790 

00000300 03e00000    791 	mvneq	r0,0

                     792 ;205: 	}


                     793 ;206: 	return dsOffset;


                     794 

00000304 e8bd8100    795 	ldmfd	[sp]!,{r8,pc}

                     796 	.endf	DataSlice_getIntOffset

                     797 	.align	4

                     798 ;dsOffset	r0	local

                     799 

                     800 ;offset	r0	param

                     801 

                     802 	.section ".bss","awb"

                     803 .L870:

                     804 	.data

                     805 	.text

                     806 

                     807 ;207: }


                     808 

                     809 ;208: 


                     810 ;209: int DataSlice_getAnalogOffset(int offset)


                     811 	.align	4

                     812 	.align	4

                     813 DataSlice_getAnalogOffset::

00000308 e92d4100    814 	stmfd	[sp]!,{r8,lr}

                     815 ;210: {


                     816 

                     817 ;211: 	uint16_t dsOffset;


                     818 ;212: 	dsOffset = dataSliceIf->getAnalogOffset(offset);


                     819 

0000030c e51f814c*   820 	ldr	r8,.L174

00000310 e598c000    821 	ldr	r12,[r8]

00000314 e1a00800    822 	mov	r0,r0 lsl 16

00000318 e59cc014    823 	ldr	r12,[r12,20]

0000031c e1a00820    824 	mov	r0,r0 lsr 16

00000320 e1a0e00f    825 	mov	lr,pc

00000324 e12fff1c*   826 	bx	r12

                     827 ;213: 	if(dsOffset == 0xFFFF)


                     828 

00000328 e3a01cff    829 	mov	r1,255<<8

0000032c e28110ff    830 	add	r1,r1,255

00000330 e1500001    831 	cmp	r0,r1

                     832 ;214: 	{


                     833 

                     834 ;215: 		ERROR_REPORT("DataSlice analog offset  %d is not found", offset);


                     835 ;216: 		return -1;


                     836 

00000334 03e00000    837 	mvneq	r0,0

                     838 ;217: 	}


                     839 ;218: 	return dsOffset;


                     840 

00000338 e8bd8100    841 	ldmfd	[sp]!,{r8,pc}

                     842 	.endf	DataSlice_getAnalogOffset

                     843 	.align	4


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b081.s
                     844 ;dsOffset	r0	local

                     845 

                     846 ;offset	r0	param

                     847 

                     848 	.section ".bss","awb"

                     849 .L918:

                     850 	.data

                     851 	.text

                     852 

                     853 ;219: }


                     854 

                     855 ;220: 


                     856 ;221: static void DataSlice_callback()


                     857 	.align	4

                     858 	.align	4

                     859 	.align	4

                     860 DataSlice_callback:

0000033c e92d4010    861 	stmfd	[sp]!,{r4,lr}

                     862 ;222: {	


                     863 

                     864 ;223:     g_newCallBack();


                     865 

00000340 e59f4050*   866 	ldr	r4,.L979

00000344 e594c004    867 	ldr	r12,[r4,4]

00000348 e1a0e00f    868 	mov	lr,pc

0000034c e12fff1c*   869 	bx	r12

                     870 ;224:     if(g_oldCallBack)


                     871 

00000350 e594c000    872 	ldr	r12,[r4]

00000354 e35c0000    873 	cmp	r12,0

                     874 ;225:     {


                     875 

                     876 ;226:         g_oldCallBack();


                     877 

00000358 11a0e00f    878 	movne	lr,pc

0000035c 112fff1c*   879 	bxne	r12

00000360 e8bd4010    880 	ldmfd	[sp]!,{r4,lr}

00000364 e12fff1e*   881 	ret	

                     882 	.endf	DataSlice_callback

                     883 	.align	4

                     884 

                     885 	.section ".bss","awb"

                     886 .L968:

00000000 00000000    887 g_oldCallBack:	.space	4

00000004 00000000    888 g_newCallBack:	.space	4

                     889 	.data

                     890 	.text

                     891 

                     892 ;227:     }


                     893 ;228: }


                     894 

                     895 ;229: 


                     896 ;230: void DataSlice_setCallBack(void (*func)(void))


                     897 	.align	4

                     898 	.align	4

                     899 DataSlice_setCallBack::

00000368 e92d4110    900 	stmfd	[sp]!,{r4,r8,lr}

0000036c e59f4028*   901 	ldr	r4,.L1012

                     902 ;231: {


                     903 

                     904 ;232:     g_newCallBack = func;



                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b081.s
                     905 

00000370 e51f81b0*   906 	ldr	r8,.L174

00000374 e5840004    907 	str	r0,[r4,4]

00000378 e598c000    908 	ldr	r12,[r8]

0000037c e59f001c*   909 	ldr	r0,.L1013

                     910 ;233:     g_oldCallBack = dataSliceIf->setOnUpdateCallback(DataSlice_callback);


                     911 

00000380 e59cc000    912 	ldr	r12,[r12]

00000384 e1a0e00f    913 	mov	lr,pc

00000388 e12fff1c*   914 	bx	r12

0000038c e5840000    915 	str	r0,[r4]

00000390 e8bd8110    916 	ldmfd	[sp]!,{r4,r8,pc}

                     917 	.endf	DataSlice_setCallBack

                     918 	.align	4

                     919 

                     920 ;func	r12	param

                     921 

                     922 	.data

                     923 	.text

                     924 

                     925 ;234: }


                     926 	.align	4

                     927 	.align	4

                     928 dataSliceRelease::

                     929 ;71: {


                     930 

00000394 e12fff1e*   931 	ret	

                     932 	.endf	dataSliceRelease

                     933 	.align	4

                     934 

                     935 	.section ".bss","awb"

                     936 .L1038:

                     937 	.data

                     938 	.text

                     939 	.align	4

                     940 .L979:

00000398 00000000*   941 	.data.w	.L968

                     942 	.type	.L979,$object

                     943 	.size	.L979,4

                     944 

                     945 .L1012:

0000039c 00000000*   946 	.data.w	g_oldCallBack

                     947 	.type	.L1012,$object

                     948 	.size	.L1012,4

                     949 

                     950 .L1013:

000003a0 00000000*   951 	.data.w	DataSlice_callback

                     952 	.type	.L1013,$object

                     953 	.size	.L1013,4

                     954 

                     955 	.align	4

                     956 ;g_oldCallBack	g_oldCallBack	static

                     957 ;g_newCallBack	g_newCallBack	static

                     958 

                     959 	.data

                     960 	.comm	dataSliceIf,4,4

                     961 	.type	dataSliceIf,$object

                     962 	.size	dataSliceIf,4

                     963 	.comm	currentDataSlice,4,4

                     964 	.type	currentDataSlice,$object

                     965 	.size	currentDataSlice,4


                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b081.s
                     966 	.comm	dataSliceCopy,16384,4

                     967 	.type	dataSliceCopy,$object

                     968 	.size	dataSliceCopy,16384

                     969 	.comm	dataSliceSize,4,4

                     970 	.type	dataSliceSize,$object

                     971 	.size	dataSliceSize,4

                     972 	.comm	settsDataSlice,4,4

                     973 	.type	settsDataSlice,$object

                     974 	.size	settsDataSlice,4

                     975 	.comm	settsDataSliceSize,4,4

                     976 	.type	settsDataSliceSize,$object

                     977 	.size	settsDataSliceSize,4

                     978 	.ghsnote version,6

                     979 	.ghsnote tools,3

                     980 	.ghsnote options,0

                     981 	.text

                     982 	.align	4

                     983 	.section ".bss","awb"

                     984 	.align	4

                     985 	.text

