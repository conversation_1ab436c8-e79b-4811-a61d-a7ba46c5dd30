 #include "timers.h"
#include "Clib.h"
#include "string.h"
#include <hw/sama5dx.h>

//Проверка кодировки

//Для аппартного таймера GOOSE
#define TC_PERIOD	1000  // период таймера в мкс
#define TIMER_ID	(26)  // see at91core\core\timer\src\timer.asm
#define TC_TIMER_BASE (volatile unsigned long*)0xF0010080
#define AIC_BASE	0xFFFFF000
#define PMC_BASE	0xFFFFFC00
#define MCK	138000000

static STIMER g_softTimer;
static uint32_t tickCounter32 = 0;

// предыдущий обработчик аппаратного таймера (или dummyCallback)
void (*oldHWTmrIsrHandlerPtr)(unsigned int id) = NULL;

static void(*goose1msCallbackFunc)(void) = NULL;
static void(*integrity1msCallbackFunc)(void) = NULL;
static void(*netBusCheck1msCallbackFunc)(void) = NULL;

static void softTimerProc(void)
{    
    if (integrity1msCallbackFunc)
    {
        integrity1msCallbackFunc();
    }
	if (netBusCheck1msCallbackFunc)
	{
		netBusCheck1msCallbackFunc();
	}	
    tickCounter32++;
}

void Timers_setGoose1msCallBack(void(*func)(void))
{
    goose1msCallbackFunc = func;
}

void Timers_setIntegrity1msCallBack(void(*func)(void))
{
    integrity1msCallbackFunc = func;
}

void Timers_setNetBusChek1msCallback(void(*func)(void))
{
	netBusCheck1msCallbackFunc = func;
}

static void gooseTimerIsr(unsigned int id)
{
    volatile unsigned int *TC = (volatile unsigned int*)TC_TIMER_BASE;
    unsigned int status =  TC[TC_SR] & TC[TC_IMR];
    // если прерывание от нужного таймера
    if (status & TC_SR_CPCS)
    {
        // пользовательский обработчик
        if (goose1msCallbackFunc)
        {
            goose1msCallbackFunc();
        }
    }
    // вызываем следующий обработчик
    oldHWTmrIsrHandlerPtr(id);
}

static void initGOOSETimer(void)
{
    int prev = lockInterrupt();
    volatile unsigned long *TC = TC_TIMER_BASE;

    volatile unsigned int *AIC = (volatile unsigned int*)AIC_BASE;
    volatile unsigned int *PMC = (volatile unsigned int*)PMC_BASE;
    // старый обработчик
    GetIntCallBack(TIMER_ID,(void (**)())&oldHWTmrIsrHandlerPtr);
    // включаем таймер
    PMC[PMC_PCER0]=1 << TIMER_ID;
    AIC[AIC_SSR] = TIMER_ID;
    AIC[AIC_SMR] = (0<<5) | 3;
    AIC[AIC_IECR] = 1;


    SetIntCallBack(TIMER_ID,gooseTimerIsr);
    // Disable CLK
    TC[TC_CCR]=TC_CCR_CLKDIS;

    // Disable all timer interrupts
    TC[TC_IDR] = 0xFF;

    TC[TC_CMR] = TC_CMR_WAVE | TC_CMR_CPCTRG | TC_CMR_TIMER_CLOCK2;  // MCK/8


    TC[TC_SR]; // read status
    TC[TC_IER] = TC_IER_CPCS; // enable interrupt
    TC[TC_RC] = MCK/8/(1000000/TC_PERIOD);


    // Reset timer and enable
    TC[TC_CCR]=TC_CCR_SWTRG | TC_CCR_CLKEN;
    unlockInterrupt(prev);
}

void Timers_init(void)
{    
    memset(&g_softTimer, 0, sizeof(g_softTimer));
    CreateTimer(&g_softTimer);
    g_softTimer.TimerProc = softTimerProc;
    g_softTimer.AlarmTime = 1;
    g_softTimer.Precision = 1;
    g_softTimer.Started = 1;

    initGOOSETimer();
}

uint32_t Timers_getTickCount(void)
{
    return tickCounter32;
}

bool Timers_isTimeout(uint32_t startTime, uint32_t timeout)
{
    uint32_t tickCount = Timers_getTickCount();
    uint32_t timePassed;

    // сколько прошло времени с момента запуска
    if (startTime  <= tickCount)
    {
        timePassed = tickCount - startTime;
    }
    else // переполнение
    {
        timePassed = UINT32_MAX - startTime + tickCount;
    }
    return timePassed > timeout;
}

