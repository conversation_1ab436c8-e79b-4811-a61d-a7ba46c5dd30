#include "frsm.h"


FRSM g_FRSM;

//ID для одиночного FRSM
#define SOLITARY_FRSM_ID 0x55

static void frsm_lock()
{

}

static void frsm_unlock()
{

}

bool frsm_init(void)
{
    g_FRSM.busy = FALSE;
    return TRUE;
}

bool frsm_alloc(uint32_t* id)
{
    frsm_lock();
    if (g_FRSM.busy)
    {
        frsm_unlock();
        return FALSE;
    }
    *id = SOLITARY_FRSM_ID;
    g_FRSM.busy = TRUE;
    frsm_unlock();
    return TRUE;
}

bool frsm_free(uint32_t id)
{
    FRSM* frsm;
    frsm_lock();

    if (!frsm_getById(id, &frsm))
    {
        frsm_unlock();
        return FALSE;
    }

    frsm->busy = FALSE;
    frsm_unlock();
    return TRUE;
}

bool frsm_getById(uint32_t id, FRSM** frsm)
{
    if (id != SOLITARY_FRSM_ID)
    {
        return FALSE;
    }
    *frsm = &g_FRSM;
    return TRUE;
}
