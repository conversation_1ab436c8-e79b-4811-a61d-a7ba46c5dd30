#include <stdlib.h>
#include <string.h>
#include <types.h>
#include "../local_types.h"
#include "OscWriteBuffer.h"
#include "OscFiles.h"

bool OscWriteBuffer_create(OscWriteBuffer *wb, size_t size)
{
	void *p;

	if (wb->p) // p должно быть NULL для корректного удаления
	{
		return false;
	}

	p = OscFiles_malloc(size);
	if (!p)
	{
		return false;
	}

	wb->p = p;
	wb->data = p;
	wb->size = size;
	wb->dataSize = 0;
	wb->dataPos = 0;
	return true;
}

bool OscWriteBuffer_attach(OscWriteBuffer *wb, unsigned char *p, size_t size)
{
	wb->p = NULL;
	wb->data = p;
	wb->size = size;
	wb->dataSize = 0;
	wb->dataPos = 0;
	return true;
}

void OscWriteBuffer_reset(OscWriteBuffer *wb)
{
	wb->dataPos = 0;
	wb->dataSize = 0;
}

static size_t getFreeSize(OscWriteBuffer *wb)
{
	return wb->size - wb->dataSize - wb->dataPos;
}
bool OscWriteBuffer_write(OscWriteBuffer *wb, void *data, size_t size)
{
	size_t freeSize = getFreeSize(wb);
	// не хватает места
	if (freeSize < size)
	{
		void *p = wb->p;
		int newSize = wb->size * 2; // увеличивается в 2 раза
		if (p == NULL) // статический буфер, нельзя перевыделить
		{
			return false;
		}
	
		p = OscFiles_realloc(p, newSize);
		if (!p)
		{
			return false;
		}

		wb->p = p;
		wb->data = wb->p;
		wb->size = newSize;
	}


	memcpy(wb->data + wb->dataPos + wb->dataSize, data, size);
	wb->dataSize += size;
	return true;
}

bool OscWriteBuffer_toWriteBuffer(OscWriteBuffer *dst, OscWriteBuffer *src)
{
	unsigned char *data = OscWriteBuffer_data(src);
	size_t size = OscWriteBuffer_dataLen(src);
	bool result =  OscWriteBuffer_write(dst,data, size);
	if (result)
	{
		OscWriteBuffer_reset(src);
	}

	return result;
}

bool OscWriteBuffer_resize(OscWriteBuffer *wb, int len)
{
	size_t newLen = wb->dataSize + len;
	if (newLen + wb->dataPos > wb->size)
	{
		return false;
	}
	wb->dataSize += len;
	return true;
}

bool OscWriteBuffer_empty(OscWriteBuffer *wb) 
{
	return wb->dataSize == 0;
}

void * OscWriteBuffer_data(OscWriteBuffer *wb)
{
	return wb->data + wb->dataPos;
}

size_t OscWriteBuffer_dataLen(OscWriteBuffer *wb)
{
	return wb->dataSize;
}

size_t OscWriteBuffer_size(OscWriteBuffer *wb)
{
	return wb->size - wb->dataSize;
}

void OscWriteBuffer_destroy(OscWriteBuffer *wb)
{
	// буфер создан через выделение памяти
	if (wb->p)
	{
		OscFiles_free(wb->p);
		return;
	}
	// буфер создан OscWriteBuffer_attach
}

size_t OscWriteBuffer_toBufferView(BufferView *bv, OscWriteBuffer *wb)
{
	int writenSize;

	if (OscWriteBuffer_empty(wb))
	{
		return 0;
	}

	writenSize = BufferView_writeData(bv, OscWriteBuffer_data(wb),
		OscWriteBuffer_dataLen(wb));
	wb->dataPos += writenSize;
	wb->dataSize -= writenSize;

	// все записали
	if (wb->dataSize == 0)
	{
		OscWriteBuffer_reset(wb);
	}
	return writenSize;
}
