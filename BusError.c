#include "BusError.h"

#include "timers.h"
#include "netTools.h"
#include <debug.h>

// Выдержка времени в миллисекундах для выдачи ошибки шины
#define BUS_ERROR_TIMEOUT 5000

// Выдержка времени для признания ошибки шины
static volatile uint32_t _busErrorTimeOutCounter = 0;

static void busCheckCallBack(void)
{
	if(NetTools_busOK())
	{
		if(_busErrorTimeOutCounter)
		{
			TRACE("Bus OK");
		}

		_busErrorTimeOutCounter = 0;
	}
	else if(_busErrorTimeOutCounter < BUS_ERROR_TIMEOUT)
	{
		_busErrorTimeOutCounter++;

		if(_busErrorTimeOutCounter == BUS_ERROR_TIMEOUT)
		{
			TRACE("Bus error set");
		}
	}
}

void BusError_init(void)
{
	Timers_setNetBusChek1msCallback(busCheckCallBack);
}

bool BusError_check(void)
{
	bool result = _busErrorTimeOutCounter < BUS_ERROR_TIMEOUT;

	return result;
}
