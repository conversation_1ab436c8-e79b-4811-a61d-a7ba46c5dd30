                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fg01.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=MemoryManager.c -o gh_fg01.o -list=MemoryManager.lst C:\Users\<USER>\AppData\Local\Temp\gh_fg01.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_fg01.s
Source File: MemoryManager.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      10 ;		MemoryManager.c -o MemoryManager.o

                      11 ;Source File:   MemoryManager.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Oct 07 09:39:27 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "MemoryManager.h"


                      21 ;2: #include <stdint.h>


                      22 ;3: #include <stdlib.h>


                      23 ;4: 


                      24 ;5: //Комментарий для проверки кодировки


                      25 ;6: 


                      26 ;7: #define MEMORY_SIZE (2048 * 1024)


                      27 ;8: 


                      28 ;9: static uint8_t* g_memory = NULL;


                      29 ;10: static size_t g_pos = 0;


                      30 ;11: 


                      31 ;12: bool MM_init(void)


                      32 ;13: {


                      33 ;14:     g_pos = 0;


                      34 ;15:     g_memory = malloc(MEMORY_SIZE);


                      35 ;16:     if(g_memory != NULL)


                      36 ;17:     {


                      37 ;18: 


                      38 ;19:         return true;


                      39 ;20:     }


                      40 ;21:     else


                      41 ;22:     {


                      42 ;23:         return false;


                      43 ;24:     }


                      44 ;25: }


                      45 ;26: 


                      46 ;27: void* MM_alloc(size_t size)


                      47 ;28: {


                      48 ;29:     void* allocated;


                      49 ;30:     if (g_memory == NULL)


                      50 ;31:     {



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fg01.s
                      51 ;32:         return NULL;


                      52 ;33:     }


                      53 ;34:     //Округляем в большую сторону до 4


                      54 ;35:     if ((size & 3) != 0)


                      55 ;36:     {


                      56 ;37:         size += 4 - (size & 3);


                      57 ;38:     }


                      58 ;39: 


                      59 ;40:     if (g_pos + size > MEMORY_SIZE)


                      60 ;41:     {


                      61 ;42:         return false;


                      62 ;43:     }


                      63 ;44:     allocated = g_memory + g_pos;


                      64 ;45:     g_pos += size;


                      65 ;46:     return allocated;


                      66 ;47: }


                      67 ;48: 


                      68 ;49: size_t MM_getAllocated(void)


                      69 

                      70 ;52: }


                      71 

                      72 	.text

                      73 	.align	4

                      74 MM_init::

00000000 e92d4000     75 	stmfd	[sp]!,{lr}

00000004 e59f1078*    76 	ldr	r1,.L62

00000008 e3a00000     77 	mov	r0,0

0000000c e5810000     78 	str	r0,[r1]

00000010 e3a00980     79 	mov	r0,1<<21

00000014 eb000000*    80 	bl	malloc

00000018 e59f1068*    81 	ldr	r1,.L63

0000001c e3500000     82 	cmp	r0,0

00000020 e5810000     83 	str	r0,[r1]

00000024 13a00001     84 	movne	r0,1

00000028 e8bd8000     85 	ldmfd	[sp]!,{pc}

                      86 	.endf	MM_init

                      87 	.align	4

                      88 

                      89 	.section ".bss","awb"

                      90 .L53:

                      91 	.data

                      92 .L54:

00000000 00000000     93 g_memory:	.data.b	0,0,0,0

                      94 	.type	g_memory,$object

                      95 	.size	g_memory,4

                      96 .L55:

00000004 00000000     97 g_pos:	.data.b	0,0,0,0

                      98 	.type	g_pos,$object

                      99 	.size	g_pos,4

                     100 	.text

                     101 

                     102 

                     103 	.align	4

                     104 	.align	4

                     105 MM_alloc::

0000002c e59fc054*   106 	ldr	r12,.L63

00000030 e59c3000    107 	ldr	r3,[r12]

00000034 e3530000    108 	cmp	r3,0

00000038 0a000008    109 	beq	.L72

0000003c e3100003    110 	tst	r0,3

00000040 13c00003    111 	bicne	r0,r0,3


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fg01.s
00000044 e59c1004    112 	ldr	r1,[r12,4]

00000048 12800004    113 	addne	r0,r0,4

0000004c e0812000    114 	add	r2,r1,r0

00000050 e3520980    115 	cmp	r2,1<<21

00000054 90810003    116 	addls	r0,r1,r3

00000058 958c2004    117 	strls	r2,[r12,4]

0000005c 9a000000    118 	bls	.L64

                     119 .L72:

00000060 e3a00000    120 	mov	r0,0

                     121 .L64:

00000064 e12fff1e*   122 	ret	

                     123 	.endf	MM_alloc

                     124 	.align	4

                     125 

                     126 ;size	r0	param

                     127 

                     128 	.data

                     129 	.text

                     130 

                     131 

                     132 ;53: 


                     133 ;54: size_t MM_getRemaining(void)


                     134 	.align	4

                     135 	.align	4

                     136 MM_getRemaining::

                     137 ;55: {


                     138 

                     139 ;56:     return MEMORY_SIZE - g_pos;


                     140 

00000068 e59fc014*   141 	ldr	r12,.L62

0000006c e59c0000    142 	ldr	r0,[r12]

00000070 e2600980    143 	rsb	r0,r0,1<<21

00000074 e12fff1e*   144 	ret	

                     145 	.endf	MM_getRemaining

                     146 	.align	4

                     147 

                     148 	.data

                     149 	.text

                     150 

                     151 ;57: }


                     152 	.align	4

                     153 	.align	4

                     154 MM_getAllocated::

                     155 ;50: {


                     156 

                     157 ;51:     return g_pos;


                     158 

00000078 e59fc004*   159 	ldr	r12,.L62

0000007c e59c0000    160 	ldr	r0,[r12]

00000080 e12fff1e*   161 	ret	

                     162 	.endf	MM_getAllocated

                     163 	.align	4

                     164 

                     165 	.data

                     166 	.text

                     167 	.align	4

                     168 .L62:

00000084 00000000*   169 	.data.w	.L55

                     170 	.type	.L62,$object

                     171 	.size	.L62,4

                     172 


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fg01.s
                     173 .L63:

00000088 00000000*   174 	.data.w	.L54

                     175 	.type	.L63,$object

                     176 	.size	.L63,4

                     177 

                     178 	.align	4

                     179 ;g_memory	.L54	static

                     180 ;g_pos	.L55	static

                     181 

                     182 	.data

                     183 	.ghsnote version,6

                     184 	.ghsnote tools,3

                     185 	.ghsnote options,0

                     186 	.text

                     187 	.align	4

                     188 	.data

                     189 	.align	4

                     190 	.text

