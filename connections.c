#include "connections.h"

#include "mms.h"

#include <stddef.h>
#include <stdlib.h>
#include <string.h>

#include <debug.h>

IsoConnection* allocateConnection(void)
{
	IsoConnection* allocated = malloc(sizeof(IsoConnection));
	if (allocated == NULL)
	{
		return NULL;
	}
	memset(allocated, 0, sizeof(IsoConnection));
	return allocated;
}

void freeConnection(IsoConnection* conn)
{
	TRACE("Free connection memory");
	free(conn);
}

