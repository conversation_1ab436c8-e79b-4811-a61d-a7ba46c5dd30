#pragma once
#include <types.h>
#include <stdbool.h>
#include "../bufView.h"
#include "OscWriteBuffer.h"
#include "../../../../OscDescrMake/OscDescr.h"
typedef struct OSCInfoStruct OSCInfoStruct;

typedef struct OSCSampleBuffer OSCSampleBuffer;

bool OSCInfo_init(void);

//! передается буфер выделенный OSCInfo_lockHeaderBuf
OSCInfoStruct* OSCInfo_create(OscWriteBuffer *headBufferView);
void OSCInfo_destroy(OSCInfoStruct *oscInfo);

//! возвращает указатель на статический буфер для чтения заголовка осцилограммы
OscWriteBuffer *OSCInfo_lockHeaderBuf(void);
void OSCInfo_unlockHeaderBuf(void);

//! время осцилограммы
unsigned long OSCInfo_getUTCDate(OSCInfoStruct *oscInfo);
unsigned long OSCInfo_getDateMS(OSCInfoStruct *oscInfo);
//! версия
unsigned long OSCInfo_getOscVersion(OSCInfoStruct *oscInfo);

unsigned long OSCInfo_getADCClkFreq(OSCInfoStruct *oscInfo);
unsigned long OSCInfo_getPrehistFrameCount(OSCInfoStruct *oscInfo);
unsigned long OSCInfo_getPrehistFirstFrameNum(OSCInfoStruct *oscInfo);
unsigned long OSCInfo_getPointPerFrameCount(OSCInfoStruct *oscInfo);
unsigned long OSCInfo_getAnalogCount(OSCInfoStruct *oscInfo);
unsigned long OSCInfo_getBoolCount(OSCInfoStruct *oscInfo);
unsigned long OSCInfo_getHeaderSize(OSCInfoStruct *oscInfo);

//! количество фреймов, включая фреймы предыстории
unsigned long OSCInfo_getFrameCount(OSCInfoStruct *oscInfo);
unsigned int OSCInfo_getFrameOffset(OSCInfoStruct *oscInfo, unsigned int frameNum);

int OSCInfo_getADCSampleSize(OSCInfoStruct *oscInfo);
int OSCInfo_getADCFractionSize(OSCInfoStruct *oscInfo);

//! буфер для чтения состава
OscWriteBuffer * OSCInfo_getBufferContent(OSCInfoStruct *oscInfo);
//! буфер для чтения фрейма
OscWriteBuffer *OSCInfo_getFrameBuffer(OSCInfoStruct *oscInfo);

//! инициализация состава осчилограммы
bool OSCInfo_initContent(OSCInfoStruct *oscInfo);

//! получение указателя на элемент осцилограммы по индексу 
//! максимальные значения индекса возвращают OSCInfo_getAnalogCount,OSCInfo_getBoolCount
OscDescrAnalog *OSCInfo_getAnalog(OSCInfoStruct *oscInfo, int index);
OscDescrBool *OSCInfo_getBool(OSCInfoStruct *oscInfo, int index);

//! коэффициенты аналогового канала
float OSCInfo_getAnalogCft(OSCInfoStruct *oscInfo, int index);
//! максимальное и минимальное значение
int OSCInfo_getAnalogMax(OSCInfoStruct *oscInfo, int index);
int OSCInfo_getAnalogMin(OSCInfoStruct *oscInfo, int index);


// период текущего фреймя в тиках АЦП
int OSCFrame_getADCPeriod(OSCInfoStruct *oscInfo);
//! аналоговое значение в dat файл
int OSCFrame_getAnalogValue(OSCInfoStruct *oscInfo, unsigned int pointNum, unsigned int analogNum);
//! дискретное значение в dat файл
int OSCFrame_getBoolValue(OSCInfoStruct *oscInfo, int sampleNum, int boolNum);
//! время текущего отсчета в us
bool OSCFrame_getTick(OSCInfoStruct *oscInfo, double *tick);
bool OSCFrame_getFreq(OSCInfoStruct *oscInfo, float *freq);








