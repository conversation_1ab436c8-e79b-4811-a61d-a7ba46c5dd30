                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=OscInfo.c -o fs\gh_9o01.o -list=fs/OscInfo.lst C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
Source File: OscInfo.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile fs/OscInfo.c

                      10 ;		-o fs/OscInfo.o

                      11 ;Source File:   fs/OscInfo.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Oct 07 09:39:30 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include <stdlib.h>


                      21 ;2: #include <string.h>


                      22 ;3: #include <stdint.h>


                      23 ;4: #include <stdbool.h>


                      24 ;5: 


                      25 ;6: #include "OscInfo.h"


                      26 ;7: #include "../bufView.h"


                      27 ;8: #include "platform_critical_section.h"


                      28 ;9: #include "OscFiles.h"


                      29 ;10: 


                      30 ;11: // заголовоки осцилограмм разных версий


                      31 ;12: #ifndef STATIC_ASSERT


                      32 ;13: #define STATIC_ASSERT(e) typedef char __C_ASSERT__[(e)?1:-1]


                      33 ;14: #endif


                      34 ;15: 


                      35 ;16: 


                      36 ;17: OscWriteBuffer oscHeaderBuf;


                      37 ;18: #pragma alignvar (4)


                      38 ;19: CriticalSection csHeaderBuf;


                      39 ;20: 


                      40 ;21: #define OSC_HEADER_SIZE offsetof(OSCInfoStruct, iface)


                      41 ;22: 


                      42 ;23: 


                      43 ;24: // NOTE: копии этих структур храняться в мониторе и соответсвующих программах log


                      44 ;25: typedef struct OSCInfoStruct3 OSCInfoStruct3;


                      45 ;26: struct OSCInfoStruct3


                      46 ;27: {


                      47 ;28: 	//! Размер осцилограммы в байтах. Размер:4	    


                      48 ;29: 	unsigned long size;


                      49 ;30: 	//! Абсолютный порядковый номер. Размер:4			    


                      50 ;31: 	unsigned long absNumber;



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
                      51 ;32: 	//! SizeCRC - контрольная сумма размера. Размер: 2


                      52 ;33: 	unsigned short sizeCRC;


                      53 ;34: 	//! количество кадров предыстории размер: 1		    


                      54 ;35: 	unsigned char prehistCount;


                      55 ;36: 	//! номер первого кадра предыстории размер: 1		    


                      56 ;37: 	unsigned char prehistStart;


                      57 ;38: 	//! Длительность осциллограммы в миллисекундах. Размер:4	    


                      58 ;39: 	unsigned long duration;


                      59 ;40: 	//! Тактовая частота АЦП в герцах. Размер:4		    


                      60 ;41: 	unsigned long adcClkFreq;


                      61 ;42: 	//! Дата (UTC) размер: 4					    


                      62 ;43: 	unsigned long time;


                      63 ;44: 	//! миллисекунды (Дополнение к дате, 0-999) размер: 2	    


                      64 ;45: 	unsigned short time_ms;


                      65 ;46: 	//! неиспользуемые байты 


                      66 ;47: 	unsigned char oscVersionUnusedByte;


                      67 ;48: 	//! Версия осциллографа 1 байта


                      68 ;49: 	unsigned char oscVersion;


                      69 ;50: 


                      70 ;51: 	//! колличество отсчетов на кадр


                      71 ;52: 	unsigned short pointPerFrame;


                      72 ;53: 	unsigned short reservedField;


                      73 ;54: 	//! число аналоговых каналов в осциллограмме


                      74 ;55: 	unsigned short ainCount;


                      75 ;56: 	//! число дискретных каналов в осциллограмме


                      76 ;57: 	unsigned short dinCount;


                      77 ;58: 


                      78 ;59: };


                      79 ;60: 


                      80 ;61: typedef struct OSCInfoStruct4 OSCInfoStruct4;


                      81 ;62: struct  OSCInfoStruct4


                      82 ;63: {


                      83 ;64: 	//! Размер осцилограммы в байтах. Размер:4	    


                      84 ;65: 	unsigned long size;


                      85 ;66: 	//! Абсолютный порядковый номер. Размер:4			    


                      86 ;67: 	unsigned long absNumber;


                      87 ;68: 	//! SizeCRC - контрольная сумма размера. Размер: 2


                      88 ;69: 	unsigned short sizeCRC;


                      89 ;70: 	unsigned short reservedField;


                      90 ;71: 	//! Длительность осциллограммы в миллисекундах. Размер:4	    


                      91 ;72: 	unsigned long duration;


                      92 ;73: 	//! Тактовая частота АЦП в герцах. Размер:4		    


                      93 ;74: 	unsigned long adcClkFreq;


                      94 ;75: 	//! Дата (UTC) размер: 4		


                      95 ;76: 	unsigned long time;


                      96 ;77: 	//! миллисекунды (Дополнение к дате, 0-999) размер: 2	    


                      97 ;78: 	unsigned short time_ms;


                      98 ;79: 


                      99 ;80: 	unsigned char oscVersionUnusedByte;


                     100 ;81: 	//! Версия осциллографа 1 байта


                     101 ;82: 	unsigned char oscVersion;


                     102 ;83: 


                     103 ;84: 	//! количество кадров предыстории размер: 4		    


                     104 ;85: 	unsigned long prehist_count;


                     105 ;86: 	//! номер первого кадра предыстории размер: 4	


                     106 ;87: 	unsigned long prehistStart;


                     107 ;88: 	//! колличество отсчетов на кадр


                     108 ;89: 	unsigned short pointPerFrame;


                     109 ;90: 	//! Размер одной выборки АЦП в байтах


                     110 ;91: 	unsigned char adcSampleSize;


                     111 ;92: 	//! Количество бит после фиксированной точки в выборке АЦП,



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
                     112 ;93: 	//! включая незначащие биты.


                     113 ;94: 	//! (значение выборки AЦП надо поделить на 2 в этой степени)	


                     114 ;95: 	unsigned char adcFractionSize;


                     115 ;96: 	//! число аналоговых каналов в осциллограмме


                     116 ;97: 	unsigned short ainCount;


                     117 ;98: 	//! число дискретных каналов в осциллограмме


                     118 ;99: 	unsigned short dinCount;


                     119 ;100: };


                     120 ;101: 


                     121 ;102: // интерфейс


                     122 ;103: typedef struct IOSCInfo IOSCInfo;


                     123 ;104: struct IOSCInfo


                     124 ;105: {


                     125 ;106: 	// одинаковые для v3 и v4


                     126 ;107: 	unsigned long(*getUTCDate)(OSCInfoStruct *oscInfo);


                     127 ;108: 	unsigned long(*getDateMS)(OSCInfoStruct *oscInfo);


                     128 ;109: 	unsigned long(*getOscVersion)(OSCInfoStruct *oscInfo);


                     129 ;110: 	unsigned long(*getADCClkFreq)(OSCInfoStruct *oscInfo);


                     130 ;111: 	unsigned long(*getPrehistFrameCount)(OSCInfoStruct *oscInfo);


                     131 ;112: 	unsigned long(*getPrehistFirstFrameNum)(OSCInfoStruct *oscInfo);


                     132 ;113: 	unsigned long(*getPointPerFrameCount)(OSCInfoStruct *oscInfo);


                     133 ;114: 	unsigned long(*getAnalogInCount)(OSCInfoStruct *oscInfo);


                     134 ;115: 	unsigned long(*getDigInCount)(OSCInfoStruct *oscInfo);


                     135 ;116: 	unsigned int(*getOscSize)(OSCInfoStruct *oscInfo);


                     136 ;117: 	// разные для v3 и v4


                     137 ;118: 	unsigned long(*getHeaderSize)(OSCInfoStruct *oscInfo);


                     138 ;119: 	int(*getADCSampleSize)(OSCInfoStruct *oscInfo);


                     139 ;120: 	int(*getADCFractionSize)(OSCInfoStruct *oscInfo);


                     140 ;121: };


                     141 ;122: 


                     142 ;123: typedef struct OSCInfoAnalog OSCInfoAnalog;


                     143 ;124: struct OSCInfoAnalog


                     144 ;125: {


                     145 ;126: 	OscDescrAnalog *pDescr;


                     146 ;127: 	float cft;


                     147 ;128: 	int maxValue;


                     148 ;129: 	int minValue;


                     149 ;130: };


                     150 ;131: 


                     151 ;132: typedef struct OSCInfoBool OSCInfoBool;


                     152 ;133: struct OSCInfoBool


                     153 ;134: {


                     154 ;135: 	OscDescrBool *pDescr;


                     155 ;136: };


                     156 ;137: //! класс


                     157 ;138: struct  OSCInfoStruct


                     158 ;139: {


                     159 ;140: 	// должны идти первыми


                     160 ;141: 	union 


                     161 ;142: 	{


                     162 ;143: 		OSCInfoStruct3 v3;


                     163 ;144: 		OSCInfoStruct4 v4;


                     164 ;145: 	};


                     165 ;146: 	IOSCInfo *iface;


                     166 ;147: 	OscWriteBuffer wbContent;


                     167 ;148: 	OscWriteBuffer wbFrame;


                     168 ;149: 	OSCInfoAnalog *pAnalog;


                     169 ;150: 	OSCInfoBool *pBool;


                     170 ;151: 	// количество фреймов в осцилограмме, включая предысторию


                     171 ;152: 	unsigned int frameCount; 


                     172 ;153: 	unsigned int frameSize;



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
                     173 ;154: 


                     174 ;155: 	unsigned int firstFrameOffset;


                     175 ;156: };


                     176 ;157: 


                     177 ;158: 


                     178 ;159: static unsigned long getUTCDate(OSCInfoStruct *oscInfo)


                     179 ;160: {


                     180 ;161: 	return oscInfo->v3.time;


                     181 ;162: }


                     182 ;163: static unsigned long getDateMS(OSCInfoStruct *oscInfo)


                     183 ;164: {


                     184 ;165: 	return oscInfo->v3.time_ms;


                     185 ;166: }


                     186 ;167: static unsigned long getOscVersion(OSCInfoStruct *oscInfo)


                     187 ;168: {


                     188 ;169: 	return oscInfo->v3.oscVersion;


                     189 ;170: }


                     190 ;171: static unsigned long getADCClkFreq(OSCInfoStruct *oscInfo)


                     191 ;172: {


                     192 ;173: 	return oscInfo->v3.adcClkFreq;


                     193 ;174: }


                     194 ;175: static unsigned long getPrehistFrameCount3(OSCInfoStruct *oscInfo)


                     195 ;176: {


                     196 ;177: 	return oscInfo->v3.prehistCount;


                     197 ;178: }


                     198 ;179: 


                     199 ;180: static unsigned long getPrehistFirstFrameNum3(OSCInfoStruct *oscInfo)


                     200 ;181: {


                     201 ;182: 	return oscInfo->v3.prehistStart;


                     202 ;183: }


                     203 ;184: 


                     204 ;185: static unsigned long getPrehistFrameCount4(OSCInfoStruct *oscInfo)


                     205 ;186: {


                     206 ;187: 	return oscInfo->v4.prehist_count;


                     207 ;188: }


                     208 ;189: 


                     209 ;190: static unsigned long getPrehistFirstFrameNum4(OSCInfoStruct *oscInfo)


                     210 ;191: {


                     211 ;192: 	return oscInfo->v4.prehistStart;


                     212 ;193: }


                     213 ;194: 


                     214 ;195: 


                     215 ;196: static unsigned long getPointPerFrameCount3(OSCInfoStruct *oscInfo)


                     216 ;197: {


                     217 ;198: 	return oscInfo->v3.pointPerFrame;


                     218 ;199: }


                     219 ;200: 


                     220 ;201: static unsigned long getPointPerFrameCount4(OSCInfoStruct *oscInfo)


                     221 ;202: {


                     222 ;203: 	return oscInfo->v4.pointPerFrame;


                     223 ;204: }


                     224 ;205: 


                     225 ;206: 


                     226 ;207: static unsigned long getAnalogInCount3(OSCInfoStruct *oscInfo)


                     227 ;208: {


                     228 ;209: 	return oscInfo->v3.ainCount;


                     229 ;210: }


                     230 ;211: static unsigned long getDigInCount3(OSCInfoStruct *oscInfo)


                     231 ;212: {


                     232 ;213: 	return oscInfo->v3.dinCount;


                     233 ;214: }



                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
                     234 ;215: 


                     235 ;216: static unsigned long getAnalogInCount4(OSCInfoStruct *oscInfo)


                     236 ;217: {


                     237 ;218: 	return oscInfo->v4.ainCount;


                     238 ;219: }


                     239 ;220: static unsigned long getDigInCount4(OSCInfoStruct *oscInfo)


                     240 ;221: {


                     241 ;222: 	return oscInfo->v4.dinCount;


                     242 ;223: }


                     243 ;224: 


                     244 ;225: static unsigned long getHeaderSize3(OSCInfoStruct *oscInfo)


                     245 

                     246 ;228: }


                     247 

                     248 ;229: static unsigned long getHeaderSize4(OSCInfoStruct *oscInfo)


                     249 

                     250 ;232: }


                     251 

                     252 ;233: 


                     253 ;234: 


                     254 ;235: static int getADCSampleSize3(OSCInfoStruct *oscInfo)


                     255 

                     256 ;238: }


                     257 

                     258 ;239: static int getADCFractionSize3(OSCInfoStruct *oscInfo)


                     259 

                     260 ;242: }


                     261 

                     262 ;243: 


                     263 ;244: static int getADCSampleSize4(OSCInfoStruct *oscInfo)


                     264 ;245: {


                     265 ;246: 	int adcSampleSize = oscInfo->v4.adcSampleSize;


                     266 ;247: 	if (adcSampleSize == 0)


                     267 ;248: 	{


                     268 ;249: 		//На случай кривого формата (коммент из монитора)


                     269 ;250: 		adcSampleSize = 4;


                     270 ;251: 	}


                     271 ;252: 	return adcSampleSize;


                     272 ;253: }


                     273 ;254: static int getADCFractionSize4(OSCInfoStruct *oscInfo)


                     274 ;255: {


                     275 ;256: 	return oscInfo->v4.adcFractionSize;


                     276 ;257: }


                     277 ;258: 


                     278 ;259: static int getAnalogContentIdSize(OSCInfoStruct *oscInfo)


                     279 

                     280 ;262: }


                     281 

                     282 ;263: static int getBoolContentIdSize(OSCInfoStruct *oscInfo)


                     283 

                     284 ;266: }


                     285 

                     286 ;267: static unsigned int getOscSize(OSCInfoStruct *oscInfo)


                     287 ;268: {


                     288 ;269: 	return oscInfo->v3.size;


                     289 ;270: }


                     290 ;271: // инициализация интерфейсов


                     291 ;272: IOSCInfo oscInfoV3 = 


                     292 ;273: {


                     293 ;274: 	getUTCDate,


                     294 ;275: 	getDateMS,



                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
                     295 ;276: 	getOscVersion,


                     296 ;277: 	getADCClkFreq,


                     297 ;278: 	getPrehistFrameCount3,


                     298 ;279: 	getPrehistFirstFrameNum3,


                     299 ;280: 	getPointPerFrameCount3,


                     300 ;281: 	getAnalogInCount3,


                     301 ;282: 	getDigInCount3,


                     302 ;283: 	getOscSize,


                     303 ;284: 	getHeaderSize3,


                     304 ;285: 	getADCSampleSize3,


                     305 ;286: 	getADCFractionSize3,


                     306 ;287: };


                     307 ;288: IOSCInfo oscInfoV4 =


                     308 ;289: {


                     309 ;290: 	getUTCDate,


                     310 ;291: 	getDateMS,


                     311 ;292: 	getOscVersion,


                     312 ;293: 	getADCClkFreq,


                     313 ;294: 	getPrehistFrameCount4,


                     314 ;295: 	getPrehistFirstFrameNum4,


                     315 ;296: 	getPointPerFrameCount4,


                     316 ;297: 	getAnalogInCount4,


                     317 ;298: 	getDigInCount4,


                     318 ;299: 	getOscSize,


                     319 ;300: 	getHeaderSize4,


                     320 ;301: 	getADCSampleSize4,


                     321 ;302: 	getADCFractionSize4


                     322 ;303: };


                     323 ;304: 


                     324 ;305: bool OSCInfo_init(void)


                     325 ;306: {	


                     326 ;307: 	#pragma alignvar (4)


                     327 ;308: 	static unsigned char oscHeader[OSC_HEADER_SIZE];


                     328 ;309: 	if (!OscWriteBuffer_attach(&oscHeaderBuf, oscHeader, OSC_HEADER_SIZE))


                     329 ;310: 	{


                     330 ;311: 		return false;


                     331 ;312: 	}


                     332 ;313: 


                     333 ;314: 	CriticalSection_Init(&csHeaderBuf);


                     334 ;315: 	return true;


                     335 ;316: }


                     336 ;317: 


                     337 ;318: static unsigned int getAdcPeriodSize(OSCInfoStruct *oscInfo)


                     338 

                     339 ;321: }


                     340 

                     341 ;322: 


                     342 ;323: static unsigned int getFrameSize(OSCInfoStruct *oscInfo)


                     343 ;324: {


                     344 ;325: 	int pointsPerFrame = OSCInfo_getPointPerFrameCount(oscInfo);


                     345 ;326: 	int analogCount = OSCInfo_getAnalogCount(oscInfo);


                     346 ;327: 	int boolCount = OSCInfo_getBoolCount(oscInfo);


                     347 ;328: 	int adcPeriodSize = getAdcPeriodSize(oscInfo);


                     348 ;329: 	int adcSampleSize = OSCInfo_getADCSampleSize(oscInfo);


                     349 ;330: 	if (adcSampleSize == 0)


                     350 ;331: 	{


                     351 ;332: 		return 0;


                     352 ;333: 	}


                     353 ;334: 	if (pointsPerFrame == 0)


                     354 ;335: 	{


                     355 ;336: 		return 0;



                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
                     356 ;337: 	}


                     357 ;338: 	if (adcPeriodSize == 0)


                     358 ;339: 	{


                     359 ;340: 		return 0;


                     360 ;341: 	}


                     361 ;342: 


                     362 ;343: 


                     363 ;344: 	return adcPeriodSize +


                     364 ;345: 		pointsPerFrame * adcSampleSize*analogCount


                     365 ;346: 		+ 1 * boolCount;


                     366 ;347: }


                     367 ;348: 


                     368 ;349: static unsigned int getContentInfoSize(OSCInfoStruct *oscInfo)


                     369 ;350: {


                     370 ;351: 	int analogCount;


                     371 ;352: 	int boolCount;


                     372 ;353: 	analogCount = OSCInfo_getAnalogCount(oscInfo);


                     373 ;354: 	boolCount = OSCInfo_getBoolCount(oscInfo);


                     374 ;355: 	


                     375 ;356: 	return analogCount*getAnalogContentIdSize(oscInfo)


                     376 ;357: 		+ boolCount * getBoolContentIdSize(oscInfo);


                     377 ;358: 


                     378 ;359: }


                     379 ;360: static unsigned int getFrameCount(OSCInfoStruct *oscInfo)


                     380 

                     381 ;376: }


                     382 

                     383 ;377: OSCInfoStruct* OSCInfo_create(OscWriteBuffer *headBufferView)


                     384 ;378: {


                     385 ;379: 	OSCInfoStruct *oscHeader = (OSCInfoStruct*)OscWriteBuffer_data(headBufferView);


                     386 ;380: 	OSCInfoStruct *oscInfo;


                     387 ;381: 	IOSCInfo *iface;


                     388 ;382: 	int contentSize;


                     389 ;383: 	int analogCount;


                     390 ;384: 	int boolCount;


                     391 ;385: 	


                     392 ;386: 	if (oscHeader->v3.oscVersion == 3 )


                     393 ;387: 	{


                     394 ;388: 		iface = &oscInfoV3;


                     395 ;389: 	}


                     396 ;390: 	else if (oscHeader->v4.oscVersion == 4)


                     397 ;391: 	{


                     398 ;392: 		iface = &oscInfoV4;


                     399 ;393: 	}


                     400 ;394: 	else


                     401 ;395: 	{


                     402 ;396: 		return NULL;


                     403 ;397: 	}


                     404 ;398: 


                     405 ;399: 	// память


                     406 ;400: 	oscInfo = OscFiles_malloc(sizeof(OSCInfoStruct));


                     407 ;401: 	if (!oscInfo)


                     408 ;402: 	{


                     409 ;403: 		return NULL;


                     410 ;404: 	}


                     411 ;405: 	


                     412 ;406: 	memset(oscInfo, 0, sizeof(OSCInfoStruct));


                     413 ;407: 


                     414 ;408: 	// инициализация заголовка


                     415 ;409: 	memcpy(oscInfo, oscHeader, OSC_HEADER_SIZE);


                     416 ;410: 	// интерфейс и приватные поля



                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
                     417 ;411: 	oscInfo->iface = iface;


                     418 ;412: 	oscInfo->pAnalog = NULL;


                     419 ;413: 	oscInfo->pBool = NULL;


                     420 ;414: 


                     421 ;415: 


                     422 ;416: 	analogCount = OSCInfo_getAnalogCount(oscInfo);


                     423 ;417: 	// не может быть без аналоговых каналов


                     424 ;418: 	if (analogCount == 0)


                     425 ;419: 	{


                     426 ;420: 		OSCInfo_destroy(oscInfo);


                     427 ;421: 		return NULL;


                     428 ;422: 	}


                     429 ;423: 	oscInfo->pAnalog = OscFiles_malloc(analogCount * sizeof(OSCInfoAnalog));


                     430 ;424: 	if (!oscInfo->pAnalog)


                     431 ;425: 	{


                     432 ;426: 		OSCInfo_destroy(oscInfo);


                     433 ;427: 		return NULL;


                     434 ;428: 	}


                     435 ;429: 


                     436 ;430: 


                     437 ;431: 	boolCount = OSCInfo_getBoolCount(oscInfo);


                     438 ;432: 	if (boolCount != 0)


                     439 ;433: 	{


                     440 ;434: 		oscInfo->pBool = OscFiles_malloc(boolCount * sizeof(OSCInfoBool));


                     441 ;435: 		if (!oscInfo->pBool)


                     442 ;436: 		{


                     443 ;437: 			OSCInfo_destroy(oscInfo);


                     444 ;438: 			return NULL;


                     445 ;439: 		}


                     446 ;440: 	}


                     447 ;441: 


                     448 ;442: 


                     449 ;443: 	contentSize = getContentInfoSize(oscInfo);


                     450 ;444: 	if (contentSize == 0)


                     451 ;445: 	{


                     452 ;446: 		OSCInfo_destroy(oscInfo);


                     453 ;447: 		return NULL;


                     454 ;448: 	}


                     455 ;449: 


                     456 ;450: 	// инициализация количества кадров


                     457 ;451: 	oscInfo->frameCount = getFrameCount(oscInfo);


                     458 ;452: 	if (oscInfo->frameCount == 0)


                     459 ;453: 	{


                     460 ;454: 		OSCInfo_destroy(oscInfo);


                     461 ;455: 		return FALSE;


                     462 ;456: 	}


                     463 ;457: 


                     464 ;458: 	oscInfo->firstFrameOffset = OSCInfo_getHeaderSize(oscInfo) +


                     465 ;459: 		contentSize;


                     466 ;460: 


                     467 ;461: 	// буфер под состав осцилограммы


                     468 ;462: 	if (!OscWriteBuffer_create(&oscInfo->wbContent, contentSize))


                     469 ;463: 	{


                     470 ;464: 		OSCInfo_destroy(oscInfo);


                     471 ;465: 		return NULL;


                     472 ;466: 	}


                     473 ;467: 	


                     474 ;468: 	// буфер под фрейм


                     475 ;469: 	oscInfo->frameSize = getFrameSize(oscInfo);


                     476 ;470: 	if (oscInfo->frameSize == 0)


                     477 ;471: 	{



                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
                     478 ;472: 		OSCInfo_destroy(oscInfo);


                     479 ;473: 		return NULL;


                     480 ;474: 	}


                     481 ;475: 


                     482 ;476: 	if (!OscWriteBuffer_create(&oscInfo->wbFrame, oscInfo->frameSize))


                     483 ;477: 	{


                     484 ;478: 		OSCInfo_destroy(oscInfo);


                     485 ;479: 		return NULL;


                     486 ;480: 	}


                     487 ;481: 


                     488 ;482: 	return oscInfo;


                     489 ;483: }


                     490 ;484: 


                     491 ;485: void OSCInfo_destroy(OSCInfoStruct *oscInfo)


                     492 ;486: {


                     493 ;487: 	if (oscInfo->pAnalog)


                     494 ;488: 	{


                     495 ;489: 		OscFiles_free(oscInfo->pAnalog);


                     496 ;490: 	}


                     497 ;491: 	if (oscInfo->pBool)


                     498 ;492: 	{


                     499 ;493: 		OscFiles_free(oscInfo->pBool);


                     500 ;494: 	}


                     501 ;495: 


                     502 ;496: 	OscWriteBuffer_destroy(&oscInfo->wbContent);


                     503 ;497: 	OscWriteBuffer_destroy(&oscInfo->wbFrame);


                     504 ;498: 


                     505 ;499: 	OscFiles_free(oscInfo);


                     506 ;500: }


                     507 ;501: 


                     508 ;502: OscWriteBuffer * OSCInfo_lockHeaderBuf(void)


                     509 ;503: {


                     510 ;504: 	CriticalSection_Lock(&csHeaderBuf);


                     511 ;505: 	return &oscHeaderBuf;


                     512 ;506: }


                     513 ;507: 


                     514 ;508: void OSCInfo_unlockHeaderBuf(void)


                     515 ;509: {


                     516 ;510: 	CriticalSection_Unlock(&csHeaderBuf);


                     517 ;511: }


                     518 ;512: 


                     519 ;513: unsigned long OSCInfo_getUTCDate(OSCInfoStruct *oscInfo)


                     520 ;514: {


                     521 ;515: 	return oscInfo->iface->getUTCDate(oscInfo);


                     522 ;516: }


                     523 ;517: 


                     524 ;518: unsigned long OSCInfo_getDateMS(OSCInfoStruct *oscInfo)


                     525 ;519: {


                     526 ;520: 	return oscInfo->iface->getDateMS(oscInfo);


                     527 ;521: }


                     528 ;522: 


                     529 ;523: unsigned long OSCInfo_getOscVersion(OSCInfoStruct *oscInfo)


                     530 ;524: {


                     531 ;525: 	return oscInfo->iface->getOscVersion(oscInfo);


                     532 ;526: }


                     533 ;527: 


                     534 ;528: unsigned long OSCInfo_getADCClkFreq(OSCInfoStruct *oscInfo)


                     535 ;529: {


                     536 ;530: 	return oscInfo->iface->getADCClkFreq(oscInfo);


                     537 ;531: }


                     538 ;532: 



                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
                     539 ;533: unsigned long OSCInfo_getPrehistFrameCount(OSCInfoStruct *oscInfo)


                     540 ;534: {


                     541 ;535: 	return oscInfo->iface->getPrehistFrameCount(oscInfo);


                     542 ;536: }


                     543 ;537: 


                     544 ;538: unsigned long OSCInfo_getPrehistFirstFrameNum(OSCInfoStruct *oscInfo)


                     545 ;539: {


                     546 ;540: 	return oscInfo->iface->getPrehistFirstFrameNum(oscInfo);


                     547 ;541: }


                     548 ;542: 


                     549 ;543: unsigned long OSCInfo_getPointPerFrameCount(OSCInfoStruct *oscInfo)


                     550 ;544: {


                     551 ;545: 	return oscInfo->iface->getPointPerFrameCount(oscInfo);


                     552 ;546: }


                     553 ;547: 


                     554 ;548: unsigned long OSCInfo_getAnalogCount(OSCInfoStruct *oscInfo)


                     555 ;549: {


                     556 ;550: 	return oscInfo->iface->getAnalogInCount(oscInfo);


                     557 ;551: }


                     558 ;552: 


                     559 ;553: unsigned long OSCInfo_getBoolCount(OSCInfoStruct *oscInfo)


                     560 ;554: {


                     561 ;555: 	return oscInfo->iface->getDigInCount(oscInfo);


                     562 ;556: }


                     563 ;557: 


                     564 ;558: unsigned long OSCInfo_getHeaderSize(OSCInfoStruct *oscInfo)


                     565 ;559: {


                     566 ;560: 	return oscInfo->iface->getHeaderSize(oscInfo);


                     567 ;561: }


                     568 ;562: 


                     569 ;563: unsigned long OSCInfo_getFrameCount(OSCInfoStruct *oscInfo)


                     570 ;564: {


                     571 ;565: 	return oscInfo->frameCount;


                     572 ;566: }


                     573 ;567: 


                     574 ;568: unsigned int OSCInfo_getFrameOffset(OSCInfoStruct *oscInfo, unsigned int frameNum)


                     575 ;569: {


                     576 ;570: 	unsigned int prehistFrameCount = OSCInfo_getPrehistFrameCount(oscInfo);


                     577 ;571: 	// если кадр из предыстории


                     578 ;572: 	if (frameNum < prehistFrameCount)


                     579 ;573: 	{


                     580 ;574: 		int firstFrameNum = OSCInfo_getPrehistFirstFrameNum(oscInfo);


                     581 ;575: 		unsigned int relativeFrameNum = firstFrameNum + frameNum;


                     582 ;576: 


                     583 ;577: 		if (relativeFrameNum >= prehistFrameCount)


                     584 ;578: 		{


                     585 ;579: 			relativeFrameNum = relativeFrameNum - prehistFrameCount;


                     586 ;580: 		}


                     587 ;581: 		return oscInfo->firstFrameOffset + relativeFrameNum * oscInfo->frameSize;


                     588 ;582: 	}


                     589 ;583: 	else


                     590 ;584: 	{


                     591 ;585: 		return oscInfo->firstFrameOffset + frameNum * oscInfo->frameSize;


                     592 ;586: 	}


                     593 ;587: }


                     594 ;588: 


                     595 ;589: int OSCInfo_getADCSampleSize(OSCInfoStruct *oscInfo)


                     596 ;590: {


                     597 ;591: 	return oscInfo->iface->getADCSampleSize(oscInfo);


                     598 ;592: }


                     599 ;593: 



                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
                     600 ;594: int OSCInfo_getADCFractionSize(OSCInfoStruct *oscInfo)


                     601 ;595: {


                     602 ;596: 	return oscInfo->iface->getADCFractionSize(oscInfo);


                     603 ;597: }


                     604 ;598: 


                     605 ;599: int OSCInfo_getOscContentOffset(OSCInfoStruct *oscInfo)


                     606 ;600: {


                     607 ;601: 	return OSCInfo_getHeaderSize(oscInfo);


                     608 ;602: }


                     609 ;603: 


                     610 ;604: OscWriteBuffer * OSCInfo_getBufferContent(OSCInfoStruct *oscInfo)


                     611 ;605: {


                     612 ;606: 	return &oscInfo->wbContent;


                     613 ;607: }


                     614 ;608: 


                     615 ;609: OscWriteBuffer * OSCInfo_getFrameBuffer(OSCInfoStruct *oscInfo)


                     616 ;610: {


                     617 ;611: 	return &oscInfo->wbFrame;


                     618 ;612: }


                     619 ;613: 


                     620 ;614: //! период текущего фрейма


                     621 ;615: int OSCFrame_getADCPeriod(OSCInfoStruct *oscInfo)


                     622 ;616: {


                     623 ;617: 	unsigned int result;


                     624 ;618: 	unsigned char *frame = OscWriteBuffer_data(&oscInfo->wbFrame);


                     625 ;619: 	result = frame[0];


                     626 ;620: 	result += frame[1] << 8;


                     627 ;621: 	result += frame[2] << 16;


                     628 ;622: 	result += frame[3] << 24;


                     629 ;623: 	return result;


                     630 ;624: }


                     631 ;625: static int extractWithSignExtension(void* pData, size_t byteCount)


                     632 

                     633 ;642: }


                     634 

                     635 ;643: 


                     636 ;644: 


                     637 ;645: #define MAX_COMTRADE_SAMPLE_BIT_WIDTH 20


                     638 ;646: 


                     639 ;647: static void updateADCMaxAbsValue(OSCInfoStruct *oscInfo, unsigned int analogNum)


                     640 

                     641 ;660: }


                     642 

                     643 ;661: 


                     644 ;662: static void updateAnalogCft(OSCInfoStruct *oscInfo, unsigned int analogNum)


                     645 

                     646 ;673: }


                     647 

                     648 ;674: 


                     649 ;675: int OSCFrame_getAnalogValue(OSCInfoStruct *oscInfo, unsigned int pointNum, unsigned int analogNum)


                     650 ;676: {


                     651 ;677: 	//	OSCInfoAnalog *pAnalog = &oscInfo->pAnalog[analogNum];


                     652 ;678: 	//	int analogCount = OSCInfo_getAnalogCount(oscInfo);


                     653 ;679: 	unsigned char *frame = OscWriteBuffer_data(&oscInfo->wbFrame);


                     654 ;680: 	unsigned char *value;


                     655 ;681: 	int result = 0;


                     656 ;682: 	int adcSampleSize = OSCInfo_getADCSampleSize(oscInfo);


                     657 ;683: 	int adcSampleCount = OSCInfo_getPointPerFrameCount(oscInfo);


                     658 ;684: 


                     659 ;685: 		


                     660 ;686: 	// смещение на 0 аналоговое значение



                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
                     661 ;687: 	value = frame + getAdcPeriodSize(oscInfo);


                     662 ;688: 	// значение с учетом номер отсчета


                     663 ;689: 	value += analogNum * adcSampleCount * adcSampleSize;


                     664 ;690: 	// значение с учетом номера канала


                     665 ;691: 	value += adcSampleSize * pointNum;


                     666 ;692: 	// память в знаковое значение


                     667 ;693: 	result = extractWithSignExtension(value, adcSampleSize);


                     668 ;694: 


                     669 ;695: 	return result;


                     670 ;696: }


                     671 ;697: 


                     672 ;698: 


                     673 ;699: int OSCFrame_getBoolValue(OSCInfoStruct *oscInfo, int sampleNum, int boolNum)


                     674 ;700: {


                     675 ;701: 	unsigned char *frame = OscWriteBuffer_data(&oscInfo->wbFrame);


                     676 ;702: 	unsigned char *value;


                     677 ;703: 	int analogCount = OSCInfo_getAnalogCount(oscInfo);


                     678 ;704: 	int adcSampleSize = OSCInfo_getADCSampleSize(oscInfo);


                     679 ;705: 	int pointPerFrame = OSCInfo_getPointPerFrameCount(oscInfo);


                     680 ;706: 


                     681 ;707: 	value = frame + getAdcPeriodSize(oscInfo) + pointPerFrame * analogCount * adcSampleSize;


                     682 ;708: 	value += boolNum;


                     683 ;709: 	return *value;


                     684 ;710: }


                     685 ;711: 


                     686 ;712: 


                     687 ;713: bool OSCFrame_getTick(OSCInfoStruct *oscInfo, double *tick)


                     688 ;714: {


                     689 ;715: 	unsigned char *frame = OscWriteBuffer_data(&oscInfo->wbFrame);


                     690 ;716: 	unsigned long adcTick;


                     691 ;717: 	double mainFreq = OSCInfo_getADCClkFreq(oscInfo);


                     692 ;718: 	double result ;


                     693 ;719: 	memcpy(&adcTick, frame, getAdcPeriodSize(oscInfo));


                     694 ;720: 	if (adcTick != 0)


                     695 ;721: 	{


                     696 ;722: 		result = 1000000 / (mainFreq / adcTick);


                     697 ;723: 		*tick = result;


                     698 ;724: 		return true;


                     699 ;725: 	}


                     700 ;726: 	{


                     701 ;727: 		return false;


                     702 ;728: 	}


                     703 ;729: }


                     704 ;730: 


                     705 ;731: bool OSCFrame_getFreq(OSCInfoStruct *oscInfo, float *freq)


                     706 ;732: {


                     707 ;733: 	unsigned char *frame = OscWriteBuffer_data(&oscInfo->wbFrame);


                     708 ;734: 	unsigned long adcFreq;


                     709 ;735: 	double mainFreq = OSCInfo_getADCClkFreq(oscInfo);


                     710 ;736: 	double result;


                     711 ;737: 	memcpy(&adcFreq, frame, getAdcPeriodSize(oscInfo));


                     712 ;738: 	if (adcFreq != 0)


                     713 ;739: 	{


                     714 ;740: 		result = mainFreq / adcFreq;


                     715 ;741: 		*freq = (float)result;


                     716 ;742: 		return true;


                     717 ;743: 	}


                     718 ;744: 	else


                     719 ;745: 	{


                     720 ;746: 		return false;


                     721 ;747: 	}



                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
                     722 ;748: }


                     723 ;749: 


                     724 ;750: //! указатель состав аналоговых каналов


                     725 ;751: static void *getAnalogContentPtr(OSCInfoStruct *oscInfo)


                     726 

                     727 ;756: }


                     728 

                     729 ;757: //! указатель на состав дискретных каналов


                     730 ;758: static void *getBoolContentPtr(OSCInfoStruct *oscInfo)


                     731 

                     732 ;763: }


                     733 

                     734 ;764: 


                     735 ;765: static bool boolInit(OSCInfoStruct *oscInfo)


                     736 

                     737 ;785: }


                     738 

                     739 ;786: 


                     740 ;787: static bool analogInit(OSCInfoStruct *oscInfo)


                     741 

                     742 ;807: }


                     743 

                     744 	.text

                     745 	.align	4

                     746 getUTCDate:

00000000 e5900014    747 	ldr	r0,[r0,20]

00000004 e12fff1e*   748 	ret	

                     749 	.endf	getUTCDate

                     750 	.align	4

                     751 

                     752 ;oscInfo	r0	param

                     753 

                     754 	.section ".bss","awb"

                     755 .L305:

                     756 	.data

                     757 	.text

                     758 

                     759 

                     760 	.align	4

                     761 	.align	4

                     762 getDateMS:

00000008 e1d001b8    763 	ldrh	r0,[r0,24]

0000000c e12fff1e*   764 	ret	

                     765 	.endf	getDateMS

                     766 	.align	4

                     767 

                     768 ;oscInfo	r0	param

                     769 

                     770 	.section ".bss","awb"

                     771 .L337:

                     772 	.data

                     773 	.text

                     774 

                     775 

                     776 	.align	4

                     777 	.align	4

                     778 getOscVersion:

00000010 e5d0001b    779 	ldrb	r0,[r0,27]

00000014 e12fff1e*   780 	ret	

                     781 	.endf	getOscVersion

                     782 	.align	4


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
                     783 

                     784 ;oscInfo	r0	param

                     785 

                     786 	.section ".bss","awb"

                     787 .L369:

                     788 	.data

                     789 	.text

                     790 

                     791 

                     792 	.align	4

                     793 	.align	4

                     794 getADCClkFreq:

00000018 e5900010    795 	ldr	r0,[r0,16]

0000001c e12fff1e*   796 	ret	

                     797 	.endf	getADCClkFreq

                     798 	.align	4

                     799 

                     800 ;oscInfo	r0	param

                     801 

                     802 	.section ".bss","awb"

                     803 .L401:

                     804 	.data

                     805 	.text

                     806 

                     807 

                     808 	.align	4

                     809 	.align	4

                     810 getPrehistFrameCount3:

00000020 e5d0000a    811 	ldrb	r0,[r0,10]

00000024 e12fff1e*   812 	ret	

                     813 	.endf	getPrehistFrameCount3

                     814 	.align	4

                     815 

                     816 ;oscInfo	r0	param

                     817 

                     818 	.section ".bss","awb"

                     819 .L433:

                     820 	.data

                     821 	.text

                     822 

                     823 

                     824 	.align	4

                     825 	.align	4

                     826 getPrehistFirstFrameNum3:

00000028 e5d0000b    827 	ldrb	r0,[r0,11]

0000002c e12fff1e*   828 	ret	

                     829 	.endf	getPrehistFirstFrameNum3

                     830 	.align	4

                     831 

                     832 ;oscInfo	r0	param

                     833 

                     834 	.section ".bss","awb"

                     835 .L465:

                     836 	.data

                     837 	.text

                     838 

                     839 

                     840 	.align	4

                     841 	.align	4

                     842 getPrehistFrameCount4:

00000030 e590001c    843 	ldr	r0,[r0,28]


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
00000034 e12fff1e*   844 	ret	

                     845 	.endf	getPrehistFrameCount4

                     846 	.align	4

                     847 

                     848 ;oscInfo	r0	param

                     849 

                     850 	.section ".bss","awb"

                     851 .L497:

                     852 	.data

                     853 	.text

                     854 

                     855 

                     856 	.align	4

                     857 	.align	4

                     858 getPrehistFirstFrameNum4:

00000038 e5900020    859 	ldr	r0,[r0,32]

0000003c e12fff1e*   860 	ret	

                     861 	.endf	getPrehistFirstFrameNum4

                     862 	.align	4

                     863 

                     864 ;oscInfo	r0	param

                     865 

                     866 	.section ".bss","awb"

                     867 .L529:

                     868 	.data

                     869 	.text

                     870 

                     871 

                     872 	.align	4

                     873 	.align	4

                     874 getPointPerFrameCount3:

00000040 e1d001bc    875 	ldrh	r0,[r0,28]

00000044 e12fff1e*   876 	ret	

                     877 	.endf	getPointPerFrameCount3

                     878 	.align	4

                     879 

                     880 ;oscInfo	r0	param

                     881 

                     882 	.section ".bss","awb"

                     883 .L561:

                     884 	.data

                     885 	.text

                     886 

                     887 

                     888 	.align	4

                     889 	.align	4

                     890 getPointPerFrameCount4:

00000048 e1d002b4    891 	ldrh	r0,[r0,36]

0000004c e12fff1e*   892 	ret	

                     893 	.endf	getPointPerFrameCount4

                     894 	.align	4

                     895 

                     896 ;oscInfo	r0	param

                     897 

                     898 	.section ".bss","awb"

                     899 .L593:

                     900 	.data

                     901 	.text

                     902 

                     903 

                     904 	.align	4


                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
                     905 	.align	4

                     906 getAnalogInCount3:

00000050 e1d002b0    907 	ldrh	r0,[r0,32]

00000054 e12fff1e*   908 	ret	

                     909 	.endf	getAnalogInCount3

                     910 	.align	4

                     911 

                     912 ;oscInfo	r0	param

                     913 

                     914 	.section ".bss","awb"

                     915 .L625:

                     916 	.data

                     917 	.text

                     918 

                     919 

                     920 	.align	4

                     921 	.align	4

                     922 getDigInCount3:

00000058 e1d002b2    923 	ldrh	r0,[r0,34]

0000005c e12fff1e*   924 	ret	

                     925 	.endf	getDigInCount3

                     926 	.align	4

                     927 

                     928 ;oscInfo	r0	param

                     929 

                     930 	.section ".bss","awb"

                     931 .L657:

                     932 	.data

                     933 	.text

                     934 

                     935 

                     936 	.align	4

                     937 	.align	4

                     938 getAnalogInCount4:

00000060 e1d002b8    939 	ldrh	r0,[r0,40]

00000064 e12fff1e*   940 	ret	

                     941 	.endf	getAnalogInCount4

                     942 	.align	4

                     943 

                     944 ;oscInfo	r0	param

                     945 

                     946 	.section ".bss","awb"

                     947 .L689:

                     948 	.data

                     949 	.text

                     950 

                     951 

                     952 	.align	4

                     953 	.align	4

                     954 getDigInCount4:

00000068 e1d002ba    955 	ldrh	r0,[r0,42]

0000006c e12fff1e*   956 	ret	

                     957 	.endf	getDigInCount4

                     958 	.align	4

                     959 

                     960 ;oscInfo	r0	param

                     961 

                     962 	.section ".bss","awb"

                     963 .L721:

                     964 	.data

                     965 	.text


                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
                     966 

                     967 

                     968 	.align	4

                     969 	.align	4

                     970 getADCSampleSize4:

00000070 e5d00026    971 	ldrb	r0,[r0,38]

00000074 e3500000    972 	cmp	r0,0

00000078 03a00004    973 	moveq	r0,4

0000007c e12fff1e*   974 	ret	

                     975 	.endf	getADCSampleSize4

                     976 	.align	4

                     977 ;adcSampleSize	r0	local

                     978 

                     979 ;oscInfo	r0	param

                     980 

                     981 	.section ".bss","awb"

                     982 .L761:

                     983 	.data

                     984 	.text

                     985 

                     986 

                     987 	.align	4

                     988 	.align	4

                     989 getADCFractionSize4:

00000080 e5d00027    990 	ldrb	r0,[r0,39]

00000084 e12fff1e*   991 	ret	

                     992 	.endf	getADCFractionSize4

                     993 	.align	4

                     994 

                     995 ;oscInfo	r0	param

                     996 

                     997 	.section ".bss","awb"

                     998 .L801:

                     999 	.data

                    1000 	.text

                    1001 

                    1002 

                    1003 	.align	4

                    1004 	.align	4

                    1005 getOscSize:

00000088 e5900000   1006 	ldr	r0,[r0]

0000008c e12fff1e*  1007 	ret	

                    1008 	.endf	getOscSize

                    1009 	.align	4

                    1010 

                    1011 ;oscInfo	r0	param

                    1012 

                    1013 	.section ".bss","awb"

                    1014 .L833:

                    1015 	.data

                    1016 	.text

                    1017 

                    1018 

                    1019 	.align	4

                    1020 	.align	4

                    1021 OSCInfo_init::

00000090 e92d4000   1022 	stmfd	[sp]!,{lr}

00000094 e59f1a74*  1023 	ldr	r1,.L900

00000098 e59f0a74*  1024 	ldr	r0,.L901

0000009c e3a0202c   1025 	mov	r2,44

000000a0 eb000000*  1026 	bl	OscWriteBuffer_attach


                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
000000a4 e3500000   1027 	cmp	r0,0

000000a8 0a000002   1028 	beq	.L840

000000ac e59f0a64*  1029 	ldr	r0,.L902

000000b0 eb000000*  1030 	bl	CriticalSection_Init

000000b4 e3a00001   1031 	mov	r0,1

                    1032 .L840:

000000b8 e8bd8000   1033 	ldmfd	[sp]!,{pc}

                    1034 	.endf	OSCInfo_init

                    1035 	.align	4

                    1036 ;oscHeader	.L888	static

                    1037 

                    1038 	.section ".bss","awb"

                    1039 .L885:

00000000 00000000   1040 .L888:	.space	44

00000004 00000000 
00000008 00000000 
0000000c 00000000 
00000010 00000000 
00000014 00000000 
00000018 00000000 
0000001c 00000000 
00000020 00000000 
00000024 00000000 
00000028 00000000 
                    1041 	.data

                    1042 	.text

                    1043 

                    1044 

                    1045 	.align	4

                    1046 	.align	4

                    1047 getFrameSize:

000000bc e92d40f0   1048 	stmfd	[sp]!,{r4-r7,lr}

000000c0 e1a06000   1049 	mov	r6,r0

000000c4 eb0000bb*  1050 	bl	OSCInfo_getPointPerFrameCount

000000c8 e1a07000   1051 	mov	r7,r0

000000cc e1a00006   1052 	mov	r0,r6

000000d0 eb0000be*  1053 	bl	OSCInfo_getAnalogCount

000000d4 e1a04000   1054 	mov	r4,r0

000000d8 e1a00006   1055 	mov	r0,r6

000000dc eb0000c1*  1056 	bl	OSCInfo_getBoolCount

000000e0 e1a05000   1057 	mov	r5,r0

                    1058 ;319: {


                    1059 

                    1060 ;320: 	return 4;


                    1061 

000000e4 e1a00006   1062 	mov	r0,r6

000000e8 eb0000dd*  1063 	bl	OSCInfo_getADCSampleSize

000000ec e3500000   1064 	cmp	r0,0

000000f0 13570000   1065 	cmpne	r7,0

000000f4 10000097   1066 	mulne	r0,r7,r0

000000f8 10205094   1067 	mlane	r0,r4,r0,r5

000000fc 12800004   1068 	addne	r0,r0,4

00000100 03a00000   1069 	moveq	r0,0

00000104 e8bd40f0   1070 	ldmfd	[sp]!,{r4-r7,lr}

00000108 e12fff1e*  1071 	ret	

                    1072 	.endf	getFrameSize

                    1073 	.align	4

                    1074 ;pointsPerFrame	r7	local

                    1075 ;analogCount	r4	local

                    1076 ;boolCount	r5	local

                    1077 ;adcSampleSize	r0	local


                                                                      Page 19
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
                    1078 

                    1079 ;oscInfo	r6	param

                    1080 

                    1081 	.section ".bss","awb"

                    1082 .L976:

                    1083 	.data

                    1084 	.text

                    1085 

                    1086 

                    1087 	.align	4

                    1088 	.align	4

                    1089 getContentInfoSize:

0000010c e92d4030   1090 	stmfd	[sp]!,{r4-r5,lr}

00000110 e1a05000   1091 	mov	r5,r0

00000114 eb0000ad*  1092 	bl	OSCInfo_getAnalogCount

00000118 e1a04000   1093 	mov	r4,r0

0000011c e1a00005   1094 	mov	r0,r5

00000120 eb0000b0*  1095 	bl	OSCInfo_getBoolCount

                    1096 ;260: {


                    1097 

                    1098 ;261: 	return 2;


                    1099 

                    1100 ;264: {


                    1101 

                    1102 ;265: 	return 2;


                    1103 

00000124 e0800004   1104 	add	r0,r0,r4

00000128 e1a00080   1105 	mov	r0,r0 lsl 1

0000012c e8bd4030   1106 	ldmfd	[sp]!,{r4-r5,lr}

00000130 e12fff1e*  1107 	ret	

                    1108 	.endf	getContentInfoSize

                    1109 	.align	4

                    1110 ;analogCount	r4	local

                    1111 

                    1112 ;oscInfo	r5	param

                    1113 

                    1114 	.section ".bss","awb"

                    1115 .L1022:

                    1116 	.data

                    1117 	.text

                    1118 

                    1119 

                    1120 	.align	4

                    1121 	.align	4

                    1122 	.align	4

                    1123 OSCInfo_create::

00000134 e92d40f0   1124 	stmfd	[sp]!,{r4-r7,lr}

00000138 eb000000*  1125 	bl	OscWriteBuffer_data

0000013c e1a05000   1126 	mov	r5,r0

00000140 e5d5001b   1127 	ldrb	r0,[r5,27]

00000144 e3500003   1128 	cmp	r0,3

00000148 1a000005   1129 	bne	.L1031

0000014c e59f69c8*  1130 	ldr	r6,.L1382

00000150 e3a0006c   1131 	mov	r0,108

00000154 eb000000*  1132 	bl	OscFiles_malloc

00000158 e1b04000   1133 	movs	r4,r0

0000015c 0a000007   1134 	beq	.L1039

00000160 ea000008   1135 	b	.L1038

                    1136 .L1031:

00000164 e3500004   1137 	cmp	r0,4

00000168 1a000004   1138 	bne	.L1039


                                                                      Page 20
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
0000016c e59f69ac*  1139 	ldr	r6,.L1383

00000170 e3a0006c   1140 	mov	r0,108

00000174 eb000000*  1141 	bl	OscFiles_malloc

00000178 e1b04000   1142 	movs	r4,r0

0000017c 1a000001   1143 	bne	.L1038

                    1144 .L1039:

00000180 e3a00000   1145 	mov	r0,0

00000184 ea000050   1146 	b	.L1029

                    1147 .L1038:

00000188 e3a0206c   1148 	mov	r2,108

0000018c e3a01000   1149 	mov	r1,0

00000190 eb000000*  1150 	bl	memset

00000194 e1a01005   1151 	mov	r1,r5

00000198 e1a00004   1152 	mov	r0,r4

0000019c e3a0202c   1153 	mov	r2,44

000001a0 eb000000*  1154 	bl	memcpy

000001a4 e584602c   1155 	str	r6,[r4,44]

000001a8 e3a00000   1156 	mov	r0,0

000001ac e5840058   1157 	str	r0,[r4,88]

000001b0 e584005c   1158 	str	r0,[r4,92]

000001b4 e1a00004   1159 	mov	r0,r4

000001b8 eb000084*  1160 	bl	OSCInfo_getAnalogCount

000001bc e3500000   1161 	cmp	r0,0

000001c0 0a00003e   1162 	beq	.L1073

000001c4 e1a00200   1163 	mov	r0,r0 lsl 4

000001c8 eb000000*  1164 	bl	OscFiles_malloc

000001cc e5840058   1165 	str	r0,[r4,88]

000001d0 e3500000   1166 	cmp	r0,0

000001d4 0a000039   1167 	beq	.L1073

000001d8 e1a00004   1168 	mov	r0,r4

000001dc eb000081*  1169 	bl	OSCInfo_getBoolCount

000001e0 e3500000   1170 	cmp	r0,0

000001e4 0a000004   1171 	beq	.L1047

000001e8 e1a00100   1172 	mov	r0,r0 lsl 2

000001ec eb000000*  1173 	bl	OscFiles_malloc

000001f0 e584005c   1174 	str	r0,[r4,92]

000001f4 e3500000   1175 	cmp	r0,0

000001f8 0a000030   1176 	beq	.L1073

                    1177 .L1047:

000001fc e1a00004   1178 	mov	r0,r4

00000200 ebffffc1*  1179 	bl	getContentInfoSize

00000204 e1b05000   1180 	movs	r5,r0

00000208 0a00002c   1181 	beq	.L1073

                    1182 ;361: {


                    1183 

                    1184 ;362: 	int dataLen = oscInfo->iface->getOscSize(oscInfo);


                    1185 

0000020c e594002c   1186 	ldr	r0,[r4,44]

00000210 e590c024   1187 	ldr	r12,[r0,36]

00000214 e1a00004   1188 	mov	r0,r4

00000218 e1a0e00f   1189 	mov	lr,pc

0000021c e12fff1c*  1190 	bx	r12

00000220 e1a06000   1191 	mov	r6,r0

                    1192 ;363: 	int frameSize = getFrameSize(oscInfo);


                    1193 

00000224 e1a00004   1194 	mov	r0,r4

00000228 ebffffa3*  1195 	bl	getFrameSize

0000022c e1b07000   1196 	movs	r7,r0

                    1197 ;364: 	if (frameSize == 0)


                    1198 

00000230 0a000006   1199 	beq	.L1060


                                                                      Page 21
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
                    1200 ;365: 	{


                    1201 

                    1202 ;366: 		return 0;


                    1203 

                    1204 ;367: 	}


                    1205 ;368: 	dataLen -= OSCInfo_getHeaderSize(oscInfo);


                    1206 

00000234 e1a00004   1207 	mov	r0,r4

00000238 eb000070*  1208 	bl	OSCInfo_getHeaderSize

0000023c e0466000   1209 	sub	r6,r6,r0

                    1210 ;369: 	dataLen -= getContentInfoSize(oscInfo);


                    1211 

00000240 e1a00004   1212 	mov	r0,r4

00000244 ebffffb0*  1213 	bl	getContentInfoSize

00000248 e0566000   1214 	subs	r6,r6,r0

                    1215 ;370: 	if (dataLen < 0)


                    1216 

0000024c 5a000002   1217 	bpl	.L1061

                    1218 .L1060:

                    1219 ;371: 	{


                    1220 

                    1221 ;372: 		return 0;


                    1222 

00000250 e3a00000   1223 	mov	r0,0

00000254 e5840060   1224 	str	r0,[r4,96]

00000258 ea000018   1225 	b	.L1073

                    1226 .L1061:

                    1227 ;373: 	}


                    1228 ;374: 


                    1229 ;375: 	return dataLen / frameSize;


                    1230 

0000025c e1a01006   1231 	mov	r1,r6

00000260 e1a00007   1232 	mov	r0,r7

00000264 eb000000*  1233 	bl	__sdiv_32_32

00000268 e5840060   1234 	str	r0,[r4,96]

0000026c e3500000   1235 	cmp	r0,0

00000270 0a000012   1236 	beq	.L1073

00000274 e1a00004   1237 	mov	r0,r4

00000278 eb000060*  1238 	bl	OSCInfo_getHeaderSize

0000027c e1a01005   1239 	mov	r1,r5

00000280 e0850000   1240 	add	r0,r5,r0

00000284 e5840068   1241 	str	r0,[r4,104]

00000288 e2840030   1242 	add	r0,r4,48

0000028c eb000000*  1243 	bl	OscWriteBuffer_create

00000290 e3500000   1244 	cmp	r0,0

00000294 0a000009   1245 	beq	.L1073

00000298 e1a00004   1246 	mov	r0,r4

0000029c ebffff86*  1247 	bl	getFrameSize

000002a0 e1b01000   1248 	movs	r1,r0

000002a4 e5841064   1249 	str	r1,[r4,100]

000002a8 0a000004   1250 	beq	.L1073

000002ac e2840044   1251 	add	r0,r4,68

000002b0 eb000000*  1252 	bl	OscWriteBuffer_create

000002b4 e3500000   1253 	cmp	r0,0

000002b8 11a00004   1254 	movne	r0,r4

000002bc 1a000002   1255 	bne	.L1029

                    1256 .L1073:

000002c0 e1a00004   1257 	mov	r0,r4

000002c4 eb000001*  1258 	bl	OSCInfo_destroy

000002c8 e3a00000   1259 	mov	r0,0

                    1260 .L1029:


                                                                      Page 22
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
000002cc e8bd80f0   1261 	ldmfd	[sp]!,{r4-r7,pc}

                    1262 	.endf	OSCInfo_create

                    1263 	.align	4

                    1264 ;oscHeader	r5	local

                    1265 ;oscInfo	r4	local

                    1266 ;iface	r6	local

                    1267 ;contentSize	r5	local

                    1268 ;analogCount	r0	local

                    1269 ;boolCount	r0	local

                    1270 ;dataLen	r6	local

                    1271 ;frameSize	r7	local

                    1272 

                    1273 ;headBufferView	none	param

                    1274 

                    1275 	.section ".bss","awb"

                    1276 .L1320:

                    1277 	.data

                    1278 	.text

                    1279 

                    1280 

                    1281 	.align	4

                    1282 	.align	4

                    1283 OSCInfo_destroy::

000002d0 e92d4010   1284 	stmfd	[sp]!,{r4,lr}

000002d4 e1a04000   1285 	mov	r4,r0

000002d8 e5940058   1286 	ldr	r0,[r4,88]

000002dc e3500000   1287 	cmp	r0,0

000002e0 1b000000*  1288 	blne	OscFiles_free

000002e4 e594005c   1289 	ldr	r0,[r4,92]

000002e8 e3500000   1290 	cmp	r0,0

000002ec 1b000000*  1291 	blne	OscFiles_free

000002f0 e2840030   1292 	add	r0,r4,48

000002f4 eb000000*  1293 	bl	OscWriteBuffer_destroy

000002f8 e2840044   1294 	add	r0,r4,68

000002fc eb000000*  1295 	bl	OscWriteBuffer_destroy

00000300 e1a00004   1296 	mov	r0,r4

00000304 e8bd4010   1297 	ldmfd	[sp]!,{r4,lr}

00000308 ea000000*  1298 	b	OscFiles_free

                    1299 	.endf	OSCInfo_destroy

                    1300 	.align	4

                    1301 

                    1302 ;oscInfo	r4	param

                    1303 

                    1304 	.section ".bss","awb"

                    1305 .L1433:

                    1306 	.data

                    1307 	.text

                    1308 

                    1309 

                    1310 	.align	4

                    1311 	.align	4

                    1312 OSCInfo_lockHeaderBuf::

0000030c e92d4000   1313 	stmfd	[sp]!,{lr}

00000310 e59f0800*  1314 	ldr	r0,.L902

00000314 eb000000*  1315 	bl	CriticalSection_Lock

00000318 e59f07f4*  1316 	ldr	r0,.L901

0000031c e8bd8000   1317 	ldmfd	[sp]!,{pc}

                    1318 	.endf	OSCInfo_lockHeaderBuf

                    1319 	.align	4

                    1320 

                    1321 	.section ".bss","awb"


                                                                      Page 23
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
                    1322 .L1470:

                    1323 	.data

                    1324 	.text

                    1325 

                    1326 

                    1327 	.align	4

                    1328 	.align	4

                    1329 OSCInfo_unlockHeaderBuf::

00000320 e59f07f0*  1330 	ldr	r0,.L902

00000324 ea000000*  1331 	b	CriticalSection_Unlock

                    1332 	.endf	OSCInfo_unlockHeaderBuf

                    1333 	.align	4

                    1334 

                    1335 	.section ".bss","awb"

                    1336 .L1502:

                    1337 	.data

                    1338 	.text

                    1339 

                    1340 

                    1341 	.align	4

                    1342 	.align	4

                    1343 OSCInfo_getUTCDate::

00000328 e92d4000   1344 	stmfd	[sp]!,{lr}

0000032c e590c02c   1345 	ldr	r12,[r0,44]

00000330 e59cc000   1346 	ldr	r12,[r12]

00000334 e1a0e00f   1347 	mov	lr,pc

00000338 e12fff1c*  1348 	bx	r12

0000033c e8bd8000   1349 	ldmfd	[sp]!,{pc}

                    1350 	.endf	OSCInfo_getUTCDate

                    1351 	.align	4

                    1352 

                    1353 ;oscInfo	none	param

                    1354 

                    1355 	.section ".bss","awb"

                    1356 .L1534:

                    1357 	.data

                    1358 	.text

                    1359 

                    1360 

                    1361 	.align	4

                    1362 	.align	4

                    1363 OSCInfo_getDateMS::

00000340 e92d4000   1364 	stmfd	[sp]!,{lr}

00000344 e590c02c   1365 	ldr	r12,[r0,44]

00000348 e59cc004   1366 	ldr	r12,[r12,4]

0000034c e1a0e00f   1367 	mov	lr,pc

00000350 e12fff1c*  1368 	bx	r12

00000354 e8bd8000   1369 	ldmfd	[sp]!,{pc}

                    1370 	.endf	OSCInfo_getDateMS

                    1371 	.align	4

                    1372 

                    1373 ;oscInfo	none	param

                    1374 

                    1375 	.section ".bss","awb"

                    1376 .L1566:

                    1377 	.data

                    1378 	.text

                    1379 

                    1380 

                    1381 	.align	4

                    1382 	.align	4


                                                                      Page 24
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
                    1383 OSCInfo_getOscVersion::

00000358 e92d4000   1384 	stmfd	[sp]!,{lr}

0000035c e590c02c   1385 	ldr	r12,[r0,44]

00000360 e59cc008   1386 	ldr	r12,[r12,8]

00000364 e1a0e00f   1387 	mov	lr,pc

00000368 e12fff1c*  1388 	bx	r12

0000036c e8bd8000   1389 	ldmfd	[sp]!,{pc}

                    1390 	.endf	OSCInfo_getOscVersion

                    1391 	.align	4

                    1392 

                    1393 ;oscInfo	none	param

                    1394 

                    1395 	.section ".bss","awb"

                    1396 .L1598:

                    1397 	.data

                    1398 	.text

                    1399 

                    1400 

                    1401 	.align	4

                    1402 	.align	4

                    1403 OSCInfo_getADCClkFreq::

00000370 e92d4000   1404 	stmfd	[sp]!,{lr}

00000374 e590c02c   1405 	ldr	r12,[r0,44]

00000378 e59cc00c   1406 	ldr	r12,[r12,12]

0000037c e1a0e00f   1407 	mov	lr,pc

00000380 e12fff1c*  1408 	bx	r12

00000384 e8bd8000   1409 	ldmfd	[sp]!,{pc}

                    1410 	.endf	OSCInfo_getADCClkFreq

                    1411 	.align	4

                    1412 

                    1413 ;oscInfo	none	param

                    1414 

                    1415 	.section ".bss","awb"

                    1416 .L1630:

                    1417 	.data

                    1418 	.text

                    1419 

                    1420 

                    1421 	.align	4

                    1422 	.align	4

                    1423 OSCInfo_getPrehistFrameCount::

00000388 e92d4000   1424 	stmfd	[sp]!,{lr}

0000038c e590c02c   1425 	ldr	r12,[r0,44]

00000390 e59cc010   1426 	ldr	r12,[r12,16]

00000394 e1a0e00f   1427 	mov	lr,pc

00000398 e12fff1c*  1428 	bx	r12

0000039c e8bd8000   1429 	ldmfd	[sp]!,{pc}

                    1430 	.endf	OSCInfo_getPrehistFrameCount

                    1431 	.align	4

                    1432 

                    1433 ;oscInfo	none	param

                    1434 

                    1435 	.section ".bss","awb"

                    1436 .L1662:

                    1437 	.data

                    1438 	.text

                    1439 

                    1440 

                    1441 	.align	4

                    1442 	.align	4

                    1443 OSCInfo_getPrehistFirstFrameNum::


                                                                      Page 25
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
000003a0 e92d4000   1444 	stmfd	[sp]!,{lr}

000003a4 e590c02c   1445 	ldr	r12,[r0,44]

000003a8 e59cc014   1446 	ldr	r12,[r12,20]

000003ac e1a0e00f   1447 	mov	lr,pc

000003b0 e12fff1c*  1448 	bx	r12

000003b4 e8bd8000   1449 	ldmfd	[sp]!,{pc}

                    1450 	.endf	OSCInfo_getPrehistFirstFrameNum

                    1451 	.align	4

                    1452 

                    1453 ;oscInfo	none	param

                    1454 

                    1455 	.section ".bss","awb"

                    1456 .L1694:

                    1457 	.data

                    1458 	.text

                    1459 

                    1460 

                    1461 	.align	4

                    1462 	.align	4

                    1463 OSCInfo_getPointPerFrameCount::

000003b8 e92d4000   1464 	stmfd	[sp]!,{lr}

000003bc e590c02c   1465 	ldr	r12,[r0,44]

000003c0 e59cc018   1466 	ldr	r12,[r12,24]

000003c4 e1a0e00f   1467 	mov	lr,pc

000003c8 e12fff1c*  1468 	bx	r12

000003cc e8bd8000   1469 	ldmfd	[sp]!,{pc}

                    1470 	.endf	OSCInfo_getPointPerFrameCount

                    1471 	.align	4

                    1472 

                    1473 ;oscInfo	none	param

                    1474 

                    1475 	.section ".bss","awb"

                    1476 .L1726:

                    1477 	.data

                    1478 	.text

                    1479 

                    1480 

                    1481 	.align	4

                    1482 	.align	4

                    1483 OSCInfo_getAnalogCount::

000003d0 e92d4000   1484 	stmfd	[sp]!,{lr}

000003d4 e590c02c   1485 	ldr	r12,[r0,44]

000003d8 e59cc01c   1486 	ldr	r12,[r12,28]

000003dc e1a0e00f   1487 	mov	lr,pc

000003e0 e12fff1c*  1488 	bx	r12

000003e4 e8bd8000   1489 	ldmfd	[sp]!,{pc}

                    1490 	.endf	OSCInfo_getAnalogCount

                    1491 	.align	4

                    1492 

                    1493 ;oscInfo	none	param

                    1494 

                    1495 	.section ".bss","awb"

                    1496 .L1758:

                    1497 	.data

                    1498 	.text

                    1499 

                    1500 

                    1501 	.align	4

                    1502 	.align	4

                    1503 OSCInfo_getBoolCount::

000003e8 e92d4000   1504 	stmfd	[sp]!,{lr}


                                                                      Page 26
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
000003ec e590c02c   1505 	ldr	r12,[r0,44]

000003f0 e59cc020   1506 	ldr	r12,[r12,32]

000003f4 e1a0e00f   1507 	mov	lr,pc

000003f8 e12fff1c*  1508 	bx	r12

000003fc e8bd8000   1509 	ldmfd	[sp]!,{pc}

                    1510 	.endf	OSCInfo_getBoolCount

                    1511 	.align	4

                    1512 

                    1513 ;oscInfo	none	param

                    1514 

                    1515 	.section ".bss","awb"

                    1516 .L1790:

                    1517 	.data

                    1518 	.text

                    1519 

                    1520 

                    1521 	.align	4

                    1522 	.align	4

                    1523 OSCInfo_getHeaderSize::

00000400 e92d4000   1524 	stmfd	[sp]!,{lr}

00000404 e590c02c   1525 	ldr	r12,[r0,44]

00000408 e59cc028   1526 	ldr	r12,[r12,40]

0000040c e1a0e00f   1527 	mov	lr,pc

00000410 e12fff1c*  1528 	bx	r12

00000414 e8bd8000   1529 	ldmfd	[sp]!,{pc}

                    1530 	.endf	OSCInfo_getHeaderSize

                    1531 	.align	4

                    1532 

                    1533 ;oscInfo	none	param

                    1534 

                    1535 	.section ".bss","awb"

                    1536 .L1822:

                    1537 	.data

                    1538 	.text

                    1539 

                    1540 

                    1541 	.align	4

                    1542 	.align	4

                    1543 OSCInfo_getFrameCount::

00000418 e5900060   1544 	ldr	r0,[r0,96]

0000041c e12fff1e*  1545 	ret	

                    1546 	.endf	OSCInfo_getFrameCount

                    1547 	.align	4

                    1548 

                    1549 ;oscInfo	r0	param

                    1550 

                    1551 	.section ".bss","awb"

                    1552 .L1854:

                    1553 	.data

                    1554 	.text

                    1555 

                    1556 

                    1557 	.align	4

                    1558 	.align	4

                    1559 OSCInfo_getFrameOffset::

00000420 e92d4070   1560 	stmfd	[sp]!,{r4-r6,lr}

00000424 e1a04001   1561 	mov	r4,r1

00000428 e1a05000   1562 	mov	r5,r0

0000042c ebffffd5*  1563 	bl	OSCInfo_getPrehistFrameCount

00000430 e1a06000   1564 	mov	r6,r0

00000434 e1540006   1565 	cmp	r4,r6


                                                                      Page 27
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
00000438 2a000005   1566 	bhs	.L1861

0000043c e1a00005   1567 	mov	r0,r5

00000440 ebffffd6*  1568 	bl	OSCInfo_getPrehistFirstFrameNum

00000444 e0800004   1569 	add	r0,r0,r4

00000448 e0501006   1570 	subs	r1,r0,r6

0000044c 31a01000   1571 	movlo	r1,r0

00000450 e1a04001   1572 	mov	r4,r1

                    1573 .L1861:

00000454 e5951068   1574 	ldr	r1,[r5,104]

00000458 e5950064   1575 	ldr	r0,[r5,100]

0000045c e0201094   1576 	mla	r0,r4,r0,r1

00000460 e8bd8070   1577 	ldmfd	[sp]!,{r4-r6,pc}

                    1578 	.endf	OSCInfo_getFrameOffset

                    1579 	.align	4

                    1580 ;prehistFrameCount	r6	local

                    1581 ;relativeFrameNum	r0	local

                    1582 

                    1583 ;oscInfo	r5	param

                    1584 ;frameNum	r4	param

                    1585 

                    1586 	.section ".bss","awb"

                    1587 .L1920:

                    1588 	.data

                    1589 	.text

                    1590 

                    1591 

                    1592 	.align	4

                    1593 	.align	4

                    1594 OSCInfo_getADCSampleSize::

00000464 e92d4000   1595 	stmfd	[sp]!,{lr}

00000468 e590c02c   1596 	ldr	r12,[r0,44]

0000046c e59cc02c   1597 	ldr	r12,[r12,44]

00000470 e1a0e00f   1598 	mov	lr,pc

00000474 e12fff1c*  1599 	bx	r12

00000478 e8bd8000   1600 	ldmfd	[sp]!,{pc}

                    1601 	.endf	OSCInfo_getADCSampleSize

                    1602 	.align	4

                    1603 

                    1604 ;oscInfo	none	param

                    1605 

                    1606 	.section ".bss","awb"

                    1607 .L1950:

                    1608 	.data

                    1609 	.text

                    1610 

                    1611 

                    1612 	.align	4

                    1613 	.align	4

                    1614 OSCInfo_getADCFractionSize::

0000047c e92d4000   1615 	stmfd	[sp]!,{lr}

00000480 e590c02c   1616 	ldr	r12,[r0,44]

00000484 e59cc030   1617 	ldr	r12,[r12,48]

00000488 e1a0e00f   1618 	mov	lr,pc

0000048c e12fff1c*  1619 	bx	r12

00000490 e8bd8000   1620 	ldmfd	[sp]!,{pc}

                    1621 	.endf	OSCInfo_getADCFractionSize

                    1622 	.align	4

                    1623 

                    1624 ;oscInfo	none	param

                    1625 

                    1626 	.section ".bss","awb"


                                                                      Page 28
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
                    1627 .L1982:

                    1628 	.data

                    1629 	.text

                    1630 

                    1631 

                    1632 	.align	4

                    1633 	.align	4

                    1634 OSCInfo_getOscContentOffset::

00000494 eaffffd9*  1635 	b	OSCInfo_getHeaderSize

                    1636 	.endf	OSCInfo_getOscContentOffset

                    1637 	.align	4

                    1638 

                    1639 ;oscInfo	none	param

                    1640 

                    1641 	.section ".bss","awb"

                    1642 .L2014:

                    1643 	.data

                    1644 	.text

                    1645 

                    1646 

                    1647 	.align	4

                    1648 	.align	4

                    1649 OSCInfo_getBufferContent::

00000498 e2800030   1650 	add	r0,r0,48

0000049c e12fff1e*  1651 	ret	

                    1652 	.endf	OSCInfo_getBufferContent

                    1653 	.align	4

                    1654 

                    1655 ;oscInfo	r0	param

                    1656 

                    1657 	.section ".bss","awb"

                    1658 .L2046:

                    1659 	.data

                    1660 	.text

                    1661 

                    1662 

                    1663 	.align	4

                    1664 	.align	4

                    1665 OSCInfo_getFrameBuffer::

000004a0 e2800044   1666 	add	r0,r0,68

000004a4 e12fff1e*  1667 	ret	

                    1668 	.endf	OSCInfo_getFrameBuffer

                    1669 	.align	4

                    1670 

                    1671 ;oscInfo	r0	param

                    1672 

                    1673 	.section ".bss","awb"

                    1674 .L2078:

                    1675 	.data

                    1676 	.text

                    1677 

                    1678 

                    1679 	.align	4

                    1680 	.align	4

                    1681 OSCFrame_getADCPeriod::

000004a8 e92d4000   1682 	stmfd	[sp]!,{lr}

000004ac e2800044   1683 	add	r0,r0,68

000004b0 eb000000*  1684 	bl	OscWriteBuffer_data

000004b4 e5d02001   1685 	ldrb	r2,[r0,1]

000004b8 e5d01000   1686 	ldrb	r1,[r0]

000004bc e0811402   1687 	add	r1,r1,r2 lsl 8


                                                                      Page 29
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
000004c0 e5d02002   1688 	ldrb	r2,[r0,2]

000004c4 e5d00003   1689 	ldrb	r0,[r0,3]

000004c8 e0811802   1690 	add	r1,r1,r2 lsl 16

000004cc e1a00c00   1691 	mov	r0,r0 lsl 24

000004d0 e0810000   1692 	add	r0,r1,r0

000004d4 e8bd8000   1693 	ldmfd	[sp]!,{pc}

                    1694 	.endf	OSCFrame_getADCPeriod

                    1695 	.align	4

                    1696 ;result	r1	local

                    1697 

                    1698 ;oscInfo	r0	param

                    1699 

                    1700 	.section ".bss","awb"

                    1701 .L2110:

                    1702 	.data

                    1703 	.text

                    1704 

                    1705 

                    1706 	.align	4

                    1707 	.align	4

                    1708 OSCFrame_getAnalogValue::

000004d8 e92d44f0   1709 	stmfd	[sp]!,{r4-r7,r10,lr}

000004dc e24dd004   1710 	sub	sp,sp,4

000004e0 e1a06001   1711 	mov	r6,r1

000004e4 e1a0a002   1712 	mov	r10,r2

000004e8 e1a07000   1713 	mov	r7,r0

000004ec e2870044   1714 	add	r0,r7,68

000004f0 eb000000*  1715 	bl	OscWriteBuffer_data

000004f4 e1a05000   1716 	mov	r5,r0

000004f8 e1a00007   1717 	mov	r0,r7

000004fc ebffffd8*  1718 	bl	OSCInfo_getADCSampleSize

00000500 e1a04000   1719 	mov	r4,r0

00000504 e1a00007   1720 	mov	r0,r7

00000508 ebffffaa*  1721 	bl	OSCInfo_getPointPerFrameCount

                    1722 ;319: {


                    1723 

                    1724 ;320: 	return 4;


                    1725 

0000050c e000009a   1726 	mul	r0,r10,r0

00000510 e0205094   1727 	mla	r0,r4,r0,r5

00000514 e0200694   1728 	mla	r0,r4,r6,r0

00000518 e3540004   1729 	cmp	r4,4

                    1730 ;630: 	{


                    1731 

                    1732 ;631: 		//Не лезет в int


                    1733 ;632: 		return 0;


                    1734 

0000051c e2801004   1735 	add	r1,r0,4

                    1736 ;626: {


                    1737 

00000520 e3a00000   1738 	mov	r0,0

00000524 e58d0000   1739 	str	r0,[sp]

                    1740 ;627: 	int result = 0;


                    1741 

                    1742 ;628: 	int insignificantBitCount;


                    1743 ;629: 	if (byteCount > sizeof(int))


                    1744 

00000528 8a000008   1745 	bhi	.L2117

                    1746 ;633: 	}


                    1747 ;634: 


                    1748 ;635: 



                                                                      Page 30
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
                    1749 ;636: 	memcpy(&result, pData, byteCount);


                    1750 

0000052c e1a02004   1751 	mov	r2,r4

00000530 e1a0000d   1752 	mov	r0,sp

00000534 eb000000*  1753 	bl	memcpy

                    1754 ;637: 	//Расширяем знак


                    1755 ;638: 	insignificantBitCount = (sizeof(int) - byteCount) * 8;


                    1756 

00000538 e1a00184   1757 	mov	r0,r4 lsl 3

0000053c e59d1000   1758 	ldr	r1,[sp]

00000540 e2600020   1759 	rsb	r0,r0,32

                    1760 ;639: 	result <<= insignificantBitCount;


                    1761 

00000544 e1a01011   1762 	mov	r1,r1 lsl r0

                    1763 ;640: 	result >>= insignificantBitCount;


                    1764 

00000548 e1a00051   1765 	mov	r0,r1 asr r0

0000054c e58d0000   1766 	str	r0,[sp]

                    1767 ;641: 	return result;


                    1768 

                    1769 .L2117:

00000550 e28dd004   1770 	add	sp,sp,4

00000554 e8bd84f0   1771 	ldmfd	[sp]!,{r4-r7,r10,pc}

                    1772 	.endf	OSCFrame_getAnalogValue

                    1773 	.align	4

                    1774 ;frame	r5	local

                    1775 ;value	r1	local

                    1776 ;byteCount	r4	local

                    1777 ;result	[sp]	local

                    1778 ;insignificantBitCount	r0	local

                    1779 

                    1780 ;oscInfo	r7	param

                    1781 ;pointNum	r6	param

                    1782 ;analogNum	r10	param

                    1783 

                    1784 	.section ".bss","awb"

                    1785 .L2176:

                    1786 	.data

                    1787 	.text

                    1788 

                    1789 

                    1790 	.align	4

                    1791 	.align	4

                    1792 OSCFrame_getBoolValue::

00000558 e92d44f0   1793 	stmfd	[sp]!,{r4-r7,r10,lr}

0000055c e1a06002   1794 	mov	r6,r2

00000560 e1a07000   1795 	mov	r7,r0

00000564 e2870044   1796 	add	r0,r7,68

00000568 eb000000*  1797 	bl	OscWriteBuffer_data

0000056c e1a04000   1798 	mov	r4,r0

00000570 e1a00007   1799 	mov	r0,r7

00000574 ebffff95*  1800 	bl	OSCInfo_getAnalogCount

00000578 e1a0a000   1801 	mov	r10,r0

0000057c e1a00007   1802 	mov	r0,r7

00000580 ebffffb7*  1803 	bl	OSCInfo_getADCSampleSize

00000584 e1a05000   1804 	mov	r5,r0

00000588 e1a00007   1805 	mov	r0,r7

0000058c ebffff89*  1806 	bl	OSCInfo_getPointPerFrameCount

                    1807 ;319: {


                    1808 

                    1809 ;320: 	return 4;



                                                                      Page 31
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
                    1810 

00000590 e000009a   1811 	mul	r0,r10,r0

00000594 e0204095   1812 	mla	r0,r5,r0,r4

00000598 e0800006   1813 	add	r0,r0,r6

0000059c e5d00004   1814 	ldrb	r0,[r0,4]

000005a0 e8bd84f0   1815 	ldmfd	[sp]!,{r4-r7,r10,pc}

                    1816 	.endf	OSCFrame_getBoolValue

                    1817 	.align	4

                    1818 ;frame	r4	local

                    1819 ;value	r0	local

                    1820 ;analogCount	r10	local

                    1821 ;adcSampleSize	r5	local

                    1822 

                    1823 ;oscInfo	r7	param

                    1824 ;sampleNum	none	param

                    1825 ;boolNum	r6	param

                    1826 

                    1827 	.section ".bss","awb"

                    1828 .L2222:

                    1829 	.data

                    1830 	.text

                    1831 

                    1832 

                    1833 	.align	4

                    1834 	.align	4

                    1835 OSCFrame_getTick::

000005a4 e92d40f0   1836 	stmfd	[sp]!,{r4-r7,lr}

000005a8 e24dd004   1837 	sub	sp,sp,4

000005ac e1a06001   1838 	mov	r6,r1

000005b0 e1a04000   1839 	mov	r4,r0

000005b4 e2840044   1840 	add	r0,r4,68

000005b8 eb000000*  1841 	bl	OscWriteBuffer_data

000005bc e1a07000   1842 	mov	r7,r0

000005c0 e1a00004   1843 	mov	r0,r4

000005c4 ebffff69*  1844 	bl	OSCInfo_getADCClkFreq

000005c8 eb000000*  1845 	bl	__utod

000005cc e1a05001   1846 	mov	r5,r1

                    1847 ;319: {


                    1848 

                    1849 ;320: 	return 4;


                    1850 

000005d0 e1a01007   1851 	mov	r1,r7

000005d4 e1a04000   1852 	mov	r4,r0

000005d8 e1a0000d   1853 	mov	r0,sp

000005dc e3a02004   1854 	mov	r2,4

000005e0 eb000000*  1855 	bl	memcpy

000005e4 e59d1000   1856 	ldr	r1,[sp]

000005e8 e1b00001   1857 	movs	r0,r1

000005ec 020000ff   1858 	andeq	r0,r0,255

000005f0 0a00000c   1859 	beq	.L2229

000005f4 eb000000*  1860 	bl	__utod

000005f8 e1a02000   1861 	mov	r2,r0

000005fc e1a00004   1862 	mov	r0,r4

00000600 e1a03001   1863 	mov	r3,r1

00000604 e1a01005   1864 	mov	r1,r5

00000608 eb000000*  1865 	bl	__ddiv

0000060c e1a02000   1866 	mov	r2,r0

00000610 e1a03001   1867 	mov	r3,r1

00000614 e59f1508*  1868 	ldr	r1,.L2291

00000618 e3a00000   1869 	mov	r0,0

0000061c eb000000*  1870 	bl	__ddiv


                                                                      Page 32
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
00000620 e8860003   1871 	stmea	[r6],{r0-r1}

00000624 e3a00001   1872 	mov	r0,1

                    1873 .L2229:

00000628 e28dd004   1874 	add	sp,sp,4

0000062c e8bd80f0   1875 	ldmfd	[sp]!,{r4-r7,pc}

                    1876 	.endf	OSCFrame_getTick

                    1877 	.align	4

                    1878 ;frame	r7	local

                    1879 ;adcTick	[sp]	local

                    1880 ;mainFreq	r4	local

                    1881 

                    1882 ;oscInfo	r4	param

                    1883 ;tick	r6	param

                    1884 

                    1885 	.section ".bss","awb"

                    1886 .L2277:

                    1887 	.data

                    1888 	.text

                    1889 

                    1890 

                    1891 	.align	4

                    1892 	.align	4

                    1893 OSCFrame_getFreq::

00000630 e92d40f0   1894 	stmfd	[sp]!,{r4-r7,lr}

00000634 e24dd004   1895 	sub	sp,sp,4

00000638 e1a06001   1896 	mov	r6,r1

0000063c e1a04000   1897 	mov	r4,r0

00000640 e2840044   1898 	add	r0,r4,68

00000644 eb000000*  1899 	bl	OscWriteBuffer_data

00000648 e1a07000   1900 	mov	r7,r0

0000064c e1a00004   1901 	mov	r0,r4

00000650 ebffff46*  1902 	bl	OSCInfo_getADCClkFreq

00000654 eb000000*  1903 	bl	__utod

00000658 e1a05001   1904 	mov	r5,r1

                    1905 ;319: {


                    1906 

                    1907 ;320: 	return 4;


                    1908 

0000065c e1a01007   1909 	mov	r1,r7

00000660 e1a04000   1910 	mov	r4,r0

00000664 e1a0000d   1911 	mov	r0,sp

00000668 e3a02004   1912 	mov	r2,4

0000066c eb000000*  1913 	bl	memcpy

00000670 e59d1000   1914 	ldr	r1,[sp]

00000674 e1b00001   1915 	movs	r0,r1

00000678 020000ff   1916 	andeq	r0,r0,255

0000067c 0a000008   1917 	beq	.L2292

00000680 eb000000*  1918 	bl	__utod

00000684 e1a02000   1919 	mov	r2,r0

00000688 e1a00004   1920 	mov	r0,r4

0000068c e1a03001   1921 	mov	r3,r1

00000690 e1a01005   1922 	mov	r1,r5

00000694 eb000000*  1923 	bl	__ddiv

00000698 eb000000*  1924 	bl	__dtof

0000069c e5860000   1925 	str	r0,[r6]

000006a0 e3a00001   1926 	mov	r0,1

                    1927 .L2292:

000006a4 e28dd004   1928 	add	sp,sp,4

000006a8 e8bd80f0   1929 	ldmfd	[sp]!,{r4-r7,pc}

                    1930 	.endf	OSCFrame_getFreq

                    1931 	.align	4


                                                                      Page 33
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
                    1932 ;frame	r7	local

                    1933 ;adcFreq	[sp]	local

                    1934 ;mainFreq	r4	local

                    1935 

                    1936 ;oscInfo	r4	param

                    1937 ;freq	r6	param

                    1938 

                    1939 	.section ".bss","awb"

                    1940 .L2341:

                    1941 	.data

                    1942 	.text

                    1943 

                    1944 

                    1945 ;808: 


                    1946 ;809: bool OSCInfo_initContent(OSCInfoStruct *oscInfo)


                    1947 	.align	4

                    1948 	.align	4

                    1949 OSCInfo_initContent::

000006ac e92d4ff0   1950 	stmfd	[sp]!,{r4-fp,lr}

000006b0 e3a086ff   1951 	mov	r8,255<<20

000006b4 e28885c0   1952 	add	r8,r8,3<<28

                    1953 ;810: {


                    1954 

                    1955 ;811: 	return analogInit(oscInfo) && boolInit(oscInfo);


                    1956 

000006b8 e3a07000   1957 	mov	r7,0

000006bc e1a06007   1958 	mov	r6,r7

000006c0 e24dd024   1959 	sub	sp,sp,36

000006c4 e28d1010   1960 	add	r1,sp,16

000006c8 e88101c0   1961 	stmea	[r1],{r6-r8}

                    1962 ;788: {


                    1963 

                    1964 ;789: 	size_t count = OSCInfo_getAnalogCount(oscInfo);


                    1965 

000006cc e1a04000   1966 	mov	r4,r0

000006d0 ebffff3e*  1967 	bl	OSCInfo_getAnalogCount

000006d4 e1a05000   1968 	mov	r5,r0

                    1969 ;790: 	unsigned short *pIndexs = getAnalogContentPtr(oscInfo);


                    1970 

                    1971 ;752: {


                    1972 

                    1973 ;753: 	unsigned char *data = OscWriteBuffer_data(&oscInfo->wbContent);


                    1974 

000006d8 e2840030   1975 	add	r0,r4,48

000006dc eb000000*  1976 	bl	OscWriteBuffer_data

                    1977 ;754: 	unsigned short *pIndexs = (unsigned short*)data;


                    1978 

                    1979 ;755: 	return pIndexs;


                    1980 

000006e0 e1a0a000   1981 	mov	r10,r0

                    1982 ;791: 	unsigned int i;


                    1983 ;792: 	OSCInfoAnalog *pAnalog = oscInfo->pAnalog;


                    1984 

000006e4 e5940058   1985 	ldr	r0,[r4,88]

000006e8 e3550000   1986 	cmp	r5,0

000006ec e58d0008   1987 	str	r0,[sp,8]

                    1988 ;793: 	


                    1989 ;794: 	for (i = 0; i < count; ++i)


                    1990 

000006f0 a1a08005   1991 	movge	r8,r5

000006f4 b3a08000   1992 	movlt	r8,0


                                                                      Page 34
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
000006f8 e1b070a8   1993 	movs	r7,r8 lsr 1

000006fc 0a00005b   1994 	beq	.L2529

                    1995 .L2530:

00000700 e1a00086   1996 	mov	r0,r6 lsl 1

00000704 e19a00b0   1997 	ldrh	r0,[r10,r0]

00000708 eb000000*  1998 	bl	OSCDescr_findDescrAnalogItem

0000070c e3500000   1999 	cmp	r0,0

00000710 0a0000e0   2000 	beq	.L2358

00000714 e59d2008   2001 	ldr	r2,[sp,8]

00000718 e1a09206   2002 	mov	r9,r6 lsl 4

0000071c e7890002   2003 	str	r0,[r9,r2]

00000720 e5940058   2004 	ldr	r0,[r4,88]

00000724 e0895000   2005 	add	r5,r9,r0

00000728 e4950008   2006 	ldr	r0,[r5],8

0000072c e5d0b004   2007 	ldrb	fp,[r0,4]

00000730 e1a00004   2008 	mov	r0,r4

00000734 ebffff50*  2009 	bl	OSCInfo_getADCFractionSize

00000738 e08b0000   2010 	add	r0,fp,r0

0000073c e2400001   2011 	sub	r0,r0,1

00000740 e3a0b001   2012 	mov	fp,1

00000744 e1a0001b   2013 	mov	r0,fp lsl r0

00000748 e2400001   2014 	sub	r0,r0,1

0000074c e2602000   2015 	rsb	r2,r0,0

00000750 e8850005   2016 	stmea	[r5],{r0,r2}

00000754 e5940058   2017 	ldr	r0,[r4,88]

00000758 e0895000   2018 	add	r5,r9,r0

0000075c e5950000   2019 	ldr	r0,[r5]

00000760 e5900000   2020 	ldr	r0,[r0]

00000764 eb000000*  2021 	bl	__ftod

00000768 e58d1020   2022 	str	r1,[sp,32]

0000076c e58d001c   2023 	str	r0,[sp,28]

00000770 e1a00004   2024 	mov	r0,r4

00000774 ebffff40*  2025 	bl	OSCInfo_getADCFractionSize

00000778 e1a0001b   2026 	mov	r0,fp lsl r0

0000077c eb000000*  2027 	bl	__itod

00000780 e1a02000   2028 	mov	r2,r0

00000784 e1a03001   2029 	mov	r3,r1

00000788 e59d001c   2030 	ldr	r0,[sp,28]

0000078c e59d1020   2031 	ldr	r1,[sp,32]

00000790 eb000000*  2032 	bl	__dmul

00000794 e1a03001   2033 	mov	r3,r1

00000798 e1a02000   2034 	mov	r2,r0

0000079c e59d0014   2035 	ldr	r0,[sp,20]

000007a0 e59d1018   2036 	ldr	r1,[sp,24]

000007a4 e2866001   2037 	add	r6,r6,1

000007a8 eb000000*  2038 	bl	__ddiv

000007ac eb000000*  2039 	bl	__dtof

000007b0 e5850004   2040 	str	r0,[r5,4]

000007b4 e1a00086   2041 	mov	r0,r6 lsl 1

000007b8 e1a0500a   2042 	mov	r5,r10

000007bc e19500b0   2043 	ldrh	r0,[r5,r0]

000007c0 eb000000*  2044 	bl	OSCDescr_findDescrAnalogItem

000007c4 e3500000   2045 	cmp	r0,0

000007c8 0a0000b2   2046 	beq	.L2358

000007cc e59d2008   2047 	ldr	r2,[sp,8]

000007d0 e7820206   2048 	str	r0,[r2,r6 lsl 4]

000007d4 e5940058   2049 	ldr	r0,[r4,88]

000007d8 e0805206   2050 	add	r5,r0,r6 lsl 4

000007dc e4950008   2051 	ldr	r0,[r5],8

000007e0 e5d0b004   2052 	ldrb	fp,[r0,4]

000007e4 e1a00004   2053 	mov	r0,r4


                                                                      Page 35
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
000007e8 ebffff23*  2054 	bl	OSCInfo_getADCFractionSize

000007ec e08b0000   2055 	add	r0,fp,r0

000007f0 e2400001   2056 	sub	r0,r0,1

000007f4 e3a0b001   2057 	mov	fp,1

000007f8 e1a0001b   2058 	mov	r0,fp lsl r0

000007fc e2400001   2059 	sub	r0,r0,1

00000800 e2602000   2060 	rsb	r2,r0,0

00000804 e8850005   2061 	stmea	[r5],{r0,r2}

00000808 e5940058   2062 	ldr	r0,[r4,88]

0000080c e0805206   2063 	add	r5,r0,r6 lsl 4

00000810 e5950000   2064 	ldr	r0,[r5]

00000814 e5900000   2065 	ldr	r0,[r0]

00000818 eb000000*  2066 	bl	__ftod

0000081c e58d1020   2067 	str	r1,[sp,32]

00000820 e58d001c   2068 	str	r0,[sp,28]

00000824 e1a00004   2069 	mov	r0,r4

00000828 ebffff13*  2070 	bl	OSCInfo_getADCFractionSize

0000082c e1a0001b   2071 	mov	r0,fp lsl r0

00000830 eb000000*  2072 	bl	__itod

00000834 e1a02000   2073 	mov	r2,r0

00000838 e1a03001   2074 	mov	r3,r1

0000083c e59d1020   2075 	ldr	r1,[sp,32]

00000840 e59d001c   2076 	ldr	r0,[sp,28]

00000844 eb000000*  2077 	bl	__dmul

00000848 e1a03001   2078 	mov	r3,r1

0000084c e1a02000   2079 	mov	r2,r0

00000850 e59d0014   2080 	ldr	r0,[sp,20]

00000854 e59d1018   2081 	ldr	r1,[sp,24]

00000858 e2866001   2082 	add	r6,r6,1

0000085c eb000000*  2083 	bl	__ddiv

00000860 eb000000*  2084 	bl	__dtof

00000864 e5850004   2085 	str	r0,[r5,4]

00000868 e2577001   2086 	subs	r7,r7,1

0000086c 1affffa3   2087 	bne	.L2530

                    2088 .L2529:

00000870 e2187001   2089 	ands	r7,r8,1

00000874 0a000030   2090 	beq	.L2371

00000878 e1a08206   2091 	mov	r8,r6 lsl 4

                    2092 .L2540:

0000087c e1a00086   2093 	mov	r0,r6 lsl 1

00000880 e19a00b0   2094 	ldrh	r0,[r10,r0]

00000884 eb000000*  2095 	bl	OSCDescr_findDescrAnalogItem

00000888 e3500000   2096 	cmp	r0,0

0000088c 0a000081   2097 	beq	.L2358

00000890 e59d1008   2098 	ldr	r1,[sp,8]

00000894 e1a05008   2099 	mov	r5,r8

00000898 e7810005   2100 	str	r0,[r1,r5]

0000089c e5940058   2101 	ldr	r0,[r4,88]

000008a0 e7b50000   2102 	ldr	r0,[r5,r0]!

000008a4 e5d0b004   2103 	ldrb	fp,[r0,4]

000008a8 e1a00004   2104 	mov	r0,r4

000008ac ebfffef2*  2105 	bl	OSCInfo_getADCFractionSize

000008b0 e08b0000   2106 	add	r0,fp,r0

000008b4 e2400001   2107 	sub	r0,r0,1

000008b8 e3a0b001   2108 	mov	fp,1

000008bc e1a0001b   2109 	mov	r0,fp lsl r0

000008c0 e2400001   2110 	sub	r0,r0,1

000008c4 e5850008   2111 	str	r0,[r5,8]

000008c8 e2600000   2112 	rsb	r0,r0,0

000008cc e585000c   2113 	str	r0,[r5,12]

000008d0 e5940058   2114 	ldr	r0,[r4,88]


                                                                      Page 36
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
000008d4 e0885000   2115 	add	r5,r8,r0

000008d8 e5950000   2116 	ldr	r0,[r5]

000008dc e5900000   2117 	ldr	r0,[r0]

000008e0 eb000000*  2118 	bl	__ftod

000008e4 e58d1020   2119 	str	r1,[sp,32]

000008e8 e58d001c   2120 	str	r0,[sp,28]

000008ec e1a00004   2121 	mov	r0,r4

000008f0 ebfffee1*  2122 	bl	OSCInfo_getADCFractionSize

000008f4 e1a0001b   2123 	mov	r0,fp lsl r0

000008f8 eb000000*  2124 	bl	__itod

000008fc e1a02000   2125 	mov	r2,r0

00000900 e1a03001   2126 	mov	r3,r1

00000904 e59d1020   2127 	ldr	r1,[sp,32]

00000908 e59d001c   2128 	ldr	r0,[sp,28]

0000090c eb000000*  2129 	bl	__dmul

00000910 e1a03001   2130 	mov	r3,r1

00000914 e1a02000   2131 	mov	r2,r0

00000918 e59d0014   2132 	ldr	r0,[sp,20]

0000091c e59d1018   2133 	ldr	r1,[sp,24]

00000920 e2866001   2134 	add	r6,r6,1

00000924 eb000000*  2135 	bl	__ddiv

00000928 eb000000*  2136 	bl	__dtof

0000092c e5850004   2137 	str	r0,[r5,4]

00000930 e2888010   2138 	add	r8,r8,16

00000934 e2577001   2139 	subs	r7,r7,1

00000938 1affffcf   2140 	bne	.L2540

                    2141 .L2371:

                    2142 ;805: 	}


                    2143 ;806: 	return true;


                    2144 

                    2145 ;766: {


                    2146 

                    2147 ;767: 	size_t count = OSCInfo_getBoolCount(oscInfo);


                    2148 

0000093c e1a00004   2149 	mov	r0,r4

00000940 ebfffea8*  2150 	bl	OSCInfo_getBoolCount

00000944 e1a07000   2151 	mov	r7,r0

                    2152 ;768: 	unsigned short *pOffsets = getBoolContentPtr(oscInfo);


                    2153 

                    2154 ;759: {


                    2155 

                    2156 ;760: 	size_t offset = OSCInfo_getAnalogCount(oscInfo) * getAnalogContentIdSize(oscInfo);


                    2157 

                    2158 ;260: {


                    2159 

                    2160 ;261: 	return 2;


                    2161 

00000948 e1a00004   2162 	mov	r0,r4

0000094c ebfffe9f*  2163 	bl	OSCInfo_getAnalogCount

00000950 e1a05000   2164 	mov	r5,r0

                    2165 ;761: 	unsigned char *data = OscWriteBuffer_data(&oscInfo->wbContent);


                    2166 

00000954 e2840030   2167 	add	r0,r4,48

00000958 eb000000*  2168 	bl	OscWriteBuffer_data

                    2169 ;762: 	return (unsigned short*)(data + offset);


                    2170 

0000095c e0805085   2171 	add	r5,r0,r5 lsl 1

                    2172 ;769: 


                    2173 ;770: 	unsigned int i;


                    2174 ;771: 	OSCInfoBool *pBool = oscInfo->pBool;


                    2175 


                                                                      Page 37
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
00000960 e594605c   2176 	ldr	r6,[r4,92]

                    2177 ;772: 


                    2178 ;773: 


                    2179 ;774: 	for (i = 0; i < count; ++i)


                    2180 

00000964 e3a04000   2181 	mov	r4,0

00000968 e3570000   2182 	cmp	r7,0

0000096c a1a0b007   2183 	movge	fp,r7

00000970 b3a0b000   2184 	movlt	fp,0

00000974 e1b071ab   2185 	movs	r7,fp lsr 3

00000978 0a000039   2186 	beq	.L2488

                    2187 .L2489:

0000097c e1a00084   2188 	mov	r0,r4 lsl 1

00000980 e19500b0   2189 	ldrh	r0,[r5,r0]

00000984 eb000000*  2190 	bl	OSCDescr_findDescrBoolItem

00000988 e3500000   2191 	cmp	r0,0

0000098c 0a000041   2192 	beq	.L2358

00000990 e7860104   2193 	str	r0,[r6,r4 lsl 2]

00000994 e2844001   2194 	add	r4,r4,1

00000998 e1a00084   2195 	mov	r0,r4 lsl 1

0000099c e19500b0   2196 	ldrh	r0,[r5,r0]

000009a0 eb000000*  2197 	bl	OSCDescr_findDescrBoolItem

000009a4 e3500000   2198 	cmp	r0,0

000009a8 0a00003a   2199 	beq	.L2358

000009ac e7860104   2200 	str	r0,[r6,r4 lsl 2]

000009b0 e2844001   2201 	add	r4,r4,1

000009b4 e1a00084   2202 	mov	r0,r4 lsl 1

000009b8 e19500b0   2203 	ldrh	r0,[r5,r0]

000009bc eb000000*  2204 	bl	OSCDescr_findDescrBoolItem

000009c0 e3500000   2205 	cmp	r0,0

000009c4 0a000033   2206 	beq	.L2358

000009c8 e7860104   2207 	str	r0,[r6,r4 lsl 2]

000009cc e2844001   2208 	add	r4,r4,1

000009d0 e1a00084   2209 	mov	r0,r4 lsl 1

000009d4 e19500b0   2210 	ldrh	r0,[r5,r0]

000009d8 eb000000*  2211 	bl	OSCDescr_findDescrBoolItem

000009dc e3500000   2212 	cmp	r0,0

000009e0 0a00002c   2213 	beq	.L2358

000009e4 e7860104   2214 	str	r0,[r6,r4 lsl 2]

000009e8 e2844001   2215 	add	r4,r4,1

000009ec e1a00084   2216 	mov	r0,r4 lsl 1

000009f0 e19500b0   2217 	ldrh	r0,[r5,r0]

000009f4 eb000000*  2218 	bl	OSCDescr_findDescrBoolItem

000009f8 e3500000   2219 	cmp	r0,0

000009fc 0a000025   2220 	beq	.L2358

00000a00 e7860104   2221 	str	r0,[r6,r4 lsl 2]

00000a04 e2844001   2222 	add	r4,r4,1

00000a08 e1a00084   2223 	mov	r0,r4 lsl 1

00000a0c e19500b0   2224 	ldrh	r0,[r5,r0]

00000a10 eb000000*  2225 	bl	OSCDescr_findDescrBoolItem

00000a14 e3500000   2226 	cmp	r0,0

00000a18 0a00001e   2227 	beq	.L2358

00000a1c e7860104   2228 	str	r0,[r6,r4 lsl 2]

00000a20 e2844001   2229 	add	r4,r4,1

00000a24 e1a00084   2230 	mov	r0,r4 lsl 1

00000a28 e19500b0   2231 	ldrh	r0,[r5,r0]

00000a2c eb000000*  2232 	bl	OSCDescr_findDescrBoolItem

00000a30 e3500000   2233 	cmp	r0,0

00000a34 0a000017   2234 	beq	.L2358

00000a38 e7860104   2235 	str	r0,[r6,r4 lsl 2]

00000a3c e2844001   2236 	add	r4,r4,1


                                                                      Page 38
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
00000a40 e1a00084   2237 	mov	r0,r4 lsl 1

00000a44 e19500b0   2238 	ldrh	r0,[r5,r0]

00000a48 eb000000*  2239 	bl	OSCDescr_findDescrBoolItem

00000a4c e3500000   2240 	cmp	r0,0

00000a50 0a000010   2241 	beq	.L2358

00000a54 e7860104   2242 	str	r0,[r6,r4 lsl 2]

00000a58 e2844001   2243 	add	r4,r4,1

00000a5c e2577001   2244 	subs	r7,r7,1

00000a60 1affffc5   2245 	bne	.L2489

                    2246 .L2488:

00000a64 e21b7007   2247 	ands	r7,fp,7

00000a68 0a000008   2248 	beq	.L2368

                    2249 .L2523:

00000a6c e1a00084   2250 	mov	r0,r4 lsl 1

00000a70 e19500b0   2251 	ldrh	r0,[r5,r0]

00000a74 eb000000*  2252 	bl	OSCDescr_findDescrBoolItem

00000a78 e3500000   2253 	cmp	r0,0

00000a7c 0a000005   2254 	beq	.L2358

00000a80 e7860104   2255 	str	r0,[r6,r4 lsl 2]

00000a84 e2844001   2256 	add	r4,r4,1

00000a88 e2577001   2257 	subs	r7,r7,1

00000a8c 1afffff6   2258 	bne	.L2523

                    2259 .L2368:

                    2260 ;783: 	}


                    2261 ;784: 	return true;


                    2262 

00000a90 e3a00001   2263 	mov	r0,1

00000a94 e58d0010   2264 	str	r0,[sp,16]

                    2265 .L2358:

00000a98 e59d1010   2266 	ldr	r1,[sp,16]

00000a9c e20100ff   2267 	and	r0,r1,255

00000aa0 e28dd024   2268 	add	sp,sp,36

00000aa4 e8bd8ff0   2269 	ldmfd	[sp]!,{r4-fp,pc}

                    2270 	.endf	OSCInfo_initContent

                    2271 	.align	4

                    2272 ;count	r5	local

                    2273 ;pIndexs	r10	local

                    2274 ;i	r6	local

                    2275 ;pAnalog	[sp,8]	local

                    2276 ;descr	r0	local

                    2277 ;pAnalog	r5	local

                    2278 ;adcIntPartSize	fp	local

                    2279 ;comtradeSampleSize	r0	local

                    2280 ;adcMaxAbsValue	r0	local

                    2281 ;pAnalog	r5	local

                    2282 ;count	r7	local

                    2283 ;pOffsets	r5	local

                    2284 ;i	r4	local

                    2285 ;pBool	r6	local

                    2286 ;offset	r5	local

                    2287 ;descr	r0	local

                    2288 ;secondaryCoef	[sp,28]	local

                    2289 

                    2290 ;oscInfo	r4	param

                    2291 

                    2292 	.section ".bss","awb"

                    2293 .L2982:

                    2294 	.data

                    2295 	.text

                    2296 

                    2297 ;812: }



                                                                      Page 39
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
                    2298 

                    2299 ;813: 


                    2300 ;814: OscDescrAnalog * OSCInfo_getAnalog(OSCInfoStruct *oscInfo, int index)


                    2301 	.align	4

                    2302 	.align	4

                    2303 OSCInfo_getAnalog::

                    2304 ;815: {


                    2305 

                    2306 ;816: 	return oscInfo->pAnalog[index].pDescr;


                    2307 

00000aa8 e5900058   2308 	ldr	r0,[r0,88]

00000aac e7900201   2309 	ldr	r0,[r0,r1 lsl 4]

00000ab0 e12fff1e*  2310 	ret	

                    2311 	.endf	OSCInfo_getAnalog

                    2312 	.align	4

                    2313 

                    2314 ;oscInfo	r0	param

                    2315 ;index	r1	param

                    2316 

                    2317 	.section ".bss","awb"

                    2318 .L3089:

                    2319 	.data

                    2320 	.text

                    2321 

                    2322 ;817: }


                    2323 

                    2324 ;818: 


                    2325 ;819: OscDescrBool * OSCInfo_getBool(OSCInfoStruct *oscInfo, int index)


                    2326 	.align	4

                    2327 	.align	4

                    2328 OSCInfo_getBool::

                    2329 ;820: {


                    2330 

                    2331 ;821: 	return oscInfo->pBool[index].pDescr;


                    2332 

00000ab4 e590005c   2333 	ldr	r0,[r0,92]

00000ab8 e7900101   2334 	ldr	r0,[r0,r1 lsl 2]

00000abc e12fff1e*  2335 	ret	

                    2336 	.endf	OSCInfo_getBool

                    2337 	.align	4

                    2338 

                    2339 ;oscInfo	r0	param

                    2340 ;index	r1	param

                    2341 

                    2342 	.section ".bss","awb"

                    2343 .L3121:

                    2344 	.data

                    2345 	.text

                    2346 

                    2347 ;822: }


                    2348 

                    2349 ;823: 


                    2350 ;824: float OSCInfo_getAnalogCft(OSCInfoStruct *oscInfo, int analogNum)


                    2351 	.align	4

                    2352 	.align	4

                    2353 OSCInfo_getAnalogCft::

                    2354 ;825: {


                    2355 

                    2356 ;826: 	OSCInfoAnalog *pAnalog = &oscInfo->pAnalog[analogNum];


                    2357 

00000ac0 e5900058   2358 	ldr	r0,[r0,88]


                                                                      Page 40
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
00000ac4 e0800201   2359 	add	r0,r0,r1 lsl 4

                    2360 ;827: 	return pAnalog->cft;


                    2361 

00000ac8 e5900004   2362 	ldr	r0,[r0,4]

00000acc e12fff1e*  2363 	ret	

                    2364 	.endf	OSCInfo_getAnalogCft

                    2365 	.align	4

                    2366 ;pAnalog	r0	local

                    2367 

                    2368 ;oscInfo	r0	param

                    2369 ;analogNum	r1	param

                    2370 

                    2371 	.section ".bss","awb"

                    2372 .L3150:

                    2373 	.data

                    2374 	.text

                    2375 

                    2376 ;828: }


                    2377 

                    2378 ;829: 


                    2379 ;830: int OSCInfo_getAnalogMax(OSCInfoStruct *oscInfo, int index)


                    2380 	.align	4

                    2381 	.align	4

                    2382 OSCInfo_getAnalogMax::

                    2383 ;831: {


                    2384 

                    2385 ;832: 	OSCInfoAnalog *pAnalog = &oscInfo->pAnalog[index];


                    2386 

00000ad0 e5900058   2387 	ldr	r0,[r0,88]

00000ad4 e0800201   2388 	add	r0,r0,r1 lsl 4

                    2389 ;833: 	return pAnalog->maxValue;


                    2390 

00000ad8 e5900008   2391 	ldr	r0,[r0,8]

00000adc e12fff1e*  2392 	ret	

                    2393 	.endf	OSCInfo_getAnalogMax

                    2394 	.align	4

                    2395 ;pAnalog	r0	local

                    2396 

                    2397 ;oscInfo	r0	param

                    2398 ;index	r1	param

                    2399 

                    2400 	.section ".bss","awb"

                    2401 .L3182:

                    2402 	.data

                    2403 	.text

                    2404 

                    2405 ;834: }


                    2406 

                    2407 ;835: 


                    2408 ;836: int OSCInfo_getAnalogMin(OSCInfoStruct *oscInfo, int index)


                    2409 	.align	4

                    2410 	.align	4

                    2411 OSCInfo_getAnalogMin::

                    2412 ;837: {


                    2413 

                    2414 ;838: 	OSCInfoAnalog *pAnalog = &oscInfo->pAnalog[index];


                    2415 

00000ae0 e5900058   2416 	ldr	r0,[r0,88]

00000ae4 e0800201   2417 	add	r0,r0,r1 lsl 4

                    2418 ;839: 	return pAnalog->minValue;


                    2419 


                                                                      Page 41
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
00000ae8 e590000c   2420 	ldr	r0,[r0,12]

00000aec e12fff1e*  2421 	ret	

                    2422 	.endf	OSCInfo_getAnalogMin

                    2423 	.align	4

                    2424 ;pAnalog	r0	local

                    2425 

                    2426 ;oscInfo	r0	param

                    2427 ;index	r1	param

                    2428 

                    2429 	.section ".bss","awb"

                    2430 .L3214:

                    2431 	.data

                    2432 	.text

                    2433 

                    2434 ;840: }


                    2435 	.align	4

                    2436 	.align	4

                    2437 getHeaderSize3:

                    2438 ;226: {


                    2439 

                    2440 ;227: 	return sizeof(OSCInfoStruct3);


                    2441 

00000af0 e3a00024   2442 	mov	r0,36

00000af4 e12fff1e*  2443 	ret	

                    2444 	.endf	getHeaderSize3

                    2445 	.align	4

                    2446 

                    2447 ;oscInfo	none	param

                    2448 

                    2449 	.section ".bss","awb"

                    2450 .L3246:

                    2451 	.data

                    2452 	.text

                    2453 	.align	4

                    2454 	.align	4

                    2455 getHeaderSize4:

                    2456 ;230: {


                    2457 

                    2458 ;231: 	return sizeof(OSCInfoStruct4);


                    2459 

00000af8 e3a0002c   2460 	mov	r0,44

00000afc e12fff1e*  2461 	ret	

                    2462 	.endf	getHeaderSize4

                    2463 	.align	4

                    2464 

                    2465 ;oscInfo	none	param

                    2466 

                    2467 	.section ".bss","awb"

                    2468 .L3278:

                    2469 	.data

                    2470 	.text

                    2471 	.align	4

                    2472 	.align	4

                    2473 getADCSampleSize3:

                    2474 ;236: {


                    2475 

                    2476 ;237: 	return 2;


                    2477 

00000b00 e3a00002   2478 	mov	r0,2

00000b04 e12fff1e*  2479 	ret	

                    2480 	.endf	getADCSampleSize3


                                                                      Page 42
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
                    2481 	.align	4

                    2482 

                    2483 ;oscInfo	none	param

                    2484 

                    2485 	.section ".bss","awb"

                    2486 .L3310:

                    2487 	.data

                    2488 	.text

                    2489 	.align	4

                    2490 	.align	4

                    2491 getADCFractionSize3:

                    2492 ;240: {


                    2493 

                    2494 ;241: 	return 0;


                    2495 

00000b08 e3a00000   2496 	mov	r0,0

00000b0c e12fff1e*  2497 	ret	

                    2498 	.endf	getADCFractionSize3

                    2499 	.align	4

                    2500 

                    2501 ;oscInfo	none	param

                    2502 

                    2503 	.section ".bss","awb"

                    2504 .L3342:

                    2505 	.data

                    2506 	.text

                    2507 	.align	4

                    2508 .L900:

00000b10 00000000*  2509 	.data.w	.L888

                    2510 	.type	.L900,$object

                    2511 	.size	.L900,4

                    2512 

                    2513 .L901:

00000b14 00000000*  2514 	.data.w	oscHeaderBuf

                    2515 	.type	.L901,$object

                    2516 	.size	.L901,4

                    2517 

                    2518 .L902:

00000b18 00000000*  2519 	.data.w	csHeaderBuf

                    2520 	.type	.L902,$object

                    2521 	.size	.L902,4

                    2522 

                    2523 .L1382:

00000b1c 00000000*  2524 	.data.w	oscInfoV3

                    2525 	.type	.L1382,$object

                    2526 	.size	.L1382,4

                    2527 

                    2528 .L1383:

00000b20 00000000*  2529 	.data.w	oscInfoV4

                    2530 	.type	.L1383,$object

                    2531 	.size	.L1383,4

                    2532 

                    2533 .L2291:

00000b24 412e8480   2534 	.data.w	0x412e8480

                    2535 	.type	.L2291,$object

                    2536 	.size	.L2291,4

                    2537 

                    2538 	.align	4

                    2539 

                    2540 	.data

                    2541 .L3364:


                                                                      Page 43
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
                    2542 	.globl	oscInfoV3

00000000 00000000*  2543 oscInfoV3:	.data.w	getUTCDate

00000004 00000000*  2544 	.data.w	getDateMS

00000008 00000000*  2545 	.data.w	getOscVersion

0000000c 00000000*  2546 	.data.w	getADCClkFreq

00000010 00000000*  2547 	.data.w	getPrehistFrameCount3

00000014 00000000*  2548 	.data.w	getPrehistFirstFrameNum3

00000018 00000000*  2549 	.data.w	getPointPerFrameCount3

0000001c 00000000*  2550 	.data.w	getAnalogInCount3

00000020 00000000*  2551 	.data.w	getDigInCount3

00000024 00000000*  2552 	.data.w	getOscSize

00000028 00000000*  2553 	.data.w	getHeaderSize3

0000002c 00000000*  2554 	.data.w	getADCSampleSize3

00000030 00000000*  2555 	.data.w	getADCFractionSize3

                    2556 	.type	oscInfoV3,$object

                    2557 	.size	oscInfoV3,52

                    2558 .L3365:

                    2559 	.globl	oscInfoV4

00000034 00000000*  2560 oscInfoV4:	.data.w	getUTCDate

00000038 00000000*  2561 	.data.w	getDateMS

0000003c 00000000*  2562 	.data.w	getOscVersion

00000040 00000000*  2563 	.data.w	getADCClkFreq

00000044 00000000*  2564 	.data.w	getPrehistFrameCount4

00000048 00000000*  2565 	.data.w	getPrehistFirstFrameNum4

0000004c 00000000*  2566 	.data.w	getPointPerFrameCount4

00000050 00000000*  2567 	.data.w	getAnalogInCount4

00000054 00000000*  2568 	.data.w	getDigInCount4

00000058 00000000*  2569 	.data.w	getOscSize

0000005c 00000000*  2570 	.data.w	getHeaderSize4

00000060 00000000*  2571 	.data.w	getADCSampleSize4

00000064 00000000*  2572 	.data.w	getADCFractionSize4

                    2573 	.type	oscInfoV4,$object

                    2574 	.size	oscInfoV4,52

                    2575 	.comm	oscHeaderBuf,20,4

                    2576 	.type	oscHeaderBuf,$object

                    2577 	.size	oscHeaderBuf,20

                    2578 	.comm	csHeaderBuf,4,4

                    2579 	.type	csHeaderBuf,$object

                    2580 	.size	csHeaderBuf,4

                    2581 	.ghsnote version,6

                    2582 	.ghsnote tools,3

                    2583 	.ghsnote options,0

                    2584 	.text

                    2585 	.align	4

                    2586 	.data

                    2587 	.align	4

                    2588 	.section ".bss","awb"

                    2589 	.align	4

                    2590 	.text

