                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_buk1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=infoReport.c -o gh_buk1.o -list=infoReport.lst C:\Users\<USER>\AppData\Local\Temp\gh_buk1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_buk1.s
Source File: infoReport.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile infoReport.c

                      10 ;		-o infoReport.o

                      11 ;Source File:   infoReport.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:55:49 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "infoReport.h"


                      21 ;2: 


                      22 ;3: #include "AsnEncoding.h"


                      23 ;4: #include "bufViewBER.h"


                      24 ;5: #include "mms.h"


                      25 ;6: #include "iedTree/iedEntity.h"


                      26 ;7: #include "iedTree/iedObjects.h"


                      27 ;8: #include <debug.h>


                      28 ;9: 


                      29 ;10: #pragma alignvar (4)


                      30 ;11: 


                      31 ;12: //ApplError information report variable access specification


                      32 ;13: //Эта информация неизменна и всегда содержит


                      33 ;14: //строку "LastApplError"


                      34 ;15: uint8_t applErrorVarSpec[] = {


                      35 ;16: 0xA0, 0x13, 0x30, 0x11, 0xA0, 0x0F, 0x80, 0x0D,


                      36 ;17: 0x4C, 0x61, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6C,


                      37 ;18: 0x45, 0x72, 0x72, 0x6F, 0x72 };


                      38 ;19: 


                      39 ;20: char *lastApplErrorName = "LastApplError";


                      40 ;21: 


                      41 ;22: 


                      42 ;23: typedef struct


                      43 ;24: {


                      44 ;25:     size_t cntrlObjName;


                      45 ;26:     size_t error;


                      46 ;27:     size_t originFields;


                      47 ;28:     size_t origin;


                      48 ;29:     size_t ctlNum;


                      49 ;30:     size_t addCause;


                      50 ;31:     size_t allFields;



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_buk1.s
                      51 ;32:     size_t fullStruct;


                      52 ;33: } LastApplErrorSizes;


                      53 ;34: 


                      54 ;35: // Имя объекта управления (скорее всего pos) при при передаче


                      55 ;36: // negative CommandTermination.


                      56 ;37: // Используется из потока отчётов reportsThread


                      57 ;38: static uint8_t ctrlObjNameBuf[MAX_OBJECT_REFERENCE];


                      58 ;39: 


                      59 ;40: static void calcLastApplErrSizes(LastApplErrorSizes* sizes,


                      60 	.text

                      61 	.align	4

                      62 calcLastApplErrSizes:

00000000 e92d4cf0     63 	stmfd	[sp]!,{r4-r7,r10-fp,lr}

00000004 e1a0b001     64 	mov	fp,r1

00000008 e1a05002     65 	mov	r5,r2

0000000c e1a06003     66 	mov	r6,r3

00000010 e59d701c     67 	ldr	r7,[sp,28]

00000014 e1a04000     68 	mov	r4,r0

00000018 e5dd0024     69 	ldrb	r0,[sp,36]

0000001c e5dda020     70 	ldrb	r10,[sp,32]

                      71 ;41:                                  StringView* cntrlObj, uint8_t error, uint8_t orCat,


                      72 ;42:                                  StringView* orIdent, uint8_t ctlNum, uint8_t addCause)


                      73 ;43: {


                      74 

                      75 ;44:     size_t orIdentSize;


                      76 ;45:     size_t orCatSize;


                      77 ;46: 


                      78 ;47:     sizes->addCause = BerEncoder_uint32determineEncodedSizeTL(addCause);


                      79 

00000020 eb000000*    80 	bl	BerEncoder_uint32determineEncodedSizeTL

00000024 e5840014     81 	str	r0,[r4,20]

                      82 ;48:     sizes->ctlNum = BerEncoder_uint32determineEncodedSizeTL(ctlNum);


                      83 

00000028 e1a0000a     84 	mov	r0,r10

0000002c eb000000*    85 	bl	BerEncoder_uint32determineEncodedSizeTL

00000030 e5840010     86 	str	r0,[r4,16]

                      87 ;49: 


                      88 ;50:     orIdentSize = BerEncoder_determineFullObjectSize(orIdent->len);


                      89 

00000034 e5970000     90 	ldr	r0,[r7]

00000038 eb000000*    91 	bl	BerEncoder_determineFullObjectSize

0000003c e1a07000     92 	mov	r7,r0

                      93 ;51:     orCatSize = BerEncoder_uint32determineEncodedSizeTL(orCat);


                      94 

00000040 e1a00006     95 	mov	r0,r6

00000044 eb000000*    96 	bl	BerEncoder_uint32determineEncodedSizeTL

                      97 ;52:     sizes->originFields = orIdentSize + orCatSize;


                      98 

00000048 e0800007     99 	add	r0,r0,r7

0000004c e5840008    100 	str	r0,[r4,8]

                     101 ;53: 


                     102 ;54:     sizes->origin = BerEncoder_determineFullObjectSize(orCatSize + orIdentSize);


                     103 

00000050 eb000000*   104 	bl	BerEncoder_determineFullObjectSize

00000054 e584000c    105 	str	r0,[r4,12]

                     106 ;55:     sizes->error = BerEncoder_uint32determineEncodedSizeTL(error);


                     107 

00000058 e1a00005    108 	mov	r0,r5

0000005c eb000000*   109 	bl	BerEncoder_uint32determineEncodedSizeTL

00000060 e5840004    110 	str	r0,[r4,4]

                     111 ;56:     sizes->cntrlObjName = BerEncoder_determineFullObjectSize(cntrlObj->len);



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_buk1.s
                     112 

00000064 e59b0000    113 	ldr	r0,[fp]

00000068 eb000000*   114 	bl	BerEncoder_determineFullObjectSize

0000006c e484000c    115 	str	r0,[r4],12

                     116 ;57: 


                     117 ;58:     sizes->allFields = sizes->cntrlObjName + sizes->error + sizes->origin


                     118 

00000070 e5141008    119 	ldr	r1,[r4,-8]

00000074 e8940068    120 	ldmfd	[r4],{r3,r5-r6}

00000078 e0811006    121 	add	r1,r1,r6

0000007c e0832001    122 	add	r2,r3,r1

00000080 e0851002    123 	add	r1,r5,r2

00000084 e0800001    124 	add	r0,r0,r1

00000088 e584000c    125 	str	r0,[r4,12]

                     126 ;59:             + sizes->ctlNum + sizes->addCause;


                     127 ;60:     sizes->fullStruct = BerEncoder_determineFullObjectSize(sizes->allFields);


                     128 

0000008c eb000000*   129 	bl	BerEncoder_determineFullObjectSize

00000090 e5840010    130 	str	r0,[r4,16]

00000094 e8bd4cf0    131 	ldmfd	[sp]!,{r4-r7,r10-fp,lr}

00000098 e12fff1e*   132 	ret	

                     133 	.endf	calcLastApplErrSizes

                     134 	.align	4

                     135 ;orIdentSize	r7	local

                     136 

                     137 ;sizes	r4	param

                     138 ;cntrlObj	fp	param

                     139 ;error	r5	param

                     140 ;orCat	r6	param

                     141 ;orIdent	r7	param

                     142 ;ctlNum	r10	param

                     143 ;addCause	r12	param

                     144 

                     145 	.section ".bss","awb"

                     146 .L30:

                     147 	.data

                     148 	.text

                     149 

                     150 ;61: }


                     151 

                     152 ;62: 


                     153 ;63: static bool encodeLastApplErrErr(BufferView* wrBuf, LastApplErrorSizes* sizes,


                     154 	.align	4

                     155 	.align	4

                     156 encodeLastApplErrErr:

0000009c e92d4cf4    157 	stmfd	[sp]!,{r2,r4-r7,r10-fp,lr}

                     158 ;64:                               StringView* cntrlObjName, uint8_t error, uint8_t orCat,


                     159 ;65:                               StringView* orIdent, uint8_t ctlNum, uint8_t addCause)


                     160 ;66: {


                     161 

                     162 ;67:     //Структура


                     163 ;68:     if(!BufferView_encodeTL(wrBuf, 0xA2,  sizes->allFields))


                     164 

000000a0 e1a0b003    165 	mov	fp,r3

000000a4 e59d6024    166 	ldr	r6,[sp,36]

000000a8 e5dd7028    167 	ldrb	r7,[sp,40]

000000ac e5dda02c    168 	ldrb	r10,[sp,44]

000000b0 e1a05001    169 	mov	r5,r1

000000b4 e5952018    170 	ldr	r2,[r5,24]

000000b8 e1a04000    171 	mov	r4,r0

000000bc e3a010a2    172 	mov	r1,162


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_buk1.s
000000c0 eb000000*   173 	bl	BufferView_encodeTL

000000c4 e3500000    174 	cmp	r0,0

000000c8 0a000029    175 	beq	.L61

                     176 ;69:     {


                     177 

                     178 ;70:         return false;


                     179 

                     180 ;71:     }


                     181 ;72:     //Поля структуры


                     182 ;73:     //cntrlObj


                     183 ;74:     if(!BufferView_encodeStringView(wrBuf, IEC61850_BER_VISIBLE_STRING,


                     184 

000000cc e59d2000    185 	ldr	r2,[sp]

000000d0 e1a00004    186 	mov	r0,r4

000000d4 e3a0108a    187 	mov	r1,138

000000d8 eb000000*   188 	bl	BufferView_encodeStringView

000000dc e3500000    189 	cmp	r0,0

000000e0 0a000023    190 	beq	.L61

                     191 ;75:                                     cntrlObjName))


                     192 ;76:     {


                     193 

                     194 ;77:         return false;


                     195 

                     196 ;78:     }


                     197 ;79:     //error


                     198 ;80:     if(!BufferView_encodeUInt32(wrBuf, IEC61850_BER_INTEGER, error))


                     199 

000000e4 e1a0200b    200 	mov	r2,fp

000000e8 e1a00004    201 	mov	r0,r4

000000ec e3a01085    202 	mov	r1,133

000000f0 eb000000*   203 	bl	BufferView_encodeUInt32

000000f4 e3500000    204 	cmp	r0,0

000000f8 0a00001d    205 	beq	.L61

                     206 ;81:     {


                     207 

                     208 ;82:         return false;


                     209 

                     210 ;83:     }


                     211 ;84: 


                     212 ;85:     //origin


                     213 ;86:     if(!BufferView_encodeTL(wrBuf, 0xA2, sizes->originFields))


                     214 

000000fc e5952008    215 	ldr	r2,[r5,8]

00000100 e1a00004    216 	mov	r0,r4

00000104 e3a010a2    217 	mov	r1,162

00000108 eb000000*   218 	bl	BufferView_encodeTL

0000010c e3500000    219 	cmp	r0,0

00000110 0a000017    220 	beq	.L61

                     221 ;87:     {


                     222 

                     223 ;88:         return false;


                     224 

                     225 ;89:     }


                     226 ;90:     //orCat


                     227 ;91:     if(!BufferView_encodeUInt32(wrBuf, IEC61850_BER_INTEGER, orCat))


                     228 

00000114 e5dd2020    229 	ldrb	r2,[sp,32]

00000118 e1a00004    230 	mov	r0,r4

0000011c e3a01085    231 	mov	r1,133

00000120 eb000000*   232 	bl	BufferView_encodeUInt32

00000124 e3500000    233 	cmp	r0,0


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_buk1.s
00000128 0a000011    234 	beq	.L61

                     235 ;92:     {


                     236 

                     237 ;93:         return false;


                     238 

                     239 ;94:     }


                     240 ;95:     //orIdent


                     241 ;96:     if(!BufferView_encodeStringView(wrBuf, IEC61850_BER_OCTET_STRING,


                     242 

0000012c e1a02006    243 	mov	r2,r6

00000130 e1a00004    244 	mov	r0,r4

00000134 e3a01089    245 	mov	r1,137

00000138 eb000000*   246 	bl	BufferView_encodeStringView

0000013c e3500000    247 	cmp	r0,0

00000140 0a00000b    248 	beq	.L61

                     249 ;97:                                     orIdent))


                     250 ;98:     {


                     251 

                     252 ;99:         return false;


                     253 

                     254 ;100:     }


                     255 ;101:     //ctlNum


                     256 ;102:     if(!BufferView_encodeUInt32(wrBuf, IEC61850_BER_UNSIGNED_INTEGER,


                     257 

00000144 e1a02007    258 	mov	r2,r7

00000148 e1a00004    259 	mov	r0,r4

0000014c e3a01086    260 	mov	r1,134

00000150 eb000000*   261 	bl	BufferView_encodeUInt32

00000154 e3500000    262 	cmp	r0,0

00000158 0a000005    263 	beq	.L61

                     264 ;103:                                 ctlNum))


                     265 ;104:     {


                     266 

                     267 ;105:         return false;


                     268 

                     269 ;106:     }


                     270 ;107:     //addCause


                     271 ;108:     if(!BufferView_encodeUInt32(wrBuf, IEC61850_BER_INTEGER, addCause))


                     272 

0000015c e1a0200a    273 	mov	r2,r10

00000160 e1a00004    274 	mov	r0,r4

00000164 e3a01085    275 	mov	r1,133

00000168 eb000000*   276 	bl	BufferView_encodeUInt32

0000016c e3500000    277 	cmp	r0,0

                     278 ;111:     }


                     279 ;112:     return true;


                     280 

00000170 13a00001    281 	movne	r0,1

                     282 .L61:

                     283 ;109:     {


                     284 

                     285 ;110:         return false;


                     286 

00000174 03a00000    287 	moveq	r0,0

                     288 .L37:

00000178 e8bd4cf4    289 	ldmfd	[sp]!,{r2,r4-r7,r10-fp,lr}

0000017c e12fff1e*   290 	ret	

                     291 	.endf	encodeLastApplErrErr

                     292 	.align	4

                     293 

                     294 ;wrBuf	r4	param


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_buk1.s
                     295 ;sizes	r5	param

                     296 ;cntrlObjName	[sp]	param

                     297 ;error	fp	param

                     298 ;orCat	[sp,32]	param

                     299 ;orIdent	r6	param

                     300 ;ctlNum	r7	param

                     301 ;addCause	r10	param

                     302 

                     303 	.section ".bss","awb"

                     304 .L168:

                     305 	.data

                     306 	.text

                     307 

                     308 ;113: }


                     309 

                     310 ;114: 


                     311 ;115: bool InfoReport_createLastApplErrorReport(BufferView* wrBuf,


                     312 	.align	4

                     313 	.align	4

                     314 	.align	4

                     315 InfoReport_createLastApplErrorReport::

00000180 e92d4cf0    316 	stmfd	[sp]!,{r4-r7,r10-fp,lr}

                     317 ;116:                                       StringView* cntrlObj, uint8_t error, uint8_t orCat,


                     318 ;117:                                       StringView* orIdent, uint8_t ctlNum, uint8_t addCause)


                     319 ;118: {


                     320 

                     321 ;119:     LastApplErrorSizes errSizes;


                     322 ;120: 


                     323 ;121:     //Variable acess specification (0xA0)


                     324 ;122:     //Эта часть для этого вида отчёта всегда одинаковая


                     325 ;123:     if(!BufferView_writeData(wrBuf, applErrorVarSpec, sizeof(applErrorVarSpec)))


                     326 

00000184 e1a06003    327 	mov	r6,r3

00000188 e24dd030    328 	sub	sp,sp,48

0000018c e59d704c    329 	ldr	r7,[sp,76]

00000190 e5dda054    330 	ldrb	r10,[sp,84]

00000194 e1a04001    331 	mov	r4,r1

00000198 e59f14b8*   332 	ldr	r1,.L288

0000019c e1a0b000    333 	mov	fp,r0

000001a0 e1a05002    334 	mov	r5,r2

000001a4 e3a02015    335 	mov	r2,21

000001a8 eb000000*   336 	bl	BufferView_writeData

000001ac e3500000    337 	cmp	r0,0

000001b0 0a00000c    338 	beq	.L209

                     339 ;124:     {


                     340 

                     341 ;125:         return false;


                     342 

                     343 ;126:     }


                     344 ;127: 


                     345 ;128:     //Вычисляем размеры для List of access result (0xA0)


                     346 ;129:     calcLastApplErrSizes(&errSizes, cntrlObj, error, orCat, orIdent, ctlNum, addCause);


                     347 

000001b4 e5dd1050    348 	ldrb	r1,[sp,80]

000001b8 e1a00007    349 	mov	r0,r7

000001bc e88d0403    350 	stmea	[sp],{r0-r1,r10}

000001c0 e1a03006    351 	mov	r3,r6

000001c4 e1a02005    352 	mov	r2,r5

000001c8 e1a01004    353 	mov	r1,r4

000001cc e28d0010    354 	add	r0,sp,16

000001d0 ebffff8a*   355 	bl	calcLastApplErrSizes


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_buk1.s
                     356 ;130: 


                     357 ;131: 


                     358 ;132:     // Кодируем List of access result (0xA0)


                     359 ;133:     if(!BufferView_encodeTL(wrBuf, 0xA0, errSizes.fullStruct))


                     360 

000001d4 e59d202c    361 	ldr	r2,[sp,44]

000001d8 e1a0000b    362 	mov	r0,fp

000001dc e3a010a0    363 	mov	r1,160

000001e0 eb000000*   364 	bl	BufferView_encodeTL

000001e4 e3500000    365 	cmp	r0,0

                     366 .L209:

                     367 ;134:     {


                     368 

                     369 ;135:         return false;


                     370 

000001e8 03a00000    371 	moveq	r0,0

000001ec 0a000007    372 	beq	.L203

                     373 .L208:

                     374 ;136:     }


                     375 ;137: 


                     376 ;138:     return encodeLastApplErrErr(wrBuf, &errSizes, cntrlObj, error, orCat, orIdent,


                     377 

000001f0 e1a0c00a    378 	mov	r12,r10

000001f4 e5dda050    379 	ldrb	r10,[sp,80]

000001f8 e88d14c0    380 	stmea	[sp],{r6-r7,r10,r12}

000001fc e1a03005    381 	mov	r3,r5

00000200 e1a02004    382 	mov	r2,r4

00000204 e28d1010    383 	add	r1,sp,16

00000208 e1a0000b    384 	mov	r0,fp

0000020c ebffffa2*   385 	bl	encodeLastApplErrErr

                     386 .L203:

00000210 e28dd030    387 	add	sp,sp,48

00000214 e8bd8cf0    388 	ldmfd	[sp]!,{r4-r7,r10-fp,pc}

                     389 	.endf	InfoReport_createLastApplErrorReport

                     390 	.align	4

                     391 ;errSizes	[sp,16]	local

                     392 

                     393 ;wrBuf	fp	param

                     394 ;cntrlObj	r4	param

                     395 ;error	r5	param

                     396 ;orCat	r6	param

                     397 ;orIdent	r7	param

                     398 ;ctlNum	[sp,80]	param

                     399 ;addCause	r10	param

                     400 

                     401 	.data

                     402 .L274:

                     403 	.section ".rodata","a"

                     404 .L275:;	"LastApplError\000"

00000000 7473614c    405 	.data.b	76,97,115,116

00000004 6c707041    406 	.data.b	65,112,112,108

00000008 6f727245    407 	.data.b	69,114,114,111

0000000c 0072       408 	.data.b	114,0

0000000e 0000       409 	.space	2

                     410 	.section ".bss","awb"

00000000 00000000    411 ctrlObjNameBuf:	.space	129

00000004 00000000 
00000008 00000000 
0000000c 00000000 
00000010 00000000 
00000014 00000000 

                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_buk1.s
00000018 00000000 
0000001c 00000000 
00000020 00000000 
00000024 00000000 
00000028 00000000 
0000002c 00000000 
00000030 00000000 
00000034 00000000 
00000038 00000000 
0000003c 00000000 
00000040 00000000 
00000044 00000000 
00000048 00000000 
0000004c 00000000 
00000050 00000000 
00000054 00000000 
00000058 00000000 
0000005c 00000000 
00000060 00000000 
00000064 00000000 
00000068 00000000 
0000006c 00000000 
00000070 00000000 
00000074 00000000 
00000078 00000000 
0000007c 00000000 
00000080 00 
00000081 000000     412 	.space	3

                     413 	.data

                     414 	.text

                     415 

                     416 ;139:                                 ctlNum, addCause);


                     417 ;140: }


                     418 

                     419 ;141: 


                     420 ;142: 


                     421 ;143: bool InfoReport_createPositiveCmdTermReport( IEDEntity cntrlObj,


                     422 	.align	4

                     423 	.align	4

                     424 InfoReport_createPositiveCmdTermReport::

00000218 e92d4ff0    425 	stmfd	[sp]!,{r4-fp,lr}

                     426 ;144:     BufferView* wrBuf, StringView* domainId, StringView* itemId)


                     427 ;145: {


                     428 

                     429 ;146:     size_t cntrlObjLen;


                     430 ;147:     size_t domainSpecificLen;


                     431 ;148:     size_t nameObjLen;


                     432 ;149:     size_t listOfVarLen;


                     433 ;150:     size_t varAccessSpec;


                     434 ;151: 


                     435 ;152:     //===============Вычисляем размеры=============


                     436 ;153:     //Variable acess specification (0xA0)


                     437 ;154:     domainSpecificLen = BerEncoder_determineFullObjectSize(domainId->len)


                     438 

0000021c e1a04001    439 	mov	r4,r1

00000220 e24dd00c    440 	sub	sp,sp,12

00000224 e1a05000    441 	mov	r5,r0

00000228 e1a07002    442 	mov	r7,r2

0000022c e5970000    443 	ldr	r0,[r7]

00000230 e1a0a003    444 	mov	r10,r3

00000234 eb000000*   445 	bl	BerEncoder_determineFullObjectSize


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_buk1.s
00000238 e1a06000    446 	mov	r6,r0

0000023c e59a0000    447 	ldr	r0,[r10]

00000240 eb000000*   448 	bl	BerEncoder_determineFullObjectSize

00000244 e0806006    449 	add	r6,r0,r6

                     450 ;155:             + BerEncoder_determineFullObjectSize(itemId->len);


                     451 ;156:     nameObjLen = BerEncoder_determineFullObjectSize(domainSpecificLen);


                     452 

00000248 e1a00006    453 	mov	r0,r6

0000024c eb000000*   454 	bl	BerEncoder_determineFullObjectSize

                     455 ;157:     listOfVarLen = BerEncoder_determineFullObjectSize(nameObjLen);


                     456 

00000250 e1a08000    457 	mov	r8,r0

00000254 eb000000*   458 	bl	BerEncoder_determineFullObjectSize

                     459 ;158:     varAccessSpec = BerEncoder_determineFullObjectSize(listOfVarLen);


                     460 

00000258 e1a0b000    461 	mov	fp,r0

0000025c eb000000*   462 	bl	BerEncoder_determineFullObjectSize

00000260 e595c060    463 	ldr	r12,[r5,96]

00000264 e1a0100d    464 	mov	r1,sp

00000268 e1a09000    465 	mov	r9,r0

                     466 ;159: 


                     467 ;160:     //Control object


                     468 ;161:     if(!cntrlObj->calcReadLen(cntrlObj, &cntrlObjLen))


                     469 

0000026c e1a00005    470 	mov	r0,r5

00000270 e1a0e00f    471 	mov	lr,pc

00000274 e12fff1c*   472 	bx	r12

00000278 e3500000    473 	cmp	r0,0

0000027c 0a000028    474 	beq	.L313

                     475 ;162:     {


                     476 

                     477 ;163:         return false;


                     478 

                     479 ;164:     }


                     480 ;165: 


                     481 ;166:     //===================Кодируем==================


                     482 ;167:     //List of varibles (0xA0)


                     483 ;168:     if(!BufferView_encodeTL(wrBuf, 0xA0, varAccessSpec))


                     484 

00000280 e1a02009    485 	mov	r2,r9

00000284 e1a00004    486 	mov	r0,r4

00000288 e3a010a0    487 	mov	r1,160

0000028c eb000000*   488 	bl	BufferView_encodeTL

00000290 e3500000    489 	cmp	r0,0

00000294 0a000022    490 	beq	.L313

                     491 ;169:     {


                     492 

                     493 ;170:         return false;


                     494 

                     495 ;171:     }


                     496 ;172:     //Имя всегда в тэге SEQUENCE (0x30)


                     497 ;173:     if(!BufferView_encodeTL(wrBuf, ASN_SEQUENCE, listOfVarLen))


                     498 

00000298 e1a0200b    499 	mov	r2,fp

0000029c e1a00004    500 	mov	r0,r4

000002a0 e3a01030    501 	mov	r1,48

000002a4 eb000000*   502 	bl	BufferView_encodeTL

000002a8 e3500000    503 	cmp	r0,0

000002ac 0a00001c    504 	beq	.L313

                     505 ;174:     {


                     506 


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_buk1.s
                     507 ;175:         return false;


                     508 

                     509 ;176:     }


                     510 ;177:     //Name(0xA0)


                     511 ;178:     if(!BufferView_encodeTL(wrBuf, 0xA0, nameObjLen))


                     512 

000002b0 e1a02008    513 	mov	r2,r8

000002b4 e1a00004    514 	mov	r0,r4

000002b8 e3a010a0    515 	mov	r1,160

000002bc eb000000*   516 	bl	BufferView_encodeTL

000002c0 e3500000    517 	cmp	r0,0

000002c4 0a000016    518 	beq	.L313

                     519 ;179:     {


                     520 

                     521 ;180:         return false;


                     522 

                     523 ;181:     }


                     524 ;182:     //Domain specific(0xA1)


                     525 ;183:     if(!BufferView_encodeTL(wrBuf, 0xA1, domainSpecificLen))


                     526 

000002c8 e1a02006    527 	mov	r2,r6

000002cc e1a00004    528 	mov	r0,r4

000002d0 e3a010a1    529 	mov	r1,161

000002d4 eb000000*   530 	bl	BufferView_encodeTL

000002d8 e3500000    531 	cmp	r0,0

000002dc 0a000010    532 	beq	.L313

                     533 ;184:     {


                     534 

                     535 ;185:         return false;


                     536 

                     537 ;186:     }


                     538 ;187:     //Domain ID


                     539 ;188:     if(!BufferView_encodeStringView(wrBuf, ASN_VISIBLE_STRING, domainId))


                     540 

000002e0 e1a02007    541 	mov	r2,r7

000002e4 e1a00004    542 	mov	r0,r4

000002e8 e3a0101a    543 	mov	r1,26

000002ec eb000000*   544 	bl	BufferView_encodeStringView

000002f0 e3500000    545 	cmp	r0,0

000002f4 0a00000a    546 	beq	.L313

                     547 ;189:     {


                     548 

                     549 ;190:         return false;


                     550 

                     551 ;191:     }


                     552 ;192:     //Item ID


                     553 ;193:     if(!BufferView_encodeStringView(wrBuf, ASN_VISIBLE_STRING, itemId))


                     554 

000002f8 e1a0200a    555 	mov	r2,r10

000002fc e1a00004    556 	mov	r0,r4

00000300 e3a0101a    557 	mov	r1,26

00000304 eb000000*   558 	bl	BufferView_encodeStringView

00000308 e3500000    559 	cmp	r0,0

0000030c 0a000004    560 	beq	.L313

                     561 ;194:     {


                     562 

                     563 ;195:         return false;


                     564 

                     565 ;196:     }


                     566 ;197: 


                     567 ;198:     // Кодируем List of access result (0xA0)



                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_buk1.s
                     568 ;199:     if(!BufferView_encodeTL(wrBuf, 0xA0, cntrlObjLen))


                     569 

00000310 e59d2000    570 	ldr	r2,[sp]

00000314 e1a00004    571 	mov	r0,r4

00000318 e3a010a0    572 	mov	r1,160

0000031c eb000000*   573 	bl	BufferView_encodeTL

00000320 e3500000    574 	cmp	r0,0

                     575 .L313:

                     576 ;200:     {


                     577 

                     578 ;201:         return false;


                     579 

00000324 03a00000    580 	moveq	r0,0

00000328 0a000004    581 	beq	.L289

                     582 .L312:

                     583 ;202:     }


                     584 ;203:     //Control object


                     585 ;204:     return cntrlObj->encodeRead(cntrlObj, wrBuf);


                     586 

0000032c e595c05c    587 	ldr	r12,[r5,92]

00000330 e1a01004    588 	mov	r1,r4

00000334 e1a00005    589 	mov	r0,r5

00000338 e1a0e00f    590 	mov	lr,pc

0000033c e12fff1c*   591 	bx	r12

                     592 .L289:

00000340 e28dd00c    593 	add	sp,sp,12

00000344 e8bd8ff0    594 	ldmfd	[sp]!,{r4-fp,pc}

                     595 	.endf	InfoReport_createPositiveCmdTermReport

                     596 	.align	4

                     597 ;cntrlObjLen	[sp]	local

                     598 ;domainSpecificLen	r6	local

                     599 ;nameObjLen	r8	local

                     600 ;listOfVarLen	fp	local

                     601 ;varAccessSpec	r9	local

                     602 

                     603 ;cntrlObj	r5	param

                     604 ;wrBuf	r4	param

                     605 ;domainId	r7	param

                     606 ;itemId	r10	param

                     607 

                     608 	.section ".bss","awb"

                     609 .L436:

                     610 	.data

                     611 	.text

                     612 

                     613 ;205: }


                     614 

                     615 ;206: 


                     616 ;207: bool InfoReport_createNegativeCmdTermReport( IEDEntity ctrlObj,


                     617 	.align	4

                     618 	.align	4

                     619 InfoReport_createNegativeCmdTermReport::

00000348 e92d4ff0    620 	stmfd	[sp]!,{r4-fp,lr}

                     621 ;208:     BufferView* wrBuf, StringView* domainId, StringView* itemId, uint8_t addCause)


                     622 ;209: {


                     623 

                     624 ;210:     LastApplErrorSizes errSizes;


                     625 ;211:     StringView ctrlObjParentFullName;


                     626 ;212:     BufferView ctrlObjParentNameBuf;


                     627 ;213:     int32_t orCat;


                     628 ;214:     StringView orIdent;



                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_buk1.s
                     629 ;215:     size_t ctrlObjLen;


                     630 ;216:     size_t errNameLen;


                     631 ;217:     size_t errNameVmdSpecificLen;


                     632 ;218:     size_t errNameStrucLen;


                     633 ;219:     size_t operDomainSpecificLen;


                     634 ;220:     size_t operVarSpecName;


                     635 ;221:     size_t operVarNameStrucLen;


                     636 ;222:     size_t allVarSpec;


                     637 ;223:     size_t operNameLen;


                     638 ;224:     uint8_t error = 0;


                     639 

                     640 ;225:     uint8_t ctlNum = 0;


                     641 

                     642 ;226: 


                     643 ;227: 


                     644 ;228: 


                     645 ;229: 


                     646 ;230:     //Получаем полное имя родителя


                     647 ;231:     BufferView_init(&ctrlObjParentNameBuf, ctrlObjNameBuf,


                     648 

0000034c e24dd068    649 	sub	sp,sp,104

00000350 e5dda08c    650 	ldrb	r10,[sp,140]

00000354 e1a05000    651 	mov	r5,r0

00000358 e1a04001    652 	mov	r4,r1

0000035c e59f12f8*   653 	ldr	r1,.L754

00000360 e28d0034    654 	add	r0,sp,52

00000364 e1a07003    655 	mov	r7,r3

00000368 e3a03000    656 	mov	r3,0

0000036c e1a06002    657 	mov	r6,r2

00000370 e3a02081    658 	mov	r2,129

00000374 eb000000*   659 	bl	BufferView_init

                     660 ;232:                     sizeof(ctrlObjNameBuf), 0);


                     661 ;233:     if(!IEDEntity_getFullName(ctrlObj->parent, &ctrlObjParentNameBuf ))


                     662 

00000378 e5950000    663 	ldr	r0,[r5]

0000037c e28d1034    664 	add	r1,sp,52

00000380 eb000000*   665 	bl	IEDEntity_getFullName

00000384 e3500000    666 	cmp	r0,0

00000388 0a000081    667 	beq	.L509

                     668 ;234:     {


                     669 

                     670 ;235:         ERROR_REPORT("Unable to get full object name");


                     671 ;236:         return false;


                     672 

                     673 ;237:     }


                     674 ;238:     StringView_init(&ctrlObjParentFullName, (char*)ctrlObjParentNameBuf.p,


                     675 

0000038c e59d2038    676 	ldr	r2,[sp,56]

00000390 e59d1034    677 	ldr	r1,[sp,52]

00000394 e28d0040    678 	add	r0,sp,64

00000398 eb000000*   679 	bl	StringView_init

                     680 ;239:                     ctrlObjParentNameBuf.pos);


                     681 ;240: 


                     682 ;241: 


                     683 ;242:     if(!IEDControlDA_getOrCat(ctrlObj, &orCat))


                     684 

0000039c e28d1010    685 	add	r1,sp,16

000003a0 e1a00005    686 	mov	r0,r5

000003a4 eb000000*   687 	bl	IEDControlDA_getOrCat

000003a8 e3500000    688 	cmp	r0,0

000003ac 0a000078    689 	beq	.L509


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_buk1.s
                     690 ;243:     {


                     691 

                     692 ;244:         ERROR_REPORT("Unable to get orCat");


                     693 ;245:         return false;


                     694 

                     695 ;246:     }


                     696 ;247: 


                     697 ;248:     if(!IEDControlDA_getOrIdent(ctrlObj, &orIdent))


                     698 

000003b0 e28d102c    699 	add	r1,sp,44

000003b4 e1a00005    700 	mov	r0,r5

000003b8 eb000000*   701 	bl	IEDControlDA_getOrIdent

000003bc e3500000    702 	cmp	r0,0

000003c0 0a000073    703 	beq	.L509

                     704 ;249:     {


                     705 

                     706 ;250:         ERROR_REPORT("Unable to get orIdent");


                     707 ;251:         return false;


                     708 

                     709 ;252:     }


                     710 ;253: 


                     711 ;254:     //===============Вычисляем размеры=============


                     712 ;255:     //Variable acess specification (0xA0)


                     713 ;256:     //LastApplError


                     714 ;257:     errNameLen = BerEncoder_determineFullObjectSize(strlen(lastApplErrorName));


                     715 

000003c4 e59f1294*   716 	ldr	r1,.L755

000003c8 e5910000    717 	ldr	r0,[r1]

000003cc eb000000*   718 	bl	strlen

000003d0 eb000000*   719 	bl	BerEncoder_determineFullObjectSize

                     720 ;258:     errNameVmdSpecificLen = BerEncoder_determineFullObjectSize(errNameLen);


                     721 

000003d4 e58d0024    722 	str	r0,[sp,36]

000003d8 eb000000*   723 	bl	BerEncoder_determineFullObjectSize

                     724 ;259:     errNameStrucLen = BerEncoder_determineFullObjectSize(errNameVmdSpecificLen);


                     725 

000003dc e58d0028    726 	str	r0,[sp,40]

000003e0 eb000000*   727 	bl	BerEncoder_determineFullObjectSize

000003e4 e1a08000    728 	mov	r8,r0

                     729 ;260: 


                     730 ;261:     //Oper


                     731 ;262:     operNameLen = BerEncoder_determineFullObjectSize(domainId->len)


                     732 

000003e8 e5960000    733 	ldr	r0,[r6]

000003ec eb000000*   734 	bl	BerEncoder_determineFullObjectSize

000003f0 e1a0b000    735 	mov	fp,r0

000003f4 e5970000    736 	ldr	r0,[r7]

000003f8 eb000000*   737 	bl	BerEncoder_determineFullObjectSize

000003fc e080b00b    738 	add	fp,r0,fp

                     739 ;263:             + BerEncoder_determineFullObjectSize(itemId->len);


                     740 ;264:     operDomainSpecificLen = BerEncoder_determineFullObjectSize(operNameLen);


                     741 

00000400 e1a0000b    742 	mov	r0,fp

00000404 eb000000*   743 	bl	BerEncoder_determineFullObjectSize

                     744 ;265:     operVarSpecName = BerEncoder_determineFullObjectSize(operDomainSpecificLen);


                     745 

00000408 e1a09000    746 	mov	r9,r0

0000040c eb000000*   747 	bl	BerEncoder_determineFullObjectSize

                     748 ;266:     operVarNameStrucLen = BerEncoder_determineFullObjectSize(operVarSpecName);


                     749 

00000410 e58d0020    750 	str	r0,[sp,32]


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_buk1.s
00000414 eb000000*   751 	bl	BerEncoder_determineFullObjectSize

                     752 ;267: 


                     753 ;268:     //allVarSpec = BerEncoder_determineFullObjectSize(errNameStrucLen + operVarNameStrucLen);


                     754 ;269:     allVarSpec = errNameStrucLen + operVarNameStrucLen;


                     755 

00000418 e3a02000    756 	mov	r2,0

0000041c e0808008    757 	add	r8,r0,r8

                     758 ;270: 


                     759 ;271:     //Размер данных


                     760 ;272:     //LastApplError


                     761 ;273:     calcLastApplErrSizes(&errSizes, &ctrlObjParentFullName, error, orCat, &orIdent,


                     762 

00000420 e28d002c    763 	add	r0,sp,44

00000424 e88d0405    764 	stmea	[sp],{r0,r2,r10}

00000428 e5dd3010    765 	ldrb	r3,[sp,16]

0000042c e28d1040    766 	add	r1,sp,64

00000430 e28d0048    767 	add	r0,sp,72

00000434 ebfffef1*   768 	bl	calcLastApplErrSizes

                     769 ;274:                          ctlNum, addCause);


                     770 ;275: 


                     771 ;276:     //Control object


                     772 ;277:     if(!ctrlObj->calcReadLen(ctrlObj, &ctrlObjLen))


                     773 

00000438 e595c060    774 	ldr	r12,[r5,96]

0000043c e28d1014    775 	add	r1,sp,20

00000440 e1a00005    776 	mov	r0,r5

00000444 e1a0e00f    777 	mov	lr,pc

00000448 e12fff1c*   778 	bx	r12

0000044c e3500000    779 	cmp	r0,0

00000450 0a00004f    780 	beq	.L509

                     781 ;278:     {


                     782 

                     783 ;279:         return false;


                     784 

                     785 ;280:     }


                     786 ;281: 


                     787 ;282:     //===================Кодируем==================


                     788 ;283:     //List of varibles (0xA0)


                     789 ;284:     if(!BufferView_encodeTL(wrBuf, 0xA0, allVarSpec))


                     790 

00000454 e1a02008    791 	mov	r2,r8

00000458 e1a00004    792 	mov	r0,r4

0000045c e3a010a0    793 	mov	r1,160

00000460 eb000000*   794 	bl	BufferView_encodeTL

00000464 e3500000    795 	cmp	r0,0

00000468 0a000049    796 	beq	.L509

                     797 ;285:     {


                     798 

                     799 ;286:         return false;


                     800 

                     801 ;287:     }


                     802 ;288:     //LastApplError


                     803 ;289:     //Имя всегда в тэге SEQUENCE (0x30)


                     804 ;290:     if(!BufferView_encodeTL(wrBuf, ASN_SEQUENCE, errNameVmdSpecificLen))


                     805 

0000046c e59d2028    806 	ldr	r2,[sp,40]

00000470 e1a00004    807 	mov	r0,r4

00000474 e3a01030    808 	mov	r1,48

00000478 eb000000*   809 	bl	BufferView_encodeTL

0000047c e3500000    810 	cmp	r0,0

00000480 0a000043    811 	beq	.L509


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_buk1.s
                     812 ;291:     {


                     813 

                     814 ;292:         return false;


                     815 

                     816 ;293:     }


                     817 ;294:     //0xA0


                     818 ;295:     if(!BufferView_encodeTL(wrBuf, 0xA0, errNameLen ))


                     819 

00000484 e59d2024    820 	ldr	r2,[sp,36]

00000488 e1a00004    821 	mov	r0,r4

0000048c e3a010a0    822 	mov	r1,160

00000490 eb000000*   823 	bl	BufferView_encodeTL

00000494 e3500000    824 	cmp	r0,0

00000498 0a00003d    825 	beq	.L509

                     826 ;296:     {


                     827 

                     828 ;297:         return false;


                     829 

                     830 ;298:     }


                     831 ;299:     //0x80


                     832 ;300:     if(!BufferView_encodeStr(wrBuf, 0x80 ,lastApplErrorName))


                     833 

0000049c e59f01bc*   834 	ldr	r0,.L755

000004a0 e3a01080    835 	mov	r1,128

000004a4 e5902000    836 	ldr	r2,[r0]

000004a8 e1a00004    837 	mov	r0,r4

000004ac eb000000*   838 	bl	BufferView_encodeStr

000004b0 e3500000    839 	cmp	r0,0

000004b4 0a000036    840 	beq	.L509

                     841 ;301:     {


                     842 

                     843 ;302:         return false;


                     844 

                     845 ;303:     }


                     846 ;304: 


                     847 ;305:     //Oper


                     848 ;306:     //Имя всегда в тэге SEQUENCE (0x30)


                     849 ;307:     if(!BufferView_encodeTL(wrBuf, ASN_SEQUENCE, operVarSpecName))


                     850 

000004b8 e59d2020    851 	ldr	r2,[sp,32]

000004bc e1a00004    852 	mov	r0,r4

000004c0 e3a01030    853 	mov	r1,48

000004c4 eb000000*   854 	bl	BufferView_encodeTL

000004c8 e3500000    855 	cmp	r0,0

000004cc 0a000030    856 	beq	.L509

                     857 ;308:     {


                     858 

                     859 ;309:         return false;


                     860 

                     861 ;310:     }


                     862 ;311:     //Name(0xA0)


                     863 ;312:     if(!BufferView_encodeTL(wrBuf, 0xA0, operDomainSpecificLen))


                     864 

000004d0 e1a02009    865 	mov	r2,r9

000004d4 e1a00004    866 	mov	r0,r4

000004d8 e3a010a0    867 	mov	r1,160

000004dc eb000000*   868 	bl	BufferView_encodeTL

000004e0 e3500000    869 	cmp	r0,0

000004e4 0a00002a    870 	beq	.L509

                     871 ;313:     {


                     872 


                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_buk1.s
                     873 ;314:         return false;


                     874 

                     875 ;315:     }


                     876 ;316:     //Domain specific(0xA1)


                     877 ;317:     if(!BufferView_encodeTL(wrBuf, 0xA1, operNameLen))


                     878 

000004e8 e1a0200b    879 	mov	r2,fp

000004ec e1a00004    880 	mov	r0,r4

000004f0 e3a010a1    881 	mov	r1,161

000004f4 eb000000*   882 	bl	BufferView_encodeTL

000004f8 e3500000    883 	cmp	r0,0

000004fc 0a000024    884 	beq	.L509

                     885 ;318:     {


                     886 

                     887 ;319:         return false;


                     888 

                     889 ;320:     }


                     890 ;321:     //Domain ID


                     891 ;322:     if(!BufferView_encodeStringView(wrBuf, ASN_VISIBLE_STRING, domainId))


                     892 

00000500 e1a02006    893 	mov	r2,r6

00000504 e1a00004    894 	mov	r0,r4

00000508 e3a0101a    895 	mov	r1,26

0000050c eb000000*   896 	bl	BufferView_encodeStringView

00000510 e3500000    897 	cmp	r0,0

00000514 0a00001e    898 	beq	.L509

                     899 ;323:     {


                     900 

                     901 ;324:         return false;


                     902 

                     903 ;325:     }


                     904 ;326:     //Item ID


                     905 ;327:     if(!BufferView_encodeStringView(wrBuf, ASN_VISIBLE_STRING, itemId))


                     906 

00000518 e1a02007    907 	mov	r2,r7

0000051c e1a00004    908 	mov	r0,r4

00000520 e3a0101a    909 	mov	r1,26

00000524 eb000000*   910 	bl	BufferView_encodeStringView

00000528 e3500000    911 	cmp	r0,0

0000052c 0a000018    912 	beq	.L509

                     913 ;328:     {


                     914 

                     915 ;329:         return false;


                     916 

                     917 ;330:     }


                     918 ;331: 


                     919 ;332: 


                     920 ;333:     // Кодируем List of access result (0xA0)


                     921 ;334:     if(!BufferView_encodeTL(wrBuf, 0xA0, errSizes.fullStruct + ctrlObjLen))


                     922 

00000530 e59d1064    923 	ldr	r1,[sp,100]

00000534 e59d0014    924 	ldr	r0,[sp,20]

00000538 e0802001    925 	add	r2,r0,r1

0000053c e1a00004    926 	mov	r0,r4

00000540 e3a010a0    927 	mov	r1,160

00000544 eb000000*   928 	bl	BufferView_encodeTL

00000548 e3500000    929 	cmp	r0,0

0000054c 0a000010    930 	beq	.L509

                     931 ;335:     {


                     932 

                     933 ;336:         return false;



                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_buk1.s
                     934 

                     935 ;337:     }


                     936 ;338: 


                     937 ;339:     //LastApplError


                     938 ;340:     if(!encodeLastApplErrErr(wrBuf, &errSizes,&ctrlObjParentFullName, error, orCat,


                     939 

00000550 e3a03000    940 	mov	r3,0

00000554 e5dd0010    941 	ldrb	r0,[sp,16]

00000558 e28d102c    942 	add	r1,sp,44

0000055c e88d040b    943 	stmea	[sp],{r0-r1,r3,r10}

00000560 e28d2040    944 	add	r2,sp,64

00000564 e28d1048    945 	add	r1,sp,72

00000568 e1a00004    946 	mov	r0,r4

0000056c ebfffeca*   947 	bl	encodeLastApplErrErr

00000570 e3500000    948 	cmp	r0,0

00000574 0a000006    949 	beq	.L509

                     950 ;341:                              &orIdent, ctlNum, addCause))


                     951 ;342:     {


                     952 

                     953 ;343:         return false;


                     954 

                     955 ;344:     }


                     956 ;345:     //Oper


                     957 ;346:     if(!ctrlObj->encodeRead(ctrlObj, wrBuf))


                     958 

00000578 e595c05c    959 	ldr	r12,[r5,92]

0000057c e1a01004    960 	mov	r1,r4

00000580 e1a00005    961 	mov	r0,r5

00000584 e1a0e00f    962 	mov	lr,pc

00000588 e12fff1c*   963 	bx	r12

0000058c e3500000    964 	cmp	r0,0

                     965 ;349:     }


                     966 ;350:     return true;


                     967 

00000590 13a00001    968 	movne	r0,1

                     969 .L509:

                     970 ;347:     {


                     971 

                     972 ;348:         return false;


                     973 

00000594 03a00000    974 	moveq	r0,0

                     975 .L461:

00000598 e28dd068    976 	add	sp,sp,104

0000059c e8bd8ff0    977 	ldmfd	[sp]!,{r4-fp,pc}

                     978 	.endf	InfoReport_createNegativeCmdTermReport

                     979 	.align	4

                     980 ;errSizes	[sp,72]	local

                     981 ;ctrlObjParentFullName	[sp,64]	local

                     982 ;ctrlObjParentNameBuf	[sp,52]	local

                     983 ;orCat	[sp,16]	local

                     984 ;orIdent	[sp,44]	local

                     985 ;ctrlObjLen	[sp,20]	local

                     986 ;errNameLen	[sp,36]	local

                     987 ;errNameVmdSpecificLen	[sp,40]	local

                     988 ;errNameStrucLen	r8	local

                     989 ;operDomainSpecificLen	r9	local

                     990 ;operVarSpecName	[sp,32]	local

                     991 ;allVarSpec	r8	local

                     992 ;operNameLen	fp	local

                     993 

                     994 ;ctrlObj	r5	param


                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_buk1.s
                     995 ;wrBuf	r4	param

                     996 ;domainId	r6	param

                     997 ;itemId	r7	param

                     998 ;addCause	r10	param

                     999 

                    1000 	.data

                    1001 	.text

                    1002 

                    1003 ;351: }


                    1004 

                    1005 ;352: 


                    1006 ;353: 


                    1007 ;354: //Оформляет и отправляет отчёт


                    1008 ;355: void InfoReport_send(IsoConnection* isoConn, uint8_t * infoReport,


                    1009 	.align	4

                    1010 	.align	4

                    1011 InfoReport_send::

000005a0 e92d4cf2   1012 	stmfd	[sp]!,{r1,r4-r7,r10-fp,lr}

                    1013 ;356:                      size_t byteCount, uint8_t* reportMmsBuf,


                    1014 ;357:                      uint8_t* reportPresentationBuf)


                    1015 ;358: {


                    1016 

                    1017 ;359:     int sessionDataLen;


                    1018 ;360:     int presentationDataLen;


                    1019 ;361:     int bufPos = 0;


                    1020 

                    1021 ;362:     int reportLen;


                    1022 ;363: 


                    1023 ;364:     SessionOutBuffer* sessionOutBuf = allocSessionOutBuffer(


                    1024 

000005a4 e1a04002   1025 	mov	r4,r2

000005a8 e1a06003   1026 	mov	r6,r3

000005ac e59da020   1027 	ldr	r10,[sp,32]

000005b0 e1a07000   1028 	mov	r7,r0

000005b4 e2870e80   1029 	add	r0,r7,1<<11

000005b8 e2800018   1030 	add	r0,r0,24

000005bc e3a01c60   1031 	mov	r1,3<<13

000005c0 eb000000*  1032 	bl	allocSessionOutBuffer

000005c4 e1b05000   1033 	movs	r5,r0

                    1034 ;365:         &isoConn->outBuffers, SESSION_OUT_BUF_SIZE);


                    1035 ;366:     if (!sessionOutBuf)


                    1036 

000005c8 0a000021   1037 	beq	.L756

                    1038 ;367:     {


                    1039 

                    1040 ;368:         ERROR_REPORT("Unable to allocate buffer for the report");


                    1041 ;369:         return;


                    1042 

                    1043 ;370:     }


                    1044 ;371: 


                    1045 ;372:     //Определяем длины


                    1046 ;373: 


                    1047 ;374:     //A0 Information report


                    1048 ;375:     reportLen = 1 +


                    1049 

000005cc e1a00004   1050 	mov	r0,r4

000005d0 eb000000*  1051 	bl	BerEncoder_determineLengthSize

000005d4 e1a02006   1052 	mov	r2,r6

000005d8 e3a03000   1053 	mov	r3,0

000005dc e0800004   1054 	add	r0,r0,r4

000005e0 e2801001   1055 	add	r1,r0,1


                                                                      Page 19
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_buk1.s
                    1056 ;376:         BerEncoder_determineLengthSize(byteCount) + byteCount;


                    1057 ;377: 


                    1058 ;378:     // Кодируем


                    1059 ;379:     //Unconfirmed PDU


                    1060 ;380:     bufPos = BerEncoder_encodeTL(0xA3, reportLen, reportMmsBuf, bufPos);


                    1061 

000005e4 e3a000a3   1062 	mov	r0,163

000005e8 eb000000*  1063 	bl	BerEncoder_encodeTL

                    1064 ;381:     bufPos = BerEncoder_encodeTL(0xA0, byteCount, reportMmsBuf, bufPos);


                    1065 

000005ec e1a02006   1066 	mov	r2,r6

000005f0 e1a01004   1067 	mov	r1,r4

000005f4 e1a03000   1068 	mov	r3,r0

000005f8 e3a000a0   1069 	mov	r0,160

000005fc eb000000*  1070 	bl	BerEncoder_encodeTL

00000600 e1a02004   1071 	mov	r2,r4

00000604 e1a0b000   1072 	mov	fp,r0

                    1073 ;382:     memcpy(reportMmsBuf + bufPos, infoReport, byteCount);


                    1074 

00000608 e59d1000   1075 	ldr	r1,[sp]

0000060c e08b0006   1076 	add	r0,fp,r6

00000610 eb000000*  1077 	bl	memcpy

                    1078 ;383:     bufPos += byteCount;


                    1079 

00000614 e08b3004   1080 	add	r3,fp,r4

                    1081 ;384:     byteCount = bufPos;


                    1082 

                    1083 ;385: 


                    1084 ;386:     presentationDataLen = IsoPresentation_createUserData(&isoConn->presentation,


                    1085 

00000618 e1a02006   1086 	mov	r2,r6

0000061c e1a0100a   1087 	mov	r1,r10

00000620 e2870edb   1088 	add	r0,r7,0x0db0

00000624 e2800bf0   1089 	add	r0,r0,15<<14

00000628 eb000000*  1090 	bl	IsoPresentation_createUserData

                    1091 ;387:         reportPresentationBuf, reportMmsBuf, byteCount);


                    1092 ;388: 


                    1093 ;389:     sessionDataLen = isoSession_createDataSpdu(sessionOutBuf->cotpOutBuf,


                    1094 

0000062c e1a0200a   1095 	mov	r2,r10

00000630 e1a03000   1096 	mov	r3,r0

00000634 e2850008   1097 	add	r0,r5,8

00000638 e3a01c60   1098 	mov	r1,3<<13

0000063c eb000000*  1099 	bl	isoSession_createDataSpdu

                    1100 ;390:         SESSION_OUT_BUF_SIZE, reportPresentationBuf, presentationDataLen);


                    1101 ;391:     sessionOutBuf->byteCount = sessionDataLen;


                    1102 

00000640 e1a01005   1103 	mov	r1,r5

00000644 e5850004   1104 	str	r0,[r5,4]

                    1105 ;392:     if (!OutQueue_insert(&isoConn->outQueue, sessionOutBuf))


                    1106 

00000648 e2870bf2   1107 	add	r0,r7,242<<10

0000064c e280006c   1108 	add	r0,r0,108

00000650 eb000000*  1109 	bl	OutQueue_insert

                    1110 .L756:

                    1111 ;393:     {


                    1112 

                    1113 ;394:         ERROR_REPORT("Out queue overflow");


                    1114 ;395:         return;


                    1115 

00000654 e8bd8cf2   1116 	ldmfd	[sp]!,{r1,r4-r7,r10-fp,pc}


                                                                      Page 20
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_buk1.s
                    1117 	.endf	InfoReport_send

                    1118 	.align	4

                    1119 ;bufPos	fp	local

                    1120 ;sessionOutBuf	r5	local

                    1121 

                    1122 ;isoConn	r7	param

                    1123 ;infoReport	[sp]	param

                    1124 ;byteCount	r4	param

                    1125 ;reportMmsBuf	r6	param

                    1126 ;reportPresentationBuf	r10	param

                    1127 

                    1128 	.section ".bss","awb"

                    1129 .L792:

                    1130 	.data

                    1131 	.text

                    1132 

                    1133 ;396:     }


                    1134 ;397: }


                    1135 	.align	4

                    1136 .L288:

00000658 00000000*  1137 	.data.w	applErrorVarSpec

                    1138 	.type	.L288,$object

                    1139 	.size	.L288,4

                    1140 

                    1141 .L754:

0000065c 00000000*  1142 	.data.w	ctrlObjNameBuf

                    1143 	.type	.L754,$object

                    1144 	.size	.L754,4

                    1145 

                    1146 .L755:

00000660 00000000*  1147 	.data.w	lastApplErrorName

                    1148 	.type	.L755,$object

                    1149 	.size	.L755,4

                    1150 

                    1151 	.align	4

                    1152 ;ctrlObjNameBuf	ctrlObjNameBuf	static

                    1153 ;.L801	.L275	static

                    1154 

                    1155 	.data

                    1156 .L805:

                    1157 	.globl	applErrorVarSpec

00000000 113013a0   1158 applErrorVarSpec:	.data.b	160,19,48,17

00000004 0d800fa0   1159 	.data.b	160,15,128,13

00000008 7473614c   1160 	.data.b	76,97,115,116

0000000c 6c707041   1161 	.data.b	65,112,112,108

00000010 6f727245   1162 	.data.b	69,114,114,111

00000014 72        1163 	.data.b	114

00000015 000000    1164 	.space	3

                    1165 	.type	applErrorVarSpec,$object

                    1166 	.size	applErrorVarSpec,24

                    1167 .L806:

                    1168 	.globl	lastApplErrorName

00000018 00000000*  1169 lastApplErrorName:	.data.w	.L275

                    1170 	.type	lastApplErrorName,$object

                    1171 	.size	lastApplErrorName,4

                    1172 	.ghsnote version,6

                    1173 	.ghsnote tools,3

                    1174 	.ghsnote options,0

                    1175 	.text

                    1176 	.align	4

                    1177 	.data


                                                                      Page 21
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_buk1.s
                    1178 	.align	4

                    1179 	.section ".bss","awb"

                    1180 	.align	4

                    1181 	.section ".rodata","a"

                    1182 	.align	4

                    1183 	.text

