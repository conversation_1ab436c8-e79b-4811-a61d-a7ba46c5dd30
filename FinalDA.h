#pragma once

//В этом файле FinalDA исключительно для GOOSE

#include "IEDCompile/InnerAttributeTypes.h"
#include "bufView.h"
#include "local_types.h"

void* FDA_create(enum InnerAttributeType attrType, void* accessInfo);

//! Закодированный в BER размер пр условии кодирования с фиксированным размером
bool FDA_getFixedEncodedSize(void* da, size_t* size);

bool FDA_encodeGOOSETemplate(void* da, BufferView* templateBuf);

bool FDA_readAndCompare(void* da, void* dataSliceWnd,
                          bool* changed);

//! Записывает считанное ранее значение DA в позицию буфера, сохранённую при
//! создании шаблона.
void FDA_encodeFixedData(void* da);
