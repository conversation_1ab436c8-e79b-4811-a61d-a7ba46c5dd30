#include "iedQuality.h"


#include "debug.h"
#include "../DataSlice.h"
#include "iedFinalDA.h"
#include "iedTree.h"
#include "../AsnEncoding.h"
#include "../BusError.h"

#define QUALITY_ENCODED_SIZE 5


// Получает Quality из текущего "захваченного" DataSlice.
// Поддерживаются только два самых важных бита.
// Возвращаемое значение подготовлено к отправке в виде
// двухбайтного буфера в формате BER bit string: little endian и LSB->MSB.
// То есть bit 0 Quality это bit 7 результата функции.
// А bit 1 Quality это bit 6 результата
static uint16_t getQualityFastCurrDS(QualityAccsessInfo* accessInfo)
{
	int offset;
	uint16_t quality = 0;

	// Функция поддерживает только 2 бита Q потому что
	// остальные не нужны(пока?) и	 только добавят тормозов

	
	// Первый бит слева при отображении в IEDScout
	// x000000000000	
	offset = accessInfo->goodInvalidOffset;
	if(offset != -1 && DataSlice_getBoolFastCurrDS(offset))
	{		
		quality |= (1 << 7);
	}

	// Второй бит слева при отображении в IEDScout
	// 0x00000000000
	offset = accessInfo->reservedQuestionableOffset;
	if(offset != -1 && DataSlice_getBoolFastCurrDS(offset))
	{
		quality |= (1 << 6);
	}

	return quality;
}

static void updateFromDataSlice(IEDEntity entity)
{
	TerminalItem* termItem = entity->extInfo;
	uint16_t value;

	if(!BusError_check())
	{
		//Ставим bit 0
		// Из за хитрого формата quality, который здесь используется
		// (смотри функцию getQualityFastCurrDS), bit 0 это bit 7
		value = 0x40;
	}
	else
	{
		 value = getQualityFastCurrDS(&termItem->q.accessInfo);
	}

	if(entity->qualityValue == value)
	{
		entity->changed = TRGOP_NONE;
	}
	else
	{
		entity->changed = entity->trgOps;
		entity->qualityValue = value;
		IEDEntity_setTimeStamp(entity, dataSliceGetTimeStamp());
	}
}

static bool calcReadLen(IEDEntity entity, size_t* pLen )
{
	*pLen = QUALITY_ENCODED_SIZE;
	return true;
}

static bool encodeRead(IEDEntity entity, BufferView* outBuf)
{
	uint8_t* encodeBuf;

	if(!BufferView_alloc(outBuf,QUALITY_ENCODED_SIZE, &encodeBuf))
	{
		ERROR_REPORT("Unable to allocat buffer");
		return false;
	}

	// Возвращаемое значение не нужно,
	// потому что функция не возвращает ошибки, а размер известен заранее
	BerEncoder_encodeBitStringUshortBuf(ASN_TYPEDESCRIPTION_BIT_STRING,
		13, entity->qualityValue, encodeBuf, 0);
	outBuf->pos += QUALITY_ENCODED_SIZE;
	return true;
}

void IEDQuality_init(IEDEntity entity)
{
	TerminalItem* extInfo;
	// accessInfo в элементе дерева, настраивается на смещения в DataSlice
	QualityAccsessInfo* qAccessInfo;
	//accessInfo из бинарника модели
	QualityAccsessInfo* accessInfo;



	extInfo = entity->extInfo;
	accessInfo = extInfo->accessInfo;
	qAccessInfo = &extInfo->q.accessInfo;
	*qAccessInfo = *accessInfo;



	//Заменяем абсолютные смещения на смещения в DataSlice
	qAccessInfo->goodInvalidOffset =
			DataSlice_getBoolOffset(qAccessInfo->goodInvalidOffset);
	qAccessInfo->reservedQuestionableOffset =
			DataSlice_getBoolOffset(qAccessInfo->reservedQuestionableOffset);
	qAccessInfo->overflowOffset =
			DataSlice_getBoolOffset(qAccessInfo->overflowOffset);
	qAccessInfo->outOfRangeOffset =
			DataSlice_getBoolOffset(qAccessInfo->outOfRangeOffset);
	qAccessInfo->badReferenceOffset =
			DataSlice_getBoolOffset(qAccessInfo->badReferenceOffset);
	qAccessInfo->oscillatoryOffset =
			DataSlice_getBoolOffset(qAccessInfo->oscillatoryOffset);
	qAccessInfo->failureOffset =
			DataSlice_getBoolOffset(qAccessInfo->failureOffset);
	qAccessInfo->oldDataOffset =
			DataSlice_getBoolOffset(qAccessInfo->oldDataOffset);
	qAccessInfo->inconsistentOffset =
			DataSlice_getBoolOffset(qAccessInfo->inconsistentOffset);
	qAccessInfo->inaccurateOffset =
			DataSlice_getBoolOffset(qAccessInfo->inaccurateOffset);
	qAccessInfo->processSubstitutedOffset =
			DataSlice_getBoolOffset(qAccessInfo->processSubstitutedOffset);
	qAccessInfo->testOffset =
			DataSlice_getBoolOffset(qAccessInfo->testOffset);
	qAccessInfo->operatorBlockedOffset =
			DataSlice_getBoolOffset(qAccessInfo->operatorBlockedOffset);

	entity->updateFromDataSlice = updateFromDataSlice;
	entity->calcReadLen = calcReadLen;
	entity->encodeRead = encodeRead;

	IEDTree_addToCmpList(entity);
}
