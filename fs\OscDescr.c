#include <stdlib.h>
#include "OscFiles.h"
#include "OscDescr.h"
#include "../pwin_access.h"
#include "platform_critical_section.h"

static OscDescrHeader *oscDescr = NULL;


static OscDescrAnalog *getAnalogPtr(void)
{
	return (OscDescrAnalog*)(oscDescr + 1);
}

static OscDescrBool *getBoolPtr(void)
{
	// дискретные входы находятся после аналоговых
	OscDescrAnalog *pAnalog = getAnalogPtr();
	pAnalog += oscDescr->analogCount;

	return (OscDescrBool*)pAnalog;
}


// ищет информацию об аналоговом канале по индексу
OscDescrAnalog *OSCDescr_findDescrAnalogItem(unsigned int itemIdex)
{
	OscDescrAnalog *pAnalog = getAnalogPtr();
	int firstIndex = 0;
	int lastIndex = oscDescr->analogCount;
	int averageIndex = 0;
	unsigned int curIndex;
	while (firstIndex < lastIndex)
	{
		averageIndex = (firstIndex + lastIndex) / 2;
		curIndex = pAnalog[averageIndex].index;
		if (itemIdex < curIndex)
		{
			lastIndex = averageIndex;
		}
		else
			if (itemIdex > curIndex)
			{
				firstIndex = averageIndex + 1;
			}
			else
			{

				return &pAnalog[averageIndex];
			}
	}

	return NULL;
}

// ищет информацию о дискретном канале по смещению
OscDescrBool *OSCDescr_findDescrBoolItem(unsigned int itemOffset)
{
	// смещение дискретных
	OscDescrBool *pBool = getBoolPtr();

	int firstIndex = 0;
	int lastIndex = oscDescr->boolCount;
	int averageIndex = 0;
	unsigned int curOffset;
	while (firstIndex < lastIndex)
	{
		averageIndex = (firstIndex + lastIndex) / 2;
		curOffset = pBool[averageIndex].offset;
		if (itemOffset < curOffset)
		{
			lastIndex = averageIndex;
		}
		else
			if (itemOffset > curOffset)
			{
				firstIndex = averageIndex + 1;
			}
			else
			{

				return &pBool[averageIndex];
			}
	}

	return NULL;
}


// загрузка ром модуля с описанием
static bool initOscDescr(void)
{
	void *pRommModule;
	unsigned char * pRommModuleData;
	size_t moduleDataSize;
	OscDescrHeader *descr;

	if (!loadRomModule('OSCD', &pRommModule, &pRommModuleData, &moduleDataSize,NULL))
	{
		return false;
	}


	descr = (OscDescrHeader *)pRommModuleData;
	// неверная версия
	if (descr->version != OSC_DESCR_VERSION)
	{
		free(descr);
		return false;
	}
	oscDescr = descr;
	
	return true;
} 

bool OSCDescr_init(void)
{
	if (!initOscDescr())
	{
		return false;
	}


	return true;
}

const char * OSCDescr_getTerminalName(void)
{
	return oscDescr->terminalName;
}

unsigned int OSCDescr_getTerminalVersion(void)
{
	return oscDescr->terminalVersion;
}


const char * OSCDescr_analogName(OscDescrAnalog *analog)
{
	return OSCDESCR_ANALOG_NAME(analog);
}
const char * OSCDescr_analogUnits(OscDescrAnalog *analog)
{
	return OSCDESCR_ANALOG_UNIT(analog);
}

const char * OSCDescr_boolName(OscDescrBool *pBool)
{
	return OSCDESCR_BOOL_NAME(pBool);
}

float OSCDescr_getFreq(void)
{
	return oscDescr->freq;
}


