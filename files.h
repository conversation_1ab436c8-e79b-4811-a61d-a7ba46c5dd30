#pragma once
typedef struct FileInfo FileInfo;
//! информация о файле (сейчас используется только для осцилограмм)
struct FileInfo
{
	//! имя (абсолютный номер осцилограммы/журнальной записи)
	unsigned int name;
	//! размер (для осцилограммы размер .smm)
	unsigned int size;
	//! время в формате 32.32
	unsigned long long time;
	//! для осцилограммы номер кластера, для журнала номер относительной записи
	unsigned int id;
	//! не используется
	unsigned long flags;
};
