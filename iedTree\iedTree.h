#pragma once

#include "iedEntity.h"

#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>

// Представление информационной модели в виде дерева в оперативной
// памяти.
// Дурацкое название IEDTree потому что IEDModel уже занято.

bool IEDTree_init(uint8_t* iedModel, size_t modelSize);

void IEDTree_lock(void);
void IEDTree_unlock(void);

IEDEntity IEDTree_findDataByFullName(StringView* ldName,
                                     StringView* dataName);
IEDEntity IEDTree_findDataSetByFullName(StringView* ldName,
									 StringView* dataSetName);
//! Находит Data Set по полному имени в виде одного StringView вида
//! LD/LN$DataSetName
IEDEntity IEDTree_findDataSetBySingleName(StringView* dataSetName);

MmsDataAccessError IEDTree_write(StringView* ldName,
                                 StringView* itemName, IsoConnection* isoConn,
                                   BufferView* value);

//! Проходит по списку сравнения, обнаруживает изменения и
//! обновляет данные в FinalDA из DataSlice
void IEDTree_updateFromDataSlice(void);

//! Добавляет DA в список для сравнения
void IEDTree_addToCmpList(IEDEntity da);
