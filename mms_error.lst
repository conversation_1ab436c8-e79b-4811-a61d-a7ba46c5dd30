                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_53c1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=mms_error.c -o gh_53c1.o -list=mms_error.lst C:\Users\<USER>\AppData\Local\Temp\gh_53c1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_53c1.s
Source File: mms_error.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile mms_error.c -o

                      10 ;		mms_error.o

                      11 ;Source File:   mms_error.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:56:04 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "mms_error.h"


                      21 ;2: 


                      22 ;3: #include "AsnEncoding.h"


                      23 ;4: #include "MmsConst.h"


                      24 ;5: #include <debug.h>


                      25 ;6: #include <stddef.h>


                      26 ;7: //Для memcpy


                      27 ;8: #include <string.h>


                      28 ;9: 


                      29 ;10: 


                      30 ;11: #define MMS_REJECT_CONFIRMED_REQUEST 1


                      31 ;12: //#define MMS_REJECT_CONFIRMED_RESPONSE 2


                      32 ;13: //#define MMS_REJECT_CONFIRMED_ERROR 3


                      33 ;14: //#define MMS_REJECT_UNCONFIRMED 4


                      34 ;15: #define MMS_REJECT_PDU_ERROR 5


                      35 ;16: //#define MMS_REJECT_CANCEL_REQUEST 6


                      36 ;17: //#define MMS_REJECT_CANCEL_RESPONSE 7


                      37 ;18: //#define MMS_REJECT_CANCEL_ERROR 8


                      38 ;19: //#define MMS_REJECT_CONCLUDE_REQUEST 9


                      39 ;20: //#define MMS_REJECT_CONCLUDE_RESPONSE 10


                      40 ;21: //#define MMS_REJECT_CONCLUDE_ERROR 11


                      41 ;22: 


                      42 ;23: #define MMS_REJECT_CONFIRMED_REQUEST_OTHER 0


                      43 ;24: #define MMS_REJECT_CONFIRMED_REQUEST_UNRECOGNIZED_SERVICE 1


                      44 ;25: //#define MMS_REJECT_CONFIRMED_REQUEST_UNRECOGNIZED_MODIFIER 2


                      45 ;26: //#define MMS_REJECT_CONFIRMED_REQUEST_INVALID_INVOKE_ID 3


                      46 ;27: #define MMS_REJECT_CONFIRMED_REQUEST_INVALID_ARGUMENT 4


                      47 ;28: //#define MMS_REJECT_CONFIRMED_REQUEST_INVALID_MODIFIER 5


                      48 ;29: //#define MMS_REJECT_CONFIRMED_REQUEST_MAX_SERV_OUTSTANDING_EXCEEDED 6


                      49 ;30: //#define MMS_REJECT_CONFIRMED_REQUEST_MAX_RECURSION_EXCEEDED 8


                      50 ;31: //#define MMS_REJECT_CONFIRMED_REQUEST_VALUE_OUT_OF_RANGE 9



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_53c1.s
                      51 ;32: 


                      52 ;33: #define MMS_REJECT_PDU_ERROR_UNKNOWN_PDU_TYPE 0


                      53 ;34: #define MMS_REJECT_PDU_ERROR_INVALID_PDU 1


                      54 ;35: //#define MMS_REJECT_PDU_ERROR_ILLEGAL_ACSI_MAPPING 2


                      55 ;36: 


                      56 ;37: static int encodeMmsRejectPdu(unsigned int* invokeId, int rejectType, int rejectReason,


                      57 

                      58 ;62: }


                      59 

                      60 ;63: 


                      61 ;64: int mms_createMmsRejectPdu(unsigned int* invokeId, int reason, uint8_t* outBuf)


                      62 	.text

                      63 	.align	4

                      64 mms_createMmsRejectPdu::

00000000 e92d44f0     65 	stmfd	[sp]!,{r4-r7,r10,lr}

                      66 ;65: {


                      67 

                      68 ;66:     int rejectType = 0;


                      69 

                      70 ;67:     int rejectReason = 0;


                      71 

                      72 ;68: 


                      73 ;69:     switch (reason) {


                      74 

00000004 e2511065     75 	subs	r1,r1,101

00000008 3a00000d     76 	blo	.L33

                      77 ;74:         break;


                      78 ;75: 


                      79 ;76:     case MMS_ERROR_REJECT_UNKNOWN_PDU_TYPE:


                      80 ;77:         rejectType = MMS_REJECT_PDU_ERROR;


                      81 

0000000c 03a06005     82 	moveq	r6,5

                      83 ;78:         rejectReason = MMS_REJECT_PDU_ERROR_UNKNOWN_PDU_TYPE;


                      84 

00000010 03a07000     85 	moveq	r7,0

00000014 0a00000c     86 	beq	.L27

00000018 e2511002     87 	subs	r1,r1,2

                      88 ;84:         break;


                      89 ;85: 


                      90 ;86:     case MMS_ERROR_REJECT_INVALID_PDU:


                      91 ;87:         rejectType = MMS_REJECT_PDU_ERROR;


                      92 

0000001c 33a06005     93 	movlo	r6,5

                      94 ;88:         rejectReason = MMS_REJECT_PDU_ERROR_INVALID_PDU;


                      95 

00000020 33a07001     96 	movlo	r7,1

00000024 3a000008     97 	blo	.L27

                      98 ;70: 


                      99 ;71:     case MMS_ERROR_REJECT_UNRECOGNIZED_SERVICE:


                     100 ;72:         rejectType = MMS_REJECT_CONFIRMED_REQUEST;


                     101 

00000028 03a06001    102 	moveq	r6,1

                     103 ;73:         rejectReason = MMS_REJECT_CONFIRMED_REQUEST_UNRECOGNIZED_SERVICE;


                     104 

0000002c 01a07006    105 	moveq	r7,r6

00000030 0a000005    106 	beq	.L27

00000034 e3510002    107 	cmp	r1,2

                     108 ;79:         break;


                     109 ;80: 


                     110 ;81:     case MMS_ERROR_REJECT_REQUEST_INVALID_ARGUMENT:


                     111 ;82:         rejectType = MMS_REJECT_CONFIRMED_REQUEST;



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_53c1.s
                     112 

00000038 03a06001    113 	moveq	r6,1

                     114 ;83:         rejectReason = MMS_REJECT_CONFIRMED_REQUEST_INVALID_ARGUMENT;


                     115 

0000003c 03a07004    116 	moveq	r7,4

00000040 0a000001    117 	beq	.L27

                     118 .L33:

                     119 ;89:         break;


                     120 ;90: 


                     121 ;91:     default:


                     122 ;92:         rejectType = MMS_REJECT_CONFIRMED_REQUEST;


                     123 

00000044 e3a06001    124 	mov	r6,1

                     125 ;93:         rejectReason = MMS_REJECT_CONFIRMED_REQUEST_OTHER;


                     126 

00000048 e3a07000    127 	mov	r7,0

                     128 .L27:

                     129 ;94:     }


                     130 ;95: 


                     131 ;96:     return encodeMmsRejectPdu(invokeId, rejectType, rejectReason, outBuf);


                     132 

0000004c e1b05000    133 	movs	r5,r0

00000050 e1a04002    134 	mov	r4,r2

                     135 ;38:                                uint8_t* outBuf)


                     136 ;39: {


                     137 

00000054 e3a0a000    138 	mov	r10,0

00000058 e3a01003    139 	mov	r1,3

                     140 ;40:     int outBufPos = 0;


                     141 

                     142 ;41:     unsigned int invokeIdLength = 0;


                     143 

                     144 ;42:     unsigned int rejectPduLength = 3;


                     145 

                     146 ;43: 


                     147 ;44:     if (invokeId != NULL) {


                     148 

0000005c 0a000003    149 	beq	.L38

                     150 ;45:         invokeIdLength = BerEncoder_UInt32determineEncodedSize(*invokeId);


                     151 

00000060 e5950000    152 	ldr	r0,[r5]

00000064 eb000000*   153 	bl	BerEncoder_UInt32determineEncodedSize

00000068 e1a0a000    154 	mov	r10,r0

                     155 ;46:         rejectPduLength += 2 + invokeIdLength;


                     156 

0000006c e28a1005    157 	add	r1,r10,5

                     158 .L38:

                     159 ;47:     }


                     160 ;48: 


                     161 ;49:     /* Encode reject PDU */


                     162 ;50:     outBufPos = BerEncoder_encodeTL(0xa4, rejectPduLength, outBuf, outBufPos);


                     163 

00000070 e1a02004    164 	mov	r2,r4

00000074 e3a03000    165 	mov	r3,0

00000078 e3a000a4    166 	mov	r0,164

0000007c eb000000*   167 	bl	BerEncoder_encodeTL

00000080 e1a03000    168 	mov	r3,r0

                     169 ;51: 


                     170 ;52:     if (invokeId != NULL) {


                     171 

00000084 e3550000    172 	cmp	r5,0


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_53c1.s
00000088 0a000008    173 	beq	.L40

                     174 ;53:        outBufPos = BerEncoder_encodeTL(0x80, invokeIdLength, outBuf, outBufPos);


                     175 

0000008c e1a02004    176 	mov	r2,r4

00000090 e1a0100a    177 	mov	r1,r10

00000094 e3a00080    178 	mov	r0,128

00000098 eb000000*   179 	bl	BerEncoder_encodeTL

                     180 ;54:        outBufPos = BerEncoder_encodeUInt32(*invokeId, outBuf, outBufPos);


                     181 

0000009c e1a02000    182 	mov	r2,r0

000000a0 e5950000    183 	ldr	r0,[r5]

000000a4 e1a01004    184 	mov	r1,r4

000000a8 eb000000*   185 	bl	BerEncoder_encodeUInt32

000000ac e1a03000    186 	mov	r3,r0

                     187 .L40:

                     188 ;55:     }


                     189 ;56: 


                     190 ;57:     outBuf[outBufPos++] = (uint8_t) (0x80 + rejectType);


                     191 

000000b0 e2860080    192 	add	r0,r6,128

000000b4 e7c40003    193 	strb	r0,[r4,r3]

000000b8 e2833001    194 	add	r3,r3,1

                     195 ;58:     outBuf[outBufPos++] = 0x01;


                     196 

000000bc e3a00001    197 	mov	r0,1

000000c0 e7c40003    198 	strb	r0,[r4,r3]

000000c4 e2833001    199 	add	r3,r3,1

                     200 ;59:     outBuf[outBufPos++] = (uint8_t) rejectReason;


                     201 

000000c8 e7c47003    202 	strb	r7,[r4,r3]

                     203 ;60: 


                     204 ;61:     return outBufPos;


                     205 

000000cc e2830001    206 	add	r0,r3,1

000000d0 e8bd84f0    207 	ldmfd	[sp]!,{r4-r7,r10,pc}

                     208 	.endf	mms_createMmsRejectPdu

                     209 	.align	4

                     210 ;rejectType	r6	local

                     211 ;rejectReason	r7	local

                     212 ;invokeId	r5	local

                     213 ;outBuf	r4	local

                     214 ;outBufPos	r3	local

                     215 ;invokeIdLength	r10	local

                     216 ;rejectPduLength	r1	local

                     217 

                     218 ;invokeId	r0	param

                     219 ;reason	r1	param

                     220 ;outBuf	r2	param

                     221 

                     222 	.section ".bss","awb"

                     223 .L119:

                     224 	.data

                     225 	.text

                     226 

                     227 ;97: }


                     228 

                     229 ;98: 


                     230 ;99: 


                     231 ;100: int CreateMmsConfirmedErrorPdu( unsigned int iInvokeId, unsigned char* pResponseBuffer,


                     232 	.align	4

                     233 	.align	4


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_53c1.s
                     234 CreateMmsConfirmedErrorPdu::

000000d4 e92d44f0    235 	stmfd	[sp]!,{r4-r7,r10,lr}

000000d8 e1a05001    236 	mov	r5,r1

                     237 ;101:                                 MmsError ErrorType )


                     238 ;102: {


                     239 

000000dc e59f1128*   240 	ldr	r1,.L315

000000e0 e5913000    241 	ldr	r3,[r1]

000000e4 e1a04002    242 	mov	r4,r2

000000e8 e52d3008    243 	str	r3,[sp,-8]!

000000ec e1d130b4    244 	ldrh	r3,[r1,4]

000000f0 e1a07000    245 	mov	r7,r0

000000f4 e1cd30b4    246 	strh	r3,[sp,4]

000000f8 e5d11006    247 	ldrb	r1,[r1,6]

000000fc e3a0a000    248 	mov	r10,0

                     249 ;103:     unsigned char ErrorCodeBuf[] = { 0xa2, 0x05, 0xa0, 0x03, 0x87, 0x01, 0x01 };


                     250 

                     251 ;104:     int iErrorCodeBufSize = 7;


                     252 

                     253 ;105: 


                     254 ;106:     int iIvokeIdSize = 2 + BerEncoder_UInt32determineEncodedSize( iInvokeId );


                     255 

00000100 e5cd1006    256 	strb	r1,[sp,6]

00000104 eb000000*   257 	bl	BerEncoder_UInt32determineEncodedSize

00000108 e2806004    258 	add	r6,r0,4

                     259 ;107:     int iPduLength = 2 + iIvokeIdSize;


                     260 

                     261 ;108: 


                     262 ;109:     int iBufPos = 0;


                     263 

                     264 ;110:     int exists_error = 0;


                     265 

                     266 ;111: 


                     267 ;112:     if (ErrorType == MMS_ERROR_ACCESS_OTHER) {


                     268 

0000010c e3540050    269 	cmp	r4,80

                     270 ;113:         ErrorCodeBuf[6] = 0x00;


                     271 

00000110 03a00000    272 	moveq	r0,0

00000114 05cd0006    273 	streqb	r0,[sp,6]

                     274 ;114:         exists_error = 1;


                     275 

00000118 03a0a001    276 	moveq	r10,1

                     277 ;115:     }


                     278 ;116:     if (ErrorType == MMS_ERROR_ACCESS_OBJECT_ACCESS_UNSUPPORTED) {


                     279 

0000011c e3540052    280 	cmp	r4,82

                     281 ;117:         ErrorCodeBuf[6] = 0x01;


                     282 

00000120 03a0a001    283 	moveq	r10,1

00000124 05cda006    284 	streqb	r10,[sp,6]

                     285 ;118:         exists_error = 1;


                     286 

                     287 ;119:     }


                     288 ;120:     if (ErrorType == MMS_ERROR_ACCESS_OBJECT_NON_EXISTENT) {


                     289 

00000128 e3540051    290 	cmp	r4,81

                     291 ;121:         ErrorCodeBuf[6] = 0x02;


                     292 

0000012c 03a00002    293 	moveq	r0,2

00000130 05cd0006    294 	streqb	r0,[sp,6]


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_53c1.s
                     295 ;122:         exists_error = 1;


                     296 

00000134 03a0a001    297 	moveq	r10,1

                     298 ;123:     }


                     299 ;124:     if (ErrorType == MMS_ERROR_ACCESS_OBJECT_ACCESS_DENIED) {


                     300 

00000138 e3540053    301 	cmp	r4,83

                     302 ;125:         ErrorCodeBuf[6] = 0x03;


                     303 

0000013c 03a00003    304 	moveq	r0,3

00000140 05cd0006    305 	streqb	r0,[sp,6]

                     306 ;126:         exists_error = 1;


                     307 

00000144 03a0a001    308 	moveq	r10,1

                     309 ;127:     }


                     310 ;128:     if (ErrorType == MMS_ERROR_ACCESS_OBJECT_INVALIDATED) {


                     311 

00000148 e3540054    312 	cmp	r4,84

                     313 ;129:         ErrorCodeBuf[6] = 0x04;


                     314 

0000014c 03a00004    315 	moveq	r0,4

00000150 05cd0006    316 	streqb	r0,[sp,6]

                     317 ;130:         exists_error = 1;


                     318 

00000154 0a000002    319 	beq	.L167

                     320 ;131:     }


                     321 ;132: 


                     322 ;133:     if (exists_error != 0) {


                     323 

00000158 e35a0000    324 	cmp	r10,0

                     325 ;141:     }


                     326 ;142:     else {


                     327 

                     328 ;143:         return -1;


                     329 

0000015c 03e00000    330 	mvneq	r0,0

00000160 0a00000e    331 	beq	.L154

                     332 .L167:

                     333 ;134:         iPduLength += iErrorCodeBufSize;


                     334 

                     335 ;135:         iBufPos = BerEncoder_encodeTL(MMS_CONFIRMED_ERRROR_PDU, iPduLength - 2,


                     336 

00000164 e1a02005    337 	mov	r2,r5

00000168 e2861005    338 	add	r1,r6,5

0000016c e3a03000    339 	mov	r3,0

00000170 e3a000a2    340 	mov	r0,162

00000174 eb000000*   341 	bl	BerEncoder_encodeTL

                     342 ;136:             pResponseBuffer, iBufPos);


                     343 ;137:         iBufPos = BerEncoder_encodeUInt32WithTL(0x80, iInvokeId,


                     344 

00000178 e1a02005    345 	mov	r2,r5

0000017c e1a01007    346 	mov	r1,r7

00000180 e1a03000    347 	mov	r3,r0

00000184 e3a00080    348 	mov	r0,128

00000188 eb000000*   349 	bl	BerEncoder_encodeUInt32WithTL

                     350 ;138:             pResponseBuffer, iBufPos);


                     351 ;139:         memcpy(&pResponseBuffer[iBufPos], ErrorCodeBuf, iErrorCodeBufSize);


                     352 

0000018c e1a0100d    353 	mov	r1,sp

00000190 e0800005    354 	add	r0,r0,r5

00000194 e3a02007    355 	mov	r2,7


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_53c1.s
00000198 eb000000*   356 	bl	memcpy

0000019c e2860007    357 	add	r0,r6,7

                     358 ;140:         return iPduLength;


                     359 

                     360 .L154:

000001a0 e28dd008    361 	add	sp,sp,8

000001a4 e8bd84f0    362 	ldmfd	[sp]!,{r4-r7,r10,pc}

                     363 	.endf	CreateMmsConfirmedErrorPdu

                     364 	.align	4

                     365 ;ErrorCodeBuf	[sp]	local

                     366 ;.L279	.L282	static

                     367 ;iPduLength	r6	local

                     368 ;exists_error	r10	local

                     369 

                     370 ;iInvokeId	r7	param

                     371 ;pResponseBuffer	r5	param

                     372 ;ErrorType	r4	param

                     373 

                     374 	.section ".bss","awb"

                     375 .L278:

                     376 	.section ".rodata","a"

00000000 03a005a2    377 .L282:	.data.b	162,5,160,3

00000004 0187       378 	.data.b	135,1

00000006 01         379 	.data.b	1

00000007 00         380 	.space	1

                     381 	.type	.L282,$object

                     382 	.size	.L282,8

                     383 	.data

                     384 	.text

                     385 

                     386 ;144:     }


                     387 ;145: }


                     388 

                     389 ;146: 


                     390 ;147: 


                     391 ;148: bool MMSError_createConfirmedErrorPdu(uint32_t invokeId, MmsError errorType,


                     392 	.align	4

                     393 	.align	4

                     394 MMSError_createConfirmedErrorPdu::

000001a8 e92d4070    395 	stmfd	[sp]!,{r4-r6,lr}

                     396 ;149:     BufferView* outBufView)


                     397 ;150: {   


                     398 

                     399 ;151:     // Это обёртка над CreateMmsConfirmedErrorPdu


                     400 ;152:     size_t maxErrPduLen = 15;


                     401 

                     402 ;153:     int pduLen;


                     403 ;154:     uint8_t* pduBuf;


                     404 ;155:     if (!BufferView_alloc(outBufView, maxErrPduLen, &pduBuf))


                     405 

000001ac e24dd004    406 	sub	sp,sp,4

000001b0 e1a04002    407 	mov	r4,r2

000001b4 e1a0200d    408 	mov	r2,sp

000001b8 e1a05000    409 	mov	r5,r0

000001bc e1a00004    410 	mov	r0,r4

000001c0 e1a06001    411 	mov	r6,r1

000001c4 e3a0100f    412 	mov	r1,15

000001c8 eb000000*   413 	bl	BufferView_alloc

000001cc e3500000    414 	cmp	r0,0

000001d0 0a00000a    415 	beq	.L325

                     416 ;156:     {



                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_53c1.s
                     417 

                     418 ;157:         ERROR_REPORT("Unable to allocate buffer");


                     419 ;158:         return false;


                     420 

                     421 ;159:     }


                     422 ;160:     pduLen = CreateMmsConfirmedErrorPdu(invokeId, pduBuf, errorType);


                     423 

000001d4 e1a02006    424 	mov	r2,r6

000001d8 e59d1000    425 	ldr	r1,[sp]

000001dc e1a00005    426 	mov	r0,r5

000001e0 ebffffbb*   427 	bl	CreateMmsConfirmedErrorPdu

000001e4 e1b01000    428 	movs	r1,r0

                     429 ;161:     if (pduLen < 0)


                     430 

000001e8 4a000004    431 	bmi	.L325

                     432 ;162:     {


                     433 

                     434 ;163:         ERROR_REPORT("Unable to create MMS error PDU");


                     435 ;164:         return false;


                     436 

                     437 ;165:     }


                     438 ;166:     if (!BufferView_advance(outBufView, pduLen))


                     439 

000001ec e1a00004    440 	mov	r0,r4

000001f0 eb000000*   441 	bl	BufferView_advance

000001f4 e3500000    442 	cmp	r0,0

                     443 ;170:     }


                     444 ;171:     return true; 


                     445 

000001f8 13a00001    446 	movne	r0,1

000001fc 1a000000    447 	bne	.L316

                     448 .L325:

                     449 ;167:     {


                     450 

                     451 ;168:         ERROR_REPORT("Unable to advance buffer view");


                     452 ;169:         return false;


                     453 

00000200 e3a00000    454 	mov	r0,0

                     455 .L316:

00000204 e28dd004    456 	add	sp,sp,4

00000208 e8bd8070    457 	ldmfd	[sp]!,{r4-r6,pc}

                     458 	.endf	MMSError_createConfirmedErrorPdu

                     459 	.align	4

                     460 ;pduLen	r1	local

                     461 ;pduBuf	[sp]	local

                     462 

                     463 ;invokeId	r5	param

                     464 ;errorType	r6	param

                     465 ;outBufView	r4	param

                     466 

                     467 	.section ".bss","awb"

                     468 .L390:

                     469 	.data

                     470 	.text

                     471 

                     472 ;172: }


                     473 	.align	4

                     474 .L315:

0000020c 00000000*   475 	.data.w	.L282

                     476 	.type	.L315,$object

                     477 	.size	.L315,4


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_53c1.s
                     478 

                     479 	.align	4

                     480 

                     481 	.data

                     482 	.ghsnote version,6

                     483 	.ghsnote tools,3

                     484 	.ghsnote options,0

                     485 	.text

                     486 	.align	4

                     487 	.section ".rodata","a"

                     488 	.align	4

                     489 	.text

