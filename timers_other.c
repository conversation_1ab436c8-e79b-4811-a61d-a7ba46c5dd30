#include "timers.h"
#include "Clib.h"
#include "string.h"

//Проверка кодировки


static STIMER g_gooseTimer;
static uint32_t tickCounter32 = 0;

static void(*goose1msCallbackFunc)(void) = NULL;
static void(*integrity1msCallbackFunc)(void) = NULL;

static void timerProc(void)
{
    if (goose1msCallbackFunc)
    {
        goose1msCallbackFunc();
    }
    if (integrity1msCallbackFunc)
    {
        integrity1msCallbackFunc();
    }
    tickCounter32++;
}

void Timers_setGoose1msCallBack(void(*func)(void))
{
    goose1msCallbackFunc = func;
}

void Timers_setIntegrity1msCallBack(void(*func)(void))
{
    integrity1msCallbackFunc = func;
}

void Timers_setNetBusChek1msCallback(void(*func)(void))
{
	//Проверка шины делается только в контроллере присоединения, поэтому
	//таймер не назначается
}

void Timers_init(void)
{
    memset(&g_gooseTimer, 0, sizeof(g_gooseTimer));
    CreateTimer(&g_gooseTimer);
    g_gooseTimer.TimerProc = timerProc;
    g_gooseTimer.AlarmTime = 1;
    g_gooseTimer.Precision = 1;
    g_gooseTimer.Started = 1;
}

uint32_t Timers_getTickCount(void)
{
    return tickCounter32;
}

bool Timers_isTimeout(uint32_t startTime, uint32_t timeout)
{
    uint32_t tickCount = Timers_getTickCount();
    uint32_t timePassed;

    // сколько прошло времени с момента запуска
    if (startTime  <= tickCount)
    {
        timePassed = tickCount - startTime;
    }
    else // переполнение
    {
        timePassed = UINT32_MAX - startTime + tickCount;
    }
    return timePassed > timeout;
}

