#pragma once


#include "RptItemTypes.h"
#include "../iedTree/DataSet.h"
#include "../reports.h"
#include "RptProcContext.h"
#include <stddef.h>


struct RptDataSetItemStruct {
	RptDataSet parentRptDataSet;
    RptItem rptItem;
	//! Список только FinalDA RptItem для обнаружения изменений.
	RptItem firstFinalDAItem, lastFinalDAItem;

	TrgOps changedOld;
	TrgOps changedNew;
	bool needsSend;
	RptDataSetItem next;
};

struct RptDataSetStruct {
	PReporter reporter;
	RptDataSetItem firstItem, lastItem;
	size_t itemCount;

	//! Флаг, что есть какие-то новые измениния
	bool hasChanges;

	//! Флаг, что есть новые изменения конфликтующе со старыми
	//! и надо посылать отчёт со старыми изменениями.
	bool needsSend;
	
};

//! Добавляет элемент RptItem в список для обнаружения изменений.
void RptDataSetItem_addFinalDARptItem(
	RptDataSetItem rptDSItem, RptItem rptItem);

RptDataSet RptDataSet_create(PReporter reporter);

//! Обновляет состояние из IEDTree после DataSlice
void RptDataSet_updateChanges(RptDataSet rptDataSet);

//! Функция копирует новые изменения поверх старых. 
//! Если если есть конфиликты, то старые данные помещаются в отчёт.
bool RptDataSet_flushAndUpdate(RptProcContext* ctx);
