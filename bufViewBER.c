#include "bufViewBER.h"
#include "AsnEncoding.h"

bool BufferView_peekTag(BufferView* bv, uint8_t* tag)
{
    if (bv->pos < bv->len)
    {
        *tag = bv->p[bv->pos];
        return TRUE;
    }
    else
    {
        return FALSE;
    }
}

bool BufferView_skipObject(BufferView* bv, uint8_t tag, bool strict)
{
    uint8_t objTag;
    size_t len;

    if (!BufferView_peekTag(bv, &objTag))
    {
        return FALSE;
    }
    if (objTag != tag)
    {
        return !strict;
    }
    return BufferView_decodeTL(bv, NULL, &len, NULL)
        && BufferView_advance(bv, len);
}

bool BufferView_skipAnyObject(BufferView* bv)
{
    size_t len;
    return BufferView_decodeTL(bv, NULL, &len, NULL)
        && BufferView_advance(bv, len);
}

bool BufferView_decodeTL(BufferView* bv, uint8_t* pTag, size_t* pLen, size_t* pFullLen)
{
    uint8_t tag;
    int len;
    int objPos = bv->pos;

    if (bv->len - bv->pos < 2)
    {
        return FALSE;
    }

    tag = BufferView_readTag(bv);
    bv->pos = BerDecoder_decodeLength(bv->p, &len, bv->pos, bv->len);
    if (bv->pos <= 0)
    {
        return FALSE;
    }

    if (pTag != NULL)
    {
        *pTag = tag;
    }

    if (pLen != NULL)
    {
        *pLen = len;
    }

    if (pFullLen != NULL)
    {
        *pFullLen = bv->pos - objPos + len;
    }

    return TRUE;
}

bool BufferView_decodeExtTL(BufferView* bv, uint32_t* pTag, size_t* pLen, size_t* pFullLen)
{
    uint32_t tag;
    //Запоминаем начало объекта чтобы потом посчитать полную длину
    size_t objPos = bv->pos;
    size_t len;
    int newPos;


    if (bv->len - bv->pos < 2)
    {
        return false;
    }

    tag = BufferView_readTag(bv);
    if ((tag & 0x1f) == 0x1f)
    {
        //Multibyte tag
        tag <<= 8;
        tag |= BufferView_readTag(bv);
        if (tag & 0x80)
        {
            //Тэг больше двух байт. С такими пока не работаем.
            return false;
        }
    }

    newPos = BerDecoder_decodeLength(bv->p, (int*)&len, bv->pos, bv->len);
    if (newPos <= 0)
    {
        return false;
    }
    bv->pos = newPos;

    if (pTag != NULL)
    {
        *pTag = tag;
    }

    if (pLen != NULL)
    {
        *pLen = len;
    }

    if (pFullLen != NULL)
    {
        *pFullLen = bv->pos - objPos + len;
    }

    return true;
}

bool BufferView_decodeInt32(BufferView* bv, size_t intLen, int32_t* result)
{
    if (bv->pos + intLen > bv->len)
    {
        return false;
    }
    *result = BerDecoder_decodeInt32(bv->p, intLen, bv->pos);
    bv->pos += intLen;
    return true;
}

bool BufferView_decodeUInt32(BufferView* bv, size_t intLen, uint32_t* result)
{
    if (bv->pos + intLen > bv->len)
    {
        return FALSE;
    }
    *result = BerDecoder_decodeUint32(bv->p, intLen, bv->pos);
    bv->pos += intLen;
    return TRUE;
}

bool BufferView_decodeInt32TL(BufferView* bv, uint8_t expectedTag,
    int32_t* result)
{
    uint8_t tag;
    size_t intLen;
    if (!BufferView_decodeTL(bv, &tag, &intLen, NULL) || tag != expectedTag)
    {
        return false;
    }

    return BufferView_decodeInt32(bv, intLen, result);
}

bool BufferView_decodeUInt32TL(BufferView* bv, uint8_t expectedTag,
    uint32_t* result)
{
    uint8_t tag;
    size_t intLen;
    if (!BufferView_decodeTL(bv, &tag, &intLen, NULL) || tag != expectedTag)
    {
        return FALSE;
    }

    return BufferView_decodeUInt32(bv, intLen, result);
}

bool BufferView_decodeStringViewTL(BufferView* bv, uint8_t expectedTag,
    StringView* result)
{
    uint8_t tag;
    size_t len;
    if (!BufferView_decodeTL(bv, &tag, &len, NULL) || tag != expectedTag)
    {
        return FALSE;
    }
    StringView_init(result, (const char*)bv->p + bv->pos, len);
    bv->pos += len;
    return TRUE;
}

bool BufferView_writeTag(BufferView* bv, uint8_t tag)
{
    if (bv->pos + 1 > bv->len)
    {
        //Не лезет в буфер
        return FALSE;
    }
    bv->p[bv->pos++] = tag;
    return TRUE;
}

bool BufferView_encodeStringView(BufferView* bv, uint8_t tag,
    const StringView* strView)
{
    size_t fullSize = BerEncoder_determineFullObjectSize(strView->len);
    size_t freeSpace = bv->len - bv->pos;
    if (fullSize > freeSpace)
    {
        return FALSE;
    }
    //Пишем тэг
    bv->p[bv->pos++] = tag;

    if (strView->len == 0)
    {
        //Пишем длину
        bv->p[bv->pos++] = 0;
        //Больше писать нечего - пустая строка
    }
    else
    {
        //Пишем длину
        bv->pos = BerEncoder_encodeLength(strView->len, bv->p, bv->pos);
    }
    return BufferView_writeStringView(bv, strView);
}

bool BufferView_encodeStr(BufferView* bv, uint8_t tag, const char* str)
{
    StringView strView;
    StringView_fromCStr(&strView, (char*)str);
    return BufferView_encodeStringView(bv, tag, &strView);
}

bool BufferView_encodeTL(BufferView* bv, uint8_t tag, size_t length)
{
    size_t lenSize = BerEncoder_determineLengthSize(length);
    if (1 + lenSize > bv->len - bv->pos)
    {
        return FALSE;
    }
    bv->pos = BerEncoder_encodeTL(tag, length, bv->p, bv->pos);
    return TRUE;
}

bool BufferView_encodeExtTL(BufferView* bv, uint16_t extTag, size_t length)
{
    size_t lenSize = BerEncoder_determineLengthSize(length);
    if (2 + lenSize > bv->len - bv->pos)
    {
        return FALSE;
    }
    bv->p[bv->pos++] = extTag >> 8;
    bv->pos = BerEncoder_encodeTL(extTag & 0xFF, length, bv->p, bv->pos);
    return TRUE;
}

bool BufferView_encodeInt32(BufferView* bv, uint8_t tag, int32_t value)
{
    int encodedLen = 2 //тэг + длина
        + BerEncoder_Int32DetermineEncodedSize(value);
    if (bv->pos + encodedLen > bv->len)
    {
        //Не лезет в буфер
        return false;
    }
    bv->pos = BerEncoder_EncodeInt32WithTL(tag, value, bv->p, bv->pos);
    return true;
}

bool BufferView_encodeUInt32(BufferView* bv, uint8_t tag, uint32_t value)
{
    int encodedLen = 2 //тэг + длина
        + BerEncoder_UInt32determineEncodedSize(value);
    if (bv->pos + encodedLen > bv->len)
    {
        //Не лезет в буфер
        return FALSE;
    }
    bv->pos = BerEncoder_encodeUInt32WithTL(tag, value, bv->p, bv->pos);
    return TRUE;
}

bool BufferView_encodeBoolean(BufferView* bv, uint8_t tag, bool val)
{
    if (bv->pos + 3 > bv->len)
    {
        //Не лезет в буфер
        return FALSE;
    }
    bv->pos = BerEncoder_encodeBoolean(tag, val, bv->p, bv->pos);
    return TRUE;
}

bool BufferView_encodeOctetString(BufferView* bv, uint8_t tag, void* data,
    size_t dataLen)
{
    int encodedLen = BerEncoder_determineFullObjectSize(dataLen);
    if (bv->pos + encodedLen > bv->len)
    {
        //Не лезет в буфер
        return FALSE;
    }
    bv->pos = BerEncoder_encodeOctetString(tag, data, dataLen, bv->p, bv->pos);
    return TRUE;
}

bool BufferView_encodeBufferView(BufferView* bv, uint8_t tag, BufferView* data)
{
    return BufferView_encodeOctetString(bv, tag, data->p, data->pos);
}

bool BufferView_reverseWrite(BufferView *bv, const void *src, size_t len)
{
    size_t i;
    const uint8_t* pSrc = src;

    if (bv->pos + len > bv->len)
    {
        return false;
    }

    pSrc += len - 1;

    for (i = 0; i < len; i++)
    {
        bv->p[bv->pos++] = *pSrc--;
    }
    return true;
}

/*
BOOL_T BufferView_encodeBitString(BufferView* bv, uint8_t tag,
    size_t bitStringSize, uint8_t* bitString)
{
    size_t byteSize = bitStringSize / 8;

    if (bitStringSize % 8)
    {
        byteSize++;
    }
    byteSize++;//padding



    !!!
    BerEncoder_encodeBitString(tag, bitStringSize, bitString, bv->p, bv->pos)
}
*/
