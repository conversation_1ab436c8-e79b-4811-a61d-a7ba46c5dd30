#pragma once

#include <stdint.h>

#ifdef SYSLOG

    //Загадочная магия чтобы можно было вставлять номер строки
    #define CONCAT2(x) #x
    #define CONCAT1(x) CONCAT2(x)

    #define ERROR_REPORT(...) printf_wrapper("ERROR in " __FILE__ \
    " line " CONCAT1(__LINE__)  ": ", __VA_ARGS__)


    #define TRACE(...) printf_wrapper(__VA_ARGS__)


    #define VERIFY(...)

    #define  WRITE_TO_FILE(...)

    void printf_wrapper(char* fmtMsg, ...);

#else

    #define ERROR_REPORT(...)
    #define TRACE(...)

#endif

#define VERIFY(...)
#define  WRITE_TO_FILE(...)

void debugStart(void);
void debugSendText(char* text);
void debugSendUshort(char* text, unsigned short value);
void debugSendStrL(char* text, uint8_t* str, int strLen);
void debugSendDump(char* text, uint8_t* data, int byteCount);

