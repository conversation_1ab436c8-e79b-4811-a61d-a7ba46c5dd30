#include "iedObjects.h"

#include "iedFinalDA.h"
#include "iedControlModel.h"

#include "../iedmodel.h"
#include "../BaseAsnTypes.h"
#include "../AsnEncoding.h"
#include "IEDCompile/InnerAttributeTypes.h"
#include "IEDCompile/AccessInfo.h"
#include "../mms_gocb.h"
#include "../mms_rcb.h"
#include "../pwin_access.h"
#include "../control.h"
#include "../timers.h"
#include <Clib.h>
#include <string.h>


typedef struct {
    IEDEntity dataSection;
    IEDEntity dataSetSection;
} IEDLD;

bool IEDLD_init(IEDEntity entity)
{
    IEDLD* extInfo = IEDEntity_alloc(sizeof(IEDLD));
    if(extInfo == NULL)
    {
        return false;
    }
    entity->extInfo = extInfo;
    entity->type = IED_ENTITY_LD;
    extInfo->dataSection = IEDEntity_getChildByTag(entity, IED_VMD_DATA_SECTION);
    extInfo->dataSetSection = IEDEntity_getChildByTag(entity, IED_VMD_DATA_SET_SECTION);
    return true;
}

IEDEntity IEDLD_getDataSection(IEDEntity ld)
{
    IEDLD* extInfo = ld->extInfo;
    if(extInfo == NULL)
    {
        return NULL;
    }
    return extInfo->dataSection;
}

IEDEntity IEDLD_getDataSetSection(IEDEntity ld)
{
    IEDLD* extInfo = ld->extInfo;
    if(extInfo == NULL)
    {
        return NULL;
    }
    return extInfo->dataSetSection;
}

bool IEDComplexObj_init(IEDEntity entity)
{
	switch(entity->tag)
	{
	case IED_VMD_DATA_SECTION:
		entity->type = IED_ENTITY_DATA_SECTION;
		break;
	case IED_LN:
		entity->type = IED_ENTITY_LN;		
		break;
	case IED_FC:
		entity->type = IED_ENTITY_FC;
		break;
	default:
		ERROR_REPORT("Invalid object tag");
		return false;
	}
	return true;
}

bool IEDDO_init(IEDEntity entity)
{
	IEDEntity timeStampDA;
    IEDEntity oper;
    entity->type = IED_ENTITY_DO;

	// Если у этого DO есть t, то для него и всех его
	// детей устанавлмваем указатель на объект timeStamp
	timeStampDA = IEDDO_getTimeStampDA(entity);
	if (timeStampDA != NULL)
	{
		TimeStamp* pTimeStamp = timeStampDA->extInfo;
		IEDEntity_attachTimeStamp(entity, pTimeStamp);
	}

    oper = IEDEntity_getChildByCStrName(entity, "Oper");
	if(oper != NULL && IEDControlDA_isControlDA(oper))
    {
        return IEDControlDO_init(entity);
    }
    return true;
}

IEDEntity IEDDO_getTimeStampDA(IEDEntity entity)
{
	IEDEntity child = entity->firstChild;
	while(child != NULL)
	{
		if(child->type == IED_ENTITY_DA_TIMESTAMP
		   //Timestamp бывает не только t, а нас интересует только t
		   && StringView_cmpCStr(&child->name, "t") == 0)
		{
			return child;
		}
		child = child->next;
	}
	return NULL;
}

bool IEDDA_init(IEDEntity entity)
{
    uint8_t tag;
    BufferView ber = entity->ber;
    entity->type = IED_ENTITY_DA;

    //Проверяем наличие информации о сервисе Control
    if(entity->name.p == NULL)
    {
        //Если нет имени, то и дополнительной информации нет
        return true;
    }

    //Пропустить тэг и длину
    BufferView_decodeTL(&ber, NULL, NULL, NULL);

    if(BufferView_endOfBuf(&ber))
    {
        return true;
    }

    //Пропустить имя
    if(!BufferView_skipObject(&ber, ASN_VISIBLE_STRING, false))
    {
        ERROR_REPORT("Name is not found");
        return false;
    }

    if(!BufferView_peekTag(&ber, &tag))
    {
        return true;
    }

    if(tag != IED_CONTROL_INFO)
    {
        return true;
    }

    return IEDControlDA_init(entity, &ber);
}

bool IEDConstDA_calcReadLen(IEDEntity entity, size_t *pLen)
{
    int dataLen;
    int constPos;

    if(entity->type != IED_ENTITY_DA_CONST)
    {
        ERROR_REPORT("Invalid terminal item DA");
        return false;
    }

    //Получаем позицию в модели для вызова старой функции
    constPos = (int)entity->extInfo;

    dataLen = encodeReadConst(0, 0, constPos, true);
    if(dataLen <=0)
    {
        ERROR_REPORT("Invalid read length");
        return false;
    }
	*pLen = dataLen;

    return true;
}

bool IEDConstDA_encodeRead(IEDEntity entity, BufferView *outBufView)
{
    int dataLen;
    int constPos;
    uint8_t *writeBuf;

    if(entity->type != IED_ENTITY_DA_CONST)
    {
        ERROR_REPORT("Invalid terminal item DA");
        return false;
    }

    //Получаем позицию в модели для вызова старой функции
    constPos = (int)entity->extInfo;

    writeBuf = outBufView->p + outBufView->pos;

    dataLen = encodeReadConst(writeBuf, 0, constPos, false);
    if(dataLen <=0)
    {
        ERROR_REPORT("Invalid read length");
        return false;
    }

    if(!BufferView_advance(outBufView, dataLen))
    {
        ERROR_REPORT("Buffer overflow");
        return false;
    }
    return true;
}



MmsDataAccessError IEDComplexObj_write(IEDEntity entity,
                                           IsoConnection* isoConn, BufferView* value)
{
    uint8_t tag;
    size_t len;
    IEDEntity nextChild;
    BufferView controlValue;

    if(entity->type != IED_ENTITY_DA  && entity->type != IED_ENTITY_DO)
    {
        ERROR_REPORT("Invalid object to write");
        return DATA_ACCESS_ERROR_TYPE_INCONSISTENT;
    }

    if(!BufferView_decodeTL(value, &tag, &len, NULL))
    {
        ERROR_REPORT("Invalid write value");
        return DATA_ACCESS_ERROR_OBJECT_VALUE_INVALID;
    }
    if(tag != 0xA2)
    {
        ERROR_REPORT("Structure tag expected");
        return DATA_ACCESS_ERROR_OBJECT_VALUE_INVALID;
    }

	if(IEDControlDA_isControlDA(entity))
    {
        if(IEDControlDA_isReady(entity))
        {
            return DATA_ACCESS_ERROR_TEMPORARILY_UNAVAILABLE;
        }
        //Сохраняем значение для control объектов
        BufferView_init(&controlValue, value->p + value->pos, len, 0);
    }


    nextChild = entity->firstChild;
    while(nextChild != NULL)
    {
        MmsDataAccessError result =
                IEDEntity_write(nextChild, isoConn, value);
        if(result != DATA_ACCESS_ERROR_SUCCESS)
        {
            return result;
        }
        nextChild = nextChild->next;
    }

	if(IEDControlDA_isControlDA(entity))
    {
		return IEDControlDA_write(entity, isoConn, &controlValue);
    }
    else
    {
        return DATA_ACCESS_ERROR_SUCCESS;
    }

}

static bool calcChildrenReadLen(IEDEntity entity, size_t* pLen )
{
    IEDEntity nextChild;
    size_t childrenLen = 0;

    nextChild = entity->firstChild;
    while(nextChild != NULL)
    {
        size_t childLen;
		bool result = nextChild->calcReadLen(nextChild, &childLen);
        if(!result)
        {
            return false;
        }
        childrenLen += childLen;
        nextChild = nextChild->next;
    }
    *pLen = childrenLen;
    return true;
}

bool IEDComplexObj_calcReadLen(IEDEntity entity, size_t* pLen)
{
    size_t childrenLen;

    if(!calcChildrenReadLen(entity, &childrenLen))
    {
        ERROR_REPORT("calcChildrenReadLen error");
        return false;
    }

    *pLen = 1 + BerEncoder_determineLengthSize(childrenLen) + childrenLen;
    return true;
}

bool IEDComplexObj_encodeRead(IEDEntity entity, BufferView* outBuf)
{
    IEDEntity nextChild;
    size_t childrenReadLen;

    if(entity->type != IED_ENTITY_LN
            && entity->type != IED_ENTITY_FC
            && entity->type != IED_ENTITY_DO
            && entity->type != IED_ENTITY_DA)
    {
        ERROR_REPORT("Invalid complex object");
        return false;
    }

    if(!calcChildrenReadLen(entity, &childrenReadLen))
    {
        ERROR_REPORT("calcChildrenReadLen error");
        return false;
    }

    //Тэг и размер структуры
    if(!BufferView_encodeTL(outBuf, 0xA2, childrenReadLen))
    {
        ERROR_REPORT("Children TL encoding error");
        return false;
    }

    nextChild = entity->firstChild;
    while(nextChild != NULL)
    {
		bool result = nextChild->encodeRead(nextChild, outBuf);
        if(!result)
        {
            ERROR_REPORT("Error reading child");
            return false;
        }
        nextChild = nextChild->next;
    }
    return true;
}
