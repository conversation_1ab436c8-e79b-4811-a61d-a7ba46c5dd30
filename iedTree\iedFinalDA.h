
#include "iedEntity.h"

#include "iedQuality.h"
#include "iedBool.h"
#include "iedFloat.h"
#include "iedCodedEnum.h"
#include "iedEnum.h"

//Объекты Final DA для IEDtree



//! Подтипы FinalDA типа "переменная" (IED_ENTITY_DA_VAR)
typedef enum {
    DA_SUBTYPE_DEFAULT = 0,
    DA_SUBTYPE_CONTROL,
    DA_SUBTYPE_ORIDENT,
    DA_SUBTYPE_ORCAT
} DAVarSubType;


union TerminalItemInfo
{
    QualityData q;
    //! Используется для доступа к real и float
    AnalogData f;
    CodedEnumData ce;
} ;

typedef struct {
    //! Указатель на accessInfo в бинарнике(ROM-модуле) информационной модели
    void* accessInfo;
    union TerminalItemInfo;
} TerminalItem;

bool IEDFinalDA_init(IEDEntity entity);

//! Для FinalDA, содержащего элемент терминала,
//! возвращает структуру для доступа.
//! Для других типов возвращает NULL.
void* IEDTermItemDA_getTerminalItemDescr(IEDEntity entity);

MmsDataAccessError IEDTermItemDA_write(IEDEntity entity,
                                           IsoConnection* isoConn, BufferView* value);


bool IEDTermItemDA_calcReadLen(IEDEntity entity, size_t *pLen);
bool IEDTermItemDA_encodeRead(IEDEntity entity, BufferView* outBuf);

