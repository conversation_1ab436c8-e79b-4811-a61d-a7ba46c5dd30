#pragma once
#include "mmsconnection.h"

int mms_createNameListResponse(MmsConnection* mmsConn,
        unsigned int invokeId, unsigned char* response, int rootObjPos,
                               uint8_t* continueAfter, int continueAfterLen,
                               bool recursive, int objectsTagTowrite);

int mms_handleGetNameListRequest(MmsConnection* mmsConn,
                                 unsigned char* inBuf, int bufPos, int maxBufPos,
                                  unsigned int invokeId, unsigned char* response);

//GetNameList Service Codes
#define MMS_GET_NAME_LIST_OBJECT_CLASS_CODE		0xa0
#define MMS_GET_NAME_LIST_OBJECT_SCOPE_CODE		0xa1
#define MMS_GET_NAME_LIST_OBJECT_SCOPE_VMD_SPECIFIC_CODE			0x80
#define MMS_GET_NAME_LIST_OBJECT_SCOPE_DOMAIN_SPECIFIC_CODE			0x81
#define MMS_GET_NAME_LIST_OBJECT_SCOPE_ASSOCIATION_SPECIFIC_CODE	0x82

#define MMS_GET_NAME_LIST_LIST_IDENTIFIER_CODE	0xa0
#define MMS_GET_NAME_LIST_STRING_CODE			0x1a
#define MMS_GET_NAME_LIST_MORE_FOLLOWS			0x81
