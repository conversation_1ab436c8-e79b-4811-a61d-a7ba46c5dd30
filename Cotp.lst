                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ec41.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=Cotp.c -o gh_ec41.o -list=Cotp.lst C:\Users\<USER>\AppData\Local\Temp\gh_ec41.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_ec41.s
Source File: Cotp.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile Cotp.c -o

                      10 ;		Cotp.o

                      11 ;Source File:   Cotp.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Oct 07 09:39:32 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "Cotp.h"


                      21 ;2: 


                      22 ;3: #include <debug.h>


                      23 ;4: #include "platform_socket.h"


                      24 ;5: #include <stddef.h>


                      25 ;6: #include <string.h>


                      26 ;7: 


                      27 ;8: #define TPDU_TYPE_CONNECT_REQUEST 0xe0


                      28 ;9: #define TPDU_TYPE_CONNECT_RESPONSE 0xd0


                      29 ;10: #define TPDU_TYPE_DATA 0xf0


                      30 ;11: 


                      31 ;12: #define CONNECTION_RESPONSE_HEADER_LENGTH 7


                      32 ;13: #define DATA_HEADER_LENGTH 3


                      33 ;14: 


                      34 ;15: #define OPT_TPDU_SIZE 0xC0


                      35 ;16: 


                      36 ;17: // Читает заголовок TPKT, LI, и DT в буфер заголовка


                      37 ;18: // Возвращает полный размер TPDU или -1 при ошибке


                      38 ;19: static int readHeader(COTPConnection * conn)


                      39 

                      40 ;44: }


                      41 

                      42 ;45: 


                      43 ;46: static int parseCOTPoptions(COTPConnection* cotpConn, unsigned char* options,


                      44 

                      45 ;79: }


                      46 

                      47 ;80: 


                      48 ;81: static int getCOTPoptionsLength()


                      49 

                      50 ;85: }



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ec41.s
                      51 

                      52 ;86: 


                      53 ;87: void writeTPKTHeader(int messageLen, unsigned char* buf)


                      54 ;88: {


                      55 ;89:     TPKTHeader* header = (TPKTHeader*)buf;


                      56 ;90:     header->version = 3;


                      57 ;91:     header->reserved = 0;


                      58 ;92:     header->packetLenH = messageLen >> 8;


                      59 ;93:     header->packetLenL = messageLen & 0xFF;


                      60 ;94: }


                      61 ;95: 


                      62 ;96: static void writeCOTPConnectResponseHeader(COTPConnection* cotpConn,int optionsLength,


                      63 

                      64 ;109: }


                      65 

                      66 ;110: 


                      67 ;111: static void writeCOTPoptions(unsigned char* buf)


                      68 

                      69 ;116: }


                      70 

                      71 ;117: 


                      72 ;118: static int sendCOTPConnectionResponse(COTPConnection* cotpConn, unsigned char* buf)


                      73 

                      74 ;134: }


                      75 

                      76 ;135: 


                      77 ;136: static int processCOTPconnectRequest(COTPConnection* cotpConn, int tpduSize)


                      78 

                      79 ;183: }


                      80 

                      81 ;184: 


                      82 ;185: // Получает остаток пакета DATA TPDU и складывает в текущую позицию буфера


                      83 ;186: // Возвращает


                      84 ;187: // 0 если не последний пакет,


                      85 ;188: // 1 если последний


                      86 ;189: // -1 при ошибке


                      87 ;190: static int processCOTPdataTPDU(COTPConnection* cotpConn,


                      88 

                      89 ;231:      }


                      90 ;232: }


                      91 

                      92 ;233: 


                      93 ;234: //Формирует TPKT пакет с данными и посылает.


                      94 ;235: //Возвращает, сколько фактически послано данных включая заголовок


                      95 ;236: //или -1


                      96 ;237: static int cotpSendDataTPDU(COTPConnection* cotpConn, void* sendData, int sendByteCount,


                      97 

                      98 ;257: }


                      99 

                     100 	.text

                     101 	.align	4

                     102 writeTPKTHeader::

00000000 e3a02003    103 	mov	r2,3

00000004 e5c12000    104 	strb	r2,[r1]

00000008 e3a02000    105 	mov	r2,0

0000000c e5c12001    106 	strb	r2,[r1,1]

00000010 e1a02440    107 	mov	r2,r0 asr 8

00000014 e5c12002    108 	strb	r2,[r1,2]

00000018 e5c10003    109 	strb	r0,[r1,3]

0000001c e12fff1e*   110 	ret	

                     111 	.endf	writeTPKTHeader


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ec41.s
                     112 	.align	4

                     113 

                     114 ;messageLen	r0	param

                     115 ;buf	r1	param

                     116 

                     117 	.section ".bss","awb"

                     118 .L286:

                     119 	.data

                     120 	.text

                     121 

                     122 

                     123 ;258: 


                     124 ;259: 


                     125 ;260: //Посылает данные по COTP, разбивая на фрагменты по необходимости.


                     126 ;261: //Возвращает byteCount


                     127 ;262: //или -1 при ошибке


                     128 ;263: int cotpSendData(COTPConnection* cotpConn, void* data, int byteCount)


                     129 	.align	4

                     130 	.align	4

                     131 cotpSendData::

00000020 e92d4cfc    132 	stmfd	[sp]!,{r2-r7,r10-fp,lr}

                     133 ;264: {


                     134 

                     135 ;265:     unsigned char* buf = data;


                     136 

00000024 e1a0a000    137 	mov	r10,r0

00000028 e1a0b001    138 	mov	fp,r1

                     139 ;266:     int bytesRemain = byteCount;


                     140 

0000002c e59d6000    141 	ldr	r6,[sp]

                     142 ;267:     int eot;


                     143 ;268:     do


                     144 

                     145 .L297:

                     146 ;269:     {


                     147 

00000030 e3a07000    148 	mov	r7,0

                     149 ;273:         if(bytesToSend >= bytesRemain)


                     150 

00000034 e3a04fc0    151 	mov	r4,3<<8

00000038 e28440f9    152 	add	r4,r4,249

                     153 ;270:         int bytesToSend = MAX_TPKT_TPDU_SIZE - sizeof(TPKTHeader)


                     154 

                     155 ;271:                 - DATA_HEADER_LENGTH;


                     156 ;272:         eot = 0;


                     157 

0000003c e1560004    158 	cmp	r6,r4

                     159 ;274:         {


                     160 

                     161 ;275:             bytesToSend = bytesRemain;


                     162 

00000040 d3a07001    163 	movle	r7,1

                     164 ;277:         }


                     165 ;278:         if( cotpSendDataTPDU(cotpConn, buf, bytesToSend, eot) == -1 )


                     166 

                     167 ;238:                             int eot)


                     168 ;239: {


                     169 

                     170 ;240:     //RFC905 13.7.1    


                     171 ;241:     unsigned char* buf = cotpConn->outBuf;


                     172 


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ec41.s
00000044 d1a04006    173 	movle	r4,r6

                     174 ;276:             eot = 1;


                     175 

00000048 e2840007    176 	add	r0,r4,7

0000004c e58d0004    177 	str	r0,[sp,4]

                     178 ;244: 	


                     179 ;245:     writeTPKTHeader(messageLen, buf);


                     180 

00000050 e28a5f45    181 	add	r5,r10,0x0114

                     182 ;242:     int bufPos = 0;


                     183 

                     184 ;243:     int messageLen = sizeof(TPKTHeader) + DATA_HEADER_LENGTH + sendByteCount;


                     185 

00000054 e1a01005    186 	mov	r1,r5

00000058 ebffffe8*   187 	bl	writeTPKTHeader

                     188 ;246:     bufPos += sizeof(TPKTHeader);


                     189 

                     190 ;247:     //Write COTP data header


                     191 ;248:     //li


                     192 ;249:     buf[bufPos++] = 2;// DT + EOT


                     193 

0000005c e1a02004    194 	mov	r2,r4

00000060 e3a01002    195 	mov	r1,2

00000064 e5ca1118    196 	strb	r1,[r10,280]

                     197 ;250:     //dt


                     198 ;251:     buf[bufPos++] =COTP_DATA_TRANSFER;


                     199 

00000068 e3a010f0    200 	mov	r1,240

0000006c e5ca1119    201 	strb	r1,[r10,281]

                     202 ;252:     //EOT


                     203 ;253:     buf[bufPos++] = eot ? 0x80: 0;


                     204 

00000070 e3a01000    205 	mov	r1,0

00000074 e3570000    206 	cmp	r7,0

00000078 13a01080    207 	movne	r1,128

0000007c e5ca111a    208 	strb	r1,[r10,282]

                     209 ;254:     //Write user data


                     210 ;255:     memcpy(buf + bufPos, sendData, sendByteCount);    


                     211 

00000080 e1a0100b    212 	mov	r1,fp

00000084 e2850007    213 	add	r0,r5,7

00000088 eb000000*   214 	bl	memcpy

                     215 ;256:     return writeSocket(cotpConn->socket, buf, messageLen);


                     216 

0000008c e59d2004    217 	ldr	r2,[sp,4]

00000090 e59a0000    218 	ldr	r0,[r10]

00000094 e1a01005    219 	mov	r1,r5

00000098 eb000000*   220 	bl	writeSocket

0000009c e3700001    221 	cmn	r0,1

000000a0 1a000003    222 	bne	.L300

                     223 ;279:         {


                     224 

                     225 ;280:             debugSendText("Error sending TPDU");


                     226 

000000a4 e28f0000*   227 	adr	r0,.L413

000000a8 eb000000*   228 	bl	debugSendText

                     229 ;281:             return -1;


                     230 

000000ac e3e00000    231 	mvn	r0,0

000000b0 ea000004    232 	b	.L293

                     233 .L300:


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ec41.s
                     234 ;282:         }        


                     235 ;283:         bytesRemain -= bytesToSend;


                     236 

000000b4 e0466004    237 	sub	r6,r6,r4

                     238 ;284:         buf += bytesToSend;


                     239 

000000b8 e08bb004    240 	add	fp,fp,r4

000000bc e3570000    241 	cmp	r7,0

000000c0 0affffda    242 	beq	.L297

                     243 ;285:     }


                     244 ;286:     while(!eot);


                     245 ;287:     return byteCount;


                     246 

000000c4 e59d0000    247 	ldr	r0,[sp]

                     248 .L293:

000000c8 e8bd8cfc    249 	ldmfd	[sp]!,{r2-r7,r10-fp,pc}

                     250 	.endf	cotpSendData

                     251 	.align	4

                     252 ;buf	fp	local

                     253 ;bytesRemain	r6	local

                     254 ;eot	r7	local

                     255 ;bytesToSend	r4	local

                     256 ;buf	r5	local

                     257 ;bufPos	r0	local

                     258 ;messageLen	[sp,4]	local

                     259 ;.L384	.L387	static

                     260 

                     261 ;cotpConn	r10	param

                     262 ;data	r1	param

                     263 ;byteCount	[sp]	param

                     264 

                     265 	.section ".bss","awb"

                     266 .L383:

                     267 	.data

                     268 	.text

                     269 

                     270 ;288: }


                     271 

                     272 ;289: 


                     273 ;290: int cotpReceiveData(COTPConnection* cotpConn, void* recvBuf, int maxByteCount )


                     274 	.align	4

                     275 	.align	4

                     276 	.align	4

                     277 cotpReceiveData::

000000cc e92d4cf2    278 	stmfd	[sp]!,{r1,r4-r7,r10-fp,lr}

                     279 ;291: {


                     280 

                     281 ;292:     int lastDataPacket;


                     282 ;293:     cotpConn->inBufPos = 0;


                     283 

000000d0 e24dd004    284 	sub	sp,sp,4

000000d4 e58d1004    285 	str	r1,[sp,4]

000000d8 e1a0b002    286 	mov	fp,r2

000000dc e1a05000    287 	mov	r5,r0

000000e0 e3a00000    288 	mov	r0,0

000000e4 e585000c    289 	str	r0,[r5,12]

                     290 ;294:     while(1)


                     291 

                     292 .L421:

                     293 ;295:     {


                     294 


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ec41.s
                     295 ;296:         //Читаем заголовок пакета (TPKT + кусочек COTP)


                     296 ;297:         int tpduSize = readHeader(cotpConn);


                     297 

                     298 ;20: {


                     299 

                     300 ;21:     TPKTHeader* pTPKTHeader = &conn->resvTPKTHeader.tpktHeader;


                     301 

000000e8 e2854010    302 	add	r4,r5,16

                     303 ;22:     int recvResult;


                     304 ;23: 	int cotpLength;


                     305 ;24: 


                     306 ;25:     //Получаем заголовок TPKT, LI, DT


                     307 ;26:     recvResult = readSocket(conn->socket, &conn->resvTPKTHeader, sizeof(PacketHeader));


                     308 

000000ec e1a01004    309 	mov	r1,r4

000000f0 e5950000    310 	ldr	r0,[r5]

000000f4 e3a02006    311 	mov	r2,6

000000f8 eb000000*   312 	bl	readSocket

                     313 ;27:     if(!recvResult)


                     314 

000000fc e3500000    315 	cmp	r0,0

00000100 0a000004    316 	beq	.L425

                     317 ;28:     {


                     318 

                     319 ;29:         //Ошибка чтения


                     320 ;30:         ERROR_REPORT("Read TPKT header Error");


                     321 ;31:         return -1;


                     322 

                     323 ;32:     }


                     324 ;33: 


                     325 ;34:     //проверяем заголовок TPKT


                     326 ;35:     if(pTPKTHeader->version != 3 || pTPKTHeader->reserved != 0 )


                     327 

00000104 e5d50010    328 	ldrb	r0,[r5,16]

00000108 e3500003    329 	cmp	r0,3

0000010c 05d40001    330 	ldreqb	r0,[r4,1]

00000110 03500000    331 	cmpeq	r0,0

00000114 0a000002    332 	beq	.L426

                     333 .L425:

                     334 ;36:     {


                     335 

                     336 ;37:         //Неверный заголовок TPKT


                     337 ;38: 		ERROR_REPORT("Invalid TPKT header");


                     338 ;39:         return -1;


                     339 

00000118 e3e04000    340 	mvn	r4,0

                     341 ;298:         if(tpduSize == -1)


                     342 

0000011c e3740001    343 	cmn	r4,1

00000120 0a000080    344 	beq	.L478

                     345 .L426:

                     346 ;40:     }


                     347 ;41: 


                     348 ;42: 	cotpLength = pTPKTHeader->packetLenH * 256 + pTPKTHeader->packetLenL;		


                     349 

00000124 05d42003    350 	ldreqb	r2,[r4,3]

00000128 05d40002    351 	ldreqb	r0,[r4,2]

0000012c 00824400    352 	addeq	r4,r2,r0 lsl 8

                     353 ;43:     return cotpLength;


                     354 

                     355 ;298:         if(tpduSize == -1)



                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ec41.s
                     356 

00000130 03740001    357 	cmneq	r4,1

00000134 0a00007b    358 	beq	.L478

                     359 .L428:

                     360 ;299:         {


                     361 

                     362 ;300:             return -1;


                     363 

                     364 ;301:         }


                     365 ;302: 


                     366 ;303:         switch (cotpConn->resvTPKTHeader.dt) {


                     367 

00000138 e5d51015    368 	ldrb	r1,[r5,21]

0000013c e25100e0    369 	subs	r0,r1,224

00000140 0a000002    370 	beq	.L433

00000144 e3500010    371 	cmp	r0,16

00000148 0a000057    372 	beq	.L462

0000014c ea00007b    373 	b	.L484

                     374 .L433:

                     375 ;304:         case TPDU_TYPE_CONNECT_REQUEST:


                     376 ;305:             debugSendText("Received COTP connect request");


                     377 

00000150 e28f0000*   378 	adr	r0,.L996

00000154 eb000000*   379 	bl	debugSendText

                     380 ;306:             if(processCOTPconnectRequest(cotpConn, tpduSize) < 0)


                     381 

                     382 ;137: {


                     383 

                     384 ;138:     //RFC905 13.3.1


                     385 ;139:    ConnectRequest* pConnectRequest = &cotpConn->recvConnectRequest;


                     386 

                     387 ;140: 


                     388 ;141:     //Размер пакета без заголовка TPKT, LI и DT


                     389 ;142:     int remainPacketSize = tpduSize - sizeof (PacketHeader);


                     390 

00000158 e2442006    391 	sub	r2,r4,6

                     392 ;143: 


                     393 ;144:     unsigned char li = cotpConn->resvTPKTHeader.li;


                     394 

                     395 ;145: 


                     396 ;146:     if(remainPacketSize > sizeof(ConnectRequest))


                     397 

0000015c e35200fe    398 	cmp	r2,254

00000160 9a000003    399 	bls	.L440

                     400 ;147:     {


                     401 

                     402 ;148:         //Пакет не лезет в буфер


                     403 ;149:         debugSendText("TPDU does not fit in the buffer");


                     404 

00000164 e59f021c*   405 	ldr	r0,.L997

                     406 ;150:         return -1;


                     407 

00000168 eb000000*   408 	bl	debugSendText

                     409 ;315:             {


                     410 

                     411 ;316:                 return -1;


                     412 

0000016c e3e00000    413 	mvn	r0,0

00000170 ea000075    414 	b	.L414

                     415 .L440:

00000174 e5d56014    416 	ldrb	r6,[r5,20]


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ec41.s
                     417 ;151:     }


                     418 ;152: 


                     419 ;153:     if(li < 6)


                     420 

00000178 e2857016    421 	add	r7,r5,22

0000017c e3560006    422 	cmp	r6,6

00000180 3a000068    423 	blo	.L478

                     424 ;154:     {


                     425 

                     426 ;155:         return -1;


                     427 

                     428 ;156:     }


                     429 ;157: 


                     430 ;158:     //Читаем оставшуюся часть пакета


                     431 ;159:     if(readSocket(cotpConn->socket, pConnectRequest, remainPacketSize ) == -1)


                     432 

00000184 e5950000    433 	ldr	r0,[r5]

00000188 e1a01007    434 	mov	r1,r7

0000018c eb000000*   435 	bl	readSocket

00000190 e3700001    436 	cmn	r0,1

00000194 0a000063    437 	beq	.L478

                     438 ;160:     {


                     439 

                     440 ;161:         //Ошибка чтения


                     441 ;162:         return -1;


                     442 

                     443 ;163:     }


                     444 ;164:     cotpConn->remoteRef = (pConnectRequest->srcRefH << 8) + pConnectRequest->srcRefL;


                     445 

00000198 e5d72002    446 	ldrb	r2,[r7,2]

0000019c e5d70003    447 	ldrb	r0,[r7,3]

000001a0 e3a04000    448 	mov	r4,0

                     449 ;49:     int optionPos = 0;    


                     450 

                     451 ;50:     while(optionPos < allOptionsLen)


                     452 

000001a4 e0820400    453 	add	r0,r2,r0 lsl 8

000001a8 e1c500b4    454 	strh	r0,[r5,4]

                     455 ;165:     //debugSendUshort("\tremoteRef:", cotpConn->remoteRef);


                     456 ;166:     cotpConn->protocolClass = pConnectRequest->protocolClass;


                     457 

000001ac e5d70004    458 	ldrb	r0,[r7,4]

000001b0 e246a006    459 	sub	r10,r6,6

000001b4 e5c50008    460 	strb	r0,[r5,8]

                     461 ;167:     //debugSendUshort("\tprotocolClass:", cotpConn->protocolClass);


                     462 ;168: 


                     463 ;169:     //  Внимание! LI не входит в длину


                     464 ;170:     if(parseCOTPoptions(cotpConn, pConnectRequest->variablePart, li - 6) < 0)


                     465 

                     466 ;47:                             int allOptionsLen)


                     467 ;48: {


                     468 

000001b8 e154000a    469 	cmp	r4,r10

000001bc aa000016    470 	bge	.L455

                     471 .L445:

                     472 ;51:     {                


                     473 

                     474 ;52:         int optionType = options[optionPos++];


                     475 

000001c0 e0840007    476 	add	r0,r4,r7

000001c4 e2844001    477 	add	r4,r4,1


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ec41.s
000001c8 e5d01005    478 	ldrb	r1,[r0,5]

                     479 ;53:         int optionLen = options[optionPos++];        


                     480 

000001cc e0840007    481 	add	r0,r4,r7

000001d0 e5d06005    482 	ldrb	r6,[r0,5]

                     483 ;54:         if(optionPos + optionLen > allOptionsLen)


                     484 

000001d4 e2844001    485 	add	r4,r4,1

000001d8 e0860004    486 	add	r0,r6,r4

000001dc e150000a    487 	cmp	r0,r10

000001e0 da000003    488 	ble	.L447

                     489 ;55:         {


                     490 

                     491 ;56:             //option length error


                     492 ;57:             debugSendText("option length error");


                     493 

000001e4 e59f01a0*   494 	ldr	r0,.L998

                     495 ;58:             return -1;


                     496 

000001e8 eb000000*   497 	bl	debugSendText

                     498 ;315:             {


                     499 

                     500 ;316:                 return -1;


                     501 

000001ec e3e00000    502 	mvn	r0,0

000001f0 ea000055    503 	b	.L414

                     504 .L447:

                     505 ;59:         }


                     506 ;60:         switch(optionType)


                     507 

000001f4 e35100c0    508 	cmp	r1,192

000001f8 1a000004    509 	bne	.L450

                     510 ;61:         {


                     511 ;62:         case OPT_TPDU_SIZE:


                     512 ;63:             if (optionLen == 1) {


                     513 

000001fc e3560001    514 	cmp	r6,1

                     515 ;64:                 //TODO Use requestedTpduSize


                     516 ;65:                 //int requestedTpduSize = (1 << options[optionPos]);


                     517 ;66:                 //debugSendUshort("\tmax TPDU size:", requestedTpduSize);


                     518 ;67:             }


                     519 ;68:             else


                     520 ;69:             {


                     521 

                     522 ;70:                debugSendUshort("\tInvalid option size", optionType);


                     523 

00000200 11a01801    524 	movne	r1,r1 lsl 16

00000204 159f0184*   525 	ldrne	r0,.L999

00000208 11a01821    526 	movne	r1,r1 lsr 16

0000020c 1b000000*   527 	blne	debugSendUshort

                     528 .L450:

                     529 ;71:             }


                     530 ;72:             break;        


                     531 ;73:         //default:


                     532 ;74:             //debugSendUshort("\tUnknown option:", optionType);


                     533 ;75:         }


                     534 ;76:         optionPos += optionLen;


                     535 

00000210 e0844006    536 	add	r4,r4,r6

00000214 e154000a    537 	cmp	r4,r10

00000218 baffffe8    538 	blt	.L445


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ec41.s
                     539 .L455:

                     540 ;77:     }


                     541 ;78:     return 1;


                     542 

                     543 ;171:     {


                     544 

                     545 ;172:         return -1;


                     546 

                     547 ;173:     }


                     548 ;174: 


                     549 ;175:     if(sendCOTPConnectionResponse(cotpConn, cotpConn->outBuf) == -1)


                     550 

                     551 ;119: {


                     552 

                     553 ;120:     int bufPos = 0;


                     554 

                     555 ;121:     int optionsLength = getCOTPoptionsLength();    


                     556 

                     557 ;82: {


                     558 

                     559 ;83:     //TPDU size option;


                     560 ;84:     return 3;


                     561 

                     562 ;122:     //4 + 7 + 3


                     563 ;123:     int messageLen = sizeof(TPKTHeader) + CONNECTION_RESPONSE_HEADER_LENGTH


                     564 

                     565 ;124:             + optionsLength;


                     566 ;125: 


                     567 ;126:     writeTPKTHeader(messageLen, buf);


                     568 

0000021c e2851f45    569 	add	r1,r5,0x0114

00000220 e3a0000e    570 	mov	r0,14

00000224 ebffff75*   571 	bl	writeTPKTHeader

                     572 ;127:     bufPos += sizeof( TPKTHeader);


                     573 

                     574 ;128:     writeCOTPConnectResponseHeader(cotpConn, optionsLength, buf + bufPos );


                     575 

                     576 ;97:                                            unsigned char* buf)


                     577 ;98: {


                     578 

                     579 ;99: 


                     580 ;100:     int headerLengthValue =


                     581 

                     582 ;101:             CONNECTION_RESPONSE_HEADER_LENGTH -1;//Длина не входит


                     583 ;102:     buf[0] =  headerLengthValue  + optionsLength;


                     584 

00000228 e3a00009    585 	mov	r0,9

0000022c e5c50118    586 	strb	r0,[r5,280]

                     587 ;103:     buf[1] = TPDU_TYPE_CONNECT_RESPONSE;


                     588 

00000230 e3a000d0    589 	mov	r0,208

00000234 e5c50119    590 	strb	r0,[r5,281]

                     591 ;104:     buf[2] = cotpConn->remoteRef >> 8;


                     592 

00000238 e1d500b4    593 	ldrh	r0,[r5,4]

0000023c e2851f45    594 	add	r1,r5,0x0114

                     595 ;112: {


                     596 

                     597 ;113:         buf[0] = OPT_TPDU_SIZE;


                     598 

00000240 e1a02440    599 	mov	r2,r0 asr 8


                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ec41.s
00000244 e5c5011b    600 	strb	r0,[r5,283]

                     601 ;106:     buf[4] = cotpConn->localRef >> 8;


                     602 

00000248 e1d500b6    603 	ldrh	r0,[r5,6]

0000024c e5c5211a    604 	strb	r2,[r5,282]

                     605 ;105:     buf[3] = cotpConn->remoteRef & 0xff;


                     606 

00000250 e1a02440    607 	mov	r2,r0 asr 8

00000254 e5c5011d    608 	strb	r0,[r5,285]

                     609 ;108:     buf[6] = cotpConn->protocolClass;


                     610 

00000258 e5d50008    611 	ldrb	r0,[r5,8]

0000025c e5c5211c    612 	strb	r2,[r5,284]

                     613 ;107:     buf[5] = cotpConn->localRef & 0xff;


                     614 

00000260 e5c5011e    615 	strb	r0,[r5,286]

                     616 ;129:     bufPos += CONNECTION_RESPONSE_HEADER_LENGTH;


                     617 

                     618 ;130: 


                     619 ;131:     writeCOTPoptions(buf + bufPos );


                     620 

00000264 e3a000c0    621 	mov	r0,192

00000268 e5c5011f    622 	strb	r0,[r5,287]

                     623 ;114:         buf[1] = 1;// Размер опции


                     624 

0000026c e3a00001    625 	mov	r0,1

00000270 e5c50120    626 	strb	r0,[r5,288]

                     627 ;115:         buf[2] = 10;//Максимальный размер TPDU: степень 2


                     628 

00000274 e3a0000a    629 	mov	r0,10

00000278 e5c50121    630 	strb	r0,[r5,289]

                     631 ;132: 


                     632 ;133:     return writeSocket(cotpConn->socket, buf, messageLen);


                     633 

0000027c e5950000    634 	ldr	r0,[r5]

00000280 e3a0200e    635 	mov	r2,14

00000284 eb000000*   636 	bl	writeSocket

00000288 e3700001    637 	cmn	r0,1

0000028c 1a000003    638 	bne	.L457

                     639 ;176:     {


                     640 

                     641 ;177:         debugSendText("sendCOTPConnectionResponse error");


                     642 

00000290 e59f00fc*   643 	ldr	r0,.L1000

                     644 ;178:         return -1;


                     645 

00000294 eb000000*   646 	bl	debugSendText

                     647 ;315:             {


                     648 

                     649 ;316:                 return -1;


                     650 

00000298 e3e00000    651 	mvn	r0,0

0000029c ea00002a    652 	b	.L414

                     653 .L457:

                     654 ;179:     }


                     655 ;180: 


                     656 ;181:     debugSendText("Connection response is sent");


                     657 

000002a0 e59f00f0*   658 	ldr	r0,.L1001

000002a4 eb000000*   659 	bl	debugSendText

                     660 ;182:     return 1;



                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ec41.s
                     661 

000002a8 eaffff8e    662 	b	.L421

                     663 .L462:

                     664 ;307:             {


                     665 

                     666 ;308:                 return -1;


                     667 

                     668 ;309:             }


                     669 ;310:             continue;


                     670 

                     671 ;311:         case TPDU_TYPE_DATA:			            


                     672 ;312:             lastDataPacket = processCOTPdataTPDU(cotpConn, recvBuf, maxByteCount,


                     673 

000002ac e1a06005    674 	mov	r6,r5

000002b0 e596000c    675 	ldr	r0,[r6,12]

000002b4 e2444007    676 	sub	r4,r4,7

                     677 ;196: 


                     678 ;197:     if(cotpConn->inBufPos +  dataSize > inBufferSize)


                     679 

000002b8 e0840000    680 	add	r0,r4,r0

000002bc e150000b    681 	cmp	r0,fp

                     682 ;198:     {


                     683 

                     684 ;199:         //Не лезет в буфер


                     685 ;200:         return -1;


                     686 

                     687 ;201:     }


                     688 ;202: 


                     689 ;203:     //RFC905 13.7.1


                     690 ;204:     if (cotpConn->resvTPKTHeader.li != 2)// DT + TPDU-NR


                     691 

000002c0 d5d60014    692 	ldrleb	r0,[r6,20]

000002c4 e59d7004    693 	ldr	r7,[sp,4]

                     694 ;191:                                unsigned char* inBuffer, int inBufferSize, int tpduSize)


                     695 ;192: {


                     696 

                     697 ;193:     unsigned char flowControl;


                     698 ;194: 


                     699 ;195:     int dataSize = tpduSize - sizeof(PacketHeader) - 1/*flow control*/;


                     700 

000002c8 d3500002    701 	cmple	r0,2

000002cc 1a00000c    702 	bne	.L472

                     703 ;205:     {


                     704 

                     705 ;206:          return -1;


                     706 

                     707 ;207:     }


                     708 ;208: 


                     709 ;209:     //Читаем оставшуюся часть заголовка


                     710 ;210:     if(readSocket(cotpConn->socket, &flowControl, 1) == -1)


                     711 

000002d0 e28d1003    712 	add	r1,sp,3

000002d4 e5960000    713 	ldr	r0,[r6]

000002d8 e3a02001    714 	mov	r2,1

000002dc eb000000*   715 	bl	readSocket

000002e0 e3700001    716 	cmn	r0,1

000002e4 0a000006    717 	beq	.L472

                     718 ;211:     {


                     719 

                     720 ;212:         return -1;


                     721 


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ec41.s
                     722 ;213:     }


                     723 ;214: 


                     724 ;215:     //Читаем сами данные


                     725 ;216:     if(readSocket(cotpConn->socket, inBuffer + cotpConn->inBufPos, dataSize) == -1)


                     726 

000002e8 e596000c    727 	ldr	r0,[r6,12]

000002ec e0801007    728 	add	r1,r0,r7

000002f0 e5960000    729 	ldr	r0,[r6]

000002f4 e1a02004    730 	mov	r2,r4

000002f8 eb000000*   731 	bl	readSocket

000002fc e3700001    732 	cmn	r0,1

00000300 1a000001    733 	bne	.L473

                     734 .L472:

                     735 ;217:     {


                     736 

                     737 ;218:             return -1;


                     738 

00000304 e3e00000    739 	mvn	r0,0

                     740 ;223:      {


                     741 

                     742 ;224:          //Последний пакет


                     743 ;225:          return 1;


                     744 

                     745 ;226:      }


                     746 ;227:      else


                     747 ;228:      {


                     748 

                     749 ;229:          //Не последний пакет


                     750 ;230:          return 0;


                     751 

                     752 ;313:                                                  tpduSize);


                     753 ;314:             if(lastDataPacket < 0)


                     754 

00000308 ea000006    755 	b	.L478

                     756 .L473:

                     757 ;219:     }


                     758 ;220:     cotpConn->inBufPos += dataSize;


                     759 

0000030c e596000c    760 	ldr	r0,[r6,12]

00000310 e0800004    761 	add	r0,r0,r4

00000314 e586000c    762 	str	r0,[r6,12]

00000318 e5dd0003    763 	ldrb	r0,[sp,3]

0000031c e1a00c00    764 	mov	r0,r0 lsl 24

00000320 e1b00fa0    765 	movs	r0,r0 lsr 31

                     766 ;221: 


                     767 ;222:      if (flowControl & 0x80)


                     768 

                     769 ;223:      {


                     770 

                     771 ;224:          //Последний пакет


                     772 ;225:          return 1;


                     773 

                     774 ;226:      }


                     775 ;227:      else


                     776 ;228:      {


                     777 

                     778 ;229:          //Не последний пакет


                     779 ;230:          return 0;


                     780 

                     781 ;313:                                                  tpduSize);


                     782 ;314:             if(lastDataPacket < 0)



                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ec41.s
                     783 

00000324 5a000001    784 	bpl	.L477

                     785 .L478:

                     786 ;315:             {


                     787 

                     788 ;316:                 return -1;


                     789 

00000328 e3e00000    790 	mvn	r0,0

0000032c ea000006    791 	b	.L414

                     792 .L477:

                     793 ;317:             }


                     794 ;318:             if(lastDataPacket)


                     795 

00000330 e3500000    796 	cmp	r0,0

00000334 0affff6b    797 	beq	.L421

                     798 ;319:             {


                     799 

                     800 ;320:                 return cotpConn->inBufPos;


                     801 

00000338 e595000c    802 	ldr	r0,[r5,12]

0000033c ea000002    803 	b	.L414

                     804 .L484:

                     805 ;321:             }


                     806 ;322:             continue;


                     807 

                     808 ;323:         default:


                     809 ;324:             debugSendUshort("Unknown TPDU type", cotpConn->resvTPKTHeader.dt);


                     810 

00000340 e28f0000*   811 	adr	r0,.L1002

00000344 eb000000*   812 	bl	debugSendUshort

                     813 ;325:             continue;


                     814 

00000348 eaffff66    815 	b	.L421

                     816 .L414:

0000034c e28dd004    817 	add	sp,sp,4

00000350 e8bd8cf2    818 	ldmfd	[sp]!,{r1,r4-r7,r10-fp,pc}

                     819 	.endf	cotpReceiveData

                     820 	.align	4

                     821 .L413:

                     822 ;	"Error sending TPDU\000"

00000354 6f727245    823 	.data.b	69,114,114,111

00000358 65732072    824 	.data.b	114,32,115,101

0000035c 6e69646e    825 	.data.b	110,100,105,110

00000360 50542067    826 	.data.b	103,32,84,80

00000364 5544       827 	.data.b	68,85

00000366 00         828 	.data.b	0

00000367 00         829 	.align 4

                     830 

                     831 	.type	.L413,$object

                     832 	.size	.L413,4

                     833 

                     834 .L996:

                     835 ;	"Received COTP connect request\000"

00000368 65636552    836 	.data.b	82,101,99,101

0000036c 64657669    837 	.data.b	105,118,101,100

00000370 544f4320    838 	.data.b	32,67,79,84

00000374 6f632050    839 	.data.b	80,32,99,111

00000378 63656e6e    840 	.data.b	110,110,101,99

0000037c 65722074    841 	.data.b	116,32,114,101

00000380 73657571    842 	.data.b	113,117,101,115

00000384 0074       843 	.data.b	116,0


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ec41.s
00000386 0000       844 	.align 4

                     845 

                     846 	.type	.L996,$object

                     847 	.size	.L996,4

                     848 

                     849 .L997:

00000388 00000000*   850 	.data.w	.L895

                     851 	.type	.L997,$object

                     852 	.size	.L997,4

                     853 

                     854 .L998:

0000038c 00000000*   855 	.data.w	.L896

                     856 	.type	.L998,$object

                     857 	.size	.L998,4

                     858 

                     859 .L999:

00000390 00000000*   860 	.data.w	.L898

                     861 	.type	.L999,$object

                     862 	.size	.L999,4

                     863 

                     864 .L1000:

00000394 00000000*   865 	.data.w	.L899

                     866 	.type	.L1000,$object

                     867 	.size	.L1000,4

                     868 

                     869 .L1001:

00000398 00000000*   870 	.data.w	.L897

                     871 	.type	.L1001,$object

                     872 	.size	.L1001,4

                     873 

                     874 .L1002:

                     875 ;	"Unknown TPDU type\000"

0000039c 6e6b6e55    876 	.data.b	85,110,107,110

000003a0 206e776f    877 	.data.b	111,119,110,32

000003a4 55445054    878 	.data.b	84,80,68,85

000003a8 70797420    879 	.data.b	32,116,121,112

000003ac 0065       880 	.data.b	101,0

000003ae 0000       881 	.align 4

                     882 

                     883 	.type	.L1002,$object

                     884 	.size	.L1002,4

                     885 

                     886 	.align	4

                     887 ;lastDataPacket	r0	local

                     888 ;tpduSize	r4	local

                     889 ;pTPKTHeader	r4	local

                     890 ;.L893	.L902	static

                     891 ;pConnectRequest	r7	local

                     892 ;remainPacketSize	r2	local

                     893 ;li	r6	local

                     894 ;optionPos	r4	local

                     895 ;optionType	r1	local

                     896 ;optionLen	r6	local

                     897 ;cotpConn	r6	local

                     898 ;inBuffer	r7	local

                     899 ;flowControl	[sp,3]	local

                     900 ;dataSize	r4	local

                     901 ;.L894	.L903	static

                     902 

                     903 ;cotpConn	r5	param

                     904 ;recvBuf	[sp,4]	param


                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ec41.s
                     905 ;maxByteCount	fp	param

                     906 

                     907 	.section ".bss","awb"

                     908 .L892:

                     909 	.section ".rodata","a"

                     910 .L895:

                     911 __UNNAMED_1_static_in_processCOTPconnectRequest:;	"TPDU does not fit in the buffer\000"

00000000 55445054    912 	.data.b	84,80,68,85

00000004 656f6420    913 	.data.b	32,100,111,101

00000008 6f6e2073    914 	.data.b	115,32,110,111

0000000c 69662074    915 	.data.b	116,32,102,105

00000010 6e692074    916 	.data.b	116,32,105,110

00000014 65687420    917 	.data.b	32,116,104,101

00000018 66756220    918 	.data.b	32,98,117,102

0000001c 00726566    919 	.data.b	102,101,114,0

                     920 	.type	__UNNAMED_1_static_in_processCOTPconnectRequest,$object

                     921 	.size	__UNNAMED_1_static_in_processCOTPconnectRequest,32

                     922 .L896:

                     923 __UNNAMED_1_static_in_parseCOTPoptions:;	"option length error\000"

00000020 6974706f    924 	.data.b	111,112,116,105

00000024 6c206e6f    925 	.data.b	111,110,32,108

00000028 74676e65    926 	.data.b	101,110,103,116

0000002c 72652068    927 	.data.b	104,32,101,114

00000030 00726f72    928 	.data.b	114,111,114,0

                     929 	.type	__UNNAMED_1_static_in_parseCOTPoptions,$object

                     930 	.size	__UNNAMED_1_static_in_parseCOTPoptions,20

                     931 .L897:

                     932 __UNNAMED_3_static_in_processCOTPconnectRequest:;	"Connection response is sent\000"

00000034 6e6e6f43    933 	.data.b	67,111,110,110

00000038 69746365    934 	.data.b	101,99,116,105

0000003c 72206e6f    935 	.data.b	111,110,32,114

00000040 6f707365    936 	.data.b	101,115,112,111

00000044 2065736e    937 	.data.b	110,115,101,32

00000048 73207369    938 	.data.b	105,115,32,115

0000004c 00746e65    939 	.data.b	101,110,116,0

                     940 	.type	__UNNAMED_3_static_in_processCOTPconnectRequest,$object

                     941 	.size	__UNNAMED_3_static_in_processCOTPconnectRequest,28

                     942 .L898:

                     943 __UNNAMED_2_static_in_parseCOTPoptions:;	"\tInvalid option size\000"

00000050 766e4909    944 	.data.b	9,73,110,118

00000054 64696c61    945 	.data.b	97,108,105,100

00000058 74706f20    946 	.data.b	32,111,112,116

0000005c 206e6f69    947 	.data.b	105,111,110,32

00000060 657a6973    948 	.data.b	115,105,122,101

00000064 00         949 	.data.b	0

00000065 000000     950 	.space	3

                     951 	.type	__UNNAMED_2_static_in_parseCOTPoptions,$object

                     952 	.size	__UNNAMED_2_static_in_parseCOTPoptions,24

                     953 .L899:

                     954 __UNNAMED_2_static_in_processCOTPconnectRequest:;	"sendCOTPConnectionResponse error\000"

00000068 646e6573    955 	.data.b	115,101,110,100

0000006c 50544f43    956 	.data.b	67,79,84,80

00000070 6e6e6f43    957 	.data.b	67,111,110,110

00000074 69746365    958 	.data.b	101,99,116,105

00000078 65526e6f    959 	.data.b	111,110,82,101

0000007c 6e6f7073    960 	.data.b	115,112,111,110

00000080 65206573    961 	.data.b	115,101,32,101

00000084 726f7272    962 	.data.b	114,114,111,114

00000088 00         963 	.data.b	0

00000089 000000     964 	.space	3

                     965 	.type	__UNNAMED_2_static_in_processCOTPconnectRequest,$object


                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ec41.s
                     966 	.size	__UNNAMED_2_static_in_processCOTPconnectRequest,36

                     967 	.data

                     968 	.text

                     969 

                     970 ;326:         }


                     971 ;327:     }


                     972 ;328: 


                     973 ;329: }


                     974 

                     975 ;330: 


                     976 ;331: /*


                     977 ;332: //Возвращает количество полученных данных


                     978 ;333: int cotp(COTPConnection* cotpConn, void* sendData, int sendByteCount, void* recvBuf,


                     979 ;334:          int maxByteCount)


                     980 ;335: {    


                     981 ;336:     int lastDataPacket;


                     982 ;337:     cotpConn->inBufPos = 0;


                     983 ;338:     if(sendData != NULL && sendByteCount)


                     984 ;339:     {


                     985 ;340:         if(cotpSendData(cotpConn, sendData, sendByteCount) == -1)


                     986 ;341:         {


                     987 ;342:             return -1;


                     988 ;343:         }


                     989 ;344:     }


                     990 ;345:     while(1)


                     991 ;346:     {


                     992 ;347:         //Читаем заголовок пакета (TPKT + кусочек COTP)


                     993 ;348:         int tpduSize = readHeader(cotpConn);


                     994 ;349:         if(tpduSize == -1)


                     995 ;350:         {


                     996 ;351:             return -1;


                     997 ;352:         }


                     998 ;353: 


                     999 ;354:         switch (cotpConn->resvTPKTHeader.dt) {


                    1000 ;355:         case TPDU_TYPE_CONNECT_REQUEST:            


                    1001 ;356:             debugSendText("Received COTP connect request");


                    1002 ;357:             if(processCOTPconnectRequest(cotpConn, tpduSize) < 0)


                    1003 ;358:             {


                    1004 ;359:                 return -1;


                    1005 ;360:             }


                    1006 ;361:             continue;


                    1007 ;362:         case TPDU_TYPE_DATA:


                    1008 ;363:             //debugSendText("Received COTP data");


                    1009 ;364:             lastDataPacket = processCOTPdataTPDU(cotpConn, recvBuf, maxByteCount,


                    1010 ;365:                                                  tpduSize);


                    1011 ;366:             if(lastDataPacket < 0)


                    1012 ;367:             {


                    1013 ;368:                 return -1;


                    1014 ;369:             }


                    1015 ;370:             if(lastDataPacket)


                    1016 ;371:             {


                    1017 ;372:                 return cotpConn->inBufPos;


                    1018 ;373:             }


                    1019 ;374:             continue;


                    1020 ;375:         default:


                    1021 ;376:             debugSendUshort("Unknown TPDU type", cotpConn->resvTPKTHeader.dt);


                    1022 ;377:             continue;


                    1023 ;378:         }


                    1024 ;379:     }    


                    1025 ;380: }


                    1026 ;381: */



                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ec41.s
                    1027 ;382: 


                    1028 ;383: void initCOTPConnection(COTPConnection* cotpConn, SERVER_SOCKET socket)


                    1029 	.align	4

                    1030 	.align	4

                    1031 initCOTPConnection::

                    1032 ;384: {


                    1033 

                    1034 ;385:     cotpConn->socket = socket;


                    1035 

000003b0 e5801000   1036 	str	r1,[r0]

                    1037 ;386:     cotpConn->protocolClass = 0xFF;


                    1038 

000003b4 e3a010ff   1039 	mov	r1,255

000003b8 e5c01008   1040 	strb	r1,[r0,8]

                    1041 ;387:     cotpConn->remoteRef = 0xFFFF;


                    1042 

000003bc e3a01cff   1043 	mov	r1,255<<8

000003c0 e28110ff   1044 	add	r1,r1,255

000003c4 e1c010b4   1045 	strh	r1,[r0,4]

                    1046 ;388:     cotpConn->localRef = 1;


                    1047 

000003c8 e3a01001   1048 	mov	r1,1

000003cc e1c010b6   1049 	strh	r1,[r0,6]

000003d0 e12fff1e*  1050 	ret	

                    1051 	.endf	initCOTPConnection

                    1052 	.align	4

                    1053 

                    1054 ;cotpConn	r0	param

                    1055 ;socket	r1	param

                    1056 

                    1057 	.section ".bss","awb"

                    1058 .L1022:

                    1059 	.data

                    1060 	.text

                    1061 

                    1062 ;389: }


                    1063 	.align	4

                    1064 ;__UNNAMED_1_static_in_parseCOTPoptions	.L896	static

                    1065 ;__UNNAMED_2_static_in_parseCOTPoptions	.L898	static

                    1066 ;__UNNAMED_1_static_in_processCOTPconnectRequest	.L895	static

                    1067 ;__UNNAMED_2_static_in_processCOTPconnectRequest	.L899	static

                    1068 ;__UNNAMED_3_static_in_processCOTPconnectRequest	.L897	static

                    1069 

                    1070 	.data

                    1071 	.ghsnote version,6

                    1072 	.ghsnote tools,3

                    1073 	.ghsnote options,0

                    1074 	.text

                    1075 	.align	4

                    1076 	.section ".rodata","a"

                    1077 	.align	4

                    1078 	.text

