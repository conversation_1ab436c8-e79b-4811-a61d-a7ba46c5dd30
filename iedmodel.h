#pragma once

#include "stringView.h"
#include "bufViewBER.h"
#include "DomainNameWriter.h"
#include "IEDCompile/InnerAttributeTypes.h"
#include "tools.h"
#include <types.h>
#include <stdbool.h>

typedef enum {
	IED_ANY_TAG = -1,
    IED_LD = 0xE2,
    IED_LN = 0xE4,
    IED_FC = 0xE0,
    IED_DO = 0xE6,
	IED_DATA_SET = 0xE7,
	IED_DA = 0xE8,
	IED_DA_FINAL = 0xE9,
	IED_VMD_DATA_SECTION = 0xEC,
	IED_VMD_DATA_SET_SECTION = 0xEE,
	IED_DATASET_DESCRIPTION = 0x04,
	IED_GSE_LIST = 0xF0,
	IED_CONTROL_INFO = 0xF1,
	IED_OBJ_FLAGS = 0xC0
} IEDTags;


#define IED_OBJ_FLAGS_DCHG 1
#define IED_OBJ_FLAGS_QCHG 2


extern uint8_t* iedModel;
extern int iedModelSize;

//! Устанавливает глобальный указатель на описание информационной модели
void setIedModel(unsigned char* pModel, int modelSize);

//! Читает заголовок объекта и заполняет переменные по указателям
//! Любой указатель может быть NULL если значение не нужно
//! Возвращает позицию сразу за заголовком или 0 при ошибке
int readTL(int pos, uint8_t* pTag, int* pLen, int* pFullLen);


//! Пропускет объект в указанной позиции.
//!Возвращает новую позицию.
//! При ошибке возвращает 0
int skipObject(int pos);

bool getObjectName(int objPos, StringView* result);

//! Возвращает позицию начала и конца IEC61850-подобъектов (т.е. DO, DA, и т.п.) указанного объекта,
//! пропуская служебную информацию (имя VisibleString, IED_GSE_LIST, и т.п.). 
//! Если подобъектов нет, возвращаемое значение равно *pEndPos.
//! Это можно использовать в цикле while по подобъектам.
//! При ошибке возвращает 0
int getSubObjectsPos(int rootObjPos, int* pEndPos);

//! Находит объект по имени среди непосредственных подобъектов
//! указанного корневого объекта.
//! Возвращает позицию в или 0 если ошибка
int findObjectBySimpleName(int rootObjPos,  unsigned char* name, int nameLen);

int findObjectByTag(int rootObjPos, uint8_t tagToFind);

int findDomainSection(int section, uint8_t* domainId, int domainIdLen);

int getSimpleNameLen(uint8_t* name, int fullNameLen, bool* delimiterFound);

int findObjectByPath(int rootObjPos, uint8_t* name, int fullNameLen);

int findObjectByFullName(int section, StringView* domainName, StringView* objectName);

int getDataSetByPath(StringView* pDatasetPath);

//! Пишет в буфер список имён детей
//! objectsTagToWrite - пишутся IED-объекты только с этим тэгом.
void writeChildrenNames(int rootObjPos, DomainNameWriter* writer,
	bool recursive, int objectsTagToWrite);

int encodeReadConst(uint8_t* outBuf, int bufPos, int constObjPos,
                    bool determineSize);

uint8_t* getAlignedDescrStruct(int pos);

int encodeObjectAccessAttrs(uint8_t* outBuf, int bufPos, int objectPos, bool determineSize,
                            bool topStruct);

//! Проверяет что тэг относится к служебной информации об объекте
//! Например, имя объекта.
bool IEDModel_isServiceInfo(uint8_t tag);

void* IEDModel_ptrFromPos(size_t pos);

void processSubobjects(int parentPos, void(*func)(int));

//! Возвращает позицию значения простого объекта по имени и позиции 
//! объекта-владльца. Результат указывает на тег константы или OCTET_STRING
//! структуры описания.
//! При ошибке возвращает 0
int getDAValuePos(int rootObjPos, uint8_t* name, int nameLen,
	enum InnerAttributeType* attrType);

//! В указанном DO находит атрибут с указанным именем и возвращает константную 
//! строку, которую он содержит. Если атрибут содержит что-то другое, то
//! возвращается что есть.
bool getConstDAString(size_t doPos, const char* attrName, StringView* result);

//! Возвращает значение константного DA в виде uint32_t
bool getConstDAULong(size_t parentPos, const char* attrName, uint32_t* result);

bool getIEDObjectNameView(size_t pos, StringView* name);

bool IEDModel_skipServiceInfo(BufferView* subObjects);

bool IEDModel_getChildren(const BufferView* berObject , BufferView *children);

bool IEDModel_getTermItemDescrStruct(BufferView* descrObject,
                                    void** pDescrStruct);

bool IEDModel_getBufferView(size_t pos, BufferView* bv);
