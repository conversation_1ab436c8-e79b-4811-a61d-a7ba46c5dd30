#pragma once

#include <platform_critical_section.h>
#include <types.h>
#include <stdbool.h>


#define OUT_QUEUE_SIZE 8

typedef struct
{
    CriticalSection cs;
    int head, tail;
    void* queue[OUT_QUEUE_SIZE];
} OutQueue;

void OutQueue_init(OutQueue* self);
void OutQueue_done(OutQueue* self);
bool OutQueue_isEmpty(OutQueue* self);
bool OutQueue_insert(OutQueue* self, void* item);
void* OutQueue_get(OutQueue* self);

