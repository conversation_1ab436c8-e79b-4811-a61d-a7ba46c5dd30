#include "bufView.h"
#include "stringView.h"
#include <string.h>
#include <stddef.h>

void BufferView_init(BufferView* bv, uint8_t* buf, size_t len, size_t pos)
{
    bv->p = buf;
    bv->len = len;
    bv->pos = pos;
}

bool BufferView_alloc(BufferView* bv, size_t byteCount, uint8_t** pFreeSpace)
{
    size_t freeSpace = bv->len - bv->pos;
    if (freeSpace < byteCount)
    {
        return false;
    }
    *pFreeSpace = bv->p + bv->pos;
    return true;
}


bool BufferView_writeStringView(BufferView* bv, const StringView* strView)
{
    if (strView->len == 0)
    {
        return true;
    }
    if (strView->len > bv->len - bv->pos)
    {
        return false;
    }
    memcpy(bv->p + bv->pos, strView->p, strView->len);
    bv->pos += strView->len;
    return true;
}

bool BufferView_writeStr(BufferView* bv, const char* str)
{
    StringView strView;
    StringView_fromCStr(&strView, (char*)str);
    return BufferView_writeStringView(bv, &strView);
}

bool BufferView_writeUshortBE(BufferView* bv, uint16_t data)
{
    if (bv->pos + sizeof(uint16_t) > bv->len)
    {
        return false;
    }
    bv->p[bv->pos++] = data >> 8;
    bv->p[bv->pos++] = data & 0xFF;
    return true;
}

size_t BufferView_writeData(BufferView* bv, void* data, size_t byteCount)
{
    size_t freeSpace = bv->len - bv->pos;
    if (freeSpace == 0)
    {
        return 0;
    }
    if (freeSpace < byteCount)
    {
        byteCount = freeSpace;
    }
    memcpy(bv->p + bv->pos, data, byteCount);
    bv->pos += byteCount;
    return byteCount;
}

bool BufferView_readStringView(BufferView* bv, size_t strLen,
    StringView* result)
{
    if (bv->pos + strLen > bv->len)
    {
        return false;
    }
    StringView_init(result, (const char*)bv->p + bv->pos, strLen);
    bv->pos += strLen;
    return true;
}

bool BufferView_advance(BufferView* bv, size_t len)
{
    if (bv->pos + len > bv->len)
    {
        return false;
    }
    bv->pos += len;
    return true;
}
