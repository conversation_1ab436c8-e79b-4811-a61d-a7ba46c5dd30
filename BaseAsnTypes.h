#ifndef  __BASEASNTYPES_H__
#define  __BASEASNTYPES_H__


#define BER_TAG_CLASS_MASK 0xC0
#define BER_CONTEXT_SPECIFIC 0x80

// Тэги, которые используются при передаче значений и описания соответствующих
// типов
#define IEC61850_BER_BOOLEAN 0x83
#define IEC61850_BER_BIT_STRING 0x84
#define IEC61850_BER_INTEGER 0x85
#define IEC61850_BER_UNSIGNED_INTEGER 0x86
#define IEC61850_BER_FLOAT 0x87
#define IEC61850_BER_OCTET_STRING 0x89
#define IEC61850_BER_VISIBLE_STRING 0x8A
#define IEC61850_BER_BINARY_TIME 0x8C
#define IEC61850_BER_MMS_STRING 0x90
#define IEC61850_BER_TIMESTAMP 0x91


#define	ASN_BOOLEAN				0x01
#define	ASN_INTEGER				0x02
#define	ASN_BIT_STRING			0x03
#define ASN_OCTET_STRING 0x04
#define ASN_OBJECT_IDENTIFIER	0x06
#define ASN_GRAPHIC_STRING      0x19
#define ASN_VISIBLE_STRING      0x1a

#define	ASN_SEQUENCE		0x30
#define	ASN_SET				0x31

#define ASN_TYPEDESCRIPTION_STRUCTURE   0xa2
#define ASN_TYPEDESCRIPTION_INTEGER     0x85
#define ASN_TYPEDESCRIPTION_FLOAT       0xa7

#define ASN_TYPEDESCRIPTION_VISIBLE_STRING   0x8a
#define ASN_TYPEDESCRIPTION_MMS_STRING   0x90
#define ASN_TYPEDESCRIPTION_BIT_STRING   0x84

#define ASN_TYPEDESCRIPTION_STRUCTURE_COMPONENTS 0xa1


#define ASN_TYPEDESCRIPTION_COMPONENT_NAME 0x80
#define ASN_TYPEDESCRIPTION_COMPONENT_TYPE 0xa1


#define	ASN_BOOLEANTYPE_SIZE	0x03
#define	ASN_INTEGER_SIZE		0x04


#define ASN_OBJECT_NAME_VMD_SPECIFIC    0xa0
#define ASN_OBJECT_NAME_DOMAIN_SPECIFIC 0xa1
#define ASN_OBJECT_NAME_AA_SPECIFIC     0xa2

typedef	signed char		ASN_Integer8;			//Ineger8    ::= INTEGER(-128..128)					стр.77
typedef	signed short	ASN_Integer16;			//Ineger16   ::= INTEGER(-32768..32767)				стр.77
typedef	signed int		ASN_Integer32;			//Ineger32   ::= INTEGER(-2147483648..2147483647)	стр.77
typedef	unsigned char	ASN_Unsigned8;			//Unsigned8	 ::= INTEGER(0..127)					стр.77
typedef	unsigned short	ASN_Unsigned16;			//Unsigned16 ::= INTEGER(0..32767)					стр.77
typedef	unsigned int	ASN_Unsigned32;			//Unsigned32 ::= INTEGER(0..2147483647)				стр.77

#define	ASN_maxIdenifier	32					//maxIdenifier INTEGER ::= 32						стр.77

//ASN_Identifier
typedef	struct
{
    char	Identifier[ ASN_maxIdenifier ];

}ASN_Identifier;

#endif
