#include <stdint.h>
#include <stddef.h>
#include "fast_memcpy.h"

// функция выдрана почти без переделки из lvgl
// копирует по 4 байта.
// если данные не выровненные - по байту или
// если их можно выровнять - выравнивание и затем по 4 

#define COPY32 *d32 = *s32; d32++; s32++;
#define COPY8 *d8 = *s8; d8++; s8++;
#define SET32(x) *d32 = x; d32++;
#define REPEAT8(expr) expr expr expr expr expr expr expr expr

#define ALIGN_MASK  0x3
void* fast_memcpy(void * dst, const void * src, size_t len)
{
    uint8_t * d8 = dst;
    const uint8_t * s8 = src;

    uintptr_t d_align = (uintptr_t)d8 & ALIGN_MASK;
    uintptr_t s_align = (uintptr_t)s8 & ALIGN_MASK;

    /*Byte copy for unaligned memories*/
    if(s_align != d_align) {
        while(len > 32) {
            REPEAT8(COPY8);
            REPEAT8(COPY8);
            REPEAT8(COPY8);
            REPEAT8(COPY8);
            len -= 32;
        }
        while(len) {
            COPY8
            len--;
        }
        return dst;
    }


    /*Make the memories aligned*/
    if(d_align) {
        d_align = ALIGN_MASK + 1 - d_align;
        while(d_align && len) {
            COPY8;
            d_align--;
            len--;
        }
    }
	
	{
		uint32_t * d32 = (uint32_t *)d8;
		const uint32_t * s32 = (uint32_t *)s8;
		while(len > 32) {
			REPEAT8(COPY32)
			len -= 32;
		}
	
		while(len > 4) {
			COPY32;
			len -= 4;
		}
	
		d8 = (uint8_t *)d32;
		s8 = (const uint8_t *)s32;
		while(len) {
			COPY8
			len--;
		}
	}

    return dst;
} 

