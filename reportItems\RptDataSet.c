
#include "RptDataSet.h"

#include "RptItem.h"
#include "../iedTree/DataSet.h"
#include "../reporter.h"
#include "../reports.h"




static RptDataSetItem createRptDataSetItem(
    RptDataSet rptDataSet, DataSetItem* srcDSItem)
{
    RptItem rptitem;
    RptDataSetItem dsItem = RptItem_alloc(sizeof(struct RptDataSetItemStruct));
    if(dsItem == NULL)
    {
        return NULL;
    }
    dsItem->parentRptDataSet = rptDataSet;

    rptitem = RptItem_create(srcDSItem->obj, dsItem);
    if(rptitem == NULL)
    {
        return NULL;
    }
    
    dsItem->rptItem = rptitem;

    return dsItem;
}    

static void addRptDataSetItem(RptDataSet rptDataSet, RptDataSetItem rptDsItem)
{
    if(rptDataSet->firstItem == NULL)
    {
        rptDataSet->firstItem = rptDsItem;
    }
    else
    {
        rptDataSet->lastItem->next = rptDsItem;
    }
    rptDataSet->lastItem = rptDsItem;
}

void RptDataSetItem_addFinalDARptItem(RptDataSetItem rptDSItem, RptItem rptItem)
{
    if(rptDSItem->firstFinalDAItem == NULL)
    {
        rptDSItem->firstFinalDAItem = rptItem;
    }
    else
    {
        rptDSItem->lastFinalDAItem->nextFinalDARptItem = rptItem;
    }
    rptDSItem->lastFinalDAItem = rptItem;
}



// Обновляет новоесостояние RptDataSetItem на основе данных из IEDTree
static void RptDataSetItem_updateChanges(RptDataSetItem rptDsItem)
{
    RptDataSet rptDataSet = rptDsItem->parentRptDataSet;
    TrgOps trgOps = (TrgOps)rptDataSet->reporter->rcb.trgOps;
    RptItem rptItem = rptDsItem->firstFinalDAItem;
    while(rptItem != NULL)
    {
        TrgOps changed = rptItem->iedObj->changed;
        if(changed != TRGOP_NONE)
        {            
            RptDataSetItem rptDsItem = rptItem->rptDsItem;
            rptDsItem->changedNew = changed;

            //Если есть конфликтующие изменения, то надо посылать отчёт
            if((rptDsItem->changedOld & trgOps)  != TRGOP_NONE
                && (changed & trgOps) != TRGOP_NONE)             
            {
                rptDsItem->needsSend = true;
                rptDataSet->needsSend = true;
            }
            rptItem->changedNew = changed;            
            rptItem->behaviour->updateChanges(rptItem);
        }        
        rptItem = rptItem->nextFinalDARptItem;
    }
}

// Переписывает новые значения поверх старых
static void RptDataSetItem_overwriteOld(RptDataSetItem rptDsItem)
{
    RptItem rptItem = rptDsItem->firstFinalDAItem;
    while(rptItem != NULL)
    {
        rptItem->behaviour->overwriteOld(rptItem);
        rptItem = rptItem->nextFinalDARptItem;
    }
}

RptDataSet RptDataSet_create(PReporter reporter)
{   
    DataSetItem* dsItem; 
    DataSet* srcDataSet = reporter->dataSet;
    RptDataSet rptDataSet = RptItem_alloc(sizeof(struct RptDataSetStruct));
    if(rptDataSet == NULL)
    {
        return NULL;
    }

    dsItem = srcDataSet->firstItem;
    while(dsItem != NULL)
    {
        RptDataSetItem rptDsItem = createRptDataSetItem(rptDataSet,dsItem);
        if(rptDsItem == NULL)
        {
            return NULL;
        }
        addRptDataSetItem(rptDataSet, rptDsItem);
        dsItem = dsItem->next;
    }

    return rptDataSet;    
}

void RptDataSet_updateChanges(RptDataSet rptDataSet)
{    
    RptDataSetItem rptDsItem = rptDataSet->firstItem;
    rptDataSet->needsSend = false;
    rptDataSet->hasChanges = false;

    while(rptDsItem != NULL)
    {
        RptDataSetItem_updateChanges(rptDsItem);
        rptDsItem = rptDsItem->next;
    }
}

bool RptDataSet_flushAndUpdate(RptProcContext* ctx)
{
    RptDataSet rptDataSet = ctx->reporter->rptDataSet;
    RptDataSetItem rptDsItem = rptDataSet->firstItem;


    #pragma message "Временно не обрабытываем GI, INTEGRITY и INIT"
    if(ctx->mode == RPT_GI || ctx->mode == RPT_INTG || ctx->mode == RPT_INIT)
    {
        return true;
    }

    if(!rptDataSet->hasChanges)
    {
        return true;
    }

    while(rptDsItem != NULL)
    {        
        if(rptDsItem->changedNew != TRGOP_NONE)
        {                 
            RptItem rptItem = rptDsItem->rptItem;            
            if(rptDsItem->needsSend)
            {                
                rptDsItem->needsSend = false;
                if(!rptItem->behaviour->encodeRead(rptItem, ctx->outBuf))
                {
                    return false;
                }            
                ctx->inclusionReasons[ctx->itemIdx] = rptDsItem->changedOld;
                
            }            
            else
            {
                RptDataSetItem_overwriteOld(rptDsItem);
                ctx->inclusionReasons[ctx->itemIdx] = 0;

            }
            
            rptDsItem->changedOld = rptDsItem->changedNew;
            rptDsItem->changedNew = TRGOP_NONE;
        }        
        rptDsItem = rptDsItem->next;
        ctx->itemIdx++;
    }
    rptDataSet->hasChanges = false;
    rptDataSet->needsSend = false;
    return true;
}

