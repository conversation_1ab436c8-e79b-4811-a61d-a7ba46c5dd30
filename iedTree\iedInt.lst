                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ams1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=iedInt.c -o iedTree\gh_ams1.o -list=iedTree/iedInt.lst C:\Users\<USER>\AppData\Local\Temp\gh_ams1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_ams1.s
Source File: iedInt.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      10 ;		iedTree/iedInt.c -o iedTree/iedInt.o

                      11 ;Source File:   iedTree/iedInt.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:55:12 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "iedInt.h"


                      21 ;2: 


                      22 ;3: #include "iedTree.h"


                      23 ;4: #include "iedFinalDA.h"


                      24 ;5: 


                      25 ;6: #include "../DataSlice.h"


                      26 ;7: #include "../AsnEncoding.h"


                      27 ;8: 


                      28 ;9: #include "debug.h"


                      29 ;10: 


                      30 ;11: #include "IEDCompile/AccessInfo.h"


                      31 ;12: 


                      32 ;13: #define MAX_INT32_ENCODED_SIZE 6


                      33 ;14: 


                      34 ;15: static void updateFromDataSlice(IEDEntity entity)


                      35 	.text

                      36 	.align	4

                      37 updateFromDataSlice:

00000000 e92d4010     38 	stmfd	[sp]!,{r4,lr}

00000004 e1a04000     39 	mov	r4,r0

                      40 ;16: {


                      41 

                      42 ;17: 	int offset  = entity->dataSliceOffset;


                      43 

00000008 e594002c     44 	ldr	r0,[r4,44]

                      45 ;18: 	int32_t value;


                      46 ;19: 


                      47 ;20: 	if(offset == -1)


                      48 

0000000c e3700001     49 	cmn	r0,1

00000010 0a00000f     50 	beq	.L2


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ams1.s
                      51 ;21: 	{


                      52 

                      53 ;22: 		return;


                      54 

                      55 ;23: 	}


                      56 ;24: 


                      57 ;25: 	value = DataSlice_getInt32FastCurrDS(offset);


                      58 

00000014 e1a00800     59 	mov	r0,r0 lsl 16

00000018 e1a00820     60 	mov	r0,r0 lsr 16

0000001c eb000000*    61 	bl	DataSlice_getInt32FastCurrDS

                      62 ;26: 


                      63 ;27: 


                      64 ;28: 	if(entity->intValue == value)


                      65 

00000020 e5941030     66 	ldr	r1,[r4,48]

00000024 e1510000     67 	cmp	r1,r0

                      68 ;29: 	{


                      69 

                      70 ;30: 		entity->changed = TRGOP_NONE;


                      71 

00000028 03a00000     72 	moveq	r0,0

0000002c 05840028     73 	streq	r0,[r4,40]

00000030 0a000007     74 	beq	.L2

                      75 ;31: 	}


                      76 ;32: 	else


                      77 ;33: 	{


                      78 

                      79 ;34: 		entity->changed = entity->trgOps;


                      80 

00000034 e5941024     81 	ldr	r1,[r4,36]

00000038 e5840030     82 	str	r0,[r4,48]

                      83 ;36: 		IEDEntity_setTimeStamp(entity, dataSliceGetTimeStamp());


                      84 

0000003c e5841028     85 	str	r1,[r4,40]

                      86 ;35: 		entity->intValue = value;


                      87 

00000040 eb000000*    88 	bl	dataSliceGetTimeStamp

00000044 e1a02001     89 	mov	r2,r1

00000048 e1a01000     90 	mov	r1,r0

0000004c e1a00004     91 	mov	r0,r4

00000050 eb000000*    92 	bl	IEDEntity_setTimeStamp

                      93 .L2:

00000054 e8bd4010     94 	ldmfd	[sp]!,{r4,lr}

00000058 e12fff1e*    95 	ret	

                      96 	.endf	updateFromDataSlice

                      97 	.align	4

                      98 ;offset	r0	local

                      99 ;value	r0	local

                     100 

                     101 ;entity	r4	param

                     102 

                     103 	.section ".bss","awb"

                     104 .L56:

                     105 	.data

                     106 	.text

                     107 

                     108 ;37: 	}


                     109 ;38: }


                     110 

                     111 ;39: 



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ams1.s
                     112 ;40: 


                     113 ;41: static bool calcReadLen(IEDEntity entity, size_t* pLen )


                     114 	.align	4

                     115 	.align	4

                     116 calcReadLen:

0000005c e92d4010    117 	stmfd	[sp]!,{r4,lr}

                     118 ;42: {


                     119 

                     120 ;43: 	// +2 for tag and length


                     121 ;44: 	*pLen = BerEncoder_Int32DetermineEncodedSize(entity->intValue) + 2;


                     122 

00000060 e5900030    123 	ldr	r0,[r0,48]

00000064 e1a04001    124 	mov	r4,r1

00000068 eb000000*   125 	bl	BerEncoder_Int32DetermineEncodedSize

0000006c e2800002    126 	add	r0,r0,2

00000070 e5840000    127 	str	r0,[r4]

                     128 ;45: 	return true;


                     129 

00000074 e3a00001    130 	mov	r0,1

00000078 e8bd4010    131 	ldmfd	[sp]!,{r4,lr}

0000007c e12fff1e*   132 	ret	

                     133 	.endf	calcReadLen

                     134 	.align	4

                     135 

                     136 ;entity	r0	param

                     137 ;pLen	r4	param

                     138 

                     139 	.section ".bss","awb"

                     140 .L97:

                     141 	.data

                     142 	.text

                     143 

                     144 ;46: }


                     145 

                     146 ;47: 


                     147 ;48: static bool encodeRead(IEDEntity entity, BufferView* outBuf)


                     148 	.align	4

                     149 	.align	4

                     150 encodeRead:

00000080 e92d4030    151 	stmfd	[sp]!,{r4-r5,lr}

00000084 e24dd004    152 	sub	sp,sp,4

00000088 e1a0200d    153 	mov	r2,sp

0000008c e1a05000    154 	mov	r5,r0

00000090 e1a04001    155 	mov	r4,r1

00000094 e1a00004    156 	mov	r0,r4

00000098 e3a01006    157 	mov	r1,6

0000009c eb000000*   158 	bl	BufferView_alloc

                     159 ;49: {


                     160 

                     161 ;50: 	uint8_t* encodeBuf;


                     162 ;51: 	int fullEncodedLen;


                     163 ;52: 


                     164 ;53: 	//Запрашиваем в буфере максимум места чтобы не вычислять.


                     165 ;54: 	//Это фактически только проверка, поэтому небольшая жадность не повредит.


                     166 ;55: 	if(!BufferView_alloc(outBuf,MAX_INT32_ENCODED_SIZE, &encodeBuf))


                     167 

000000a0 e3500000    168 	cmp	r0,0

                     169 ;56: 	{


                     170 

                     171 ;57: 		ERROR_REPORT("Unable to allocate buffer");


                     172 ;58: 		return false;



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ams1.s
                     173 

000000a4 0a000008    174 	beq	.L104

                     175 ;59: 	}


                     176 ;60: 


                     177 ;61: 	//Функция возвращает новое смещение в буфере, но поскольку начальное


                     178 ;62: 	//смещение 0, можно считать это размером.


                     179 ;63:     fullEncodedLen = BerEncoder_EncodeInt32WithTL(


                     180 

000000a8 e59d2000    181 	ldr	r2,[sp]

000000ac e5951030    182 	ldr	r1,[r5,48]

000000b0 e3a03000    183 	mov	r3,0

000000b4 e3a00085    184 	mov	r0,133

000000b8 eb000000*   185 	bl	BerEncoder_EncodeInt32WithTL

                     186 ;64:                 IEC61850_BER_INTEGER, entity->intValue, encodeBuf, 0);


                     187 ;65: 


                     188 ;66: 	outBuf->pos += fullEncodedLen;


                     189 

000000bc e5941004    190 	ldr	r1,[r4,4]

000000c0 e0811000    191 	add	r1,r1,r0

000000c4 e5841004    192 	str	r1,[r4,4]

                     193 ;67: 	return true;


                     194 

000000c8 e3a00001    195 	mov	r0,1

                     196 .L104:

000000cc e28dd004    197 	add	sp,sp,4

000000d0 e8bd4030    198 	ldmfd	[sp]!,{r4-r5,lr}

000000d4 e12fff1e*   199 	ret	

                     200 	.endf	encodeRead

                     201 	.align	4

                     202 ;encodeBuf	[sp]	local

                     203 

                     204 ;entity	r5	param

                     205 ;outBuf	r4	param

                     206 

                     207 	.section ".bss","awb"

                     208 .L154:

                     209 	.data

                     210 	.text

                     211 

                     212 ;68: }


                     213 

                     214 ;69: 


                     215 ;70: 


                     216 ;71: void IEDInt32_init(IEDEntity entity)


                     217 	.align	4

                     218 	.align	4

                     219 IEDInt32_init::

000000d8 e92d4070    220 	stmfd	[sp]!,{r4-r6,lr}

000000dc e280405c    221 	add	r4,r0,92

000000e0 e5140004    222 	ldr	r0,[r4,-4]

                     223 ;74: 	IntBoolAccessInfo* accessInfo = extInfo->accessInfo;


                     224 

000000e4 e5900000    225 	ldr	r0,[r0]

                     226 ;75: 


                     227 ;76: 	//Если будет ошибка, то запишется -1;


                     228 ;77: 	entity->dataSliceOffset = DataSlice_getIntOffset(accessInfo->valueOffset);


                     229 

000000e8 e59f5028*   230 	ldr	r5,.L197

000000ec e5900004    231 	ldr	r0,[r0,4]

000000f0 e59f6024*   232 	ldr	r6,.L198

                     233 ;72: {



                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ams1.s
                     234 

                     235 ;73: 	TerminalItem* extInfo = entity->extInfo;


                     236 

000000f4 eb000000*   237 	bl	DataSlice_getIntOffset

000000f8 e5040030    238 	str	r0,[r4,-48]

                     239 ;78: 


                     240 ;79: 	entity->calcReadLen = calcReadLen;


                     241 

                     242 ;80: 	entity->encodeRead = encodeRead;


                     243 

000000fc e1a00006    244 	mov	r0,r6

00000100 e8840021    245 	stmea	[r4],{r0,r5}

                     246 ;81: 	entity->updateFromDataSlice = updateFromDataSlice;


                     247 

00000104 e59f0014*   248 	ldr	r0,.L199

00000108 e584000c    249 	str	r0,[r4,12]

                     250 ;82: 


                     251 ;83: 	IEDTree_addToCmpList(entity);


                     252 

0000010c e244005c    253 	sub	r0,r4,92

00000110 e8bd4070    254 	ldmfd	[sp]!,{r4-r6,lr}

00000114 ea000000*   255 	b	IEDTree_addToCmpList

                     256 	.endf	IEDInt32_init

                     257 	.align	4

                     258 ;extInfo	r0	local

                     259 ;accessInfo	r0	local

                     260 

                     261 ;entity	r4	param

                     262 

                     263 	.section ".bss","awb"

                     264 .L190:

                     265 	.data

                     266 	.text

                     267 

                     268 ;84: }


                     269 	.align	4

                     270 .L197:

00000118 00000000*   271 	.data.w	calcReadLen

                     272 	.type	.L197,$object

                     273 	.size	.L197,4

                     274 

                     275 .L198:

0000011c 00000000*   276 	.data.w	encodeRead

                     277 	.type	.L198,$object

                     278 	.size	.L198,4

                     279 

                     280 .L199:

00000120 00000000*   281 	.data.w	updateFromDataSlice

                     282 	.type	.L199,$object

                     283 	.size	.L199,4

                     284 

                     285 	.align	4

                     286 

                     287 	.data

                     288 	.ghsnote version,6

                     289 	.ghsnote tools,3

                     290 	.ghsnote options,0

                     291 	.text

                     292 	.align	4

