{"configurations": [{"name": "BayController", "includePath": ["${workspaceFolder}/**", ".", "platform", "../../../../AT91CORE/clib", "../../../../AT91CORE/clib/ansi", "../../../../AT91CORE/modules/SYSDLL/include", "../../..", "../../../../lwipport/lwiplib", "../../../../lwipport/include", "${LIB_PATH}/lwip/src/include", "../../../../AT91CORE/modules/BayController/ethbus", "fs", "tlsf", "fdzipstream", "iedTree"], "defines": ["_DEBUG", "UNICODE", "_UNICODE", "LWIP"]}], "version": 4}