#include "ConfigFiles.h"
#include "../pwin_access.h"
#include <debug.h>
#include <string.h>
#include <stdlib.h>

//Комментарий для проверки кодировки


// информация о конфигурационном файле
typedef struct 
{
	//! сигнатура (заполняется пользователем)
	unsigned long signature;
	//! имя файла, отображаемое в файловой системе (заполняется пользователем)
	char fileName[8];
	// указатель, где файл хранится в памяти или NULL если файл не найден
	void *pHeader;
	// указатель на данные
	unsigned char *pData;
	//! атрибуты
	size_t fileSize;
	__time32_t time;	
}CfgFileInfo;


//! количество конфигурационных файлов (автоматически)
#define CFG_FILES_COUNT (sizeof(cfgFiles)/sizeof(cfgFiles[0]))

//! icd файл
CfgFileInfo icdFile = {'ICDF',"icd.zip" ,NULL}; 
//! cid файл
CfgFileInfo  cidFile = {'CIDF', "cid.zip" ,NULL}; 
//! все файлы 
CfgFileInfo *cfgFiles[] = { &icdFile, &cidFile };


static FNameErrCode getCfgFileInfo(FSFindData* fileInfo,
	BufferView* bufToWrite)
{
	CfgFileInfo *info = NULL;
	if (fileInfo->fileIndex >= CFG_FILES_COUNT)
	{
		return FNAME_NOT_FOUND;
	}

	info = cfgFiles[fileInfo->fileIndex];
	if (!info || !info->pData)
	{
		return FNAME_NOT_FOUND;
	}

	fileInfo->attr.fileSize = info->fileSize;
	fileInfo->attr.time = info->time;
	fileInfo->attr.ms = 0;

	if (!BufferView_writeStr(bufToWrite, info->fileName))
	{
		return FNAME_BUF_ERROR;
	}
	return FNAME_OK;
}

//! загружает файл по сигнатуре, всегда успешно
static void loadCfgFile(uint32_t signature, CfgFileInfo *info)
{
	info->pHeader = info->pData = NULL;
	loadRomModule(signature, &info->pHeader, &info->pData,
		&info->fileSize,
		&info->time);
}

bool CFGFS_init(void)
{
	int i;
	// чтение файлов в память, без проверки на результат 
	for (i = 0; i < CFG_FILES_COUNT; ++i)
	{
		loadCfgFile(cfgFiles[i]->signature, cfgFiles[i]);
	}
	return TRUE;
}

FNameErrCode CFGFS_findFirst(StringView* startFileName, FSFindData* findData,
	BufferView* fnameBuf)
{
	FNameErrCode result;
	
	findData->fileIndex = 0;

	while (findData->fileIndex < CFG_FILES_COUNT)
	{
		result = getCfgFileInfo(findData, fnameBuf);
		findData->fileIndex++;
		// файл найден
		if (result == FNAME_OK)
		{
			break;
		}
		
		// файл не найден - ищем дальше
	}

	return result;
}

FNameErrCode CFGFS_findNext(FSFindData* findData, BufferView* fnameBuf)
{
	FNameErrCode result;	
	result = getCfgFileInfo(findData, fnameBuf);	
	findData->fileIndex++;
	return result;
}

void CFGFS_findClose(FSFindData* findData)
{
	
}
bool CFGFS_openFile(StringView* fileName, FRSM* frsm, FSFileAttr* attr)
{	
	CfgFileInfo *info = NULL;
	int i;

	for (i = 0; i < CFG_FILES_COUNT; ++i)
	{
		if (StringView_cmpCStr(fileName, cfgFiles[i]->fileName) == 0)
		{
			info = cfgFiles[i];
		}
	}
    
	// по идее такого быть не должно, т.к. если файл не найден - он не должен запрашиваться
	if (!info || !info->pData)
	{
		return FALSE;
	}

	frsm->start = info->pData;
	frsm->size = info->fileSize;
	frsm->pos = 0;

	attr->fileSize = info->fileSize;
	attr->time = info->time;
	attr->ms = 0;

	return TRUE;
}

bool CFGFS_closeFile(FRSM* frsm)
{
	return TRUE;
}

bool CFGFS_readFile(FRSM* frsm, BufferView* readBuf, bool* moreFollows)
{
	size_t readCount = frsm->size - frsm->pos;

	if (readCount == 0)
	{
		return FALSE;
	}

	readCount = BufferView_writeData(readBuf,
		frsm->start + frsm->pos, readCount);
	*moreFollows = (readCount < frsm->size - frsm->pos);
	frsm->pos += readCount;
	return TRUE;
}

