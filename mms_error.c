#include "mms_error.h"

#include "AsnEncoding.h"
#include "MmsConst.h"
#include <debug.h>
#include <stddef.h>
//Для memcpy
#include <string.h>


#define MMS_REJECT_CONFIRMED_REQUEST 1
//#define MMS_REJECT_CONFIRMED_RESPONSE 2
//#define MMS_REJECT_CONFIRMED_ERROR 3
//#define MMS_REJECT_UNCONFIRMED 4
#define MMS_REJECT_PDU_ERROR 5
//#define MMS_REJECT_CANCEL_REQUEST 6
//#define MMS_REJECT_CANCEL_RESPONSE 7
//#define MMS_REJECT_CANCEL_ERROR 8
//#define MMS_REJECT_CONCLUDE_REQUEST 9
//#define MMS_REJECT_CONCLUDE_RESPONSE 10
//#define MMS_REJECT_CONCLUDE_ERROR 11

#define MMS_REJECT_CONFIRMED_REQUEST_OTHER 0
#define MMS_REJECT_CONFIRMED_REQUEST_UNRECOGNIZED_SERVICE 1
//#define MMS_REJECT_CONFIRMED_REQUEST_UNRECOGNIZED_MODIFIER 2
//#define MMS_REJECT_CONFIRMED_REQUEST_INVALID_INVOKE_ID 3
#define MMS_REJECT_CONFIRMED_REQUEST_INVALID_ARGUMENT 4
//#define MMS_REJECT_CONFIRMED_REQUEST_INVALID_MODIFIER 5
//#define MMS_REJECT_CONFIRMED_REQUEST_MAX_SERV_OUTSTANDING_EXCEEDED 6
//#define MMS_REJECT_CONFIRMED_REQUEST_MAX_RECURSION_EXCEEDED 8
//#define MMS_REJECT_CONFIRMED_REQUEST_VALUE_OUT_OF_RANGE 9

#define MMS_REJECT_PDU_ERROR_UNKNOWN_PDU_TYPE 0
#define MMS_REJECT_PDU_ERROR_INVALID_PDU 1
//#define MMS_REJECT_PDU_ERROR_ILLEGAL_ACSI_MAPPING 2

static int encodeMmsRejectPdu(unsigned int* invokeId, int rejectType, int rejectReason,
                               uint8_t* outBuf)
{
    int outBufPos = 0;
    unsigned int invokeIdLength = 0;
    unsigned int rejectPduLength = 3;

    if (invokeId != NULL) {
        invokeIdLength = BerEncoder_UInt32determineEncodedSize(*invokeId);
        rejectPduLength += 2 + invokeIdLength;
    }

    /* Encode reject PDU */
    outBufPos = BerEncoder_encodeTL(0xa4, rejectPduLength, outBuf, outBufPos);

    if (invokeId != NULL) {
       outBufPos = BerEncoder_encodeTL(0x80, invokeIdLength, outBuf, outBufPos);
       outBufPos = BerEncoder_encodeUInt32(*invokeId, outBuf, outBufPos);
    }

    outBuf[outBufPos++] = (uint8_t) (0x80 + rejectType);
    outBuf[outBufPos++] = 0x01;
    outBuf[outBufPos++] = (uint8_t) rejectReason;

    return outBufPos;
}

int mms_createMmsRejectPdu(unsigned int* invokeId, int reason, uint8_t* outBuf)
{
    int rejectType = 0;
    int rejectReason = 0;

    switch (reason) {

    case MMS_ERROR_REJECT_UNRECOGNIZED_SERVICE:
        rejectType = MMS_REJECT_CONFIRMED_REQUEST;
        rejectReason = MMS_REJECT_CONFIRMED_REQUEST_UNRECOGNIZED_SERVICE;
        break;

    case MMS_ERROR_REJECT_UNKNOWN_PDU_TYPE:
        rejectType = MMS_REJECT_PDU_ERROR;
        rejectReason = MMS_REJECT_PDU_ERROR_UNKNOWN_PDU_TYPE;
        break;

    case MMS_ERROR_REJECT_REQUEST_INVALID_ARGUMENT:
        rejectType = MMS_REJECT_CONFIRMED_REQUEST;
        rejectReason = MMS_REJECT_CONFIRMED_REQUEST_INVALID_ARGUMENT;
        break;

    case MMS_ERROR_REJECT_INVALID_PDU:
        rejectType = MMS_REJECT_PDU_ERROR;
        rejectReason = MMS_REJECT_PDU_ERROR_INVALID_PDU;
        break;

    default:
        rejectType = MMS_REJECT_CONFIRMED_REQUEST;
        rejectReason = MMS_REJECT_CONFIRMED_REQUEST_OTHER;
    }

    return encodeMmsRejectPdu(invokeId, rejectType, rejectReason, outBuf);
}


int CreateMmsConfirmedErrorPdu( unsigned int iInvokeId, unsigned char* pResponseBuffer,
                                MmsError ErrorType )
{
    unsigned char ErrorCodeBuf[] = { 0xa2, 0x05, 0xa0, 0x03, 0x87, 0x01, 0x01 };
    int iErrorCodeBufSize = 7;

    int iIvokeIdSize = 2 + BerEncoder_UInt32determineEncodedSize( iInvokeId );
    int iPduLength = 2 + iIvokeIdSize;

    int iBufPos = 0;
    int exists_error = 0;

    if (ErrorType == MMS_ERROR_ACCESS_OTHER) {
        ErrorCodeBuf[6] = 0x00;
        exists_error = 1;
    }
    if (ErrorType == MMS_ERROR_ACCESS_OBJECT_ACCESS_UNSUPPORTED) {
        ErrorCodeBuf[6] = 0x01;
        exists_error = 1;
    }
    if (ErrorType == MMS_ERROR_ACCESS_OBJECT_NON_EXISTENT) {
        ErrorCodeBuf[6] = 0x02;
        exists_error = 1;
    }
    if (ErrorType == MMS_ERROR_ACCESS_OBJECT_ACCESS_DENIED) {
        ErrorCodeBuf[6] = 0x03;
        exists_error = 1;
    }
    if (ErrorType == MMS_ERROR_ACCESS_OBJECT_INVALIDATED) {
        ErrorCodeBuf[6] = 0x04;
        exists_error = 1;
    }

    if (exists_error != 0) {
        iPduLength += iErrorCodeBufSize;
        iBufPos = BerEncoder_encodeTL(MMS_CONFIRMED_ERRROR_PDU, iPduLength - 2,
            pResponseBuffer, iBufPos);
        iBufPos = BerEncoder_encodeUInt32WithTL(0x80, iInvokeId,
            pResponseBuffer, iBufPos);
        memcpy(&pResponseBuffer[iBufPos], ErrorCodeBuf, iErrorCodeBufSize);
        return iPduLength;
    }
    else {
        return -1;
    }
}


bool MMSError_createConfirmedErrorPdu(uint32_t invokeId, MmsError errorType,
    BufferView* outBufView)
{   
    // Это обёртка над CreateMmsConfirmedErrorPdu
    size_t maxErrPduLen = 15;
    int pduLen;
    uint8_t* pduBuf;
    if (!BufferView_alloc(outBufView, maxErrPduLen, &pduBuf))
    {
        ERROR_REPORT("Unable to allocate buffer");
        return false;
    }
    pduLen = CreateMmsConfirmedErrorPdu(invokeId, pduBuf, errorType);
    if (pduLen < 0)
    {
        ERROR_REPORT("Unable to create MMS error PDU");
        return false;
    }
    if (!BufferView_advance(outBufView, pduLen))
    {
        ERROR_REPORT("Unable to advance buffer view");
        return false;
    }
    return true; 
}
