#ifndef MMS_GET_VARIABLE_ACCESS_ATTRIBURES_H_
#define MMS_GET_VARIABLE_ACCESS_ATTRIBURES_H_

#include "mmsconnection.h"

int mms_handleGetVariableAccessAttr(MmsConnection* mmsConn,
                                 unsigned char* inBuf, int bufPos, int maxBufPos,
                                  unsigned int invokeId, unsigned char* response);


#define MMS_GET_VARIABLE_ACCESS_ATTRIBUTES_REQUEST_ADDRESS		0xa1

#define MMS_GET_VARIABLE_ACCESS_ATTRIBURES_RESPONSE_TYPE_DESCRIPTION 0xa2

#endif
