#pragma once

#include "../stringView.h"
#include "../bufView.h"
#include "../frsm.h"

//Доступ к конфигурационным файлам

bool CFGFS_init(void);
FNameErrCode CFGFS_findFirst(StringView* startFileName, FSFindData* findData,
	BufferView* fnameBuf);
FNameErrCode CFGFS_findNext(FSFindData* findData, BufferView* fnameBuf);
void CFGFS_findClose(FSFindData* findData);
bool CFGFS_openFile(StringView* fileName, FRSM* frsm, FSFileAttr* attr);
bool CFGFS_closeFile(FRSM* frsm);
bool CFGFS_readFile(FRSM* frsm, BufferView* readBuf, bool* moreFollows);

