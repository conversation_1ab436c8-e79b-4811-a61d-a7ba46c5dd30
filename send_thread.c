#include "send_thread.h"
#include "out_buffers.h"
#include "Cotp.h"
#include <Clib.h>
#include <stddef.h>

void sendThread(IsoConnection* isoConn)
{
    isoConn->sendThreadIsRunning = true;
    while(isoConn->connected)
    {
        SessionOutBuffer* buffer = OutQueue_get(&isoConn->outQueue);
        if(buffer == NULL)
        {
            Idle();
        }
        else
        {
            //Посылаем
            cotpSendData(&isoConn->cotpConn, buffer->cotpOutBuf, buffer->byteCount);
			freeSessionOutBuffer(buffer);
        }
    }
    isoConn->sendThreadIsRunning = false;
}
