                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cp81.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=RptItem.c -o reportItems\gh_cp81.o -list=reportItems/RptItem.lst C:\Users\<USER>\AppData\Local\Temp\gh_cp81.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_cp81.s
Source File: RptItem.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      10 ;		reportItems/RptItem.c -o reportItems/RptItem.o

                      11 ;Source File:   reportItems/RptItem.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:55:49 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "RptItem.h"


                      21 ;2: 


                      22 ;3: 


                      23 ;4: #include "RptDataSet.h"


                      24 ;5: 


                      25 ;6: #include "RptBool.h"


                      26 ;7: 


                      27 ;8: #include "../MemoryManager.h"


                      28 ;9: 


                      29 ;10: 


                      30 ;11: #include "IEDCompile/InnerAttributeTypes.h"


                      31 ;12: #include <debug.h>


                      32 ;13: 


                      33 ;14: #include <string.h>


                      34 ;15: 


                      35 ;16: //==================== Загрушки функций=====================


                      36 ;17: 


                      37 ;18: static void dafaultUpdateChanges(RptItem item)


                      38 

                      39 ;20:     ERROR_REPORT("dafaultUpdateChanges called");    


                      40 ;21: }


                      41 

                      42 ;22: 


                      43 ;23: static bool defaultEncodeRead(RptItem item, BufferView* outBuf)


                      44 

                      45 ;27: }


                      46 

                      47 ;28: 


                      48 ;29: static bool defaultCalcReadLen(RptItem item, size_t* pLen )


                      49 

                      50 ;33: }



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cp81.s
                      51 

                      52 ;34: 


                      53 ;35: static void defaultOverwriteOld(RptItem item)


                      54 

                      55 ;37:     ERROR_REPORT("defaultOverwriteOld called"); 


                      56 ;38: }


                      57 

                      58 ;39: 


                      59 ;40: struct RptItemBehavior defaultBehavior = {


                      60 ;41:     dafaultUpdateChanges,


                      61 ;42:     defaultEncodeRead,


                      62 ;43:     defaultCalcReadLen,


                      63 ;44:     defaultOverwriteOld


                      64 ;45: };


                      65 ;46: 


                      66 ;47: //================= Загрушки функций для FinalDA =================


                      67 ;48: 


                      68 ;49: static void finalDADefaultUpdateChanges(RptItem item)


                      69 

                      70 ;51:     ERROR_REPORT("finalDADefaultUpdateChanges called"); 


                      71 ;52: }


                      72 

                      73 ;53: 


                      74 ;54: static bool finalDADefaultEncodeRead(RptItem item, BufferView* outBuf)


                      75 

                      76 ;58: }


                      77 

                      78 ;59: 


                      79 ;60: static bool finalDADefaultCalcReadLen(RptItem item, size_t* pLen )


                      80 

                      81 ;64: }


                      82 

                      83 ;65: 


                      84 ;66: static void finalDADefaultOverwriteOld(RptItem item)


                      85 

                      86 ;68:     ERROR_REPORT("finalDADefaultOverwriteOld called"); 


                      87 ;69: }


                      88 

                      89 ;70: 


                      90 ;71: struct RptItemBehavior defaultFinalDABehavior = {


                      91 ;72:     finalDADefaultUpdateChanges,


                      92 ;73:     finalDADefaultEncodeRead,


                      93 ;74:     finalDADefaultCalcReadLen,


                      94 ;75:     finalDADefaultOverwriteOld


                      95 ;76: };


                      96 ;77: 


                      97 ;78: 


                      98 ;79: void *RptItem_alloc(size_t size)


                      99 	.text

                     100 	.align	4

                     101 RptItem_alloc::

00000000 e92d4030    102 	stmfd	[sp]!,{r4-r5,lr}

                     103 ;80: {


                     104 

                     105 ;81:     void* p = MM_alloc(size);


                     106 

00000004 e1a05000    107 	mov	r5,r0

00000008 eb000000*   108 	bl	MM_alloc

0000000c e1b04000    109 	movs	r4,r0

                     110 ;82:     if(p != NULL)


                     111 


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cp81.s
                     112 ;83:     {


                     113 

                     114 ;84:         memset(p, 0, size);


                     115 

00000010 11a02005    116 	movne	r2,r5

00000014 13a01000    117 	movne	r1,0

00000018 1b000000*   118 	blne	memset

                     119 ;85:     }


                     120 ;86:     return p;


                     121 

0000001c e1a00004    122 	mov	r0,r4

00000020 e8bd8030    123 	ldmfd	[sp]!,{r4-r5,pc}

                     124 	.endf	RptItem_alloc

                     125 	.align	4

                     126 ;p	r4	local

                     127 

                     128 ;size	r5	param

                     129 

                     130 	.section ".bss","awb"

                     131 .L173:

                     132 	.data

                     133 	.text

                     134 

                     135 ;87: }


                     136 

                     137 ;88: 


                     138 ;89: 


                     139 ;90: 


                     140 ;91: bool initFinalDARptItem(RptItem item)


                     141 	.align	4

                     142 	.align	4

                     143 initFinalDARptItem::

00000024 e92d4000    144 	stmfd	[sp]!,{lr}

                     145 ;92: {


                     146 

                     147 ;93:     IEDEntity iedObj = item->iedObj;    


                     148 

00000028 e5901008    149 	ldr	r1,[r0,8]

                     150 ;94:     if(iedObj->type == IED_ENTITY_DA_TERMINAL_ITEM)


                     151 

0000002c e5912050    152 	ldr	r2,[r1,80]

00000030 e3520008    153 	cmp	r2,8

                     154 ;109:         }


                     155 ;110:     }


                     156 ;111:     


                     157 ;112:     return true;


                     158 

00000034 13a00001    159 	movne	r0,1

00000038 1a000005    160 	bne	.L186

                     161 ;95:     {


                     162 

                     163 ;96:         if(iedObj->trgOps == TRGOP_NONE)


                     164 

                     165 ;97:         {


                     166 

                     167 ;98:             TRACE("FinalDA without trgOps in report");            


                     168 ;99:         }


                     169 ;100: 


                     170 ;101:         switch(iedObj->subType)


                     171 

0000003c e5911054    172 	ldr	r1,[r1,84]


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cp81.s
00000040 e3510006    173 	cmp	r1,6

                     174 ;105:             break;


                     175 ;106:         default:


                     176 ;107:             ERROR_REPORT("Unsupported FinalDA type");


                     177 ;108:             return false;


                     178 

00000044 13a00000    179 	movne	r0,0

00000048 1a000001    180 	bne	.L186

                     181 ;102:         {


                     182 ;103:         case INNER_TYPE_BOOLEAN:


                     183 ;104:             RptBool_init(item);


                     184 

0000004c eb000000*   185 	bl	RptBool_init

                     186 ;109:         }


                     187 ;110:     }


                     188 ;111:     


                     189 ;112:     return true;


                     190 

00000050 e3a00001    191 	mov	r0,1

                     192 .L186:

00000054 e8bd8000    193 	ldmfd	[sp]!,{pc}

                     194 	.endf	initFinalDARptItem

                     195 	.align	4

                     196 ;iedObj	r1	local

                     197 

                     198 ;item	r0	param

                     199 

                     200 	.section ".bss","awb"

                     201 .L256:

                     202 	.data

                     203 	.text

                     204 

                     205 ;113: }


                     206 

                     207 ;114: 


                     208 ;115: RptItem RptItem_create(IEDEntity iedObj, RptDataSetItem rptDsItem)


                     209 	.align	4

                     210 	.align	4

                     211 	.align	4

                     212 RptItem_create::

00000058 e92d4070    213 	stmfd	[sp]!,{r4-r6,lr}

                     214 ;116: {


                     215 

                     216 ;117:     RptItem item = RptItem_alloc(sizeof(struct RptItemStruct));


                     217 

0000005c e1a06001    218 	mov	r6,r1

00000060 e1a05000    219 	mov	r5,r0

00000064 e3a0002c    220 	mov	r0,44

00000068 ebffffe4*   221 	bl	RptItem_alloc

0000006c e1b04000    222 	movs	r4,r0

                     223 ;118:     if(item == NULL)


                     224 

00000070 0a00000a    225 	beq	.L283

                     226 ;119:     {


                     227 

                     228 ;120:         return NULL;


                     229 

                     230 ;121:     }


                     231 ;122: 


                     232 ;123:     item->behaviour = &defaultBehavior;


                     233 


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cp81.s
00000074 e59f00bc*   234 	ldr	r0,.L446

00000078 e5840004    235 	str	r0,[r4,4]

                     236 ;124: 


                     237 ;125:     if(IEDEntity_isFinalDA(iedObj))


                     238 

0000007c e1a00005    239 	mov	r0,r5

00000080 eb000000*   240 	bl	IEDEntity_isFinalDA

00000084 e3500000    241 	cmp	r0,0

00000088 0a00000b    242 	beq	.L280

                     243 ;126:     {        


                     244 

                     245 ;127:         item->behaviour = &defaultFinalDABehavior;


                     246 

0000008c e59f00a8*   247 	ldr	r0,.L447

00000090 e9840061    248 	stmfa	[r4],{r0,r5-r6}

                     249 ;128:         item->iedObj = iedObj;


                     250 

                     251 ;129:         item->rptDsItem = rptDsItem;


                     252 

                     253 ;130:         if(!initFinalDARptItem(item))


                     254 

00000094 e1a00004    255 	mov	r0,r4

00000098 ebffffe1*   256 	bl	initFinalDARptItem

0000009c e3500000    257 	cmp	r0,0

                     258 .L283:

                     259 ;131:         {


                     260 

                     261 ;132:             return NULL;


                     262 

000000a0 03a00000    263 	moveq	r0,0

000000a4 0a000016    264 	beq	.L275

                     265 .L282:

                     266 ;133:         }


                     267 ;134:         RptDataSetItem_addFinalDARptItem(rptDsItem, item);


                     268 

000000a8 e1a01004    269 	mov	r1,r4

000000ac e1a00006    270 	mov	r0,r6

000000b0 eb000000*   271 	bl	RptDataSetItem_addFinalDARptItem

                     272 ;158:         }


                     273 ;159:     }


                     274 ;160: 


                     275 ;161:     return item;    


                     276 

000000b4 e1a00004    277 	mov	r0,r4

000000b8 ea000011    278 	b	.L275

                     279 .L280:

                     280 ;135:     }


                     281 ;136:     else


                     282 ;137:     {


                     283 

                     284 ;138:         // Это составной элемент


                     285 ;139:         // Рекурсивно создаём детей


                     286 ;140:         IEDEntity child = iedObj->firstChild;


                     287 

000000bc e5955004    288 	ldr	r5,[r5,4]

                     289 ;141:         while(child != NULL)


                     290 

000000c0 e3550000    291 	cmp	r5,0

000000c4 0a00000d    292 	beq	.L285

                     293 .L288:

                     294 ;142:         {



                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cp81.s
                     295 

                     296 ;143:             RptItem childItem = RptItem_create(child, rptDsItem);


                     297 

000000c8 e1a01006    298 	mov	r1,r6

000000cc e1a00005    299 	mov	r0,r5

000000d0 ebffffe0*   300 	bl	RptItem_create

                     301 ;144:             if(childItem == NULL)


                     302 

000000d4 e3500000    303 	cmp	r0,0

000000d8 0afffff0    304 	beq	.L283

                     305 ;145:             {


                     306 

                     307 ;146:                 return NULL;


                     308 

                     309 ;147:             }


                     310 ;148:             if(item->firstChild == NULL)


                     311 

000000dc e5941024    312 	ldr	r1,[r4,36]

000000e0 e595500c    313 	ldr	r5,[r5,12]

000000e4 e3510000    314 	cmp	r1,0

                     315 ;149:             {


                     316 

                     317 ;150:                 item->firstChild = childItem;


                     318 

000000e8 15941028    319 	ldrne	r1,[r4,40]

000000ec 05840024    320 	streq	r0,[r4,36]

                     321 ;155:             }


                     322 ;156:             item->lastChild = childItem;


                     323 

                     324 ;157:             child = child->next;


                     325 

                     326 ;151:             }


                     327 ;152:             else


                     328 ;153:             {


                     329 

                     330 ;154:                 item->lastChild->nextSibling = childItem;


                     331 

000000f0 15810000    332 	strne	r0,[r1]

                     333 ;155:             }


                     334 ;156:             item->lastChild = childItem;


                     335 

000000f4 e5840028    336 	str	r0,[r4,40]

                     337 ;157:             child = child->next;


                     338 

000000f8 e3550000    339 	cmp	r5,0

000000fc 1afffff1    340 	bne	.L288

                     341 .L285:

                     342 ;158:         }


                     343 ;159:     }


                     344 ;160: 


                     345 ;161:     return item;    


                     346 

00000100 e1a00004    347 	mov	r0,r4

                     348 .L275:

00000104 e8bd8070    349 	ldmfd	[sp]!,{r4-r6,pc}

                     350 	.endf	RptItem_create

                     351 	.align	4

                     352 ;item	r4	local

                     353 ;child	r5	local

                     354 ;childItem	r0	local

                     355 


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cp81.s
                     356 ;iedObj	r5	param

                     357 ;rptDsItem	r6	param

                     358 

                     359 	.data

                     360 .L408:

                     361 	.text

                     362 

                     363 ;162: }


                     364 	.align	4

                     365 	.align	4

                     366 dafaultUpdateChanges:

                     367 ;19: {


                     368 

00000108 e12fff1e*   369 	ret	

                     370 	.endf	dafaultUpdateChanges

                     371 	.align	4

                     372 

                     373 ;item	none	param

                     374 

                     375 	.section ".bss","awb"

                     376 .L462:

                     377 	.data

                     378 	.text

                     379 	.align	4

                     380 	.align	4

                     381 defaultEncodeRead:

                     382 ;24: {


                     383 

                     384 ;25:     ERROR_REPORT("defaultEncodeRead called"); 


                     385 ;26:     return false;


                     386 

0000010c e3a00000    387 	mov	r0,0

00000110 e12fff1e*   388 	ret	

                     389 	.endf	defaultEncodeRead

                     390 	.align	4

                     391 

                     392 ;item	none	param

                     393 ;outBuf	none	param

                     394 

                     395 	.section ".bss","awb"

                     396 .L494:

                     397 	.data

                     398 	.text

                     399 	.align	4

                     400 	.align	4

                     401 defaultCalcReadLen:

                     402 ;30: {


                     403 

                     404 ;31:     ERROR_REPORT("defaultCalcReadLen called"); 


                     405 ;32:     return false;


                     406 

00000114 e3a00000    407 	mov	r0,0

00000118 e12fff1e*   408 	ret	

                     409 	.endf	defaultCalcReadLen

                     410 	.align	4

                     411 

                     412 ;item	none	param

                     413 ;pLen	none	param

                     414 

                     415 	.section ".bss","awb"

                     416 .L526:


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cp81.s
                     417 	.data

                     418 	.text

                     419 	.align	4

                     420 	.align	4

                     421 defaultOverwriteOld:

                     422 ;36: {


                     423 

0000011c e12fff1e*   424 	ret	

                     425 	.endf	defaultOverwriteOld

                     426 	.align	4

                     427 

                     428 ;item	none	param

                     429 

                     430 	.section ".bss","awb"

                     431 .L558:

                     432 	.data

                     433 	.text

                     434 	.align	4

                     435 	.align	4

                     436 finalDADefaultUpdateChanges:

                     437 ;50: {


                     438 

00000120 e12fff1e*   439 	ret	

                     440 	.endf	finalDADefaultUpdateChanges

                     441 	.align	4

                     442 

                     443 ;item	none	param

                     444 

                     445 	.section ".bss","awb"

                     446 .L590:

                     447 	.data

                     448 	.text

                     449 	.align	4

                     450 	.align	4

                     451 finalDADefaultEncodeRead:

                     452 ;55: {


                     453 

                     454 ;56:     ERROR_REPORT("finalDADefaultEncodeRead called"); 


                     455 ;57:     return false;


                     456 

00000124 e3a00000    457 	mov	r0,0

00000128 e12fff1e*   458 	ret	

                     459 	.endf	finalDADefaultEncodeRead

                     460 	.align	4

                     461 

                     462 ;item	none	param

                     463 ;outBuf	none	param

                     464 

                     465 	.section ".bss","awb"

                     466 .L622:

                     467 	.data

                     468 	.text

                     469 	.align	4

                     470 	.align	4

                     471 finalDADefaultCalcReadLen:

                     472 ;61: {


                     473 

                     474 ;62:     ERROR_REPORT("finalDADefaultCalcReadLen called"); 


                     475 ;63:     return false;


                     476 

0000012c e3a00000    477 	mov	r0,0


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cp81.s
00000130 e12fff1e*   478 	ret	

                     479 	.endf	finalDADefaultCalcReadLen

                     480 	.align	4

                     481 

                     482 ;item	none	param

                     483 ;pLen	none	param

                     484 

                     485 	.section ".bss","awb"

                     486 .L654:

                     487 	.data

                     488 	.text

                     489 	.align	4

                     490 	.align	4

                     491 finalDADefaultOverwriteOld:

                     492 ;67: {


                     493 

00000134 e12fff1e*   494 	ret	

                     495 	.endf	finalDADefaultOverwriteOld

                     496 	.align	4

                     497 

                     498 ;item	none	param

                     499 

                     500 	.section ".bss","awb"

                     501 .L686:

                     502 	.data

                     503 	.text

                     504 	.align	4

                     505 .L446:

00000138 00000000*   506 	.data.w	defaultBehavior

                     507 	.type	.L446,$object

                     508 	.size	.L446,4

                     509 

                     510 .L447:

0000013c 00000000*   511 	.data.w	defaultFinalDABehavior

                     512 	.type	.L447,$object

                     513 	.size	.L447,4

                     514 

                     515 	.align	4

                     516 

                     517 	.data

                     518 .L708:

                     519 	.globl	defaultBehavior

00000000 00000000*   520 defaultBehavior:	.data.w	dafaultUpdateChanges

00000004 00000000*   521 	.data.w	defaultEncodeRead

00000008 00000000*   522 	.data.w	defaultCalcReadLen

0000000c 00000000*   523 	.data.w	defaultOverwriteOld

                     524 	.type	defaultBehavior,$object

                     525 	.size	defaultBehavior,16

                     526 .L709:

                     527 	.globl	defaultFinalDABehavior

00000010 00000000*   528 defaultFinalDABehavior:	.data.w	finalDADefaultUpdateChanges

00000014 00000000*   529 	.data.w	finalDADefaultEncodeRead

00000018 00000000*   530 	.data.w	finalDADefaultCalcReadLen

0000001c 00000000*   531 	.data.w	finalDADefaultOverwriteOld

                     532 	.type	defaultFinalDABehavior,$object

                     533 	.size	defaultFinalDABehavior,16

                     534 	.ghsnote version,6

                     535 	.ghsnote tools,3

                     536 	.ghsnote options,0

                     537 	.text

                     538 	.align	4


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cp81.s
                     539 	.data

                     540 	.align	4

                     541 	.text

