                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ajs1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=RptItem.c -o reportItems\gh_ajs1.o -list=reportItems/RptItem.lst C:\Users\<USER>\AppData\Local\Temp\gh_ajs1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_ajs1.s
Source File: RptItem.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      10 ;		reportItems/RptItem.c -o reportItems/RptItem.o

                      11 ;Source File:   reportItems/RptItem.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Oct 07 09:39:22 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "RptItem.h"


                      21 ;2: 


                      22 ;3: 


                      23 ;4: #include "RptDataSet.h"


                      24 ;5: 


                      25 ;6: #include "RptBool.h"


                      26 ;7: 


                      27 ;8: #include "../MemoryManager.h"


                      28 ;9: 


                      29 ;10: 


                      30 ;11: #include "IEDCompile/InnerAttributeTypes.h"


                      31 ;12: #include <debug.h>


                      32 ;13: 


                      33 ;14: #include <string.h>


                      34 ;15: 


                      35 ;16: //==================== Загрушки функций=====================


                      36 ;17: 


                      37 ;18: static void defaultInitValue(RptItem item)


                      38 

                      39 ;20:     ERROR_REPORT("defaultInitValue called"); 


                      40 ;21: }


                      41 

                      42 ;22: 


                      43 ;23: static void dafaultUpdateChanges(RptItem item)


                      44 

                      45 ;25:     ERROR_REPORT("dafaultUpdateChanges called");    


                      46 ;26: }


                      47 

                      48 ;27: 


                      49 ;28: static bool defaultEncodeRead(RptItem item, BufferView* outBuf)


                      50 


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ajs1.s
                      51 ;32: }


                      52 

                      53 ;33: 


                      54 ;34: static bool defaultCalcReadLen(RptItem item, size_t* pLen )


                      55 

                      56 ;38: }


                      57 

                      58 ;39: 


                      59 ;40: static void defaultOverwriteOld(RptItem item)


                      60 

                      61 ;42:     ERROR_REPORT("defaultOverwriteOld called"); 


                      62 ;43: }


                      63 

                      64 ;44: 


                      65 ;45: struct RptItemBehavior defaultBehavior = {


                      66 ;46:     defaultInitValue,


                      67 ;47:     dafaultUpdateChanges,


                      68 ;48:     defaultEncodeRead,


                      69 ;49:     defaultCalcReadLen,


                      70 ;50:     defaultOverwriteOld


                      71 ;51: };


                      72 ;52: 


                      73 ;53: //================= Загрушки функций для FinalDA =================


                      74 ;54: 


                      75 ;55: static void finalDAInitValue(RptItem item)


                      76 ;56: {


                      77 ;57:     ERROR_REPORT("finalDAInitValue called"); 


                      78 ;58: }


                      79 ;59: 


                      80 ;60: static void finalDADefaultUpdateChanges(RptItem item)


                      81 

                      82 ;62:     ERROR_REPORT("finalDADefaultUpdateChanges called"); 


                      83 ;63: }


                      84 

                      85 ;64: 


                      86 ;65: static bool finalDADefaultEncodeRead(RptItem item, BufferView* outBuf)


                      87 

                      88 ;69: }


                      89 

                      90 ;70: 


                      91 ;71: static bool finalDADefaultCalcReadLen(RptItem item, size_t* pLen )


                      92 

                      93 ;75: }


                      94 

                      95 ;76: 


                      96 ;77: static void finalDADefaultOverwriteOld(RptItem item)


                      97 

                      98 ;79:     ERROR_REPORT("finalDADefaultOverwriteOld called"); 


                      99 ;80: }


                     100 

                     101 ;81: 


                     102 ;82: struct RptItemBehavior defaultFinalDABehavior = {


                     103 ;83:     finalDADefaultUpdateChanges,


                     104 ;84:     finalDADefaultEncodeRead,


                     105 ;85:     finalDADefaultCalcReadLen,


                     106 ;86:     finalDADefaultOverwriteOld


                     107 ;87: };


                     108 ;88: 


                     109 ;89: 


                     110 ;90: void *RptItem_alloc(size_t size)


                     111 	.text


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ajs1.s
                     112 	.align	4

                     113 RptItem_alloc::

00000000 e92d4030    114 	stmfd	[sp]!,{r4-r5,lr}

                     115 ;91: {


                     116 

                     117 ;92:     void* p = MM_alloc(size);


                     118 

00000004 e1a05000    119 	mov	r5,r0

00000008 eb000000*   120 	bl	MM_alloc

0000000c e1b04000    121 	movs	r4,r0

                     122 ;93:     if(p != NULL)


                     123 

                     124 ;94:     {


                     125 

                     126 ;95:         memset(p, 0, size);


                     127 

00000010 11a02005    128 	movne	r2,r5

00000014 13a01000    129 	movne	r1,0

00000018 1b000000*   130 	blne	memset

                     131 ;96:     }


                     132 ;97:     return p;


                     133 

0000001c e1a00004    134 	mov	r0,r4

00000020 e8bd8030    135 	ldmfd	[sp]!,{r4-r5,pc}

                     136 	.endf	RptItem_alloc

                     137 	.align	4

                     138 ;p	r4	local

                     139 

                     140 ;size	r5	param

                     141 

                     142 	.section ".bss","awb"

                     143 .L189:

                     144 	.data

                     145 	.text

                     146 

                     147 ;98: }


                     148 

                     149 ;99: 


                     150 ;100: 


                     151 ;101: bool initFinalDARptItem(RptItem item)


                     152 	.align	4

                     153 	.align	4

                     154 initFinalDARptItem::

00000024 e92d4000    155 	stmfd	[sp]!,{lr}

                     156 ;102: {


                     157 

                     158 ;103:     IEDEntity iedObj = item->iedObj;    


                     159 

00000028 e5901008    160 	ldr	r1,[r0,8]

                     161 ;104:     if(iedObj->type == IED_ENTITY_DA_TERMINAL_ITEM)


                     162 

0000002c e5912050    163 	ldr	r2,[r1,80]

00000030 e3520008    164 	cmp	r2,8

                     165 ;119:         }


                     166 ;120:     }


                     167 ;121:     


                     168 ;122:     return true;


                     169 

00000034 13a00001    170 	movne	r0,1

00000038 1a000005    171 	bne	.L202

                     172 ;105:     {



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ajs1.s
                     173 

                     174 ;106:         if(iedObj->trgOps == TRGOP_NONE)


                     175 

                     176 ;107:         {


                     177 

                     178 ;108:             TRACE("FinalDA without trgOps in report");            


                     179 ;109:         }


                     180 ;110: 


                     181 ;111:         switch(iedObj->subType)


                     182 

0000003c e5911054    183 	ldr	r1,[r1,84]

00000040 e3510006    184 	cmp	r1,6

                     185 ;115:             break;


                     186 ;116:         default:


                     187 ;117:             ERROR_REPORT("Unsupported FinalDA type");


                     188 ;118:             return false;


                     189 

00000044 13a00000    190 	movne	r0,0

00000048 1a000001    191 	bne	.L202

                     192 ;112:         {


                     193 ;113:         case INNER_TYPE_BOOLEAN:


                     194 ;114:             RptBool_init(item);


                     195 

0000004c eb000000*   196 	bl	RptBool_init

                     197 ;119:         }


                     198 ;120:     }


                     199 ;121:     


                     200 ;122:     return true;


                     201 

00000050 e3a00001    202 	mov	r0,1

                     203 .L202:

00000054 e8bd8000    204 	ldmfd	[sp]!,{pc}

                     205 	.endf	initFinalDARptItem

                     206 	.align	4

                     207 ;iedObj	r1	local

                     208 

                     209 ;item	r0	param

                     210 

                     211 	.section ".bss","awb"

                     212 .L272:

                     213 	.data

                     214 	.text

                     215 

                     216 ;123: }


                     217 

                     218 ;124: 


                     219 ;125: RptItem RptItem_create(IEDEntity iedObj, RptDataSetItem rptDsItem)


                     220 	.align	4

                     221 	.align	4

                     222 	.align	4

                     223 RptItem_create::

00000058 e92d4070    224 	stmfd	[sp]!,{r4-r6,lr}

                     225 ;126: {


                     226 

                     227 ;127:     RptItem item = RptItem_alloc(sizeof(struct RptItemStruct));


                     228 

0000005c e1a06001    229 	mov	r6,r1

00000060 e1a05000    230 	mov	r5,r0

00000064 e3a0002c    231 	mov	r0,44

00000068 ebffffe4*   232 	bl	RptItem_alloc

0000006c e1b04000    233 	movs	r4,r0


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ajs1.s
                     234 ;128:     if(item == NULL)


                     235 

00000070 0a00000a    236 	beq	.L299

                     237 ;129:     {


                     238 

                     239 ;130:         return NULL;


                     240 

                     241 ;131:     }


                     242 ;132: 


                     243 ;133:     item->behaviour = &defaultBehavior;


                     244 

00000074 e59f00c0*   245 	ldr	r0,.L462

00000078 e5840004    246 	str	r0,[r4,4]

                     247 ;134: 


                     248 ;135:     if(IEDEntity_isFinalDA(iedObj))


                     249 

0000007c e1a00005    250 	mov	r0,r5

00000080 eb000000*   251 	bl	IEDEntity_isFinalDA

00000084 e3500000    252 	cmp	r0,0

00000088 0a00000b    253 	beq	.L296

                     254 ;136:     {        


                     255 

                     256 ;137:         item->behaviour = &defaultFinalDABehavior;


                     257 

0000008c e59f00ac*   258 	ldr	r0,.L463

00000090 e9840061    259 	stmfa	[r4],{r0,r5-r6}

                     260 ;138:         item->iedObj = iedObj;


                     261 

                     262 ;139:         item->rptDsItem = rptDsItem;


                     263 

                     264 ;140:         if(!initFinalDARptItem(item))


                     265 

00000094 e1a00004    266 	mov	r0,r4

00000098 ebffffe1*   267 	bl	initFinalDARptItem

0000009c e3500000    268 	cmp	r0,0

                     269 .L299:

                     270 ;141:         {


                     271 

                     272 ;142:             return NULL;


                     273 

000000a0 03a00000    274 	moveq	r0,0

000000a4 0a000016    275 	beq	.L291

                     276 .L298:

                     277 ;143:         }


                     278 ;144:         RptDataSetItem_addFinalDARptItem(rptDsItem, item);


                     279 

000000a8 e1a01004    280 	mov	r1,r4

000000ac e1a00006    281 	mov	r0,r6

000000b0 eb000000*   282 	bl	RptDataSetItem_addFinalDARptItem

                     283 ;168:         }


                     284 ;169:     }


                     285 ;170: 


                     286 ;171:     return item;    


                     287 

000000b4 e1a00004    288 	mov	r0,r4

000000b8 ea000011    289 	b	.L291

                     290 .L296:

                     291 ;145:     }


                     292 ;146:     else


                     293 ;147:     {


                     294 


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ajs1.s
                     295 ;148:         // Это составной элемент


                     296 ;149:         // Рекурсивно создаём детей


                     297 ;150:         IEDEntity child = iedObj->firstChild;


                     298 

000000bc e5955004    299 	ldr	r5,[r5,4]

                     300 ;151:         while(child != NULL)


                     301 

000000c0 e3550000    302 	cmp	r5,0

000000c4 0a00000d    303 	beq	.L301

                     304 .L304:

                     305 ;152:         {


                     306 

                     307 ;153:             RptItem childItem = RptItem_create(child, rptDsItem);


                     308 

000000c8 e1a01006    309 	mov	r1,r6

000000cc e1a00005    310 	mov	r0,r5

000000d0 ebffffe0*   311 	bl	RptItem_create

                     312 ;154:             if(childItem == NULL)


                     313 

000000d4 e3500000    314 	cmp	r0,0

000000d8 0afffff0    315 	beq	.L299

                     316 ;155:             {


                     317 

                     318 ;156:                 return NULL;


                     319 

                     320 ;157:             }


                     321 ;158:             if(item->firstChild == NULL)


                     322 

000000dc e5941024    323 	ldr	r1,[r4,36]

000000e0 e595500c    324 	ldr	r5,[r5,12]

000000e4 e3510000    325 	cmp	r1,0

                     326 ;159:             {


                     327 

                     328 ;160:                 item->firstChild = childItem;


                     329 

000000e8 15941028    330 	ldrne	r1,[r4,40]

000000ec 05840024    331 	streq	r0,[r4,36]

                     332 ;165:             }


                     333 ;166:             item->lastChild = childItem;


                     334 

                     335 ;167:             child = child->next;


                     336 

                     337 ;161:             }


                     338 ;162:             else


                     339 ;163:             {


                     340 

                     341 ;164:                 item->lastChild->nextSibling = childItem;


                     342 

000000f0 15810000    343 	strne	r0,[r1]

                     344 ;165:             }


                     345 ;166:             item->lastChild = childItem;


                     346 

000000f4 e5840028    347 	str	r0,[r4,40]

                     348 ;167:             child = child->next;


                     349 

000000f8 e3550000    350 	cmp	r5,0

000000fc 1afffff1    351 	bne	.L304

                     352 .L301:

                     353 ;168:         }


                     354 ;169:     }


                     355 ;170: 



                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ajs1.s
                     356 ;171:     return item;    


                     357 

00000100 e1a00004    358 	mov	r0,r4

                     359 .L291:

00000104 e8bd8070    360 	ldmfd	[sp]!,{r4-r6,pc}

                     361 	.endf	RptItem_create

                     362 	.align	4

                     363 ;item	r4	local

                     364 ;child	r5	local

                     365 ;childItem	r0	local

                     366 

                     367 ;iedObj	r5	param

                     368 ;rptDsItem	r6	param

                     369 

                     370 	.data

                     371 .L424:

                     372 	.text

                     373 

                     374 ;172: }


                     375 	.align	4

                     376 	.align	4

                     377 defaultInitValue:

                     378 ;19: {


                     379 

00000108 e12fff1e*   380 	ret	

                     381 	.endf	defaultInitValue

                     382 	.align	4

                     383 

                     384 ;item	none	param

                     385 

                     386 	.section ".bss","awb"

                     387 .L478:

                     388 	.data

                     389 	.text

                     390 	.align	4

                     391 	.align	4

                     392 dafaultUpdateChanges:

                     393 ;24: {


                     394 

0000010c e12fff1e*   395 	ret	

                     396 	.endf	dafaultUpdateChanges

                     397 	.align	4

                     398 

                     399 ;item	none	param

                     400 

                     401 	.section ".bss","awb"

                     402 .L510:

                     403 	.data

                     404 	.text

                     405 	.align	4

                     406 	.align	4

                     407 defaultEncodeRead:

                     408 ;29: {


                     409 

                     410 ;30:     ERROR_REPORT("defaultEncodeRead called"); 


                     411 ;31:     return false;


                     412 

00000110 e3a00000    413 	mov	r0,0

00000114 e12fff1e*   414 	ret	

                     415 	.endf	defaultEncodeRead

                     416 	.align	4


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ajs1.s
                     417 

                     418 ;item	none	param

                     419 ;outBuf	none	param

                     420 

                     421 	.section ".bss","awb"

                     422 .L542:

                     423 	.data

                     424 	.text

                     425 	.align	4

                     426 	.align	4

                     427 defaultCalcReadLen:

                     428 ;35: {


                     429 

                     430 ;36:     ERROR_REPORT("defaultCalcReadLen called"); 


                     431 ;37:     return false;


                     432 

00000118 e3a00000    433 	mov	r0,0

0000011c e12fff1e*   434 	ret	

                     435 	.endf	defaultCalcReadLen

                     436 	.align	4

                     437 

                     438 ;item	none	param

                     439 ;pLen	none	param

                     440 

                     441 	.section ".bss","awb"

                     442 .L574:

                     443 	.data

                     444 	.text

                     445 	.align	4

                     446 	.align	4

                     447 defaultOverwriteOld:

                     448 ;41: {


                     449 

00000120 e12fff1e*   450 	ret	

                     451 	.endf	defaultOverwriteOld

                     452 	.align	4

                     453 

                     454 ;item	none	param

                     455 

                     456 	.section ".bss","awb"

                     457 .L606:

                     458 	.data

                     459 	.text

                     460 	.align	4

                     461 	.align	4

                     462 finalDADefaultUpdateChanges:

                     463 ;61: {


                     464 

00000124 e12fff1e*   465 	ret	

                     466 	.endf	finalDADefaultUpdateChanges

                     467 	.align	4

                     468 

                     469 ;item	none	param

                     470 

                     471 	.section ".bss","awb"

                     472 .L638:

                     473 	.data

                     474 	.text

                     475 	.align	4

                     476 	.align	4

                     477 finalDADefaultEncodeRead:


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ajs1.s
                     478 ;66: {


                     479 

                     480 ;67:     ERROR_REPORT("finalDADefaultEncodeRead called"); 


                     481 ;68:     return false;


                     482 

00000128 e3a00000    483 	mov	r0,0

0000012c e12fff1e*   484 	ret	

                     485 	.endf	finalDADefaultEncodeRead

                     486 	.align	4

                     487 

                     488 ;item	none	param

                     489 ;outBuf	none	param

                     490 

                     491 	.section ".bss","awb"

                     492 .L670:

                     493 	.data

                     494 	.text

                     495 	.align	4

                     496 	.align	4

                     497 finalDADefaultCalcReadLen:

                     498 ;72: {


                     499 

                     500 ;73:     ERROR_REPORT("finalDADefaultCalcReadLen called"); 


                     501 ;74:     return false;


                     502 

00000130 e3a00000    503 	mov	r0,0

00000134 e12fff1e*   504 	ret	

                     505 	.endf	finalDADefaultCalcReadLen

                     506 	.align	4

                     507 

                     508 ;item	none	param

                     509 ;pLen	none	param

                     510 

                     511 	.section ".bss","awb"

                     512 .L702:

                     513 	.data

                     514 	.text

                     515 	.align	4

                     516 	.align	4

                     517 finalDADefaultOverwriteOld:

                     518 ;78: {


                     519 

00000138 e12fff1e*   520 	ret	

                     521 	.endf	finalDADefaultOverwriteOld

                     522 	.align	4

                     523 

                     524 ;item	none	param

                     525 

                     526 	.section ".bss","awb"

                     527 .L734:

                     528 	.data

                     529 	.text

                     530 	.align	4

                     531 .L462:

0000013c 00000000*   532 	.data.w	defaultBehavior

                     533 	.type	.L462,$object

                     534 	.size	.L462,4

                     535 

                     536 .L463:

00000140 00000000*   537 	.data.w	defaultFinalDABehavior

                     538 	.type	.L463,$object


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ajs1.s
                     539 	.size	.L463,4

                     540 

                     541 	.align	4

                     542 

                     543 	.data

                     544 .L756:

                     545 	.globl	defaultBehavior

00000000 00000000*   546 defaultBehavior:	.data.w	defaultInitValue

00000004 00000000*   547 	.data.w	dafaultUpdateChanges

00000008 00000000*   548 	.data.w	defaultEncodeRead

0000000c 00000000*   549 	.data.w	defaultCalcReadLen

00000010 00000000*   550 	.data.w	defaultOverwriteOld

                     551 	.type	defaultBehavior,$object

                     552 	.size	defaultBehavior,20

                     553 .L757:

                     554 	.globl	defaultFinalDABehavior

00000014 00000000*   555 defaultFinalDABehavior:	.data.w	finalDADefaultUpdateChanges

00000018 00000000*   556 	.data.w	finalDADefaultEncodeRead

0000001c 00000000*   557 	.data.w	finalDADefaultCalcReadLen

00000020 00000000*   558 	.data.w	finalDADefaultOverwriteOld

00000024 00000000    559 	.space	4

                     560 	.type	defaultFinalDABehavior,$object

                     561 	.size	defaultFinalDABehavior,20

                     562 	.ghsnote version,6

                     563 	.ghsnote tools,3

                     564 	.ghsnote options,0

                     565 	.text

                     566 	.align	4

                     567 	.data

                     568 	.align	4

                     569 	.text

