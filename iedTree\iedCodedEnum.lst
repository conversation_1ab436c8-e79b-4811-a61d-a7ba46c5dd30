                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a7k1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=iedCodedEnum.c -o iedTree\gh_a7k1.o -list=iedTree/iedCodedEnum.lst C:\Users\<USER>\AppData\Local\Temp\gh_a7k1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_a7k1.s
Source File: iedCodedEnum.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      10 ;		iedTree/iedCodedEnum.c -o iedTree/iedCodedEnum.o

                      11 ;Source File:   iedTree/iedCodedEnum.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Oct 07 09:39:21 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "iedCodedEnum.h"


                      21 ;2: 


                      22 ;3: 


                      23 ;4: #include "debug.h"


                      24 ;5: #include "../DataSlice.h"


                      25 ;6: #include "iedFinalDA.h"


                      26 ;7: #include "iedTree.h"


                      27 ;8: #include "../AsnEncoding.h"


                      28 ;9: 


                      29 ;10: #define CODEDENUM_ENCODED_SIZE 4


                      30 ;11: 


                      31 ;12: static void updateFromDataSlice(IEDEntity entity)


                      32 	.text

                      33 	.align	4

                      34 updateFromDataSlice:

00000000 e92d48f3     35 	stmfd	[sp]!,{r0-r1,r4-r7,fp,lr}

                      36 ;13: {	


                      37 

00000004 e59d0000     38 	ldr	r0,[sp]

00000008 e3a04000     39 	mov	r4,0

                      40 ;14: 	TerminalItem* termItem = entity->extInfo;	


                      41 

0000000c e5901058     42 	ldr	r1,[r0,88]

                      43 ;15: 	size_t bitCount = termItem->ce.bitCount;


                      44 

00000010 e1a07004     45 	mov	r7,r4

00000014 e5912004     46 	ldr	r2,[r1,4]

                      47 ;16: 	int* dsOffsets = termItem->ce.dsOffsets;


                      48 

00000018 e281b008     49 	add	fp,r1,8

                      50 ;17: 	uint8_t value = 0;



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a7k1.s
                      51 

                      52 ;18: 


                      53 ;19: 	size_t i;


                      54 ;20: 	for(i = 0; i < bitCount; ++i)


                      55 

0000001c e3520000     56 	cmp	r2,0

00000020 a1a00002     57 	movge	r0,r2

00000024 b3a00000     58 	movlt	r0,0

00000028 e58d0004     59 	str	r0,[sp,4]

0000002c e1b061a0     60 	movs	r6,r0 lsr 3

00000030 0a00001e     61 	beq	.L34

00000034 e281500c     62 	add	r5,r1,12

                      63 .L50:

00000038 e1a01107     64 	mov	r1,r7 lsl 2

0000003c e19100bb     65 	ldrh	r0,[r1,fp]

00000040 eb000000*    66 	bl	DataSlice_getBoolFastCurrDS

00000044 e1804084     67 	orr	r4,r0,r4 lsl 1

00000048 e0d502b0     68 	ldrh	r0,[r5],32

0000004c eb000000*    69 	bl	DataSlice_getBoolFastCurrDS

00000050 e1804084     70 	orr	r4,r0,r4 lsl 1

00000054 e15501bc     71 	ldrh	r0,[r5,-28]

00000058 eb000000*    72 	bl	DataSlice_getBoolFastCurrDS

0000005c e1804084     73 	orr	r4,r0,r4 lsl 1

00000060 e15501b8     74 	ldrh	r0,[r5,-24]

00000064 eb000000*    75 	bl	DataSlice_getBoolFastCurrDS

00000068 e1804084     76 	orr	r4,r0,r4 lsl 1

0000006c e15501b4     77 	ldrh	r0,[r5,-20]

00000070 eb000000*    78 	bl	DataSlice_getBoolFastCurrDS

00000074 e1804084     79 	orr	r4,r0,r4 lsl 1

00000078 e15501b0     80 	ldrh	r0,[r5,-16]

0000007c eb000000*    81 	bl	DataSlice_getBoolFastCurrDS

00000080 e1804084     82 	orr	r4,r0,r4 lsl 1

00000084 e15500bc     83 	ldrh	r0,[r5,-12]

00000088 e2877008     84 	add	r7,r7,8

0000008c eb000000*    85 	bl	DataSlice_getBoolFastCurrDS

00000090 e1804084     86 	orr	r4,r0,r4 lsl 1

00000094 e1a04084     87 	mov	r4,r4 lsl 1

00000098 e15500b8     88 	ldrh	r0,[r5,-8]

0000009c e20440ff     89 	and	r4,r4,255

000000a0 eb000000*    90 	bl	DataSlice_getBoolFastCurrDS

000000a4 e1844000     91 	orr	r4,r4,r0

000000a8 e2566001     92 	subs	r6,r6,1

000000ac 1affffe1     93 	bne	.L50

                      94 .L34:

000000b0 e59d0004     95 	ldr	r0,[sp,4]

000000b4 e2106007     96 	ands	r6,r0,7

000000b8 0a000008     97 	beq	.L4

                      98 .L54:

000000bc e1a04084     99 	mov	r4,r4 lsl 1

000000c0 e1a01107    100 	mov	r1,r7 lsl 2

000000c4 e19100bb    101 	ldrh	r0,[r1,fp]

000000c8 e20440ff    102 	and	r4,r4,255

000000cc eb000000*   103 	bl	DataSlice_getBoolFastCurrDS

000000d0 e1844000    104 	orr	r4,r4,r0

000000d4 e2877001    105 	add	r7,r7,1

000000d8 e2566001    106 	subs	r6,r6,1

000000dc 1afffff6    107 	bne	.L54

                     108 .L4:

                     109 ;24: 	}


                     110 ;25: 


                     111 ;26: 	if(entity->codedEnumValue == value)



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a7k1.s
                     112 

000000e0 e59d0000    113 	ldr	r0,[sp]

000000e4 e5d01030    114 	ldrb	r1,[r0,48]

000000e8 e1510004    115 	cmp	r1,r4

                     116 ;27: 	{


                     117 

                     118 ;28: 		entity->changed = TRGOP_NONE;


                     119 

000000ec 03a01000    120 	moveq	r1,0

000000f0 05801028    121 	streq	r1,[r0,40]

000000f4 0a000007    122 	beq	.L2

                     123 ;29: 	}


                     124 ;30: 	else


                     125 ;31: 	{


                     126 

                     127 ;32: 		entity->changed = entity->trgOps;


                     128 

000000f8 e5901024    129 	ldr	r1,[r0,36]

000000fc e5c04030    130 	strb	r4,[r0,48]

                     131 ;34: 		IEDEntity_setTimeStamp(entity, dataSliceGetTimeStamp());


                     132 

00000100 e5801028    133 	str	r1,[r0,40]

                     134 ;33: 		entity->codedEnumValue = value;


                     135 

00000104 eb000000*   136 	bl	dataSliceGetTimeStamp

00000108 e1a02001    137 	mov	r2,r1

0000010c e1a01000    138 	mov	r1,r0

00000110 e59d0000    139 	ldr	r0,[sp]

00000114 eb000000*   140 	bl	IEDEntity_setTimeStamp

                     141 .L2:

00000118 e8bd48f3    142 	ldmfd	[sp]!,{r0-r1,r4-r7,fp,lr}

0000011c e12fff1e*   143 	ret	

                     144 	.endf	updateFromDataSlice

                     145 	.align	4

                     146 ;termItem	r1	local

                     147 ;bitCount	r2	local

                     148 ;dsOffsets	fp	local

                     149 ;value	r4	local

                     150 ;i	r7	local

                     151 

                     152 ;entity	[sp]	param

                     153 

                     154 	.section ".bss","awb"

                     155 .L247:

                     156 	.data

                     157 	.text

                     158 

                     159 ;35: 	}


                     160 ;36: }


                     161 

                     162 ;37: 


                     163 ;38: static bool calcReadLen(IEDEntity entity, size_t* pLen )


                     164 	.align	4

                     165 	.align	4

                     166 calcReadLen:

                     167 ;39: {	


                     168 

                     169 ;40: 	*pLen = CODEDENUM_ENCODED_SIZE;


                     170 

00000120 e3a00004    171 	mov	r0,4

00000124 e5810000    172 	str	r0,[r1]


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a7k1.s
                     173 ;41: 	return true;


                     174 

00000128 e3a00001    175 	mov	r0,1

0000012c e12fff1e*   176 	ret	

                     177 	.endf	calcReadLen

                     178 	.align	4

                     179 

                     180 ;entity	none	param

                     181 ;pLen	r1	param

                     182 

                     183 	.section ".bss","awb"

                     184 .L302:

                     185 	.data

                     186 	.text

                     187 

                     188 ;42: }


                     189 

                     190 ;43: 


                     191 ;44: static bool encodeRead(IEDEntity entity, BufferView* outBuf)


                     192 	.align	4

                     193 	.align	4

                     194 encodeRead:

00000130 e92d4070    195 	stmfd	[sp]!,{r4-r6,lr}

00000134 e24dd008    196 	sub	sp,sp,8

00000138 e1a05000    197 	mov	r5,r0

                     198 ;45: {


                     199 

                     200 ;46: 	TerminalItem* termItem = entity->extInfo;


                     201 

0000013c e5950058    202 	ldr	r0,[r5,88]

                     203 ;47: 	size_t bitCount = termItem->ce.bitCount;


                     204 

00000140 e28d2004    205 	add	r2,sp,4

00000144 e5906004    206 	ldr	r6,[r0,4]

00000148 e1a04001    207 	mov	r4,r1

0000014c e1a00004    208 	mov	r0,r4

00000150 e3a01004    209 	mov	r1,4

00000154 eb000000*   210 	bl	BufferView_alloc

                     211 ;48: 


                     212 ;49: 	uint8_t* encodeBuf;


                     213 ;50: 


                     214 ;51: 	if(!BufferView_alloc(outBuf,CODEDENUM_ENCODED_SIZE, &encodeBuf))


                     215 

00000158 e3500000    216 	cmp	r0,0

                     217 ;52: 	{


                     218 

                     219 ;53: 		ERROR_REPORT("Unable to allocate buffer");


                     220 ;54: 		return false;


                     221 

0000015c 0a00000a    222 	beq	.L309

                     223 ;55: 	}


                     224 ;56: 


                     225 ;57: 	// Возвращаемое значение не нужно,


                     226 ;58: 	// потому что функция не возвращает ошибки, а размер известен заранее


                     227 ;59: 	BerEncoder_encodeUcharBitString(ASN_TYPEDESCRIPTION_BIT_STRING,


                     228 

00000160 e3a00000    229 	mov	r0,0

00000164 e58d0000    230 	str	r0,[sp]

00000168 e59d3004    231 	ldr	r3,[sp,4]

0000016c e5d52030    232 	ldrb	r2,[r5,48]

00000170 e1a01006    233 	mov	r1,r6


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a7k1.s
00000174 e3a00084    234 	mov	r0,132

00000178 eb000000*   235 	bl	BerEncoder_encodeUcharBitString

                     236 ;60: 									bitCount,  entity->codedEnumValue, encodeBuf, 0);


                     237 ;61: 


                     238 ;62: 	outBuf->pos += CODEDENUM_ENCODED_SIZE;


                     239 

0000017c e5940004    240 	ldr	r0,[r4,4]

00000180 e2800004    241 	add	r0,r0,4

00000184 e5840004    242 	str	r0,[r4,4]

                     243 ;63: 	return true;


                     244 

00000188 e3a00001    245 	mov	r0,1

                     246 .L309:

0000018c e28dd008    247 	add	sp,sp,8

00000190 e8bd4070    248 	ldmfd	[sp]!,{r4-r6,lr}

00000194 e12fff1e*   249 	ret	

                     250 	.endf	encodeRead

                     251 	.align	4

                     252 ;termItem	r0	local

                     253 ;bitCount	r6	local

                     254 ;encodeBuf	[sp,4]	local

                     255 

                     256 ;entity	r5	param

                     257 ;outBuf	r4	param

                     258 

                     259 	.section ".bss","awb"

                     260 .L362:

                     261 	.data

                     262 	.text

                     263 

                     264 ;64: }


                     265 

                     266 ;65: 


                     267 ;66: void IEDCodedEnum_init(IEDEntity entity)


                     268 	.align	4

                     269 	.align	4

                     270 IEDCodedEnum_init::

00000198 e92d4cf7    271 	stmfd	[sp]!,{r0-r2,r4-r7,r10-fp,lr}

0000019c e59d0000    272 	ldr	r0,[sp]

000001a0 e5901058    273 	ldr	r1,[r0,88]

000001a4 e3a07000    274 	mov	r7,0

000001a8 e5913000    275 	ldr	r3,[r1]

000001ac e281a008    276 	add	r10,r1,8

                     277 ;87: 


                     278 ;88: 


                     279 ;89: 	for (i = 0; i < bitCount; ++i)


                     280 

000001b0 e5932004    281 	ldr	r2,[r3,4]

000001b4 e58d3008    282 	str	r3,[sp,8]

000001b8 e3520008    283 	cmp	r2,8

000001bc 83a02008    284 	movhi	r2,8

                     285 ;67: {	


                     286 

                     287 ;68: 	TerminalItem* extInfo;	


                     288 ;69: 	//accessInfo из бинарника модели


                     289 ;70: 	CodedEnumAccessInfo* accessInfo;


                     290 ;71: 	size_t bitCount;


                     291 ;72: 	int* dsOffsets;


                     292 ;73: 	size_t i;


                     293 ;74: 


                     294 ;75: 	extInfo = entity->extInfo;



                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a7k1.s
                     295 

                     296 ;76: 	accessInfo = extInfo->accessInfo;


                     297 

                     298 ;77: 


                     299 ;78: 	bitCount = accessInfo->bitCount;


                     300 

                     301 ;79: 	if(bitCount > 8)


                     302 

                     303 

                     304 

                     305 ;83: 	}


                     306 ;84: 	extInfo->ce.bitCount = bitCount;


                     307 

000001c0 e5812004    308 	str	r2,[r1,4]

                     309 ;85: 


                     310 ;86: 	dsOffsets = extInfo->ce.dsOffsets;


                     311 

000001c4 e3520000    312 	cmp	r2,0

000001c8 a1a00002    313 	movge	r0,r2

000001cc b3a00000    314 	movlt	r0,0

000001d0 e58d0004    315 	str	r0,[sp,4]

000001d4 e1b061a0    316 	movs	r6,r0 lsr 3

000001d8 0a00001c    317 	beq	.L402

000001dc e2834008    318 	add	r4,r3,8

000001e0 e281500c    319 	add	r5,r1,12

                     320 .L418:

000001e4 e4940004    321 	ldr	r0,[r4],4

000001e8 eb000000*   322 	bl	DataSlice_getBoolOffset

000001ec e78a0107    323 	str	r0,[r10,r7 lsl 2]

000001f0 e4940004    324 	ldr	r0,[r4],4

000001f4 eb000000*   325 	bl	DataSlice_getBoolOffset

000001f8 e4850020    326 	str	r0,[r5],32

000001fc e4940004    327 	ldr	r0,[r4],4

00000200 eb000000*   328 	bl	DataSlice_getBoolOffset

00000204 e505001c    329 	str	r0,[r5,-28]

00000208 e4940004    330 	ldr	r0,[r4],4

0000020c eb000000*   331 	bl	DataSlice_getBoolOffset

00000210 e5050018    332 	str	r0,[r5,-24]

00000214 e4940004    333 	ldr	r0,[r4],4

00000218 eb000000*   334 	bl	DataSlice_getBoolOffset

0000021c e5050014    335 	str	r0,[r5,-20]

00000220 e4940004    336 	ldr	r0,[r4],4

00000224 eb000000*   337 	bl	DataSlice_getBoolOffset

00000228 e5050010    338 	str	r0,[r5,-16]

0000022c e4940004    339 	ldr	r0,[r4],4

00000230 eb000000*   340 	bl	DataSlice_getBoolOffset

00000234 e505000c    341 	str	r0,[r5,-12]

00000238 e4940004    342 	ldr	r0,[r4],4

0000023c e2877008    343 	add	r7,r7,8

00000240 eb000000*   344 	bl	DataSlice_getBoolOffset

00000244 e5050008    345 	str	r0,[r5,-8]

00000248 e2566001    346 	subs	r6,r6,1

0000024c 1affffe4    347 	bne	.L418

                     348 .L402:

00000250 e59d0004    349 	ldr	r0,[sp,4]

00000254 e2106007    350 	ands	r6,r0,7

00000258 0a000008    351 	beq	.L380

0000025c e59d0008    352 	ldr	r0,[sp,8]

00000260 e2801008    353 	add	r1,r0,8

00000264 e0814107    354 	add	r4,r1,r7 lsl 2

                     355 .L422:


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a7k1.s
00000268 e4940004    356 	ldr	r0,[r4],4

0000026c eb000000*   357 	bl	DataSlice_getBoolOffset

00000270 e78a0107    358 	str	r0,[r10,r7 lsl 2]

00000274 e2877001    359 	add	r7,r7,1

00000278 e2566001    360 	subs	r6,r6,1

0000027c 1afffff9    361 	bne	.L422

                     362 .L380:

00000280 e59d0000    363 	ldr	r0,[sp]

00000284 e59f1018*   364 	ldr	r1,.L635

00000288 e59f2018*   365 	ldr	r2,.L636

                     366 ;92: 	}


                     367 ;93: 


                     368 ;94: 	entity->updateFromDataSlice = updateFromDataSlice;


                     369 

0000028c e5801068    370 	str	r1,[r0,104]

                     371 ;95: 	entity->calcReadLen = calcReadLen;


                     372 

00000290 e59f1014*   373 	ldr	r1,.L637

00000294 e5802060    374 	str	r2,[r0,96]

                     375 ;96: 	entity->encodeRead = encodeRead;


                     376 

00000298 e580105c    377 	str	r1,[r0,92]

                     378 ;97: 


                     379 ;98: 	IEDTree_addToCmpList(entity);


                     380 

0000029c eb000000*   381 	bl	IEDTree_addToCmpList

000002a0 e8bd8cf7    382 	ldmfd	[sp]!,{r0-r2,r4-r7,r10-fp,pc}

                     383 	.endf	IEDCodedEnum_init

                     384 	.align	4

                     385 ;extInfo	r1	local

                     386 ;accessInfo	[sp,8]	local

                     387 ;bitCount	r2	local

                     388 ;dsOffsets	r10	local

                     389 ;i	r7	local

                     390 

                     391 ;entity	[sp]	param

                     392 

                     393 	.section ".bss","awb"

                     394 .L610:

                     395 	.data

                     396 	.text

                     397 

                     398 ;99: }


                     399 	.align	4

                     400 .L635:

000002a4 00000000*   401 	.data.w	updateFromDataSlice

                     402 	.type	.L635,$object

                     403 	.size	.L635,4

                     404 

                     405 .L636:

000002a8 00000000*   406 	.data.w	calcReadLen

                     407 	.type	.L636,$object

                     408 	.size	.L636,4

                     409 

                     410 .L637:

000002ac 00000000*   411 	.data.w	encodeRead

                     412 	.type	.L637,$object

                     413 	.size	.L637,4

                     414 

                     415 	.align	4

                     416 


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a7k1.s
                     417 	.data

                     418 	.ghsnote version,6

                     419 	.ghsnote tools,3

                     420 	.ghsnote options,0

                     421 	.text

                     422 	.align	4

