                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_13c1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=iedTree.c -o iedTree\gh_13c1.o -list=iedTree/iedTree.lst C:\Users\<USER>\AppData\Local\Temp\gh_13c1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_13c1.s
Source File: iedTree.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      10 ;		iedTree/iedTree.c -o iedTree/iedTree.o

                      11 ;Source File:   iedTree/iedTree.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:55:08 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "iedTree.h"


                      21 ;2: 


                      22 ;3: #include "iedEntity.h"


                      23 ;4: #include "iedObjects.h"


                      24 ;5: #include "iedNoEntity.h"


                      25 ;6: 


                      26 ;7: #include <platform_critical_section.h>


                      27 ;8: 


                      28 ;9: #include <stddef.h>


                      29 ;10: #include <debug.h>


                      30 ;11: 


                      31 ;12: static IEDEntity iedTreeRoot = NULL;


                      32 ;13: 


                      33 ;14: static CriticalSection iedTreeCS;


                      34 ;15: 


                      35 ;16: // Список элементов, которые нужно проверать на изменения


                      36 ;17: // при получении нового DataSlice


                      37 ;18: // Xоть следующие переменные используются локально, здесь нельзя писать static, т.к.


                      38 ;19: // GHS генерирует неверный код в функции IEDTree_addToCmpList


                      39 ;20: IEDEntity dataSliceUpdateList = NULL;


                      40 ;21: IEDEntity* dataSliceUpdateListTail = &dataSliceUpdateList;


                      41 ;22: 


                      42 ;23: bool IEDTree_init(uint8_t *iedModel, size_t modelSize)


                      43 	.text

                      44 	.align	4

                      45 IEDTree_init::

00000000 e92d4030     46 	stmfd	[sp]!,{r4-r5,lr}

                      47 ;24: {


                      48 

                      49 ;25:     IEDEntity root;


                      50 ;26:     BufferView ber;



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_13c1.s
                      51 ;27: 


                      52 ;28: 


                      53 ;29:     CriticalSection_Init(&iedTreeCS);


                      54 

00000004 e24dd010     55 	sub	sp,sp,16

00000008 e1a04000     56 	mov	r4,r0

0000000c e59f018c*    57 	ldr	r0,.L74

00000010 e1a05001     58 	mov	r5,r1

00000014 eb000000*    59 	bl	CriticalSection_Init

                      60 ;30: 


                      61 ;31:     BufferView_init(&ber, iedModel, modelSize, 0);


                      62 

00000018 e1a02005     63 	mov	r2,r5

0000001c e1a01004     64 	mov	r1,r4

00000020 e28d0004     65 	add	r0,sp,4

00000024 e3a03000     66 	mov	r3,0

00000028 eb000000*    67 	bl	BufferView_init

                      68 ;32: 


                      69 ;33:     if(!IEDEntity_createFromBER(&root, &ber , NULL))


                      70 

0000002c e28d1004     71 	add	r1,sp,4

00000030 e1a0000d     72 	mov	r0,sp

00000034 e3a02000     73 	mov	r2,0

00000038 eb000000*    74 	bl	IEDEntity_createFromBER

0000003c e3500000     75 	cmp	r0,0

00000040 0a000004     76 	beq	.L8

                      77 ;34:     {


                      78 

                      79 ;35:         return false;


                      80 

                      81 ;36:     }


                      82 ;37:     iedTreeRoot = root;


                      83 

00000044 e59f1158*    84 	ldr	r1,.L75

00000048 e59d0000     85 	ldr	r0,[sp]

0000004c e5810000     86 	str	r0,[r1]

                      87 ;38: 


                      88 ;39:     //Дополнительная инициализация для элементов,


                      89 ;40:     //требующих готового дерева


                      90 ;41:     if(!IEDEntity_postCreate(root))


                      91 

00000050 eb000000*    92 	bl	IEDEntity_postCreate

00000054 e3500000     93 	cmp	r0,0

                      94 .L8:

                      95 ;42:     {


                      96 

                      97 ;43:         return false;


                      98 

00000058 03a00000     99 	moveq	r0,0

0000005c 0a000001    100 	beq	.L2

                     101 .L7:

                     102 ;44:     }


                     103 ;45: 


                     104 ;46:     IEDNoEntity_init();


                     105 

00000060 eb000000*   106 	bl	IEDNoEntity_init

                     107 ;47: 


                     108 ;48:     return true;


                     109 

00000064 e3a00001    110 	mov	r0,1

                     111 .L2:


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_13c1.s
00000068 e28dd010    112 	add	sp,sp,16

0000006c e8bd8030    113 	ldmfd	[sp]!,{r4-r5,pc}

                     114 	.endf	IEDTree_init

                     115 	.align	4

                     116 ;root	[sp]	local

                     117 ;ber	[sp,4]	local

                     118 

                     119 ;iedModel	r4	param

                     120 ;modelSize	r5	param

                     121 

                     122 	.section ".bss","awb"

                     123 .L60:

                     124 	.data

                     125 .L61:

00000000 00000000    126 iedTreeRoot:	.data.b	0,0,0,0

                     127 	.type	iedTreeRoot,$object

                     128 	.size	iedTreeRoot,4

                     129 	.section ".bss","awb"

00000000 00         130 iedTreeCS:	.space	1

                     131 	.data

                     132 	.text

                     133 

                     134 ;49: }


                     135 

                     136 ;50: 


                     137 ;51: void IEDTree_lock(void)


                     138 	.align	4

                     139 	.align	4

                     140 IEDTree_lock::

                     141 ;52: {


                     142 

                     143 ;53:     CriticalSection_Lock(&iedTreeCS);


                     144 

00000070 e59f0128*   145 	ldr	r0,.L74

00000074 ea000000*   146 	b	CriticalSection_Lock

                     147 	.endf	IEDTree_lock

                     148 	.align	4

                     149 

                     150 	.data

                     151 	.text

                     152 

                     153 ;54: }


                     154 

                     155 ;55: 


                     156 ;56: void IEDTree_unlock(void)


                     157 	.align	4

                     158 	.align	4

                     159 IEDTree_unlock::

                     160 ;57: {


                     161 

                     162 ;58:     CriticalSection_Unlock(&iedTreeCS);


                     163 

00000078 e59f0120*   164 	ldr	r0,.L74

0000007c ea000000*   165 	b	CriticalSection_Unlock

                     166 	.endf	IEDTree_unlock

                     167 	.align	4

                     168 

                     169 	.data

                     170 	.text

                     171 

                     172 ;59: }



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_13c1.s
                     173 

                     174 ;60: 


                     175 ;61: IEDEntity IEDTree_findDataByFullName(StringView* ldName,


                     176 	.align	4

                     177 	.align	4

                     178 IEDTree_findDataByFullName::

00000080 e92d4010    179 	stmfd	[sp]!,{r4,lr}

                     180 ;62:                                      StringView* dataName)


                     181 ;63: {


                     182 

                     183 ;64:     IEDEntity dataSection;


                     184 ;65:     IEDEntity ld = IEDEntity_getChildByName(iedTreeRoot, ldName);


                     185 

00000084 e1a04001    186 	mov	r4,r1

00000088 e59f2114*   187 	ldr	r2,.L75

0000008c e1a01000    188 	mov	r1,r0

00000090 e5920000    189 	ldr	r0,[r2]

00000094 eb000000*   190 	bl	IEDEntity_getChildByName

                     191 ;66:     if(ld == NULL)


                     192 

00000098 e3500000    193 	cmp	r0,0

0000009c 0a000004    194 	beq	.L138

                     195 ;67:     {


                     196 

                     197 ;68:         return NULL;


                     198 

                     199 ;69:     }


                     200 ;70:     dataSection = IEDLD_getDataSection(ld);


                     201 

000000a0 eb000000*   202 	bl	IEDLD_getDataSection

                     203 ;71:     if(dataSection == NULL)


                     204 

000000a4 e3500000    205 	cmp	r0,0

                     206 ;74:     }


                     207 ;75:     return IEDEntity_getChildByFullName(dataSection, dataName);


                     208 

000000a8 11a01004    209 	movne	r1,r4

000000ac 18bd4010    210 	ldmnefd	[sp]!,{r4,lr}

000000b0 1a000000*   211 	bne	IEDEntity_getChildByFullName

                     212 .L138:

                     213 ;72:     {


                     214 

                     215 ;73:         return NULL;


                     216 

000000b4 e3a00000    217 	mov	r0,0

000000b8 e8bd8010    218 	ldmfd	[sp]!,{r4,pc}

                     219 	.endf	IEDTree_findDataByFullName

                     220 	.align	4

                     221 ;dataSection	r2	local

                     222 ;ld	r2	local

                     223 

                     224 ;ldName	r0	param

                     225 ;dataName	r4	param

                     226 

                     227 	.section ".bss","awb"

                     228 .L188:

                     229 	.data

                     230 	.text

                     231 

                     232 ;76: }


                     233 


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_13c1.s
                     234 ;77: 


                     235 ;78: IEDEntity IEDTree_findDataSetByFullName(StringView* ldName,


                     236 	.align	4

                     237 	.align	4

                     238 IEDTree_findDataSetByFullName::

000000bc e92d4010    239 	stmfd	[sp]!,{r4,lr}

                     240 ;79:                                      StringView* dataSetName)


                     241 ;80: {


                     242 

                     243 ;81:     IEDEntity dataSetSection;


                     244 ;82:     IEDEntity ld = IEDEntity_getChildByName(iedTreeRoot, ldName);


                     245 

000000c0 e1a04001    246 	mov	r4,r1

000000c4 e59f20d8*   247 	ldr	r2,.L75

000000c8 e1a01000    248 	mov	r1,r0

000000cc e5920000    249 	ldr	r0,[r2]

000000d0 eb000000*   250 	bl	IEDEntity_getChildByName

                     251 ;83:     if(ld == NULL)


                     252 

000000d4 e3500000    253 	cmp	r0,0

000000d8 0a000004    254 	beq	.L211

                     255 ;84:     {


                     256 

                     257 ;85:         return NULL;


                     258 

                     259 ;86:     }


                     260 ;87:     dataSetSection = IEDLD_getDataSetSection(ld);


                     261 

000000dc eb000000*   262 	bl	IEDLD_getDataSetSection

                     263 ;88:     if(dataSetSection == NULL)


                     264 

000000e0 e3500000    265 	cmp	r0,0

                     266 ;91:     }


                     267 ;92:     return IEDEntity_getChildByFullName(dataSetSection, dataSetName);


                     268 

000000e4 11a01004    269 	movne	r1,r4

000000e8 18bd4010    270 	ldmnefd	[sp]!,{r4,lr}

000000ec 1a000000*   271 	bne	IEDEntity_getChildByFullName

                     272 .L211:

                     273 ;89:     {


                     274 

                     275 ;90:         return NULL;


                     276 

000000f0 e3a00000    277 	mov	r0,0

000000f4 e8bd8010    278 	ldmfd	[sp]!,{r4,pc}

                     279 	.endf	IEDTree_findDataSetByFullName

                     280 	.align	4

                     281 ;dataSetSection	r2	local

                     282 ;ld	r2	local

                     283 

                     284 ;ldName	r0	param

                     285 ;dataSetName	r4	param

                     286 

                     287 	.section ".bss","awb"

                     288 .L268:

                     289 	.data

                     290 	.text

                     291 

                     292 ;93: }


                     293 

                     294 ;94: 



                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_13c1.s
                     295 ;95: IEDEntity IEDTree_findDataSetBySingleName(StringView* dataSetName)


                     296 	.align	4

                     297 	.align	4

                     298 IEDTree_findDataSetBySingleName::

000000f8 e92d4000    299 	stmfd	[sp]!,{lr}

000000fc e24dd010    300 	sub	sp,sp,16

00000100 e1a0300d    301 	mov	r3,sp

00000104 e28d2008    302 	add	r2,sp,8

00000108 e3a0102f    303 	mov	r1,47

0000010c eb000000*   304 	bl	StringView_splitChar

                     305 ;96: {


                     306 

                     307 ;97:     StringView ldName;


                     308 ;98:     StringView remainingName;


                     309 ;99:     if(!StringView_splitChar(dataSetName, '/', &ldName, &remainingName))


                     310 

00000110 e3500000    311 	cmp	r0,0

                     312 ;100:     {


                     313 

                     314 ;101:         return NULL;


                     315 

                     316 ;102:     }


                     317 ;103:     return IEDTree_findDataSetByFullName(&ldName, &remainingName);


                     318 

00000114 11a0100d    319 	movne	r1,sp

00000118 128d0008    320 	addne	r0,sp,8

0000011c 1bffffe6*   321 	blne	IEDTree_findDataSetByFullName

00000120 e28dd010    322 	add	sp,sp,16

00000124 e8bd8000    323 	ldmfd	[sp]!,{pc}

                     324 	.endf	IEDTree_findDataSetBySingleName

                     325 	.align	4

                     326 ;ldName	[sp,8]	local

                     327 ;remainingName	[sp]	local

                     328 

                     329 ;dataSetName	none	param

                     330 

                     331 	.section ".bss","awb"

                     332 .L341:

                     333 	.data

                     334 	.text

                     335 

                     336 ;104: }


                     337 

                     338 ;105: 


                     339 ;106: MmsDataAccessError IEDTree_write(StringView* ldName,


                     340 	.align	4

                     341 	.align	4

                     342 IEDTree_write::

00000128 e92d4030    343 	stmfd	[sp]!,{r4-r5,lr}

0000012c e1a04002    344 	mov	r4,r2

00000130 e1a05003    345 	mov	r5,r3

                     346 ;107:                                  StringView* itemName, IsoConnection* isoConn,


                     347 ;108:                                    BufferView* value)


                     348 ;109: {


                     349 

                     350 ;110:     IEDEntity entity = IEDTree_findDataByFullName(ldName, itemName);


                     351 

00000134 ebffffd1*   352 	bl	IEDTree_findDataByFullName

                     353 ;111:     if(entity == NULL)


                     354 

00000138 e3500000    355 	cmp	r0,0


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_13c1.s
                     356 ;115:     }


                     357 ;116:     return IEDEntity_write(entity, isoConn, value);


                     358 

0000013c 11a02005    359 	movne	r2,r5

00000140 11a01004    360 	movne	r1,r4

00000144 18bd4030    361 	ldmnefd	[sp]!,{r4-r5,lr}

00000148 1a000000*   362 	bne	IEDEntity_write

                     363 ;112:     {


                     364 

                     365 ;113:         ERROR_REPORT("Item is not found");


                     366 ;114:         return DATA_ACCESS_ERROR_OBJECT_NONE_EXISTENT;


                     367 

0000014c e3a0000a    368 	mov	r0,10

00000150 e8bd8030    369 	ldmfd	[sp]!,{r4-r5,pc}

                     370 	.endf	IEDTree_write

                     371 	.align	4

                     372 ;entity	r1	local

                     373 

                     374 ;ldName	none	param

                     375 ;itemName	none	param

                     376 ;isoConn	r4	param

                     377 ;value	r5	param

                     378 

                     379 	.section ".bss","awb"

                     380 .L390:

                     381 	.data

                     382 	.text

                     383 

                     384 ;117: }


                     385 

                     386 ;118: 


                     387 ;119: void IEDTree_updateFromDataSlice(void)


                     388 	.align	4

                     389 	.align	4

                     390 IEDTree_updateFromDataSlice::

00000154 e92d4010    391 	stmfd	[sp]!,{r4,lr}

                     392 ;120: {


                     393 

                     394 ;121:     IEDEntity currEntity = dataSliceUpdateList;


                     395 

00000158 e59f0048*   396 	ldr	r0,.L448

0000015c e5904000    397 	ldr	r4,[r0]

                     398 ;122: 


                     399 ;123:     while(currEntity != NULL)


                     400 

00000160 e3540000    401 	cmp	r4,0

00000164 0a000006    402 	beq	.L404

                     403 .L408:

                     404 ;124:     {


                     405 

                     406 ;125:         currEntity->updateFromDataSlice(currEntity);


                     407 

00000168 e594c068    408 	ldr	r12,[r4,104]

0000016c e1a00004    409 	mov	r0,r4

00000170 e1a0e00f    410 	mov	lr,pc

00000174 e12fff1c*   411 	bx	r12

                     412 ;126:         currEntity = currEntity->nextCompare;


                     413 

00000178 e5944010    414 	ldr	r4,[r4,16]

0000017c e3540000    415 	cmp	r4,0

00000180 1afffff8    416 	bne	.L408


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_13c1.s
                     417 .L404:

00000184 e8bd8010    418 	ldmfd	[sp]!,{r4,pc}

                     419 	.endf	IEDTree_updateFromDataSlice

                     420 	.align	4

                     421 ;currEntity	r4	local

                     422 

                     423 	.section ".bss","awb"

                     424 .L438:

                     425 	.data

                     426 	.text

                     427 

                     428 ;127:     }


                     429 ;128: }


                     430 

                     431 ;129: 


                     432 ;130: void IEDTree_addToCmpList(IEDEntity da)


                     433 	.align	4

                     434 	.align	4

                     435 IEDTree_addToCmpList::

                     436 ;131: {


                     437 

                     438 ;132:     *dataSliceUpdateListTail = da;


                     439 

00000188 e59f201c*   440 	ldr	r2,.L485

0000018c e5921000    441 	ldr	r1,[r2]

00000190 e5810000    442 	str	r0,[r1]

                     443 ;133:     dataSliceUpdateListTail = &da->nextCompare;


                     444 

00000194 e2800010    445 	add	r0,r0,16

00000198 e5820000    446 	str	r0,[r2]

0000019c e12fff1e*   447 	ret	

                     448 	.endf	IEDTree_addToCmpList

                     449 	.align	4

                     450 

                     451 ;da	r0	param

                     452 

                     453 	.section ".bss","awb"

                     454 .L478:

                     455 	.data

                     456 	.text

                     457 

                     458 ;134: }


                     459 	.align	4

                     460 .L74:

000001a0 00000000*   461 	.data.w	iedTreeCS

                     462 	.type	.L74,$object

                     463 	.size	.L74,4

                     464 

                     465 .L75:

000001a4 00000000*   466 	.data.w	.L61

                     467 	.type	.L75,$object

                     468 	.size	.L75,4

                     469 

                     470 .L448:

000001a8 00000000*   471 	.data.w	dataSliceUpdateList

                     472 	.type	.L448,$object

                     473 	.size	.L448,4

                     474 

                     475 .L485:

000001ac 00000000*   476 	.data.w	dataSliceUpdateListTail

                     477 	.type	.L485,$object


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_13c1.s
                     478 	.size	.L485,4

                     479 

                     480 	.align	4

                     481 ;iedTreeRoot	.L61	static

                     482 ;iedTreeCS	iedTreeCS	static

                     483 

                     484 	.data

                     485 .L500:

                     486 	.globl	dataSliceUpdateList

00000004 00000000    487 dataSliceUpdateList:	.data.b	0,0,0,0

                     488 	.type	dataSliceUpdateList,$object

                     489 	.size	dataSliceUpdateList,4

                     490 .L501:

                     491 	.globl	dataSliceUpdateListTail

00000008 00000000*   492 dataSliceUpdateListTail:	.data.w	.L500

                     493 	.type	dataSliceUpdateListTail,$object

                     494 	.size	dataSliceUpdateListTail,4

                     495 	.ghsnote version,6

                     496 	.ghsnote tools,3

                     497 	.ghsnote options,0

                     498 	.text

                     499 	.align	4

                     500 	.data

                     501 	.align	4

                     502 	.text

