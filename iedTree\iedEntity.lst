                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4f81.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=iedEntity.c -o iedTree\gh_4f81.o -list=iedTree/iedEntity.lst C:\Users\<USER>\AppData\Local\Temp\gh_4f81.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_4f81.s
Source File: iedEntity.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      10 ;		iedTree/iedEntity.c -o iedTree/iedEntity.o

                      11 ;Source File:   iedTree/iedEntity.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:55:09 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "iedEntity.h"


                      21 ;2: 


                      22 ;3: #include "..\MemoryManager.h"


                      23 ;4: #include "..\bufViewBER.h"


                      24 ;5: #include "..\iedmodel.h"


                      25 ;6: #include "..\BaseAsnTypes.h"


                      26 ;7: #include "iedFC.h"


                      27 ;8: #include "iedObjects.h"


                      28 ;9: #include "iedFinalDA.h"


                      29 ;10: #include "iedTimeStamp.h"


                      30 ;11: #include "DataSet.h"


                      31 ;12: #include "..\IsoConnectionForward.h"


                      32 ;13: 


                      33 ;14: #include <string.h>


                      34 ;15: #include <stdlib.h>


                      35 ;16: 


                      36 ;17: #include "debug.h"


                      37 ;18: 


                      38 ;19: static bool createChildrenFromBER(IEDEntity parent,


                      39 ;20:     BufferView* childrenBER);


                      40 ;21: 


                      41 ;22: void *IEDEntity_alloc(size_t size)


                      42 ;23: {


                      43 ;24:     void* p = MM_alloc(size);


                      44 ;25:     if(p != NULL)


                      45 ;26:     {


                      46 ;27:         memset(p, 0, size);


                      47 ;28:     }


                      48 ;29:     return p;


                      49 ;30: }


                      50 ;31: 



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4f81.s
                      51 ;32: // Заглушка типа IEDEntity_OnNewDataSlice


                      52 ;33: static void updateFromDataSliceStub(IEDEntity da)


                      53 

                      54 ;35:     ERROR_REPORT("Stub called:onNewDataSliceStub");


                      55 ;36: }


                      56 

                      57 ;37: 


                      58 ;38: static bool calcReadLen(IEDEntity entity, size_t* pLen )


                      59 ;39: {


                      60 ;40:     size_t len;


                      61 ;41:     bool result;


                      62 ;42:     switch (entity->type)


                      63 ;43:     {


                      64 ;44:     case IED_ENTITY_DA_TERMINAL_ITEM:


                      65 ;45:         result = IEDTermItemDA_calcReadLen(entity, &len);


                      66 ;46:         break;


                      67 ;47:     case IED_ENTITY_DA_TIMESTAMP:


                      68 ;48:         result = IEDTimeStampDA_calcReadLen(entity, &len);


                      69 ;49:         break;


                      70 ;50:     case IED_ENTITY_DA_CONST:


                      71 ;51:         result = IEDConstDA_calcReadLen(entity, &len);


                      72 ;52:         break;


                      73 ;53:     case IED_ENTITY_DA_VAR:


                      74 ;54:         result = IEDVarDA_calcReadLen(entity, &len);


                      75 ;55:         break;


                      76 ;56:     case IED_ENTITY_LN:


                      77 ;57:     case IED_ENTITY_FC:


                      78 ;58:     case IED_ENTITY_DO:


                      79 ;59:     case IED_ENTITY_DA:


                      80 ;60:         result = IEDComplexObj_calcReadLen(entity, &len);


                      81 ;61:         break;


                      82 ;62:     default:


                      83 ;63:         ERROR_REPORT("Invalid object to read");


                      84 ;64:         return false;


                      85 ;65:     }


                      86 ;66:     *pLen = len;


                      87 ;67:     return result;


                      88 ;68: }


                      89 ;69: 


                      90 ;70: static bool encodeRead(IEDEntity entity, BufferView* outBuf)


                      91 ;71: {


                      92 ;72:     switch (entity->type)


                      93 ;73:     {


                      94 ;74:     case IED_ENTITY_DA_TERMINAL_ITEM:


                      95 ;75:         return IEDTermItemDA_encodeRead(entity, outBuf);


                      96 ;76:     case IED_ENTITY_DA_TIMESTAMP:


                      97 ;77:         return IEDTimeStampDA_encodeRead(entity, outBuf);


                      98 ;78:     case IED_ENTITY_DA_CONST:


                      99 ;79:         return IEDConstDA_encodeRead(entity, outBuf);


                     100 ;80:     case IED_ENTITY_DA_VAR:


                     101 ;81:         return IEDVarDA_encodeRead(entity, outBuf);


                     102 ;82:     case IED_ENTITY_LN:


                     103 ;83:     case IED_ENTITY_FC:


                     104 ;84:     case IED_ENTITY_DO:


                     105 ;85:     case IED_ENTITY_DA:


                     106 ;86:         return IEDComplexObj_encodeRead(entity, outBuf);


                     107 ;87:     default:


                     108 ;88:         ERROR_REPORT("Invalid object to read");


                     109 ;89:         return false;


                     110 ;90:     }


                     111 ;91: }



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4f81.s
                     112 ;92: 


                     113 ;93: static MmsDataAccessError write(IEDEntity entity, IsoConnection* isoConn,


                     114 ;94:                                    BufferView* value)


                     115 ;95: {


                     116 ;96:     switch (entity->type)


                     117 ;97:     {


                     118 ;98:     case IED_ENTITY_DA_TERMINAL_ITEM:


                     119 ;99:         return IEDTermItemDA_write(entity, isoConn, value);


                     120 ;100:     case IED_ENTITY_DA_TIMESTAMP:


                     121 ;101:         return IEDTimeStampDA_write(entity, value);


                     122 ;102:     case IED_ENTITY_DA_CONST:


                     123 ;103:         //Константы специально игнорируем


                     124 ;104:         if(!BufferView_skipAnyObject(value))


                     125 ;105:         {


                     126 ;106:             return DATA_ACCESS_ERROR_UNKNOWN;


                     127 ;107:         }


                     128 ;108:         return DATA_ACCESS_ERROR_SUCCESS;


                     129 ;109:     case IED_ENTITY_DA_VAR:


                     130 ;110:         return IEDVarDA_write(entity, isoConn, value);


                     131 ;111:     case IED_ENTITY_DA:


                     132 ;112:     case IED_ENTITY_DO:


                     133 ;113:         return IEDComplexObj_write(entity, isoConn, value);


                     134 ;114:     default:


                     135 ;115:         ERROR_REPORT("Invalid object to write");


                     136 ;116:         return DATA_ACCESS_ERROR_OBJECT_ACCESS_UNSUPPORTED;


                     137 ;117:     }


                     138 ;118: }


                     139 ;119: 


                     140 ;120: 


                     141 ;121: void IEDEntity_addChild(IEDEntity parent, IEDEntity child)


                     142 ;122: {


                     143 ;123:     child->parent = parent;


                     144 ;124:     child->next = NULL;


                     145 ;125:     if (parent->firstChild == NULL)


                     146 ;126:     {


                     147 ;127:         parent->firstChild = child;


                     148 ;128:     }


                     149 ;129:     else


                     150 ;130:     {


                     151 ;131:         parent->lastChild->next = child;


                     152 ;132:     }


                     153 ;133:     parent->lastChild = child;


                     154 ;134: }


                     155 ;135: 


                     156 ;136: void IEDEntity_init(IEDEntity iedEntity, IEDEntity parent)


                     157 ;137: {


                     158 ;138:     //Для всех объектов ставим заглушку.


                     159 ;139:     //Потом каждый объект заменит её на то что ему надо.


                     160 ;140:     iedEntity->updateFromDataSlice = updateFromDataSliceStub;


                     161 ;141: 


                     162 ;142:     // Эти функции общие для всех элементов


                     163 ;143:     // они внутри используют switch по типу элемента.


                     164 ;144:     // Со временем можно постепенно заменить их на индивидуальные


                     165 ;145:     // для каждого.


                     166 ;146:     iedEntity->encodeRead = encodeRead;


                     167 ;147:     iedEntity->calcReadLen = calcReadLen;


                     168 ;148:     iedEntity->write = write;


                     169 ;149: 


                     170 ;150:     // По умолчанию невалидное значение


                     171 ;151:     iedEntity->dataSliceOffset = -1;


                     172 ;152: 



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4f81.s
                     173 ;153: 


                     174 ;154:     if(parent != NULL)


                     175 ;155:     {


                     176 ;156:         IEDEntity_addChild(parent, iedEntity);


                     177 ;157:     }


                     178 ;158: }


                     179 ;159: 


                     180 ;160: bool IEDEntity_create(IEDEntity* iedEntity, IEDEntity parent)


                     181 ;161: {


                     182 ;162:     size_t size = sizeof(struct IEDEntityStruct);


                     183 ;163:     *iedEntity = IEDEntity_alloc(size);


                     184 ;164:     if(*iedEntity == NULL)


                     185 ;165:     {


                     186 ;166:         ERROR_REPORT("IEDEntity allocation error");


                     187 ;167:         return false;


                     188 ;168:     }


                     189 ;169:     IEDEntity_init(*iedEntity, parent);


                     190 ;170:     return true;


                     191 ;171: }


                     192 ;172: 


                     193 ;173: bool IEDEntity_postCreate(IEDEntity entity)


                     194 ;174: {


                     195 ;175:     IEDEntity nextChild;


                     196 ;176: 


                     197 ;177:     //Вызываем postCreate детей


                     198 ;178:     nextChild = entity->firstChild;


                     199 ;179:     while(nextChild != NULL)


                     200 ;180:     {


                     201 ;181:         bool result = IEDEntity_postCreate(nextChild);


                     202 ;182:         if(!result)


                     203 ;183:         {


                     204 ;184:             return false;


                     205 ;185:         }


                     206 ;186:         nextChild = nextChild->next;


                     207 ;187:     }


                     208 ;188: 


                     209 ;189:     //Выполняем postCreate для себя


                     210 ;190:     switch(entity->tag) {


                     211 ;191:     case IED_DATA_SET:


                     212 ;192:         return DataSet_postCreate(entity);


                     213 ;193:     }


                     214 ;194:     return true;


                     215 ;195: }


                     216 ;196: 


                     217 ;197: 


                     218 ;198: static bool initSpecific(IEDEntity entity)


                     219 

                     220 ;218: }


                     221 

                     222 ;219: 


                     223 ;220: // Заполняет служебную информацию  любого IEDEntiy из BER


                     224 ;221: // вроде имени и флагов


                     225 ;222: // ber должен указывать на начало содержимого объекта


                     226 ;223: //(то есть сразу после длины).


                     227 ;224: // При успешном завершении ber указывает на буфер сразу


                     228 ;225: // за параметрами.


                     229 ;226: static bool readServiceInfo(IEDEntity entity, BufferView* ber)


                     230 

                     231 ;283: }


                     232 

                     233 ;284: 



                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4f81.s
                     234 ;285: bool IEDEntity_createFromBER(IEDEntity* iedEntity, BufferView* ber,


                     235 ;286:                              IEDEntity parent)


                     236 ;287: {


                     237 ;288:     uint8_t tag;


                     238 ;289:     size_t len, fullLen;


                     239 ;290:     BufferView berObject;


                     240 ;291:     IEDEntity entity;


                     241 ;292:     uint8_t* pObjectBER = ber->p + ber->pos;


                     242 ;293: 


                     243 ;294:     if(!IEDEntity_create(&entity, parent))


                     244 ;295:     {


                     245 ;296:         ERROR_REPORT("IEDEntity_create error");


                     246 ;297:         return false;


                     247 ;298:     }


                     248 ;299: 


                     249 ;300:     *iedEntity = entity;


                     250 ;301: 


                     251 ;302:     //Получить размер объекта


                     252 ;303:     if(!BufferView_decodeTL(ber, &tag, &len, &fullLen))


                     253 ;304:     {


                     254 ;305:         return false;


                     255 ;306:     }


                     256 ;307:     entity->tag = tag;


                     257 ;308: 


                     258 ;309:     BufferView_init(&entity->ber, pObjectBER, fullLen, 0 );


                     259 ;310: 


                     260 ;311:     BufferView_init(&berObject, ber->p + ber->pos, len, 0);


                     261 ;312: 


                     262 ;313:     //Перемещаем позицию буфера за объект.


                     263 ;314:     //Дальше будем действовать через его собственный буфер berObject


                     264 ;315:     if(!BufferView_advance(ber,len))


                     265 ;316:     {


                     266 ;317:         return false;


                     267 ;318:     }


                     268 ;319: 


                     269 ;320:     if(! readServiceInfo(entity, &berObject))


                     270 ;321:     {


                     271 ;322:         return false;


                     272 ;323:     }


                     273 ;324: 


                     274 ;325:     //Дети


                     275 ;326:     if (tag != IED_DA_FINAL && tag != IED_DATA_SET)


                     276 ;327:     {


                     277 ;328:         if(!createChildrenFromBER(entity, &berObject))


                     278 ;329:         {


                     279 ;330:             return false;


                     280 ;331:         }


                     281 ;332:     }


                     282 ;333:     //Инициализация специфических особенностей объекта делается


                     283 ;334:     //после полной инициализации его детей.


                     284 ;335:     //Т.е. тип(или подтип) родителя может зависеть от типа детей


                     285 ;336:     return initSpecific(entity);


                     286 ;337: }


                     287 ;338: 


                     288 ;339: bool createChildrenFromBER(IEDEntity parent,


                     289 

                     290 ;352: }


                     291 

                     292 	.text

                     293 	.align	4

                     294 IEDEntity_alloc::


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4f81.s
00000000 e92d4030    295 	stmfd	[sp]!,{r4-r5,lr}

00000004 e1a05000    296 	mov	r5,r0

00000008 eb000000*   297 	bl	MM_alloc

0000000c e1b04000    298 	movs	r4,r0

00000010 11a02005    299 	movne	r2,r5

00000014 13a01000    300 	movne	r1,0

00000018 1b000000*   301 	blne	memset

0000001c e1a00004    302 	mov	r0,r4

00000020 e8bd8030    303 	ldmfd	[sp]!,{r4-r5,pc}

                     304 	.endf	IEDEntity_alloc

                     305 	.align	4

                     306 ;p	r4	local

                     307 

                     308 ;size	r5	param

                     309 

                     310 	.section ".bss","awb"

                     311 .L189:

                     312 	.data

                     313 	.text

                     314 

                     315 

                     316 	.align	4

                     317 	.align	4

                     318 calcReadLen:

00000024 e92d4010    319 	stmfd	[sp]!,{r4,lr}

00000028 e24dd004    320 	sub	sp,sp,4

0000002c e5902050    321 	ldr	r2,[r0,80]

00000030 e1a04001    322 	mov	r4,r1

00000034 e2522003    323 	subs	r2,r2,3

00000038 e2522004    324 	subs	r2,r2,4

0000003c 3a00001b    325 	blo	.L210

00000040 0a000010    326 	beq	.L208

00000044 e2522002    327 	subs	r2,r2,2

00000048 3a000004    328 	blo	.L206

0000004c 0a000008    329 	beq	.L207

00000050 e3520001    330 	cmp	r2,1

00000054 13a00000    331 	movne	r0,0

00000058 1a000018    332 	bne	.L202

0000005c ea00000e    333 	b	.L209

                     334 .L206:

00000060 e1a0100d    335 	mov	r1,sp

00000064 eb000000*   336 	bl	IEDTermItemDA_calcReadLen

00000068 e59d1000    337 	ldr	r1,[sp]

0000006c e5841000    338 	str	r1,[r4]

00000070 ea000012    339 	b	.L202

                     340 .L207:

00000074 e1a0100d    341 	mov	r1,sp

00000078 eb000000*   342 	bl	IEDTimeStampDA_calcReadLen

0000007c e59d1000    343 	ldr	r1,[sp]

00000080 e5841000    344 	str	r1,[r4]

00000084 ea00000d    345 	b	.L202

                     346 .L208:

00000088 e1a0100d    347 	mov	r1,sp

0000008c eb000000*   348 	bl	IEDConstDA_calcReadLen

00000090 e59d1000    349 	ldr	r1,[sp]

00000094 e5841000    350 	str	r1,[r4]

00000098 ea000008    351 	b	.L202

                     352 .L209:

0000009c e1a0100d    353 	mov	r1,sp

000000a0 eb000000*   354 	bl	IEDVarDA_calcReadLen

000000a4 e59d1000    355 	ldr	r1,[sp]


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4f81.s
000000a8 e5841000    356 	str	r1,[r4]

000000ac ea000003    357 	b	.L202

                     358 .L210:

000000b0 e1a0100d    359 	mov	r1,sp

000000b4 eb000000*   360 	bl	IEDComplexObj_calcReadLen

000000b8 e59d1000    361 	ldr	r1,[sp]

000000bc e5841000    362 	str	r1,[r4]

                     363 .L202:

000000c0 e28dd004    364 	add	sp,sp,4

000000c4 e8bd4010    365 	ldmfd	[sp]!,{r4,lr}

000000c8 e12fff1e*   366 	ret	

                     367 	.endf	calcReadLen

                     368 	.align	4

                     369 ;len	[sp]	local

                     370 ;result	r0	local

                     371 

                     372 ;entity	r0	param

                     373 ;pLen	r4	param

                     374 

                     375 	.section ".bss","awb"

                     376 .L276:

                     377 	.data

                     378 	.text

                     379 

                     380 

                     381 	.align	4

                     382 	.align	4

                     383 encodeRead:

000000cc e92d4000    384 	stmfd	[sp]!,{lr}

000000d0 e5902050    385 	ldr	r2,[r0,80]

000000d4 e2522003    386 	subs	r2,r2,3

000000d8 e2522004    387 	subs	r2,r2,4

000000dc 3a00000d    388 	blo	.L314

000000e0 0a00000a    389 	beq	.L310

000000e4 e2522002    390 	subs	r2,r2,2

000000e8 3a000004    391 	blo	.L306

000000ec 0a000005    392 	beq	.L308

000000f0 e3520001    393 	cmp	r2,1

000000f4 13a00000    394 	movne	r0,0

000000f8 0b000000*   395 	bleq	IEDVarDA_encodeRead

000000fc ea000006    396 	b	.L302

                     397 .L306:

00000100 eb000000*   398 	bl	IEDTermItemDA_encodeRead

00000104 ea000004    399 	b	.L302

                     400 .L308:

00000108 eb000000*   401 	bl	IEDTimeStampDA_encodeRead

0000010c ea000002    402 	b	.L302

                     403 .L310:

00000110 eb000000*   404 	bl	IEDConstDA_encodeRead

00000114 ea000000    405 	b	.L302

                     406 .L314:

00000118 eb000000*   407 	bl	IEDComplexObj_encodeRead

                     408 .L302:

0000011c e8bd4000    409 	ldmfd	[sp]!,{lr}

00000120 e12fff1e*   410 	ret	

                     411 	.endf	encodeRead

                     412 	.align	4

                     413 

                     414 ;entity	r0	param

                     415 ;outBuf	r1	param

                     416 


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4f81.s
                     417 	.section ".bss","awb"

                     418 .L367:

                     419 	.data

                     420 	.text

                     421 

                     422 

                     423 	.align	4

                     424 	.align	4

                     425 write:

00000124 e92d4000    426 	stmfd	[sp]!,{lr}

00000128 e5903050    427 	ldr	r3,[r0,80]

0000012c e2533005    428 	subs	r3,r3,5

00000130 e2533002    429 	subs	r3,r3,2

00000134 3a000012    430 	blo	.L407

00000138 0a00000b    431 	beq	.L400

0000013c e2533002    432 	subs	r3,r3,2

00000140 3a000004    433 	blo	.L396

00000144 0a000005    434 	beq	.L398

00000148 e3530001    435 	cmp	r3,1

0000014c 13a00009    436 	movne	r0,9

00000150 0b000000*   437 	bleq	IEDVarDA_write

00000154 ea00000b    438 	b	.L392

                     439 .L396:

00000158 eb000000*   440 	bl	IEDTermItemDA_write

0000015c ea000009    441 	b	.L392

                     442 .L398:

00000160 e1a01002    443 	mov	r1,r2

00000164 eb000000*   444 	bl	IEDTimeStampDA_write

00000168 ea000006    445 	b	.L392

                     446 .L400:

0000016c e1a00002    447 	mov	r0,r2

00000170 eb000000*   448 	bl	BufferView_skipAnyObject

00000174 e3500000    449 	cmp	r0,0

00000178 13e00000    450 	mvnne	r0,0

0000017c 03a0000c    451 	moveq	r0,12

00000180 ea000000    452 	b	.L392

                     453 .L407:

00000184 eb000000*   454 	bl	IEDComplexObj_write

                     455 .L392:

00000188 e8bd4000    456 	ldmfd	[sp]!,{lr}

0000018c e12fff1e*   457 	ret	

                     458 	.endf	write

                     459 	.align	4

                     460 

                     461 ;entity	r0	param

                     462 ;isoConn	r1	param

                     463 ;value	r2	param

                     464 

                     465 	.section ".bss","awb"

                     466 .L462:

                     467 	.data

                     468 	.text

                     469 

                     470 

                     471 	.align	4

                     472 	.align	4

                     473 IEDEntity_addChild::

00000190 e5810000    474 	str	r0,[r1]

00000194 e3a02000    475 	mov	r2,0

00000198 e581200c    476 	str	r2,[r1,12]

0000019c e5902004    477 	ldr	r2,[r0,4]


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4f81.s
000001a0 e3520000    478 	cmp	r2,0

000001a4 15902008    479 	ldrne	r2,[r0,8]

000001a8 05801004    480 	streq	r1,[r0,4]

000001ac 1582100c    481 	strne	r1,[r2,12]

000001b0 e5801008    482 	str	r1,[r0,8]

000001b4 e12fff1e*   483 	ret	

                     484 	.endf	IEDEntity_addChild

                     485 	.align	4

                     486 

                     487 ;parent	r0	param

                     488 ;child	r1	param

                     489 

                     490 	.section ".bss","awb"

                     491 .L522:

                     492 	.data

                     493 	.text

                     494 

                     495 

                     496 	.align	4

                     497 	.align	4

                     498 IEDEntity_init::

000001b8 e1b02001    499 	movs	r2,r1

000001bc e59f12a4*   500 	ldr	r1,.L579

000001c0 e5801068    501 	str	r1,[r0,104]

000001c4 e59f12a0*   502 	ldr	r1,.L580

000001c8 e59f32a0*   503 	ldr	r3,.L581

000001cc e5801060    504 	str	r1,[r0,96]

000001d0 e59f129c*   505 	ldr	r1,.L582

000001d4 e580305c    506 	str	r3,[r0,92]

000001d8 e5801064    507 	str	r1,[r0,100]

000001dc e3e01000    508 	mvn	r1,0

000001e0 e580102c    509 	str	r1,[r0,44]

000001e4 11a01000    510 	movne	r1,r0

000001e8 11a00002    511 	movne	r0,r2

000001ec 1affffe7*   512 	bne	IEDEntity_addChild

000001f0 e12fff1e*   513 	ret	

                     514 	.endf	IEDEntity_init

                     515 	.align	4

                     516 

                     517 ;iedEntity	r0	param

                     518 ;parent	r2	param

                     519 

                     520 	.section ".bss","awb"

                     521 .L568:

                     522 	.data

                     523 	.text

                     524 

                     525 

                     526 	.align	4

                     527 	.align	4

                     528 IEDEntity_create::

000001f4 e92d4030    529 	stmfd	[sp]!,{r4-r5,lr}

000001f8 e1a05001    530 	mov	r5,r1

000001fc e1a04000    531 	mov	r4,r0

00000200 e3a0006c    532 	mov	r0,108

00000204 ebffff7d*   533 	bl	IEDEntity_alloc

00000208 e1b02000    534 	movs	r2,r0

0000020c e5842000    535 	str	r2,[r4]

00000210 020200ff    536 	andeq	r0,r2,255

00000214 0a000002    537 	beq	.L583

00000218 e1a01005    538 	mov	r1,r5


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4f81.s
0000021c ebffffe5*   539 	bl	IEDEntity_init

00000220 e3a00001    540 	mov	r0,1

                     541 .L583:

00000224 e8bd8030    542 	ldmfd	[sp]!,{r4-r5,pc}

                     543 	.endf	IEDEntity_create

                     544 	.align	4

                     545 

                     546 ;iedEntity	r4	param

                     547 ;parent	r5	param

                     548 

                     549 	.section ".bss","awb"

                     550 .L629:

                     551 	.data

                     552 	.text

                     553 

                     554 

                     555 	.align	4

                     556 	.align	4

                     557 IEDEntity_postCreate::

00000228 e92d4030    558 	stmfd	[sp]!,{r4-r5,lr}

0000022c e1a05000    559 	mov	r5,r0

00000230 e5954004    560 	ldr	r4,[r5,4]

00000234 e3540000    561 	cmp	r4,0

00000238 0a000006    562 	beq	.L646

                     563 .L647:

0000023c e1a00004    564 	mov	r0,r4

00000240 ebfffff8*   565 	bl	IEDEntity_postCreate

00000244 e3500000    566 	cmp	r0,0

00000248 0a000008    567 	beq	.L643

0000024c e594400c    568 	ldr	r4,[r4,12]

00000250 e3540000    569 	cmp	r4,0

00000254 1afffff8    570 	bne	.L647

                     571 .L646:

00000258 e5d50020    572 	ldrb	r0,[r5,32]

0000025c e35000e7    573 	cmp	r0,231

00000260 01a00005    574 	moveq	r0,r5

00000264 08bd4030    575 	ldmeqfd	[sp]!,{r4-r5,lr}

00000268 0a000000*   576 	beq	DataSet_postCreate

0000026c e3a00001    577 	mov	r0,1

                     578 .L643:

00000270 e8bd8030    579 	ldmfd	[sp]!,{r4-r5,pc}

                     580 	.endf	IEDEntity_postCreate

                     581 	.align	4

                     582 ;nextChild	r4	local

                     583 ;result	r0	local

                     584 

                     585 ;entity	r5	param

                     586 

                     587 	.section ".bss","awb"

                     588 .L738:

                     589 	.data

                     590 	.text

                     591 

                     592 

                     593 	.align	4

                     594 	.align	4

                     595 IEDEntity_createFromBER::

00000274 e92d4070    596 	stmfd	[sp]!,{r4-r6,lr}

00000278 e24dd024    597 	sub	sp,sp,36

0000027c e1a06000    598 	mov	r6,r0

00000280 e1a04001    599 	mov	r4,r1


                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4f81.s
00000284 e8940021    600 	ldmfd	[r4],{r0,r5}

00000288 e1a01002    601 	mov	r1,r2

0000028c e0855000    602 	add	r5,r5,r0

00000290 e28d000c    603 	add	r0,sp,12

00000294 ebffffd6*   604 	bl	IEDEntity_create

00000298 e3500000    605 	cmp	r0,0

0000029c 0a00006f    606 	beq	.L801

000002a0 e28d3008    607 	add	r3,sp,8

000002a4 e28d2004    608 	add	r2,sp,4

000002a8 e59d000c    609 	ldr	r0,[sp,12]

000002ac e28d1002    610 	add	r1,sp,2

000002b0 e5860000    611 	str	r0,[r6]

000002b4 e1a00004    612 	mov	r0,r4

000002b8 eb000000*   613 	bl	BufferView_decodeTL

000002bc e3500000    614 	cmp	r0,0

000002c0 0a000066    615 	beq	.L801

000002c4 e59d000c    616 	ldr	r0,[sp,12]

000002c8 e5dd1002    617 	ldrb	r1,[sp,2]

000002cc e59d2008    618 	ldr	r2,[sp,8]

000002d0 e5c01020    619 	strb	r1,[r0,32]

000002d4 e1a01005    620 	mov	r1,r5

000002d8 e2800014    621 	add	r0,r0,20

000002dc e3a03000    622 	mov	r3,0

000002e0 eb000000*   623 	bl	BufferView_init

000002e4 e59d2004    624 	ldr	r2,[sp,4]

000002e8 e894000a    625 	ldmfd	[r4],{r1,r3}

000002ec e28d0018    626 	add	r0,sp,24

000002f0 e0831001    627 	add	r1,r3,r1

000002f4 e3a03000    628 	mov	r3,0

000002f8 eb000000*   629 	bl	BufferView_init

000002fc e59d1004    630 	ldr	r1,[sp,4]

00000300 e1a00004    631 	mov	r0,r4

00000304 eb000000*   632 	bl	BufferView_advance

00000308 e3500000    633 	cmp	r0,0

0000030c 0a000053    634 	beq	.L801

00000310 e59d1020    635 	ldr	r1,[sp,32]

00000314 e59d001c    636 	ldr	r0,[sp,28]

00000318 e28d4018    637 	add	r4,sp,24

                     638 ;273:                 {


                     639 

                     640 ;274:                     ERROR_REPORT("BER error");


                     641 ;275:                     return false;


                     642 

0000031c e1500001    643 	cmp	r0,r1

00000320 0a000039    644 	beq	.L775

                     645 .L780:

                     646 ;227: {


                     647 

                     648 ;228:     uint32_t objFlags;


                     649 ;229: 


                     650 ;230:     while(!BufferView_endOfBuf(ber))


                     651 

                     652 ;231:     {


                     653 

                     654 ;232:         uint8_t tag;


                     655 ;233:         if(!BufferView_peekTag(ber, &tag))


                     656 

00000324 e28d1003    657 	add	r1,sp,3

00000328 e1a00004    658 	mov	r0,r4

0000032c eb000000*   659 	bl	BufferView_peekTag

00000330 e3500000    660 	cmp	r0,0


                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4f81.s
00000334 0a000034    661 	beq	.L775

                     662 ;234:         {


                     663 

                     664 ;235:             break;


                     665 

                     666 ;236:         }


                     667 ;237: 


                     668 ;238:         if(!IEDModel_isServiceInfo(tag))


                     669 

00000338 e5dd0003    670 	ldrb	r0,[sp,3]

0000033c eb000000*   671 	bl	IEDModel_isServiceInfo

00000340 e3500000    672 	cmp	r0,0

00000344 0a000030    673 	beq	.L775

                     674 ;239:         {


                     675 

                     676 ;240:             //Служебная информация кончилась


                     677 ;241:             break;


                     678 

                     679 ;242:         }


                     680 ;243: 


                     681 ;244:         switch(tag)


                     682 

00000348 e5dd0003    683 	ldrb	r0,[sp,3]

0000034c e250001a    684 	subs	r0,r0,26

00000350 0a000002    685 	beq	.L783

00000354 e35000a6    686 	cmp	r0,166

00000358 0a000008    687 	beq	.L785

0000035c ea000022    688 	b	.L791

                     689 .L783:

                     690 ;245:         {


                     691 ;246:             case ASN_VISIBLE_STRING:


                     692 ;247:                 //Имя


                     693 ;248:                 if (!BufferView_decodeStringViewTL(ber, ASN_VISIBLE_STRING,


                     694 

00000360 e59d000c    695 	ldr	r0,[sp,12]

00000364 e3a0101a    696 	mov	r1,26

00000368 e2802048    697 	add	r2,r0,72

0000036c e1a00004    698 	mov	r0,r4

00000370 eb000000*   699 	bl	BufferView_decodeStringViewTL

00000374 e3500000    700 	cmp	r0,0

00000378 1a00001f    701 	bne	.L793

0000037c ea000037    702 	b	.L801

                     703 .L785:

                     704 ;249:                     &entity->name))


                     705 ;250:                 {


                     706 

                     707 ;251:                     ERROR_REPORT("Error getting object name");


                     708 ;252:                     return false;


                     709 

                     710 ;253:                 }


                     711 ;254:                 break;


                     712 ;255:             case IED_OBJ_FLAGS:


                     713 ;256:                 if(!BufferView_decodeUInt32TL(ber, IED_OBJ_FLAGS, &objFlags))


                     714 

00000380 e28d2010    715 	add	r2,sp,16

00000384 e1a00004    716 	mov	r0,r4

00000388 e3a010c0    717 	mov	r1,192

0000038c eb000000*   718 	bl	BufferView_decodeUInt32TL

00000390 e3500000    719 	cmp	r0,0

00000394 0a000031    720 	beq	.L801

                     721 ;257:                 {



                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4f81.s
                     722 

                     723 ;258:                     ERROR_REPORT("Error getting object flags");


                     724 ;259:                     return false;


                     725 

                     726 ;260:                 }


                     727 ;261:                 if(objFlags & IED_OBJ_FLAGS_QCHG)


                     728 

00000398 e59d0010    729 	ldr	r0,[sp,16]

0000039c e3100002    730 	tst	r0,2

000003a0 0a000007    731 	beq	.L789

                     732 ;262:                 {


                     733 

                     734 ;263:                     entity->trgOps = TRGOP_QCHG;


                     735 

000003a4 e59d000c    736 	ldr	r0,[sp,12]

000003a8 e3a01008    737 	mov	r1,8

000003ac e5801024    738 	str	r1,[r0,36]

                     739 ;273:                 {


                     740 

                     741 ;274:                     ERROR_REPORT("BER error");


                     742 ;275:                     return false;


                     743 

000003b0 e59d1020    744 	ldr	r1,[sp,32]

000003b4 e59d001c    745 	ldr	r0,[sp,28]

000003b8 e1500001    746 	cmp	r0,r1

000003bc 1affffd8    747 	bne	.L780

000003c0 ea000011    748 	b	.L775

                     749 .L789:

                     750 ;264:                 }


                     751 ;265:                 else if (objFlags & IED_OBJ_FLAGS_DCHG)


                     752 

000003c4 e3100001    753 	tst	r0,1

000003c8 0a00000b    754 	beq	.L793

                     755 ;266:                 {


                     756 

                     757 ;267:                     entity->trgOps = TRGOP_DCHG;


                     758 

000003cc e59d000c    759 	ldr	r0,[sp,12]

000003d0 e3a01010    760 	mov	r1,16

000003d4 e5801024    761 	str	r1,[r0,36]

                     762 ;273:                 {


                     763 

                     764 ;274:                     ERROR_REPORT("BER error");


                     765 ;275:                     return false;


                     766 

000003d8 e59d1020    767 	ldr	r1,[sp,32]

000003dc e59d001c    768 	ldr	r0,[sp,28]

000003e0 e1500001    769 	cmp	r0,r1

000003e4 1affffce    770 	bne	.L780

000003e8 ea000007    771 	b	.L775

                     772 .L791:

                     773 ;268:                 }


                     774 ;269: 


                     775 ;270:                 break;


                     776 ;271:             default:


                     777 ;272:                 if(!BufferView_skipAnyObject(ber))


                     778 

000003ec e1a00004    779 	mov	r0,r4

000003f0 eb000000*   780 	bl	BufferView_skipAnyObject

000003f4 e3500000    781 	cmp	r0,0

000003f8 0a000018    782 	beq	.L801


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4f81.s
                     783 .L793:

                     784 ;273:                 {


                     785 

                     786 ;274:                     ERROR_REPORT("BER error");


                     787 ;275:                     return false;


                     788 

000003fc e59d1020    789 	ldr	r1,[sp,32]

00000400 e59d001c    790 	ldr	r0,[sp,28]

00000404 e1500001    791 	cmp	r0,r1

00000408 1affffc5    792 	bne	.L780

                     793 .L775:

                     794 ;276:                 }


                     795 ;277:         }


                     796 ;278:     }


                     797 ;279: 


                     798 ;280:     //Собрали всю инфу


                     799 ;281: 


                     800 ;282:     return true;


                     801 

0000040c e5dd0002    802 	ldrb	r0,[sp,2]

00000410 e35000e9    803 	cmp	r0,233

00000414 135000e7    804 	cmpne	r0,231

00000418 0a000016    805 	beq	.L813

0000041c e28d4018    806 	add	r4,sp,24

00000420 e1a05004    807 	mov	r5,r4

00000424 e59d1020    808 	ldr	r1,[sp,32]

00000428 e59d001c    809 	ldr	r0,[sp,28]

0000042c e28d6014    810 	add	r6,sp,20

                     811 ;347:         {


                     812 

                     813 ;348:             return false;


                     814 

00000430 e1500001    815 	cmp	r0,r1

00000434 0a00000f    816 	beq	.L813

                     817 .L805:

00000438 e59d200c    818 	ldr	r2,[sp,12]

0000043c e1a01005    819 	mov	r1,r5

00000440 e1a00006    820 	mov	r0,r6

00000444 ebffff8a*   821 	bl	IEDEntity_createFromBER

                     822 ;340:                                      BufferView* childrenBER)


                     823 ;341: {


                     824 

                     825 ;342: 


                     826 ;343:     while(!BufferView_endOfBuf(childrenBER))


                     827 

                     828 ;344:     {


                     829 

                     830 ;345:         IEDEntity child;


                     831 ;346:         if(!IEDEntity_createFromBER(&child, childrenBER, parent))


                     832 

00000448 e3500000    833 	cmp	r0,0

0000044c 0a000003    834 	beq	.L801

                     835 ;347:         {


                     836 

                     837 ;348:             return false;


                     838 

00000450 e9940003    839 	ldmed	[r4],{r0-r1}

00000454 e1500001    840 	cmp	r0,r1

00000458 1afffff6    841 	bne	.L805

0000045c ea000005    842 	b	.L813

                     843 .L801:


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4f81.s
                     844 ;349:         }


                     845 ;350:     }


                     846 ;351:     return true;


                     847 

00000460 e3a00000    848 	mov	r0,0

00000464 ea000026    849 	b	.L764

                     850 	.align	4

                     851 .L579:

00000468 00000000*   852 	.data.w	updateFromDataSliceStub

                     853 	.type	.L579,$object

                     854 	.size	.L579,4

                     855 

                     856 .L580:

0000046c 00000000*   857 	.data.w	calcReadLen

                     858 	.type	.L580,$object

                     859 	.size	.L580,4

                     860 

                     861 .L581:

00000470 00000000*   862 	.data.w	encodeRead

                     863 	.type	.L581,$object

                     864 	.size	.L581,4

                     865 

                     866 .L582:

00000474 00000000*   867 	.data.w	write

                     868 	.type	.L582,$object

                     869 	.size	.L582,4

                     870 

                     871 .L813:

                     872 ;199: {


                     873 

                     874 ;200:     switch(entity->tag) {


                     875 

00000478 e59d000c    876 	ldr	r0,[sp,12]

0000047c e5d01020    877 	ldrb	r1,[r0,32]

00000480 e25110e0    878 	subs	r1,r1,224

00000484 e351000c    879 	cmp	r1,12

00000488 8a00001c    880 	bhi	.L821

0000048c e08ff101    881 	add	pc,pc,r1 lsl 2

                     882 .L1301:

                     883 

00000490 e1a00000    884 	nop	

00000494 ea00000f    885 	b	.L816

00000498 ea000018    886 	b	.L821

0000049c ea000009    887 	b	.L814

000004a0 ea000016    888 	b	.L821

000004a4 ea000009    889 	b	.L815

000004a8 ea000014    890 	b	.L821

000004ac ea00000b    891 	b	.L817

000004b0 ea000010    892 	b	.L820

000004b4 ea00000b    893 	b	.L818

000004b8 ea00000c    894 	b	.L819

000004bc ea00000f    895 	b	.L821

000004c0 ea00000e    896 	b	.L821

000004c4 ea000001    897 	b	.L815

                     898 .L814:

                     899 ;201:     case IED_LD:


                     900 ;202:         return IEDLD_init(entity);


                     901 

000004c8 eb000000*   902 	bl	IEDLD_init

000004cc ea00000c    903 	b	.L764

                     904 .L815:


                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4f81.s
                     905 ;203:     case IED_VMD_DATA_SECTION:


                     906 ;204:     case IED_LN:


                     907 ;205:         return IEDComplexObj_init(entity);


                     908 

000004d0 eb000000*   909 	bl	IEDComplexObj_init

000004d4 ea00000a    910 	b	.L764

                     911 .L816:

                     912 ;206:     case IED_FC:


                     913 ;207:         return IEDFC_init(entity);


                     914 

000004d8 eb000000*   915 	bl	IEDFC_init

000004dc ea000008    916 	b	.L764

                     917 .L817:

                     918 ;208:     case IED_DO:


                     919 ;209:         return IEDDO_init(entity);


                     920 

000004e0 eb000000*   921 	bl	IEDDO_init

000004e4 ea000006    922 	b	.L764

                     923 .L818:

                     924 ;210:     case IED_DA:


                     925 ;211:         return IEDDA_init(entity);


                     926 

000004e8 eb000000*   927 	bl	IEDDA_init

000004ec ea000004    928 	b	.L764

                     929 .L819:

                     930 ;212:     case IED_DA_FINAL:


                     931 ;213:         return IEDFinalDA_init(entity);


                     932 

000004f0 eb000000*   933 	bl	IEDFinalDA_init

000004f4 ea000002    934 	b	.L764

                     935 .L820:

                     936 ;214:     case IED_DATA_SET:


                     937 ;215:         return DataSet_init(entity);


                     938 

000004f8 eb000000*   939 	bl	DataSet_init

000004fc ea000000    940 	b	.L764

                     941 .L821:

                     942 ;216:     }


                     943 ;217:     return true;


                     944 

00000500 e3a00001    945 	mov	r0,1

                     946 .L764:

00000504 e28dd024    947 	add	sp,sp,36

00000508 e8bd8070    948 	ldmfd	[sp]!,{r4-r6,pc}

                     949 	.endf	IEDEntity_createFromBER

                     950 	.align	4

                     951 ;tag	[sp,2]	local

                     952 ;len	[sp,4]	local

                     953 ;fullLen	[sp,8]	local

                     954 ;berObject	[sp,24]	local

                     955 ;entity	[sp,12]	local

                     956 ;pObjectBER	r5	local

                     957 ;objFlags	[sp,16]	local

                     958 ;tag	[sp,3]	local

                     959 ;child	[sp,20]	local

                     960 

                     961 ;iedEntity	r6	param

                     962 ;ber	r4	param

                     963 ;parent	r2	param

                     964 

                     965 	.section ".bss","awb"


                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4f81.s
                     966 .L1300:

                     967 	.data

                     968 	.ghsnote jtable,5,.L1301,.L1301,.L1301,14

                     969 	.text

                     970 

                     971 

                     972 ;353: 


                     973 ;354: IEDEntity IEDEntity_getChildByName(IEDEntity parent, StringView* name)


                     974 	.align	4

                     975 	.align	4

                     976 IEDEntity_getChildByName::

0000050c e92d4030    977 	stmfd	[sp]!,{r4-r5,lr}

                     978 ;355: {


                     979 

                     980 ;356:     IEDEntity child = parent->firstChild;


                     981 

00000510 e5904004    982 	ldr	r4,[r0,4]

                     983 ;357:     while(child != NULL)


                     984 

00000514 e1a05001    985 	mov	r5,r1

00000518 e3540000    986 	cmp	r4,0

0000051c 0a000006    987 	beq	.L1368

                     988 .L1369:

                     989 ;358:     {


                     990 

                     991 ;359:         if(0 == StringView_cmp(name, &child->name))


                     992 

00000520 e2841048    993 	add	r1,r4,72

00000524 e1a00005    994 	mov	r0,r5

00000528 eb000000*   995 	bl	StringView_cmp

0000052c e3500000    996 	cmp	r0,0

                     997 ;360:         {


                     998 

                     999 ;361:             return child;


                    1000 

                    1001 ;362:         }


                    1002 ;363:         child = child->next;


                    1003 

00000530 1594400c   1004 	ldrne	r4,[r4,12]

00000534 13540000   1005 	cmpne	r4,0

00000538 1afffff8   1006 	bne	.L1369

                    1007 .L1368:

                    1008 ;364:     }


                    1009 ;365:     return NULL;


                    1010 

0000053c e1a00004   1011 	mov	r0,r4

00000540 e8bd8030   1012 	ldmfd	[sp]!,{r4-r5,pc}

                    1013 	.endf	IEDEntity_getChildByName

                    1014 	.align	4

                    1015 ;child	r4	local

                    1016 

                    1017 ;parent	r0	param

                    1018 ;name	r5	param

                    1019 

                    1020 	.section ".bss","awb"

                    1021 .L1434:

                    1022 	.data

                    1023 	.text

                    1024 

                    1025 ;366: }


                    1026 


                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4f81.s
                    1027 ;367: 


                    1028 ;368: IEDEntity IEDEntity_getChildByCStrName(IEDEntity parent, const char* name)


                    1029 	.align	4

                    1030 	.align	4

                    1031 IEDEntity_getChildByCStrName::

00000544 e92d4030   1032 	stmfd	[sp]!,{r4-r5,lr}

                    1033 ;369: {


                    1034 

                    1035 ;370:     IEDEntity child = parent->firstChild;


                    1036 

00000548 e5904004   1037 	ldr	r4,[r0,4]

                    1038 ;371:     while(child != NULL)


                    1039 

0000054c e1a05001   1040 	mov	r5,r1

00000550 e3540000   1041 	cmp	r4,0

00000554 0a000006   1042 	beq	.L1455

                    1043 .L1456:

                    1044 ;372:     {


                    1045 

                    1046 ;373:         if(0 == StringView_cmpCStr(&child->name, name))


                    1047 

00000558 e1a01005   1048 	mov	r1,r5

0000055c e2840048   1049 	add	r0,r4,72

00000560 eb000000*  1050 	bl	StringView_cmpCStr

00000564 e3500000   1051 	cmp	r0,0

                    1052 ;374:         {


                    1053 

                    1054 ;375:             return child;


                    1055 

                    1056 ;376:         }


                    1057 ;377:         child = child->next;


                    1058 

00000568 1594400c   1059 	ldrne	r4,[r4,12]

0000056c 13540000   1060 	cmpne	r4,0

00000570 1afffff8   1061 	bne	.L1456

                    1062 .L1455:

                    1063 ;378:     }


                    1064 ;379:     return NULL;


                    1065 

00000574 e1a00004   1066 	mov	r0,r4

00000578 e8bd8030   1067 	ldmfd	[sp]!,{r4-r5,pc}

                    1068 	.endf	IEDEntity_getChildByCStrName

                    1069 	.align	4

                    1070 ;child	r4	local

                    1071 

                    1072 ;parent	r0	param

                    1073 ;name	r5	param

                    1074 

                    1075 	.section ".bss","awb"

                    1076 .L1530:

                    1077 	.data

                    1078 	.text

                    1079 

                    1080 ;380: }


                    1081 

                    1082 ;381: 


                    1083 ;382: IEDEntity IEDEntity_getChildByFullName(IEDEntity parent,  StringView* name)


                    1084 	.align	4

                    1085 	.align	4

                    1086 IEDEntity_getChildByFullName::

0000057c e92d40f0   1087 	stmfd	[sp]!,{r4-r7,lr}


                                                                      Page 19
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4f81.s
00000580 e24dd018   1088 	sub	sp,sp,24

                    1089 ;383: {


                    1090 

                    1091 ;384:     StringView fullName = *name;


                    1092 

00000584 e5913000   1093 	ldr	r3,[r1]

00000588 e1a04000   1094 	mov	r4,r0

                    1095 ;389: 


                    1096 ;390:     do


                    1097 

0000058c e58d3010   1098 	str	r3,[sp,16]

00000590 e5911004   1099 	ldr	r1,[r1,4]

00000594 e28d6008   1100 	add	r6,sp,8

00000598 e58d1014   1101 	str	r1,[sp,20]

                    1102 ;385:     StringView directChildName;


                    1103 ;386:     StringView remainingName;


                    1104 ;387:     bool lastName;


                    1105 ;388:     IEDEntity entity = parent;


                    1106 

0000059c e1a0700d   1107 	mov	r7,sp

                    1108 .L1552:

                    1109 ;391:     {


                    1110 

                    1111 ;392:         lastName = !StringView_splitChar(&fullName, '$',


                    1112 

000005a0 e1a03007   1113 	mov	r3,r7

000005a4 e1a02006   1114 	mov	r2,r6

000005a8 e28d0010   1115 	add	r0,sp,16

000005ac e3a01024   1116 	mov	r1,36

000005b0 eb000000*  1117 	bl	StringView_splitChar

000005b4 e3500000   1118 	cmp	r0,0

000005b8 03a05001   1119 	moveq	r5,1

000005bc 13a05000   1120 	movne	r5,0

                    1121 ;393:                                            &directChildName, &remainingName);


                    1122 ;394:         if(lastName)


                    1123 

000005c0 e3550000   1124 	cmp	r5,0

                    1125 ;395:         {


                    1126 

                    1127 ;396:             //Разделитель не найден, значит это последнее имя


                    1128 ;397:             directChildName = fullName;


                    1129 

000005c4 159d2010   1130 	ldrne	r2,[sp,16]

000005c8 159d0014   1131 	ldrne	r0,[sp,20]

000005cc 158d2008   1132 	strne	r2,[sp,8]

000005d0 e1a01006   1133 	mov	r1,r6

000005d4 158d000c   1134 	strne	r0,[sp,12]

                    1135 ;398:         }


                    1136 ;399:         entity = IEDEntity_getChildByName(entity, &directChildName);


                    1137 

000005d8 e1a00004   1138 	mov	r0,r4

000005dc ebffffca*  1139 	bl	IEDEntity_getChildByName

000005e0 e1b04000   1140 	movs	r4,r0

                    1141 ;400:         if(entity == NULL)


                    1142 

000005e4 0a000004   1143 	beq	.L1550

                    1144 ;401:         {


                    1145 

                    1146 ;402:             return NULL;


                    1147 

                    1148 ;403:         }



                                                                      Page 20
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4f81.s
                    1149 ;404:         fullName = remainingName;


                    1150 

000005e8 e89d000c   1151 	ldmfd	[sp],{r2-r3}

000005ec e58d2010   1152 	str	r2,[sp,16]

000005f0 e58d3014   1153 	str	r3,[sp,20]

000005f4 e3550000   1154 	cmp	r5,0

000005f8 0affffe8   1155 	beq	.L1552

                    1156 .L1550:

                    1157 ;405:     } while(!lastName);


                    1158 ;406: 


                    1159 ;407:     return entity;


                    1160 

000005fc e1a00004   1161 	mov	r0,r4

00000600 e28dd018   1162 	add	sp,sp,24

00000604 e8bd80f0   1163 	ldmfd	[sp]!,{r4-r7,pc}

                    1164 	.endf	IEDEntity_getChildByFullName

                    1165 	.align	4

                    1166 ;fullName	[sp,16]	local

                    1167 ;directChildName	[sp,8]	local

                    1168 ;remainingName	[sp]	local

                    1169 ;lastName	r5	local

                    1170 ;entity	r4	local

                    1171 

                    1172 ;parent	r0	param

                    1173 ;name	r1	param

                    1174 

                    1175 	.section ".bss","awb"

                    1176 .L1648:

                    1177 	.data

                    1178 	.text

                    1179 

                    1180 ;408: }


                    1181 

                    1182 ;409: 


                    1183 ;410: IEDEntity IEDEntity_getChildByTag(IEDEntity parent,  uint8_t tag)


                    1184 	.align	4

                    1185 	.align	4

                    1186 IEDEntity_getChildByTag::

                    1187 ;411: {


                    1188 

                    1189 ;412:     IEDEntity child = parent->firstChild;


                    1190 

00000608 e5900004   1191 	ldr	r0,[r0,4]

                    1192 ;413:     while(child != NULL)


                    1193 

0000060c e3500000   1194 	cmp	r0,0

                    1195 ;414:     {


                    1196 

                    1197 ;415:         if(child->tag == tag)


                    1198 

00000610 15d02020   1199 	ldrneb	r2,[r0,32]

00000614 11520001   1200 	cmpne	r2,r1

                    1201 .L1675:

                    1202 ;416:         {


                    1203 

                    1204 ;417:             return child;


                    1205 

                    1206 ;418:         }


                    1207 ;419:         child = child->next;


                    1208 

00000618 1590000c   1209 	ldrne	r0,[r0,12]


                                                                      Page 21
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4f81.s
0000061c 13500000   1210 	cmpne	r0,0

                    1211 ;414:     {


                    1212 

                    1213 ;415:         if(child->tag == tag)


                    1214 

00000620 15d02020   1215 	ldrneb	r2,[r0,32]

00000624 11520001   1216 	cmpne	r2,r1

00000628 1afffffa   1217 	bne	.L1675

                    1218 .L1673:

                    1219 ;420:     }


                    1220 ;421:     return NULL;


                    1221 

0000062c e12fff1e*  1222 	ret	

                    1223 	.endf	IEDEntity_getChildByTag

                    1224 	.align	4

                    1225 ;child	r0	local

                    1226 

                    1227 ;parent	r0	param

                    1228 ;tag	r1	param

                    1229 

                    1230 	.section ".bss","awb"

                    1231 .L1738:

                    1232 	.data

                    1233 	.text

                    1234 

                    1235 ;422: }


                    1236 

                    1237 ;423: 


                    1238 ;424: MmsDataAccessError IEDEntity_write(IEDEntity entity, IsoConnection* isoConn,


                    1239 	.align	4

                    1240 	.align	4

                    1241 IEDEntity_write::

00000630 e92d4000   1242 	stmfd	[sp]!,{lr}

                    1243 ;425:                                    BufferView* value)


                    1244 ;426: {    


                    1245 

                    1246 ;427:     if(entity->readOnly)


                    1247 

00000634 e5d0c022   1248 	ldrb	r12,[r0,34]

00000638 e35c0000   1249 	cmp	r12,0

                    1250 ;428:     {


                    1251 

                    1252 ;429:         return DATA_ACCESS_ERROR_OBJECT_ACCESS_DENIED;


                    1253 

0000063c 13a00003   1254 	movne	r0,3

                    1255 ;430:     }


                    1256 ;431:     return entity->write(entity, isoConn, value);


                    1257 

00000640 0590c064   1258 	ldreq	r12,[r0,100]

00000644 01a0e00f   1259 	moveq	lr,pc

00000648 012fff1c*  1260 	bxeq	r12

0000064c e8bd8000   1261 	ldmfd	[sp]!,{pc}

                    1262 	.endf	IEDEntity_write

                    1263 	.align	4

                    1264 

                    1265 ;entity	r0	param

                    1266 ;isoConn	r1	param

                    1267 ;value	r2	param

                    1268 

                    1269 	.section ".bss","awb"

                    1270 .L1782:


                                                                      Page 22
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4f81.s
                    1271 	.data

                    1272 	.text

                    1273 

                    1274 ;432: }


                    1275 

                    1276 ;433: 


                    1277 ;434: bool IEDEntity_getFullName(IEDEntity entity, BufferView* nameBuf)


                    1278 	.align	4

                    1279 	.align	4

                    1280 IEDEntity_getFullName::

00000650 e92d4030   1281 	stmfd	[sp]!,{r4-r5,lr}

00000654 e1a04000   1282 	mov	r4,r0

                    1283 ;435: {


                    1284 

                    1285 ;436:     char *delimiter;


                    1286 ;437:     if(entity->parent !=NULL && entity->type != IED_ENTITY_LD)


                    1287 

00000658 e5940000   1288 	ldr	r0,[r4]

0000065c e1a05001   1289 	mov	r5,r1

00000660 e3500000   1290 	cmp	r0,0

00000664 15941050   1291 	ldrne	r1,[r4,80]

00000668 13510001   1292 	cmpne	r1,1

0000066c 0a000010   1293 	beq	.L1797

                    1294 ;438:     {


                    1295 

                    1296 ;439:         if(!IEDEntity_getFullName(entity->parent, nameBuf))


                    1297 

00000670 e1a01005   1298 	mov	r1,r5

00000674 ebfffff5*  1299 	bl	IEDEntity_getFullName

00000678 e3500000   1300 	cmp	r0,0

0000067c 0a00000a   1301 	beq	.L1811

                    1302 ;440:         {


                    1303 

                    1304 ;441:             return false;


                    1305 

                    1306 ;442:         }


                    1307 ;443: 


                    1308 ;444:         if(entity->name.len == 0)


                    1309 

00000680 e5940048   1310 	ldr	r0,[r4,72]

00000684 e3500000   1311 	cmp	r0,0

                    1312 ;445:         {


                    1313 

                    1314 ;446:             return true;


                    1315 

00000688 03a00001   1316 	moveq	r0,1

0000068c 0a00000c   1317 	beq	.L1795

                    1318 ;447:         }


                    1319 ;448: 


                    1320 ;449:         switch(entity->type)


                    1321 

00000690 e5940050   1322 	ldr	r0,[r4,80]

00000694 e3500003   1323 	cmp	r0,3

                    1324 ;453:             break;


                    1325 ;454:         default:


                    1326 ;455:             delimiter = "$";


                    1327 

00000698 128f1000*  1328 	adrne	r1,.L1977

                    1329 ;456:             break;


                    1330 ;457:         }


                    1331 ;458:         if(!BufferView_writeStr(nameBuf, delimiter))



                                                                      Page 23
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4f81.s
                    1332 

                    1333 ;450:         {


                    1334 ;451:         case IED_ENTITY_LN:


                    1335 ;452:             delimiter = "/";


                    1336 

0000069c 028f1000*  1337 	adreq	r1,.L1978

                    1338 ;456:             break;


                    1339 ;457:         }


                    1340 ;458:         if(!BufferView_writeStr(nameBuf, delimiter))


                    1341 

000006a0 e1a00005   1342 	mov	r0,r5

000006a4 eb000000*  1343 	bl	BufferView_writeStr

000006a8 e3500000   1344 	cmp	r0,0

                    1345 .L1811:

                    1346 ;459:         {


                    1347 

                    1348 ;460:             ERROR_REPORT("Name buffer overflow");


                    1349 ;461:             return false;


                    1350 

000006ac 03a00000   1351 	moveq	r0,0

000006b0 0a000003   1352 	beq	.L1795

                    1353 .L1797:

                    1354 ;462:         }


                    1355 ;463:     }


                    1356 ;464:     return BufferView_writeStringView(nameBuf, &entity->name);


                    1357 

000006b4 e2841048   1358 	add	r1,r4,72

000006b8 e1a00005   1359 	mov	r0,r5

000006bc e8bd4030   1360 	ldmfd	[sp]!,{r4-r5,lr}

000006c0 ea000000*  1361 	b	BufferView_writeStringView

                    1362 .L1795:

000006c4 e8bd8030   1363 	ldmfd	[sp]!,{r4-r5,pc}

                    1364 	.endf	IEDEntity_getFullName

                    1365 	.align	4

                    1366 ;.L1943	.L1947	static

                    1367 ;.L1944	.L1948	static

                    1368 

                    1369 ;entity	r4	param

                    1370 ;nameBuf	r5	param

                    1371 

                    1372 	.section ".bss","awb"

                    1373 .L1942:

                    1374 	.data

                    1375 	.text

                    1376 

                    1377 ;465: }


                    1378 

                    1379 ;466: 


                    1380 ;467: bool IEDEntity_getFullItemId(IEDEntity entity, BufferView* nameBuf)


                    1381 	.align	4

                    1382 	.align	4

                    1383 IEDEntity_getFullItemId::

000006c8 e92d4030   1384 	stmfd	[sp]!,{r4-r5,lr}

000006cc e1a04000   1385 	mov	r4,r0

                    1386 ;468: {


                    1387 

                    1388 ;469:     if(entity->parent !=NULL && entity->type != IED_ENTITY_LN)


                    1389 

000006d0 e5940000   1390 	ldr	r0,[r4]

000006d4 e1a05001   1391 	mov	r5,r1

000006d8 e3500000   1392 	cmp	r0,0


                                                                      Page 24
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4f81.s
000006dc 15941050   1393 	ldrne	r1,[r4,80]

000006e0 13510003   1394 	cmpne	r1,3

000006e4 0a00000d   1395 	beq	.L1981

                    1396 ;470:     {


                    1397 

                    1398 ;471:         if(!IEDEntity_getFullItemId(entity->parent, nameBuf))


                    1399 

000006e8 e1a01005   1400 	mov	r1,r5

000006ec ebfffff5*  1401 	bl	IEDEntity_getFullItemId

000006f0 e3500000   1402 	cmp	r0,0

000006f4 0a000007   1403 	beq	.L1991

                    1404 ;472:         {


                    1405 

                    1406 ;473:             return false;


                    1407 

                    1408 ;474:         }


                    1409 ;475: 


                    1410 ;476:         if(entity->name.len == 0)


                    1411 

000006f8 e5940048   1412 	ldr	r0,[r4,72]

000006fc e3500000   1413 	cmp	r0,0

                    1414 ;477:         {


                    1415 

                    1416 ;478:             return true;


                    1417 

00000700 03a00001   1418 	moveq	r0,1

00000704 0a000009   1419 	beq	.L1979

                    1420 ;479:         }


                    1421 ;480: 


                    1422 ;481:         if(!BufferView_writeStr(nameBuf, "$"))


                    1423 

00000708 e28f1000*  1424 	adr	r1,.L1977

0000070c e1a00005   1425 	mov	r0,r5

00000710 eb000000*  1426 	bl	BufferView_writeStr

00000714 e3500000   1427 	cmp	r0,0

                    1428 .L1991:

                    1429 ;482:         {


                    1430 

                    1431 ;483:             ERROR_REPORT("Name buffer overflow");


                    1432 ;484:             return false;


                    1433 

00000718 03a00000   1434 	moveq	r0,0

0000071c 0a000003   1435 	beq	.L1979

                    1436 .L1981:

                    1437 ;485:         }


                    1438 ;486:     }


                    1439 ;487:     return BufferView_writeStringView(nameBuf, &entity->name);


                    1440 

00000720 e2841048   1441 	add	r1,r4,72

00000724 e1a00005   1442 	mov	r0,r5

00000728 e8bd4030   1443 	ldmfd	[sp]!,{r4-r5,lr}

0000072c ea000000*  1444 	b	BufferView_writeStringView

                    1445 .L1979:

00000730 e8bd8030   1446 	ldmfd	[sp]!,{r4-r5,pc}

                    1447 	.endf	IEDEntity_getFullItemId

                    1448 	.align	4

                    1449 ;.L2095	.L2098	static

                    1450 

                    1451 ;entity	r4	param

                    1452 ;nameBuf	r5	param

                    1453 


                                                                      Page 25
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4f81.s
                    1454 	.section ".bss","awb"

                    1455 .L2094:

                    1456 	.data

                    1457 	.text

                    1458 

                    1459 ;488: }


                    1460 

                    1461 ;489: 


                    1462 ;490: bool IEDEntity_getDomainId(IEDEntity entity, StringView** name)


                    1463 	.align	4

                    1464 	.align	4

                    1465 IEDEntity_getDomainId::

                    1466 ;491: {


                    1467 

                    1468 ;492:     if(entity->type == IED_ENTITY_LD)


                    1469 

00000734 e5902050   1470 	ldr	r2,[r0,80]

00000738 e3520001   1471 	cmp	r2,1

                    1472 ;493:     {


                    1473 

                    1474 ;494:         *name = &entity->name;


                    1475 

0000073c 02800048   1476 	addeq	r0,r0,72

00000740 05810000   1477 	streq	r0,[r1]

                    1478 ;495:         return true;


                    1479 

00000744 03a00001   1480 	moveq	r0,1

00000748 0a000003   1481 	beq	.L2123

0000074c e5902000   1482 	ldr	r2,[r0]

00000750 e1b00002   1483 	movs	r0,r2

                    1484 ;496:     }


                    1485 ;497:     if(entity->parent ==NULL )


                    1486 

                    1487 ;500:     }


                    1488 ;501: 


                    1489 ;502:     return IEDEntity_getDomainId(entity->parent, name);


                    1490 

00000754 1afffff6   1491 	bne	IEDEntity_getDomainId

                    1492 ;498:     {


                    1493 

                    1494 ;499:         return false;


                    1495 

00000758 e20000ff   1496 	and	r0,r0,255

                    1497 .L2123:

0000075c e12fff1e*  1498 	ret	

                    1499 	.endf	IEDEntity_getDomainId

                    1500 	.align	4

                    1501 

                    1502 ;entity	r0	param

                    1503 ;name	r1	param

                    1504 

                    1505 	.section ".bss","awb"

                    1506 .L2202:

                    1507 	.data

                    1508 	.text

                    1509 

                    1510 ;503: }


                    1511 

                    1512 ;504: 


                    1513 ;505: void IEDEntity_attachTimeStamp(IEDEntity entity, TimeStamp* timeStamp)


                    1514 	.align	4


                                                                      Page 26
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4f81.s
                    1515 	.align	4

                    1516 IEDEntity_attachTimeStamp::

00000760 e92d4030   1517 	stmfd	[sp]!,{r4-r5,lr}

00000764 e1a05001   1518 	mov	r5,r1

                    1519 ;506: {


                    1520 

                    1521 ;507:     IEDEntity child;


                    1522 ;508: 


                    1523 ;509:     if(entity->type == IED_ENTITY_DA_TIMESTAMP)


                    1524 

00000768 e5901050   1525 	ldr	r1,[r0,80]

0000076c e3510009   1526 	cmp	r1,9

                    1527 ;510:     {


                    1528 

                    1529 ;511:         return;


                    1530 

                    1531 ;512:     }


                    1532 ;513:     entity->timeStamp = timeStamp;


                    1533 

00000770 15904004   1534 	ldrne	r4,[r0,4]

                    1535 ;516:     while(child != NULL)


                    1536 

00000774 15805044   1537 	strne	r5,[r0,68]

                    1538 ;514: 


                    1539 ;515:     child = entity->firstChild;


                    1540 

00000778 13540000   1541 	cmpne	r4,0

0000077c 0a000005   1542 	beq	.L2221

                    1543 .L2228:

                    1544 ;517:     {


                    1545 

                    1546 ;518:         IEDEntity_attachTimeStamp(child, timeStamp);


                    1547 

00000780 e1a01005   1548 	mov	r1,r5

00000784 e1a00004   1549 	mov	r0,r4

00000788 ebfffff4*  1550 	bl	IEDEntity_attachTimeStamp

                    1551 ;519:         child = child->next;


                    1552 

0000078c e594400c   1553 	ldr	r4,[r4,12]

00000790 e3540000   1554 	cmp	r4,0

00000794 1afffff9   1555 	bne	.L2228

                    1556 .L2221:

00000798 e8bd8030   1557 	ldmfd	[sp]!,{r4-r5,pc}

                    1558 	.endf	IEDEntity_attachTimeStamp

                    1559 	.align	4

                    1560 ;child	r4	local

                    1561 

                    1562 ;entity	r0	param

                    1563 ;timeStamp	r5	param

                    1564 

                    1565 	.section ".bss","awb"

                    1566 .L2276:

                    1567 	.data

                    1568 	.text

                    1569 

                    1570 ;520:     }


                    1571 ;521: }


                    1572 

                    1573 ;522: 


                    1574 ;523: void IEDEntity_writeFullName(IEDEntity entity, BufferView* bv )


                    1575 	.align	4


                                                                      Page 27
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4f81.s
                    1576 	.align	4

                    1577 IEDEntity_writeFullName::

0000079c e92d4030   1578 	stmfd	[sp]!,{r4-r5,lr}

000007a0 e1a05000   1579 	mov	r5,r0

                    1580 ;524: {


                    1581 

                    1582 ;525:     if(entity->parent !=NULL)


                    1583 

000007a4 e5950000   1584 	ldr	r0,[r5]

000007a8 e1a04001   1585 	mov	r4,r1

000007ac e3500000   1586 	cmp	r0,0

000007b0 0a000003   1587 	beq	.L2293

                    1588 ;526:     {


                    1589 

                    1590 ;527:         IEDEntity_writeFullName(entity->parent, bv);


                    1591 

000007b4 ebfffff8*  1592 	bl	IEDEntity_writeFullName

                    1593 ;528:         BufferView_writeStr(bv, "/");


                    1594 

000007b8 e28f1000*  1595 	adr	r1,.L1978

000007bc e1a00004   1596 	mov	r0,r4

000007c0 eb000000*  1597 	bl	BufferView_writeStr

                    1598 .L2293:

                    1599 ;529:     }


                    1600 ;530:     BufferView_writeStringView(bv, &entity->name);


                    1601 

000007c4 e2851048   1602 	add	r1,r5,72

000007c8 e1a00004   1603 	mov	r0,r4

000007cc e8bd4030   1604 	ldmfd	[sp]!,{r4-r5,lr}

000007d0 ea000000*  1605 	b	BufferView_writeStringView

                    1606 	.endf	IEDEntity_writeFullName

                    1607 	.align	4

                    1608 ;.L2334	.L2337	static

                    1609 

                    1610 ;entity	r5	param

                    1611 ;bv	r4	param

                    1612 

                    1613 	.section ".bss","awb"

                    1614 .L2333:

                    1615 	.data

                    1616 	.text

                    1617 

                    1618 ;531: }


                    1619 

                    1620 ;532: 


                    1621 ;533: void IEDEntity_printFullName(IEDEntity entity)


                    1622 	.align	4

                    1623 	.align	4

                    1624 IEDEntity_printFullName::

000007d4 e92d4030   1625 	stmfd	[sp]!,{r4-r5,lr}

                    1626 ;534: {


                    1627 

                    1628 ;535:     BufferView nameBuf;


                    1629 ;536:     void* p = malloc(257);


                    1630 

000007d8 e24dd00c   1631 	sub	sp,sp,12

000007dc e1a05000   1632 	mov	r5,r0

000007e0 e3a00f40   1633 	mov	r0,256

000007e4 e2800001   1634 	add	r0,r0,1

000007e8 eb000000*  1635 	bl	malloc

000007ec e1b04000   1636 	movs	r4,r0


                                                                      Page 28
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4f81.s
                    1637 ;537:     if(p == NULL)


                    1638 

000007f0 0a00000d   1639 	beq	.L2346

                    1640 ;538:     {


                    1641 

                    1642 ;539:         return;


                    1643 

                    1644 ;540:     }


                    1645 ;541:     memset(p, 0, 257);


                    1646 

000007f4 e3a02f40   1647 	mov	r2,256

000007f8 e2822001   1648 	add	r2,r2,1

000007fc e3a01000   1649 	mov	r1,0

00000800 eb000000*  1650 	bl	memset

                    1651 ;542:     BufferView_init(&nameBuf, p, 256, 0);


                    1652 

00000804 e1a01004   1653 	mov	r1,r4

00000808 e1a0000d   1654 	mov	r0,sp

0000080c e3a03000   1655 	mov	r3,0

00000810 e3a02f40   1656 	mov	r2,256

00000814 eb000000*  1657 	bl	BufferView_init

                    1658 ;543:     IEDEntity_writeFullName(entity, &nameBuf);	//TRACE(nameBuf.p);


                    1659 

00000818 e1a0100d   1660 	mov	r1,sp

0000081c e1a00005   1661 	mov	r0,r5

00000820 ebffffdd*  1662 	bl	IEDEntity_writeFullName

                    1663 ;544:     TRACE((char*)nameBuf.p);


                    1664 ;545:     free(p);


                    1665 

00000824 e1a00004   1666 	mov	r0,r4

00000828 eb000000*  1667 	bl	free

                    1668 .L2346:

0000082c e28dd00c   1669 	add	sp,sp,12

00000830 e8bd8030   1670 	ldmfd	[sp]!,{r4-r5,pc}

                    1671 	.endf	IEDEntity_printFullName

                    1672 	.align	4

                    1673 ;nameBuf	[sp]	local

                    1674 ;p	r4	local

                    1675 

                    1676 ;entity	r5	param

                    1677 

                    1678 	.section ".bss","awb"

                    1679 .L2376:

                    1680 	.data

                    1681 	.text

                    1682 

                    1683 ;546: }


                    1684 

                    1685 ;547: 


                    1686 ;548: void IEDEntity_setTimeStamp(IEDEntity entity, uint64_t timeStamp)


                    1687 	.align	4

                    1688 	.align	4

                    1689 IEDEntity_setTimeStamp::

                    1690 ;549: {


                    1691 

                    1692 ;550:     if(entity->timeStamp != NULL)


                    1693 

00000834 e5900044   1694 	ldr	r0,[r0,68]

00000838 e3500000   1695 	cmp	r0,0

                    1696 ;551:     {


                    1697 


                                                                      Page 29
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4f81.s
                    1698 ;552:         entity->timeStamp->timeStamp = timeStamp;


                    1699 

0000083c 18800006   1700 	stmneea	[r0],{r1-r2}

00000840 e12fff1e*  1701 	ret	

                    1702 	.endf	IEDEntity_setTimeStamp

                    1703 	.align	4

                    1704 .L1977:

                    1705 ;	"$\000"

00000844 0024      1706 	.data.b	36,0

00000846 0000      1707 	.align 4

                    1708 

                    1709 	.type	.L1977,$object

                    1710 	.size	.L1977,4

                    1711 

                    1712 .L1978:

                    1713 ;	"/\000"

00000848 002f      1714 	.data.b	47,0

0000084a 0000      1715 	.align 4

                    1716 

                    1717 	.type	.L1978,$object

                    1718 	.size	.L1978,4

                    1719 

                    1720 	.align	4

                    1721 

                    1722 ;entity	r0	param

                    1723 ;timeStamp	r1	param

                    1724 

                    1725 	.section ".bss","awb"

                    1726 .L2424:

                    1727 	.data

                    1728 	.text

                    1729 

                    1730 ;553:     }


                    1731 ;554: }


                    1732 

                    1733 ;555: 


                    1734 ;556: TrgOps IEDEntity_findChanges(IEDEntity entity, TrgOps trgOps)


                    1735 	.align	4

                    1736 	.align	4

                    1737 IEDEntity_findChanges::

0000084c e92d4030   1738 	stmfd	[sp]!,{r4-r5,lr}

00000850 e1a02000   1739 	mov	r2,r0

                    1740 ;557: {


                    1741 

                    1742 ;558:     IEDEntity child;


                    1743 ;559:     TrgOps change;


                    1744 ;560: 


                    1745 ;561:     change = (TrgOps)(entity->changed & trgOps);


                    1746 

00000854 e5920028   1747 	ldr	r0,[r2,40]

00000858 e1a05001   1748 	mov	r5,r1

0000085c e0150000   1749 	ands	r0,r5,r0

                    1750 ;562: 


                    1751 ;563:     if(change)


                    1752 

                    1753 .L2438:

                    1754 ;564:     {


                    1755 

                    1756 ;565:         return change;


                    1757 

00000860 1a00000b   1758 	bne	.L2435


                                                                      Page 30
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4f81.s
                    1759 .L2437:

                    1760 ;566:     }


                    1761 ;567: 


                    1762 ;568:     child = entity->firstChild;


                    1763 

00000864 e5924004   1764 	ldr	r4,[r2,4]

                    1765 ;569: 


                    1766 ;570:     while(child != NULL)


                    1767 

00000868 e3540000   1768 	cmp	r4,0

0000086c e1a00004   1769 	mov	r0,r4

00000870 0a000007   1770 	beq	.L2435

                    1771 .L2442:

                    1772 ;571:     {


                    1773 

                    1774 ;572:         change = IEDEntity_findChanges(child, trgOps);


                    1775 

00000874 e1a01005   1776 	mov	r1,r5

00000878 ebfffff3*  1777 	bl	IEDEntity_findChanges

                    1778 ;573:         if(change)


                    1779 

0000087c e3500000   1780 	cmp	r0,0

00000880 1afffff6   1781 	bne	.L2438

                    1782 ;574:         {


                    1783 

                    1784 ;575:             return change;


                    1785 

                    1786 ;576:         }


                    1787 ;577:         child = child->next;


                    1788 

00000884 e594400c   1789 	ldr	r4,[r4,12]

00000888 e3540000   1790 	cmp	r4,0

0000088c e1a00004   1791 	mov	r0,r4

00000890 1afffff7   1792 	bne	.L2442

                    1793 ;578:     }


                    1794 ;579:     return TRGOP_NONE;


                    1795 

                    1796 .L2435:

00000894 e8bd8030   1797 	ldmfd	[sp]!,{r4-r5,pc}

                    1798 	.endf	IEDEntity_findChanges

                    1799 	.align	4

                    1800 ;child	r4	local

                    1801 ;change	r0	local

                    1802 

                    1803 ;entity	r2	param

                    1804 ;trgOps	r5	param

                    1805 

                    1806 	.section ".bss","awb"

                    1807 .L2528:

                    1808 	.data

                    1809 	.text

                    1810 

                    1811 ;580: }


                    1812 

                    1813 ;581: 


                    1814 ;582: void IEDEntity_setReadOnlyRecursive(IEDEntity entity, bool readOnly)


                    1815 	.align	4

                    1816 	.align	4

                    1817 IEDEntity_setReadOnlyRecursive::

00000898 e92d4030   1818 	stmfd	[sp]!,{r4-r5,lr}

                    1819 ;583: {



                                                                      Page 31
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4f81.s
                    1820 

                    1821 ;584:     IEDEntity child;


                    1822 ;585: 


                    1823 ;586:     entity->readOnly = readOnly;


                    1824 

0000089c e1a05001   1825 	mov	r5,r1

000008a0 e5904004   1826 	ldr	r4,[r0,4]

                    1827 ;588:     while(child != NULL)


                    1828 

000008a4 e5c05022   1829 	strb	r5,[r0,34]

                    1830 ;587:     child = entity->firstChild;


                    1831 

000008a8 e3540000   1832 	cmp	r4,0

000008ac 0a000005   1833 	beq	.L2546

                    1834 .L2550:

                    1835 ;589:     {


                    1836 

                    1837 ;590:         IEDEntity_setReadOnlyRecursive(child, readOnly);


                    1838 

000008b0 e1a01005   1839 	mov	r1,r5

000008b4 e1a00004   1840 	mov	r0,r4

000008b8 ebfffff6*  1841 	bl	IEDEntity_setReadOnlyRecursive

                    1842 ;591:         child = child->next;


                    1843 

000008bc e594400c   1844 	ldr	r4,[r4,12]

000008c0 e3540000   1845 	cmp	r4,0

000008c4 1afffff9   1846 	bne	.L2550

                    1847 .L2546:

000008c8 e8bd8030   1848 	ldmfd	[sp]!,{r4-r5,pc}

                    1849 	.endf	IEDEntity_setReadOnlyRecursive

                    1850 	.align	4

                    1851 ;child	r4	local

                    1852 

                    1853 ;entity	r0	param

                    1854 ;readOnly	r5	param

                    1855 

                    1856 	.section ".bss","awb"

                    1857 .L2582:

                    1858 	.data

                    1859 	.text

                    1860 

                    1861 ;592:     }


                    1862 ;593: }


                    1863 

                    1864 ;594: 


                    1865 ;595: bool IEDEntity_isFinalDA(IEDEntity entity)


                    1866 	.align	4

                    1867 	.align	4

                    1868 IEDEntity_isFinalDA::

000008cc e1a01000   1869 	mov	r1,r0

                    1870 ;596: {


                    1871 

                    1872 ;597:     return entity->type == IED_ENTITY_DA_TERMINAL_ITEM


                    1873 

000008d0 e5911050   1874 	ldr	r1,[r1,80]

000008d4 e3a00001   1875 	mov	r0,1

000008d8 e3510008   1876 	cmp	r1,8

000008dc 13510009   1877 	cmpne	r1,9

000008e0 13510007   1878 	cmpne	r1,7

000008e4 1351000a   1879 	cmpne	r1,10

000008e8 13a00000   1880 	movne	r0,0


                                                                      Page 32
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4f81.s
000008ec e20000ff   1881 	and	r0,r0,255

000008f0 e12fff1e*  1882 	ret	

                    1883 	.endf	IEDEntity_isFinalDA

                    1884 	.align	4

                    1885 

                    1886 ;entity	r1	param

                    1887 

                    1888 	.section ".bss","awb"

                    1889 .L2662:

                    1890 	.data

                    1891 	.text

                    1892 

                    1893 ;598:         || entity->type == IED_ENTITY_DA_TIMESTAMP


                    1894 ;599:         || entity->type == IED_ENTITY_DA_CONST


                    1895 ;600:         || entity->type == IED_ENTITY_DA_VAR;


                    1896 ;601: }


                    1897 	.align	4

                    1898 	.align	4

                    1899 updateFromDataSliceStub:

                    1900 ;34: {


                    1901 

000008f4 e12fff1e*  1902 	ret	

                    1903 	.endf	updateFromDataSliceStub

                    1904 	.align	4

                    1905 

                    1906 ;da	none	param

                    1907 

                    1908 	.section ".bss","awb"

                    1909 .L2702:

                    1910 	.data

                    1911 	.text

                    1912 	.align	4

                    1913 

                    1914 	.data

                    1915 	.ghsnote version,6

                    1916 	.ghsnote tools,3

                    1917 	.ghsnote options,0

                    1918 	.text

                    1919 	.align	4

