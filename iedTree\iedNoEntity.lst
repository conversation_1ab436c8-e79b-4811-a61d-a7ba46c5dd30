                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_doo1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=iedNoEntity.c -o iedTree\gh_doo1.o -list=iedTree/iedNoEntity.lst C:\Users\<USER>\AppData\Local\Temp\gh_doo1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_doo1.s
Source File: iedNoEntity.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      10 ;		iedTree/iedNoEntity.c -o iedTree/iedNoEntity.o

                      11 ;Source File:   iedTree/iedNoEntity.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Oct 07 09:39:22 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "iedNoEntity.h"


                      21 ;2: 


                      22 ;3: #include "../bufView.h"


                      23 ;4: 


                      24 ;5: #include <string.h>


                      25 ;6: 


                      26 ;7: 


                      27 ;8: static struct IEDEntityStruct noEntity;


                      28 ;9: 


                      29 ;10: static bool calcReadLen(IEDEntity entity, size_t* pLen )


                      30 ;11: {


                      31 ;12:     *pLen = 3;


                      32 ;13:     return true;


                      33 ;14: }


                      34 ;15: 


                      35 ;16: static bool encodeRead(IEDEntity entity, BufferView* outBuf)


                      36 ;17: {


                      37 ;18:     // При чтении создаёт код object-non-existent


                      38 ;19:     return BufferView_writeData(outBuf, "\x80\x01\x0A", 3);


                      39 ;20: }


                      40 ;21: 


                      41 ;22: static MmsDataAccessError write(IEDEntity entity, IsoConnection* isoConn,


                      42 

                      43 ;26: }


                      44 

                      45 ;27: 


                      46 ;28: 


                      47 ;29: void IEDNoEntity_init(void)


                      48 ;30: {


                      49 ;31:     memset(&noEntity, 0, sizeof(noEntity));


                      50 ;32:     noEntity.type = IED_ENTITY_NONE;



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_doo1.s
                      51 ;33:     noEntity.encodeRead = encodeRead;


                      52 ;34:     noEntity.calcReadLen = calcReadLen;


                      53 ;35:     noEntity.write = write;


                      54 ;36: }


                      55 ;37: 


                      56 ;38: IEDEntity IEDNoEntity_get(void)


                      57 

                      58 ;41: }


                      59 

                      60 	.text

                      61 	.align	4

                      62 calcReadLen:

00000000 e3a00003     63 	mov	r0,3

00000004 e5810000     64 	str	r0,[r1]

00000008 e3a00001     65 	mov	r0,1

0000000c e12fff1e*    66 	ret	

                      67 	.endf	calcReadLen

                      68 	.align	4

                      69 

                      70 ;entity	none	param

                      71 ;pLen	r1	param

                      72 

                      73 	.section ".bss","awb"

                      74 .L62:

                      75 	.data

                      76 	.text

                      77 

                      78 

                      79 	.align	4

                      80 	.align	4

                      81 encodeRead:

00000010 e92d4000     82 	stmfd	[sp]!,{lr}

00000014 e1a00001     83 	mov	r0,r1

00000018 e59f1058*    84 	ldr	r1,.L106

0000001c e3a02003     85 	mov	r2,3

00000020 eb000000*    86 	bl	BufferView_writeData

00000024 e20000ff     87 	and	r0,r0,255

00000028 e8bd4000     88 	ldmfd	[sp]!,{lr}

0000002c e12fff1e*    89 	ret	

                      90 	.endf	encodeRead

                      91 	.align	4

                      92 ;.L98	.L101	static

                      93 

                      94 ;entity	none	param

                      95 ;outBuf	r2	param

                      96 

                      97 	.section ".bss","awb"

                      98 .L97:

                      99 	.section ".rodata","a"

                     100 .L101:;	"\200\001\n\000"

00000000 000a0180    101 	.data.b	128,1,10,0

                     102 	.type	.L101,$object

                     103 	.size	.L101,4

                     104 	.data

                     105 	.text

                     106 

                     107 

                     108 	.align	4

                     109 	.align	4

                     110 	.align	4

                     111 IEDNoEntity_init::


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_doo1.s
00000030 e92d4070    112 	stmfd	[sp]!,{r4-r6,lr}

00000034 e59f5040*   113 	ldr	r5,.L133

00000038 e59f4040*   114 	ldr	r4,.L134

0000003c e59f6040*   115 	ldr	r6,.L135

00000040 e1a00004    116 	mov	r0,r4

00000044 e3a0206c    117 	mov	r2,108

00000048 e3a01000    118 	mov	r1,0

0000004c eb000000*   119 	bl	memset

00000050 e3a00000    120 	mov	r0,0

00000054 e5840050    121 	str	r0,[r4,80]

00000058 e59fc028*   122 	ldr	r12,.L136

0000005c e284405c    123 	add	r4,r4,92

00000060 e8841060    124 	stmea	[r4],{r5-r6,r12}

00000064 e8bd8070    125 	ldmfd	[sp]!,{r4-r6,pc}

                     126 	.endf	IEDNoEntity_init

                     127 	.align	4

                     128 

                     129 	.section ".bss","awb"

                     130 .L126:

00000000 00000000    131 noEntity:	.space	108

00000004 00000000 
00000008 00000000 
0000000c 00000000 
00000010 00000000 
00000014 00000000 
00000018 00000000 
0000001c 00000000 
00000020 00000000 
00000024 00000000 
00000028 00000000 
0000002c 00000000 
00000030 00000000 
00000034 00000000 
00000038 00000000 
0000003c 00000000 
00000040 00000000 
00000044 00000000 
00000048 00000000 
0000004c 00000000 
00000050 00000000 
00000054 00000000 
00000058 00000000 
0000005c 00000000 
00000060 00000000 
00000064 00000000 
00000068 00000000 
                     132 	.data

                     133 	.text

                     134 

                     135 	.align	4

                     136 	.align	4

                     137 write:

                     138 ;23:                                BufferView* value)


                     139 ;24: {


                     140 

                     141 ;25:     return DATA_ACCESS_ERROR_OBJECT_NONE_EXISTENT;


                     142 

00000068 e3a0000a    143 	mov	r0,10

0000006c e12fff1e*   144 	ret	

                     145 	.endf	write

                     146 	.align	4


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_doo1.s
                     147 

                     148 ;entity	none	param

                     149 ;isoConn	none	param

                     150 ;value	none	param

                     151 

                     152 	.section ".bss","awb"

                     153 .L158:

                     154 	.data

                     155 	.text

                     156 	.align	4

                     157 	.align	4

                     158 IEDNoEntity_get::

                     159 ;39: {


                     160 

                     161 ;40:     return &noEntity;


                     162 

00000070 e59f0008*   163 	ldr	r0,.L134

00000074 e12fff1e*   164 	ret	

                     165 	.endf	IEDNoEntity_get

                     166 	.align	4

                     167 

                     168 	.data

                     169 	.text

                     170 	.align	4

                     171 .L106:

00000078 00000000*   172 	.data.w	.L101

                     173 	.type	.L106,$object

                     174 	.size	.L106,4

                     175 

                     176 .L133:

0000007c 00000000*   177 	.data.w	encodeRead

                     178 	.type	.L133,$object

                     179 	.size	.L133,4

                     180 

                     181 .L134:

00000080 00000000*   182 	.data.w	noEntity

                     183 	.type	.L134,$object

                     184 	.size	.L134,4

                     185 

                     186 .L135:

00000084 00000000*   187 	.data.w	calcReadLen

                     188 	.type	.L135,$object

                     189 	.size	.L135,4

                     190 

                     191 .L136:

00000088 00000000*   192 	.data.w	write

                     193 	.type	.L136,$object

                     194 	.size	.L136,4

                     195 

                     196 	.align	4

                     197 ;noEntity	noEntity	static

                     198 

                     199 	.data

                     200 	.ghsnote version,6

                     201 	.ghsnote tools,3

                     202 	.ghsnote options,0

                     203 	.text

                     204 	.align	4

                     205 	.section ".bss","awb"

                     206 	.align	4

                     207 	.section ".rodata","a"


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_doo1.s
                     208 	.align	4

                     209 	.text

