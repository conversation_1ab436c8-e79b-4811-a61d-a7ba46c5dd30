#ifndef  __ASSOCIATIONASNTYPES_H__
#define  __ASSOCIATIONASNTYPES_H__

#include "BaseAsnTypes.h"


#define	ASN_PARAMETERSUPPORTOPTIONS_SIZE	18
#define	ASN_SERVICESUPPORTOPTIONS_SIZE		93

//ASN_ParameterSupportOptions										стр.41
typedef struct														//ParameterSupportOptions ::= BIT STRING {
{

	unsigned char	str1  : 1;										//str1	(0),
	unsigned char	str2  : 1;										//str2	(1),
	unsigned char	vnam  : 1;										//vnam	(2),
	unsigned char	valt  : 1;										//valt	(3),
	unsigned char	vadr  : 1;										//vadr	(4),
	//-- bit 5 is reserved for the service defined in Annex E.
	unsigned char	vcsa  : 1;										//vcsa	(5),
	unsigned char	tpy   : 1;										//tpy	(6),
	unsigned char	vlis  : 1;										//vlis	(7),
	unsigned char	rsv8  : 1;										//-- bit 8 is reserved
	unsigned char	rsv9  : 1;										//-- bit 9 is reserved
	unsigned char	cei   : 1;										//cei	(10),
	unsigned char	aco   : 1;										//aco	(11),
	unsigned char	sem   : 1;										//sem	(12),
	unsigned char	csr   : 1;										//csr	(13),
	unsigned char	csnc  : 1;										//csnc	(14),
	unsigned char	csplc : 1;										//csplc	(15),
	unsigned char	cspi  : 1;										//cspi	(16),
	unsigned char	charr : 1;										//char	(17),

}ASN_ParameterSupportOptions;										//} (SIZE(18))

//ASN_ServiceSupportOptions										стр.40
typedef struct														//ServiceSupportOptions ::= BIT STRING {
{

	unsigned char	status	 						: 1;			//status							(0),
	unsigned char	getNameList						: 1;			//getNameList						(1),
	unsigned char	identify					    : 1;			//identify							(2),
	unsigned char	rename							: 1;			//rename							(3),
	unsigned char	read							: 1;			//read								(4),
	unsigned char	write							: 1;			//write								(5),
	unsigned char	getVariableAccessAttributes     : 1;			//getVariableAccessAttributes		(6),
	unsigned char	defineNamedVariable				: 1;			//defineNamedVariable				(7),
	//-- bit 8 is reserved for use of a service defined in Annex E.
	unsigned char	defineScatteredAccess			: 1;			//defineScatteredAccess				(8),
	//-- bit 9 is reserved for use of a service defined in Annex E.
	unsigned char	getScatteredAccessAttributes	: 1;			//getScatteredAccessAttributes		(9),
	unsigned char	deleteVariableAccess			: 1;			//deleteVariableAccess				(10),
	unsigned char	defineNamedVariableList			: 1;			//defineNamedVariableList			(11),
	unsigned char	getNamedVariableListAttributes  : 1;			//getNamedVariableListAttributes	(12),
	unsigned char	deleteNamedVariableList			: 1;			//deleteNamedVariableList			(13),
	unsigned char	defineNamedType					: 1;			//defineNamedType					(14),
	unsigned char	getNamedTypeAttributes			: 1;			//getNamedTypeAttributes			(15),
	unsigned char	deleteNamedType					: 1;			//deleteNamedType					(16),
	unsigned char	input							: 1;			//input								(17),
	unsigned char	output							: 1;			//output							(18),
	unsigned char	takeControl						: 1;			//takeControl						(19),
	unsigned char	relinquishControl				: 1;			//relinquishControl					(20),
	unsigned char	defineSemaphore					: 1;			//defineSemaphore					(21),
	unsigned char	deleteSemaphore					: 1;			//deleteSemaphore					(22),
	unsigned char	reportSemaphoreStatus			: 1;			//reportSemaphoreStatus				(23),
	unsigned char	reportPoolSemaphoreStatus		: 1;			//reportPoolSemaphoreStatus			(24),
	unsigned char	reportSemaphoreEntryStatus		: 1;			//reportSemaphoreEntryStatus		(25),
	unsigned char	initiateDownloadSequence		: 1;			//initiateDownloadSequence			(26),
	unsigned char	downloadSegment					: 1;			//downloadSegment					(27),
	unsigned char	terminateDownloadSequece		: 1;			//terminateDownloadSequece			(28),
	unsigned char	initiateUploadSequece			: 1;			//initiateUploadSequece				(29),
	unsigned char	uploadSegment					: 1;			//uploadSegment						(30),
	unsigned char	terminateUploadSequece			: 1;			//terminateUploadSequece			(31),
	unsigned char	requestDomainDownload			: 1;			//requestDomainDownload				(32),
	unsigned char	requestDomainUpload				: 1;			//requestDomainUpload				(33),
	unsigned char	loadDomainContent				: 1;			//loadDomainContent					(34),
	unsigned char	storeDomainContent				: 1;			//storeDomainContent				(35),
	unsigned char	deleteDomain					: 1;			//deleteDomain						(36),
	unsigned char	getDomainAttributes				: 1;			//getDomainAttributes				(37),
	unsigned char	createProgramInvocation			: 1;			//createProgramInvocation			(38),
	unsigned char	deleteProgramInvocation			: 1;			//deleteProgramInvocation			(39),
	unsigned char	start							: 1;			//start								(40),
	unsigned char	stop							: 1;			//stop								(41),
	unsigned char	resume							: 1;			//resume							(42),
	unsigned char	reset							: 1;			//reset								(43),
	unsigned char	kill							: 1;			//kill								(44),
	unsigned char	getProgramInvocationAttributes	: 1;			//getProgramInvocationAttributes	(45),
	unsigned char	obtainFile						: 1;			//obtainFile						(46),
	unsigned char	defineEventCondition			: 1;			//defineEventCondition				(47),
	unsigned char	deleteEventCondition			: 1;			//deleteEventCondition				(48),
	unsigned char	getEventConditionAttributes		: 1;			//getEventConditionAttributes		(49),
	unsigned char	reportEventConditionStatus		: 1;			//reportEventConditionStatus		(50),
	unsigned char	alterEventConditionMonitoring	: 1;			//alterEventConditionMonitoring		(51),
	unsigned char	triggerEvent					: 1;			//triggerEvent						(52),
	unsigned char	defineEventAction				: 1;			//defineEventAction					(53),
	unsigned char	deleteEventAction				: 1;			//deleteEventAction					(54),
	unsigned char	getEventActionAttributes		: 1;			//getEventActionAttributes			(55),
	unsigned char	reportEventActionStatus			: 1;			//reportEventActionStatus			(56),
	unsigned char	defineEventEnrollment			: 1;			//defineEventEnrollment				(57),
	unsigned char	deleteEventEnrollment			: 1;			//deleteEventEnrollment				(58),
	unsigned char	alterEventEnrollment			: 1;			//alterEventEnrollment				(59),
	unsigned char	reportEventEnrollmentStatus		: 1;			//reportEventEnrollmentStatus		(60),
	unsigned char	getEventEnrollmentAttributes	: 1;			//getEventEnrollmentAttributes		(61),
	unsigned char	acknowledgeEventNotification	: 1;			//acknowledgeEventNotification		(62),
	unsigned char	getAlarmSummary					: 1;			//getAlarmSummary					(63),
	unsigned char	getAlarmEnrollmentSummary		: 1;			//getAlarmEnrollmentSummary			(64),
	unsigned char	redJournal						: 1;			//redJounal							(65),
	unsigned char	writeJournal					: 1;			//writeJournal						(66),
	unsigned char	initializeJournal				: 1;			//initializeJournal					(67),
	unsigned char	reportJournelStatus				: 1;			//reportJournelStatus				(68),
	unsigned char	createJournal					: 1;			//createJournal						(69),
	unsigned char	deleteJournal					: 1;			//deleteJournal						(70),
	unsigned char	getCapabilityList				: 1;			//getCapabilityList					(71),
	//-- bit 72 is reserved for use of a service defined in Annex D.
	unsigned char	fileOpen						: 1;			//fileOpen							(72),
	//-- bit 73 is reserved for use of a service defined in Annex D.
	unsigned char	fileRead						: 1;			//fileRead							(73),
	//-- bit 74 is reserved for use of a service defined in Annex D.
	unsigned char	fileClose						: 1;			//fileClose							(74),
	//-- bit 75 is reserved for use of a service defined in Annex D.
	unsigned char	fileRename						: 1;			//fileRename						(75),
	//-- bit 76 is reserved for use of a service defined in Annex D.
	unsigned char	fileDelete						: 1;			//fileDelete						(76),
	//-- bit 77 is reserved for use of a service defined in Annex D.
	unsigned char	fileDirectory					: 1;			//fileDirectory						(77),
	unsigned char	unsolicitedStatus				: 1;			//unsolicitedStatus					(78),
	unsigned char	informationReport				: 1;			//informationReport					(79),
	unsigned char	eventNotification				: 1;			//eventNotification					(80),
	unsigned char	attachToEventCondition			: 1;			//attachToEventCondition			(81),
	unsigned char	attachToSemaphore				: 1;			//attachToSemaphore					(82),
	unsigned char	conclude						: 1;			//conclude							(83),
	unsigned char	cancel							: 1;			//cancel							(84),
	unsigned char	getDataExchangeAttributes		: 1;			//getDataExchangeAttributes			(85),
	//-- Shall not appear in minor version one
	unsigned char	exchangeData					: 1;			//exchangeData						(86),
	//-- Shall not appear in minor version one
	unsigned char	defineAccessControlList			: 1;			//defineAccessControlList			(87),
	//-- Shall not appear in minor version one or two
	unsigned char	getAccessControlListAttributes	: 1;			//getAccessControlListAttributes	(88),
	//-- Shall not appear in minor version one or two
	unsigned char	reportAccessControlledObjects	: 1;			//reportAccessControlledObjects		(89),
	//-- Shall not appear in minor version one or two
	unsigned char	deleteAccessControlList			: 1;			//deleteAccessControlList			(90),
	//-- Shall not appear in minor version one or two
	unsigned char	alterAccessControl				: 1;			//alterAccessControl				(91),
	//-- Shall not appear in minor version one or two
	unsigned char	reconfigerProgramInvocation		: 1;			//reconfigerProgramInvocation		(92)

}ASN_ServiceSupportOptions;										//} (SIZE(93))

//ASN_Initiate_RequestPDU											стр.81
typedef struct														//Initiate_RequestPDU ::= SEQUENCE {	
{																	//
																	//
	ASN_Integer32	localDetailCalling;								//localDetailCalling				[0] IMPLICIT Integer32 OPTIONAL,
	ASN_Integer16	proposedMaxServOutstandingCalling;				//proposedMaxServOutstandingCalling [1] IMPLICIT Integer16,
	ASN_Integer16	proposedMaxServOutstandingCalled;				//proposedMaxServOutstandingCalled  [2] IMPLICIT Integer16,
	ASN_Integer8	proposedDataStructureNestingLevel;				//proposedDataStructureNestingLevel [3] IMPLICIT Integer8 OPTIONAL,
	struct															//initRequestDetail					[4] IMPLICIT SEQUENCE {
	{

		ASN_Integer16					proposedVersionNumber;			//proposedVersionNumber		[0] IMPLICIT Integer16,
		ASN_ParameterSupportOptions		proposedParameterCBB;			//proposedParameterCBB		[1] IMPLICIT ParameterSupportOptions,
		ASN_ServiceSupportOptions		serviceSupportedCalling;		//serviceSupportedCalling	[2] IMPLICIT ServiceSupportOptions

	}ASN_initRequestDetail;											//  }
}ASN_Initiate_RequestPDU;											//}

//ASN_Initiate_ResponsePDU											стр.81
typedef struct														//Initiate_ResponsePDU ::= SEQUENCE {	
{																	//
																	//
	ASN_Integer32	localDetailCalled;								//localDetailCalled						[0] IMPLICIT Integer32 OPTIONAL,
	ASN_Integer16	negotiatedMaxServOutstandingCalling;			//negotiatedMaxServOutstandingCalling	[1] IMPLICIT Integer16,
	ASN_Integer16	negotiatedMaxServOutstandingCalled;				//negotiatedMaxServOutstandingCalled	[2] IMPLICIT Integer16,
	ASN_Integer8	negotiatedDataStructureNestingLevel;			//negotiatedDataStructureNestingLevel	[3] IMPLICIT Integer8 OPTIONAL,
	struct															//initResponseDetail					[4] IMPLICIT SEQUENCE {
	{

		ASN_Integer16					negotiatedVersionNumber;		//negotiatedVersionNumber	[0] IMPLICIT Integer16,
		ASN_ParameterSupportOptions		negotiatedParameterCBB;			//negotiatedParameterCBB	[1] IMPLICIT ParameterSupportOptions,
		ASN_ServiceSupportOptions		serviceSupportedCalled;			//serviceSupportedCalled	[2] IMPLICIT ServiceSupportOptions

	}ASN_initResponseDetail;											//  }


}ASN_Initiate_ResponsePDU;											//}

#endif
