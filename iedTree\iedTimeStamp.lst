                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c281.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=iedTimeStamp.c -o iedTree\gh_c281.o -list=iedTree/iedTimeStamp.lst C:\Users\<USER>\AppData\Local\Temp\gh_c281.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_c281.s
Source File: iedTimeStamp.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      10 ;		iedTree/iedTimeStamp.c -o iedTree/iedTimeStamp.o

                      11 ;Source File:   iedTree/iedTimeStamp.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Oct 07 09:39:21 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "iedTimeStamp.h"


                      21 ;2: 


                      22 ;3: #include "../DataSlice.h"


                      23 ;4: #include "../mms_data.h"


                      24 ;5: #include "../BaseAsnTypes.h"


                      25 ;6: 


                      26 ;7: #include <debug.h>


                      27 ;8: 


                      28 ;9: #include <stdint.h>


                      29 ;10: 


                      30 ;11: bool IEDTimeStampDA_calcReadLen(IEDEntity entity, size_t *pLen)


                      31 	.text

                      32 	.align	4

                      33 IEDTimeStampDA_calcReadLen::

                      34 ;12: {


                      35 

                      36 ;13:     *pLen = 10;


                      37 

00000000 e3a0000a     38 	mov	r0,10

00000004 e5810000     39 	str	r0,[r1]

                      40 ;14:     return true;


                      41 

00000008 e3a00001     42 	mov	r0,1

0000000c e12fff1e*    43 	ret	

                      44 	.endf	IEDTimeStampDA_calcReadLen

                      45 	.align	4

                      46 

                      47 ;entity	none	param

                      48 ;pLen	r1	param

                      49 

                      50 	.section ".bss","awb"


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c281.s
                      51 .L30:

                      52 	.data

                      53 	.text

                      54 

                      55 ;15: }


                      56 

                      57 ;16: 


                      58 ;17: bool IEDTimeStampDA_encodeRead(IEDEntity entity, BufferView *outBufView)


                      59 	.align	4

                      60 	.align	4

                      61 IEDTimeStampDA_encodeRead::

00000010 e92d4010     62 	stmfd	[sp]!,{r4,lr}

00000014 e24dd004     63 	sub	sp,sp,4

                      64 ;18: {


                      65 

                      66 ;19:     int dataLen;


                      67 ;20:     uint8_t *writeBuf;


                      68 ;21:     uint64_t timeStamp;


                      69 ;22: 


                      70 ;23:     TimeStamp* pTimeStamp = entity->extInfo;


                      71 

00000018 e5900058     72 	ldr	r0,[r0,88]

                      73 ;24:     timeStamp = pTimeStamp->timeStamp;


                      74 

0000001c e1a04001     75 	mov	r4,r1

00000020 e8900006     76 	ldmfd	[r0],{r1-r2}

                      77 ;25: 


                      78 ;26:     //Если значения времени в самом элементе нет, берём напрямую из DataSlice


                      79 ;27:     if(timeStamp == 0)


                      80 

00000024 e1910002     81 	orrs	r0,r1,r2

00000028 1a000002     82 	bne	.L39

                      83 ;28:     {


                      84 

                      85 ;29:         timeStamp= dataSliceGetTimeStamp();


                      86 

0000002c eb000000*    87 	bl	dataSliceGetTimeStamp

00000030 e1a02001     88 	mov	r2,r1

00000034 e1a01000     89 	mov	r1,r0

                      90 .L39:

                      91 ;30:     }


                      92 ;31: 


                      93 ;32:     writeBuf = outBufView->p + outBufView->pos;


                      94 

00000038 e8941008     95 	ldmfd	[r4],{r3,r12}

0000003c e3a00000     96 	mov	r0,0

00000040 e08c3003     97 	add	r3,r12,r3

                      98 ;33: 


                      99 ;34:     dataLen = MMSData_encodeTimeStamp(


                     100 

00000044 e58d0000    101 	str	r0,[sp]

00000048 e3a00091    102 	mov	r0,145

0000004c eb000000*   103 	bl	MMSData_encodeTimeStamp

00000050 e2501000    104 	subs	r1,r0,0

                     105 ;35:         IEC61850_BER_TIMESTAMP, timeStamp,writeBuf,0);


                     106 ;36:     if(dataLen <=0)


                     107 

00000054 da000004    108 	ble	.L45

                     109 ;37:     {


                     110 

                     111 ;38:         ERROR_REPORT("Invalid read length");



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c281.s
                     112 ;39:         return false;


                     113 

                     114 ;40:     }


                     115 ;41: 


                     116 ;42:     if(!BufferView_advance(outBufView, dataLen))


                     117 

00000058 e1a00004    118 	mov	r0,r4

0000005c eb000000*   119 	bl	BufferView_advance

00000060 e3500000    120 	cmp	r0,0

                     121 ;46:     }


                     122 ;47:     return true;


                     123 

00000064 13a00001    124 	movne	r0,1

00000068 1a000000    125 	bne	.L37

                     126 .L45:

                     127 ;43:     {


                     128 

                     129 ;44:         ERROR_REPORT("Buffer overflow");


                     130 ;45:         return false;


                     131 

0000006c e3a00000    132 	mov	r0,0

                     133 .L37:

00000070 e28dd004    134 	add	sp,sp,4

00000074 e8bd8010    135 	ldmfd	[sp]!,{r4,pc}

                     136 	.endf	IEDTimeStampDA_encodeRead

                     137 	.align	4

                     138 ;dataLen	r1	local

                     139 ;writeBuf	r0	local

                     140 ;timeStamp	r1	local

                     141 ;pTimeStamp	r0	local

                     142 

                     143 ;entity	r0	param

                     144 ;outBufView	r4	param

                     145 

                     146 	.section ".bss","awb"

                     147 .L106:

                     148 	.data

                     149 	.text

                     150 

                     151 ;48: }


                     152 

                     153 ;49: 


                     154 ;50: MmsDataAccessError IEDTimeStampDA_write(IEDEntity entity, BufferView *inBufView)


                     155 	.align	4

                     156 	.align	4

                     157 IEDTimeStampDA_write::

00000078 e92d48f0    158 	stmfd	[sp]!,{r4-r7,fp,lr}

                     159 ;51: {    


                     160 

0000007c e24dd010    161 	sub	sp,sp,16

00000080 e28d500f    162 	add	r5,sp,15

                     163 ;57:     // Указатель на входные данные


                     164 ;58:     uint8_t *pInData;


                     165 ;59:     TimeStamp* pTimeStamp = entity->extInfo;


                     166 

00000084 e28d2004    167 	add	r2,sp,4

00000088 e1a06001    168 	mov	r6,r1

0000008c e3a01000    169 	mov	r1,0

00000090 e58d100c    170 	str	r1,[sp,12]

                     171 ;52:     uint8_t tag;


                     172 ;53:     size_t len;



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c281.s
                     173 ;54:     uint64_t timeStamp = 0;


                     174 

                     175 ;55:     // Указатель на старший байт


                     176 ;56:     uint8_t *pValue = ((uint8_t*)&timeStamp) + 7;


                     177 

00000094 e58d1008    178 	str	r1,[sp,8]

00000098 e28d1003    179 	add	r1,sp,3

0000009c e590b058    180 	ldr	fp,[r0,88]

                     181 ;60:     size_t i;


                     182 ;61: 


                     183 ;62:     if(!BufferView_decodeTL(inBufView, &tag, &len, NULL))


                     184 

000000a0 e1a00006    185 	mov	r0,r6

000000a4 e3a03000    186 	mov	r3,0

000000a8 eb000000*   187 	bl	BufferView_decodeTL

000000ac e3500000    188 	cmp	r0,0

000000b0 0a00000b    189 	beq	.L139

                     190 ;63:     {


                     191 

                     192 ;64:         return DATA_ACCESS_ERROR_UNKNOWN;


                     193 

                     194 ;65:     }


                     195 ;66:     if(tag != IEC61850_BER_TIMESTAMP || len != 8)


                     196 

000000b4 e5dd0003    197 	ldrb	r0,[sp,3]

000000b8 e3500091    198 	cmp	r0,145

000000bc 059d0004    199 	ldreq	r0,[sp,4]

000000c0 03500008    200 	cmpeq	r0,8

                     201 ;67:     {


                     202 

                     203 ;68:         return DATA_ACCESS_ERROR_TYPE_INCONSISTENT;


                     204 

000000c4 13a00007    205 	movne	r0,7

000000c8 1a00001b    206 	bne	.L129

                     207 ;69:     }


                     208 ;70: 


                     209 ;71:     pInData = inBufView->p + inBufView->pos;


                     210 

000000cc e8960011    211 	ldmfd	[r6],{r0,r4}

000000d0 e1a07000    212 	mov	r7,r0

                     213 ;72:     if(!BufferView_advance(inBufView, len))


                     214 

000000d4 e1a00006    215 	mov	r0,r6

000000d8 e3a01008    216 	mov	r1,8

000000dc eb000000*   217 	bl	BufferView_advance

000000e0 e3500000    218 	cmp	r0,0

                     219 .L139:

                     220 ;73:     {


                     221 

                     222 ;74:         return DATA_ACCESS_ERROR_UNKNOWN;


                     223 

000000e4 03a0000c    224 	moveq	r0,12

000000e8 0a000013    225 	beq	.L129

                     226 .L141:

000000ec e7f40007    227 	ldrb	r0,[r4,r7]!

000000f0 e5c50000    228 	strb	r0,[r5]

000000f4 e5d40001    229 	ldrb	r0,[r4,1]

000000f8 e5450001    230 	strb	r0,[r5,-1]

000000fc e5d40002    231 	ldrb	r0,[r4,2]

00000100 e5450002    232 	strb	r0,[r5,-2]

00000104 e5d40003    233 	ldrb	r0,[r4,3]


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c281.s
00000108 e5450003    234 	strb	r0,[r5,-3]

0000010c e5d40004    235 	ldrb	r0,[r4,4]

00000110 e5450004    236 	strb	r0,[r5,-4]

00000114 e5d40005    237 	ldrb	r0,[r4,5]

00000118 e5450005    238 	strb	r0,[r5,-5]

0000011c e5d40006    239 	ldrb	r0,[r4,6]

00000120 e5450006    240 	strb	r0,[r5,-6]

00000124 e5d40007    241 	ldrb	r0,[r4,7]

00000128 e5450007    242 	strb	r0,[r5,-7]

                     243 ;75:     }


                     244 ;76: 


                     245 ;77:     // Разворачиваем для little endian


                     246 ;78:     for(i = 0; i < 8; i++)


                     247 

                     248 ;81:     }


                     249 ;82:     pTimeStamp->timeStamp = timeStamp;


                     250 

0000012c e59d100c    251 	ldr	r1,[sp,12]

00000130 e59d0008    252 	ldr	r0,[sp,8]

00000134 e88b0003    253 	stmea	[fp],{r0-r1}

                     254 ;83:     return DATA_ACCESS_ERROR_SUCCESS;


                     255 

00000138 e3e00000    256 	mvn	r0,0

                     257 .L129:

0000013c e28dd010    258 	add	sp,sp,16

00000140 e8bd88f0    259 	ldmfd	[sp]!,{r4-r7,fp,pc}

                     260 	.endf	IEDTimeStampDA_write

                     261 	.align	4

                     262 ;tag	[sp,3]	local

                     263 ;len	[sp,4]	local

                     264 ;timeStamp	[sp,8]	local

                     265 ;pValue	r5	local

                     266 ;pInData	r4	local

                     267 ;pTimeStamp	fp	local

                     268 

                     269 ;entity	r0	param

                     270 ;inBufView	r6	param

                     271 

                     272 	.section ".bss","awb"

                     273 .L286:

                     274 	.data

                     275 	.text

                     276 

                     277 ;84: }


                     278 	.align	4

                     279 

                     280 	.data

                     281 	.ghsnote version,6

                     282 	.ghsnote tools,3

                     283 	.ghsnote options,0

                     284 	.text

                     285 	.align	4

