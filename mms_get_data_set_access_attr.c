#include "mms_get_data_set_access_attr.h"

#include "stringView.h"
#include "MmsConst.h"
#include "iedmodel.h"
#include "BaseAsnTypes.h"
#include "AsnEncoding.h"
#include "mms_error.h"
#include "debug.h"
#include <string.h>


//Одна ссылка
int encodeDataSetRefAttr(uint8_t* outBuf, int bufPos, int objectPos, bool determineSize)
{
    int domainPos;
    int domainSize;
    int namePos;
    int nameSize;
    int size1;
    int size2;
    int seqenceSize;
    uint8_t tag;
    int pos = objectPos;


    //============Определяем размеры и позиции==============
    pos = readTL(pos, &tag, NULL, NULL);

    if(pos == 0 || tag != ASN_SEQUENCE)
    {
        ERROR_REPORT("Error reading reference TL");
        return 0;
    }
    
    //Домен
    domainPos = pos;
    pos = readTL(domainPos, &tag, NULL, &domainSize);
    if(pos == 0 || tag != ASN_VISIBLE_STRING)
    {
        ERROR_REPORT("Error reading domain name TL");
        return 0;
    }
    namePos = domainPos + domainSize;

    //Имя
    pos = readTL(namePos, &tag, NULL, &nameSize);
    if(pos == 0 || tag != ASN_VISIBLE_STRING)
    {
        ERROR_REPORT("Error reading object name TL");
        return 0;
    }

    //A1
    size1 = 1 + BerEncoder_determineLengthSize(domainSize + nameSize)
            + domainSize + nameSize;

    //A0
    size2 = 1 + BerEncoder_determineLengthSize(size1) + size1;

    //Sequence
    seqenceSize = 1 + BerEncoder_determineLengthSize(size2) + size2;

    if(determineSize)
    {
        return seqenceSize;
    }

    //================Кодируем====================
    //Sequence
    bufPos = BerEncoder_encodeTL(ASN_SEQUENCE, size2, outBuf, bufPos);
    //A0
    bufPos = BerEncoder_encodeTL(0xA0, size1, outBuf, bufPos);
    //A1
    bufPos = BerEncoder_encodeTL(0xA1, domainSize + nameSize, outBuf, bufPos);
    //Домен
    memcpy(outBuf + bufPos, iedModel + domainPos, domainSize);
    bufPos += domainSize;
    //Имя
    memcpy(outBuf + bufPos, iedModel + namePos, nameSize);
    bufPos += nameSize;
    return bufPos;
}


static int encodeDataSetRefAttrs(uint8_t* outBuf, int bufPos, int dataSetPos,
	bool determineSize)
{
    int endPos;
    int dataSetLen;
    int refPos;
    uint8_t tag;
	int size = 0;
    int pos = readTL(dataSetPos, NULL, &dataSetLen, NULL );
    endPos = pos + dataSetLen;
    //Пропускаем имя
    pos = skipObject(pos);
	VERIFY(pos);

    //Пропускаем дополнительную инфу для отчётов, если она есть.
    //Она должна быть всегда, но в отладочных моделях может и отсутствовать.
    tag = iedModel[pos];
    if(tag == IED_DATASET_DESCRIPTION)
    {
        pos = skipObject(pos);
    }
    else
    {
        TRACE("Dataset description is not found");
    }

    refPos = pos;
    while(refPos < endPos)
    {
		bufPos = encodeDataSetRefAttr(outBuf, bufPos, refPos, determineSize);
		VERIFY(bufPos);		
		if (determineSize)
		{
			size += bufPos;
			bufPos = size;
		}				
		refPos = skipObject(refPos);
		VERIFY(refPos);
    }
	return bufPos;
}

//Весь dataset
int encodeDataSetAttrs(uint32_t invokeId ,uint8_t* outBuf, int dataSetPos)
{
	int allRefsSize;
	int accessResultSize;
	int fullConfirmedResponseSize;
	int invokeIdSize;
	int bufPos = 0;


	//==================Определяем размеры====================	
	allRefsSize = encodeDataSetRefAttrs(outBuf, bufPos, dataSetPos, TRUE);

	invokeIdSize = BerEncoder_UInt32determineEncodedSize(invokeId) + 2;

	accessResultSize = allRefsSize
		+ BerEncoder_determineLengthSize(allRefsSize) + 1 // A1 TL
		+ 3; //Размер флага Deletable
	fullConfirmedResponseSize = accessResultSize
		//confirmed response
		+ BerEncoder_determineLengthSize(accessResultSize) + 1
		//invokeId
		+ invokeIdSize;
		
    //==================Кодируем====================
    bufPos = 0;

    // confirmed response PDU
    bufPos = BerEncoder_encodeTL(MMS_CONFIRMED_RESPONSE_PDU,  
		fullConfirmedResponseSize, outBuf, bufPos);

    // invoke id  
	bufPos = BerEncoder_encodeUInt32WithTL(ASN_INTEGER, invokeId, outBuf,
		bufPos);
		
    // confirmed-service-response getNamedVariableAccessAttributes
    bufPos = BerEncoder_encodeTL(0xAC, accessResultSize,
                                 outBuf, bufPos);

	//Deletable
	bufPos = BerEncoder_encodeBoolean(VAR_DELETABLE, FALSE, outBuf, bufPos);	
	// A1
	bufPos = BerEncoder_encodeTL(0xa1, allRefsSize, outBuf, bufPos);

	// кодируем ссылки
	bufPos = encodeDataSetRefAttrs(outBuf, bufPos, dataSetPos, FALSE);

    return bufPos;
}


int getDataSetAccessAttrs(unsigned int invokeId, 
	StringView* domainId, StringView* itemId, uint8_t* outBuf)
{
    //=============Находим dataset===============
    int dataSetPos = findObjectByFullName(IED_VMD_DATA_SET_SECTION, domainId, itemId);
	if (dataSetPos == 0)
	{
		return CreateMmsConfirmedErrorPdu(invokeId, outBuf,
			MMS_ERROR_ACCESS_OBJECT_NON_EXISTENT);
	}

	return encodeDataSetAttrs(invokeId, outBuf, dataSetPos);
}


int mms_handleGetDataSetAccessAttr(MmsConnection* mmsConn,
                                unsigned char* inBuf, int bufPos, int maxBufPos,
                                unsigned int invokeId, unsigned char* response)
{
	StringView domainId;
	StringView itemId;		
	
	bufPos = BerDecoder_DecodeObjectNameToStringView(inBuf, bufPos, 
		maxBufPos, &domainId, &itemId);
	if (bufPos < 0)
	{
		ERROR_REPORT("Unable to find object");
		return CreateMmsConfirmedErrorPdu(invokeId, response,
			MMS_ERROR_ACCESS_OBJECT_NON_EXISTENT);
	}	
	
	//TODO Обработать случай когда domainId и itemId не инициализированы
	return getDataSetAccessAttrs(invokeId, &domainId, &itemId, response);
}


