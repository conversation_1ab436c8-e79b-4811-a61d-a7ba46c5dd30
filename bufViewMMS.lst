                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bho1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=bufViewMMS.c -o gh_bho1.o -list=bufViewMMS.lst C:\Users\<USER>\AppData\Local\Temp\gh_bho1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_bho1.s
Source File: bufViewMMS.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile bufViewMMS.c

                      10 ;		-o bufViewMMS.o

                      11 ;Source File:   bufViewMMS.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:55:55 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "bufViewMMS.h"


                      21 ;2: 


                      22 ;3: #include "AsnEncoding.h"


                      23 ;4: 


                      24 ;5: 


                      25 ;6: 


                      26 ;7: bool BufView_decodeObjectName(BufferView *bv, StringView *domainId,


                      27 	.text

                      28 	.align	4

                      29 BufView_decodeObjectName::

00000000 e92d4010     30 	stmfd	[sp]!,{r4,lr}

                      31 ;8:                               StringView *itemId)


                      32 ;9: {


                      33 

                      34 ;10:     int nameLen = BerDecoder_DecodeObjectName(bv->p, bv->pos, bv->len,


                      35 

00000004 e24dd00c     36 	sub	sp,sp,12

00000008 e1a04000     37 	mov	r4,r0

0000000c e2813004     38 	add	r3,r1,4

00000010 e1a0c001     39 	mov	r12,r1

00000014 e88d100c     40 	stmea	[sp],{r2-r3,r12}

00000018 e2823004     41 	add	r3,r2,4

0000001c e8940007     42 	ldmfd	[r4],{r0-r2}

00000020 eb000000*    43 	bl	BerDecoder_DecodeObjectName

                      44 ;11:         (uint8_t**)&itemId->p, (int*)&itemId->len,


                      45 ;12:         (uint8_t**)&domainId->p, (int*)&domainId->len);


                      46 ;13: 


                      47 ;14:     if(nameLen < 0)


                      48 

00000024 e3500000     49 	cmp	r0,0

                      50 ;15:     {



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bho1.s
                      51 

                      52 ;16:         return false;


                      53 

00000028 b3a00000     54 	movlt	r0,0

                      55 ;17:     }


                      56 ;18: 


                      57 ;19:     bv->pos = nameLen;


                      58 

0000002c a5840004     59 	strge	r0,[r4,4]

                      60 ;20:     return true;


                      61 

00000030 a3a00001     62 	movge	r0,1

00000034 e28dd00c     63 	add	sp,sp,12

00000038 e8bd8010     64 	ldmfd	[sp]!,{r4,pc}

                      65 	.endf	BufView_decodeObjectName

                      66 	.align	4

                      67 ;nameLen	r0	local

                      68 

                      69 ;bv	r4	param

                      70 ;domainId	r1	param

                      71 ;itemId	r2	param

                      72 

                      73 	.section ".bss","awb"

                      74 .L43:

                      75 	.data

                      76 	.text

                      77 

                      78 ;21: }


                      79 	.align	4

                      80 

                      81 	.data

                      82 	.ghsnote version,6

                      83 	.ghsnote tools,3

                      84 	.ghsnote options,0

                      85 	.text

                      86 	.align	4

