#ifndef MMS_DEVICE_H_
#define MMS_DEVICE_H_

#define	DOMAIN_NAME_MAX_LENGTH	65

/*
//MMS_Domain
typedef struct
{

    char*									DomainName;
    int										NamedVariablesCount;
    MmsVariableSpecification**				NamedVariables;
}MMS_Domain;


//MMS_Device
typedef struct
{

    char*	DeviceName;

    // MMS VMD scope variables support
    //int namedVariablesCount;
    //MmsVariableSpecification** namedVariables;

    // MMS VMD scope named variables list support
    //LinkedList  namedVariableLists;

    // MMS domain support
    int DomainCount;
    MMS_Domain** ppDomains;

} MMS_Device;
*/

#endif //MMS_DEVICE_H_
