                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_f7s1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=mms_read.c -o gh_f7s1.o -list=mms_read.lst C:\Users\<USER>\AppData\Local\Temp\gh_f7s1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_f7s1.s
Source File: mms_read.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile mms_read.c -o

                      10 ;		mms_read.o

                      11 ;Source File:   mms_read.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Oct 07 09:39:36 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "mms_read.h"


                      21 ;2: #include "AsnEncoding.h"


                      22 ;3: #include "mms_data.h"


                      23 ;4: #include "mmsservices.h"


                      24 ;5: #include "iedmodel.h"


                      25 ;6: #include "iedTree/DataSet.h"


                      26 ;7: #include "iedTree/iedTree.h"


                      27 ;8: #include "iedTree/iedEntity.h"


                      28 ;9: #include "iedTree/iedNoEntity.h"


                      29 ;10: #include "ObjectNameParser.h"


                      30 ;11: #include "DataSlice.h"


                      31 ;12: #include "mms_error.h"


                      32 ;13: #include "bufViewMMS.h"


                      33 ;14: #include <debug.h>


                      34 ;15: #include <string.h>


                      35 ;16: #include <stddef.h>


                      36 ;17: 


                      37 ;18: // objectName должен содержать только имя объекта


                      38 ;19: // и обязательно с позиции 0


                      39 ;20: static bool encodeReadDataSetResponse(uint32_t invokeId, IEDEntity dataSet,


                      40 

                      41 ;91: 


                      42 ;92: }


                      43 

                      44 ;93: 


                      45 ;94: 


                      46 ;95: static bool encodeReadResponse(unsigned int invokeId, IEDEntity* objList,


                      47 

                      48 ;180: }


                      49 

                      50 ;181: 



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_f7s1.s
                      51 ;182: /*


                      52 ;183: static int handleReadNamedVariableListRequestOld(unsigned int invokeId,


                      53 ;184: 	uint8_t* pObjectName, int objectNameLen,	unsigned char* outBuf)


                      54 ;185: {


                      55 ;186: 	StringView domainId;


                      56 ;187: 	StringView itemId;


                      57 ;188: 	int dataSetPos;


                      58 ;189: 	int bufPos;


                      59 ;190: 


                      60 ;191: 	bufPos = BerDecoder_DecodeObjectNameToStringView(pObjectName, 0, 


                      61 ;192: 		objectNameLen, &domainId, &itemId);


                      62 ;193: 	RET_IF_NOT(bufPos > 1, "Unable to decode dataset name");


                      63 ;194: 


                      64 ;195: 	dataSetPos = findObjectByFullName(IED_VMD_DATA_SET_SECTION,


                      65 ;196: 		&domainId, &itemId);


                      66 ;197: 	RET_IF_NOT(dataSetPos, "DataSet is not found");


                      67 ;198: 


                      68 ;199: 	return encodeReadDataSetResponse(invokeId, dataSetPos,


                      69 ;200: 		pObjectName, objectNameLen, outBuf);


                      70 ;201: }


                      71 ;202: */


                      72 ;203: 


                      73 ;204: // Чтение DataSet по имени


                      74 ;205: // objectName должен содержать только имя объекта


                      75 ;206: // и обязательно с позиции 0.


                      76 ;207: static bool handleReadNamedVariableListRequest(unsigned int invokeId,


                      77 

                      78 ;232: }


                      79 

                      80 ;233: 


                      81 ;234: //Заполняет список объектов для чтения


                      82 ;235: //После последнего объекта записывается NULL


                      83 ;236: static bool fillReadObjList(BufferView *varSpecList, IEDEntity* objList)


                      84 

                      85 ;259: }


                      86 

                      87 ;260: 


                      88 ;261: int mms_handleReadRequest(MmsConnection* mmsConn,


                      89 	.text

                      90 	.align	4

                      91 mms_handleReadRequest::

00000000 e92d4cf0     92 	stmfd	[sp]!,{r4-r7,r10-fp,lr}

                      93 ;262:         unsigned char* inBuf, int bufPos, int maxBufPos, unsigned int invokeId,


                      94 ;263:         unsigned char* response, size_t maxRespSize)


                      95 ;264: {   	    


                      96 

00000004 e1a06002     97 	mov	r6,r2

00000008 e24dd044     98 	sub	sp,sp,68

0000000c e59d2068     99 	ldr	r2,[sp,104]

00000010 e3a05000    100 	mov	r5,0

00000014 e5cd5003    101 	strb	r5,[sp,3]

                     102 ;265:     int result;


                     103 ;266: 	BufferView objectName;


                     104 ;267:     //Чтение dataset


                     105 ;268:     bool namedVariableListRequest = FALSE;


                     106 

                     107 ;269:     unsigned char	tag;


                     108 ;270:     int				iLength;    


                     109 ;271:     BufferView specListBer;


                     110 ;272: 	BufferView responseBuf;


                     111 ;273: 	BufferView_init(&responseBuf, response, maxRespSize, 0);



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_f7s1.s
                     112 

00000018 e1a04000    113 	mov	r4,r0

0000001c e1a07001    114 	mov	r7,r1

00000020 e59d1064    115 	ldr	r1,[sp,100]

00000024 e28d0020    116 	add	r0,sp,32

00000028 e1a0b003    117 	mov	fp,r3

0000002c e1a03005    118 	mov	r3,r5

00000030 eb000000*   119 	bl	BufferView_init

                     120 ;274:     //Список объектов для чтения пустой


                     121 ;275:     mmsConn->readVarObjList[0] = NULL;


                     122 

00000034 e3a00c63    123 	mov	r0,99<<8

00000038 e2800068    124 	add	r0,r0,104

0000003c e7a45000    125 	str	r5,[r4,r0]!

                     126 ;276: 


                     127 ;277:     while (bufPos < maxBufPos)


                     128 

00000040 e156000b    129 	cmp	r6,fp

00000044 aa000049    130 	bge	.L174

                     131 .L175:

                     132 ;278:     {


                     133 

                     134 ;279: 


                     135 ;280:         tag = inBuf[bufPos++];


                     136 

00000048 e7d75006    137 	ldrb	r5,[r7,r6]

0000004c e2861001    138 	add	r1,r6,1

                     139 ;281: 


                     140 ;282:         bufPos = BerDecoder_DecodeLengthOld(inBuf, bufPos, maxBufPos, &iLength);


                     141 

00000050 e28d3004    142 	add	r3,sp,4

00000054 e1a0200b    143 	mov	r2,fp

00000058 e1a00007    144 	mov	r0,r7

0000005c eb000000*   145 	bl	BerDecoder_DecodeLengthOld

00000060 e1b06000    146 	movs	r6,r0

                     147 ;283: 


                     148 ;284:         if (bufPos < 0)


                     149 

00000064 4a00003f    150 	bmi	.L208

                     151 ;285:         {


                     152 

                     153 ;286:             return 0;


                     154 

                     155 ;287:         }


                     156 ;288: 


                     157 ;289:         switch (tag)


                     158 

00000068 e2555080    159 	subs	r5,r5,128

0000006c 0a000002    160 	beq	.L181

00000070 e3550021    161 	cmp	r5,33

00000074 0a000007    162 	beq	.L185

00000078 ea00003a    163 	b	.L208

                     164 .L181:

0000007c e59d0004    165 	ldr	r0,[sp,4]

                     166 ;290:         {


                     167 ;291:             case MMS_READ_SPECIFICATION_WITH_RESULT:


                     168 ;292:                 if (iLength != 1)


                     169 

00000080 e3500001    170 	cmp	r0,1

00000084 1a000037    171 	bne	.L208

                     172 ;293:                 {



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_f7s1.s
                     173 

                     174 ;294:                     return 0;


                     175 

                     176 ;295:                 }


                     177 ;296:                 //Пропускаем specification


                     178 ;297:                 bufPos++;


                     179 

00000088 e0866000    180 	add	r6,r6,r0

0000008c e156000b    181 	cmp	r6,fp

00000090 baffffec    182 	blt	.L175

00000094 ea000035    183 	b	.L174

                     184 .L185:

                     185 ;298:                 break;


                     186 ;299:             case MMS_READ_VARIABLE_ACCESS_SPECIFICATION:


                     187 ;300:                 tag = inBuf[bufPos++];


                     188 

00000098 e7d75006    189 	ldrb	r5,[r7,r6]

0000009c e2861001    190 	add	r1,r6,1

                     191 ;301:                 bufPos = BerDecoder_DecodeLengthOld(inBuf, bufPos,


                     192 

000000a0 e28d3004    193 	add	r3,sp,4

000000a4 e1a0200b    194 	mov	r2,fp

000000a8 e1a00007    195 	mov	r0,r7

000000ac eb000000*   196 	bl	BerDecoder_DecodeLengthOld

000000b0 e1b06000    197 	movs	r6,r0

                     198 ;302:                     maxBufPos, &iLength);


                     199 ;303:                 if (bufPos < 0)


                     200 

000000b4 4a00002b    201 	bmi	.L208

                     202 ;304:                 {


                     203 

                     204 ;305:                     return 0;


                     205 

                     206 ;306:                 }


                     207 ;307:                 switch (tag)


                     208 

000000b8 e25550a0    209 	subs	r5,r5,160

000000bc 0a00000d    210 	beq	.L192

000000c0 e3550001    211 	cmp	r5,1

000000c4 1a000027    212 	bne	.L208

                     213 ;308:                 {


                     214 ;309: 				case MMS_READ_VARIABLE_LIST_NAME://DataSet										


                     215 ;310: 					//StringView_init(&objectName, inBuf + bufPos, iLength);


                     216 ;311: 					BufferView_init(&objectName, inBuf + bufPos, iLength, 0);


                     217 

000000c8 e59d2004    218 	ldr	r2,[sp,4]

000000cc e0861007    219 	add	r1,r6,r7

000000d0 e28d0038    220 	add	r0,sp,56

000000d4 e3a03000    221 	mov	r3,0

000000d8 eb000000*   222 	bl	BufferView_init

                     223 ;312: 


                     224 ;313: 					bufPos += iLength;


                     225 

000000dc e59d0004    226 	ldr	r0,[sp,4]

000000e0 e0866000    227 	add	r6,r6,r0

                     228 ;314: 					namedVariableListRequest = TRUE;


                     229 

000000e4 e3a00001    230 	mov	r0,1

000000e8 e5cd0003    231 	strb	r0,[sp,3]

000000ec e156000b    232 	cmp	r6,fp

000000f0 baffffd4    233 	blt	.L175


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_f7s1.s
000000f4 ea00001d    234 	b	.L174

                     235 .L192:

                     236 ;315: 					break;


                     237 ;316:                 case MMS_READ_LIST_OF_VARIABLE:


                     238 ;317:                     BufferView_init(&specListBer, inBuf, bufPos + iLength,  bufPos);


                     239 

000000f8 e1a01007    240 	mov	r1,r7

000000fc e59d0004    241 	ldr	r0,[sp,4]

00000100 e1a03006    242 	mov	r3,r6

00000104 e0802006    243 	add	r2,r0,r6

00000108 e28d002c    244 	add	r0,sp,44

0000010c e1a06000    245 	mov	r6,r0

00000110 eb000000*   246 	bl	BufferView_init

                     247 ;318:                     if(!fillReadObjList(&specListBer,mmsConn->readVarObjList))


                     248 

                     249 ;237: {        


                     250 

00000114 e3a05000    251 	mov	r5,0

                     252 ;238:     IEDEntity object;       


                     253 ;239:     size_t objIndex = 0;


                     254 

                     255 ;240: 


                     256 ;241:     while (!BufferView_endOfBuf(varSpecList))


                     257 

00000118 e1a0a006    258 	mov	r10,r6

                     259 ;252:         {


                     260 

                     261 ;253:             ERROR_REPORT("Too many objects in the list");


                     262 ;254:             return false;


                     263 

0000011c e9960003    264 	ldmed	[r6],{r0-r1}

00000120 e1500001    265 	cmp	r0,r1

00000124 0a000009    266 	beq	.L193

                     267 .L198:

                     268 ;242:     {


                     269 

                     270 ;243:         object = ObjectNameParser_parse(varSpecList);


                     271 

00000128 e1a0000a    272 	mov	r0,r10

0000012c eb000000*   273 	bl	ObjectNameParser_parse

                     274 ;244:         if(object == NULL)


                     275 

00000130 e3500000    276 	cmp	r0,0

                     277 ;245:         {


                     278 

                     279 ;246:             ERROR_REPORT("Unable to parse object name");


                     280 ;247:             return false;


                     281 

                     282 ;248:         }


                     283 ;249:         


                     284 ;250:         objList[objIndex++] = object;


                     285 

00000134 17840105    286 	strne	r0,[r4,r5 lsl 2]

00000138 12855001    287 	addne	r5,r5,1

                     288 ;251:         if(objIndex >= MAX_READ_VARIABLE_OBJECT_LIST)


                     289 

0000013c 13550064    290 	cmpne	r5,100

00000140 2a0000a7    291 	bhs	.L253

                     292 ;252:         {


                     293 

                     294 ;253:             ERROR_REPORT("Too many objects in the list");



                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_f7s1.s
                     295 ;254:             return false;


                     296 

00000144 e9960003    297 	ldmed	[r6],{r0-r1}

00000148 e1500001    298 	cmp	r0,r1

0000014c 1afffff5    299 	bne	.L198

                     300 .L193:

00000150 e3a01000    301 	mov	r1,0

00000154 e59d6030    302 	ldr	r6,[sp,48]

00000158 e7841105    303 	str	r1,[r4,r5 lsl 2]

                     304 ;255:         }                


                     305 ;256:     }


                     306 ;257:     objList[objIndex] = 0;


                     307 

                     308 ;258:     return true;


                     309 

                     310 ;319:                     {


                     311 

                     312 ;320:                         return CreateMmsConfirmedErrorPdu(invokeId, response,


                     313 

                     314 ;321:                             MMS_ERROR_ACCESS_OBJECT_NON_EXISTENT);


                     315 ;322:                     }


                     316 ;323:                     bufPos = specListBer.pos;


                     317 

0000015c e156000b    318 	cmp	r6,fp

00000160 baffffb8    319 	blt	.L175

00000164 ea000001    320 	b	.L174

                     321 .L208:

                     322 ;324: 					break;


                     323 ;325:                 default:


                     324 ;326:                     return 0;


                     325 

                     326 ;327:                 }


                     327 ;328:                 break;


                     328 ;329:             default:


                     329 ;330:                 return 0;


                     330 

00000168 e3a00000    331 	mov	r0,0

0000016c ea0000a2    332 	b	.L171

                     333 .L174:

                     334 ;331:         }


                     335 ;332:     }


                     336 ;333: 


                     337 ;334: 


                     338 ;335: 	IEDTree_lock();


                     339 

00000170 eb000000*   340 	bl	IEDTree_lock

                     341 ;336: 


                     342 ;337: 	if (namedVariableListRequest)


                     343 

00000174 e5dd0003    344 	ldrb	r0,[sp,3]

00000178 e3500000    345 	cmp	r0,0

0000017c 0a000047    346 	beq	.L231

                     347 ;338: 	{		


                     348 

                     349 ;339: 		


                     350 ;340: 


                     351 ;341: 		//Чтение DataSet


                     352 ;342: 		if(!handleReadNamedVariableListRequest(invokeId, &objectName, &responseBuf))


                     353 

                     354 ;208: 											   BufferView *objectName, BufferView *outBuf)


                     355 ;209: {



                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_f7s1.s
                     356 

                     357 ;210: 	StringView domainId;


                     358 ;211: 	StringView itemId;


                     359 ;212: 	IEDEntity dataSetEntity;


                     360 ;213: 


                     361 ;214: 	//Гарантируем, что pos будет указывать на имя объекта для декодирования


                     362 ;215: 	objectName->pos = 0;


                     363 

00000180 e28d2010    364 	add	r2,sp,16

00000184 e28d1018    365 	add	r1,sp,24

00000188 e3a00000    366 	mov	r0,0

0000018c e58d003c    367 	str	r0,[sp,60]

                     368 ;216: 	if(!BufView_decodeObjectName(objectName, &domainId, &itemId))


                     369 

00000190 e28d0038    370 	add	r0,sp,56

00000194 eb000000*   371 	bl	BufView_decodeObjectName

00000198 e3500000    372 	cmp	r0,0

0000019c 0a000004    373 	beq	.L219

                     374 ;217: 	{


                     375 

                     376 ;218: 		ERROR_REPORT("Unable to decode dataset name")	;


                     377 ;219: 		return false;


                     378 

                     379 ;220: 	}	


                     380 ;221: 


                     381 ;222: 	dataSetEntity =


                     382 

000001a0 e28d1010    383 	add	r1,sp,16

000001a4 e28d0018    384 	add	r0,sp,24

000001a8 eb000000*   385 	bl	IEDTree_findDataSetByFullName

000001ac e1b04000    386 	movs	r4,r0

                     387 ;223: 			IEDTree_findDataSetByFullName(&domainId, &itemId);


                     388 ;224: 


                     389 ;225: 	if(dataSetEntity == NULL)


                     390 

000001b0 1a000002    391 	bne	.L220

                     392 .L219:

                     393 ;226: 	{


                     394 

                     395 ;227: 		ERROR_REPORT("Unable to find dataset");


                     396 ;228: 		return false;


                     397 

                     398 ;356:         {


                     399 

                     400 ;357: 			ERROR_REPORT("encodeReadResponse error");


                     401 ;358:             result = 0;


                     402 

000001b4 e3a04000    403 	mov	r4,0

                     404 ;363:         }


                     405 ;364: 	}


                     406 ;365: 	IEDTree_unlock();


                     407 

000001b8 eb000000*   408 	bl	IEDTree_unlock

                     409 ;366: 


                     410 ;367: 	if(result < 1)


                     411 

000001bc ea000088    412 	b	.L253

                     413 .L220:

                     414 ;229: 	}


                     415 ;230: 


                     416 ;231: 	return encodeReadDataSetResponse(invokeId, dataSetEntity, objectName,outBuf);



                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_f7s1.s
                     417 

                     418 ;21: 	BufferView *objectName, BufferView *outBuf)


                     419 ;22: {


                     420 

                     421 ;23: 	bool result;


                     422 ;24: 	size_t readDataSize;


                     423 ;25: 	size_t readDataSizeWithTL;


                     424 ;26: 	int objectSpecSize;


                     425 ;27: 	int objectSpecRespSize;


                     426 ;28: 	int readResponseSize;


                     427 ;29: 	int invokeIdSize;


                     428 ;30: 	int confirmedResponseContentSize;


                     429 ;31: 


                     430 ;32: 	dataSliceCapture();


                     431 

000001c0 eb000000*   432 	bl	dataSliceCapture

000001c4 e28d1008    433 	add	r1,sp,8

000001c8 e1a00004    434 	mov	r0,r4

000001cc eb000000*   435 	bl	DataSet_calcReadLen

                     436 ;33: 


                     437 ;34: 	//===============Получаем размеры===================


                     438 ;35: 


                     439 ;36: 	//Размер результатов чтения


                     440 ;37: 


                     441 ;38: 	if(!DataSet_calcReadLen(dataSet, &readDataSize))


                     442 

000001d0 e3500000    443 	cmp	r0,0

000001d4 1a000001    444 	bne	.L222

                     445 ;39: 	{


                     446 

                     447 ;40: 		ERROR_REPORT("Unable to determine dataset read size")	;		


                     448 ;41: 		return false;


                     449 

000001d8 0a00007a    450 	beq	.L228

000001dc ea00007c    451 	b	.L227

                     452 .L222:

                     453 ;42: 	}


                     454 ;43: 


                     455 ;44: 	//Размер результатов чтения с тэгом и длиной


                     456 ;45: 	readDataSizeWithTL = BerEncoder_determineFullObjectSize(readDataSize);


                     457 

000001e0 e59d0008    458 	ldr	r0,[sp,8]

000001e4 eb000000*   459 	bl	BerEncoder_determineFullObjectSize

000001e8 e1a05000    460 	mov	r5,r0

                     461 ;46: 


                     462 ;47: 	//Размер спецификации объекта ( А1)


                     463 ;48: 	objectSpecSize = BerEncoder_determineFullObjectSize(objectName->len);


                     464 

000001ec e59d0040    465 	ldr	r0,[sp,64]

000001f0 eb000000*   466 	bl	BerEncoder_determineFullObjectSize

                     467 ;49: 


                     468 ;50: 	//Весь А0


                     469 ;51: 	objectSpecRespSize = BerEncoder_determineFullObjectSize(objectSpecSize);


                     470 

000001f4 e1a0a000    471 	mov	r10,r0

000001f8 eb000000*   472 	bl	BerEncoder_determineFullObjectSize

000001fc e1a06000    473 	mov	r6,r0

                     474 ;52: 


                     475 ;53: 	//Размер размер ответа на чтение (A4)


                     476 ;54: 	readResponseSize = BerEncoder_determineFullObjectSize(


                     477 


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_f7s1.s
00000200 e0860005    478 	add	r0,r6,r5

00000204 eb000000*   479 	bl	BerEncoder_determineFullObjectSize

00000208 e1a07000    480 	mov	r7,r0

                     481 ;55: 		readDataSizeWithTL + objectSpecRespSize);


                     482 ;56: 


                     483 ;57: 	//Размер invokeId


                     484 ;58: 	invokeIdSize = BerEncoder_UInt32determineEncodedSize(invokeId) + 2;


                     485 

0000020c e59d0060    486 	ldr	r0,[sp,96]

00000210 eb000000*   487 	bl	BerEncoder_UInt32determineEncodedSize

00000214 e0800007    488 	add	r0,r0,r7

                     489 ;59: 


                     490 ;60: 	//Полный размер ответа (для A1)


                     491 ;61: 	confirmedResponseContentSize = invokeIdSize + readResponseSize;


                     492 

00000218 e2802002    493 	add	r2,r0,2

                     494 ;62: 


                     495 ;63: 	//================Кодируем=====================


                     496 ;64: 


                     497 ;65: 	// confirmed response PDU


                     498 ;66: 	BufferView_encodeTL(outBuf, 0xA1, confirmedResponseContentSize );


                     499 

0000021c e28d0020    500 	add	r0,sp,32

00000220 e3a010a1    501 	mov	r1,161

00000224 eb000000*   502 	bl	BufferView_encodeTL

                     503 ;67: 


                     504 ;68: 	// invoke id	


                     505 ;69: 	BufferView_encodeUInt32(outBuf, ASN_INTEGER, invokeId);


                     506 

00000228 e59d2060    507 	ldr	r2,[sp,96]

0000022c e28d0020    508 	add	r0,sp,32

00000230 e3a01002    509 	mov	r1,2

00000234 eb000000*   510 	bl	BufferView_encodeUInt32

                     511 ;70: 


                     512 ;71: 	// confirmed-service-response read	


                     513 ;72: 	BufferView_encodeTL(outBuf, 0xA4, objectSpecRespSize + readDataSizeWithTL);


                     514 

00000238 e0852006    515 	add	r2,r5,r6

0000023c e28d0020    516 	add	r0,sp,32

00000240 e3a010a4    517 	mov	r1,164

00000244 eb000000*   518 	bl	BufferView_encodeTL

                     519 ;73: 


                     520 ;74: 	//A0	


                     521 ;75: 	BufferView_encodeTL(outBuf, 0xA0,objectSpecSize);


                     522 

00000248 e1a0200a    523 	mov	r2,r10

0000024c e28d0020    524 	add	r0,sp,32

00000250 e3a010a0    525 	mov	r1,160

00000254 eb000000*   526 	bl	BufferView_encodeTL

                     527 ;76: 		


                     528 ;77: 	//Гарантируем, что запишем полное имя объекта


                     529 ;78: 	objectName->pos = objectName->len;


                     530 

00000258 e59d0040    531 	ldr	r0,[sp,64]

0000025c e28d2038    532 	add	r2,sp,56

00000260 e58d003c    533 	str	r0,[sp,60]

                     534 ;79: 


                     535 ;80: 	//Object name


                     536 ;81: 	BufferView_encodeBufferView(outBuf, 0xA1, objectName);	


                     537 

00000264 e28d0020    538 	add	r0,sp,32


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_f7s1.s
00000268 e3a010a1    539 	mov	r1,161

0000026c eb000000*   540 	bl	BufferView_encodeBufferView

                     541 ;82: 


                     542 ;83: 	// encode list of access results TL		


                     543 ;84: 	BufferView_encodeTL(outBuf, 0xA1, readDataSize);


                     544 

00000270 e59d2008    545 	ldr	r2,[sp,8]

00000274 e28d0020    546 	add	r0,sp,32

00000278 e3a010a1    547 	mov	r1,161

0000027c eb000000*   548 	bl	BufferView_encodeTL

                     549 ;85: 


                     550 ;86: 	// encode access results	


                     551 ;87: 	result = DataSet_encodeRead(dataSet, outBuf);


                     552 

00000280 e28d1020    553 	add	r1,sp,32

00000284 e1a00004    554 	mov	r0,r4

00000288 eb000000*   555 	bl	DataSet_encodeRead

0000028c e1a04000    556 	mov	r4,r0

                     557 ;88: 


                     558 ;89: 	dataSliceRelease();	


                     559 

00000290 eb000000*   560 	bl	dataSliceRelease

                     561 ;90: 	return result;


                     562 

00000294 e3540000    563 	cmp	r4,0

00000298 0a00004a    564 	beq	.L228

0000029c ea00004c    565 	b	.L227

                     566 .L231:

                     567 ;343: 		{


                     568 

                     569 ;344: 			ERROR_REPORT("handleReadNamedVariableListRequest error");


                     570 ;345: 			result = 0;


                     571 

                     572 ;346: 		}


                     573 ;347: 		else


                     574 ;348: 		{			


                     575 

                     576 ;349: 			result = responseBuf.pos;


                     577 

                     578 ;350: 		}


                     579 ;351: 	}


                     580 ;352: 	else


                     581 ;353: 	{        


                     582 

                     583 ;354: 		//Чтение переменных по списку


                     584 ;355: 		if(!encodeReadResponse(invokeId, mmsConn->readVarObjList, &responseBuf))


                     585 

                     586 ;96:                                BufferView *outBuf)


                     587 ;97: {    


                     588 

                     589 ;98:     size_t accessResultSize = 0;


                     590 

                     591 ;99:     size_t varAccessSpecSize = 0;


                     592 

                     593 ;100:     size_t listOfAccessResultsLength;


                     594 ;101:     size_t confirmedServiceResponseContentLength;


                     595 ;102:     size_t confirmedServiceResponseLength;


                     596 ;103:     size_t invokeIdSize;


                     597 ;104:     size_t confirmedResponseContentSize;


                     598 ;105:     size_t objIndex;


                     599 ;106: 	IEDEntity entity;



                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_f7s1.s
                     600 ;107: 


                     601 ;108:     dataSliceCapture();


                     602 

000002a0 e3a06000    603 	mov	r6,0

                     604 ;114:     for(objIndex = 0; objList[objIndex] != NULL; ++objIndex)


                     605 

000002a4 eb000000*   606 	bl	dataSliceCapture

                     607 ;109: 


                     608 ;110:     //==============determine BER encoded message sizes==============	


                     609 ;111: 


                     610 ;112:     //Общий размер всех значений


                     611 ;113: 	accessResultSize = 0;


                     612 

000002a8 e1a05004    613 	mov	r5,r4

000002ac e5950000    614 	ldr	r0,[r5]

000002b0 e28d700c    615 	add	r7,sp,12

000002b4 e3500000    616 	cmp	r0,0

000002b8 0a00000b    617 	beq	.L236

                     618 .L232:

                     619 ;115: 	{


                     620 

                     621 ;116:         size_t objReadLen;			


                     622 ;117: 		entity = objList[objIndex];


                     623 

000002bc e2855004    624 	add	r5,r5,4

                     625 ;118: 		if(!entity->calcReadLen(entity,  &objReadLen))


                     626 

000002c0 e590c060    627 	ldr	r12,[r0,96]

000002c4 e1a01007    628 	mov	r1,r7

000002c8 e1a0e00f    629 	mov	lr,pc

000002cc e12fff1c*   630 	bx	r12

000002d0 e3500000    631 	cmp	r0,0

000002d4 0a00003b    632 	beq	.L228

                     633 ;119:         {			


                     634 

                     635 ;120:             return 0;


                     636 

                     637 ;121:         }


                     638 ;122:         accessResultSize += objReadLen;


                     639 

000002d8 e59d000c    640 	ldr	r0,[sp,12]

000002dc e0866000    641 	add	r6,r6,r0

000002e0 e5950000    642 	ldr	r0,[r5]

000002e4 e3500000    643 	cmp	r0,0

000002e8 1afffff3    644 	bne	.L232

                     645 .L236:

                     646 ;123: 	}


                     647 ;124: 


                     648 ;125:     listOfAccessResultsLength = 1 +


                     649 

000002ec e1a00006    650 	mov	r0,r6

000002f0 eb000000*   651 	bl	BerEncoder_determineLengthSize

000002f4 e0800006    652 	add	r0,r0,r6

000002f8 e2805001    653 	add	r5,r0,1

                     654 ;126:                 BerEncoder_determineLengthSize(accessResultSize) +


                     655 ;127:                 accessResultSize;


                     656 ;128: 


                     657 ;129:     confirmedServiceResponseContentLength = listOfAccessResultsLength + varAccessSpecSize;


                     658 

                     659 ;130: 


                     660 ;131:     confirmedServiceResponseLength = 1 +



                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_f7s1.s
                     661 

000002fc e1a00005    662 	mov	r0,r5

00000300 eb000000*   663 	bl	BerEncoder_determineLengthSize

00000304 e0800005    664 	add	r0,r0,r5

00000308 e2807001    665 	add	r7,r0,1

                     666 ;132:             BerEncoder_determineLengthSize(confirmedServiceResponseContentLength) +


                     667 ;133:             confirmedServiceResponseContentLength;


                     668 ;134: 


                     669 ;135:     invokeIdSize = BerEncoder_UInt32determineEncodedSize(invokeId) + 2;


                     670 

0000030c e59d0060    671 	ldr	r0,[sp,96]

00000310 eb000000*   672 	bl	BerEncoder_UInt32determineEncodedSize

00000314 e0800007    673 	add	r0,r0,r7

                     674 ;136: 


                     675 ;137:     confirmedResponseContentSize = confirmedServiceResponseLength + invokeIdSize;


                     676 

00000318 e2802002    677 	add	r2,r0,2

                     678 ;138: 


                     679 ;139:     //================ encode message ============================


                     680 ;140:     // confirmed response PDU


                     681 ;141:     if(!BufferView_encodeTL(outBuf, 0xA1, confirmedResponseContentSize))


                     682 

0000031c e28d0020    683 	add	r0,sp,32

00000320 e3a010a1    684 	mov	r1,161

00000324 eb000000*   685 	bl	BufferView_encodeTL

00000328 e3500000    686 	cmp	r0,0

0000032c 0a000025    687 	beq	.L228

                     688 ;142:     {		


                     689 

                     690 ;143:         return false;


                     691 

                     692 ;144:     }


                     693 ;145: 


                     694 ;146:     // invoke id


                     695 ;147:     if(!BufferView_encodeUInt32(outBuf, ASN_INTEGER, invokeId))


                     696 

00000330 e59d2060    697 	ldr	r2,[sp,96]

00000334 e28d0020    698 	add	r0,sp,32

00000338 e3a01002    699 	mov	r1,2

0000033c eb000000*   700 	bl	BufferView_encodeUInt32

00000340 e3500000    701 	cmp	r0,0

00000344 0a00001f    702 	beq	.L228

                     703 ;148:     {		


                     704 

                     705 ;149:         return false;


                     706 

                     707 ;150:     }


                     708 ;151: 


                     709 ;152:     // confirmed-service-response read


                     710 ;153:     if(!BufferView_encodeTL(outBuf, 0xA4, confirmedServiceResponseContentLength))


                     711 

00000348 e1a02005    712 	mov	r2,r5

0000034c e28d0020    713 	add	r0,sp,32

00000350 e3a010a4    714 	mov	r1,164

00000354 eb000000*   715 	bl	BufferView_encodeTL

00000358 e3500000    716 	cmp	r0,0

0000035c 0a000019    717 	beq	.L228

                     718 ;154:     {		


                     719 

                     720 ;155:         return false;


                     721 


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_f7s1.s
                     722 ;156:     }


                     723 ;157: 


                     724 ;158:     // encode variable access specification


                     725 ;159:     //if (accessSpec != NULL)


                     726 ;160:     //    bufPos = encodeVariableAccessSpecification(accessSpec, outBuf, bufPos, true);


                     727 ;161: 


                     728 ;162:     // encode list of access results    


                     729 ;163:     if(!BufferView_encodeTL(outBuf, 0xA1, accessResultSize))


                     730 

00000360 e1a02006    731 	mov	r2,r6

00000364 e28d0020    732 	add	r0,sp,32

00000368 e3a010a1    733 	mov	r1,161

0000036c eb000000*   734 	bl	BufferView_encodeTL

00000370 e3500000    735 	cmp	r0,0

00000374 0a000013    736 	beq	.L228

                     737 ;164: 	{


                     738 

                     739 ;165:         return false;


                     740 

                     741 ;166:     }


                     742 ;167: 


                     743 ;168:     // encode access results            


                     744 ;169:     for(objIndex = 0; objList[objIndex] != NULL; ++objIndex)


                     745 

00000378 e5940000    746 	ldr	r0,[r4]

0000037c e28d5020    747 	add	r5,sp,32

                     748 ;173:         {			


                     749 

                     750 ;174:             return false;


                     751 

00000380 e3500000    752 	cmp	r0,0

00000384 0a000009    753 	beq	.L249

                     754 .L245:

                     755 ;170: 	{


                     756 

                     757 ;171: 		entity = objList[objIndex];


                     758 

00000388 e2844004    759 	add	r4,r4,4

                     760 ;172: 		if(!entity->encodeRead(entity, outBuf))


                     761 

0000038c e590c05c    762 	ldr	r12,[r0,92]

00000390 e1a01005    763 	mov	r1,r5

00000394 e1a0e00f    764 	mov	lr,pc

00000398 e12fff1c*   765 	bx	r12

0000039c e3500000    766 	cmp	r0,0

000003a0 0a000008    767 	beq	.L228

                     768 ;173:         {			


                     769 

                     770 ;174:             return false;


                     771 

000003a4 e5940000    772 	ldr	r0,[r4]

000003a8 e3500000    773 	cmp	r0,0

000003ac 1afffff5    774 	bne	.L245

                     775 .L249:

                     776 ;175:         }


                     777 ;176: 	}


                     778 ;177: 


                     779 ;178:     dataSliceRelease();	


                     780 

000003b0 eb000000*   781 	bl	dataSliceRelease

                     782 ;179:     return true;



                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_f7s1.s
                     783 

                     784 ;359:         }


                     785 ;360:         else


                     786 ;361:         {


                     787 

                     788 ;362:             result = responseBuf.pos;


                     789 

000003b4 e59d4024    790 	ldr	r4,[sp,36]

                     791 ;363:         }


                     792 ;364: 	}


                     793 ;365: 	IEDTree_unlock();


                     794 

000003b8 eb000000*   795 	bl	IEDTree_unlock

                     796 ;366: 


                     797 ;367: 	if(result < 1)


                     798 

000003bc e3540000    799 	cmp	r4,0

000003c0 ca00000c    800 	bgt	.L252

000003c4 ea000006    801 	b	.L253

                     802 .L228:

                     803 ;356:         {


                     804 

                     805 ;357: 			ERROR_REPORT("encodeReadResponse error");


                     806 ;358:             result = 0;


                     807 

000003c8 e3a04000    808 	mov	r4,0

                     809 ;363:         }


                     810 ;364: 	}


                     811 ;365: 	IEDTree_unlock();


                     812 

000003cc eb000000*   813 	bl	IEDTree_unlock

                     814 ;366: 


                     815 ;367: 	if(result < 1)


                     816 

000003d0 ea000003    817 	b	.L253

                     818 .L227:

                     819 ;359:         }


                     820 ;360:         else


                     821 ;361:         {


                     822 

                     823 ;362:             result = responseBuf.pos;


                     824 

000003d4 e59d4024    825 	ldr	r4,[sp,36]

                     826 ;363:         }


                     827 ;364: 	}


                     828 ;365: 	IEDTree_unlock();


                     829 

000003d8 eb000000*   830 	bl	IEDTree_unlock

                     831 ;366: 


                     832 ;367: 	if(result < 1)


                     833 

000003dc e3540000    834 	cmp	r4,0

000003e0 ca000004    835 	bgt	.L252

                     836 .L253:

                     837 ;368: 	{


                     838 

                     839 ;369: 		return CreateMmsConfirmedErrorPdu(invokeId, response, 


                     840 

000003e4 e59d1064    841 	ldr	r1,[sp,100]

000003e8 e59d0060    842 	ldr	r0,[sp,96]

000003ec e3a02051    843 	mov	r2,81


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_f7s1.s
000003f0 eb000000*   844 	bl	CreateMmsConfirmedErrorPdu

000003f4 ea000000    845 	b	.L171

                     846 .L252:

                     847 ;370: 			MMS_ERROR_ACCESS_OBJECT_NON_EXISTENT);


                     848 ;371: 	}	


                     849 ;372: 		


                     850 ;373: 	//TODO Обработать случай когда domainIdStr и itemIdStr неинициализированы


                     851 ;374: 	return result;


                     852 

000003f8 e1a00004    853 	mov	r0,r4

                     854 .L171:

000003fc e28dd044    855 	add	sp,sp,68

00000400 e8bd8cf0    856 	ldmfd	[sp]!,{r4-r7,r10-fp,pc}

                     857 	.endf	mms_handleReadRequest

                     858 	.align	4

                     859 ;result	r4	local

                     860 ;objectName	[sp,56]	local

                     861 ;namedVariableListRequest	[sp,3]	local

                     862 ;tag	r5	local

                     863 ;iLength	[sp,4]	local

                     864 ;specListBer	[sp,44]	local

                     865 ;responseBuf	[sp,32]	local

                     866 ;object	r0	local

                     867 ;objIndex	r5	local

                     868 ;domainId	[sp,24]	local

                     869 ;itemId	[sp,16]	local

                     870 ;dataSetEntity	r4	local

                     871 ;readDataSize	[sp,8]	local

                     872 ;readDataSizeWithTL	r5	local

                     873 ;objectSpecSize	r10	local

                     874 ;objectSpecRespSize	r6	local

                     875 ;readResponseSize	r7	local

                     876 ;invokeIdSize	r0	local

                     877 ;accessResultSize	r6	local

                     878 ;confirmedServiceResponseContentLength	r5	local

                     879 ;confirmedServiceResponseLength	r7	local

                     880 ;invokeIdSize	r0	local

                     881 ;objReadLen	[sp,12]	local

                     882 

                     883 ;mmsConn	r4	param

                     884 ;inBuf	r7	param

                     885 ;bufPos	r6	param

                     886 ;maxBufPos	fp	param

                     887 ;invokeId	[sp,96]	param

                     888 ;response	[sp,100]	param

                     889 ;maxRespSize	r12	param

                     890 

                     891 	.section ".bss","awb"

                     892 .L891:

                     893 	.data

                     894 	.text

                     895 

                     896 ;375: }


                     897 	.align	4

                     898 

                     899 	.data

                     900 	.ghsnote version,6

                     901 	.ghsnote tools,3

                     902 	.ghsnote options,0

                     903 	.text

                     904 	.align	4


                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_f7s1.s
