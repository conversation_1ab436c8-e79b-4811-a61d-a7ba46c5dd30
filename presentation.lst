                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5o41.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=presentation.c -o gh_5o41.o -list=presentation.lst C:\Users\<USER>\AppData\Local\Temp\gh_5o41.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_5o41.s
Source File: presentation.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile presentation.c

                      10 ;		-o presentation.o

                      11 ;Source File:   presentation.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Oct 07 09:39:33 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "presentation.h"


                      21 ;2: #include "AsnEncoding.h"  


                      22 ;3: 


                      23 ;4: #include <stddef.h>


                      24 ;5: #include <string.h>


                      25 ;6: 


                      26 ;7: //параметры CONNECTION PRESENTATION


                      27 ;8: #define	MODE_SELECTOR_PARAMETER 0xa0


                      28 ;9: #define MODE_PARAMETER 0x80


                      29 ;10: #define NORMAL_MODE_PARAMETERS 0xa2


                      30 ;11: #define CALLING_PRESENTATION_SELECTOR 0x81


                      31 ;12: #define CALLED_PRESENTATION_SELECTOR 0x82


                      32 ;13: #define PRESENTATION_CONTEXT_DIFINITION_LIST 0xa4


                      33 ;14: #define PRESENTATION_USER_DATA 0x61


                      34 ;15: #define RESPONDING_PRESENTATION_SELECTOR 0x83


                      35 ;16: 


                      36 ;17: //параметры ACCEPT PRESENTATION


                      37 ;18: #define CONTEXT_DIFINITION_RESULT_LIST 0xa5


                      38 ;19: #define PRESENTATION_CONTEXT_DIFINITION_LIST_RESULT 0x80


                      39 ;20: #define	TRANFER_SYNTAX_NAME_PARAMETER 0x81


                      40 ;21: #define PRESENTATION_USER_DATA_NEXT	 0xa0


                      41 ;22: 


                      42 ;23: #define	PRESENTATION_DATA_PACKET_HEADER_SIZE	9


                      43 ;24: 


                      44 ;25: unsigned char berId[] = { 0x51, 0x01 };


                      45 ;26: unsigned char calledPresentationSelector[] = { 0x00, 0x00, 0x00, 0x01 };


                      46 ;27: 


                      47 ;28: static int encodeAcceptBer( unsigned char* buf, int bufPos )


                      48 	.text

                      49 	.align	4

                      50 encodeAcceptBer:


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5o41.s
00000000 e92d4010     51 	stmfd	[sp]!,{r4,lr}

00000004 e1a04000     52 	mov	r4,r0

                      53 ;29: {


                      54 

                      55 ;30: 


                      56 ;31:     bufPos = BerEncoder_encodeTL( ASN_SEQUENCE, 7, buf, bufPos );


                      57 

00000008 e1a02004     58 	mov	r2,r4

0000000c e1a03001     59 	mov	r3,r1

00000010 e3a01007     60 	mov	r1,7

00000014 e3a00030     61 	mov	r0,48

00000018 eb000000*    62 	bl	BerEncoder_encodeTL

                      63 ;32:     bufPos = BerEncoder_encodeTL( PRESENTATION_CONTEXT_DIFINITION_LIST_RESULT,


                      64 

0000001c e1a02004     65 	mov	r2,r4

00000020 e3a01001     66 	mov	r1,1

00000024 e1a03000     67 	mov	r3,r0

00000028 e3a00080     68 	mov	r0,128

0000002c eb000000*    69 	bl	BerEncoder_encodeTL

                      70 ;33:                                   1, buf, bufPos );


                      71 ;34:     buf[bufPos++] = 0;


                      72 

00000030 e1a02004     73 	mov	r2,r4

00000034 e3a01000     74 	mov	r1,0

00000038 e7c41000     75 	strb	r1,[r4,r0]

                      76 ;35:     bufPos = BerEncoder_encodeTL( TRANFER_SYNTAX_NAME_PARAMETER, 2, buf,


                      77 

0000003c e3a01002     78 	mov	r1,2

00000040 e2803001     79 	add	r3,r0,1

00000044 e3a00081     80 	mov	r0,129

00000048 eb000000*    81 	bl	BerEncoder_encodeTL

                      82 ;36:                                   bufPos );


                      83 ;37:     buf[bufPos++] = berId[0];		//0x51;


                      84 

0000004c e59f2370*    85 	ldr	r2,.L37

00000050 e5d23000     86 	ldrb	r3,[r2]

00000054 e2801001     87 	add	r1,r0,1

00000058 e7c43000     88 	strb	r3,[r4,r0]

                      89 ;38:     buf[bufPos++] = berId[1];		//0x01;


                      90 

0000005c e5d20001     91 	ldrb	r0,[r2,1]

00000060 e7c40001     92 	strb	r0,[r4,r1]

                      93 ;39: 


                      94 ;40:     return bufPos;


                      95 

00000064 e2810001     96 	add	r0,r1,1

00000068 e8bd4010     97 	ldmfd	[sp]!,{r4,lr}

0000006c e12fff1e*    98 	ret	

                      99 	.endf	encodeAcceptBer

                     100 	.align	4

                     101 

                     102 ;buf	r4	param

                     103 ;bufPos	r1	param

                     104 

                     105 	.data

                     106 .L30:

                     107 	.text

                     108 

                     109 ;41: }


                     110 

                     111 ;42: 



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5o41.s
                     112 ;43: 


                     113 ;44: //Пишет "шапку" UserData(для ACSE) или просто определяет её размер


                     114 ;45: //encode:


                     115 ;46: //  1 - реально писать в буфер. //Возвращает новую позицию в буфере


                     116 ;47: //  0 - или только определить размер. Возвращает размер


                     117 ;48: static int encodeUserData( unsigned char* buffer, int bufPos, int userDataLength,


                     118 	.align	4

                     119 	.align	4

                     120 encodeUserData:

00000070 e92d4cf0    121 	stmfd	[sp]!,{r4-r7,r10-fp,lr}

00000074 e1a06002    122 	mov	r6,r2

                     123 ;49:                     unsigned char encode, unsigned char contextId )


                     124 ;50: {


                     125 

00000078 e2864004    126 	add	r4,r6,4

                     127 ;51:     int fullyEncodedDataLength;


                     128 ;52:     int encodedDataSetLength = 3; /* presentation-selector */


                     129 

                     130 ;53: 


                     131 ;54:     // presentation-data


                     132 ;55:     encodedDataSetLength += userDataLength + 1;


                     133 

                     134 ;56:     encodedDataSetLength += BerEncoder_determineLengthSize(userDataLength);


                     135 

0000007c e1a0b001    136 	mov	fp,r1

00000080 e1a07003    137 	mov	r7,r3

00000084 e5dda01c    138 	ldrb	r10,[sp,28]

00000088 e1a05000    139 	mov	r5,r0

0000008c e1a00006    140 	mov	r0,r6

00000090 eb000000*   141 	bl	BerEncoder_determineLengthSize

00000094 e0844000    142 	add	r4,r4,r0

                     143 ;57: 


                     144 ;58:     fullyEncodedDataLength = encodedDataSetLength;


                     145 

                     146 ;59: 


                     147 ;60:     fullyEncodedDataLength += BerEncoder_determineLengthSize(encodedDataSetLength) + 1;


                     148 

00000098 e1a00004    149 	mov	r0,r4

0000009c eb000000*   150 	bl	BerEncoder_determineLengthSize

000000a0 e0800004    151 	add	r0,r0,r4

000000a4 e2800001    152 	add	r0,r0,1

                     153 ;61: 


                     154 ;62:     if (encode) {


                     155 

000000a8 e3570000    156 	cmp	r7,0

000000ac 0a000015    157 	beq	.L40

                     158 ;63:         /* fully-encoded-data */


                     159 ;64:         bufPos = BerEncoder_encodeTL(PRESENTATION_USER_DATA,


                     160 

000000b0 e1a0300b    161 	mov	r3,fp

000000b4 e1a02005    162 	mov	r2,r5

000000b8 e1a01000    163 	mov	r1,r0

000000bc e3a00061    164 	mov	r0,97

000000c0 eb000000*   165 	bl	BerEncoder_encodeTL

                     166 ;65:                                      fullyEncodedDataLength, buffer, bufPos);


                     167 ;66:         bufPos = BerEncoder_encodeTL(ASN_SEQUENCE, encodedDataSetLength, buffer,


                     168 

000000c4 e1a02005    169 	mov	r2,r5

000000c8 e1a01004    170 	mov	r1,r4

000000cc e1a03000    171 	mov	r3,r0

000000d0 e3a00030    172 	mov	r0,48


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5o41.s
000000d4 eb000000*   173 	bl	BerEncoder_encodeTL

                     174 ;67:                                      bufPos);


                     175 ;68: 


                     176 ;69:         /* presentation-selector acse */


                     177 ;70:         bufPos = BerEncoder_encodeTL(ASN_INTEGER, 1, buffer, bufPos);


                     178 

000000d8 e1a02005    179 	mov	r2,r5

000000dc e3a01001    180 	mov	r1,1

000000e0 e1a03000    181 	mov	r3,r0

000000e4 e3a00002    182 	mov	r0,2

000000e8 eb000000*   183 	bl	BerEncoder_encodeTL

                     184 ;71:         buffer[bufPos++] = contextId;


                     185 

000000ec e1a02005    186 	mov	r2,r5

000000f0 e1a01006    187 	mov	r1,r6

000000f4 e2803001    188 	add	r3,r0,1

000000f8 e7c5a000    189 	strb	r10,[r5,r0]

                     190 ;72: 


                     191 ;73:         /* presentation-data (= acse payload) */


                     192 ;74:         bufPos = BerEncoder_encodeTL(PRESENTATION_USER_DATA_NEXT,


                     193 

000000fc e3a000a0    194 	mov	r0,160

00000100 eb000000*   195 	bl	BerEncoder_encodeTL

                     196 ;75:                                      userDataLength, buffer, bufPos);


                     197 ;76: 


                     198 ;77:         return bufPos;


                     199 

00000104 ea000002    200 	b	.L38

                     201 .L40:

                     202 ;78:     }


                     203 ;79:     else {


                     204 

                     205 ;80:         int encodedUserDataLength = fullyEncodedDataLength + 1;


                     206 

00000108 e2804001    207 	add	r4,r0,1

                     208 ;81:         encodedUserDataLength += BerEncoder_determineLengthSize(fullyEncodedDataLength);


                     209 

0000010c eb000000*   210 	bl	BerEncoder_determineLengthSize

00000110 e0840000    211 	add	r0,r4,r0

                     212 ;82: 


                     213 ;83:         return encodedUserDataLength;


                     214 

                     215 .L38:

00000114 e8bd4cf0    216 	ldmfd	[sp]!,{r4-r7,r10-fp,lr}

00000118 e12fff1e*   217 	ret	

                     218 	.endf	encodeUserData

                     219 	.align	4

                     220 ;fullyEncodedDataLength	r0	local

                     221 ;encodedDataSetLength	r4	local

                     222 ;encodedUserDataLength	r4	local

                     223 

                     224 ;buffer	r5	param

                     225 ;bufPos	fp	param

                     226 ;userDataLength	r6	param

                     227 ;encode	r7	param

                     228 ;contextId	r10	param

                     229 

                     230 	.section ".bss","awb"

                     231 .L75:

                     232 	.data

                     233 	.text


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5o41.s
                     234 

                     235 ;84:     }


                     236 ;85: }


                     237 

                     238 ;86: 


                     239 ;87: void initPresentation(IsoPresentation* presentation)


                     240 	.align	4

                     241 	.align	4

                     242 initPresentation::

                     243 ;88: {


                     244 

                     245 ;89:     //TODO acseContextId


                     246 ;90:     presentation->presentationContextId = 3;


                     247 

0000011c e3a01003    248 	mov	r1,3

00000120 e5c01008    249 	strb	r1,[r0,8]

                     250 ;91:     presentation->acseContextId = 1;


                     251 

00000124 e3a01001    252 	mov	r1,1

00000128 e5c0100a    253 	strb	r1,[r0,10]

0000012c e12fff1e*   254 	ret	

                     255 	.endf	initPresentation

                     256 	.align	4

                     257 

                     258 ;presentation	r0	param

                     259 

                     260 	.section ".bss","awb"

                     261 .L110:

                     262 	.data

                     263 	.text

                     264 

                     265 ;92: }


                     266 

                     267 ;93: 


                     268 ;94: int isoPresentation_createCpaMessage(IsoPresentation* presentation,


                     269 	.align	4

                     270 	.align	4

                     271 isoPresentation_createCpaMessage::

00000130 e92d4cf0    272 	stmfd	[sp]!,{r4-r7,r10-fp,lr}

                     273 ;95:                                  unsigned char* buf, unsigned char* userData, int userDataLen)


                     274 ;96: {


                     275 

                     276 ;97:     int contentLength = 0;


                     277 

                     278 ;98:     int normalModeLength = 0;


                     279 

                     280 ;99:     int bufPos = 0;


                     281 

                     282 ;100: 


                     283 ;101:     // mode-selector


                     284 ;102:     contentLength += 5;


                     285 

                     286 ;103:     normalModeLength += 6; // responding-presentation-selector


                     287 

                     288 ;104:     normalModeLength += 20; // context-definition-result-list


                     289 

                     290 ;105:     normalModeLength += encodeUserData(NULL, 0, userDataLen, 0/*encode*/,


                     291 

00000134 e1a0b002    292 	mov	fp,r2

00000138 e1a05003    293 	mov	r5,r3

0000013c e1a02005    294 	mov	r2,r5


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5o41.s
00000140 e1a04001    295 	mov	r4,r1

00000144 e3a03000    296 	mov	r3,0

00000148 e1a07000    297 	mov	r7,r0

0000014c e5d7000a    298 	ldrb	r0,[r7,10]

00000150 e1a01003    299 	mov	r1,r3

00000154 e52d0004    300 	str	r0,[sp,-4]!

00000158 e1a00003    301 	mov	r0,r3

0000015c ebffffc3*   302 	bl	encodeUserData

00000160 e280601a    303 	add	r6,r0,26

                     304 ;106:                                        presentation->acseContextId);


                     305 ;107: 


                     306 ;108:     contentLength += normalModeLength;


                     307 

00000164 e280a01f    308 	add	r10,r0,31

                     309 ;109: 


                     310 ;110:     contentLength += BerEncoder_determineLengthSize(normalModeLength) + 1;


                     311 

00000168 e1a00006    312 	mov	r0,r6

0000016c eb000000*   313 	bl	BerEncoder_determineLengthSize

00000170 e1a02004    314 	mov	r2,r4

00000174 e3a03000    315 	mov	r3,0

00000178 e080000a    316 	add	r0,r0,r10

0000017c e2801001    317 	add	r1,r0,1

                     318 ;111: 


                     319 ;112:     bufPos = BerEncoder_encodeTL(ASN_SET, contentLength, buf, bufPos);


                     320 

00000180 e3a00031    321 	mov	r0,49

00000184 eb000000*   322 	bl	BerEncoder_encodeTL

                     323 ;113: 


                     324 ;114:     /* mode-selector */


                     325 ;115:     bufPos = BerEncoder_encodeTL(MODE_SELECTOR_PARAMETER, 3, buf, bufPos);


                     326 

00000188 e1a02004    327 	mov	r2,r4

0000018c e3a01003    328 	mov	r1,3

00000190 e1a03000    329 	mov	r3,r0

00000194 e3a000a0    330 	mov	r0,160

00000198 eb000000*   331 	bl	BerEncoder_encodeTL

                     332 ;116:     bufPos = BerEncoder_encodeTL(MODE_PARAMETER, 1, buf, bufPos);


                     333 

0000019c e1a02004    334 	mov	r2,r4

000001a0 e3a01001    335 	mov	r1,1

000001a4 e1a03000    336 	mov	r3,r0

000001a8 e3a00080    337 	mov	r0,128

000001ac eb000000*   338 	bl	BerEncoder_encodeTL

                     339 ;117:     buf[bufPos++] = 1; /* 1 = normal-mode */


                     340 

000001b0 e1a02004    341 	mov	r2,r4

000001b4 e1a01006    342 	mov	r1,r6

000001b8 e2803001    343 	add	r3,r0,1

000001bc e3a0a001    344 	mov	r10,1

000001c0 e7c4a000    345 	strb	r10,[r4,r0]

                     346 ;118: 


                     347 ;119:     /* normal-mode-parameters */


                     348 ;120:     bufPos = BerEncoder_encodeTL(NORMAL_MODE_PARAMETERS, normalModeLength, buf, bufPos);


                     349 

000001c4 e3a000a2    350 	mov	r0,162

000001c8 eb000000*   351 	bl	BerEncoder_encodeTL

                     352 ;121: 


                     353 ;122:     /* responding-presentation-selector */


                     354 ;123:     bufPos = BerEncoder_encodeTL(RESPONDING_PRESENTATION_SELECTOR, 4, buf, bufPos);


                     355 


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5o41.s
000001cc e1a02004    356 	mov	r2,r4

000001d0 e3a01004    357 	mov	r1,4

000001d4 e1a03000    358 	mov	r3,r0

000001d8 e3a00083    359 	mov	r0,131

000001dc eb000000*   360 	bl	BerEncoder_encodeTL

000001e0 e59f11e0*   361 	ldr	r1,.L152

000001e4 e1a06000    362 	mov	r6,r0

                     363 ;124:     memcpy(buf + bufPos, calledPresentationSelector, 4);


                     364 

000001e8 e0860004    365 	add	r0,r6,r4

000001ec e3a02004    366 	mov	r2,4

000001f0 eb000000*   367 	bl	memcpy

                     368 ;125:     bufPos += 4;


                     369 

000001f4 e2863004    370 	add	r3,r6,4

                     371 ;126: 


                     372 ;127:     /* context-definition-result-list */


                     373 ;128:     bufPos = BerEncoder_encodeTL(CONTEXT_DIFINITION_RESULT_LIST, 18, buf, bufPos);


                     374 

000001f8 e1a02004    375 	mov	r2,r4

000001fc e3a01012    376 	mov	r1,18

00000200 e3a000a5    377 	mov	r0,165

00000204 eb000000*   378 	bl	BerEncoder_encodeTL

                     379 ;129:     bufPos = encodeAcceptBer(buf, bufPos); /* accept for acse */


                     380 

00000208 e1a01000    381 	mov	r1,r0

0000020c e1a00004    382 	mov	r0,r4

00000210 ebffff7a*   383 	bl	encodeAcceptBer

                     384 ;130:     bufPos = encodeAcceptBer(buf, bufPos); /* accept for mms */


                     385 

00000214 e1a01000    386 	mov	r1,r0

00000218 e1a00004    387 	mov	r0,r4

0000021c ebffff77*   388 	bl	encodeAcceptBer

                     389 ;131: 


                     390 ;132:     /* encode user data */


                     391 ;133:     //Пишем "шапку"


                     392 ;134:     bufPos = encodeUserData(buf, bufPos, userDataLen, 1, presentation->acseContextId);


                     393 

00000220 e5d7100a    394 	ldrb	r1,[r7,10]

00000224 e1a02005    395 	mov	r2,r5

00000228 e58d1000    396 	str	r1,[sp]

0000022c e1a01000    397 	mov	r1,r0

00000230 e1a00004    398 	mov	r0,r4

00000234 e1a0300a    399 	mov	r3,r10

00000238 ebffff8c*   400 	bl	encodeUserData

0000023c e1a02005    401 	mov	r2,r5

00000240 e1a0100b    402 	mov	r1,fp

00000244 e1a06000    403 	mov	r6,r0

                     404 ;135:     //Пишем сами данные


                     405 ;136:     memcpy( buf + bufPos, userData, userDataLen );


                     406 

00000248 e0860004    407 	add	r0,r6,r4

0000024c eb000000*   408 	bl	memcpy

                     409 ;137:     return bufPos + userDataLen;


                     410 

00000250 e0860005    411 	add	r0,r6,r5

00000254 e28dd004    412 	add	sp,sp,4

00000258 e8bd8cf0    413 	ldmfd	[sp]!,{r4-r7,r10-fp,pc}

                     414 	.endf	isoPresentation_createCpaMessage

                     415 	.align	4

                     416 ;contentLength	r10	local


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5o41.s
                     417 ;normalModeLength	r6	local

                     418 ;bufPos	r6	local

                     419 

                     420 ;presentation	r7	param

                     421 ;buf	r4	param

                     422 ;userData	fp	param

                     423 ;userDataLen	r5	param

                     424 

                     425 	.section ".bss","awb"

                     426 .L145:

                     427 	.data

                     428 	.text

                     429 

                     430 ;138: }


                     431 

                     432 ;139: 


                     433 ;140: 


                     434 ;141: int isoPresentation_parseUserData(IsoPresentation* presentation,


                     435 	.align	4

                     436 	.align	4

                     437 isoPresentation_parseUserData::

0000025c e92d40f0    438 	stmfd	[sp]!,{r4-r7,lr}

                     439 ;142:                                   unsigned char* inBuf, int inLen,


                     440 ;143:                                   unsigned char** pOutUserData)


                     441 ;144: {        


                     442 

                     443 ;145:     int len;//dummy?


                     444 ;146:     int userDataLength;


                     445 ;147:     int bufPos = 0;


                     446 

                     447 ;148: 


                     448 ;149:     if (inLen < PRESENTATION_DATA_PACKET_HEADER_SIZE)


                     449 

00000260 e24dd008    450 	sub	sp,sp,8

00000264 e1a06000    451 	mov	r6,r0

00000268 e1a04001    452 	mov	r4,r1

0000026c e1a05002    453 	mov	r5,r2

00000270 e3550009    454 	cmp	r5,9

00000274 e3a02000    455 	mov	r2,0

                     456 ;150:     {


                     457 

                     458 ;151:         return -1;


                     459 

                     460 ;152:     }


                     461 ;153: 


                     462 ;154:     if (inBuf[bufPos++] != PRESENTATION_USER_DATA)


                     463 

00000278 a7d40002    464 	ldrgeb	r0,[r4,r2]

0000027c e1a07003    465 	mov	r7,r3

00000280 a3500061    466 	cmpge	r0,97

00000284 1a000019    467 	bne	.L171

                     468 ;155:     {


                     469 

                     470 ;156:         return -1;


                     471 

                     472 ;157:     }


                     473 ;158:     bufPos = BerDecoder_decodeLength(inBuf, &len, bufPos, inLen);


                     474 

00000288 e1a03005    475 	mov	r3,r5

0000028c e1a0100d    476 	mov	r1,sp

00000290 e1a00004    477 	mov	r0,r4


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5o41.s
00000294 e3a02001    478 	mov	r2,1

00000298 eb000000*   479 	bl	BerDecoder_decodeLength

                     480 ;159: 


                     481 ;160:     if (inBuf[bufPos++] != ASN_SEQUENCE)


                     482 

0000029c e2802001    483 	add	r2,r0,1

000002a0 e7d40000    484 	ldrb	r0,[r4,r0]

000002a4 e3500030    485 	cmp	r0,48

000002a8 1a000010    486 	bne	.L171

                     487 ;161:     {


                     488 

                     489 ;162:         return -1;


                     490 

                     491 ;163:     }


                     492 ;164: 


                     493 ;165:     bufPos = BerDecoder_decodeLength(inBuf, &len, bufPos, inLen);


                     494 

000002ac e1a03005    495 	mov	r3,r5

000002b0 e1a0100d    496 	mov	r1,sp

000002b4 e1a00004    497 	mov	r0,r4

000002b8 eb000000*   498 	bl	BerDecoder_decodeLength

                     499 ;166: 


                     500 ;167:     if (inBuf[bufPos++] != ASN_INTEGER)


                     501 

000002bc e2802001    502 	add	r2,r0,1

000002c0 e7d40000    503 	ldrb	r0,[r4,r0]

000002c4 e3500002    504 	cmp	r0,2

                     505 ;168:     {


                     506 

                     507 ;169:         return -1;


                     508 

                     509 ;170:     }


                     510 ;171: 


                     511 ;172:     if (inBuf[bufPos++] != 0x01)


                     512 

000002c8 07d40002    513 	ldreqb	r0,[r4,r2]

000002cc 02822001    514 	addeq	r2,r2,1

000002d0 03500001    515 	cmpeq	r0,1

000002d4 1a000005    516 	bne	.L171

                     517 ;173:     {


                     518 

                     519 ;174:         return -1;


                     520 

                     521 ;175:     }


                     522 ;176: 


                     523 ;177:     presentation->nextContextId = inBuf[bufPos++];


                     524 

000002d8 e7d40002    525 	ldrb	r0,[r4,r2]

000002dc e2822001    526 	add	r2,r2,1

000002e0 e5c60009    527 	strb	r0,[r6,9]

                     528 ;178: 


                     529 ;179:     if (inBuf[bufPos++] != PRESENTATION_USER_DATA_NEXT)


                     530 

000002e4 e7d40002    531 	ldrb	r0,[r4,r2]

000002e8 e2822001    532 	add	r2,r2,1

000002ec e35000a0    533 	cmp	r0,160

                     534 .L171:

                     535 ;180:     {


                     536 

                     537 ;181:         return -1;


                     538 


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5o41.s
000002f0 13e00000    539 	mvnne	r0,0

000002f4 1a000006    540 	bne	.L153

                     541 .L170:

                     542 ;182:     }


                     543 ;183: 


                     544 ;184:     bufPos = BerDecoder_decodeLength(inBuf, &userDataLength, bufPos, inLen);


                     545 

000002f8 e1a03005    546 	mov	r3,r5

000002fc e28d1004    547 	add	r1,sp,4

00000300 e1a00004    548 	mov	r0,r4

00000304 eb000000*   549 	bl	BerDecoder_decodeLength

                     550 ;185: 


                     551 ;186:     *pOutUserData = inBuf +bufPos;


                     552 

00000308 e0800004    553 	add	r0,r0,r4

0000030c e5870000    554 	str	r0,[r7]

                     555 ;187: 


                     556 ;188:     return userDataLength;


                     557 

00000310 e59d0004    558 	ldr	r0,[sp,4]

                     559 .L153:

00000314 e28dd008    560 	add	sp,sp,8

00000318 e8bd80f0    561 	ldmfd	[sp]!,{r4-r7,pc}

                     562 	.endf	isoPresentation_parseUserData

                     563 	.align	4

                     564 ;len	[sp]	local

                     565 ;userDataLength	[sp,4]	local

                     566 ;bufPos	r2	local

                     567 

                     568 ;presentation	r6	param

                     569 ;inBuf	r4	param

                     570 ;inLen	r5	param

                     571 ;pOutUserData	r7	param

                     572 

                     573 	.section ".bss","awb"

                     574 .L260:

                     575 	.data

                     576 	.text

                     577 

                     578 ;189: }


                     579 

                     580 ;190: 


                     581 ;191: int IsoPresentation_createUserData(IsoPresentation* presentation,


                     582 	.align	4

                     583 	.align	4

                     584 IsoPresentation_createUserData::

0000031c e92d44f0    585 	stmfd	[sp]!,{r4-r7,r10,lr}

                     586 ;192:                                    unsigned char* buf, unsigned char* userData, int userDataLen)


                     587 ;193: {


                     588 

                     589 ;194:     int bufPos = 0;


                     590 

                     591 ;195: 


                     592 ;196:     int userDataLengthFieldSize = BerEncoder_determineLengthSize(userDataLen);


                     593 

00000320 e1a04001    594 	mov	r4,r1

00000324 e1a07002    595 	mov	r7,r2

00000328 e1a06000    596 	mov	r6,r0

0000032c e1a05003    597 	mov	r5,r3

00000330 e1a00005    598 	mov	r0,r5

00000334 eb000000*   599 	bl	BerEncoder_determineLengthSize


                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5o41.s
                     600 ;197: 


                     601 ;198:     int pdvListLength = userDataLen + (userDataLengthFieldSize + 4);


                     602 

00000338 e0800005    603 	add	r0,r0,r5

0000033c e280a004    604 	add	r10,r0,4

                     605 ;199: 


                     606 ;200:     int pdvListLengthFieldSize = BerEncoder_determineLengthSize(pdvListLength);


                     607 

00000340 e1a0000a    608 	mov	r0,r10

00000344 eb000000*   609 	bl	BerEncoder_determineLengthSize

                     610 ;201:     int presentationLength = pdvListLength + (pdvListLengthFieldSize + 1);


                     611 

00000348 e1a02004    612 	mov	r2,r4

0000034c e3a03000    613 	mov	r3,0

00000350 e08a0000    614 	add	r0,r10,r0

00000354 e2801001    615 	add	r1,r0,1

                     616 ;202: 


                     617 ;203:     bufPos = BerEncoder_encodeTL(PRESENTATION_USER_DATA, presentationLength,


                     618 

00000358 e3a00061    619 	mov	r0,97

0000035c eb000000*   620 	bl	BerEncoder_encodeTL

                     621 ;204:                                  buf, bufPos);


                     622 ;205: 


                     623 ;206:     bufPos = BerEncoder_encodeTL(ASN_SEQUENCE, pdvListLength, buf, bufPos);


                     624 

00000360 e1a02004    625 	mov	r2,r4

00000364 e1a0100a    626 	mov	r1,r10

00000368 e1a03000    627 	mov	r3,r0

0000036c e3a00030    628 	mov	r0,48

00000370 eb000000*   629 	bl	BerEncoder_encodeTL

                     630 ;207: 


                     631 ;208:     buf[bufPos++] = ASN_INTEGER;


                     632 

00000374 e280a001    633 	add	r10,r0,1

00000378 e3a01002    634 	mov	r1,2

0000037c e7c41000    635 	strb	r1,[r4,r0]

                     636 ;209:     buf[bufPos++] = 0x01;


                     637 

00000380 e3a00001    638 	mov	r0,1

00000384 e7c4000a    639 	strb	r0,[r4,r10]

00000388 e28aa001    640 	add	r10,r10,1

                     641 ;210:     buf[bufPos++] = presentation->presentationContextId;


                     642 

0000038c e28a3001    643 	add	r3,r10,1

                     644 ;211: 


                     645 ;212:     bufPos = BerEncoder_encodeTL(PRESENTATION_USER_DATA_NEXT, userDataLen,


                     646 

00000390 e1a02004    647 	mov	r2,r4

00000394 e5d60008    648 	ldrb	r0,[r6,8]

00000398 e1a01005    649 	mov	r1,r5

0000039c e7c4000a    650 	strb	r0,[r4,r10]

000003a0 e3a000a0    651 	mov	r0,160

000003a4 eb000000*   652 	bl	BerEncoder_encodeTL

000003a8 e1a02005    653 	mov	r2,r5

000003ac e1a01007    654 	mov	r1,r7

000003b0 e1a0a000    655 	mov	r10,r0

                     656 ;213:                                  buf, bufPos);


                     657 ;214: 


                     658 ;215:     memcpy( buf + bufPos, userData, userDataLen );


                     659 

000003b4 e08a0004    660 	add	r0,r10,r4


                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5o41.s
000003b8 eb000000*   661 	bl	memcpy

                     662 ;216:     return bufPos + userDataLen;


                     663 

000003bc e08a0005    664 	add	r0,r10,r5

000003c0 e8bd84f0    665 	ldmfd	[sp]!,{r4-r7,r10,pc}

                     666 	.endf	IsoPresentation_createUserData

                     667 	.align	4

                     668 ;bufPos	r10	local

                     669 ;pdvListLength	r10	local

                     670 

                     671 ;presentation	r6	param

                     672 ;buf	r4	param

                     673 ;userData	r7	param

                     674 ;userDataLen	r5	param

                     675 

                     676 	.section ".bss","awb"

                     677 .L318:

                     678 	.data

                     679 	.text

                     680 

                     681 ;217: }


                     682 	.align	4

                     683 .L37:

000003c4 00000000*   684 	.data.w	berId

                     685 	.type	.L37,$object

                     686 	.size	.L37,4

                     687 

                     688 .L152:

000003c8 00000000*   689 	.data.w	calledPresentationSelector

                     690 	.type	.L152,$object

                     691 	.size	.L152,4

                     692 

                     693 	.align	4

                     694 

                     695 	.data

                     696 .L340:

                     697 	.globl	berId

00000000 0151       698 berId:	.data.b	81,1

                     699 	.type	berId,$object

                     700 	.size	berId,2

00000002 0000       701 	.space	2

                     702 .L341:

                     703 	.globl	calledPresentationSelector

00000004 00         704 calledPresentationSelector:	.space	1

00000005 00         705 	.space	1

00000006 00         706 	.space	1

00000007 01         707 	.data.b	1

                     708 	.type	calledPresentationSelector,$object

                     709 	.size	calledPresentationSelector,4

                     710 .L342:

                     711 	.globl	Asn_Id_Acse

00000008 0152       712 Asn_Id_Acse:	.data.b	82,1

0000000a 00         713 	.space	1

0000000b 01         714 	.data.b	1

                     715 	.type	Asn_Id_Acse,$object

                     716 	.size	Asn_Id_Acse,4

                     717 .L343:

                     718 	.globl	Asn_Id_Mms

0000000c 0222ca28    719 Asn_Id_Mms:	.data.b	40,202,34,2

00000010 01         720 	.data.b	1

00000011 000000     721 	.space	3


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5o41.s
                     722 	.type	Asn_Id_Mms,$object

                     723 	.size	Asn_Id_Mms,8

                     724 	.ghsnote version,6

                     725 	.ghsnote tools,3

                     726 	.ghsnote options,0

                     727 	.text

                     728 	.align	4

                     729 	.data

                     730 	.align	4

                     731 	.text

