#include "OscFiles.h"
#include "OscDescr.h"
#include "../pwin_access.h"
#include <string.h>
#include <stdlib.h>
#include "../tlsf/tlsf.h"
#include "OscConverter.h"
#include "platform_critical_section.h"
#define OSC_POOL_SIZE	(8 * 1024 * 1024)
//! пул для конвертилки осцилограммы
tlsf_t osc_tlsf;
//! для malloc,free
#pragma alignvar (4)
CriticalSection tlsfCS;

bool OSCFS_init(void)
{
	void *osc_pool = malloc(OSC_POOL_SIZE);
	
	if (!osc_pool)
	{
		return FALSE;
	}

	osc_tlsf = tlsf_create_with_pool(osc_pool, OSC_POOL_SIZE);
	if (!osc_tlsf)
	{
		return FALSE;
	}

	CriticalSection_Init(&tlsfCS);
	

	if (!OscConverter_init())
	{
		return FALSE;
	}
	// инициализация описаний
	if (!OSCDescr_init())
	{
		return FALSE;
	}
	// инициализация заголовка
	if (!OSCInfo_init())
	{
		return FALSE;
	}

	return TRUE;
}

void * OscFiles_malloc(size_t size)
{
	void *result;
	CriticalSection_Lock(&tlsfCS);
	result = tlsf_malloc(osc_tlsf, size);
	//result = malloc(size);
	CriticalSection_Unlock(&tlsfCS);
	return result;
}
void * OscFiles_realloc(void *p, size_t size)
{
	void *result;
	CriticalSection_Lock(&tlsfCS);
	result = tlsf_realloc(osc_tlsf, p, size);
	//result = realloc(p, size);
	CriticalSection_Unlock(&tlsfCS);

	return result;
}

void OscFiles_free(void *p)
{
	CriticalSection_Lock(&tlsfCS);
	tlsf_free(osc_tlsf, p);
	//free(p);
	CriticalSection_Unlock(&tlsfCS);
}

static FNameErrCode fillFindData(FSFindData* findData, BufferView* fnameBuf,
	int findResult, PWFileInfo* fileInfo)
{
	//Сюда запишем номер осциллограммы в текстовом виде
    char* pNum;
	if (findResult == 0)
	{
		//Больше нет осциллограмм
		return FNAME_NOT_FOUND;
	}
	else if (findResult < 0)
	{
		//Ошибка
		return FNAME_ERROR;
	}

	findData->fileIndex = findResult;
	findData->attr.fileSize = 0;
	findData->attr.time = fileInfo->t;
	findData->attr.ms = fileInfo->ms;

	//Пишем имя
	if (!BufferView_writeStr(fnameBuf, "osc"))
	{
		return FNAME_BUF_ERROR;
	}
    if (!BufferView_alloc(fnameBuf, 11, (uint8_t**)&pNum))
	{
		return FNAME_BUF_ERROR;
	}
	_itoa(fileInfo->name, pNum, 10);
	fnameBuf->pos += strlen(pNum);
	if (!BufferView_writeStr(fnameBuf, ".zip"))
	{
		return FNAME_BUF_ERROR;
	}
	return FNAME_OK;
}

FNameErrCode OSCFS_findFirst(StringView* startFileName, FSFindData* findData,
	BufferView* fnameBuf)
{
	PWFileInfo fileInfo;
	int findResult;	
	int oscNum = 0;
	if (startFileName != NULL) {
		//Пропускаем "osc", а все оставшиеся цифры интерпертируем как номер
        oscNum = atoi((char*)startFileName->p + 3);
		if (oscNum < 1)
		{
			return FNAME_NOT_FOUND;
		}
	}

	findResult = pwaOscFindFirst(oscNum, &fileInfo);		
	return fillFindData(findData, fnameBuf, findResult, &fileInfo);
}

FNameErrCode OSCFS_findNext(FSFindData* findData, BufferView* fnameBuf)
{
	PWFileInfo fileInfo;
	int findResult = pwaOscFindNext(findData->fileIndex, &fileInfo);		
	return fillFindData(findData, fnameBuf, findResult, &fileInfo);
}

void OSCFS_findClose(FSFindData *findData)
{
	pwaOscFindClose();
}

//! читает осцилограмму в OscWriteBuffer согласно его размеру
static int readOsc(FRSM *frsm, int offset, OscWriteBuffer *wb)
{
	PWFileInfo *fileInfo;
	int result;
	unsigned char *pData;
	int readSize;

	fileInfo = &frsm->readOscContext->fileInfo;

	OscWriteBuffer_reset(wb);
	pData = OscWriteBuffer_data(wb);
	readSize = OscWriteBuffer_size(wb);

	// читается сколько влезет в буфер
	result = pwaOscOscRead(fileInfo, offset, pData, readSize);
	if (result > 0)
	{
		// успешно прочитали, новый размер
		if (!OscWriteBuffer_resize(wb, result))
		{
			return -1;
		}
	}

	return result;
}

//! из имени файла в номер осцилограмма
static int fileNameToOscNum(StringView* fileName)
{
	// приходит что-то вроде osc10.zip
	// срезается osc, а .zip не конвертится
	return atoi((char*)fileName->p + 3);
}

//! создает контекст чтения осцилограммы
static OscReadFileContext *createOscReadContext(StringView* fileName)
{
	int findResult;
	PWFileInfo fileInfo;

	int oscNum = fileNameToOscNum(fileName);
	if (oscNum < 1)
	{
		return NULL;
	}

	// ищем осцилограмму с соответствущим именем
	findResult = pwaOscFindFirst(oscNum, &fileInfo);
	if (findResult <= 0)
	{
		pwaOscFindClose();
		return NULL;
	}
	pwaOscFindClose();
	return OscReadFileContext_create(&fileInfo);
}

//! освобождает контекст чтения, всегда возвращает FALSE
static bool freeOscReadContext(FRSM* frsm)
{
	OscReadFileContext *readFileContext = frsm->readOscContext;
	// т.к. oscInfo создается createOscReadContext, освобождаем здесь
	if (readFileContext->oscInfo)
	{
		OSCInfo_destroy(readFileContext->oscInfo);
		readFileContext->oscInfo = NULL;
	}

	if (frsm->readOscContext)
	{
		OscReadFileContext_destroy(frsm->readOscContext);
	}

	return FALSE;
}
bool OSCFS_openFile(StringView* fileName, FRSM* frsm, FSFileAttr* attr)
{
	int readSize;
	OscWriteBuffer *headerBuf;
	OscWriteBuffer *contentBuf;
	OSCInfoStruct *oscInfo;

	// контекст чтения
	frsm->readOscContext = createOscReadContext(fileName);
	if (!frsm->readOscContext)
	{
		return FALSE;
	}
	
	// чтение заголовка об осcилограмме
	// указатель на временный буфер
	headerBuf = OSCInfo_lockHeaderBuf();
	readSize = readOsc(frsm, 0, headerBuf);
	if ( readSize <= 0)
	{
		OSCInfo_unlockHeaderBuf();
		return freeOscReadContext(frsm);
	}

	// инициализация заголовка осцилограммы
	oscInfo = OSCInfo_create(headerBuf);
	OSCInfo_unlockHeaderBuf();
	if (!oscInfo)
	{
		return freeOscReadContext(frsm);
	}
	frsm->readOscContext->oscInfo = oscInfo;

	
	// инициализация состава
	// буфер под состав
	contentBuf = OSCInfo_getBufferContent(oscInfo);
	readSize = readOsc(frsm, OSCInfo_getHeaderSize(oscInfo), contentBuf);
	if ( readSize <= 0)
	{
		return freeOscReadContext(frsm);
	}

	// состав осцилограммы
	// oscInfo содержит указатель на contentBuf, поэтому он не передается
	if (!OSCInfo_initContent(oscInfo))
	{
		return freeOscReadContext(frsm);
	}
	attr->fileSize = 0;
	attr->time = frsm->readOscContext->fileInfo.t;
	attr->ms = frsm->readOscContext->fileInfo.ms;
	return TRUE;
}

bool OSCFS_closeFile(FRSM* frsm)
{
	freeOscReadContext(frsm);
	return TRUE;
}

bool OSCFS_readFile(FRSM* frsm, BufferView* readBuf, bool* moreFollows)
{
	OscReadFileContext *readFileContext = frsm->readOscContext;
	OSCInfoStruct *oscInfo;
	OscWriteBuffer *oscPeriodBuf;
	int readSize;
	size_t frameOffset;
	unsigned int frameNum;
	OscWriteBuffer *streamBuffer = &readFileContext->streamBuffer;

	if (!readFileContext)
	{
		return FALSE;
	}

	oscInfo = frsm->readOscContext->oscInfo;
	if (!oscInfo)
	{
		return FALSE;
	}

	oscPeriodBuf = OSCInfo_getFrameBuffer(oscInfo);
	if (!oscPeriodBuf)
	{
		return FALSE;
	}

	// пока есть буфер с предыдущего запуска - пишем его
	if (!OscWriteBuffer_empty(streamBuffer))
	{
		OscWriteBuffer_toBufferView(readBuf, streamBuffer);
		*moreFollows = TRUE;
		return TRUE;
	}


	frameNum = readFileContext->curFrame;
	// все фреймы обработаны, можем обрабатывать остальные файлы
	if (frameNum >= OSCInfo_getFrameCount(oscInfo))
	{
		// cfg файл
		OscWriteBuffer *cfgBuffer = &readFileContext->cfgBuffer;
		// hdr file
		OscWriteBuffer *hdrBuffer = &readFileContext->hdrBuffer;
		if (!OscWriteBuffer_empty(cfgBuffer))
		{
			OscWriteBuffer_toBufferView(readBuf, cfgBuffer);
			*moreFollows = TRUE;
			return TRUE;
		}


		if (!OscWriteBuffer_empty(hdrBuffer))
		{
			OscWriteBuffer_toBufferView(readBuf, hdrBuffer);
			*moreFollows = TRUE;
			return TRUE;
		}

		*moreFollows = FALSE;
		return TRUE;
	}

	// смещение нового фрейма
	frameOffset = OSCInfo_getFrameOffset(oscInfo, frameNum);
	// чтение фрейма
	readSize = readOsc(frsm, frameOffset, oscPeriodBuf);

	// нечего читать (по идее сюда дойти не должно), т.к. 
	// выше сработает проверка if (frameNum >= OSCInfo_getFrameCount(oscInfo))
	if (readSize == 0)
	{
		return FALSE;
	}
	// ошибка чтения
	if (readSize < 0)
	{
		return FALSE;
	}

	// конвертация периода
	if (!OscConverter_processPeriod(readFileContext))
	{
		return FALSE;
	}
	
	// 
	if (!OscReadFileContext_writeDatToStream(readFileContext))
	{
		return FALSE;
	}


	readFileContext->curFrame++;
	// последний фрейм, нужно записать cfg
	if (readFileContext->curFrame >= OSCInfo_getFrameCount(oscInfo))
	{
		
		// закрываем dat и пишем все остальные файлы
		bool result = 
			// dat
			OscReadFileContext_closeDat(readFileContext) && 
			// cfg
			OscConverter_processCfg(readFileContext) &&
			OscReadFileContext_writeCfgToStream(readFileContext) &&
			OscReadFileContext_closeCfg(readFileContext) &&
			// hdr
			OscConverter_processHdr(readFileContext) &&
			OscReadFileContext_writeHdrToStream(readFileContext) &&
			OscReadFileContext_closeHdr(readFileContext) &&
			// все записать в stream
			OscReadFileContext_flushAndClose(readFileContext);
	
		if (!result)
		{
			return FALSE;
		}

	}


	// пишем сколько влезет в readBuf
	OscWriteBuffer_toBufferView(readBuf, streamBuffer);
	*moreFollows = TRUE;

	return TRUE;
}


