                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fec1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=mms_get_variable_access_attributes.c -o gh_fec1.o -list=mms_get_variable_access_attributes.lst C:\Users\<USER>\AppData\Local\Temp\gh_fec1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_fec1.s
Source File: mms_get_variable_access_attributes.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      10 ;		mms_get_variable_access_attributes.c -o

                      11 ;		mms_get_variable_access_attributes.o

                      12 ;Source File:   mms_get_variable_access_attributes.c

                      13 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      14 ;Compile Date:  Tue Sep 30 09:56:01 2025

                      15 ;Host OS:       Win32

                      16 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      17 ;Release:       MULTI v4.2.3

                      18 ;Revision Date: Wed Mar 29 05:25:47 2006

                      19 ;Release Date:  Fri Mar 31 10:02:14 2006

                      20 

                      21 ;1: #include "mms_get_variable_access_attributes.h"


                      22 ;2: #include "AsnEncoding.h"  


                      23 ;3: #include "MmsConst.h"


                      24 ;4: #include "mmsservices.h"


                      25 ;5: #include "iedmodel.h"


                      26 ;6: #include "mms_error.h"


                      27 ;7: #include <debug.h>


                      28 ;8: 


                      29 ;9: #include <stddef.h>


                      30 ;10: #include <string.h>


                      31 ;11: 


                      32 ;12: static int encodeGetVariableAccessAttrResponse(unsigned int invokeId, int objectPos,


                      33 

                      34 ;57: }


                      35 

                      36 ;58: 


                      37 ;59: static int getVariableAccessAttr(unsigned int invokeId,


                      38 

                      39 ;81: }


                      40 

                      41 ;82: 


                      42 ;83: int mms_handleGetVariableAccessAttr(MmsConnection* mmsConn,


                      43 	.text

                      44 	.align	4

                      45 mms_handleGetVariableAccessAttr::

00000000 e92d4df0     46 	stmfd	[sp]!,{r4-r8,r10-fp,lr}

                      47 ;84:                                  unsigned char* inBuf, int bufPos, int maxBufPos,


                      48 ;85:                                   unsigned int invokeId, unsigned char* response)


                      49 ;86: {


                      50 


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fec1.s
00000004 e1a05001     51 	mov	r5,r1

00000008 e24dd024     52 	sub	sp,sp,36

0000000c e59d4048     53 	ldr	r4,[sp,72]

00000010 e3a00000     54 	mov	r0,0

00000014 e58d000c     55 	str	r0,[sp,12]

00000018 e58d0010     56 	str	r0,[sp,16]

                      57 ;87:     /*


                      58 ;88:     return mms_createMmsRejectPdu(invokeId,


                      59 ;89:                            MMS_ERROR_REJECT_UNRECOGNIZED_SERVICE, response);


                      60 ;90:                            */


                      61 ;91: 


                      62 ;92:     uint8_t* domainIdStr = NULL;


                      63 

                      64 ;93:     uint8_t* itemIdStr = NULL;


                      65 

                      66 ;94:     int domainIdLen;


                      67 ;95:     int itemIdLen;


                      68 ;96:     uint8_t tag;


                      69 ;97:     int length;


                      70 ;98: 	int result;


                      71 ;99: 


                      72 ;100:     while (bufPos < maxBufPos)


                      73 

0000001c e28da014     74 	add	r10,sp,20

00000020 e1a0c002     75 	mov	r12,r2

00000024 e1a06003     76 	mov	r6,r3

00000028 e15c0006     77 	cmp	r12,r6

0000002c aa00001e     78 	bge	.L60

                      79 .L61:

                      80 ;101:     {


                      81 

                      82 ;102:         tag = inBuf[bufPos++];


                      83 

00000030 e7d5700c     84 	ldrb	r7,[r5,r12]

00000034 e28c2001     85 	add	r2,r12,1

                      86 ;103: 


                      87 ;104:         bufPos = BerDecoder_decodeLength(inBuf, &length, bufPos, maxBufPos);


                      88 

00000038 e1a03006     89 	mov	r3,r6

0000003c e28d101c     90 	add	r1,sp,28

00000040 e1a00005     91 	mov	r0,r5

00000044 eb000000*    92 	bl	BerDecoder_decodeLength

00000048 e1b0c000     93 	movs	r12,r0

                      94 ;105: 


                      95 ;106:         if (bufPos < 0)


                      96 

                      97 ;107:         {


                      98 

                      99 ;108:             return 0;


                     100 

0000004c 43a00000    101 	movmi	r0,0

00000050 4a00005a    102 	bmi	.L57

                     103 ;109:         }


                     104 ;110: 


                     105 ;111:         if (ASN_VARIABLE_SPECIFICATION_OBJECT_NAME == tag)


                     106 

00000054 e35700a0    107 	cmp	r7,160

00000058 1a00000d    108 	bne	.L65

                     109 ;112:         {


                     110 

                     111 ;113:             bufPos = BerDecoder_DecodeObjectName(inBuf, bufPos, maxBufPos,



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fec1.s
                     112 

0000005c e28d100c    113 	add	r1,sp,12

00000060 e28d0018    114 	add	r0,sp,24

00000064 e88d0403    115 	stmea	[sp],{r0-r1,r10}

00000068 e28d3010    116 	add	r3,sp,16

0000006c e1a02006    117 	mov	r2,r6

00000070 e1a0100c    118 	mov	r1,r12

00000074 e1a00005    119 	mov	r0,r5

00000078 eb000000*   120 	bl	BerDecoder_DecodeObjectName

0000007c e1b0c000    121 	movs	r12,r0

                     122 ;114:                 &itemIdStr, &itemIdLen, &domainIdStr, &domainIdLen);


                     123 ;115:             if (bufPos < 0)


                     124 

00000080 5a000007    125 	bpl	.L59

                     126 ;116:             {


                     127 

                     128 ;117:                 debugSendText("!!!!!Object name error");


                     129 

00000084 e28f0000*   130 	adr	r0,.L261

00000088 eb000000*   131 	bl	debugSendText

                     132 ;118:                 return -1;


                     133 

0000008c e3e00000    134 	mvn	r0,0

00000090 ea00004a    135 	b	.L57

                     136 .L65:

                     137 ;119:             }            


                     138 ;120:         }


                     139 ;121:         else {


                     140 

                     141 ;122:             debugSendText("!!!!!Unknown tag");


                     142 

00000094 e28f0000*   143 	adr	r0,.L262

00000098 eb000000*   144 	bl	debugSendText

                     145 ;123:             return -1;


                     146 

0000009c e3e00000    147 	mvn	r0,0

000000a0 ea000046    148 	b	.L57

                     149 .L59:

000000a4 e15c0006    150 	cmp	r12,r6

000000a8 baffffe0    151 	blt	.L61

                     152 .L60:

                     153 ;124:         }


                     154 ;125:     }		


                     155 ;126: 


                     156 ;127: 	result = getVariableAccessAttr(invokeId,


                     157 

000000ac e59db044    158 	ldr	fp,[sp,68]

                     159 ;60:                              uint8_t* domainId, int domainIdLen,


                     160 ;61:                              uint8_t* itemId, int itemIdLen,


                     161 ;62:                              unsigned char* outBuf)


                     162 ;63: {


                     163 

                     164 ;64:     //TODO выделить функцию получения объекта по именам


                     165 ;65:     int ldPos;


                     166 ;66:     int objectPos;


                     167 ;67: 


                     168 ;68:     ldPos = findDomainSection(IED_VMD_DATA_SECTION, domainId,domainIdLen);


                     169 

000000b0 e59d2014    170 	ldr	r2,[sp,20]

000000b4 e59d100c    171 	ldr	r1,[sp,12]

000000b8 e3a000ec    172 	mov	r0,236


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fec1.s
000000bc eb000000*   173 	bl	findDomainSection

000000c0 e1a05000    174 	mov	r5,r0

                     175 ;69:     debugSendUshort("ldPos:", ldPos);


                     176 

000000c4 e1a01805    177 	mov	r1,r5 lsl 16

000000c8 e59f0124*   178 	ldr	r0,.L263

000000cc e1a01821    179 	mov	r1,r1 lsr 16

000000d0 eb000000*   180 	bl	debugSendUshort

                     181 ;70:     if(ldPos == 0)


                     182 

000000d4 e3550000    183 	cmp	r5,0

000000d8 0a000009    184 	beq	.L77

                     185 ;71:     {


                     186 

                     187 ;72:         return 0;


                     188 

                     189 ;73:     }


                     190 ;74:     objectPos = findObjectByPath(ldPos,itemId,itemIdLen);


                     191 

000000dc e59d2018    192 	ldr	r2,[sp,24]

000000e0 e59d1010    193 	ldr	r1,[sp,16]

000000e4 e1a00005    194 	mov	r0,r5

000000e8 eb000000*   195 	bl	findObjectByPath

000000ec e1a05000    196 	mov	r5,r0

                     197 ;75:     debugSendUshort("objectPos:", objectPos);


                     198 

000000f0 e1a01805    199 	mov	r1,r5 lsl 16

000000f4 e59f00fc*   200 	ldr	r0,.L264

000000f8 e1a01821    201 	mov	r1,r1 lsr 16

000000fc eb000000*   202 	bl	debugSendUshort

                     203 ;76:     if(objectPos == 0)


                     204 

00000100 e3550000    205 	cmp	r5,0

                     206 .L77:

                     207 ;77:     {


                     208 

                     209 ;78:         return 0;


                     210 

00000104 03a00000    211 	moveq	r0,0

                     212 ;128: 		domainIdStr, domainIdLen, itemIdStr, itemIdLen,


                     213 ;129: 		response);


                     214 ;130: 	if (result < 1)


                     215 

00000108 0a000028    216 	beq	.L81

                     217 .L78:

                     218 ;79:     }


                     219 ;80:     return encodeGetVariableAccessAttrResponse(invokeId, objectPos, outBuf);


                     220 

                     221 ;13:                               unsigned char* outBuf)


                     222 ;14: {


                     223 

                     224 ;15:     int bufPos = 0;


                     225 

                     226 ;16:     unsigned int accessResultSize;


                     227 ;17:     unsigned int invokeIdSize;


                     228 ;18:     unsigned int confirmedResponseContentSize;


                     229 ;19:     unsigned int fullConfirmedResponseSize;


                     230 ;20: 


                     231 ;21:     //==============determine BER encoded message sizes==============


                     232 ;22:     //Общий размер всех значений


                     233 ;23:     accessResultSize = encodeObjectAccessAttrs(NULL, bufPos, objectPos, TRUE, TRUE);



                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fec1.s
                     234 

0000010c e3a06001    235 	mov	r6,1

00000110 e58d6000    236 	str	r6,[sp]

00000114 e1a02005    237 	mov	r2,r5

00000118 e1a03006    238 	mov	r3,r6

0000011c e3a01000    239 	mov	r1,0

00000120 e1a00001    240 	mov	r0,r1

00000124 eb000000*   241 	bl	encodeObjectAccessAttrs

                     242 ;24: 


                     243 ;25:     //Вместе с тэгом 0xA6 (тэг + размер размера данных + размер данных)


                     244 ;26:     confirmedResponseContentSize = 1


                     245 

00000128 e1a07000    246 	mov	r7,r0

0000012c eb000000*   247 	bl	BerEncoder_determineLengthSize

00000130 e0800007    248 	add	r0,r0,r7

00000134 e2808001    249 	add	r8,r0,1

                     250 ;27:             + BerEncoder_determineLengthSize(accessResultSize)


                     251 ;28:             + accessResultSize;


                     252 ;29: 


                     253 ;30:     invokeIdSize = BerEncoder_UInt32determineEncodedSize(invokeId) + 2;


                     254 

00000138 e1a0000b    255 	mov	r0,fp

0000013c eb000000*   256 	bl	BerEncoder_UInt32determineEncodedSize

00000140 e280a002    257 	add	r10,r0,2

                     258 ;31: 


                     259 ;32: 


                     260 ;33:     //Для тэга 0xA1


                     261 ;34:     fullConfirmedResponseSize = invokeIdSize


                     262 

00000144 e088100a    263 	add	r1,r8,r10

                     264 ;35:             + confirmedResponseContentSize;


                     265 ;36: 


                     266 ;37: 


                     267 ;38:     //================ encode message ============================


                     268 ;39:     bufPos = 0;


                     269 

                     270 ;40: 


                     271 ;41:     // confirmed response PDU


                     272 ;42:     bufPos = BerEncoder_encodeTL(0xa1,  fullConfirmedResponseSize, outBuf, bufPos);


                     273 

00000148 e1a02004    274 	mov	r2,r4

0000014c e3a03000    275 	mov	r3,0

00000150 e3a000a1    276 	mov	r0,161

00000154 eb000000*   277 	bl	BerEncoder_encodeTL

                     278 ;43: 


                     279 ;44:     // invoke id


                     280 ;45:     bufPos = BerEncoder_encodeTL(ASN_INTEGER, invokeIdSize - 2, outBuf, bufPos);


                     281 

00000158 e1a02004    282 	mov	r2,r4

0000015c e24a1002    283 	sub	r1,r10,2

00000160 e1a03000    284 	mov	r3,r0

00000164 e3a00002    285 	mov	r0,2

00000168 eb000000*   286 	bl	BerEncoder_encodeTL

                     287 ;46:     bufPos = BerEncoder_encodeUInt32(invokeId, outBuf, bufPos);


                     288 

0000016c e1a01004    289 	mov	r1,r4

00000170 e1a02000    290 	mov	r2,r0

00000174 e1a0000b    291 	mov	r0,fp

00000178 eb000000*   292 	bl	BerEncoder_encodeUInt32

                     293 ;47: 


                     294 ;48:     // confirmed-service-response getVariableAccessAttributes



                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fec1.s
                     295 ;49:     bufPos = BerEncoder_encodeTL(0xa6, accessResultSize,


                     296 

0000017c e1a02004    297 	mov	r2,r4

00000180 e1a01007    298 	mov	r1,r7

00000184 e1a03000    299 	mov	r3,r0

00000188 e3a000a6    300 	mov	r0,166

0000018c eb000000*   301 	bl	BerEncoder_encodeTL

                     302 ;50:                                  outBuf, bufPos);


                     303 ;51: 


                     304 ;52:     // encode access results


                     305 ;53:     bufPos = encodeObjectAccessAttrs(outBuf, bufPos, objectPos, FALSE, TRUE);


                     306 

00000190 e58d6000    307 	str	r6,[sp]

00000194 e1a02005    308 	mov	r2,r5

00000198 e1a01000    309 	mov	r1,r0

0000019c e1a00004    310 	mov	r0,r4

000001a0 e3a03000    311 	mov	r3,0

000001a4 eb000000*   312 	bl	encodeObjectAccessAttrs

                     313 ;54: 


                     314 ;55: 


                     315 ;56:     return bufPos;


                     316 

                     317 ;128: 		domainIdStr, domainIdLen, itemIdStr, itemIdLen,


                     318 ;129: 		response);


                     319 ;130: 	if (result < 1)


                     320 

000001a8 e3500000    321 	cmp	r0,0

000001ac ca000003    322 	bgt	.L57

                     323 .L81:

                     324 ;131: 	{


                     325 

                     326 ;132: 		return CreateMmsConfirmedErrorPdu(invokeId, response,


                     327 

000001b0 e1a01004    328 	mov	r1,r4

000001b4 e59d0044    329 	ldr	r0,[sp,68]

000001b8 e3a02051    330 	mov	r2,81

000001bc eb000000*   331 	bl	CreateMmsConfirmedErrorPdu

                     332 ;133: 			MMS_ERROR_ACCESS_OBJECT_NON_EXISTENT);


                     333 ;134: 	}


                     334 ;135: 


                     335 ;136: 	//TODO Обработать случай когда domainIdStr и itemIdStr неинициализированы


                     336 ;137: 	return result;


                     337 

                     338 .L57:

000001c0 e28dd024    339 	add	sp,sp,36

000001c4 e8bd8df0    340 	ldmfd	[sp]!,{r4-r8,r10-fp,pc}

                     341 	.endf	mms_handleGetVariableAccessAttr

                     342 	.align	4

                     343 ;domainIdStr	[sp,12]	local

                     344 ;itemIdStr	[sp,16]	local

                     345 ;domainIdLen	[sp,20]	local

                     346 ;itemIdLen	[sp,24]	local

                     347 ;tag	r7	local

                     348 ;length	[sp,28]	local

                     349 ;result	r0	local

                     350 ;.L209	.L215	static

                     351 ;.L210	.L216	static

                     352 ;invokeId	fp	local

                     353 ;ldPos	r5	local

                     354 ;objectPos	r5	local

                     355 ;accessResultSize	r7	local


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fec1.s
                     356 ;invokeIdSize	r10	local

                     357 ;confirmedResponseContentSize	r8	local

                     358 

                     359 ;mmsConn	none	param

                     360 ;inBuf	r5	param

                     361 ;bufPos	r12	param

                     362 ;maxBufPos	r6	param

                     363 ;invokeId	[sp,68]	param

                     364 ;response	r4	param

                     365 

                     366 	.section ".bss","awb"

                     367 .L208:

                     368 	.section ".rodata","a"

                     369 .L211:

                     370 __UNNAMED_1_static_in_getVariableAccessAttr:;	"ldPos:\000"

00000000 6f50646c    371 	.data.b	108,100,80,111

00000004 3a73       372 	.data.b	115,58

00000006 00         373 	.data.b	0

00000007 00         374 	.space	1

                     375 	.type	__UNNAMED_1_static_in_getVariableAccessAttr,$object

                     376 	.size	__UNNAMED_1_static_in_getVariableAccessAttr,8

                     377 .L212:

                     378 __UNNAMED_2_static_in_getVariableAccessAttr:;	"objectPos:\000"

00000008 656a626f    379 	.data.b	111,98,106,101

0000000c 6f507463    380 	.data.b	99,116,80,111

00000010 3a73       381 	.data.b	115,58

00000012 00         382 	.data.b	0

00000013 00         383 	.space	1

                     384 	.type	__UNNAMED_2_static_in_getVariableAccessAttr,$object

                     385 	.size	__UNNAMED_2_static_in_getVariableAccessAttr,12

                     386 	.data

                     387 	.text

                     388 

                     389 ;138: }


                     390 	.align	4

                     391 .L261:

                     392 ;	"!!!!!Object name error\000"

000001c8 21212121    393 	.data.b	33,33,33,33

000001cc 6a624f21    394 	.data.b	33,79,98,106

000001d0 20746365    395 	.data.b	101,99,116,32

000001d4 656d616e    396 	.data.b	110,97,109,101

000001d8 72726520    397 	.data.b	32,101,114,114

000001dc 726f       398 	.data.b	111,114

000001de 00         399 	.data.b	0

000001df 00         400 	.align 4

                     401 

                     402 	.type	.L261,$object

                     403 	.size	.L261,4

                     404 

                     405 .L262:

                     406 ;	"!!!!!Unknown tag\000"

000001e0 21212121    407 	.data.b	33,33,33,33

000001e4 6b6e5521    408 	.data.b	33,85,110,107

000001e8 6e776f6e    409 	.data.b	110,111,119,110

000001ec 67617420    410 	.data.b	32,116,97,103

000001f0 00         411 	.data.b	0

000001f1 000000     412 	.align 4

                     413 

                     414 	.type	.L262,$object

                     415 	.size	.L262,4

                     416 


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fec1.s
                     417 .L263:

000001f4 00000000*   418 	.data.w	.L211

                     419 	.type	.L263,$object

                     420 	.size	.L263,4

                     421 

                     422 .L264:

000001f8 00000000*   423 	.data.w	.L212

                     424 	.type	.L264,$object

                     425 	.size	.L264,4

                     426 

                     427 	.align	4

                     428 ;__UNNAMED_1_static_in_getVariableAccessAttr	.L211	static

                     429 ;__UNNAMED_2_static_in_getVariableAccessAttr	.L212	static

                     430 

                     431 	.data

                     432 	.ghsnote version,6

                     433 	.ghsnote tools,3

                     434 	.ghsnote options,0

                     435 	.text

                     436 	.align	4

                     437 	.section ".rodata","a"

                     438 	.align	4

                     439 	.text

