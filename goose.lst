                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=goose.c -o gh_fps1.o -list=goose.lst C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
Source File: goose.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile goose.c -o

                      10 ;		goose.o

                      11 ;Source File:   goose.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Oct 07 09:39:25 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "goose.h"


                      21 ;2: #include "bufViewBER.h"


                      22 ;3: #include "AsnEncoding.h"


                      23 ;4: #include "IEDCompile/InnerAttributeTypes.h"


                      24 ;5: #include "BaseAsnTypes.h"


                      25 ;6: #include "iedmodel.h"


                      26 ;7: #include "iedTree/iedTree.h"


                      27 ;8: #include "iedTree/DataSet.h"


                      28 ;9: #include "FinalDA.h"


                      29 ;10: #include "iedTree/iedFinalDA.h"


                      30 ;11: #include "mms_data.h"


                      31 ;12: #include "netTools.h"


                      32 ;13: #include "DataSlice.h"


                      33 ;14: #include "timers.h"


                      34 ;15: #include "platformTools.h"


                      35 ;16: #include <debug.h>


                      36 ;17: #include <string.h>


                      37 ;18: #include <stdlib.h>


                      38 ;19: #include <stdint.h>


                      39 ;20: 


                      40 ;21: 


                      41 ;22: 


                      42 ;23: #define  MAX_GO_CB_REF_SIZE 66


                      43 ;24: #define TIME_ALLOWED_TO_LIVE 4000


                      44 ;25: #define MAX_GOCB_COUNT 4


                      45 ;26: 


                      46 ;27: // Количество посылок с интервалом T1 после первой посылки изменений.


                      47 ;28: // Минимальное значение 1


                      48 ;29: #define T1_COUNT 4


                      49 ;30: 


                      50 ;31: //Максимальное количество GSE в одном GoCB



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
                      51 ;32: #define MAX_GSE_COUNT 3


                      52 ;33: 


                      53 ;34: //"Context specific" тэг настроек GSE VLAN


                      54 ;35: #define BER_VLAN_INFO 0xA0


                      55 ;36: //"Context specific" тэг настроек T0 и T1


                      56 ;37: #define BER_GOOSE_INTERVALS 0xA1


                      57 ;38: 


                      58 ;39: //Maximum transfer unit


                      59 ;40: #define MTU_SIZE 1480


                      60 ;41: #define MAX_GOOSE_DA_COUNT 600


                      61 ;42: 


                      62 ;43: #define ETHTYPE_GOOSE	0x88B8


                      63 ;44: 


                      64 ;45: typedef struct VLANInfoStruct * VLANInfo;


                      65 ;46: 


                      66 ;47: typedef struct {


                      67 ;48: 	size_t ldPos;


                      68 ;49: 	size_t lnPos;


                      69 ;50: 	size_t fcPos;


                      70 ;51: 	size_t goCBPos;


                      71 ;52: } GoCBPath;


                      72 ;53: 


                      73 ;54: //Для StNum и SqNum


                      74 ;55: typedef struct


                      75 ;56: {


                      76 ;57: 	//Куда записывать


                      77 ;58: 	uint8_t* p;


                      78 ;59: 	uint32_t value;


                      79 ;60: } GOOSEPktNum;


                      80 ;61: 


                      81 ;62: struct VLANInfoStruct


                      82 ;63: {


                      83 ;64: 	bool useVLAN;


                      84 ;65: 	//Уточнить тип


                      85 ;66: 	int id;


                      86 ;67: 	//Уточнить тип


                      87 ;68: 	int priority;


                      88 ;69: };


                      89 ;70: 


                      90 ;71: 


                      91 ;72: struct GSESettingsStruct


                      92 ;73: {


                      93 ;74: 	//!Номер сетевого интерфейса


                      94 ;75: 	uint8_t ifNum;


                      95 ;76: 	void* netIf;


                      96 ;77: 	uint8_t src[6];


                      97 ;78: 	uint8_t dst[6];


                      98 ;79: 	struct VLANInfoStruct vlan;


                      99 ;80: 	uint16_t appID;	


                     100 ;81: 	uint8_t* outPkt;


                     101 ;82: 	size_t outPktSize;


                     102 ;83: 	//Указатель и размер чтобы копировать PDU между пакетами


                     103 ;84: 	uint8_t* pPDU;


                     104 ;85: 	size_t pduSize;


                     105 ;86: 	//Место в пакете, куда положить время


                     106 ;87: 	uint8_t* pPktTime;


                     107 ;88: 	//Номер пакета


                     108 ;89: 	GOOSEPktNum sqNum;


                     109 ;90: 	//Номер изменения состояния


                     110 ;91: 	GOOSEPktNum stNum;


                     111 ;92: 	uint32_t t0;



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
                     112 ;93: 	uint32_t t1;


                     113 ;94: 	uint32_t currT;


                     114 ;95:     //Счётчик количества первых посылок с интервалом t1


                     115 ;96:     uint32_t t1Counter;


                     116 ;97: 	uint32_t msCounter;


                     117 ;98: };


                     118 ;99: 


                     119 ;100: struct GoCBStruct


                     120 ;101: {	


                     121 ;102: 	bool goEna;


                     122 ;103: 	StringView appID;


                     123 ;104: 	StringView dataSetName;	


                     124 ;105: 	IEDEntity dataSetEntity;


                     125 ;106: 	DataSet* dataSet;


                     126 ;107: 	bool ndsCom;


                     127 ;108:     char goCBRef[66];


                     128 ;109: 	uint16_t confRev;


                     129 ;110: 	uint32_t timeAllowedToLive;


                     130 ;111: 	size_t gseCount;


                     131 ;112: 	struct GSESettingsStruct gse[MAX_GSE_COUNT];


                     132 ;113: 	size_t daCount;


                     133 ;114: 	void* daList[MAX_GOOSE_DA_COUNT];


                     134 ;115: };


                     135 ;116: 


                     136 ;117: //Структура содержит размеры разных частей пакета GOOSE.


                     137 ;118: 


                     138 ;119: typedef struct  


                     139 ;120: {


                     140 ;121: 	//Размер пакета без заголовка Ethernet


                     141 ;122: 	uint16_t goosePktSize;


                     142 ;123: 	uint16_t pduWithTLSize;


                     143 ;124: 	uint16_t pduSize;


                     144 ;125: 	uint16_t dataTLSize;


                     145 ;126: 	uint16_t dataSize;


                     146 ;127: } GOOSESizes;


                     147 ;128: 


                     148 ;129: static size_t g_goCBCount = 0;


                     149 ;130: static struct GoCBStruct g_goCBs[MAX_GOCB_COUNT];


                     150 ;131: 


                     151 ;132: //Используется для хранения пути к GoCB при инициализации.


                     152 ;133: static GoCBPath g_goCBPath;


                     153 ;134: 


                     154 ;135: 


                     155 ;136: static void writeUlongBE(uint8_t* p, uint32_t data)


                     156 ;137: {	


                     157 ;138: 	p[0] = data >> 24;


                     158 ;139: 	p[1] = (data >> 16) & 0xFF;	


                     159 ;140: 	p[2] = (data >> 8) & 0xFF;


                     160 ;141: 	p[3] = data & 0xFF;


                     161 ;142: }


                     162 ;143: 


                     163 ;144: static GoCB getFreeGoCB(void)


                     164 

                     165 ;152: }


                     166 

                     167 ;153: 


                     168 ;154: //Пишем время в исходящий пакет GSE


                     169 ;155: static void writePktTime(GSESettings gse)


                     170 

                     171 ;159: }


                     172 


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
                     173 ;160: 


                     174 ;161: 


                     175 ;162: static bool calcGOOSEDataSize(GoCB goCB, GOOSESizes* gSizes)


                     176 

                     177 ;178: }


                     178 

                     179 ;179: 


                     180 ;180: static bool calcGOOSEPDUSize(GoCB goCB, GOOSESizes* gSizes)


                     181 

                     182 ;213: }


                     183 

                     184 ;214: 


                     185 ;215: static bool calcGOOSEPktSize(GoCB goCB, GSESettings gse,GOOSESizes* gSizes)


                     186 

                     187 ;241: }


                     188 

                     189 ;242: 


                     190 ;243: static bool calcGOOSESizes(GoCB goCB, GSESettings gse, GOOSESizes* gSizes)


                     191 

                     192 ;262: }


                     193 

                     194 ;263: 


                     195 ;264: static bool writeVLANInfo(VLANInfo vlan, BufferView* gooseBuf)


                     196 

                     197 ;290: }


                     198 

                     199 ;291: 


                     200 ;292: static bool writeGOOSEHeader(GSESettings gse, GOOSESizes* gooseSizes,


                     201 

                     202 ;338: }


                     203 

                     204 ;339: 


                     205 ;340: static bool writeDataTemplate(GoCB goCB, GOOSESizes* gooseSizes, 


                     206 

                     207 ;360: }


                     208 

                     209 ;361: 


                     210 ;362: static bool writeGOOSEPduTemplate(GoCB goCB, GSESettings gse, 


                     211 

                     212 ;448: }


                     213 

                     214 ;449: 


                     215 ;450: // Подготавливает буфер GOOSE


                     216 ;451: // Должна вызываться при инициализации


                     217 ;452: bool prepareGOOSEbuf(GoCB goCB, GSESettings gse, BufferView* gooseBuf)


                     218 ;453: {	


                     219 ;454: 	GOOSESizes gooseSizes;


                     220 ;455: 


                     221 ;456: 	if (!calcGOOSESizes(goCB, gse, &gooseSizes))


                     222 ;457: 	{


                     223 ;458: 		ERROR_REPORT("Unable to calculate GOOSE sizes");


                     224 ;459: 		return FALSE;


                     225 ;460: 	}


                     226 ;461: 


                     227 ;462: 	if (!writeGOOSEHeader(gse, &gooseSizes, gooseBuf))


                     228 ;463: 	{


                     229 ;464: 		return FALSE;


                     230 ;465: 	}


                     231 ;466: 


                     232 ;467: 	//GOOSE PDU	


                     233 ;468: 	if (!writeGOOSEPduTemplate(goCB, gse, &gooseSizes, gooseBuf))



                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
                     234 ;469: 	{


                     235 ;470: 		return FALSE;


                     236 ;471: 	}


                     237 ;472: 	


                     238 ;473: 	return TRUE;


                     239 ;474: }


                     240 ;475: 


                     241 ;476: // Декодирует конфигурацию VLAN. Если нужный тэг не найден,


                     242 ;477: // устанавливает useVLAN = FALSE


                     243 ;478: static bool decodeVLANinfo(VLANInfo vlan, BufferView* berData)


                     244 

                     245 ;512: }


                     246 

                     247 ;513: 


                     248 ;514: bool GSE_init(GSESettings gse, BufferView* berData)


                     249 ;515: {


                     250 ;516: 	uint8_t tag;	


                     251 ;517: 	uint32_t ifNum;


                     252 ;518: 	uint32_t appID;


                     253 ;519: 	StringView dest;


                     254 ;520: 	uint32_t t0, t1;


                     255 ;521: 


                     256 ;522: 	//Тэг и длина GSE


                     257 ;523: 	if (!BufferView_decodeTL(berData, &tag, NULL, NULL))


                     258 ;524: 	{


                     259 ;525: 		ERROR_REPORT("Unable to decode GSE tag and length");


                     260 ;526: 		return FALSE;


                     261 ;527: 	}


                     262 ;528: 	if (tag != ASN_SEQUENCE)


                     263 ;529: 	{


                     264 ;530: 		return FALSE;


                     265 ;531: 	}


                     266 ;532: 


                     267 ;533: 	//Номер сетевого интерфейса


                     268 ;534: 	if (!BufferView_decodeUInt32TL(berData, ASN_INTEGER, &ifNum))


                     269 ;535: 	{


                     270 ;536: 		ERROR_REPORT("Unable to decode GSE ifNum");


                     271 ;537: 		return FALSE;


                     272 ;538: 	}


                     273 ;539: 	gse->ifNum = (uint8_t)ifNum;


                     274 ;540: 	//Сетевой интерфейс


                     275 ;541: 	if (!NetTools_getIf(gse->ifNum, &gse->netIf))


                     276 ;542: 	{


                     277 ;543: 		ERROR_REPORT("Unable to optain net interface");


                     278 ;544: 		return FALSE;


                     279 ;545: 	}


                     280 ;546: 	//Source


                     281 ;547: 	if (!NetTools_getMac(gse->netIf, gse->src))


                     282 ;548: 	{


                     283 ;549: 		ERROR_REPORT("Unable to optain interface MAC");


                     284 ;550: 		return FALSE;


                     285 ;551: 	}


                     286 ;552: 


                     287 ;553: 	//Destination


                     288 ;554: 	if (!BufferView_decodeStringViewTL(berData, ASN_OCTET_STRING, &dest)


                     289 ;555: 		|| dest.len != 6)


                     290 ;556: 	{


                     291 ;557: 		ERROR_REPORT("Unable to decode GSE destination");


                     292 ;558: 		return FALSE;


                     293 ;559: 	}


                     294 ;560: 	memcpy(&gse->dst, dest.p, dest.len);



                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
                     295 ;561: 


                     296 ;562: 	//VLAN info


                     297 ;563: 	if (!decodeVLANinfo(&gse->vlan, berData))


                     298 ;564: 	{


                     299 ;565: 		ERROR_REPORT("Unable to decode GSE VLAN info");


                     300 ;566: 		return FALSE;


                     301 ;567: 	}


                     302 ;568: 	//APP ID


                     303 ;569: 	if (!BufferView_decodeUInt32TL(berData, ASN_INTEGER, &appID))


                     304 ;570: 	{


                     305 ;571: 		ERROR_REPORT("Unable to decode GSE appID");


                     306 ;572: 		return FALSE;


                     307 ;573: 	}


                     308 ;574: 	gse->appID = (uint16_t)appID;


                     309 ;575: 	//Тэг и длина T info


                     310 ;576: 	if (!BufferView_decodeTL(berData, &tag, NULL, NULL) 


                     311 ;577: 		|| tag != BER_GOOSE_INTERVALS)


                     312 ;578: 	{


                     313 ;579:         ERROR_REPORT("Unable to decode GSE T0/T1 configuration");


                     314 ;580: 		return FALSE;


                     315 ;581: 	}


                     316 ;582: 


                     317 ;583: 	//T0


                     318 ;584: 	if (!BufferView_decodeUInt32TL(berData, ASN_INTEGER, &t0))


                     319 ;585: 	{


                     320 ;586: 		ERROR_REPORT("Unable to decode T0");


                     321 ;587: 		return FALSE;


                     322 ;588: 	}


                     323 ;589: 	gse->t0 = t0;


                     324 ;590: 


                     325 ;591: 	//T1


                     326 ;592: 	if (!BufferView_decodeUInt32TL(berData, ASN_INTEGER, &t1))


                     327 ;593: 	{


                     328 ;594: 		ERROR_REPORT("Unable to decode T1");


                     329 ;595: 		return FALSE;


                     330 ;596: 	}


                     331 ;597: 	gse->t1 = t1;


                     332 ;598: 


                     333 ;599: 	return TRUE;


                     334 ;600: }


                     335 ;601: 


                     336 ;602: static bool initDatSet(size_t goCBPos, GoCB goCB)


                     337 

                     338 ;626: }


                     339 

                     340 ;627: 


                     341 ;628: //Если delimiter NULL, то пишется конец стоки - 0


                     342 ;629: static bool writeObjectNameToBuf(size_t pos, char* delimiter, BufferView* buf)


                     343 ;630: {


                     344 ;631: 	StringView nameView;	


                     345 ;632: 


                     346 ;633: 	if (!getObjectName(pos, &nameView))


                     347 ;634: 	{	


                     348 ;635: 		return false;


                     349 ;636: 	}


                     350 ;637: 	if (!BufferView_writeStringView(buf, &nameView))


                     351 ;638: 	{


                     352 ;639: 		return false;


                     353 ;640: 	}


                     354 ;641: 	if (delimiter != NULL)


                     355 ;642: 	{	



                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
                     356 ;643: 		// Записываем разделитель


                     357 ;644: 		if (!BufferView_writeStr(buf, delimiter))


                     358 ;645: 		{


                     359 ;646: 			return false;


                     360 ;647: 		}


                     361 ;648: 		return true;


                     362 ;649: 	}


                     363 ;650: 	//Записываем конец строки


                     364 ;651: 	if (1 != BufferView_writeData(buf, "", 1))


                     365 ;652: 	{


                     366 ;653: 		return false;


                     367 ;654: 	}


                     368 ;655: 	return true;


                     369 ;656: }


                     370 ;657: 


                     371 ;658: //Заполняет goCBRef правильной строкой ASCIIZ


                     372 ;659: static bool initGoCBRef(GoCB goCB)


                     373 

                     374 ;688: }


                     375 

                     376 ;689: 


                     377 ;690: static bool initGoCBvars(GoCB goCB, size_t goCBPos)


                     378 

                     379 ;724: }


                     380 

                     381 ;725: 


                     382 ;726: static bool initGSEList(GoCB goCB, size_t goCBPos)


                     383 

                     384 ;786: }


                     385 

                     386 ;787: 


                     387 ;788: static bool initDAObjects(GoCB goCB)


                     388 

                     389 ;818: }


                     390 

                     391 ;819: 


                     392 ;820: static bool initMsgTemplates(GoCB goCB)


                     393 

                     394 ;846: }


                     395 

                     396 ;847: 


                     397 ;848: static GOOSE_resetPktCounters(GoCB goCB)


                     398 ;849: {


                     399 ;850: 	size_t gseIdx;


                     400 ;851: 	for (gseIdx = 0; gseIdx < goCB->gseCount; ++gseIdx)


                     401 ;852: 	{


                     402 ;853: 		GSESettings gse = goCB->gse + gseIdx;


                     403 ;854: 		gse->sqNum.value = 1;


                     404 ;855: 		gse->stNum.value = 1;


                     405 ;856: 	}


                     406 ;857: }


                     407 ;858: 


                     408 ;859: static GOOSE_resetPktTimers(GoCB goCB)


                     409 ;860: {


                     410 ;861: 	size_t gseIdx;


                     411 ;862: 	for (gseIdx = 0; gseIdx < goCB->gseCount; ++gseIdx)


                     412 ;863: 	{


                     413 ;864: 		GSESettings gse = goCB->gse + gseIdx;


                     414 ;865: 		gse->currT = gse->t0;


                     415 ;866:         gse->t1Counter = 1;


                     416 ;867:         gse->msCounter = 0;



                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
                     417 ;868: 	}


                     418 ;869: }


                     419 ;870: 


                     420 ;871: static GOOSE_writePktCounters(GSESettings gse)


                     421 

                     422 ;875: }


                     423 

                     424 ;876: 


                     425 ;877: bool GoCB_init(GoCB goCB, size_t goCBPos)


                     426 ;878: {


                     427 ;879: 	goCB->ndsCom = TRUE;


                     428 ;880: 


                     429 ;881: 	if (!initGoCBvars(goCB, goCBPos))


                     430 ;882: 	{		


                     431 ;883: 		return FALSE;


                     432 ;884: 	}


                     433 ;885: 	if (!initGSEList(goCB, goCBPos) || goCB->gseCount == 0)


                     434 ;886: 	{


                     435 ;887: 		return FALSE;


                     436 ;888: 	}


                     437 ;889: 


                     438 ;890: 	//Пока передаются только Final DA эта функция вызывается здесь.


                     439 ;891: 	//Когда надо будет передавать DO, возможно этот код должен будет


                     440 ;892: 	//выполняться в процессе создания шаблонов сообщений


                     441 ;893: 	if (!initDAObjects(goCB))


                     442 ;894: 	{


                     443 ;895: 		ERROR_REPORT("Unable to init dataset DA objects");


                     444 ;896: 		return FALSE;


                     445 ;897: 	}


                     446 ;898: 	if (!initMsgTemplates(goCB))


                     447 ;899: 	{


                     448 ;900: 		ERROR_REPORT("Unable to init GOOSE message templates");


                     449 ;901: 		return FALSE;


                     450 ;902: 	}


                     451 ;903: 


                     452 ;904: 	GOOSE_resetPktCounters(goCB);


                     453 ;905: 	GOOSE_resetPktTimers(goCB);


                     454 ;906: 


                     455 ;907: 	goCB->ndsCom = FALSE;


                     456 ;908: 	return TRUE;


                     457 ;909: }


                     458 ;910: 


                     459 ;911: /*


                     460 ;912: static BOOL_T readDATypeInfo(BufferView* encodedFinalDA, 


                     461 ;913: 	enum InnerAttributeType* outAttrType, void** accessInfo)


                     462 ;914: {


                     463 ;915: 	uint32_t attrType;


                     464 ;916: 	StringView encodedElemStruct;


                     465 ;917: 	void* daObject;


                     466 ;918: 	void* accessInfo;


                     467 ;919: 	//Какое смещение использовано для выравнивания структуры описания


                     468 ;920: 	uint8_t* pDescrStructAlignOffset;


                     469 ;921: 


                     470 ;922: 	if (!BufferView_decodeTL(encodedFinalDA))


                     471 ;923: 	{


                     472 ;924: 		ERROR_REPORT("Unable to decode Final DA");


                     473 ;925: 		return NULL;


                     474 ;926: 	}


                     475 ;927: 	//Получить тип элемента


                     476 ;928: 	if (!BufferView_decodeUInt32TL(encodedFinalDA, ASN_INTEGER, &attrType))


                     477 ;929: 	{



                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
                     478 ;930: 		ERROR_REPORT("Unable to decode Final DA type");


                     479 ;931: 		return NULL;


                     480 ;932: 	}


                     481 ;933: 	*outAttrType = attrType;


                     482 ;934: 


                     483 ;935: 	//Получаем структуру элемента


                     484 ;936: 	if (BufferView_decodeStringViewTL(encodedFinalDA, ASN_OCTET_STRING,


                     485 ;937: 		&encodedElemStruct))


                     486 ;938: 	{


                     487 ;939: 		ERROR_REPORT("Unable to decode Final DA struct");


                     488 ;940: 		return NULL;


                     489 ;941: 	}


                     490 ;942: 	//Указатель непосредственно на структуру	


                     491 ;943: 	pDescrStructAlignOffset = encodedElemStruct.p;


                     492 ;944: 	if (*pDescrStructAlignOffset > 3)


                     493 ;945: 	{


                     494 ;946: 		ERROR_REPORT("Invalid alignment");


                     495 ;947: 		return NULL;


                     496 ;948: 	}


                     497 ;949: 	*accessInfo = pDescrStructAlignOffset + *pDescrStructAlignOffset + 1;


                     498 ;950: 	return TRUE;


                     499 ;951: }


                     500 ;952: 


                     501 ;953: 


                     502 ;954: 


                     503 ;955: //Создаёт объект FinalDA согласно описанию из информационной модели


                     504 ;956: // encodedFinalDA.


                     505 ;957: //Одновременно генерирует шаблон для кодирования исходящих данных.


                     506 ;958: //Возвращает указатель на объект FinalDA или NULL при ошибке


                     507 ;959: void* GoCB_initFinalDA(BufferView* encodedFinalDA, BufferView* templateBuf)


                     508 ;960: {


                     509 ;961: 	InnerAttributeType attrType;


                     510 ;962: 	void* daObject;


                     511 ;963: 


                     512 ;964: 	if (readDATypeInfo())


                     513 ;965: 	{


                     514 ;966: 		return NULL;


                     515 ;967: 	}


                     516 ;968: 	


                     517 ;969: 	//Создать элемент


                     518 ;970: 	daObject = FDA_create(attrType, accessInfo);


                     519 ;971: 	if (daObject == NULL)


                     520 ;972: 	{


                     521 ;973: 		ERROR_REPORT("Unable to create Final DA object");


                     522 ;974: 		return NULL;


                     523 ;975: 	}


                     524 ;976: 


                     525 ;977: 	//Генерировать шаблон


                     526 ;978: 	if (!FDA_encodeTemplate(daObject, templateBuf))


                     527 ;979: 	{


                     528 ;980: 		ERROR_REPORT("Unable to create Final DA template");


                     529 ;981: 		return NULL;


                     530 ;982: 	}


                     531 ;983: 


                     532 ;984: 	return daObject;


                     533 ;985: }


                     534 ;986: 


                     535 ;987: //encodedChildren - буфер, содержащий закодированный список детей


                     536 ;988: BOOL_T GoCB_initDataSetIemChildren(BufferView* encodedChildren, 


                     537 ;989: 	BufferView* templateBuf)


                     538 ;990: {



                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
                     539 ;991: 	while (encodedChildren->pos < encodedChildren->len)


                     540 ;992: 	{


                     541 ;993: 		if (!GoCB_initDataSetItem(encodedChildren, templateBuf, daList, pDaListPos,


                     542 ;994: 			maxDaListPos))


                     543 ;995: 		{


                     544 ;996: 			return FALSE;


                     545 ;997: 		}


                     546 ;998: 	}


                     547 ;999: 	return TRUE;


                     548 ;1000: }


                     549 ;1001: 


                     550 ;1002: 


                     551 ;1003: //Рекурсивно обходит элемент набора данных, создавая список FinalDA и 


                     552 ;1004: //одновременно генерирует шаблон для кодирования исходящих данных.


                     553 ;1005: //


                     554 ;1006: //Шаблон это полноценно закодированный элемент набора данных, но на месте


                     555 ;1007: //значений - зарезервированное пустое место, в которое потом можно вписать 


                     556 ;1008: //фактическое занчение. Для значений переменной длины должно быть


                     557 ;1009: //зарезервировано максимальное разумное значение


                     558 ;1010: 


                     559 ;1011: BOOL_T GoCB_initDataSetItem(BufferView* encodedItem, BufferView* templateBuf,


                     560 ;1012: 	void** daList, size_t* pDaListPos, size_t maxDaListPos)


                     561 ;1013: {


                     562 ;1014: 	uint8_t objTag;


                     563 ;1015: 


                     564 ;1016: 	if (*pDaListPos == maxDaListPos)


                     565 ;1017: 	{


                     566 ;1018: 		ERROR_REPORT("Too many DA in the dataset");


                     567 ;1019: 		return FALSE;


                     568 ;1020: 	}


                     569 ;1021: 	if (!BufferView_peekTag(&objTag))


                     570 ;1022: 	{


                     571 ;1023: 		ERROR_REPORT("Unable to read tag");


                     572 ;1024: 		return FALSE;


                     573 ;1025: 	}


                     574 ;1026: 


                     575 ;1027: 	if (objTag == IED_DA_FINAL)


                     576 ;1028: 	{


                     577 ;1029: 		void* daObject = GoCB_initFinalDA(encodedItem, templateBuf);


                     578 ;1030: 		if (daObject == NULL)


                     579 ;1031: 		{


                     580 ;1032: 			return FALSE;


                     581 ;1033: 		}


                     582 ;1034: 		daList[(*pDaListPos)] = daObject;


                     583 ;1035: 		(*pDaListPos)++;


                     584 ;1036: 	}


                     585 ;1037: 	else


                     586 ;1038: 	{


                     587 ;1039: 		BufferView subObjects;


                     588 ;1040: 		//Получаем указатель на список детей и размер


                     589 ;1041: 		if (!getSubObjectsBufView(encodedItem, &subObjects))


                     590 ;1042: 		{			


                     591 ;1043: 			ERROR_REPORT("Unable to get subobjects");


                     592 ;1044: 			return FALSE;


                     593 ;1045: 		}


                     594 ;1046: 		if (!BufferView_decodeTL())


                     595 ;1047: 		{


                     596 ;1048: 			ERROR_REPORT("Unable to decode tag and length");


                     597 ;1049: 			return FALSE;


                     598 ;1050: 		}


                     599 ;1051: 		//Пишем Sequence и размер



                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
                     600 ;1052: 		!!!


                     601 ;1053: 		//Цикл по детям


                     602 ;1054: 		if (!GoCB_initDataSetIemChildren())


                     603 ;1055: 		{


                     604 ;1056: 			return FALSE;


                     605 ;1057: 		}


                     606 ;1058: 	}


                     607 ;1059: }


                     608 ;1060: */


                     609 ;1061: 


                     610 ;1062: static void registerGoCB(int goCBPos)


                     611 ;1063: {		


                     612 ;1064: 	GoCB goCB = getFreeGoCB();


                     613 ;1065: 	g_goCBPath.goCBPos = goCBPos;


                     614 ;1066: 	TRACE("GoCB is found at 0x%04X", goCBPos);


                     615 ;1067: 	if (goCB == NULL)


                     616 ;1068: 	{


                     617 ;1069: 		ERROR_REPORT("Unable to register GoCB: too many GoCBs");


                     618 ;1070: 		return;


                     619 ;1071: 	}


                     620 ;1072: 	if (!GoCB_init(goCB, goCBPos))


                     621 ;1073: 	{


                     622 ;1074: 		ERROR_REPORT("Error initializing GoCB");


                     623 ;1075: 	}


                     624 ;1076: }


                     625 ;1077: 


                     626 ;1078: 


                     627 ;1079: // Регистрирует все GoCB, которые найдёт в указанном объекте FC


                     628 ;1080: // если FC "GO". Если FC другой, то не делает ничего


                     629 ;1081: void registerGoCBsGivenFC(int fcPos)


                     630 ;1082: {


                     631 ;1083: 	StringView fcName;


                     632 ;1084: 


                     633 ;1085: 	g_goCBPath.fcPos = fcPos;


                     634 ;1086: 


                     635 ;1087: 	if (!getObjectName(fcPos, &fcName))


                     636 ;1088: 	{


                     637 ;1089: 		ERROR_REPORT("Unable to read FC name");


                     638 ;1090: 		return;


                     639 ;1091: 	}


                     640 ;1092: 	if (fcName.len != 2)


                     641 ;1093: 	{


                     642 ;1094: 		ERROR_REPORT("Invalid FC name");


                     643 ;1095: 		return;


                     644 ;1096: 	}


                     645 ;1097: 	if (memcmp("GO", fcName.p, 2) == 0)


                     646 ;1098: 	{		


                     647 ;1099: 		processSubobjects(fcPos, registerGoCB);


                     648 ;1100: 	}	


                     649 ;1101: }


                     650 ;1102: 


                     651 ;1103: static void registerAllLogicalNodeGoCB(int lnPos)


                     652 ;1104: {


                     653 ;1105: 	g_goCBPath.lnPos = lnPos;


                     654 ;1106: 	processSubobjects(lnPos, registerGoCBsGivenFC);


                     655 ;1107: }


                     656 ;1108: 


                     657 ;1109: static void registerAllLogicalDeviceGoCB(int ldPos)


                     658 ;1110: {


                     659 ;1111: 	int dataSectionPos;


                     660 ;1112: 



                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
                     661 ;1113: 	g_goCBPath.ldPos = ldPos;


                     662 ;1114: 	dataSectionPos = findObjectByTag(ldPos, IED_VMD_DATA_SECTION);


                     663 ;1115: 	if (!dataSectionPos)


                     664 ;1116: 	{


                     665 ;1117: 		ERROR_REPORT("Data section is not found");


                     666 ;1118: 		return;


                     667 ;1119: 	}


                     668 ;1120: 


                     669 ;1121: 	processSubobjects(dataSectionPos, registerAllLogicalNodeGoCB);


                     670 ;1122: }


                     671 ;1123: 


                     672 ;1124: static void registerAllGoCB(void)


                     673 

                     674 ;1127: }


                     675 

                     676 ;1128: 


                     677 ;1129: void GOOSE_send(GSESettings gse)


                     678 ;1130: {


                     679 ;1131: 	GOOSE_writePktCounters(gse);


                     680 ;1132: 	gse->sqNum.value++;


                     681 ;1133: 	NetTools_send(gse->netIf, gse->outPkt, gse->outPktSize);


                     682 ;1134: }


                     683 ;1135: 


                     684 ;1136: static bool GOOSE_readAndDetectChange(GoCB goCB, void* dataSliceWnd)


                     685 

                     686 ;1152: }


                     687 

                     688 ;1153: 


                     689 ;1154: static void GOOSE_writeDataToFirstGSEBuf(GoCB goCB)


                     690 

                     691 ;1161: 	}


                     692 ;1162: }


                     693 

                     694 ;1163: 


                     695 ;1164: //Копирует PDU пакета


                     696 ;1165: static void copyGSEPPDU(GSESettings gseSrc, GSESettings gseDst)


                     697 

                     698 ;1168: }


                     699 

                     700 ;1169: 


                     701 ;1170: void GOOSE_sendChanges(void)


                     702 ;1171: {


                     703 ;1172: 	GSESettings gse;


                     704 ;1173: 	size_t goCBIdx;


                     705 ;1174: 	size_t gseIdx;


                     706 ;1175:     void* dataSliceWnd;


                     707 ;1176:     int interruptState = PTools_lockInterrupt();    


                     708 ;1177:     dataSliceWnd = DataSlice_getDataSliceWnd();


                     709 ;1178: 	for (goCBIdx = 0; goCBIdx < g_goCBCount; ++goCBIdx)


                     710 ;1179: 	{


                     711 ;1180: 		GoCB goCB = g_goCBs + goCBIdx;


                     712 ;1181: 		


                     713 ;1182: 		if (goCB->goEna && !goCB->ndsCom)


                     714 ;1183: 		{


                     715 ;1184:             if (GOOSE_readAndDetectChange(goCB, dataSliceWnd))


                     716 ;1185: 			{


                     717 ;1186: 				//Одинаковые данные для всех GSE


                     718 ;1187: 				gse = goCB->gse;


                     719 ;1188: 				writePktTime(gse);


                     720 ;1189: 				GOOSE_writeDataToFirstGSEBuf(goCB);


                     721 ;1190: 				gse->stNum.value++;



                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
                     722 ;1191: 				gse->sqNum.value = 1;


                     723 ;1192: 


                     724 ;1193: 				for (gseIdx = 0; gseIdx < goCB->gseCount; ++gseIdx)


                     725 ;1194: 				{


                     726 ;1195: 					gse = goCB->gse + gseIdx;


                     727 ;1196: 					


                     728 ;1197: 					if (gseIdx > 0)


                     729 ;1198: 					{						


                     730 ;1199: 						copyGSEPPDU(goCB->gse, gse);


                     731 ;1200: 					}


                     732 ;1201: 


                     733 ;1202: 					gse->currT = gse->t1;


                     734 ;1203: 					gse->msCounter = 0;


                     735 ;1204:                     gse->t1Counter = T1_COUNT;


                     736 ;1205: 					GOOSE_send(gse);


                     737 ;1206: 				}


                     738 ;1207: 			}


                     739 ;1208: 		}


                     740 ;1209: 	}


                     741 ;1210:     PTools_unlockInterrupt(interruptState);


                     742 ;1211: }


                     743 ;1212: 


                     744 ;1213: static void processGSETimer(GSESettings gse)


                     745 

                     746 ;1233:             }


                     747 ;1234:         }


                     748 ;1235: 	}	


                     749 ;1236: }


                     750 

                     751 	.text

                     752 	.align	4

                     753 writeUlongBE:

00000000 e1a02c21    754 	mov	r2,r1 lsr 24

00000004 e5c02000    755 	strb	r2,[r0]

00000008 e1a02821    756 	mov	r2,r1 lsr 16

0000000c e5c02001    757 	strb	r2,[r0,1]

00000010 e1a02421    758 	mov	r2,r1 lsr 8

00000014 e5c02002    759 	strb	r2,[r0,2]

00000018 e5c01003    760 	strb	r1,[r0,3]

0000001c e12fff1e*   761 	ret	

                     762 	.endf	writeUlongBE

                     763 	.align	4

                     764 

                     765 ;p	r0	param

                     766 ;data	r1	param

                     767 

                     768 	.section ".bss","awb"

                     769 .L814:

                     770 	.data

                     771 	.text

                     772 

                     773 

                     774 	.align	4

                     775 	.align	4

                     776 	.align	4

                     777 prepareGOOSEbuf::

00000020 e92d4cf0    778 	stmfd	[sp]!,{r4-r7,r10-fp,lr}

                     779 ;244: {	


                     780 

                     781 ;245: 	if (!calcGOOSEDataSize(goCB, gSizes))


                     782 


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
                     783 ;163: {	


                     784 

00000024 e1a0b001    785 	mov	fp,r1

00000028 e1a05002    786 	mov	r5,r2

0000002c e1a06000    787 	mov	r6,r0

00000030 e286af61    788 	add	r10,r6,0x0184

00000034 e3a04000    789 	mov	r4,0

                     790 ;164: 	size_t daIdx;


                     791 ;165: 	uint16_t totalSize = 0;


                     792 

                     793 ;166: 	for (daIdx = 0; daIdx < goCB->daCount; ++daIdx)


                     794 

00000038 e24dd020    795 	sub	sp,sp,32

0000003c e5960180    796 	ldr	r0,[r6,384]

00000040 e1a07004    797 	mov	r7,r4

00000044 e1570000    798 	cmp	r7,r0

00000048 2a00000c    799 	bhs	.L846

                     800 .L828:

0000004c e49a0004    801 	ldr	r0,[r10],4

00000050 e1a0100d    802 	mov	r1,sp

00000054 eb000000*   803 	bl	FDA_getFixedEncodedSize

                     804 ;167: 	{


                     805 

                     806 ;168: 		size_t daSize;


                     807 ;169: 		if (!FDA_getFixedEncodedSize(goCB->daList[daIdx], &daSize))


                     808 

00000058 e3500000    809 	cmp	r0,0

0000005c 0a0000e3    810 	beq	.L870

                     811 ;170: 		{


                     812 

                     813 ;171: 			return FALSE;


                     814 

                     815 ;172: 		}


                     816 ;173: 		totalSize += (uint16_t)daSize;


                     817 

00000060 e1dd00b0    818 	ldrh	r0,[sp]

00000064 e2877001    819 	add	r7,r7,1

00000068 e0844000    820 	add	r4,r4,r0

0000006c e1a04804    821 	mov	r4,r4 lsl 16

00000070 e5960180    822 	ldr	r0,[r6,384]

00000074 e1a04824    823 	mov	r4,r4 lsr 16

00000078 e1570000    824 	cmp	r7,r0

0000007c 3afffff2    825 	blo	.L828

                     826 .L846:

00000080 e1cd41bc    827 	strh	r4,[sp,28]

00000084 e1a00004    828 	mov	r0,r4

00000088 eb000000*   829 	bl	BerEncoder_determineFullObjectSize

0000008c e1a0a000    830 	mov	r10,r0

00000090 e1cda1ba    831 	strh	r10,[sp,26]

00000094 e286001d    832 	add	r0,r6,29

00000098 eb000000*   833 	bl	strlen

0000009c eb000000*   834 	bl	BerEncoder_determineFullObjectSize

000000a0 e1a07000    835 	mov	r7,r0

000000a4 e5960064    836 	ldr	r0,[r6,100]

000000a8 eb000000*   837 	bl	BerEncoder_UInt32determineEncodedSize

000000ac eb000000*   838 	bl	BerEncoder_determineFullObjectSize

000000b0 e0877000    839 	add	r7,r7,r0

000000b4 e596000c    840 	ldr	r0,[r6,12]

000000b8 eb000000*   841 	bl	BerEncoder_determineFullObjectSize

000000bc e0877000    842 	add	r7,r7,r0

000000c0 e5960004    843 	ldr	r0,[r6,4]


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
000000c4 eb000000*   844 	bl	BerEncoder_determineFullObjectSize

000000c8 e0877000    845 	add	r7,r7,r0

000000cc e1d606b0    846 	ldrh	r0,[r6,96]

000000d0 e2877019    847 	add	r7,r7,25

000000d4 eb000000*   848 	bl	BerEncoder_UInt32determineEncodedSize

000000d8 eb000000*   849 	bl	BerEncoder_determineFullObjectSize

000000dc e0877000    850 	add	r7,r7,r0

000000e0 e5960180    851 	ldr	r0,[r6,384]

000000e4 e2877003    852 	add	r7,r7,3

000000e8 eb000000*   853 	bl	BerEncoder_UInt32determineEncodedSize

000000ec eb000000*   854 	bl	BerEncoder_determineFullObjectSize

000000f0 e0877000    855 	add	r7,r7,r0

000000f4 e08aa007    856 	add	r10,r10,r7

000000f8 e1cda1b8    857 	strh	r10,[sp,24]

000000fc e1a0a80a    858 	mov	r10,r10 lsl 16

00000100 e1a0a82a    859 	mov	r10,r10 lsr 16

00000104 e1a0000a    860 	mov	r0,r10

00000108 eb000000*   861 	bl	BerEncoder_determineFullObjectSize

0000010c e1a07800    862 	mov	r7,r0 lsl 16

00000110 e1a07827    863 	mov	r7,r7 lsr 16

00000114 e1cd71b6    864 	strh	r7,[sp,22]

00000118 e28b100e    865 	add	r1,fp,14

0000011c e2870008    866 	add	r0,r7,8

00000120 e1cd00ba    867 	strh	r0,[sp,10]

00000124 e1cd01b4    868 	strh	r0,[sp,20]

                     869 ;174: 	}


                     870 ;175: 	gSizes->dataSize = totalSize;


                     871 

                     872 ;176: 	gSizes->dataTLSize = BerEncoder_determineFullObjectSize(gSizes->dataSize);


                     873 

                     874 ;177: 	return TRUE;


                     875 

                     876 ;246: 	{


                     877 

                     878 ;247: 		ERROR_REPORT("Unable to calculate GOOSE data size");


                     879 ;248: 		return FALSE;


                     880 

                     881 ;249: 	}


                     882 ;250: 	if (!calcGOOSEPDUSize(goCB, gSizes))


                     883 

                     884 ;181: {


                     885 

                     886 ;182: 	uint16_t totalSize = 0;	


                     887 

                     888 ;183: 	//gocbRef, String


                     889 ;184: 	totalSize += BerEncoder_determineFullObjectSize(strlen(goCB->goCBRef));


                     890 

                     891 ;185: 	//timeAlowedToLive, INT32U, ms


                     892 ;186: 	totalSize += BerEncoder_determineFullObjectSize(


                     893 

                     894 ;187: 		BerEncoder_UInt32determineEncodedSize(goCB->timeAllowedToLive));


                     895 ;188: 	//datSet, String


                     896 ;189: 	totalSize += BerEncoder_determineFullObjectSize(goCB->dataSetName.len);


                     897 

                     898 ;190: 	//goID, String


                     899 ;191: 	totalSize += BerEncoder_determineFullObjectSize(goCB->appID.len);


                     900 

                     901 ;192: 	//t, TimeStamp


                     902 ;193: 	totalSize += 10;


                     903 

                     904 ;194: 	//stNum, INT32U



                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
                     905 ;195: 	totalSize += 6;


                     906 

                     907 ;196: 	//sqNum, INT32U


                     908 ;197: 	totalSize += 6;


                     909 

                     910 ;198: 	//test, bool


                     911 ;199: 	totalSize += 3;


                     912 

                     913 ;200: 	//confRev, INT32U


                     914 ;201: 	totalSize += BerEncoder_determineFullObjectSize(


                     915 

                     916 ;202: 		BerEncoder_UInt32determineEncodedSize(goCB->confRev));


                     917 ;203: 	//ndsCom


                     918 ;204: 	totalSize += 3;


                     919 

                     920 ;205: 	//numDatSetEntries


                     921 ;206: 	totalSize += BerEncoder_determineFullObjectSize(


                     922 

                     923 ;207: 		BerEncoder_UInt32determineEncodedSize(


                     924 ;208: 			goCB->daCount));


                     925 ;209: 	


                     926 ;210: 	gSizes->pduSize = totalSize + gSizes->dataTLSize;


                     927 

                     928 ;211: 	gSizes->pduWithTLSize = BerEncoder_determineFullObjectSize(gSizes->pduSize);


                     929 

                     930 ;212: 	return TRUE;


                     931 

                     932 ;251: 	{


                     933 

                     934 ;252: 		ERROR_REPORT("Unable to calculate GOOSE PDU size");


                     935 ;253: 		return FALSE;


                     936 

                     937 ;254: 	}		


                     938 ;255: 	//goosePktSize;			


                     939 ;256: 	if (!calcGOOSEPktSize(goCB, gse, gSizes))


                     940 

                     941 ;216: {


                     942 

                     943 ;217: 	uint16_t totalSize = 0;


                     944 

                     945 ;218: 	/*


                     946 ;219: 	//Destination	


                     947 ;220: 	totalSize += 6;


                     948 ;221: 	//Source	


                     949 ;222: 	totalSize += 6;


                     950 ;223: 	//VLAN	


                     951 ;224: 	if (gse->vlan.useVLAN)


                     952 ;225: 	{


                     953 ;226: 		totalSize += 4;


                     954 ;227: 	}


                     955 ;228: 	//Ethernet Type	


                     956 ;229: 	totalSize += 2;


                     957 ;230: 	*/


                     958 ;231: 	//APP ID


                     959 ;232: 	totalSize += 2;


                     960 

                     961 ;233: 	//Length	


                     962 ;234: 	totalSize += 2;


                     963 

                     964 ;235: 	//Reserved1


                     965 ;236: 	totalSize += 2;



                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
                     966 

                     967 ;237: 	//Reserved2


                     968 ;238: 	totalSize += 2;


                     969 

                     970 ;239: 	gSizes->goosePktSize = totalSize + gSizes->pduWithTLSize;


                     971 

                     972 ;240: 	return TRUE;


                     973 

                     974 ;257: 	{


                     975 

                     976 ;258: 		ERROR_REPORT("Unable to calculate full GOOSE packet size");


                     977 ;259: 		return FALSE;


                     978 

                     979 ;260: 	}


                     980 ;261: 	return TRUE;


                     981 

                     982 ;293: 	BufferView* gooseBuf)


                     983 ;294: {


                     984 

                     985 ;295: 	//Destination


                     986 ;296: 	if (BufferView_writeData(gooseBuf, gse->dst, 6) < 6)


                     987 

00000128 e1a00005    988 	mov	r0,r5

0000012c e3a02006    989 	mov	r2,6

00000130 eb000000*   990 	bl	BufferView_writeData

00000134 e3500006    991 	cmp	r0,6

00000138 3a0000ac    992 	blo	.L870

                     993 ;297: 	{


                     994 

                     995 ;298: 		return FALSE;


                     996 

                     997 ;299: 	}


                     998 ;300: 	//Source


                     999 ;301: 	if (BufferView_writeData(gooseBuf, gse->src, 6) < 6)


                    1000 

0000013c e28b1008   1001 	add	r1,fp,8

00000140 e1a00005   1002 	mov	r0,r5

00000144 e3a02006   1003 	mov	r2,6

00000148 eb000000*  1004 	bl	BufferView_writeData

0000014c e3500006   1005 	cmp	r0,6

00000150 3a0000a6   1006 	blo	.L870

                    1007 ;302: 	{


                    1008 

                    1009 ;303: 		return FALSE;


                    1010 

                    1011 ;304: 	}


                    1012 ;305: 


                    1013 ;306: 	//VLAN


                    1014 ;307: 	if (!writeVLANInfo(&gse->vlan, gooseBuf))


                    1015 

                    1016 ;265: {


                    1017 

                    1018 ;266: 	if (vlan->useVLAN)


                    1019 

00000154 e5db0014   1020 	ldrb	r0,[fp,20]

00000158 e3500000   1021 	cmp	r0,0

0000015c 0a000014   1022 	beq	.L857

                    1023 ;267: 	{


                    1024 

                    1025 ;268: 		unsigned char vlanBuf[4];


                    1026 ;269: 		uint8_t tci1 = vlan->priority << 5;



                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
                    1027 

00000160 e59b001c   1028 	ldr	r0,[fp,28]

00000164 e1a01280   1029 	mov	r1,r0 lsl 5

                    1030 ;270: 		uint8_t tci2 = vlan->id % 256;


                    1031 

00000168 e59b0018   1032 	ldr	r0,[fp,24]

0000016c e1a02fc0   1033 	mov	r2,r0 asr 31

00000170 e0803002   1034 	add	r3,r0,r2

00000174 e3c330ff   1035 	bic	r3,r3,255

00000178 e0403003   1036 	sub	r3,r0,r3

                    1037 ;271: 		tci1 += vlan->id / 256;


                    1038 

0000017c e5cd3007   1039 	strb	r3,[sp,7]

                    1040 ;281: 


                    1041 ;282: 		if (BufferView_writeData(gooseBuf, vlanBuf, sizeof(vlanBuf)) < sizeof(vlanBuf))


                    1042 

00000180 e0800c22   1043 	add	r0,r0,r2 lsr 24

00000184 e0811440   1044 	add	r1,r1,r0 asr 8

                    1045 ;272: 


                    1046 ;273: 		// идентификатор vlan


                    1047 ;274: 		vlanBuf[0] = 0x81;


                    1048 

00000188 e5cd1006   1049 	strb	r1,[sp,6]

                    1050 ;279: 		// id


                    1051 ;280: 		vlanBuf[3] = tci2; 


                    1052 

0000018c e28d1004   1053 	add	r1,sp,4

00000190 e3a00081   1054 	mov	r0,129

00000194 e5cd0004   1055 	strb	r0,[sp,4]

                    1056 ;275: 		vlanBuf[1] = 0x00;


                    1057 

00000198 e3a00000   1058 	mov	r0,0

0000019c e5cd0005   1059 	strb	r0,[sp,5]

                    1060 ;276: 


                    1061 ;277: 		// priority + id


                    1062 ;278: 		vlanBuf[2] = tci1; 


                    1063 

000001a0 e1a00005   1064 	mov	r0,r5

000001a4 e3a02004   1065 	mov	r2,4

000001a8 eb000000*  1066 	bl	BufferView_writeData

000001ac e3500004   1067 	cmp	r0,4

000001b0 3a00008e   1068 	blo	.L870

                    1069 .L857:

                    1070 ;283: 		{


                    1071 

                    1072 ;284: 			return FALSE;


                    1073 

                    1074 ;285: 		}


                    1075 ;286: 


                    1076 ;287: 		return TRUE;


                    1077 

                    1078 ;288: 	}


                    1079 ;289: 	return TRUE;


                    1080 

                    1081 ;308: 	{


                    1082 

                    1083 ;309: 		return FALSE;


                    1084 

                    1085 ;310: 	}


                    1086 ;311: 


                    1087 ;312: 	//Ethernet Type



                                                                      Page 19
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
                    1088 ;313: 	if (!BufferView_writeUshortBE(gooseBuf, ETHTYPE_GOOSE))


                    1089 

000001b4 e3a01c88   1090 	mov	r1,34<<10

000001b8 e28110b8   1091 	add	r1,r1,184

000001bc e1a00005   1092 	mov	r0,r5

000001c0 eb000000*  1093 	bl	BufferView_writeUshortBE

000001c4 e3500000   1094 	cmp	r0,0

000001c8 0a000088   1095 	beq	.L870

                    1096 ;314: 	{


                    1097 

                    1098 ;315: 		return FALSE;


                    1099 

                    1100 ;316: 	}


                    1101 ;317: 


                    1102 ;318: 	//APP ID


                    1103 ;319: 	if (!BufferView_writeUshortBE(gooseBuf, gse->appID))


                    1104 

000001cc e1db12b0   1105 	ldrh	r1,[fp,32]

000001d0 e1a00005   1106 	mov	r0,r5

000001d4 eb000000*  1107 	bl	BufferView_writeUshortBE

000001d8 e3500000   1108 	cmp	r0,0

000001dc 0a000083   1109 	beq	.L870

                    1110 ;320: 	{


                    1111 

                    1112 ;321: 		return FALSE;


                    1113 

                    1114 ;322: 	}


                    1115 ;323: 


                    1116 ;324: 	


                    1117 ;325: 	//Это длина всего пакета GOOSE без Ehthernet-заголовка


                    1118 ;326: 	if (!BufferView_writeUshortBE(gooseBuf, gooseSizes->goosePktSize))


                    1119 

000001e0 e1dd10ba   1120 	ldrh	r1,[sp,10]

000001e4 e1a00005   1121 	mov	r0,r5

000001e8 eb000000*  1122 	bl	BufferView_writeUshortBE

000001ec e3500000   1123 	cmp	r0,0

000001f0 0a00007e   1124 	beq	.L870

                    1125 ;327: 	{


                    1126 

                    1127 ;328: 		return FALSE;


                    1128 

                    1129 ;329: 	}


                    1130 ;330: 


                    1131 ;331: 	//Reserved


                    1132 ;332: 	if (!BufferView_writeUshortBE(gooseBuf, 0) ||


                    1133 

000001f4 e1a00005   1134 	mov	r0,r5

000001f8 e3a01000   1135 	mov	r1,0

000001fc eb000000*  1136 	bl	BufferView_writeUshortBE

00000200 e3500000   1137 	cmp	r0,0

00000204 0a000079   1138 	beq	.L870

00000208 e1a00005   1139 	mov	r0,r5

0000020c e3a01000   1140 	mov	r1,0

00000210 eb000000*  1141 	bl	BufferView_writeUshortBE

00000214 e3500000   1142 	cmp	r0,0

00000218 0a000074   1143 	beq	.L870

                    1144 ;333: 		!BufferView_writeUshortBE(gooseBuf, 0))


                    1145 ;334: 	{


                    1146 

                    1147 ;335: 		return FALSE;


                    1148 


                                                                      Page 20
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
                    1149 ;336: 	}


                    1150 ;337: 	return TRUE;


                    1151 

                    1152 ;363: 	GOOSESizes* gooseSizes, BufferView* templateBuf)


                    1153 ;364: {


                    1154 

0000021c e59f0da8*  1155 	ldr	r0,.L1657

00000220 e5902000   1156 	ldr	r2,[r0]

00000224 e58d200c   1157 	str	r2,[sp,12]

00000228 e5900004   1158 	ldr	r0,[r0,4]

0000022c e58d0010   1159 	str	r0,[sp,16]

                    1160 ;365: 	uint8_t dummyTime[8] = { 0 };


                    1161 

                    1162 ;366: 


                    1163 ;367: 	//Указатель на PDU чтобы потом копировать


                    1164 ;368: 	gse->pPDU = templateBuf->p + templateBuf->pos;


                    1165 

00000230 e8950006   1166 	ldmfd	[r5],{r1-r2}

00000234 e58b7030   1167 	str	r7,[fp,48]

                    1168 ;370: 


                    1169 ;371: 	if (!BufferView_encodeTL(templateBuf, 0x61, gooseSizes->pduSize))


                    1170 

00000238 e0820001   1171 	add	r0,r2,r1

0000023c e1a0200a   1172 	mov	r2,r10

00000240 e58b002c   1173 	str	r0,[fp,44]

                    1174 ;369: 	gse->pduSize = gooseSizes->pduWithTLSize;


                    1175 

00000244 e1a00005   1176 	mov	r0,r5

00000248 e3a01061   1177 	mov	r1,97

0000024c eb000000*  1178 	bl	BufferView_encodeTL

00000250 e3500000   1179 	cmp	r0,0

00000254 0a000065   1180 	beq	.L870

                    1181 ;372: 	{


                    1182 

                    1183 ;373: 		return FALSE;


                    1184 

                    1185 ;374: 	}


                    1186 ;375: 


                    1187 ;376: 	//gocbRef	


                    1188 ;377: 	if (!BufferView_encodeStr(templateBuf, 0x80, goCB->goCBRef))


                    1189 

00000258 e286201d   1190 	add	r2,r6,29

0000025c e1a00005   1191 	mov	r0,r5

00000260 e3a01080   1192 	mov	r1,128

00000264 eb000000*  1193 	bl	BufferView_encodeStr

00000268 e3500000   1194 	cmp	r0,0

0000026c 0a00005f   1195 	beq	.L870

                    1196 ;378: 	{


                    1197 

                    1198 ;379: 		return FALSE;


                    1199 

                    1200 ;380: 	}	


                    1201 ;381: 	//timeAllowedToLive (int)


                    1202 ;382: 	if (!BufferView_encodeUInt32(templateBuf, 0x81, goCB->timeAllowedToLive))


                    1203 

00000270 e5962064   1204 	ldr	r2,[r6,100]

00000274 e1a00005   1205 	mov	r0,r5

00000278 e3a01081   1206 	mov	r1,129

0000027c eb000000*  1207 	bl	BufferView_encodeUInt32

00000280 e3500000   1208 	cmp	r0,0

00000284 0a000059   1209 	beq	.L870


                                                                      Page 21
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
                    1210 ;383: 	{


                    1211 

                    1212 ;384: 		return FALSE;


                    1213 

                    1214 ;385: 	}


                    1215 ;386: 	//datSet


                    1216 ;387: 	if (!BufferView_encodeStringView(templateBuf, 0x82, &goCB->dataSetName))


                    1217 

00000288 e286200c   1218 	add	r2,r6,12

0000028c e1a00005   1219 	mov	r0,r5

00000290 e3a01082   1220 	mov	r1,130

00000294 eb000000*  1221 	bl	BufferView_encodeStringView

00000298 e3500000   1222 	cmp	r0,0

0000029c 0a000053   1223 	beq	.L870

                    1224 ;388: 	{


                    1225 

                    1226 ;389: 		return FALSE;


                    1227 

                    1228 ;390: 	}


                    1229 ;391: 	//goID


                    1230 ;392: 	if (!BufferView_encodeStringView(templateBuf, 0x83, &goCB->appID))


                    1231 

000002a0 e2862004   1232 	add	r2,r6,4

000002a4 e1a00005   1233 	mov	r0,r5

000002a8 e3a01083   1234 	mov	r1,131

000002ac eb000000*  1235 	bl	BufferView_encodeStringView

000002b0 e3500000   1236 	cmp	r0,0

000002b4 0a00004d   1237 	beq	.L870

                    1238 ;393: 	{


                    1239 

                    1240 ;394: 		return FALSE;


                    1241 

                    1242 ;395: 	}


                    1243 ;396: 	//t


                    1244 ;397: 	//Запоминаем куда потом писать время


                    1245 ;398: 	gse->pPktTime = templateBuf->p + templateBuf->pos;


                    1246 

000002b8 e8950006   1247 	ldmfd	[r5],{r1-r2}

000002bc e3a03008   1248 	mov	r3,8

000002c0 e0820001   1249 	add	r0,r2,r1

000002c4 e28d200c   1250 	add	r2,sp,12

000002c8 e58b0034   1251 	str	r0,[fp,52]

                    1252 ;399: 	if (!BufferView_encodeOctetString(templateBuf, 0x84, dummyTime, 8))


                    1253 

000002cc e1a00005   1254 	mov	r0,r5

000002d0 e3a01084   1255 	mov	r1,132

000002d4 eb000000*  1256 	bl	BufferView_encodeOctetString

000002d8 e3500000   1257 	cmp	r0,0

000002dc 0a000043   1258 	beq	.L870

                    1259 ;400: 	{


                    1260 

                    1261 ;401: 		return FALSE;


                    1262 

                    1263 ;402: 	}	


                    1264 ;403: 	//stNum


                    1265 ;404: 	//Запоминаем позицию, куда потом вписывать stNum


                    1266 ;405: 	gse->stNum.p = templateBuf->p + templateBuf->pos + 2;


                    1267 

000002e0 e8950006   1268 	ldmfd	[r5],{r1-r2}

000002e4 e3a03004   1269 	mov	r3,4

000002e8 e0820001   1270 	add	r0,r2,r1


                                                                      Page 22
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
000002ec e28d200c   1271 	add	r2,sp,12

000002f0 e2800002   1272 	add	r0,r0,2

000002f4 e58b0040   1273 	str	r0,[fp,64]

                    1274 ;406: 	if (!BufferView_encodeOctetString(templateBuf, 0x85, dummyTime, 4))


                    1275 

000002f8 e1a00005   1276 	mov	r0,r5

000002fc e3a01085   1277 	mov	r1,133

00000300 eb000000*  1278 	bl	BufferView_encodeOctetString

00000304 e3500000   1279 	cmp	r0,0

00000308 0a000038   1280 	beq	.L870

                    1281 ;407: 	{


                    1282 

                    1283 ;408: 		return FALSE;


                    1284 

                    1285 ;409: 	}	


                    1286 ;410: 	//sqNum	


                    1287 ;411: 	//Запоминаем позицию, куда потом вписывать sqNum


                    1288 ;412: 	gse->sqNum.p = templateBuf->p + templateBuf->pos + 2;


                    1289 

0000030c e8950006   1290 	ldmfd	[r5],{r1-r2}

00000310 e3a03004   1291 	mov	r3,4

00000314 e0820001   1292 	add	r0,r2,r1

00000318 e28d200c   1293 	add	r2,sp,12

0000031c e2800002   1294 	add	r0,r0,2

00000320 e58b0038   1295 	str	r0,[fp,56]

                    1296 ;413: 	if (!BufferView_encodeOctetString(templateBuf, 0x86, dummyTime, 4))


                    1297 

00000324 e1a00005   1298 	mov	r0,r5

00000328 e3a01086   1299 	mov	r1,134

0000032c eb000000*  1300 	bl	BufferView_encodeOctetString

00000330 e3500000   1301 	cmp	r0,0

00000334 0a00002d   1302 	beq	.L870

                    1303 ;414: 	{


                    1304 

                    1305 ;415: 		return FALSE;


                    1306 

                    1307 ;416: 	}


                    1308 ;417: 	//test


                    1309 ;418: 	if (!BufferView_encodeBoolean(templateBuf, 0x87, FALSE))


                    1310 

00000338 e1a00005   1311 	mov	r0,r5

0000033c e3a02000   1312 	mov	r2,0

00000340 e3a01087   1313 	mov	r1,135

00000344 eb000000*  1314 	bl	BufferView_encodeBoolean

00000348 e3500000   1315 	cmp	r0,0

0000034c 0a000027   1316 	beq	.L870

                    1317 ;419: 	{


                    1318 

                    1319 ;420: 		return FALSE;


                    1320 

                    1321 ;421: 	}


                    1322 ;422: 		


                    1323 ;423: 	//confRev


                    1324 ;424: 	if (!BufferView_encodeUInt32(templateBuf, 0x88, goCB->confRev))


                    1325 

00000350 e1d626b0   1326 	ldrh	r2,[r6,96]

00000354 e1a00005   1327 	mov	r0,r5

00000358 e3a01088   1328 	mov	r1,136

0000035c eb000000*  1329 	bl	BufferView_encodeUInt32

00000360 e3500000   1330 	cmp	r0,0

00000364 0a000021   1331 	beq	.L870


                                                                      Page 23
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
                    1332 ;425: 	{


                    1333 

                    1334 ;426: 		return FALSE;


                    1335 

                    1336 ;427: 	}


                    1337 ;428: 	//ndsCom


                    1338 ;429: 	//На момент формирования шаблона ndsCom неизвестен.


                    1339 ;430: 	//Но поскольку GOOSE с ndsCom не посылается вообще,


                    1340 ;431: 	//можно смело писать false


                    1341 ;432: 	if (!BufferView_encodeBoolean(templateBuf, 0x89, false/*goCB->ndsCom*/))


                    1342 

00000368 e1a00005   1343 	mov	r0,r5

0000036c e3a02000   1344 	mov	r2,0

00000370 e3a01089   1345 	mov	r1,137

00000374 eb000000*  1346 	bl	BufferView_encodeBoolean

00000378 e3500000   1347 	cmp	r0,0

0000037c 0a00001b   1348 	beq	.L870

                    1349 ;433: 	{


                    1350 

                    1351 ;434: 		return FALSE;


                    1352 

                    1353 ;435: 	}


                    1354 ;436: 	//numDatSetEntries


                    1355 ;437: 	if (!BufferView_encodeUInt32(templateBuf, 0x8A, goCB->daCount))


                    1356 

00000380 e5962180   1357 	ldr	r2,[r6,384]

00000384 e1a00005   1358 	mov	r0,r5

00000388 e3a0108a   1359 	mov	r1,138

0000038c eb000000*  1360 	bl	BufferView_encodeUInt32

00000390 e3500000   1361 	cmp	r0,0

00000394 0a000015   1362 	beq	.L870

                    1363 ;438: 	{


                    1364 

                    1365 ;439: 		return FALSE;


                    1366 

                    1367 ;440: 	}


                    1368 ;441: 	//allData


                    1369 ;442: 	if (!writeDataTemplate(goCB, gooseSizes, templateBuf))


                    1370 

                    1371 ;341: 	BufferView* templateBuf)


                    1372 ;342: {


                    1373 

                    1374 ;343: 	size_t daIdx;


                    1375 ;344: 	


                    1376 ;345: 	if (!BufferView_encodeTL(templateBuf, 0xAB, gooseSizes->dataSize))


                    1377 

00000398 e1a02004   1378 	mov	r2,r4

0000039c e1a00005   1379 	mov	r0,r5

000003a0 e3a010ab   1380 	mov	r1,171

000003a4 eb000000*  1381 	bl	BufferView_encodeTL

000003a8 e3500000   1382 	cmp	r0,0

000003ac 0a00000f   1383 	beq	.L870

                    1384 ;346: 	{


                    1385 

                    1386 ;347: 		ERROR_REPORT("Error creating GOOSE data template");


                    1387 ;348: 		return FALSE;


                    1388 

                    1389 ;349: 	}


                    1390 ;350: 


                    1391 ;351: 	for (daIdx = 0; daIdx < goCB->daCount; ++daIdx)


                    1392 


                                                                      Page 24
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
000003b0 e2867f61   1393 	add	r7,r6,0x0184

000003b4 e5960180   1394 	ldr	r0,[r6,384]

000003b8 e3a04000   1395 	mov	r4,0

000003bc e1540000   1396 	cmp	r4,r0

000003c0 2a00000c   1397 	bhs	.L869

                    1398 .L900:

                    1399 ;352: 	{


                    1400 

                    1401 ;353: 		if (!FDA_encodeGOOSETemplate(goCB->daList[daIdx], templateBuf))


                    1402 

000003c4 e5970000   1403 	ldr	r0,[r7]

000003c8 e1a01005   1404 	mov	r1,r5

000003cc eb000000*  1405 	bl	FDA_encodeGOOSETemplate

000003d0 e3500000   1406 	cmp	r0,0

000003d4 0a000005   1407 	beq	.L870

                    1408 ;354: 		{


                    1409 

                    1410 ;355: 			ERROR_REPORT("Error creating GOODE data template");


                    1411 ;356: 			return FALSE;


                    1412 

000003d8 e2877004   1413 	add	r7,r7,4

000003dc e5960180   1414 	ldr	r0,[r6,384]

000003e0 e2844001   1415 	add	r4,r4,1

000003e4 e1540000   1416 	cmp	r4,r0

000003e8 3afffff5   1417 	blo	.L900

000003ec ea000001   1418 	b	.L869

                    1419 .L870:

                    1420 ;357: 		}


                    1421 ;358: 	}	


                    1422 ;359: 	return TRUE;


                    1423 

                    1424 ;443: 	{


                    1425 

                    1426 ;444: 		return FALSE;


                    1427 

                    1428 ;445: 	}


                    1429 ;446:     WRITE_TO_FILE("gseData.bin", gse->pPDU, gse->pduSize);


                    1430 ;447: 	return TRUE;


                    1431 

000003f0 e3a00000   1432 	mov	r0,0

000003f4 ea000000   1433 	b	.L821

                    1434 .L869:

000003f8 e3a00001   1435 	mov	r0,1

                    1436 .L821:

000003fc e28dd020   1437 	add	sp,sp,32

00000400 e8bd8cf0   1438 	ldmfd	[sp]!,{r4-r7,r10-fp,pc}

                    1439 	.endf	prepareGOOSEbuf

                    1440 	.align	4

                    1441 ;gooseSizes	[sp,20]	local

                    1442 ;daIdx	r7	local

                    1443 ;totalSize	r4	local

                    1444 ;daSize	[sp]	local

                    1445 ;totalSize	r7	local

                    1446 ;vlanBuf	[sp,4]	local

                    1447 ;tci1	r1	local

                    1448 ;tci2	r3	local

                    1449 ;dummyTime	[sp,12]	local

                    1450 ;daIdx	r4	local

                    1451 

                    1452 ;goCB	r6	param

                    1453 ;gse	fp	param


                                                                      Page 25
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
                    1454 ;gooseBuf	r5	param

                    1455 

                    1456 	.section ".bss","awb"

                    1457 .L1583:

                    1458 	.section ".rodata","a"

                    1459 .L1584:

00000000 00        1460 __UNNAMED_1_static_in_writeGOOSEPduTemplate:	.space	1

00000001 00000000   1461 	.space	7

00000005 000000 
                    1462 	.type	__UNNAMED_1_static_in_writeGOOSEPduTemplate,$object

                    1463 	.size	__UNNAMED_1_static_in_writeGOOSEPduTemplate,8

                    1464 .L1585:

                    1465 __UNNAMED_2_static_in_initGoCBvars:;	"ConfRev\000"

00000008 666e6f43   1466 	.data.b	67,111,110,102

0000000c 00766552   1467 	.data.b	82,101,118,0

                    1468 	.type	__UNNAMED_2_static_in_initGoCBvars,$object

                    1469 	.size	__UNNAMED_2_static_in_initGoCBvars,8

                    1470 	.data

                    1471 .L1586:

00000000 00000000   1472 g_goCBCount:	.data.b	0,0,0,0

                    1473 	.type	g_goCBCount,$object

                    1474 	.size	g_goCBCount,4

                    1475 	.section ".rodata","a"

                    1476 .L1587:

                    1477 __UNNAMED_1_static_in_initGoCBRef:;	"/\000"

00000010 002f      1478 	.data.b	47,0

                    1479 	.type	__UNNAMED_1_static_in_initGoCBRef,$object

                    1480 	.size	__UNNAMED_1_static_in_initGoCBRef,2

                    1481 .L1588:

                    1482 __UNNAMED_2_static_in_initGoCBRef:;	"$\000"

00000012 0024      1483 	.data.b	36,0

                    1484 	.type	__UNNAMED_2_static_in_initGoCBRef,$object

                    1485 	.size	__UNNAMED_2_static_in_initGoCBRef,2

                    1486 .L1589:

                    1487 __UNNAMED_1_static_in_initGoCBvars:;	"AppID\000"

00000014 49707041   1488 	.data.b	65,112,112,73

00000018 0044      1489 	.data.b	68,0

0000001a 0000      1490 	.space	2

                    1491 	.type	__UNNAMED_1_static_in_initGoCBvars,$object

                    1492 	.size	__UNNAMED_1_static_in_initGoCBvars,8

                    1493 .L1590:

                    1494 __UNNAMED_1_static_in_initDatSet:;	"DatSet\000"

0000001c 53746144   1495 	.data.b	68,97,116,83

00000020 7465      1496 	.data.b	101,116

00000022 00        1497 	.data.b	0

00000023 00        1498 	.space	1

                    1499 	.type	__UNNAMED_1_static_in_initDatSet,$object

                    1500 	.size	__UNNAMED_1_static_in_initDatSet,8

                    1501 	.section ".bss","awb"

00000000 00000000   1502 g_goCBPath:	.space	16

00000004 00000000 
00000008 00000000 
0000000c 00000000 
00000010 00000000   1503 g_goCBs:	.space	11152

00000014 00000000 
00000018 00000000 
0000001c 00000000 
00000020 00000000 
00000024 00000000 
00000028 00000000 
0000002c 00000000 

                                                                      Page 26
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
00000030 00000000 
00000034 00000000 
00000038 00000000 
0000003c 00000000 
00000040 00000000 
00000044 00000000 
00000048 00000000 
0000004c 00000000 
00000050 00000000 
00000054 00000000 
00000058 00000000 
0000005c 00000000 
00000060 00000000 
00000064 00000000 
00000068 00000000 
0000006c 00000000 
00000070 00000000 
00000074 00000000 
00000078 00000000 
0000007c 00000000 
00000080 00000000 
00000084 00000000 
00000088 00000000 
0000008c 00000000 
00000090 00000000 
00000094 00000000 
00000098 00000000 
0000009c 00000000 
000000a0 00000000 
000000a4 00000000 
000000a8 00000000 
000000ac 00000000 
000000b0 00000000 
000000b4 00000000 
000000b8 00000000 
000000bc 00000000 
000000c0 00000000 
000000c4 00000000 
000000c8 00000000 
000000cc 00000000 
000000d0 00000000 
000000d4 00000000 
000000d8 00000000 
000000dc 00000000 
000000e0 00000000 
000000e4 00000000 
000000e8 00000000 
000000ec 00000000 
000000f0 00000000 
000000f4 00000000 
000000f8 00000000 
000000fc 00000000 
00000100 00000000 
00000104 00000000 
00000108 00000000 
0000010c 00000000 
                    1504 	.data

                    1505 	.text

                    1506 

                    1507 

                    1508 	.align	4


                                                                      Page 27
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
                    1509 	.align	4

                    1510 GSE_init::

00000404 e92d4030   1511 	stmfd	[sp]!,{r4-r5,lr}

00000408 e24dd024   1512 	sub	sp,sp,36

0000040c e1a04001   1513 	mov	r4,r1

00000410 e28d1003   1514 	add	r1,sp,3

00000414 e1a05000   1515 	mov	r5,r0

00000418 e1a00004   1516 	mov	r0,r4

0000041c e3a03000   1517 	mov	r3,0

00000420 e1a02003   1518 	mov	r2,r3

00000424 eb000000*  1519 	bl	BufferView_decodeTL

00000428 e3500000   1520 	cmp	r0,0

0000042c 0a000065   1521 	beq	.L1708

00000430 e5dd0003   1522 	ldrb	r0,[sp,3]

00000434 e3500030   1523 	cmp	r0,48

00000438 1a000062   1524 	bne	.L1708

0000043c e28d2004   1525 	add	r2,sp,4

00000440 e1a00004   1526 	mov	r0,r4

00000444 e3a01002   1527 	mov	r1,2

00000448 eb000000*  1528 	bl	BufferView_decodeUInt32TL

0000044c e3500000   1529 	cmp	r0,0

00000450 0a00005c   1530 	beq	.L1708

00000454 e5dd0004   1531 	ldrb	r0,[sp,4]

00000458 e2851004   1532 	add	r1,r5,4

0000045c e5c50000   1533 	strb	r0,[r5]

00000460 eb000000*  1534 	bl	NetTools_getIf

00000464 e3500000   1535 	cmp	r0,0

00000468 0a000056   1536 	beq	.L1708

0000046c e5950004   1537 	ldr	r0,[r5,4]

00000470 e2851008   1538 	add	r1,r5,8

00000474 eb000000*  1539 	bl	NetTools_getMac

00000478 e3500000   1540 	cmp	r0,0

0000047c 0a000051   1541 	beq	.L1708

00000480 e28d201c   1542 	add	r2,sp,28

00000484 e1a00004   1543 	mov	r0,r4

00000488 e3a01004   1544 	mov	r1,4

0000048c eb000000*  1545 	bl	BufferView_decodeStringViewTL

00000490 e3500000   1546 	cmp	r0,0

00000494 0a00004b   1547 	beq	.L1708

00000498 e59d001c   1548 	ldr	r0,[sp,28]

0000049c e3500006   1549 	cmp	r0,6

000004a0 1a000048   1550 	bne	.L1708

000004a4 e59d1020   1551 	ldr	r1,[sp,32]

000004a8 e285000e   1552 	add	r0,r5,14

000004ac e3a02006   1553 	mov	r2,6

000004b0 eb000000*  1554 	bl	memcpy

                    1555 ;479: {


                    1556 

                    1557 ;480: 	uint32_t vlanID;


                    1558 ;481: 	uint32_t vlanPriority;


                    1559 ;482: 


                    1560 ;483: 	vlan->useVLAN = FALSE;


                    1561 

000004b4 e3a00000   1562 	mov	r0,0

000004b8 e5c50014   1563 	strb	r0,[r5,20]

                    1564 ;484: 	if (!BufferView_checkTag(berData, BER_VLAN_INFO))


                    1565 

000004bc e9940003   1566 	ldmed	[r4],{r0-r1}

000004c0 e1500001   1567 	cmp	r0,r1

000004c4 2a00001c   1568 	bhs	.L1679

000004c8 e5941000   1569 	ldr	r1,[r4]


                                                                      Page 28
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
000004cc e7d10000   1570 	ldrb	r0,[r1,r0]

000004d0 e35000a0   1571 	cmp	r0,160

000004d4 1a000018   1572 	bne	.L1679

                    1573 ;485: 	{


                    1574 

                    1575 ;486: 		//Конфигурации VLAN нет. Это нормально.		


                    1576 ;487: 		return TRUE;


                    1577 

                    1578 ;488: 	}


                    1579 ;489: 	//Пропускаем тэг и длину информации о VLAN


                    1580 ;490: 	if (!BufferView_decodeTL(berData, NULL, NULL, NULL))


                    1581 

000004d8 e1a00004   1582 	mov	r0,r4

000004dc e3a03000   1583 	mov	r3,0

000004e0 e1a02003   1584 	mov	r2,r3

000004e4 e1a01003   1585 	mov	r1,r3

000004e8 eb000000*  1586 	bl	BufferView_decodeTL

000004ec e3500000   1587 	cmp	r0,0

000004f0 0a000034   1588 	beq	.L1708

                    1589 ;491: 	{


                    1590 

                    1591 ;492: 		ERROR_REPORT("Unable to decode VLAN info tag and length");


                    1592 ;493: 		return FALSE;


                    1593 

                    1594 ;494: 	}


                    1595 ;495: 	//VLAN ID


                    1596 ;496: 	if (!BufferView_decodeUInt32TL(berData, ASN_INTEGER, &vlanID))


                    1597 

000004f4 e28d2014   1598 	add	r2,sp,20

000004f8 e1a00004   1599 	mov	r0,r4

000004fc e3a01002   1600 	mov	r1,2

00000500 eb000000*  1601 	bl	BufferView_decodeUInt32TL

00000504 e3500000   1602 	cmp	r0,0

00000508 0a00002e   1603 	beq	.L1708

                    1604 ;497: 	{


                    1605 

                    1606 ;498: 		ERROR_REPORT("Unable to decode VLAN ID");


                    1607 ;499: 		return FALSE;


                    1608 

                    1609 ;500: 	}


                    1610 ;501: 	vlan->id = vlanID;


                    1611 

0000050c e59d0014   1612 	ldr	r0,[sp,20]

00000510 e28d2018   1613 	add	r2,sp,24

00000514 e5850018   1614 	str	r0,[r5,24]

                    1615 ;502: 


                    1616 ;503: 	//VLAN priority


                    1617 ;504: 	if (!BufferView_decodeUInt32TL(berData, ASN_INTEGER, &vlanPriority))


                    1618 

00000518 e1a00004   1619 	mov	r0,r4

0000051c e3a01002   1620 	mov	r1,2

00000520 eb000000*  1621 	bl	BufferView_decodeUInt32TL

00000524 e3500000   1622 	cmp	r0,0

00000528 0a000026   1623 	beq	.L1708

                    1624 ;505: 	{


                    1625 

                    1626 ;506: 		ERROR_REPORT("Unable to decode VLAN priority");


                    1627 ;507: 		return FALSE;


                    1628 

                    1629 ;508: 	}	


                    1630 ;509: 	vlan->priority = vlanPriority;



                                                                      Page 29
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
                    1631 

0000052c e59d0018   1632 	ldr	r0,[sp,24]

00000530 e585001c   1633 	str	r0,[r5,28]

                    1634 ;510: 	vlan->useVLAN = TRUE;


                    1635 

00000534 e3a00001   1636 	mov	r0,1

00000538 e5c50014   1637 	strb	r0,[r5,20]

                    1638 ;511: 	return TRUE;


                    1639 

                    1640 .L1679:

0000053c e28d2008   1641 	add	r2,sp,8

00000540 e1a00004   1642 	mov	r0,r4

00000544 e3a01002   1643 	mov	r1,2

00000548 eb000000*  1644 	bl	BufferView_decodeUInt32TL

0000054c e3500000   1645 	cmp	r0,0

00000550 0a00001c   1646 	beq	.L1708

00000554 e59d0008   1647 	ldr	r0,[sp,8]

00000558 e28d1003   1648 	add	r1,sp,3

0000055c e1c502b0   1649 	strh	r0,[r5,32]

00000560 e1a00004   1650 	mov	r0,r4

00000564 e3a03000   1651 	mov	r3,0

00000568 e1a02003   1652 	mov	r2,r3

0000056c eb000000*  1653 	bl	BufferView_decodeTL

00000570 e3500000   1654 	cmp	r0,0

00000574 0a000013   1655 	beq	.L1708

00000578 e5dd0003   1656 	ldrb	r0,[sp,3]

0000057c e35000a1   1657 	cmp	r0,161

00000580 1a000010   1658 	bne	.L1708

00000584 e28d200c   1659 	add	r2,sp,12

00000588 e1a00004   1660 	mov	r0,r4

0000058c e3a01002   1661 	mov	r1,2

00000590 eb000000*  1662 	bl	BufferView_decodeUInt32TL

00000594 e3500000   1663 	cmp	r0,0

00000598 0a00000a   1664 	beq	.L1708

0000059c e59d000c   1665 	ldr	r0,[sp,12]

000005a0 e28d2010   1666 	add	r2,sp,16

000005a4 e5850048   1667 	str	r0,[r5,72]

000005a8 e1a00004   1668 	mov	r0,r4

000005ac e3a01002   1669 	mov	r1,2

000005b0 eb000000*  1670 	bl	BufferView_decodeUInt32TL

000005b4 e3500000   1671 	cmp	r0,0

000005b8 159d0010   1672 	ldrne	r0,[sp,16]

000005bc 1585004c   1673 	strne	r0,[r5,76]

000005c0 13a00001   1674 	movne	r0,1

000005c4 1a000000   1675 	bne	.L1658

                    1676 .L1708:

000005c8 e3a00000   1677 	mov	r0,0

                    1678 .L1658:

000005cc e28dd024   1679 	add	sp,sp,36

000005d0 e8bd8030   1680 	ldmfd	[sp]!,{r4-r5,pc}

                    1681 	.endf	GSE_init

                    1682 	.align	4

                    1683 ;tag	[sp,3]	local

                    1684 ;ifNum	[sp,4]	local

                    1685 ;appID	[sp,8]	local

                    1686 ;dest	[sp,28]	local

                    1687 ;t0	[sp,12]	local

                    1688 ;t1	[sp,16]	local

                    1689 ;vlanID	[sp,20]	local

                    1690 ;vlanPriority	[sp,24]	local

                    1691 


                                                                      Page 30
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
                    1692 ;gse	r5	param

                    1693 ;berData	r4	param

                    1694 

                    1695 	.section ".bss","awb"

                    1696 .L2096:

                    1697 	.data

                    1698 	.text

                    1699 

                    1700 

                    1701 	.align	4

                    1702 	.align	4

                    1703 writeObjectNameToBuf:

000005d4 e92d4030   1704 	stmfd	[sp]!,{r4-r5,lr}

000005d8 e1a05002   1705 	mov	r5,r2

000005dc e24dd008   1706 	sub	sp,sp,8

000005e0 e1a04001   1707 	mov	r4,r1

000005e4 e1a0100d   1708 	mov	r1,sp

000005e8 eb000000*  1709 	bl	getObjectName

000005ec e3500000   1710 	cmp	r0,0

000005f0 0a000011   1711 	beq	.L2175

000005f4 e1a0100d   1712 	mov	r1,sp

000005f8 e1a00005   1713 	mov	r0,r5

000005fc eb000000*  1714 	bl	BufferView_writeStringView

00000600 e3500000   1715 	cmp	r0,0

00000604 0a00000c   1716 	beq	.L2175

00000608 e1a00005   1717 	mov	r0,r5

0000060c e3540000   1718 	cmp	r4,0

00000610 0a000004   1719 	beq	.L2168

00000614 e1a01004   1720 	mov	r1,r4

00000618 eb000000*  1721 	bl	BufferView_writeStr

0000061c e3500000   1722 	cmp	r0,0

00000620 0a000005   1723 	beq	.L2175

00000624 ea000006   1724 	b	.L2174

                    1725 .L2168:

00000628 e59f19a0*  1726 	ldr	r1,.L2289

0000062c e3a02001   1727 	mov	r2,1

00000630 eb000000*  1728 	bl	BufferView_writeData

00000634 e3500001   1729 	cmp	r0,1

00000638 0a000001   1730 	beq	.L2174

                    1731 .L2175:

0000063c e3a00000   1732 	mov	r0,0

00000640 ea000000   1733 	b	.L2160

                    1734 .L2174:

00000644 e3a00001   1735 	mov	r0,1

                    1736 .L2160:

00000648 e28dd008   1737 	add	sp,sp,8

0000064c e8bd4030   1738 	ldmfd	[sp]!,{r4-r5,lr}

00000650 e12fff1e*  1739 	ret	

                    1740 	.endf	writeObjectNameToBuf

                    1741 	.align	4

                    1742 ;nameView	[sp]	local

                    1743 ;.L2269	.L2272	static

                    1744 

                    1745 ;pos	none	param

                    1746 ;delimiter	r4	param

                    1747 ;buf	r5	param

                    1748 

                    1749 	.section ".bss","awb"

                    1750 .L2268:

                    1751 	.section ".rodata","a"

                    1752 .L2272:;	"\000"


                                                                      Page 31
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
00000024 00        1753 	.data.b	0

                    1754 	.type	.L2272,$object

                    1755 	.size	.L2272,1

                    1756 	.data

                    1757 	.text

                    1758 

                    1759 

                    1760 	.align	4

                    1761 	.align	4

                    1762 GOOSE_resetPktCounters:

00000654 e3a03001   1763 	mov	r3,1

00000658 e280106c   1764 	add	r1,r0,108

0000065c e5900068   1765 	ldr	r0,[r0,104]

00000660 e3a02000   1766 	mov	r2,0

00000664 e1520000   1767 	cmp	r2,r0

00000668 2a000005   1768 	bhs	.L2290

                    1769 .L2294:

0000066c e581303c   1770 	str	r3,[r1,60]

00000670 e5813044   1771 	str	r3,[r1,68]

00000674 e281105c   1772 	add	r1,r1,92

00000678 e2822001   1773 	add	r2,r2,1

0000067c e1520000   1774 	cmp	r2,r0

00000680 3afffff9   1775 	blo	.L2294

                    1776 .L2290:

00000684 e12fff1e*  1777 	ret	

                    1778 	.endf	GOOSE_resetPktCounters

                    1779 	.align	4

                    1780 ;gseIdx	r2	local

                    1781 

                    1782 ;goCB	r0	param

                    1783 

                    1784 	.section ".bss","awb"

                    1785 .L2331:

                    1786 	.data

                    1787 	.text

                    1788 

                    1789 

                    1790 	.align	4

                    1791 	.align	4

                    1792 GOOSE_resetPktTimers:

00000688 e92d0010   1793 	stmfd	[sp]!,{r4}

0000068c e280106c   1794 	add	r1,r0,108

00000690 e3a02000   1795 	mov	r2,0

00000694 e1a03002   1796 	mov	r3,r2

00000698 e5904068   1797 	ldr	r4,[r0,104]

0000069c e3a0c001   1798 	mov	r12,1

000006a0 e1520004   1799 	cmp	r2,r4

000006a4 2a000007   1800 	bhs	.L2341

                    1801 .L2345:

000006a8 e5910048   1802 	ldr	r0,[r1,72]

000006ac e5a10050   1803 	str	r0,[r1,80]!

000006b0 e1a0000c   1804 	mov	r0,r12

000006b4 e9810009   1805 	stmfa	[r1],{r0,r3}

000006b8 e281100c   1806 	add	r1,r1,12

000006bc e2822001   1807 	add	r2,r2,1

000006c0 e1520004   1808 	cmp	r2,r4

000006c4 3afffff7   1809 	blo	.L2345

                    1810 .L2341:

000006c8 e8bd0010   1811 	ldmfd	[sp]!,{r4}

000006cc e12fff1e*  1812 	ret	

                    1813 	.endf	GOOSE_resetPktTimers


                                                                      Page 32
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
                    1814 	.align	4

                    1815 ;gseIdx	r2	local

                    1816 

                    1817 ;goCB	r0	param

                    1818 

                    1819 	.section ".bss","awb"

                    1820 .L2379:

                    1821 	.data

                    1822 	.text

                    1823 

                    1824 

                    1825 	.align	4

                    1826 	.align	4

                    1827 GoCB_init::

000006d0 e92d44f0   1828 	stmfd	[sp]!,{r4-r7,r10,lr}

000006d4 e1a05000   1829 	mov	r5,r0

000006d8 e2852004   1830 	add	r2,r5,4

000006dc e24dd030   1831 	sub	sp,sp,48

000006e0 e3a00001   1832 	mov	r0,1

000006e4 e5c5001c   1833 	strb	r0,[r5,28]

                    1834 ;691: {	


                    1835 

                    1836 ;692: 	uint32_t confRev;


                    1837 ;693: 	goCB->goEna = FALSE;


                    1838 

000006e8 e3a00000   1839 	mov	r0,0

000006ec e5c50000   1840 	strb	r0,[r5]

                    1841 ;694: 


                    1842 ;695: 	//AppID


                    1843 ;696: 	if (!getConstDAString(goCBPos, "AppID", &goCB->appID))


                    1844 

000006f0 e1a04001   1845 	mov	r4,r1

000006f4 e59f18d8*  1846 	ldr	r1,.L3221

000006f8 e1a00004   1847 	mov	r0,r4

000006fc eb000000*  1848 	bl	getConstDAString

00000700 e3500000   1849 	cmp	r0,0

00000704 0a0000b1   1850 	beq	.L2461

                    1851 ;697: 	{


                    1852 

                    1853 ;698: 		ERROR_REPORT("Unable to init AppID");


                    1854 ;699: 		return FALSE;


                    1855 

                    1856 ;700: 	}


                    1857 ;701: 


                    1858 ;702: 	//ConfRev


                    1859 ;703: 	if (!getConstDAULong(goCBPos, "ConfRev", &confRev))


                    1860 

00000708 e28d2004   1861 	add	r2,sp,4

0000070c e59f18c4*  1862 	ldr	r1,.L3222

00000710 e1a00004   1863 	mov	r0,r4

00000714 eb000000*  1864 	bl	getConstDAULong

00000718 e3500000   1865 	cmp	r0,0

0000071c 0a0000ab   1866 	beq	.L2461

                    1867 ;704: 	{


                    1868 

                    1869 ;705: 		ERROR_REPORT("Unable to init ConfRev");


                    1870 ;706: 		return FALSE;


                    1871 

                    1872 ;707: 	}	


                    1873 ;708: 	goCB->confRev = (uint16_t)confRev;


                    1874 


                                                                      Page 33
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
00000720 e59d0004   1875 	ldr	r0,[sp,4]

00000724 e285200c   1876 	add	r2,r5,12

00000728 e1c506b0   1877 	strh	r0,[r5,96]

                    1878 ;709: 


                    1879 ;710: 	if (!initDatSet(goCBPos, goCB))


                    1880 

                    1881 ;603: {	


                    1882 

                    1883 ;604: 	IEDEntity dataSetEntity;


                    1884 ;605: 


                    1885 ;606: 	if (!getConstDAString(goCBPos, "DatSet", &goCB->dataSetName))


                    1886 

0000072c e59f18a8*  1887 	ldr	r1,.L3223

00000730 e1a00004   1888 	mov	r0,r4

00000734 eb000000*  1889 	bl	getConstDAString

00000738 e3500000   1890 	cmp	r0,0

0000073c 0a0000a3   1891 	beq	.L2461

                    1892 ;607: 	{


                    1893 

                    1894 ;608: 		return false;


                    1895 

                    1896 ;609: 	}


                    1897 ;610: 


                    1898 ;611: 	//Получаем DataSet в IEDTree


                    1899 ;612: 	dataSetEntity = IEDTree_findDataSetBySingleName(&goCB->dataSetName);


                    1900 

00000740 e285000c   1901 	add	r0,r5,12

00000744 eb000000*  1902 	bl	IEDTree_findDataSetBySingleName

00000748 e1b01000   1903 	movs	r1,r0

                    1904 ;613: 	if(dataSetEntity == NULL)


                    1905 

0000074c 0a00009f   1906 	beq	.L2461

                    1907 ;614: 	{


                    1908 

                    1909 ;615: 		return false;


                    1910 

                    1911 ;616: 	}


                    1912 ;617: 	goCB->dataSetEntity = dataSetEntity;


                    1913 

00000750 e5851014   1914 	str	r1,[r5,20]

                    1915 ;618: 	goCB->dataSet = DataSet_getDataSetObj(dataSetEntity);


                    1916 

00000754 eb000000*  1917 	bl	DataSet_getDataSetObj

00000758 e5850018   1918 	str	r0,[r5,24]

                    1919 ;619: 	if(goCB->dataSet == NULL)


                    1920 

0000075c e3500000   1921 	cmp	r0,0

00000760 0a00009a   1922 	beq	.L2461

                    1923 ;620: 	{


                    1924 

                    1925 ;621: 		ERROR_REPORT("Invalid DataSet");


                    1926 ;622: 		return false;


                    1927 

                    1928 ;623: 	}


                    1929 ;624: 


                    1930 ;625: 	return true;


                    1931 

                    1932 ;711: 	{


                    1933 

                    1934 ;712: 		ERROR_REPORT("Unable to init DatSet");


                    1935 ;713: 		return FALSE;



                                                                      Page 34
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
                    1936 

                    1937 ;714: 	}


                    1938 ;715: 


                    1939 ;716: 	if (!initGoCBRef(goCB))


                    1940 

                    1941 ;660: {	


                    1942 

                    1943 ;661: 	BufferView refBuf;


                    1944 ;662:     BufferView_init(&refBuf, (uint8_t*)goCB->goCBRef, MAX_GO_CB_REF_SIZE, 0);


                    1945 

00000764 e285101d   1946 	add	r1,r5,29

00000768 e28d0024   1947 	add	r0,sp,36

0000076c e3a03000   1948 	mov	r3,0

00000770 e3a02042   1949 	mov	r2,66

00000774 eb000000*  1950 	bl	BufferView_init

                    1951 ;663: 


                    1952 ;664: 	if (!writeObjectNameToBuf(g_goCBPath.ldPos, "/", &refBuf))


                    1953 

00000778 e59f6860*  1954 	ldr	r6,.L3224

0000077c e59f1860*  1955 	ldr	r1,.L3225

00000780 e5960000   1956 	ldr	r0,[r6]

00000784 e28d2024   1957 	add	r2,sp,36

00000788 ebffff91*  1958 	bl	writeObjectNameToBuf

0000078c e3500000   1959 	cmp	r0,0

00000790 0a00008e   1960 	beq	.L2461

                    1961 ;665: 	{


                    1962 

                    1963 ;666: 		ERROR_REPORT("Unable to write LD name");


                    1964 ;667: 		return false;


                    1965 

                    1966 ;668: 	}


                    1967 ;669: 


                    1968 ;670: 	if (!writeObjectNameToBuf(g_goCBPath.lnPos, "$", &refBuf))


                    1969 

00000794 e28d2024   1970 	add	r2,sp,36

00000798 e59f7848*  1971 	ldr	r7,.L3226

0000079c e5960004   1972 	ldr	r0,[r6,4]

000007a0 e1a01007   1973 	mov	r1,r7

000007a4 ebffff8a*  1974 	bl	writeObjectNameToBuf

000007a8 e3500000   1975 	cmp	r0,0

000007ac 0a000087   1976 	beq	.L2461

                    1977 ;671: 	{


                    1978 

                    1979 ;672: 		ERROR_REPORT("Unable to write LN name");


                    1980 ;673: 		return false;


                    1981 

                    1982 ;674: 	}


                    1983 ;675: 


                    1984 ;676: 	if (!writeObjectNameToBuf(g_goCBPath.fcPos, "$", &refBuf))


                    1985 

000007b0 e28d2024   1986 	add	r2,sp,36

000007b4 e5960008   1987 	ldr	r0,[r6,8]

000007b8 e1a01007   1988 	mov	r1,r7

000007bc ebffff84*  1989 	bl	writeObjectNameToBuf

000007c0 e3500000   1990 	cmp	r0,0

000007c4 0a000081   1991 	beq	.L2461

                    1992 ;677: 	{


                    1993 

                    1994 ;678: 		ERROR_REPORT("Unable to write FC name");


                    1995 ;679: 		return false;


                    1996 


                                                                      Page 35
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
                    1997 ;680: 	}


                    1998 ;681: 


                    1999 ;682: 	if (!writeObjectNameToBuf(g_goCBPath.goCBPos, NULL, &refBuf))


                    2000 

000007c8 e28d2024   2001 	add	r2,sp,36

000007cc e596000c   2002 	ldr	r0,[r6,12]

000007d0 e3a01000   2003 	mov	r1,0

000007d4 ebffff7e*  2004 	bl	writeObjectNameToBuf

000007d8 e3500000   2005 	cmp	r0,0

000007dc 0a00007b   2006 	beq	.L2461

000007e0 e3a00efa   2007 	mov	r0,0x0fa0

000007e4 e5850064   2008 	str	r0,[r5,100]

                    2009 ;683: 	{


                    2010 

                    2011 ;684: 		ERROR_REPORT("Unable to write GoCB name");


                    2012 ;685: 		return false;


                    2013 

                    2014 ;686: 	}


                    2015 ;687: 	return true;


                    2016 

                    2017 ;717: 	{


                    2018 

                    2019 ;718: 		ERROR_REPORT("Unable to init GoCBRef");


                    2020 ;719: 		return FALSE;


                    2021 

                    2022 ;720: 	}


                    2023 ;721: 


                    2024 ;722: 	goCB->timeAllowedToLive = TIME_ALLOWED_TO_LIVE;


                    2025 

                    2026 ;723: 	return TRUE;


                    2027 

                    2028 ;727: {


                    2029 

                    2030 ;728: 	size_t gseIndex;


                    2031 ;729: 	BufferView gseBER;


                    2032 ;730: 	uint8_t gseListTag;


                    2033 ;731: 	size_t gseListLen;


                    2034 ;732: 	void* pGSEList;


                    2035 ;733: 


                    2036 ;734: 	//Пропускаем TL от goCB


                    2037 ;735: 	size_t currPos = readTL(goCBPos, NULL, NULL, NULL);


                    2038 

000007e8 e1a00004   2039 	mov	r0,r4

000007ec e3a03000   2040 	mov	r3,0

000007f0 e1a02003   2041 	mov	r2,r3

000007f4 e1a01003   2042 	mov	r1,r3

000007f8 eb000000*  2043 	bl	readTL

                    2044 ;736: 	if (currPos == 0)


                    2045 

000007fc e3500000   2046 	cmp	r0,0

00000800 0a000072   2047 	beq	.L2461

                    2048 ;737: 	{


                    2049 

                    2050 ;738: 		ERROR_REPORT("Unable to read GoCB");


                    2051 ;739: 		return FALSE;


                    2052 

                    2053 ;740: 	}


                    2054 ;741: 	//Пропускаем имя


                    2055 ;742: 	currPos = skipObject(currPos);


                    2056 

00000804 eb000000*  2057 	bl	skipObject


                                                                      Page 36
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
                    2058 ;743: 	if (currPos == 0)


                    2059 

00000808 e3500000   2060 	cmp	r0,0

0000080c 0a00006f   2061 	beq	.L2461

                    2062 ;744: 	{


                    2063 

                    2064 ;745: 		ERROR_REPORT("Unable to skip GoCB name");


                    2065 ;746: 		return FALSE;


                    2066 

                    2067 ;747: 	}


                    2068 ;748: 	//Получаем TL


                    2069 ;749:     currPos = readTL(currPos, &gseListTag, (int*)&gseListLen, NULL);


                    2070 

00000810 e28d2008   2071 	add	r2,sp,8

00000814 e28d1003   2072 	add	r1,sp,3

00000818 e3a03000   2073 	mov	r3,0

0000081c eb000000*  2074 	bl	readTL

00000820 e1b01000   2075 	movs	r1,r0

                    2076 ;750: 	if (currPos == 0)


                    2077 

00000824 0a000069   2078 	beq	.L2461

                    2079 ;751: 	{


                    2080 

                    2081 ;752: 		ERROR_REPORT("Unable to read GSE list");


                    2082 ;753: 		return FALSE;


                    2083 

                    2084 ;754: 	}


                    2085 ;755: 	//Проверяем что список GSE


                    2086 ;756: 	if (gseListTag != IED_GSE_LIST)


                    2087 

00000828 e5dd0003   2088 	ldrb	r0,[sp,3]

0000082c e35000f0   2089 	cmp	r0,240

00000830 1a000066   2090 	bne	.L2461

                    2091 ;757: 	{


                    2092 

                    2093 ;758: 		ERROR_REPORT("Invalid GSE list tag");


                    2094 ;759: 		return FALSE;


                    2095 

                    2096 ;760: 	}


                    2097 ;761: 	


                    2098 ;762: 	pGSEList = IEDModel_ptrFromPos(currPos);


                    2099 

00000834 e1a00001   2100 	mov	r0,r1

00000838 eb000000*  2101 	bl	IEDModel_ptrFromPos

0000083c e1b01000   2102 	movs	r1,r0

                    2103 ;763: 	if (pGSEList == NULL)


                    2104 

00000840 0a000062   2105 	beq	.L2461

00000844 e59d2008   2106 	ldr	r2,[sp,8]

00000848 e28d0018   2107 	add	r0,sp,24

0000084c e3a03000   2108 	mov	r3,0

00000850 eb000000*  2109 	bl	BufferView_init

00000854 e3a00000   2110 	mov	r0,0

00000858 e59d2020   2111 	ldr	r2,[sp,32]

0000085c e59d101c   2112 	ldr	r1,[sp,28]

00000860 e5850068   2113 	str	r0,[r5,104]

                    2114 ;764: 	{


                    2115 

                    2116 ;765: 		ERROR_REPORT("Error getting GSE list pointer");


                    2117 ;766: 		return FALSE;


                    2118 


                                                                      Page 37
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
                    2119 ;767: 	}


                    2120 ;768: 


                    2121 ;769: 	//Из длины и текущей позиции делаем BufferView


                    2122 ;770: 	BufferView_init(&gseBER, pGSEList, gseListLen, 0);


                    2123 

                    2124 ;771: 


                    2125 ;772: 	goCB->gseCount = 0;	


                    2126 

                    2127 ;773: 	for (gseIndex = 0; gseIndex < MAX_GSE_COUNT; gseIndex++)


                    2128 

00000864 e1510002   2129 	cmp	r1,r2

00000868 0a00001f   2130 	beq	.L2424

0000086c e28d1018   2131 	add	r1,sp,24

00000870 e285006c   2132 	add	r0,r5,108

00000874 ebfffee2*  2133 	bl	GSE_init

00000878 e3500000   2134 	cmp	r0,0

0000087c 0a000053   2135 	beq	.L2461

00000880 e5950068   2136 	ldr	r0,[r5,104]

00000884 e59d2020   2137 	ldr	r2,[sp,32]

00000888 e2800001   2138 	add	r0,r0,1

0000088c e59d101c   2139 	ldr	r1,[sp,28]

00000890 e5850068   2140 	str	r0,[r5,104]

00000894 e1510002   2141 	cmp	r1,r2

00000898 0a000013   2142 	beq	.L2424

0000089c e28d1018   2143 	add	r1,sp,24

000008a0 e28500c8   2144 	add	r0,r5,200

000008a4 ebfffed6*  2145 	bl	GSE_init

000008a8 e3500000   2146 	cmp	r0,0

000008ac 0a000047   2147 	beq	.L2461

000008b0 e5950068   2148 	ldr	r0,[r5,104]

000008b4 e59d2020   2149 	ldr	r2,[sp,32]

000008b8 e2800001   2150 	add	r0,r0,1

000008bc e59d101c   2151 	ldr	r1,[sp,28]

000008c0 e5850068   2152 	str	r0,[r5,104]

000008c4 e1510002   2153 	cmp	r1,r2

000008c8 0a000007   2154 	beq	.L2424

000008cc e28d1018   2155 	add	r1,sp,24

000008d0 e2850f49   2156 	add	r0,r5,0x0124

000008d4 ebfffeca*  2157 	bl	GSE_init

000008d8 e3500000   2158 	cmp	r0,0

000008dc 0a00003b   2159 	beq	.L2461

000008e0 e5950068   2160 	ldr	r0,[r5,104]

000008e4 e2800001   2161 	add	r0,r0,1

000008e8 e5850068   2162 	str	r0,[r5,104]

                    2163 .L2424:

                    2164 ;784: 	}


                    2165 ;785: 	return TRUE;


                    2166 

000008ec e3500000   2167 	cmp	r0,0

000008f0 0a000036   2168 	beq	.L2461

                    2169 ;789: {    


                    2170 

                    2171 ;790:     IEDEntity da;	


                    2172 ;791:     void* finalDAobj;


                    2173 ;792: 


                    2174 ;793: 	DataSetItem* dsItem = goCB->dataSet->firstItem;


                    2175 

000008f4 e5950018   2176 	ldr	r0,[r5,24]

000008f8 e5904000   2177 	ldr	r4,[r0]

                    2178 ;794: 	while(dsItem != NULL)


                    2179 


                                                                      Page 38
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
000008fc e3540000   2180 	cmp	r4,0

00000900 0a000012   2181 	beq	.L2464

                    2182 .L2451:

                    2183 ;795: 	{


                    2184 

                    2185 ;796: 		da = dsItem->obj;


                    2186 

00000904 e5946014   2187 	ldr	r6,[r4,20]

                    2188 ;797: 


                    2189 ;798: 		if(da->type != IED_ENTITY_DA_TERMINAL_ITEM)


                    2190 

00000908 e5960050   2191 	ldr	r0,[r6,80]

0000090c e3500008   2192 	cmp	r0,8

00000910 1a00002e   2193 	bne	.L2461

                    2194 ;799: 		{


                    2195 

                    2196 ;800: 			ERROR_REPORT("GOOSE: Unsupported object type");


                    2197 ;801: 			return false;


                    2198 

                    2199 ;802: 		}


                    2200 ;803: 		finalDAobj = FDA_create((enum InnerAttributeType)da->subType,


                    2201 

00000914 e1a00006   2202 	mov	r0,r6

00000918 eb000000*  2203 	bl	IEDTermItemDA_getTerminalItemDescr

0000091c e1a01000   2204 	mov	r1,r0

00000920 e5960054   2205 	ldr	r0,[r6,84]

00000924 eb000000*  2206 	bl	FDA_create

                    2207 ;804: 								IEDTermItemDA_getTerminalItemDescr(da));


                    2208 ;805: 		if (finalDAobj == NULL)


                    2209 

00000928 e3500000   2210 	cmp	r0,0

0000092c 0a000027   2211 	beq	.L2461

                    2212 ;806: 		{


                    2213 

                    2214 ;807: 			ERROR_REPORT("GOOSE: Unable to create the final DA object");


                    2215 ;808: 			return false;


                    2216 

                    2217 ;809: 		}


                    2218 ;810: 		goCB->daList[goCB->daCount] = finalDAobj;


                    2219 

00000930 e5951180   2220 	ldr	r1,[r5,384]

00000934 e0852101   2221 	add	r2,r5,r1 lsl 2

00000938 e5820184   2222 	str	r0,[r2,388]

                    2223 ;811: 


                    2224 ;812: 		goCB->daCount++;


                    2225 

0000093c e2810001   2226 	add	r0,r1,1

00000940 e5944000   2227 	ldr	r4,[r4]

00000944 e5850180   2228 	str	r0,[r5,384]

                    2229 ;813: 		dsItem = dsItem->next;


                    2230 

00000948 e3540000   2231 	cmp	r4,0

0000094c 1affffec   2232 	bne	.L2451

                    2233 .L2464:

                    2234 ;814: 	}


                    2235 ;815: 


                    2236 ;816: 


                    2237 ;817: 	return true;


                    2238 

                    2239 ;821: {


                    2240 


                                                                      Page 39
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
                    2241 ;822: 	size_t gseIdx;


                    2242 ;823: 	BufferView gooseBufView;


                    2243 ;824: 	//Цикл по GSE с созданием шаблонов сообщений


                    2244 ;825: 	for (gseIdx = 0; gseIdx < goCB->gseCount; ++gseIdx)


                    2245 

00000950 e285706c   2246 	add	r7,r5,108

00000954 e5950068   2247 	ldr	r0,[r5,104]

00000958 e28da00c   2248 	add	r10,sp,12

0000095c e1540000   2249 	cmp	r4,r0

00000960 2a00001c   2250 	bhs	.L2460

                    2251 .L2465:

                    2252 ;826: 	{


                    2253 

                    2254 ;827: 		//Выделяем память для пакета


                    2255 ;828: 		GSESettings gse = goCB->gse + gseIdx;


                    2256 

00000964 e1a06007   2257 	mov	r6,r7

                    2258 ;829: 		uint8_t* buf = malloc(MTU_SIZE);


                    2259 

00000968 e3a00e50   2260 	mov	r0,5<<8

0000096c e28000c8   2261 	add	r0,r0,200

00000970 eb000000*  2262 	bl	malloc

                    2263 ;830: 		if (buf == NULL)


                    2264 

00000974 e3500000   2265 	cmp	r0,0

00000978 0a000014   2266 	beq	.L2461

                    2267 ;831: 		{


                    2268 

                    2269 ;832: 			ERROR_REPORT("Unable to allocate GOOSE buffer");


                    2270 ;833: 			return FALSE;


                    2271 

                    2272 ;834: 		}


                    2273 ;835: 		gse->outPkt = buf;


                    2274 

0000097c e3a02e50   2275 	mov	r2,5<<8

00000980 e28220c8   2276 	add	r2,r2,200

00000984 e5860024   2277 	str	r0,[r6,36]

                    2278 ;836: 		BufferView_init(&gooseBufView, buf, MTU_SIZE, 0);


                    2279 

00000988 e1a01000   2280 	mov	r1,r0

0000098c e1a0000a   2281 	mov	r0,r10

00000990 e3a03000   2282 	mov	r3,0

00000994 eb000000*  2283 	bl	BufferView_init

                    2284 ;837: 		if (!prepareGOOSEbuf(goCB, gse, &gooseBufView))


                    2285 

00000998 e1a0200a   2286 	mov	r2,r10

0000099c e1a01006   2287 	mov	r1,r6

000009a0 e1a00005   2288 	mov	r0,r5

000009a4 ebfffd9d*  2289 	bl	prepareGOOSEbuf

000009a8 e3500000   2290 	cmp	r0,0

000009ac 0a000007   2291 	beq	.L2461

                    2292 ;838: 		{


                    2293 

                    2294 ;839: 			ERROR_REPORT("Unable to prepare GOOSE out buffer");


                    2295 ;840: 			return FALSE;


                    2296 

                    2297 ;841: 		}


                    2298 ;842: 		gse->outPktSize = gooseBufView.pos;


                    2299 

000009b0 e59d0010   2300 	ldr	r0,[sp,16]

000009b4 e287705c   2301 	add	r7,r7,92


                                                                      Page 40
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
000009b8 e5860028   2302 	str	r0,[r6,40]

000009bc e5950068   2303 	ldr	r0,[r5,104]

000009c0 e2844001   2304 	add	r4,r4,1

000009c4 e1540000   2305 	cmp	r4,r0

000009c8 3affffe5   2306 	blo	.L2465

000009cc ea000001   2307 	b	.L2460

                    2308 .L2461:

                    2309 ;843: 		WRITE_TO_FILE("gse.bin", gse->outPkt, gooseBufView.pos);


                    2310 ;844: 	}


                    2311 ;845: 	return TRUE;


                    2312 

000009d0 e3a00000   2313 	mov	r0,0

000009d4 ea000006   2314 	b	.L2389

                    2315 .L2460:

000009d8 e1a00005   2316 	mov	r0,r5

000009dc ebffff1c*  2317 	bl	GOOSE_resetPktCounters

000009e0 e1a00005   2318 	mov	r0,r5

000009e4 ebffff27*  2319 	bl	GOOSE_resetPktTimers

000009e8 e3a00000   2320 	mov	r0,0

000009ec e5c5001c   2321 	strb	r0,[r5,28]

000009f0 e3a00001   2322 	mov	r0,1

                    2323 .L2389:

000009f4 e28dd030   2324 	add	sp,sp,48

000009f8 e8bd84f0   2325 	ldmfd	[sp]!,{r4-r7,r10,pc}

                    2326 	.endf	GoCB_init

                    2327 	.align	4

                    2328 ;confRev	[sp,4]	local

                    2329 ;dataSetEntity	r1	local

                    2330 ;refBuf	[sp,36]	local

                    2331 ;gseBER	[sp,24]	local

                    2332 ;gseListTag	[sp,3]	local

                    2333 ;gseListLen	[sp,8]	local

                    2334 ;pGSEList	r1	local

                    2335 ;currPos	r1	local

                    2336 ;da	r6	local

                    2337 ;finalDAobj	r0	local

                    2338 ;dsItem	r4	local

                    2339 ;gseIdx	r4	local

                    2340 ;gooseBufView	[sp,12]	local

                    2341 ;gse	r6	local

                    2342 ;buf	r0	local

                    2343 

                    2344 ;goCB	r5	param

                    2345 ;goCBPos	r4	param

                    2346 

                    2347 	.data

                    2348 	.text

                    2349 

                    2350 

                    2351 	.align	4

                    2352 	.align	4

                    2353 registerGoCB:

000009fc e92d4000   2354 	stmfd	[sp]!,{lr}

                    2355 ;145: {	


                    2356 

                    2357 ;146: 	if (g_goCBCount == MAX_GOCB_COUNT)


                    2358 

00000a00 e59f25e4*  2359 	ldr	r2,.L3303

00000a04 e1a01000   2360 	mov	r1,r0

00000a08 e5920000   2361 	ldr	r0,[r2]

00000a0c e59fc5cc*  2362 	ldr	r12,.L3224


                                                                      Page 41
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
00000a10 e3500004   2363 	cmp	r0,4

                    2364 ;147: 	{


                    2365 

                    2366 ;148: 		return NULL;


                    2367 

00000a14 03a00000   2368 	moveq	r0,0

00000a18 058c100c   2369 	streq	r1,[r12,12]

00000a1c 0a000008   2370 	beq	.L3227

                    2371 ;149: 	}


                    2372 ;150: 	g_goCBCount++;


                    2373 

00000a20 e2800001   2374 	add	r0,r0,1

00000a24 e5820000   2375 	str	r0,[r2]

                    2376 ;151: 	return g_goCBs + g_goCBCount - 1;


                    2377 

00000a28 e3a02ea0   2378 	mov	r2,5<<9

00000a2c e28220e4   2379 	add	r2,r2,228

00000a30 e28c3010   2380 	add	r3,r12,16

00000a34 e0203092   2381 	mla	r0,r2,r0,r3

00000a38 e58c100c   2382 	str	r1,[r12,12]

00000a3c e0500002   2383 	subs	r0,r0,r2

00000a40 1bffff22*  2384 	blne	GoCB_init

                    2385 .L3227:

00000a44 e8bd4000   2386 	ldmfd	[sp]!,{lr}

00000a48 e12fff1e*  2387 	ret	

                    2388 	.endf	registerGoCB

                    2389 	.align	4

                    2390 ;goCB	r0	local

                    2391 

                    2392 ;goCBPos	r1	param

                    2393 

                    2394 	.data

                    2395 	.text

                    2396 

                    2397 

                    2398 	.align	4

                    2399 	.align	4

                    2400 registerGoCBsGivenFC::

00000a4c e92d4010   2401 	stmfd	[sp]!,{r4,lr}

00000a50 e59f1588*  2402 	ldr	r1,.L3224

00000a54 e24dd008   2403 	sub	sp,sp,8

00000a58 e5810008   2404 	str	r0,[r1,8]

00000a5c e1a0100d   2405 	mov	r1,sp

00000a60 e1a04000   2406 	mov	r4,r0

00000a64 eb000000*  2407 	bl	getObjectName

00000a68 e3500000   2408 	cmp	r0,0

00000a6c 0a00000a   2409 	beq	.L3304

00000a70 e59d0000   2410 	ldr	r0,[sp]

00000a74 e3500002   2411 	cmp	r0,2

00000a78 1a000007   2412 	bne	.L3304

00000a7c e59d1004   2413 	ldr	r1,[sp,4]

00000a80 e59f0568*  2414 	ldr	r0,.L3384

00000a84 e3a02002   2415 	mov	r2,2

00000a88 eb000000*  2416 	bl	memcmp

00000a8c e3500000   2417 	cmp	r0,0

00000a90 059f155c*  2418 	ldreq	r1,.L3385

00000a94 01a00004   2419 	moveq	r0,r4

00000a98 0b000000*  2420 	bleq	processSubobjects

                    2421 .L3304:

00000a9c e28dd008   2422 	add	sp,sp,8

00000aa0 e8bd8010   2423 	ldmfd	[sp]!,{r4,pc}


                                                                      Page 42
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
                    2424 	.endf	registerGoCBsGivenFC

                    2425 	.align	4

                    2426 ;fcName	[sp]	local

                    2427 ;.L3366	.L3369	static

                    2428 

                    2429 ;fcPos	r4	param

                    2430 

                    2431 	.section ".rodata","a"

                    2432 .L3369:;	"GO\000"

00000025 4f47      2433 	.data.b	71,79

00000027 00        2434 	.data.b	0

                    2435 	.type	.L3369,$object

                    2436 	.size	.L3369,3

                    2437 	.data

                    2438 	.text

                    2439 

                    2440 

                    2441 	.align	4

                    2442 	.align	4

                    2443 registerAllLogicalNodeGoCB:

00000aa4 e92d4000   2444 	stmfd	[sp]!,{lr}

00000aa8 e59f2530*  2445 	ldr	r2,.L3224

00000aac e59f1544*  2446 	ldr	r1,.L3412

00000ab0 e5820004   2447 	str	r0,[r2,4]

00000ab4 eb000000*  2448 	bl	processSubobjects

00000ab8 e8bd4000   2449 	ldmfd	[sp]!,{lr}

00000abc e12fff1e*  2450 	ret	

                    2451 	.endf	registerAllLogicalNodeGoCB

                    2452 	.align	4

                    2453 

                    2454 ;lnPos	none	param

                    2455 

                    2456 	.data

                    2457 	.text

                    2458 

                    2459 

                    2460 	.align	4

                    2461 	.align	4

                    2462 registerAllLogicalDeviceGoCB:

00000ac0 e92d4000   2463 	stmfd	[sp]!,{lr}

00000ac4 e59f1514*  2464 	ldr	r1,.L3224

00000ac8 e5810000   2465 	str	r0,[r1]

00000acc e3a010ec   2466 	mov	r1,236

00000ad0 eb000000*  2467 	bl	findObjectByTag

00000ad4 e3500000   2468 	cmp	r0,0

00000ad8 159f151c*  2469 	ldrne	r1,.L3458

00000adc 1b000000*  2470 	blne	processSubobjects

00000ae0 e8bd4000   2471 	ldmfd	[sp]!,{lr}

00000ae4 e12fff1e*  2472 	ret	

                    2473 	.endf	registerAllLogicalDeviceGoCB

                    2474 	.align	4

                    2475 ;dataSectionPos	r1	local

                    2476 

                    2477 ;ldPos	none	param

                    2478 

                    2479 	.data

                    2480 	.text

                    2481 

                    2482 

                    2483 	.align	4

                    2484 	.align	4


                                                                      Page 43
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
                    2485 GOOSE_send::

00000ae8 e92d4010   2486 	stmfd	[sp]!,{r4,lr}

00000aec e2804040   2487 	add	r4,r0,64

                    2488 ;872: {


                    2489 

                    2490 ;873: 	writeUlongBE(gse->stNum.p, gse->stNum.value & 0x7FFFFFFF);


                    2491 

00000af0 e8940009   2492 	ldmfd	[r4],{r0,r3}

00000af4 e3c31480   2493 	bic	r1,r3,1<<31

00000af8 ebfffd40*  2494 	bl	writeUlongBE

                    2495 ;874: 	writeUlongBE(gse->sqNum.p, gse->sqNum.value & 0x7FFFFFFF);


                    2496 

00000afc e9140009   2497 	ldmea	[r4],{r0,r3}

00000b00 e3c31480   2498 	bic	r1,r3,1<<31

00000b04 ebfffd3d*  2499 	bl	writeUlongBE

00000b08 e5140004   2500 	ldr	r0,[r4,-4]

00000b0c e5142018   2501 	ldr	r2,[r4,-24]

00000b10 e2800001   2502 	add	r0,r0,1

00000b14 e5040004   2503 	str	r0,[r4,-4]

00000b18 e514101c   2504 	ldr	r1,[r4,-28]

00000b1c e514003c   2505 	ldr	r0,[r4,-60]

00000b20 e8bd4010   2506 	ldmfd	[sp]!,{r4,lr}

00000b24 ea000000*  2507 	b	NetTools_send

                    2508 	.endf	GOOSE_send

                    2509 	.align	4

                    2510 

                    2511 ;gse	r4	param

                    2512 

                    2513 	.section ".bss","awb"

                    2514 .L3489:

                    2515 	.data

                    2516 	.text

                    2517 

                    2518 

                    2519 	.align	4

                    2520 	.align	4

                    2521 GOOSE_sendChanges::

00000b28 e92d4ff0   2522 	stmfd	[sp]!,{r4-fp,lr}

00000b2c e24dd014   2523 	sub	sp,sp,20

00000b30 eb000000*  2524 	bl	PTools_lockInterrupt

00000b34 e1a0a000   2525 	mov	r10,r0

00000b38 eb000000*  2526 	bl	DataSlice_getDataSliceWnd

00000b3c e3a08000   2527 	mov	r8,0

00000b40 e59f14a4*  2528 	ldr	r1,.L3303

00000b44 e1a09000   2529 	mov	r9,r0

00000b48 e5910000   2530 	ldr	r0,[r1]

00000b4c e1a01008   2531 	mov	r1,r8

00000b50 e1510000   2532 	cmp	r1,r0

00000b54 2a000054   2533 	bhs	.L3498

                    2534 .L3500:

00000b58 e1a03001   2535 	mov	r3,r1

00000b5c e3a01ea0   2536 	mov	r1,5<<9

00000b60 e59f2498*  2537 	ldr	r2,.L3836

00000b64 e28110e4   2538 	add	r1,r1,228

00000b68 e0252193   2539 	mla	r5,r3,r1,r2

00000b6c e5d51000   2540 	ldrb	r1,[r5]

00000b70 e3510000   2541 	cmp	r1,0

00000b74 0a000048   2542 	beq	.L3499

00000b78 e5d5401c   2543 	ldrb	r4,[r5,28]

00000b7c e3540000   2544 	cmp	r4,0

00000b80 1a000045   2545 	bne	.L3499


                                                                      Page 44
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
                    2546 ;1137: {


                    2547 

00000b84 e3a06000   2548 	mov	r6,0

00000b88 e5cd6007   2549 	strb	r6,[sp,7]

                    2550 ;1138: 	size_t daIdx;


                    2551 ;1139: 	//Есть изменения в каком-нибудь DA


                    2552 ;1140: 	bool changed = false;


                    2553 

                    2554 ;1141: 	//Конкретный DA изменился


                    2555 ;1142: 	bool daChanged = false;


                    2556 

                    2557 ;1143: 	


                    2558 ;1144: 	//Обязательно нужно обойти все DA, даже если изменения обнаружены


                    2559 ;1145: 	//раньше, чтобы считать все данные для передачи.


                    2560 ;1146: 	for (daIdx = 0; daIdx < goCB->daCount; ++daIdx)


                    2561 

00000b8c e5950180   2562 	ldr	r0,[r5,384]

00000b90 e2857f61   2563 	add	r7,r5,0x0184

00000b94 e1560000   2564 	cmp	r6,r0

00000b98 2a00000d   2565 	bhs	.L3507

                    2566 .L3510:

                    2567 ;1147: 	{


                    2568 

                    2569 ;1148:         FDA_readAndCompare(goCB->daList[daIdx], dataSliceWnd, &daChanged);


                    2570 

00000b9c e28d2007   2571 	add	r2,sp,7

00000ba0 e4970004   2572 	ldr	r0,[r7],4

00000ba4 e1a01009   2573 	mov	r1,r9

00000ba8 eb000000*  2574 	bl	FDA_readAndCompare

                    2575 ;1149: 		changed = (changed || daChanged);


                    2576 

00000bac e3540000   2577 	cmp	r4,0

00000bb0 05dd1007   2578 	ldreqb	r1,[sp,7]

00000bb4 e3a00001   2579 	mov	r0,1

00000bb8 03510000   2580 	cmpeq	r1,0

00000bbc 01a00001   2581 	moveq	r0,r1

00000bc0 e20040ff   2582 	and	r4,r0,255

00000bc4 e5950180   2583 	ldr	r0,[r5,384]

00000bc8 e2866001   2584 	add	r6,r6,1

00000bcc e1560000   2585 	cmp	r6,r0

00000bd0 3afffff1   2586 	blo	.L3510

                    2587 .L3507:

                    2588 ;1150: 	}


                    2589 ;1151: 	return changed;


                    2590 

00000bd4 e3540000   2591 	cmp	r4,0

00000bd8 0a00002d   2592 	beq	.L3670

00000bdc e285406c   2593 	add	r4,r5,108

                    2594 ;156: {


                    2595 

                    2596 ;157: 	uint64_t timeStamp = dataSliceGetTimeStamp();	


                    2597 

00000be0 e3a06000   2598 	mov	r6,0

00000be4 eb000000*  2599 	bl	dataSliceGetTimeStamp

                    2600 ;158: 	MMSData_encodeTimeStamp(0x84, timeStamp, gse->pPktTime, 0);


                    2601 

00000be8 e58d6000   2602 	str	r6,[sp]

00000bec e59530a0   2603 	ldr	r3,[r5,160]

00000bf0 e1a02001   2604 	mov	r2,r1

00000bf4 e1a01000   2605 	mov	r1,r0

00000bf8 e3a00084   2606 	mov	r0,132


                                                                      Page 45
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
00000bfc eb000000*  2607 	bl	MMSData_encodeTimeStamp

                    2608 ;1155: {


                    2609 

                    2610 ;1156: 	size_t daIdx;


                    2611 ;1157: 		


                    2612 ;1158: 	for (daIdx = 0; daIdx < goCB->daCount; ++daIdx)


                    2613 

00000c00 e5950180   2614 	ldr	r0,[r5,384]

00000c04 e2857f61   2615 	add	r7,r5,0x0184

00000c08 e1560000   2616 	cmp	r6,r0

00000c0c 2a000005   2617 	bhs	.L3521

                    2618 .L3524:

                    2619 ;1159: 	{


                    2620 

                    2621 ;1160: 		FDA_encodeFixedData(goCB->daList[daIdx]);


                    2622 

00000c10 e4970004   2623 	ldr	r0,[r7],4

00000c14 eb000000*  2624 	bl	FDA_encodeFixedData

00000c18 e5950180   2625 	ldr	r0,[r5,384]

00000c1c e2866001   2626 	add	r6,r6,1

00000c20 e1560000   2627 	cmp	r6,r0

00000c24 3afffff9   2628 	blo	.L3524

                    2629 .L3521:

00000c28 e285706c   2630 	add	r7,r5,108

00000c2c e5940044   2631 	ldr	r0,[r4,68]

00000c30 e3a0b004   2632 	mov	fp,4

00000c34 e2800001   2633 	add	r0,r0,1

00000c38 e5840044   2634 	str	r0,[r4,68]

00000c3c e3a00001   2635 	mov	r0,1

00000c40 e584003c   2636 	str	r0,[r4,60]

00000c44 e5950068   2637 	ldr	r0,[r5,104]

00000c48 e3a06000   2638 	mov	r6,0

00000c4c e1560000   2639 	cmp	r6,r0

00000c50 2a00000f   2640 	bhs	.L3670

                    2641 .L3529:

00000c54 e3560000   2642 	cmp	r6,0

                    2643 ;1166: {


                    2644 

                    2645 ;1167: 	memcpy(gseSrc->pPDU, gseDst->pPDU, gseSrc->pduSize);


                    2646 

00000c58 e1a04007   2647 	mov	r4,r7

00000c5c 15950098   2648 	ldrne	r0,[r5,152]

00000c60 1594102c   2649 	ldrne	r1,[r4,44]

00000c64 1595209c   2650 	ldrne	r2,[r5,156]

00000c68 1b000000*  2651 	blne	memcpy

00000c6c e5b4004c   2652 	ldr	r0,[r4,76]!

00000c70 e3a0c000   2653 	mov	r12,0

00000c74 e9841801   2654 	stmfa	[r4],{r0,fp-r12}

00000c78 e287705c   2655 	add	r7,r7,92

00000c7c e244004c   2656 	sub	r0,r4,76

00000c80 ebffff98*  2657 	bl	GOOSE_send

00000c84 e5950068   2658 	ldr	r0,[r5,104]

00000c88 e2866001   2659 	add	r6,r6,1

00000c8c e1560000   2660 	cmp	r6,r0

00000c90 3affffef   2661 	blo	.L3529

                    2662 .L3670:

00000c94 e59f1350*  2663 	ldr	r1,.L3303

00000c98 e5910000   2664 	ldr	r0,[r1]

                    2665 .L3499:

00000c9c e2888001   2666 	add	r8,r8,1

00000ca0 e1a01008   2667 	mov	r1,r8


                                                                      Page 46
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
00000ca4 e1510000   2668 	cmp	r1,r0

00000ca8 3affffaa   2669 	blo	.L3500

                    2670 .L3498:

00000cac e1a0000a   2671 	mov	r0,r10

00000cb0 eb000000*  2672 	bl	PTools_unlockInterrupt

00000cb4 e28dd014   2673 	add	sp,sp,20

00000cb8 e8bd8ff0   2674 	ldmfd	[sp]!,{r4-fp,pc}

                    2675 	.endf	GOOSE_sendChanges

                    2676 	.align	4

                    2677 ;gse	r4	local

                    2678 ;goCBIdx	r8	local

                    2679 ;gseIdx	r6	local

                    2680 ;dataSliceWnd	r9	local

                    2681 ;interruptState	r10	local

                    2682 ;goCB	r5	local

                    2683 ;daIdx	r6	local

                    2684 ;changed	r4	local

                    2685 ;daChanged	[sp,7]	local

                    2686 ;daIdx	r6	local

                    2687 

                    2688 	.data

                    2689 	.text

                    2690 

                    2691 

                    2692 ;1237: 


                    2693 ;1238: static void timerProc(void)


                    2694 	.align	4

                    2695 	.align	4

                    2696 timerProc:

00000cbc e92d4cf0   2697 	stmfd	[sp]!,{r4-r7,r10-fp,lr}

                    2698 ;1239: {


                    2699 

                    2700 ;1240: 	size_t goCBIdx;


                    2701 ;1241: 	size_t gseIdx;


                    2702 ;1242:     int interruptState = PTools_lockInterrupt();


                    2703 

00000cc0 eb000000*  2704 	bl	PTools_lockInterrupt

00000cc4 e59f1320*  2705 	ldr	r1,.L3303

00000cc8 e1a0b000   2706 	mov	fp,r0

                    2707 ;1243: 	for (goCBIdx = 0; goCBIdx < g_goCBCount; ++goCBIdx)


                    2708 

00000ccc e5910000   2709 	ldr	r0,[r1]

00000cd0 e3a0a000   2710 	mov	r10,0

00000cd4 e15a0000   2711 	cmp	r10,r0

00000cd8 2a000038   2712 	bhs	.L3839

                    2713 .L3841:

                    2714 ;1244: 	{


                    2715 

                    2716 ;1245: 		GoCB goCB = g_goCBs + goCBIdx;


                    2717 

00000cdc e3a01ea0   2718 	mov	r1,5<<9

00000ce0 e59f2318*  2719 	ldr	r2,.L3836

00000ce4 e28110e4   2720 	add	r1,r1,228

00000ce8 e026219a   2721 	mla	r6,r10,r1,r2

                    2722 ;1246: 


                    2723 ;1247: 		if (goCB->goEna && !goCB->ndsCom)


                    2724 

00000cec e5d61000   2725 	ldrb	r1,[r6]

00000cf0 e3510000   2726 	cmp	r1,0

00000cf4 0a00002e   2727 	beq	.L3840

00000cf8 e5d6401c   2728 	ldrb	r4,[r6,28]


                                                                      Page 47
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
00000cfc e3540000   2729 	cmp	r4,0

00000d00 1a00002b   2730 	bne	.L3840

                    2731 ;1248: 		{		


                    2732 

                    2733 ;1249: 			for (gseIdx = 0; gseIdx < goCB->gseCount; ++gseIdx)


                    2734 

00000d04 e286506c   2735 	add	r5,r6,108

00000d08 e5960068   2736 	ldr	r0,[r6,104]

00000d0c e3a07000   2737 	mov	r7,0

00000d10 e1540000   2738 	cmp	r4,r0

00000d14 2a000024   2739 	bhs	.L3960

                    2740 .L3852:

                    2741 ;1250: 			{


                    2742 

                    2743 ;1251: 				processGSETimer(goCB->gse + gseIdx);


                    2744 

                    2745 ;1214: {	    


                    2746 

                    2747 ;1215: 	if (gse->msCounter < gse->currT)


                    2748 

00000d18 e5951050   2749 	ldr	r1,[r5,80]

00000d1c e5950058   2750 	ldr	r0,[r5,88]

00000d20 e1500001   2751 	cmp	r0,r1

00000d24 2a000007   2752 	bhs	.L3854

                    2753 ;1216: 	{


                    2754 

                    2755 ;1217: 		gse->msCounter++;


                    2756 

00000d28 e2800001   2757 	add	r0,r0,1

00000d2c e5850058   2758 	str	r0,[r5,88]

00000d30 e285505c   2759 	add	r5,r5,92

00000d34 e5960068   2760 	ldr	r0,[r6,104]

00000d38 e2844001   2761 	add	r4,r4,1

00000d3c e1540000   2762 	cmp	r4,r0

00000d40 3afffff4   2763 	blo	.L3852

00000d44 ea000018   2764 	b	.L3960

                    2765 .L3854:

                    2766 ;1218: 	}


                    2767 ;1219: 	else


                    2768 ;1220: 	{


                    2769 

                    2770 ;1221: 		GOOSE_send(gse);                        


                    2771 

00000d48 e1a00005   2772 	mov	r0,r5

00000d4c ebffff65*  2773 	bl	GOOSE_send

                    2774 ;1222: 		gse->msCounter = 0;


                    2775 

00000d50 e5950054   2776 	ldr	r0,[r5,84]

00000d54 e5857058   2777 	str	r7,[r5,88]

                    2778 ;1223:         if(gse->t1Counter > 1)


                    2779 

00000d58 e3500001   2780 	cmp	r0,1

00000d5c 9a000007   2781 	bls	.L3856

                    2782 ;1224:         {


                    2783 

                    2784 ;1225:             gse->t1Counter--;


                    2785 

00000d60 e2400001   2786 	sub	r0,r0,1

00000d64 e5850054   2787 	str	r0,[r5,84]

00000d68 e285505c   2788 	add	r5,r5,92

00000d6c e5960068   2789 	ldr	r0,[r6,104]


                                                                      Page 48
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
00000d70 e2844001   2790 	add	r4,r4,1

00000d74 e1540000   2791 	cmp	r4,r0

00000d78 3affffe6   2792 	blo	.L3852

00000d7c ea00000a   2793 	b	.L3960

                    2794 .L3856:

                    2795 ;1226:         }


                    2796 ;1227:         else


                    2797 ;1228:         {


                    2798 

                    2799 ;1229:             gse->currT *= 2;


                    2800 

00000d80 e5950050   2801 	ldr	r0,[r5,80]

00000d84 e5951048   2802 	ldr	r1,[r5,72]

00000d88 e1a00080   2803 	mov	r0,r0 lsl 1

00000d8c e5850050   2804 	str	r0,[r5,80]

                    2805 ;1230:             if (gse->currT > gse->t0)


                    2806 

00000d90 e1500001   2807 	cmp	r0,r1

                    2808 ;1231:             {


                    2809 

                    2810 ;1232:                 gse->currT = gse->t0;


                    2811 

00000d94 85851050   2812 	strhi	r1,[r5,80]

00000d98 e285505c   2813 	add	r5,r5,92

00000d9c e5960068   2814 	ldr	r0,[r6,104]

00000da0 e2844001   2815 	add	r4,r4,1

00000da4 e1540000   2816 	cmp	r4,r0

00000da8 3affffda   2817 	blo	.L3852

                    2818 .L3960:

00000dac e59f1238*  2819 	ldr	r1,.L3303

00000db0 e5910000   2820 	ldr	r0,[r1]

                    2821 .L3840:

00000db4 e28aa001   2822 	add	r10,r10,1

00000db8 e15a0000   2823 	cmp	r10,r0

00000dbc 3affffc6   2824 	blo	.L3841

                    2825 .L3839:

                    2826 ;1252: 			}


                    2827 ;1253: 		}


                    2828 ;1254: 	}


                    2829 ;1255:     PTools_unlockInterrupt(interruptState);


                    2830 

00000dc0 e1a0000b   2831 	mov	r0,fp

00000dc4 eb000000*  2832 	bl	PTools_unlockInterrupt

00000dc8 e8bd4cf0   2833 	ldmfd	[sp]!,{r4-r7,r10-fp,lr}

00000dcc e12fff1e*  2834 	ret	

                    2835 	.endf	timerProc

                    2836 	.align	4

                    2837 ;goCBIdx	r10	local

                    2838 ;gseIdx	r4	local

                    2839 ;interruptState	fp	local

                    2840 ;goCB	r6	local

                    2841 

                    2842 	.data

                    2843 	.text

                    2844 

                    2845 ;1256: }


                    2846 

                    2847 ;1257: 


                    2848 ;1258: bool GOOSE_getGoEna(size_t cbIndex, bool* goEna)


                    2849 	.align	4

                    2850 	.align	4


                                                                      Page 49
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
                    2851 GOOSE_getGoEna::

                    2852 ;1259: {


                    2853 

                    2854 ;1260: 	if (cbIndex >= g_goCBCount)


                    2855 

00000dd0 e59f3214*  2856 	ldr	r3,.L3303

00000dd4 e5932000   2857 	ldr	r2,[r3]

00000dd8 e1500002   2858 	cmp	r0,r2

                    2859 ;1261: 	{


                    2860 

                    2861 ;1262: 		ERROR_REPORT("GoCB index is too big");


                    2862 ;1263: 		return FALSE;


                    2863 

00000ddc 23a00000   2864 	movhs	r0,0

00000de0 2a000006   2865 	bhs	.L4085

                    2866 ;1264: 	}


                    2867 ;1265: 	*goEna = g_goCBs[cbIndex].goEna;


                    2868 

00000de4 e3a02ea0   2869 	mov	r2,5<<9

00000de8 e28220e4   2870 	add	r2,r2,228

00000dec e0000092   2871 	mul	r0,r2,r0

00000df0 e59f2208*  2872 	ldr	r2,.L3836

00000df4 e7d00002   2873 	ldrb	r0,[r0,r2]

00000df8 e5c10000   2874 	strb	r0,[r1]

                    2875 ;1266: 	return TRUE;


                    2876 

00000dfc e3a00001   2877 	mov	r0,1

                    2878 .L4085:

00000e00 e12fff1e*  2879 	ret	

                    2880 	.endf	GOOSE_getGoEna

                    2881 	.align	4

                    2882 

                    2883 ;cbIndex	r0	param

                    2884 ;goEna	r1	param

                    2885 

                    2886 	.data

                    2887 	.text

                    2888 

                    2889 ;1267: }


                    2890 

                    2891 ;1268: 


                    2892 ;1269: bool GOOSE_getNdsCom(size_t cbIndex, bool* ndsCom)


                    2893 	.align	4

                    2894 	.align	4

                    2895 GOOSE_getNdsCom::

                    2896 ;1270: {


                    2897 

                    2898 ;1271: 	if (cbIndex >= g_goCBCount)


                    2899 

00000e04 e59f31e0*  2900 	ldr	r3,.L3303

00000e08 e5932000   2901 	ldr	r2,[r3]

00000e0c e1500002   2902 	cmp	r0,r2

                    2903 ;1272: 	{


                    2904 

                    2905 ;1273: 		ERROR_REPORT("GoCB index is too big");


                    2906 ;1274: 		return FALSE;


                    2907 

00000e10 23a00000   2908 	movhs	r0,0

00000e14 2a000006   2909 	bhs	.L4136

                    2910 ;1275: 	}


                    2911 ;1276: 	*ndsCom = g_goCBs[cbIndex].ndsCom;



                                                                      Page 50
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
                    2912 

00000e18 e3a03ea0   2913 	mov	r3,5<<9

00000e1c e59f21dc*  2914 	ldr	r2,.L3836

00000e20 e28330e4   2915 	add	r3,r3,228

00000e24 e0202093   2916 	mla	r0,r3,r0,r2

00000e28 e5d0001c   2917 	ldrb	r0,[r0,28]

00000e2c e5c10000   2918 	strb	r0,[r1]

                    2919 ;1277: 	return TRUE;


                    2920 

00000e30 e3a00001   2921 	mov	r0,1

                    2922 .L4136:

00000e34 e12fff1e*  2923 	ret	

                    2924 	.endf	GOOSE_getNdsCom

                    2925 	.align	4

                    2926 

                    2927 ;cbIndex	r0	param

                    2928 ;ndsCom	r1	param

                    2929 

                    2930 	.data

                    2931 	.text

                    2932 

                    2933 ;1278: }


                    2934 

                    2935 ;1279: 


                    2936 ;1280: bool GOOSE_setGoEna(size_t cbIndex, bool value)


                    2937 	.align	4

                    2938 	.align	4

                    2939 GOOSE_setGoEna::

00000e38 e92d40f0   2940 	stmfd	[sp]!,{r4-r7,lr}

                    2941 ;1281: {


                    2942 

                    2943 ;1282: 	GoCB goCB;


                    2944 ;1283: 	if (cbIndex >= g_goCBCount)


                    2945 

00000e3c e59f21a8*  2946 	ldr	r2,.L3303

00000e40 e1a04001   2947 	mov	r4,r1

00000e44 e5921000   2948 	ldr	r1,[r2]

00000e48 e1500001   2949 	cmp	r0,r1

00000e4c 2a000007   2950 	bhs	.L4185

                    2951 ;1284: 	{


                    2952 

                    2953 ;1285: 		ERROR_REPORT("GoCB index is too big");


                    2954 ;1286: 		return FALSE;


                    2955 

                    2956 ;1287: 	}


                    2957 ;1288: 	goCB = g_goCBs + cbIndex;


                    2958 

00000e50 e3a01ea0   2959 	mov	r1,5<<9

00000e54 e28110e4   2960 	add	r1,r1,228

00000e58 e0060190   2961 	mul	r6,r0,r1

00000e5c e59f719c*  2962 	ldr	r7,.L3836

00000e60 e0865007   2963 	add	r5,r6,r7

                    2964 ;1289: 


                    2965 ;1290: 	if (goCB->ndsCom)


                    2966 

00000e64 e5d5001c   2967 	ldrb	r0,[r5,28]

00000e68 e3500000   2968 	cmp	r0,0

00000e6c 0a000001   2969 	beq	.L4184

                    2970 .L4185:

                    2971 ;1291: 	{


                    2972 


                                                                      Page 51
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
                    2973 ;1292: 		return false;


                    2974 

00000e70 e3a00000   2975 	mov	r0,0

00000e74 ea00000a   2976 	b	.L4179

                    2977 .L4184:

                    2978 ;1293: 	}


                    2979 ;1294: 


                    2980 ;1295: 	if (!goCB->goEna && value)


                    2981 

00000e78 e5d50000   2982 	ldrb	r0,[r5]

00000e7c e3500000   2983 	cmp	r0,0

00000e80 1a000005   2984 	bne	.L4187

00000e84 e3540000   2985 	cmp	r4,0

00000e88 0a000003   2986 	beq	.L4187

                    2987 ;1296: 	{


                    2988 

                    2989 ;1297: 		//Если включаем


                    2990 ;1298:         GOOSE_resetPktCounters(goCB);


                    2991 

00000e8c e1a00005   2992 	mov	r0,r5

00000e90 ebfffdef*  2993 	bl	GOOSE_resetPktCounters

                    2994 ;1299: 		GOOSE_resetPktTimers(goCB);


                    2995 

00000e94 e1a00005   2996 	mov	r0,r5

00000e98 ebfffdfa*  2997 	bl	GOOSE_resetPktTimers

                    2998 .L4187:

                    2999 ;1300: 	}


                    3000 ;1301: 	g_goCBs[cbIndex].goEna = value;


                    3001 

00000e9c e7c74006   3002 	strb	r4,[r7,r6]

                    3003 ;1302: 	return TRUE;


                    3004 

00000ea0 e3a00001   3005 	mov	r0,1

                    3006 .L4179:

00000ea4 e8bd80f0   3007 	ldmfd	[sp]!,{r4-r7,pc}

                    3008 	.endf	GOOSE_setGoEna

                    3009 	.align	4

                    3010 ;goCB	r5	local

                    3011 

                    3012 ;cbIndex	r0	param

                    3013 ;value	r4	param

                    3014 

                    3015 	.data

                    3016 	.text

                    3017 

                    3018 ;1303: }


                    3019 

                    3020 ;1304: 


                    3021 ;1305: void GOOSE_init(void)


                    3022 	.align	4

                    3023 	.align	4

                    3024 GOOSE_init::

00000ea8 e92d44f0   3025 	stmfd	[sp]!,{r4-r7,r10,lr}

00000eac e59f414c*  3026 	ldr	r4,.L3836

00000eb0 e59f114c*  3027 	ldr	r1,.L4707

                    3028 ;1306: {


                    3029 

                    3030 ;1307:     size_t i;


                    3031 ;1308:     registerAllGoCB();    	            


                    3032 

                    3033 ;1125: {



                                                                      Page 52
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
                    3034 

                    3035 ;1126: 	processSubobjects(0, registerAllLogicalDeviceGoCB);


                    3036 

00000eb4 e3a00000   3037 	mov	r0,0

00000eb8 eb000000*  3038 	bl	processSubobjects

                    3039 ;1309:     DataSlice_setCallBack(GOOSE_sendChanges);


                    3040 

00000ebc e59f0144*  3041 	ldr	r0,.L4708

00000ec0 eb000000*  3042 	bl	DataSlice_setCallBack

                    3043 ;1310:     Timers_setGoose1msCallBack(timerProc);


                    3044 

00000ec4 e59f0140*  3045 	ldr	r0,.L4709

00000ec8 eb000000*  3046 	bl	Timers_setGoose1msCallBack

                    3047 ;1311: 


                    3048 ;1312:     //Включаем все GoCB


                    3049 ;1313:     for(i = 0; i < g_goCBCount; i++)


                    3050 

00000ecc e59f1118*  3051 	ldr	r1,.L3303

00000ed0 e5910000   3052 	ldr	r0,[r1]

00000ed4 e3a06000   3053 	mov	r6,0

00000ed8 e3500000   3054 	cmp	r0,0

00000edc a1a07000   3055 	movge	r7,r0

00000ee0 b3a07000   3056 	movlt	r7,0

00000ee4 e1b031a7   3057 	movs	r3,r7 lsr 3

00000ee8 0a000029   3058 	beq	.L4320

00000eec e2840c41   3059 	add	r0,r4,65<<8

00000ef0 e2800058   3060 	add	r0,r0,88

00000ef4 e2841d54   3061 	add	r1,r4,21<<8

00000ef8 e28110c8   3062 	add	r1,r1,200

00000efc e2842dd8   3063 	add	r2,r4,54<<8

00000f00 e282c074   3064 	add	r12,r2,116

00000f04 e2842ea0   3065 	add	r2,r4,5<<9

00000f08 e28250e4   3066 	add	r5,r2,228

00000f0c e3a02001   3067 	mov	r2,1

00000f10 e1a06183   3068 	mov	r6,r3 lsl 3

                    3069 .L4321:

00000f14 e555aac8   3070 	ldrb	r10,[r5,-2760]

00000f18 e35a0000   3071 	cmp	r10,0

00000f1c 05452ae4   3072 	streqb	r2,[r5,-2788]

00000f20 e551aac8   3073 	ldrb	r10,[r1,-2760]

00000f24 e35a0000   3074 	cmp	r10,0

00000f28 e5d1a01c   3075 	ldrb	r10,[r1,28]

00000f2c 05412ae4   3076 	streqb	r2,[r1,-2788]

00000f30 e35a0000   3077 	cmp	r10,0

00000f34 e5d1ab00   3078 	ldrb	r10,[r1,2816]

00000f38 05c12000   3079 	streqb	r2,[r1]

00000f3c e35a0000   3080 	cmp	r10,0

00000f40 05c12ae4   3081 	streqb	r2,[r1,2788]

00000f44 e55caac8   3082 	ldrb	r10,[r12,-2760]

00000f48 e35a0000   3083 	cmp	r10,0

00000f4c 054c2ae4   3084 	streqb	r2,[r12,-2788]

00000f50 e550aac8   3085 	ldrb	r10,[r0,-2760]

00000f54 e35a0000   3086 	cmp	r10,0

00000f58 e5d0a01c   3087 	ldrb	r10,[r0,28]

00000f5c 05402ae4   3088 	streqb	r2,[r0,-2788]

00000f60 e35a0000   3089 	cmp	r10,0

00000f64 e5d0ab00   3090 	ldrb	r10,[r0,2816]

00000f68 05c02000   3091 	streqb	r2,[r0]

00000f6c e35a0000   3092 	cmp	r10,0

00000f70 e3a0ac57   3093 	mov	r10,87<<8

00000f74 e28aa020   3094 	add	r10,r10,32


                                                                      Page 53
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
00000f78 05c02ae4   3095 	streqb	r2,[r0,2788]

00000f7c e080000a   3096 	add	r0,r0,r10

00000f80 e081100a   3097 	add	r1,r1,r10

00000f84 e08cc00a   3098 	add	r12,r12,r10

00000f88 e085500a   3099 	add	r5,r5,r10

00000f8c e2533001   3100 	subs	r3,r3,1

00000f90 1affffdf   3101 	bne	.L4321

                    3102 .L4320:

00000f94 e2173007   3103 	ands	r3,r7,7

00000f98 0a00000a   3104 	beq	.L4285

00000f9c e3a00ea0   3105 	mov	r0,5<<9

00000fa0 e28000e4   3106 	add	r0,r0,228

00000fa4 e0204096   3107 	mla	r0,r6,r0,r4

00000fa8 e3a02001   3108 	mov	r2,1

                    3109 .L4355:

00000fac e5d0101c   3110 	ldrb	r1,[r0,28]

00000fb0 e3510000   3111 	cmp	r1,0

00000fb4 e2801ea0   3112 	add	r1,r0,5<<9

00000fb8 05c02000   3113 	streqb	r2,[r0]

00000fbc e28100e4   3114 	add	r0,r1,228

00000fc0 e2533001   3115 	subs	r3,r3,1

00000fc4 1afffff8   3116 	bne	.L4355

                    3117 .L4285:

00000fc8 e8bd84f0   3118 	ldmfd	[sp]!,{r4-r7,r10,pc}

                    3119 	.endf	GOOSE_init

                    3120 	.align	4

                    3121 .L1657:

00000fcc 00000000*  3122 	.data.w	.L1584

                    3123 	.type	.L1657,$object

                    3124 	.size	.L1657,4

                    3125 

                    3126 .L2289:

00000fd0 00000000*  3127 	.data.w	.L2272

                    3128 	.type	.L2289,$object

                    3129 	.size	.L2289,4

                    3130 

                    3131 .L3221:

00000fd4 00000000*  3132 	.data.w	.L1589

                    3133 	.type	.L3221,$object

                    3134 	.size	.L3221,4

                    3135 

                    3136 .L3222:

00000fd8 00000000*  3137 	.data.w	.L1585

                    3138 	.type	.L3222,$object

                    3139 	.size	.L3222,4

                    3140 

                    3141 .L3223:

00000fdc 00000000*  3142 	.data.w	.L1590

                    3143 	.type	.L3223,$object

                    3144 	.size	.L3223,4

                    3145 

                    3146 .L3224:

00000fe0 00000000*  3147 	.data.w	g_goCBPath

                    3148 	.type	.L3224,$object

                    3149 	.size	.L3224,4

                    3150 

                    3151 .L3225:

00000fe4 00000000*  3152 	.data.w	.L1587

                    3153 	.type	.L3225,$object

                    3154 	.size	.L3225,4

                    3155 


                                                                      Page 54
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
                    3156 .L3226:

00000fe8 00000000*  3157 	.data.w	.L1588

                    3158 	.type	.L3226,$object

                    3159 	.size	.L3226,4

                    3160 

                    3161 .L3303:

00000fec 00000000*  3162 	.data.w	.L1586

                    3163 	.type	.L3303,$object

                    3164 	.size	.L3303,4

                    3165 

                    3166 .L3384:

00000ff0 00000000*  3167 	.data.w	.L3369

                    3168 	.type	.L3384,$object

                    3169 	.size	.L3384,4

                    3170 

                    3171 .L3385:

00000ff4 00000000*  3172 	.data.w	registerGoCB

                    3173 	.type	.L3385,$object

                    3174 	.size	.L3385,4

                    3175 

                    3176 .L3412:

00000ff8 00000000*  3177 	.data.w	registerGoCBsGivenFC

                    3178 	.type	.L3412,$object

                    3179 	.size	.L3412,4

                    3180 

                    3181 .L3458:

00000ffc 00000000*  3182 	.data.w	registerAllLogicalNodeGoCB

                    3183 	.type	.L3458,$object

                    3184 	.size	.L3458,4

                    3185 

                    3186 .L3836:

00001000 00000000*  3187 	.data.w	g_goCBs

                    3188 	.type	.L3836,$object

                    3189 	.size	.L3836,4

                    3190 

                    3191 .L4707:

00001004 00000000*  3192 	.data.w	registerAllLogicalDeviceGoCB

                    3193 	.type	.L4707,$object

                    3194 	.size	.L4707,4

                    3195 

                    3196 .L4708:

00001008 00000000*  3197 	.data.w	GOOSE_sendChanges

                    3198 	.type	.L4708,$object

                    3199 	.size	.L4708,4

                    3200 

                    3201 .L4709:

0000100c 00000000*  3202 	.data.w	timerProc

                    3203 	.type	.L4709,$object

                    3204 	.size	.L4709,4

                    3205 

                    3206 	.align	4

                    3207 ;i	r6	local

                    3208 

                    3209 	.data

                    3210 	.text

                    3211 

                    3212 ;1318:         }


                    3213 ;1319:     }


                    3214 ;1320: }


                    3215 	.align	4

                    3216 ;g_goCBCount	.L1586	static


                                                                      Page 55
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fps1.s
                    3217 ;g_goCBs	g_goCBs	static

                    3218 ;g_goCBPath	g_goCBPath	static

                    3219 ;__UNNAMED_1_static_in_writeGOOSEPduTemplate	.L1584	static

                    3220 ;__UNNAMED_1_static_in_initDatSet	.L1590	static

                    3221 ;__UNNAMED_1_static_in_initGoCBRef	.L1587	static

                    3222 ;__UNNAMED_2_static_in_initGoCBRef	.L1588	static

                    3223 ;__UNNAMED_1_static_in_initGoCBvars	.L1589	static

                    3224 ;__UNNAMED_2_static_in_initGoCBvars	.L1585	static

                    3225 

                    3226 	.data

                    3227 	.ghsnote version,6

                    3228 	.ghsnote tools,3

                    3229 	.ghsnote options,0

                    3230 	.text

                    3231 	.align	4

                    3232 	.data

                    3233 	.align	4

                    3234 	.section ".bss","awb"

                    3235 	.align	4

                    3236 	.section ".rodata","a"

                    3237 	.align	4

                    3238 	.text

