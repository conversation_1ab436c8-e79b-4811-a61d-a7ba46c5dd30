                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cmc1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=MMSServer.c -o gh_cmc1.o -list=MMSServer.lst C:\Users\<USER>\AppData\Local\Temp\gh_cmc1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_cmc1.s
Source File: MMSServer.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile MMSServer.c -o

                      10 ;		MMSServer.o

                      11 ;Source File:   MMSServer.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:56:07 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "main.h"


                      21 ;2: #ifdef __DEBUG


                      22 ;3: #include <Clib.h> // EnableInterrupt


                      23 ;4: #endif


                      24 ;5: 


                      25 ;6: 


                      26 ;7: #ifdef __DEBUG


                      27 ;8: void _init00(void);


                      28 ;9: unsigned int updateLoadAddr(void)


                      29 ;10: {


                      30 ;11: 	volatile unsigned int init00Addr = (unsigned int)_init00;


                      31 ;12: 	Stop();


                      32 ;13: 	return (unsigned int)init00Addr;


                      33 ;14: }


                      34 ;15: 


                      35 ;16: #endif


                      36 ;17: 


                      37 ;18: void main()


                      38 	.text

                      39 	.align	4

                      40 main::

                      41 ;19: {


                      42 

                      43 ;20: #ifdef __DEBUG	


                      44 ;21: 	DisableInterrupt();


                      45 ;22: 	updateLoadAddr();


                      46 ;23: 	EnableInterrupt();


                      47 ;24: #endif


                      48 ;25: 	


                      49 ;26:     serverMain();


                      50 


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cmc1.s
00000000 ea000000*    51 	b	serverMain

                      52 	.endf	main

                      53 	.align	4

                      54 

                      55 	.section ".bss","awb"

                      56 .L30:

                      57 	.data

                      58 	.text

                      59 

                      60 ;27: }


                      61 	.align	4

                      62 

                      63 	.data

                      64 	.ghsnote version,6

                      65 	.ghsnote tools,3

                      66 	.ghsnote options,0

                      67 	.text

                      68 	.align	4

