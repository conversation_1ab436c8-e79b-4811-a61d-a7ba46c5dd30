                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2n01.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=acse.c -o gh_2n01.o -list=acse.lst C:\Users\<USER>\AppData\Local\Temp\gh_2n01.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_2n01.s
Source File: acse.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile acse.c -o

                      10 ;		acse.o

                      11 ;Source File:   acse.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:56:00 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "acse.h"


                      21 ;2: #include "AsnEncoding.h"  


                      22 ;3: 


                      23 ;4: #include <string.h>


                      24 ;5: 


                      25 ;6: #pragma alignvar (4)


                      26 ;7: unsigned char appContextNameMms[] = { 0x28, 0xca, 0x22, 0x02, 0x03 };


                      27 ;8: 


                      28 ;9: void AcseConnection_init(AcseConnection* acse)


                      29 	.text

                      30 	.align	4

                      31 AcseConnection_init::

                      32 ;10: {


                      33 

                      34 ;11:     //TODO acse->nextReference Что это?


                      35 ;12:     acse->nextReference = 3;


                      36 

00000000 e3a01003     37 	mov	r1,3

00000004 e5801000     38 	str	r1,[r0]

00000008 e12fff1e*    39 	ret	

                      40 	.endf	AcseConnection_init

                      41 	.align	4

                      42 

                      43 ;acse	r0	param

                      44 

                      45 	.section ".bss","awb"

                      46 .L30:

                      47 	.data

                      48 	.text

                      49 

                      50 ;13: }



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2n01.s
                      51 

                      52 ;14: 


                      53 ;15: int AcseConnection_createAssociateResponseMessage(AcseConnection* acse,


                      54 	.align	4

                      55 	.align	4

                      56 	.align	4

                      57 AcseConnection_createAssociateResponseMessage::

0000000c e92d4df4     58 	stmfd	[sp]!,{r2,r4-r8,r10-fp,lr}

00000010 e1a05003     59 	mov	r5,r3

                      60 ;16:         unsigned char* buf, unsigned char* userData, int userDataLen,unsigned char acseResult)


                      61 ;17: {


                      62 

                      63 ;18:     int contentLength;


                      64 ;19:     int bufPos = 0;


                      65 

                      66 ;20: 


                      67 ;21:     int appContextLength = 9;


                      68 

                      69 ;22:     int resultLength = 5;


                      70 

                      71 ;23:     int resultDiagnosticLength = 5;


                      72 

                      73 ;24: 


                      74 ;25:     int fixedContentLength = appContextLength + resultLength + resultDiagnosticLength;


                      75 

                      76 ;26: 


                      77 ;27:     int variableContentLength = 0;


                      78 

                      79 ;28: 


                      80 ;29:     int assocDataLength;


                      81 ;30:     int userInfoLength;


                      82 ;31:     int nextRefLength;


                      83 ;32: 


                      84 ;33:     /* single ASN1 type tag */


                      85 ;34:     variableContentLength += userDataLen;


                      86 

00000014 e285a001     87 	add	r10,r5,1

                      88 ;35:     variableContentLength += 1;


                      89 

                      90 ;36:     variableContentLength += BerEncoder_determineLengthSize(userDataLen);


                      91 

00000018 e1a04001     92 	mov	r4,r1

0000001c e24dd004     93 	sub	sp,sp,4

00000020 e58d2004     94 	str	r2,[sp,4]

00000024 e1a0b000     95 	mov	fp,r0

00000028 e1a00005     96 	mov	r0,r5

0000002c eb000000*    97 	bl	BerEncoder_determineLengthSize

00000030 e08aa000     98 	add	r10,r10,r0

                      99 ;37: 


                     100 ;38:     /* indirect reference */


                     101 ;39:     nextRefLength = BerEncoder_UInt32determineEncodedSize(acse->nextReference);


                     102 

00000034 e59b0000    103 	ldr	r0,[fp]

00000038 eb000000*   104 	bl	BerEncoder_UInt32determineEncodedSize

0000003c e1a08000    105 	mov	r8,r0

                     106 ;40:     variableContentLength += nextRefLength;


                     107 

00000040 e08aa000    108 	add	r10,r10,r0

                     109 ;41:     variableContentLength += 2;


                     110 

00000044 e28a6002    111 	add	r6,r10,2


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2n01.s
                     112 ;42: 


                     113 ;43:     /* association data */


                     114 ;44:     assocDataLength = variableContentLength;


                     115 

                     116 ;45:     variableContentLength += BerEncoder_determineLengthSize(assocDataLength);


                     117 

00000048 e1a00006    118 	mov	r0,r6

0000004c eb000000*   119 	bl	BerEncoder_determineLengthSize

00000050 e086a000    120 	add	r10,r6,r0

                     121 ;46:     variableContentLength += 1;


                     122 

00000054 e28a7001    123 	add	r7,r10,1

                     124 ;47: 


                     125 ;48:     /* user information */


                     126 ;49:     userInfoLength = variableContentLength;


                     127 

                     128 ;50:     variableContentLength += BerEncoder_determineLengthSize(userInfoLength);


                     129 

00000058 e1a00007    130 	mov	r0,r7

0000005c eb000000*   131 	bl	BerEncoder_determineLengthSize

00000060 e087a000    132 	add	r10,r7,r0

                     133 ;51:     variableContentLength += 1;


                     134 

00000064 e28a1016    135 	add	r1,r10,22

                     136 ;52: 


                     137 ;53:     variableContentLength += 2;


                     138 

                     139 ;54: 


                     140 ;55:     contentLength = fixedContentLength + variableContentLength;


                     141 

                     142 ;56: 


                     143 ;57:     bufPos = BerEncoder_encodeTL(AARE_PACKET, contentLength, buf, bufPos);


                     144 

00000068 e1a02004    145 	mov	r2,r4

0000006c e3a03000    146 	mov	r3,0

00000070 e3a00061    147 	mov	r0,97

00000074 eb000000*   148 	bl	BerEncoder_encodeTL

                     149 ;58: 


                     150 ;59:     /* application context name */


                     151 ;60:     bufPos = BerEncoder_encodeTL(APPLICATION_CONTEXT_NAME, 7, buf, bufPos);


                     152 

00000078 e1a02004    153 	mov	r2,r4

0000007c e3a01007    154 	mov	r1,7

00000080 e1a03000    155 	mov	r3,r0

00000084 e3a000a1    156 	mov	r0,161

00000088 eb000000*   157 	bl	BerEncoder_encodeTL

                     158 ;61:     bufPos = BerEncoder_encodeTL(ASN_OBJECT_IDENTIFIER, 5, buf, bufPos);


                     159 

0000008c e1a02004    160 	mov	r2,r4

00000090 e3a01005    161 	mov	r1,5

00000094 e1a03000    162 	mov	r3,r0

00000098 e3a00006    163 	mov	r0,6

0000009c eb000000*   164 	bl	BerEncoder_encodeTL

000000a0 e1a0a000    165 	mov	r10,r0

                     166 ;62:     memcpy(buf + bufPos, appContextNameMms, 5);


                     167 

000000a4 e59f10fc*   168 	ldr	r1,.L75

000000a8 e08a0004    169 	add	r0,r10,r4

000000ac e3a02005    170 	mov	r2,5

000000b0 eb000000*   171 	bl	memcpy

                     172 ;63:     bufPos += 5;



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2n01.s
                     173 

000000b4 e28a3005    174 	add	r3,r10,5

                     175 ;64: 


                     176 ;65:     /* result */


                     177 ;66:     bufPos = BerEncoder_encodeTL(AARE_PACKET_RESULT, 3, buf, bufPos);


                     178 

000000b8 e1a02004    179 	mov	r2,r4

000000bc e3a01003    180 	mov	r1,3

000000c0 e3a000a2    181 	mov	r0,162

000000c4 eb000000*   182 	bl	BerEncoder_encodeTL

                     183 ;67:     bufPos = BerEncoder_encodeTL(ASN_INTEGER, 1, buf, bufPos);


                     184 

000000c8 e1a02004    185 	mov	r2,r4

000000cc e3a01001    186 	mov	r1,1

000000d0 e1a03000    187 	mov	r3,r0

000000d4 e3a00002    188 	mov	r0,2

000000d8 eb000000*   189 	bl	BerEncoder_encodeTL

                     190 ;68:     buf[bufPos++] = acseResult;


                     191 

000000dc e1a02004    192 	mov	r2,r4

000000e0 e3a01005    193 	mov	r1,5

000000e4 e5dda028    194 	ldrb	r10,[sp,40]

000000e8 e2803001    195 	add	r3,r0,1

000000ec e7c4a000    196 	strb	r10,[r4,r0]

                     197 ;69: 


                     198 ;70:     /* result source diagnostics */


                     199 ;71:     bufPos = BerEncoder_encodeTL(RESULT_SOURCE_DIAGNOSTICS, 5, buf, bufPos);


                     200 

000000f0 e3a000a3    201 	mov	r0,163

000000f4 eb000000*   202 	bl	BerEncoder_encodeTL

                     203 ;72:     bufPos = BerEncoder_encodeTL(0xa1, 3, buf, bufPos);


                     204 

000000f8 e1a02004    205 	mov	r2,r4

000000fc e3a01003    206 	mov	r1,3

00000100 e1a03000    207 	mov	r3,r0

00000104 e3a000a1    208 	mov	r0,161

00000108 eb000000*   209 	bl	BerEncoder_encodeTL

                     210 ;73:     bufPos = BerEncoder_encodeTL(ASN_INTEGER, 1, buf, bufPos);


                     211 

0000010c e1a02004    212 	mov	r2,r4

00000110 e3a01001    213 	mov	r1,1

00000114 e1a03000    214 	mov	r3,r0

00000118 e3a00002    215 	mov	r0,2

0000011c eb000000*   216 	bl	BerEncoder_encodeTL

                     217 ;74:     buf[bufPos++] = 0;


                     218 

00000120 e1a02004    219 	mov	r2,r4

00000124 e3a01000    220 	mov	r1,0

00000128 e7c41000    221 	strb	r1,[r4,r0]

                     222 ;75: 


                     223 ;76:     /* user information */


                     224 ;77:     bufPos = BerEncoder_encodeTL(USER_INFORMATION, userInfoLength, buf, bufPos);


                     225 

0000012c e1a01007    226 	mov	r1,r7

00000130 e2803001    227 	add	r3,r0,1

00000134 e3a000be    228 	mov	r0,190

00000138 eb000000*   229 	bl	BerEncoder_encodeTL

                     230 ;78: 


                     231 ;79:     /* association data */


                     232 ;80:     bufPos = BerEncoder_encodeTL(ACSE_USER_INFORMATION, assocDataLength, buf,


                     233 


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2n01.s
0000013c e1a02004    234 	mov	r2,r4

00000140 e1a01006    235 	mov	r1,r6

00000144 e1a03000    236 	mov	r3,r0

00000148 e3a00028    237 	mov	r0,40

0000014c eb000000*   238 	bl	BerEncoder_encodeTL

                     239 ;81:                                  bufPos);


                     240 ;82: 


                     241 ;83:     /* indirect-reference */


                     242 ;84:     bufPos = BerEncoder_encodeTL(ASN_INTEGER, nextRefLength, buf, bufPos);


                     243 

00000150 e1a02004    244 	mov	r2,r4

00000154 e1a01008    245 	mov	r1,r8

00000158 e1a03000    246 	mov	r3,r0

0000015c e3a00002    247 	mov	r0,2

00000160 eb000000*   248 	bl	BerEncoder_encodeTL

                     249 ;85:     bufPos = BerEncoder_encodeUInt32(acse->nextReference, buf, bufPos);


                     250 

00000164 e1a02000    251 	mov	r2,r0

00000168 e59b0000    252 	ldr	r0,[fp]

0000016c e1a01004    253 	mov	r1,r4

00000170 eb000000*   254 	bl	BerEncoder_encodeUInt32

                     255 ;86: 


                     256 ;87:     /* single ASN1 type */


                     257 ;88:     bufPos = BerEncoder_encodeTL(USER_INFORMATION_ENCODING, userDataLen,


                     258 

00000174 e1a02004    259 	mov	r2,r4

00000178 e1a01005    260 	mov	r1,r5

0000017c e1a03000    261 	mov	r3,r0

00000180 e3a000a0    262 	mov	r0,160

00000184 eb000000*   263 	bl	BerEncoder_encodeTL

00000188 e1a02005    264 	mov	r2,r5

0000018c e1a0a000    265 	mov	r10,r0

                     266 ;89:                                  buf, bufPos);


                     267 ;90: 


                     268 ;91:     memcpy(buf + bufPos,userData,userDataLen);


                     269 

00000190 e59d1004    270 	ldr	r1,[sp,4]

00000194 e08a0004    271 	add	r0,r10,r4

00000198 eb000000*   272 	bl	memcpy

                     273 ;92: 


                     274 ;93:     return bufPos + userDataLen;


                     275 

0000019c e08a0005    276 	add	r0,r10,r5

000001a0 e28dd004    277 	add	sp,sp,4

000001a4 e8bd8df4    278 	ldmfd	[sp]!,{r2,r4-r8,r10-fp,pc}

                     279 	.endf	AcseConnection_createAssociateResponseMessage

                     280 	.align	4

                     281 ;bufPos	r10	local

                     282 ;variableContentLength	r10	local

                     283 ;assocDataLength	r6	local

                     284 ;userInfoLength	r7	local

                     285 ;nextRefLength	r8	local

                     286 

                     287 ;acse	fp	param

                     288 ;buf	r4	param

                     289 ;userData	[sp,4]	param

                     290 ;userDataLen	r5	param

                     291 ;acseResult	[sp,40]	param

                     292 

                     293 	.section ".bss","awb"

                     294 .L68:


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2n01.s
                     295 	.data

                     296 	.text

                     297 

                     298 ;94: }


                     299 	.align	4

                     300 .L75:

000001a8 00000000*   301 	.data.w	appContextNameMms

                     302 	.type	.L75,$object

                     303 	.size	.L75,4

                     304 

                     305 	.align	4

                     306 

                     307 	.data

                     308 .L84:

                     309 	.globl	appContextNameMms

00000000 0222ca28    310 appContextNameMms:	.data.b	40,202,34,2

00000004 03         311 	.data.b	3

00000005 000000     312 	.space	3

                     313 	.type	appContextNameMms,$object

                     314 	.size	appContextNameMms,8

                     315 	.ghsnote version,6

                     316 	.ghsnote tools,3

                     317 	.ghsnote options,0

                     318 	.text

                     319 	.align	4

                     320 	.data

                     321 	.align	4

                     322 	.text

