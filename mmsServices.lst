                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=mmsservices.c -o gh_9o01.o -list=mmsServices.lst C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
Source File: mmsservices.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile mmsservices.c

                      10 ;		-o mmsServices.o

                      11 ;Source File:   mmsservices.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:56:04 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "mmsservices.h"


                      21 ;2: 


                      22 ;3: #include "AsnEncoding.h"


                      23 ;4: #include "mms_error.h"


                      24 ;5: #include "mms_read.h"


                      25 ;6: #include "mms_write.h"


                      26 ;7: #include "mms_get_name_list.h"


                      27 ;8: #include "mms_get_variable_access_attributes.h"


                      28 ;9: #include "mms_get_data_set_access_attr.h"


                      29 ;10: #include "mms_fs.h"


                      30 ;11: #include "mms.h"


                      31 ;12: #include "bufViewBER.h"


                      32 ;13: #include <debug.h>


                      33 ;14: #include <types.h>


                      34 ;15: #include <stddef.h>


                      35 ;16: #include <string.h>


                      36 ;17: 


                      37 ;18: static char* g_vendor = "MTRA";


                      38 ;19: static char* g_model = "device";


                      39 ;20: static char* g_revision = "1";


                      40 ;21: 


                      41 ;22: int mmsServer_handleIdentifyRequest(uint32_t invokeId, uint8_t* outBuf)


                      42 	.text

                      43 	.align	4

                      44 mmsServer_handleIdentifyRequest::

00000000 e92d4ff0     45 	stmfd	[sp]!,{r4-fp,lr}

                      46 ;23: {


                      47 

                      48 ;24:     int bufPos = 0;


                      49 

                      50 ;25: 



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
                      51 ;26:     uint32_t invokeIdLength = BerEncoder_UInt32determineEncodedSize(invokeId);


                      52 

00000004 e1a04001     53 	mov	r4,r1

00000008 e59f5394*    54 	ldr	r5,.L43

0000000c e24dd008     55 	sub	sp,sp,8

00000010 e58d0004     56 	str	r0,[sp,4]

00000014 eb000000*    57 	bl	BerEncoder_UInt32determineEncodedSize

00000018 e1a06000     58 	mov	r6,r0

                      59 ;27:     uint32_t vendorNameLength = strlen(g_vendor);


                      60 

0000001c e5950000     61 	ldr	r0,[r5]

00000020 eb000000*    62 	bl	strlen

00000024 e1a07000     63 	mov	r7,r0

                      64 ;28:     uint32_t modelNameLength = strlen(g_model);


                      65 

00000028 e5950004     66 	ldr	r0,[r5,4]

0000002c eb000000*    67 	bl	strlen

00000030 e1a0a000     68 	mov	r10,r0

                      69 ;29:     uint32_t revisionLength = strlen(g_revision);


                      70 

00000034 e5950008     71 	ldr	r0,[r5,8]

00000038 eb000000*    72 	bl	strlen

0000003c e1a0b000     73 	mov	fp,r0

                      74 ;30: 


                      75 ;31:     uint32_t identityLength = 3 +  BerEncoder_determineLengthSize(vendorNameLength)


                      76 

00000040 e1a00007     77 	mov	r0,r7

00000044 eb000000*    78 	bl	BerEncoder_determineLengthSize

00000048 e1a08000     79 	mov	r8,r0

0000004c e1a0000b     80 	mov	r0,fp

00000050 eb000000*    81 	bl	BerEncoder_determineLengthSize

00000054 e0809008     82 	add	r9,r0,r8

00000058 e1a0000a     83 	mov	r0,r10

0000005c eb000000*    84 	bl	BerEncoder_determineLengthSize

00000060 e0801009     85 	add	r1,r0,r9

00000064 e0811007     86 	add	r1,r1,r7

00000068 e081100a     87 	add	r1,r1,r10

0000006c e081100b     88 	add	r1,r1,fp

00000070 e2817003     89 	add	r7,r1,3

                      90 ;32:             + BerEncoder_determineLengthSize(modelNameLength) + BerEncoder_determineLengthSize(revisionLength)


                      91 ;33:             + vendorNameLength + modelNameLength + revisionLength;


                      92 ;34: 


                      93 ;35:     uint32_t identifyResponseLength = invokeIdLength + 2 + 1 + BerEncoder_determineLengthSize(identityLength)


                      94 

00000074 e1a00007     95 	mov	r0,r7

00000078 eb000000*    96 	bl	BerEncoder_determineLengthSize

0000007c e0861000     97 	add	r1,r6,r0

00000080 e0871001     98 	add	r1,r7,r1

00000084 e2811003     99 	add	r1,r1,3

                     100 ;36:             + identityLength;


                     101 ;37: 


                     102 ;38:     /* Identify response pdu */


                     103 ;39:     bufPos = BerEncoder_encodeTL(0xa1, identifyResponseLength, outBuf, bufPos);


                     104 

00000088 e1a02004    105 	mov	r2,r4

0000008c e3a03000    106 	mov	r3,0

00000090 e3a000a1    107 	mov	r0,161

00000094 eb000000*   108 	bl	BerEncoder_encodeTL

                     109 ;40: 


                     110 ;41:     /* invokeId */


                     111 ;42:     bufPos = BerEncoder_encodeTL(0x02, invokeIdLength, outBuf, bufPos);



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
                     112 

00000098 e1a02004    113 	mov	r2,r4

0000009c e1a01006    114 	mov	r1,r6

000000a0 e1a03000    115 	mov	r3,r0

000000a4 e3a00002    116 	mov	r0,2

000000a8 eb000000*   117 	bl	BerEncoder_encodeTL

                     118 ;43:     bufPos = BerEncoder_encodeUInt32(invokeId, outBuf, bufPos);


                     119 

000000ac e1a02000    120 	mov	r2,r0

000000b0 e59d0004    121 	ldr	r0,[sp,4]

000000b4 e1a01004    122 	mov	r1,r4

000000b8 eb000000*   123 	bl	BerEncoder_encodeUInt32

                     124 ;44: 


                     125 ;45:     bufPos = BerEncoder_encodeTL(0xa2, identityLength, outBuf, bufPos);


                     126 

000000bc e1a02004    127 	mov	r2,r4

000000c0 e1a01007    128 	mov	r1,r7

000000c4 e1a03000    129 	mov	r3,r0

000000c8 e3a000a2    130 	mov	r0,162

000000cc eb000000*   131 	bl	BerEncoder_encodeTL

                     132 ;46:     bufPos = BerEncoder_encodeStringWithTL(0x80, g_vendor, outBuf, bufPos);


                     133 

000000d0 e1a02004    134 	mov	r2,r4

000000d4 e1a03000    135 	mov	r3,r0

000000d8 e5951000    136 	ldr	r1,[r5]

000000dc e3a00080    137 	mov	r0,128

000000e0 eb000000*   138 	bl	BerEncoder_encodeStringWithTL

                     139 ;47:     bufPos = BerEncoder_encodeStringWithTL(0x81, g_model, outBuf, bufPos);


                     140 

000000e4 e1a02004    141 	mov	r2,r4

000000e8 e1a03000    142 	mov	r3,r0

000000ec e5951004    143 	ldr	r1,[r5,4]

000000f0 e3a00081    144 	mov	r0,129

000000f4 eb000000*   145 	bl	BerEncoder_encodeStringWithTL

                     146 ;48:     bufPos = BerEncoder_encodeStringWithTL(0x82, g_revision, outBuf, bufPos);


                     147 

000000f8 e1a02004    148 	mov	r2,r4

000000fc e1a03000    149 	mov	r3,r0

00000100 e5951008    150 	ldr	r1,[r5,8]

00000104 e3a00082    151 	mov	r0,130

00000108 eb000000*   152 	bl	BerEncoder_encodeStringWithTL

                     153 ;49: 


                     154 ;50:     return bufPos;


                     155 

0000010c e28dd008    156 	add	sp,sp,8

00000110 e8bd8ff0    157 	ldmfd	[sp]!,{r4-fp,pc}

                     158 	.endf	mmsServer_handleIdentifyRequest

                     159 	.align	4

                     160 ;invokeIdLength	r6	local

                     161 ;vendorNameLength	r7	local

                     162 ;modelNameLength	r10	local

                     163 ;revisionLength	fp	local

                     164 ;identityLength	r7	local

                     165 

                     166 ;invokeId	[sp,4]	param

                     167 ;outBuf	r4	param

                     168 

                     169 	.data

                     170 .L30:

                     171 .L31:

00000000 00000000*   172 g_vendor:	.data.w	.L35


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
                     173 	.type	g_vendor,$object

                     174 	.size	g_vendor,4

                     175 .L32:

00000004 00000000*   176 g_model:	.data.w	.L36

                     177 	.type	g_model,$object

                     178 	.size	g_model,4

                     179 .L33:

00000008 00000000*   180 g_revision:	.data.w	.L34

                     181 	.type	g_revision,$object

                     182 	.size	g_revision,4

                     183 	.section ".rodata","a"

                     184 .L34:;	"1\000"

00000000 0031       185 	.data.b	49,0

00000002 0000       186 	.space	2

                     187 .L35:;	"MTRA\000"

00000004 4152544d    188 	.data.b	77,84,82,65

00000008 00         189 	.data.b	0

00000009 000000     190 	.space	3

                     191 .L36:;	"device\000"

0000000c 69766564    192 	.data.b	100,101,118,105

00000010 6563       193 	.data.b	99,101

00000012 00         194 	.data.b	0

00000013 00         195 	.space	1

                     196 	.data

                     197 	.text

                     198 

                     199 ;51: }


                     200 

                     201 ;52: 


                     202 ;53: 


                     203 ;54: 


                     204 ;55: 


                     205 ;56: int handleConfirmedRequestPdu(IsoConnection* isoConn,


                     206 	.align	4

                     207 	.align	4

                     208 handleConfirmedRequestPdu::

00000114 e92d4ff0    209 	stmfd	[sp]!,{r4-fp,lr}

                     210 ;57:                                unsigned char* inBuf, int inBufPos, int inBufLen,


                     211 ;58:                                unsigned char* outBuf, int maxBufSize)


                     212 ;59: 


                     213 ;60: {	


                     214 

00000118 e1a09000    215 	mov	r9,r0

0000011c e24dd044    216 	sub	sp,sp,68

00000120 e59d6068    217 	ldr	r6,[sp,104]

00000124 e3a08000    218 	mov	r8,0

00000128 e58d800c    219 	str	r8,[sp,12]

                     220 ;61: 	BufferView inBufView;


                     221 ;62: 	BufferView outBufView;


                     222 ;63: 	MmsConnection* mmsConn = &isoConn->mmsConn;


                     223 

0000012c e1a0a001    224 	mov	r10,r1

00000130 e3a01f8f    225 	mov	r1,0x023c

00000134 e2611a49    226 	rsb	r1,r1,73<<12

00000138 e0807001    227 	add	r7,r0,r1

                     228 ;64: 


                     229 ;65:     int fullResponseSize = 0;


                     230 

                     231 ;66:     // Размер ответа на каждый запрос


                     232 ;67:     int requestResponseSize;


                     233 ;68:     unsigned int invokeId = 0;



                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
                     234 

                     235 ;69:     


                     236 ;70: 


                     237 ;71:     while(inBufPos < inBufLen)


                     238 

0000013c e1a05002    239 	mov	r5,r2

00000140 e1a0b003    240 	mov	fp,r3

00000144 e155000b    241 	cmp	r5,fp

00000148 aa0000cf    242 	bge	.L51

                     243 .L48:

                     244 ;72:     {


                     245 

                     246 ;73: 		uint32_t tag;


                     247 ;74: 		BufferView pktBufview;


                     248 ;75: 		//Размер данных тэга


                     249 ;76: 		size_t length;


                     250 ;77: 		


                     251 ;78: 		//Временная переменная для некоторых сервисов


                     252 ;79: 		bool serviceOK;


                     253 ;80: 


                     254 ;81: 		//Эта переменная проверяется только если requestResponseSize == 0.


                     255 ;82: 		//Тэги, которые просто содержат информацию, но не вызывают


                     256 ;83: 		//формирования ответа, должны устанавливать её в true.


                     257 ;84: 		//В противном случае requestResponseSize == 0 будет считаться


                     258 ;85: 		//необработанной ошибкой.


                     259 ;86: 		//Эту муть можно будет переделать когда сервисы будут явно возвращать


                     260 ;87: 		//наличие/отсутствие ошибки


                     261 ;88: 		bool tagOK = false;


                     262 

                     263 ;89: 


                     264 ;90: 		BufferView_init(&pktBufview, inBuf, inBufLen, inBufPos);


                     265 

0000014c e1a03005    266 	mov	r3,r5

00000150 e1a0200b    267 	mov	r2,fp

00000154 e1a0100a    268 	mov	r1,r10

00000158 e28d0020    269 	add	r0,sp,32

0000015c eb000000*   270 	bl	BufferView_init

                     271 ;91: 		if (!BufferView_decodeExtTL(&pktBufview, &tag, &length, NULL))


                     272 

00000160 e28d2014    273 	add	r2,sp,20

00000164 e28d1010    274 	add	r1,sp,16

00000168 e28d0020    275 	add	r0,sp,32

0000016c e3a03000    276 	mov	r3,0

00000170 eb000000*   277 	bl	BufferView_decodeExtTL

00000174 e3500000    278 	cmp	r0,0

00000178 0a0000c3    279 	beq	.L51

                     280 ;92: 		{


                     281 

                     282 ;93: 			//Не понятно, что делать с такой ошибкой


                     283 ;94: 			ERROR_REPORT("Unable to parse request");


                     284 ;95: 			break;


                     285 

                     286 ;96: 		}


                     287 ;97: 		inBufPos = pktBufview.pos;


                     288 

0000017c e59d5024    289 	ldr	r5,[sp,36]

                     290 ;98: 


                     291 ;99: 		// Пока некоторые операции используют непосредственно буфера,


                     292 ;100: 		// а некоторые - BufferView, готовим BufferView для каждой операции.


                     293 ;101: 		// В будущем надо сделать инициализацию BufferView в начале функции,


                     294 ;102: 		// Для входящих данных и для исходящих.



                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
                     295 ;103: 		BufferView_init(&inBufView, inBuf + inBufPos, length, 0);


                     296 

00000180 e59d2014    297 	ldr	r2,[sp,20]

00000184 e085100a    298 	add	r1,r5,r10

00000188 e28d0038    299 	add	r0,sp,56

0000018c e3a03000    300 	mov	r3,0

00000190 eb000000*   301 	bl	BufferView_init

                     302 ;104: 		BufferView_init(&outBufView, outBuf, maxBufSize - fullResponseSize, 0);


                     303 

00000194 e1a01006    304 	mov	r1,r6

00000198 e59d006c    305 	ldr	r0,[sp,108]

0000019c e1a04008    306 	mov	r4,r8

000001a0 e0402004    307 	sub	r2,r0,r4

000001a4 e28d002c    308 	add	r0,sp,44

000001a8 e3a03000    309 	mov	r3,0

000001ac eb000000*   310 	bl	BufferView_init

                     311 ;105: 		


                     312 ;106: 		switch (tag)


                     313 

000001b0 e59d1010    314 	ldr	r1,[sp,16]

000001b4 e25110a6    315 	subs	r1,r1,166

000001b8 2a000009    316 	bhs	.L349

000001bc e2911002    317 	adds	r1,r1,2

000001c0 8a00002f    318 	bhi	.L57

000001c4 0a000021    319 	beq	.L56

000001c8 e2911003    320 	adds	r1,r1,3

000001cc 0a000036    321 	beq	.L58

000001d0 e291101f    322 	adds	r1,r1,31

000001d4 0a000057    323 	beq	.L61

000001d8 e2911080    324 	adds	r1,r1,128

000001dc 1a000099    325 	bne	.L78

000001e0 ea000010    326 	b	.L55

                     327 .L349:

                     328 

000001e4 e2511000    329 	subs	r1,r1,0

000001e8 0a00003e    330 	beq	.L59

000001ec e2511006    331 	subs	r1,r1,6

000001f0 0a000046    332 	beq	.L60

000001f4 e3a04c9e    333 	mov	r4,158<<8

000001f8 e284409d    334 	add	r4,r4,157

000001fc e0511004    335 	subs	r1,r1,r4

00000200 0a000074    336 	beq	.L70

00000204 e2511001    337 	subs	r1,r1,1

00000208 0a00007a    338 	beq	.L74

0000020c e3a04d7c    339 	mov	r4,31<<8

00000210 e28440fe    340 	add	r4,r4,254

00000214 e0511004    341 	subs	r1,r1,r4

00000218 0a000059    342 	beq	.L66

0000021c e3510005    343 	cmp	r1,5

00000220 0a00004f    344 	beq	.L62

00000224 ea000087    345 	b	.L78

                     346 .L55:

                     347 ;107: 		{			


                     348 ;108: 		case ASN_INTEGER: //Invoke ID


                     349 ;109: 			invokeId = BerDecoder_decodeUint32(inBuf, length, inBufPos);							


                     350 

00000228 e1a02005    351 	mov	r2,r5

0000022c e59d1014    352 	ldr	r1,[sp,20]

00000230 e1a0000a    353 	mov	r0,r10

00000234 eb000000*   354 	bl	BerDecoder_decodeUint32

00000238 e59d1014    355 	ldr	r1,[sp,20]


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
0000023c e58d000c    356 	str	r0,[sp,12]

                     357 ;110: 			requestResponseSize = 0;


                     358 

                     359 ;111: 			tagOK = true;


                     360 

                     361 ;218: 				outBuf, MMS_ERROR_ACCESS_OTHER);


                     362 ;219: 		}


                     363 ;220:         


                     364 ;221:         inBufPos += length;


                     365 

00000240 e0855001    366 	add	r5,r5,r1

                     367 ;222:         outBuf += requestResponseSize;        


                     368 

                     369 ;223:         fullResponseSize += requestResponseSize;		


                     370 

00000244 e155000b    371 	cmp	r5,fp

00000248 baffffbf    372 	blt	.L48

0000024c ea00008e    373 	b	.L51

                     374 .L56:

                     375 ;112: 			break;


                     376 ;113: 


                     377 ;114: 		case MMS_SERVICE_READ_CODE: 			


                     378 ;115:             requestResponseSize = mms_handleReadRequest(mmsConn,


                     379 

00000250 e59d006c    380 	ldr	r0,[sp,108]

00000254 e59d100c    381 	ldr	r1,[sp,12]

00000258 e0403008    382 	sub	r3,r0,r8

0000025c e1a02006    383 	mov	r2,r6

00000260 e88d000e    384 	stmea	[sp],{r1-r3}

00000264 e1a0300b    385 	mov	r3,fp

00000268 e1a02005    386 	mov	r2,r5

0000026c e1a0100a    387 	mov	r1,r10

00000270 e1a00007    388 	mov	r0,r7

00000274 eb000000*   389 	bl	mms_handleReadRequest

00000278 e1b04000    390 	movs	r4,r0

                     391 ;211: 				MMS_ERROR_REJECT_UNRECOGNIZED_SERVICE, outBuf);				


                     392 ;212: 			break;


                     393 ;213: 		}


                     394 ;214: 		if (requestResponseSize == 0 && !tagOK)


                     395 

0000027c 1a00007c    396 	bne	.L79

00000280 ea000076    397 	b	.L80

                     398 .L57:

                     399 ;116:                 inBuf, inBufPos, inBufLen, invokeId, outBuf, maxBufSize - fullResponseSize);


                     400 ;117: 


                     401 ;118: 			break;


                     402 ;119: 


                     403 ;120: 		case MMS_SERVICE_WRITE_CODE:			


                     404 ;121: 			TRACE("MMS write request received");


                     405 ;122: 			requestResponseSize = mms_handleWriteRequest(isoConn, inBuf, inBufPos,


                     406 

00000284 e59d100c    407 	ldr	r1,[sp,12]

00000288 e88d0042    408 	stmea	[sp],{r1,r6}

0000028c e1a0300b    409 	mov	r3,fp

00000290 e1a02005    410 	mov	r2,r5

00000294 e1a0100a    411 	mov	r1,r10

00000298 e1a00009    412 	mov	r0,r9

0000029c eb000000*   413 	bl	mms_handleWriteRequest

000002a0 e1b04000    414 	movs	r4,r0

                     415 ;211: 				MMS_ERROR_REJECT_UNRECOGNIZED_SERVICE, outBuf);				


                     416 ;212: 			break;



                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
                     417 ;213: 		}


                     418 ;214: 		if (requestResponseSize == 0 && !tagOK)


                     419 

000002a4 1a000072    420 	bne	.L79

000002a8 ea00006c    421 	b	.L80

                     422 .L58:

                     423 ;123: 				inBufLen, invokeId, outBuf);


                     424 ;124: 			break;


                     425 ;125: 


                     426 ;126: 		case MMS_SERVICE_GET_NAME_LIST_CODE:			


                     427 ;127: 			//TRACE("MMS_SERVICE_GET_NAME_LIST_CODE");


                     428 ;128: 			requestResponseSize = mms_handleGetNameListRequest(mmsConn, inBuf, inBufPos,


                     429 

000002ac e59d100c    430 	ldr	r1,[sp,12]

000002b0 e88d0042    431 	stmea	[sp],{r1,r6}

000002b4 e1a0300b    432 	mov	r3,fp

000002b8 e1a02005    433 	mov	r2,r5

000002bc e1a0100a    434 	mov	r1,r10

000002c0 e1a00007    435 	mov	r0,r7

000002c4 eb000000*   436 	bl	mms_handleGetNameListRequest

000002c8 e1a04000    437 	mov	r4,r0

                     438 ;129: 				inBufLen, invokeId, outBuf);


                     439 ;130: 			debugSendUshort("requestResponseSize", requestResponseSize);


                     440 

000002cc e1a01804    441 	mov	r1,r4 lsl 16

000002d0 e28f0000*   442 	adr	r0,.L399

000002d4 e1a01821    443 	mov	r1,r1 lsr 16

000002d8 eb000000*   444 	bl	debugSendUshort

                     445 ;211: 				MMS_ERROR_REJECT_UNRECOGNIZED_SERVICE, outBuf);				


                     446 ;212: 			break;


                     447 ;213: 		}


                     448 ;214: 		if (requestResponseSize == 0 && !tagOK)


                     449 

000002dc e3540000    450 	cmp	r4,0

000002e0 1a000063    451 	bne	.L79

000002e4 ea00005d    452 	b	.L80

                     453 .L59:

                     454 ;131: 			break;


                     455 ;132: 


                     456 ;133: 		case MMS_SERVICE_GET_VARIABLE_ACCESS_ATRIBUTES_CODE:


                     457 ;134: 			//TRACE("MMS_SERVICE_GET_VARIABLE_ACCESS_ATRIBUTES_CODE");


                     458 ;135: 


                     459 ;136: 			requestResponseSize = mms_handleGetVariableAccessAttr(mmsConn, inBuf, inBufPos,


                     460 

000002e8 e59d100c    461 	ldr	r1,[sp,12]

000002ec e88d0042    462 	stmea	[sp],{r1,r6}

000002f0 e1a0300b    463 	mov	r3,fp

000002f4 e1a02005    464 	mov	r2,r5

000002f8 e1a0100a    465 	mov	r1,r10

000002fc e1a00007    466 	mov	r0,r7

00000300 eb000000*   467 	bl	mms_handleGetVariableAccessAttr

00000304 e1b04000    468 	movs	r4,r0

                     469 ;211: 				MMS_ERROR_REJECT_UNRECOGNIZED_SERVICE, outBuf);				


                     470 ;212: 			break;


                     471 ;213: 		}


                     472 ;214: 		if (requestResponseSize == 0 && !tagOK)


                     473 

00000308 1a000059    474 	bne	.L79

0000030c ea000053    475 	b	.L80

                     476 .L60:

                     477 ;137: 				inBufLen, invokeId, outBuf);



                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
                     478 ;138: 			break;


                     479 ;139: 


                     480 ;140: 		case MMS_SERVICE_GET_DATA_SET_ATTRIBUTES_CODE:


                     481 ;141: 			//TRACE("MMS_SERVICE_GET_DATA_SET_ATTRIBUTES_CODE");


                     482 ;142: 			requestResponseSize = mms_handleGetDataSetAccessAttr(mmsConn, inBuf, inBufPos,


                     483 

00000310 e59d100c    484 	ldr	r1,[sp,12]

00000314 e88d0042    485 	stmea	[sp],{r1,r6}

00000318 e1a0300b    486 	mov	r3,fp

0000031c e1a02005    487 	mov	r2,r5

00000320 e1a0100a    488 	mov	r1,r10

00000324 e1a00007    489 	mov	r0,r7

00000328 eb000000*   490 	bl	mms_handleGetDataSetAccessAttr

0000032c e1b04000    491 	movs	r4,r0

                     492 ;211: 				MMS_ERROR_REJECT_UNRECOGNIZED_SERVICE, outBuf);				


                     493 ;212: 			break;


                     494 ;213: 		}


                     495 ;214: 		if (requestResponseSize == 0 && !tagOK)


                     496 

00000330 1a00004f    497 	bne	.L79

00000334 ea000049    498 	b	.L80

                     499 .L61:

                     500 ;143: 				inBufLen, invokeId, outBuf);


                     501 ;144: 			break;


                     502 ;145: 


                     503 ;146: 		case MMS_SERVICE_IDENTIFY_CODE:


                     504 ;147: 			TRACE("Identify service");


                     505 ;148: 			requestResponseSize = mmsServer_handleIdentifyRequest(invokeId, outBuf);


                     506 

00000338 e59d000c    507 	ldr	r0,[sp,12]

0000033c e1a01006    508 	mov	r1,r6

00000340 ebffff2e*   509 	bl	mmsServer_handleIdentifyRequest

00000344 e1a04000    510 	mov	r4,r0

                     511 ;149: 			debugSendUshort("Identify service returned:", requestResponseSize);


                     512 

00000348 e1a01804    513 	mov	r1,r4 lsl 16

0000034c e28f0000*   514 	adr	r0,.L400

00000350 e1a01821    515 	mov	r1,r1 lsr 16

00000354 eb000000*   516 	bl	debugSendUshort

                     517 ;211: 				MMS_ERROR_REJECT_UNRECOGNIZED_SERVICE, outBuf);				


                     518 ;212: 			break;


                     519 ;213: 		}


                     520 ;214: 		if (requestResponseSize == 0 && !tagOK)


                     521 

00000358 e3540000    522 	cmp	r4,0

0000035c 1a000044    523 	bne	.L79

00000360 ea00003e    524 	b	.L80

                     525 .L62:

                     526 ;150: 			break;


                     527 ;151: 


                     528 ;152: 		case MMS_SERVICE_FILE_DIRECTORY_REQUEST:			


                     529 ;153: 			serviceOK = mms_handleFileDirRequest(mmsConn,


                     530 

00000364 e28d302c    531 	add	r3,sp,44

00000368 e59d200c    532 	ldr	r2,[sp,12]

0000036c e28d1038    533 	add	r1,sp,56

00000370 e1a00007    534 	mov	r0,r7

00000374 eb000000*   535 	bl	mms_handleFileDirRequest

                     536 ;154: 				&inBufView, invokeId, &outBufView);


                     537 ;155: 			if (serviceOK)


                     538 


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
                     539 ;183: 				&inBufView, invokeId, &outBufView);


                     540 ;184: 			if (serviceOK)


                     541 

00000378 e3500000    542 	cmp	r0,0

0000037c 0a000028    543 	beq	.L75

00000380 ea000023    544 	b	.L76

                     545 .L66:

                     546 ;156: 			{


                     547 

                     548 ;157: 				requestResponseSize = outBufView.pos;


                     549 

                     550 ;158: 			}


                     551 ;159: 			else


                     552 ;160: 			{


                     553 

                     554 ;161: 				ERROR_REPORT("handleFileDirRequest error");


                     555 ;162: 				requestResponseSize = 0;


                     556 

                     557 ;163: 			}


                     558 ;164: 


                     559 ;165: 			break;


                     560 ;166: 


                     561 ;167: 		case MMS_SERVICE_FILE_OPEN_REQUEST:			


                     562 ;168: 			serviceOK = mms_handleFileOpenRequest(mmsConn,


                     563 

00000384 e28d302c    564 	add	r3,sp,44

00000388 e59d200c    565 	ldr	r2,[sp,12]

0000038c e28d1038    566 	add	r1,sp,56

00000390 e1a00007    567 	mov	r0,r7

00000394 eb000000*   568 	bl	mms_handleFileOpenRequest

                     569 ;169: 				&inBufView, invokeId, &outBufView);


                     570 ;170: 			if (serviceOK)


                     571 

                     572 ;183: 				&inBufView, invokeId, &outBufView);


                     573 ;184: 			if (serviceOK)


                     574 

00000398 e3500000    575 	cmp	r0,0

0000039c 0a000020    576 	beq	.L75

000003a0 ea00001b    577 	b	.L76

                     578 	.align	4

                     579 .L43:

000003a4 00000000*   580 	.data.w	.L30

                     581 	.type	.L43,$object

                     582 	.size	.L43,4

                     583 

                     584 .L399:

                     585 ;	"requestResponseSize\000"

000003a8 75716572    586 	.data.b	114,101,113,117

000003ac 52747365    587 	.data.b	101,115,116,82

000003b0 6f707365    588 	.data.b	101,115,112,111

000003b4 5365736e    589 	.data.b	110,115,101,83

000003b8 00657a69    590 	.data.b	105,122,101,0

                     591 	.align 4

                     592 

                     593 	.type	.L399,$object

                     594 	.size	.L399,4

                     595 

                     596 .L400:

                     597 ;	"Identify service returned:\000"

000003bc 6e656449    598 	.data.b	73,100,101,110

000003c0 79666974    599 	.data.b	116,105,102,121


                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
000003c4 72657320    600 	.data.b	32,115,101,114

000003c8 65636976    601 	.data.b	118,105,99,101

000003cc 74657220    602 	.data.b	32,114,101,116

000003d0 656e7275    603 	.data.b	117,114,110,101

000003d4 3a64       604 	.data.b	100,58

000003d6 00         605 	.data.b	0

000003d7 00         606 	.align 4

                     607 

                     608 	.type	.L400,$object

                     609 	.size	.L400,4

                     610 

                     611 .L70:

                     612 ;171: 			{


                     613 

                     614 ;172: 				requestResponseSize = outBufView.pos;


                     615 

                     616 ;173: 			}


                     617 ;174: 			else


                     618 ;175: 			{


                     619 

                     620 ;176: 				ERROR_REPORT("handleFileOpenRequest error");


                     621 ;177: 				requestResponseSize = 0;


                     622 

                     623 ;178: 			}


                     624 ;179: 			break;


                     625 ;180: 


                     626 ;181: 		case MMS_SERVICE_FILE_READ_REQUEST:							


                     627 ;182: 			serviceOK = mms_handleFileReadRequest(mmsConn,


                     628 

000003d8 e28d302c    629 	add	r3,sp,44

000003dc e59d200c    630 	ldr	r2,[sp,12]

000003e0 e28d1038    631 	add	r1,sp,56

000003e4 e1a00007    632 	mov	r0,r7

000003e8 eb000000*   633 	bl	mms_handleFileReadRequest

                     634 ;183: 				&inBufView, invokeId, &outBufView);


                     635 ;184: 			if (serviceOK)


                     636 

000003ec e3500000    637 	cmp	r0,0

000003f0 0a00000b    638 	beq	.L75

000003f4 ea000006    639 	b	.L76

                     640 .L74:

                     641 ;185: 			{


                     642 

                     643 ;186: 				requestResponseSize = outBufView.pos;


                     644 

                     645 ;187: 			}


                     646 ;188: 			else


                     647 ;189: 			{


                     648 

                     649 ;190: 				ERROR_REPORT("handleFileReadRequest error");


                     650 ;191: 				requestResponseSize = 0;


                     651 

                     652 ;192: 			}


                     653 ;193: 			break;


                     654 ;194: 		case MMS_SERVICE_FILE_CLOSE_REQUEST:							


                     655 ;195: 			serviceOK = mms_handleFileCloseRequest(mmsConn,


                     656 

000003f8 e28d302c    657 	add	r3,sp,44

000003fc e59d200c    658 	ldr	r2,[sp,12]

00000400 e28d1038    659 	add	r1,sp,56

00000404 e1a00007    660 	mov	r0,r7


                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
00000408 eb000000*   661 	bl	mms_handleFileCloseRequest

                     662 ;196: 				&inBufView, invokeId, &outBufView);


                     663 ;197: 			if (serviceOK)


                     664 

                     665 ;183: 				&inBufView, invokeId, &outBufView);


                     666 ;184: 			if (serviceOK)


                     667 

0000040c e3500000    668 	cmp	r0,0

00000410 0a000003    669 	beq	.L75

                     670 .L76:

                     671 ;198: 			{


                     672 

                     673 ;199: 				requestResponseSize = outBufView.pos;


                     674 

00000414 e59d4030    675 	ldr	r4,[sp,48]

                     676 ;211: 				MMS_ERROR_REJECT_UNRECOGNIZED_SERVICE, outBuf);				


                     677 ;212: 			break;


                     678 ;213: 		}


                     679 ;214: 		if (requestResponseSize == 0 && !tagOK)


                     680 

00000418 e3540000    681 	cmp	r4,0

0000041c 1a000014    682 	bne	.L79

00000420 ea00000e    683 	b	.L80

                     684 .L75:

                     685 ;200: 			}


                     686 ;201: 			else


                     687 ;202: 			{


                     688 

                     689 ;203: 				ERROR_REPORT("handleFileReadRequest error");


                     690 ;204: 				requestResponseSize = 0;


                     691 

                     692 ;215: 		{


                     693 

                     694 ;216: 			ERROR_REPORT("Unknown MMS service error");


                     695 ;217: 			requestResponseSize = CreateMmsConfirmedErrorPdu(invokeId,


                     696 

00000424 e1a01006    697 	mov	r1,r6

00000428 e59d000c    698 	ldr	r0,[sp,12]

0000042c e3a02050    699 	mov	r2,80

00000430 eb000000*   700 	bl	CreateMmsConfirmedErrorPdu

                     701 ;218: 				outBuf, MMS_ERROR_ACCESS_OTHER);


                     702 ;219: 		}


                     703 ;220:         


                     704 ;221:         inBufPos += length;


                     705 

00000434 e59d1014    706 	ldr	r1,[sp,20]

00000438 e0866000    707 	add	r6,r6,r0

                     708 ;223:         fullResponseSize += requestResponseSize;		


                     709 

0000043c e0855001    710 	add	r5,r5,r1

                     711 ;222:         outBuf += requestResponseSize;        


                     712 

00000440 e0888000    713 	add	r8,r8,r0

00000444 ea00000e    714 	b	.L46

                     715 .L78:

                     716 ;205: 			}


                     717 ;206: 			break;


                     718 ;207: 


                     719 ;208: 		default:


                     720 ;209: 			ERROR_REPORT("Unsupperted MMS tag %04X", tag);


                     721 ;210: 			requestResponseSize = mms_createMmsRejectPdu(&invokeId,



                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
                     722 

00000448 e1a02006    723 	mov	r2,r6

0000044c e28d000c    724 	add	r0,sp,12

00000450 e3a01067    725 	mov	r1,103

00000454 eb000000*   726 	bl	mms_createMmsRejectPdu

00000458 e1b04000    727 	movs	r4,r0

                     728 ;211: 				MMS_ERROR_REJECT_UNRECOGNIZED_SERVICE, outBuf);				


                     729 ;212: 			break;


                     730 ;213: 		}


                     731 ;214: 		if (requestResponseSize == 0 && !tagOK)


                     732 

0000045c 1a000004    733 	bne	.L79

                     734 .L80:

                     735 ;215: 		{


                     736 

                     737 ;216: 			ERROR_REPORT("Unknown MMS service error");


                     738 ;217: 			requestResponseSize = CreateMmsConfirmedErrorPdu(invokeId,


                     739 

00000460 e1a01006    740 	mov	r1,r6

00000464 e59d000c    741 	ldr	r0,[sp,12]

00000468 e3a02050    742 	mov	r2,80

0000046c eb000000*   743 	bl	CreateMmsConfirmedErrorPdu

00000470 e1a04000    744 	mov	r4,r0

                     745 .L79:

                     746 ;218: 				outBuf, MMS_ERROR_ACCESS_OTHER);


                     747 ;219: 		}


                     748 ;220:         


                     749 ;221:         inBufPos += length;


                     750 

00000474 e59d1014    751 	ldr	r1,[sp,20]

00000478 e0866004    752 	add	r6,r6,r4

                     753 ;223:         fullResponseSize += requestResponseSize;		


                     754 

0000047c e0855001    755 	add	r5,r5,r1

                     756 ;222:         outBuf += requestResponseSize;        


                     757 

00000480 e0888004    758 	add	r8,r8,r4

                     759 .L46:

00000484 e155000b    760 	cmp	r5,fp

00000488 baffff2f    761 	blt	.L48

                     762 .L51:

                     763 ;224:     }


                     764 ;225: 


                     765 ;226: 	//TRACE("fullResponseSize = %d", fullResponseSize);


                     766 ;227: 	VERIFY(fullResponseSize <= maxBufSize)


                     767 ;228: 	


                     768 ;229:     return fullResponseSize;


                     769 

0000048c e1a00008    770 	mov	r0,r8

00000490 e28dd044    771 	add	sp,sp,68

00000494 e8bd8ff0    772 	ldmfd	[sp]!,{r4-fp,pc}

                     773 	.endf	handleConfirmedRequestPdu

                     774 	.align	4

                     775 ;inBufView	[sp,56]	local

                     776 ;outBufView	[sp,44]	local

                     777 ;mmsConn	r7	local

                     778 ;fullResponseSize	r8	local

                     779 ;requestResponseSize	r4	local

                     780 ;invokeId	[sp,12]	local

                     781 ;tag	[sp,16]	local

                     782 ;pktBufview	[sp,32]	local


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o01.s
                     783 ;length	[sp,20]	local

                     784 ;serviceOK	r0	local

                     785 ;.L347	.L352	static

                     786 ;.L348	.L353	static

                     787 

                     788 ;isoConn	r9	param

                     789 ;inBuf	r10	param

                     790 ;inBufPos	r5	param

                     791 ;inBufLen	fp	param

                     792 ;outBuf	r6	param

                     793 ;maxBufSize	[sp,108]	param

                     794 

                     795 	.section ".bss","awb"

                     796 .L346:

                     797 	.data

                     798 	.text

                     799 

                     800 ;230: }


                     801 	.align	4

                     802 ;g_vendor	.L31	static

                     803 ;g_model	.L32	static

                     804 ;g_revision	.L33	static

                     805 ;.L401	.L35	static

                     806 ;.L402	.L36	static

                     807 ;.L403	.L34	static

                     808 

                     809 	.data

                     810 	.ghsnote version,6

                     811 	.ghsnote tools,3

                     812 	.ghsnote options,0

                     813 	.text

                     814 	.align	4

                     815 	.data

                     816 	.align	4

                     817 	.section ".rodata","a"

                     818 	.align	4

                     819 	.text

