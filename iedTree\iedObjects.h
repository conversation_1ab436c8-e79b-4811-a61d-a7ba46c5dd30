#pragma once

#include "iedEntity.h"
#include "..\MmsConst.h"
#include "..\IsoConnectionForward.h"

#include <stdbool.h>

typedef enum {
	DO_SUBTYPE_DEFAULT = 0,
	DO_SUBTYPE_CONTROL
} DOSubType;


bool IEDLD_init(IEDEntity entity);
IEDEntity IEDLD_getDataSection(IEDEntity ld);
IEDEntity IEDLD_getDataSetSection(IEDEntity ld);

bool IEDComplexObj_init(IEDEntity entity);
bool IEDDO_init(IEDEntity entity);

//! Находит среди непосредственных детей timeStamp с именем t
//! и возвращает указатель на него.
//! Если не находит, возвращает NULL;
IEDEntity IEDDO_getTimeStampDA(IEDEntity entity);

bool IEDDA_init(IEDEntity entity);

bool IEDConstDA_calcReadLen(IEDEntity entity, size_t *pLen);
bool IEDConstDA_encodeRead(IEDEntity entity, BufferView *outBufView);

bool IEDVarDA_calcReadLen(IEDEntity entity, size_t *pLen);
bool IEDVarDA_encodeRead(IEDEntity entity, BufferView *outBuf);
MmsDataAccessError IEDVarDA_write(IEDEntity entity,
                                           IsoConnection* isoConn, BufferView* value);

bool IEDConstDA_encodeRead(IEDEntity entity, BufferView* outBuf);

MmsDataAccessError IEDComplexObj_write(IEDEntity entity,
                                           IsoConnection* isoConn, BufferView* value);

void IEDControlDA_checkTerminate(IEDEntity controlDA);

//! Выключает ожидание завершения операции и отключает
//! от соединения, которое эту операцию запистило.
//! Только для Oper
void IEDControlDA_disconnect(IEDEntity controlDA);

bool IEDControlDA_getOrIdent(IEDEntity entity, StringView *orIdent);

bool IEDControlDA_getOrCat(IEDEntity entity, int32_t *orCat);

bool IEDComplexObj_calcReadLen(IEDEntity entity, size_t* pLen);

bool IEDComplexObj_encodeRead(IEDEntity entity, BufferView* outBuf);
