                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ce41.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=BusError.c -o gh_ce41.o -list=BusError.lst C:\Users\<USER>\AppData\Local\Temp\gh_ce41.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_ce41.s
Source File: BusError.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile BusError.c -o

                      10 ;		BusError.o

                      11 ;Source File:   BusError.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:55:53 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "BusError.h"


                      21 ;2: 


                      22 ;3: #include "timers.h"


                      23 ;4: #include "netTools.h"


                      24 ;5: #include <debug.h>


                      25 ;6: 


                      26 ;7: // Выдержка времени в миллисекундах для выдачи ошибки шины


                      27 ;8: #define BUS_ERROR_TIMEOUT 5000


                      28 ;9: 


                      29 ;10: // Выдержка времени для признания ошибки шины


                      30 ;11: static volatile uint32_t _busErrorTimeOutCounter = 0;


                      31 ;12: 


                      32 ;13: static void busCheckCallBack(void)


                      33 	.text

                      34 	.align	4

                      35 busCheckCallBack:

00000000 e92d4010     36 	stmfd	[sp]!,{r4,lr}

00000004 e59f4060*    37 	ldr	r4,.L79

                      38 ;14: {


                      39 

                      40 ;15: 	if(NetTools_busOK())


                      41 

00000008 eb000000*    42 	bl	NetTools_busOK

0000000c e3500000     43 	cmp	r0,0

                      44 ;16: 	{


                      45 

                      46 ;17: 		if(_busErrorTimeOutCounter)


                      47 

                      48 ;18: 		{


                      49 

                      50 ;19: 			TRACE("Bus OK");



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ce41.s
                      51 ;20: 		}


                      52 ;21: 


                      53 ;22: 		_busErrorTimeOutCounter = 0;


                      54 

00000010 13a00000     55 	movne	r0,0

00000014 15840000     56 	strne	r0,[r4]

00000018 1a000007     57 	bne	.L2

0000001c e5941000     58 	ldr	r1,[r4]

                      59 ;23: 	}


                      60 ;24: 	else if(_busErrorTimeOutCounter < BUS_ERROR_TIMEOUT)


                      61 

00000020 e3a00d4c     62 	mov	r0,19<<8

00000024 e2800088     63 	add	r0,r0,136

00000028 e1510000     64 	cmp	r1,r0

                      65 ;25: 	{


                      66 

                      67 ;26: 		_busErrorTimeOutCounter++;


                      68 

0000002c 35940000     69 	ldrlo	r0,[r4]

00000030 32800001     70 	addlo	r0,r0,1

00000034 35840000     71 	strlo	r0,[r4]

                      72 ;27: 


                      73 ;28: 		if(_busErrorTimeOutCounter == BUS_ERROR_TIMEOUT)


                      74 

00000038 35940000     75 	ldrlo	r0,[r4]

                      76 .L2:

                      77 ;29: 		{


                      78 

0000003c e8bd4010     79 	ldmfd	[sp]!,{r4,lr}

00000040 e12fff1e*    80 	ret	

                      81 	.endf	busCheckCallBack

                      82 	.align	4

                      83 

                      84 	.section ".bss","awb"

                      85 .L62:

                      86 	.data

                      87 .L65:

00000000 00000000     88 _busErrorTimeOutCounter:	.data.b	0,0,0,0

                      89 	.type	_busErrorTimeOutCounter,$object

                      90 	.size	_busErrorTimeOutCounter,4

                      91 	.text

                      92 

                      93 ;30: 			TRACE("Bus error set");


                      94 ;31: 		}


                      95 ;32: 	}


                      96 ;33: }


                      97 

                      98 ;34: 


                      99 ;35: void BusError_init(void)


                     100 	.align	4

                     101 	.align	4

                     102 BusError_init::

00000044 e59f0024*   103 	ldr	r0,.L117

                     104 ;36: {


                     105 

                     106 ;37: 	Timers_setNetBusChek1msCallback(busCheckCallBack);


                     107 

00000048 ea000000*   108 	b	Timers_setNetBusChek1msCallback

                     109 	.endf	BusError_init

                     110 	.align	4

                     111 


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ce41.s
                     112 	.section ".bss","awb"

                     113 .L110:

                     114 	.data

                     115 	.text

                     116 

                     117 ;38: }


                     118 

                     119 ;39: 


                     120 ;40: bool BusError_check(void)


                     121 	.align	4

                     122 	.align	4

                     123 BusError_check::

0000004c e59fc018*   124 	ldr	r12,.L79

                     125 ;41: {


                     126 

                     127 ;42: 	bool result = _busErrorTimeOutCounter < BUS_ERROR_TIMEOUT;


                     128 

00000050 e3a01d4c    129 	mov	r1,19<<8

00000054 e59c0000    130 	ldr	r0,[r12]

00000058 e2811088    131 	add	r1,r1,136

0000005c e1500001    132 	cmp	r0,r1

00000060 33a00001    133 	movlo	r0,1

00000064 23a00000    134 	movhs	r0,0

                     135 ;43: 


                     136 ;44: 	return result;


                     137 

00000068 e12fff1e*   138 	ret	

                     139 	.endf	BusError_check

                     140 	.align	4

                     141 

                     142 	.data

                     143 	.text

                     144 

                     145 ;45: }


                     146 	.align	4

                     147 .L79:

0000006c 00000000*   148 	.data.w	.L65

                     149 	.type	.L79,$object

                     150 	.size	.L79,4

                     151 

                     152 .L117:

00000070 00000000*   153 	.data.w	busCheckCallBack

                     154 	.type	.L117,$object

                     155 	.size	.L117,4

                     156 

                     157 	.align	4

                     158 ;_busErrorTimeOutCounter	.L65	static

                     159 

                     160 	.data

                     161 	.ghsnote version,6

                     162 	.ghsnote tools,3

                     163 	.ghsnote options,0

                     164 	.text

                     165 	.align	4

                     166 	.data

                     167 	.align	4

                     168 	.text

