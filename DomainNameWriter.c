#include "DomainNameWriter.h"
#include "AsnEncoding.h"
#include <string.h>

static void processFastForward(DomainNameWriter* self)
{
	int currNameEnd;
	if (self->stackDepth == -1)
	{
		return;
	}
	currNameEnd = self->nameEndStack[self->stackDepth];
	if (currNameEnd == self->startingNameSize
		&& memcmp(self->currDomainName, self->startingName, currNameEnd) == 0)
	{
		self->fastForward = FALSE;
	}
}

void DomainNameWriter_init(DomainNameWriter* self,
	uint8_t* outBuf, int outBufSize)
{
	self->stackDepth = -1;
	self->currDomainName[0] = 0;
	self->outBuf = outBuf;
	self->totalSize = 0;
	self->outBufSize = outBufSize;
	self->totalSize = 0;
	self->bufferFull = FALSE;
	self->fastForward = FALSE;
}

void DomainNameWriter_setStartName(DomainNameWriter* self, 
	uint8_t* startName, int startNameSize)
{
	memcpy(self->startingName, startName, startNameSize);
	self->startingName[startNameSize] = 0;
	self->startingNameSize = startNameSize;
	self->fastForward = TRUE;
}

void DomainNameWriter_pushName(DomainNameWriter* self, 
	uint8_t* name, int nameLen)
{	
	int currNameEnd = 0;
	if (self->stackDepth >= 0)
	{
		currNameEnd = self->nameEndStack[self->stackDepth];
		self->currDomainName[currNameEnd++] = '$';
	}
		
	memcpy(self->currDomainName + currNameEnd, name, nameLen);

	currNameEnd += nameLen;
	self->currDomainName[currNameEnd] = 0;	

	self->stackDepth += 1;
	self->nameEndStack[self->stackDepth] = currNameEnd;
}

void DomainNameWriter_discardName(DomainNameWriter* self)
{
	if (self->stackDepth < 0)
	{
		return;
	}
	self->stackDepth--;
	if (self->stackDepth < 0)
	{
		self->currDomainName[0] = 0;
	}
	else
	{
		int currNameEnd = self->nameEndStack[self->stackDepth];
		self->currDomainName[currNameEnd] = 0;
	}
}

int DomainNameWriter_encode(DomainNameWriter* self)
{		
	int encodedSize;
	if (self->bufferFull)
	{
		return 0;
	}

	if (self->fastForward)
	{
		processFastForward(self);
		return 0;
	}

	encodedSize = BerEncoder_determineEncodedStringSize(
		(char*)self->currDomainName);

	if (self->totalSize + encodedSize > self->outBufSize)
	{
		self->bufferFull = TRUE;
		return 0;
	}

	if (self->outBuf != NULL)
	{
		self->totalSize = BerEncoder_encodeStringWithTL(ASN_VISIBLE_STRING,
			(char*)self->currDomainName, self->outBuf, self->totalSize);
	}
	else
	{
		self->totalSize += encodedSize;
	}
			
	return encodedSize;
}
