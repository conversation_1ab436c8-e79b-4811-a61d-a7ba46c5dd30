#pragma once

#include "MmsConst.h"
#include "IsoConnectionForward.h"

#include <stdint.h>


void writeBoolean(void* descrStruct, uint8_t* dataToWrite);
void writeCodedEnum(void* descrStruct, uint8_t* dataToWrite);
MmsDataAccessError writeFloatSett(void* descrStruct, uint8_t* dataToWrite);
MmsDataAccessError writeRealSett(void* descrStruct, uint8_t* dataToWrite);
MmsDataAccessError writeIntSett(void* descrStruct, uint8_t* dataToWrite);

int mms_handleWriteRequest(IsoConnection* isoConn,
        unsigned char* inBuf, int bufPos, int maxBufPos, unsigned int invokeId,
        unsigned char* response);

