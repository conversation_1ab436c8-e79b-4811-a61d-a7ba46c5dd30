                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4681.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=iedBool.c -o iedTree\gh_4681.o -list=iedTree/iedBool.lst C:\Users\<USER>\AppData\Local\Temp\gh_4681.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_4681.s
Source File: iedBool.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      10 ;		iedTree/iedBool.c -o iedTree/iedBool.o

                      11 ;Source File:   iedTree/iedBool.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Oct 07 09:39:20 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "iedBool.h"


                      21 ;2: 


                      22 ;3: #include "iedFinalDA.h"


                      23 ;4: #include "iedTree.h"


                      24 ;5: #include "../AsnEncoding.h"


                      25 ;6: #include "../DataSlice.h"


                      26 ;7: #include "debug.h"


                      27 ;8: 


                      28 ;9: #include "IEDCompile/AccessInfo.h"


                      29 ;10: 


                      30 ;11: #define BOOL_ENCODED_SIZE 3


                      31 ;12: 


                      32 ;13: static bool calcReadLen(IEDEntity entity, size_t* pLen )


                      33 	.text

                      34 	.align	4

                      35 calcReadLen:

                      36 ;14: {


                      37 

                      38 ;15: 	*pLen = BOOL_ENCODED_SIZE;


                      39 

00000000 e3a00003     40 	mov	r0,3

00000004 e5810000     41 	str	r0,[r1]

                      42 ;16: 	return true;


                      43 

00000008 e3a00001     44 	mov	r0,1

0000000c e12fff1e*    45 	ret	

                      46 	.endf	calcReadLen

                      47 	.align	4

                      48 

                      49 ;entity	none	param

                      50 ;pLen	r1	param


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4681.s
                      51 

                      52 	.section ".bss","awb"

                      53 .L30:

                      54 	.data

                      55 	.text

                      56 

                      57 ;17: }


                      58 

                      59 ;18: 


                      60 ;19: static bool encodeRead(IEDEntity entity, BufferView* outBuf)


                      61 	.align	4

                      62 	.align	4

                      63 encodeRead:

00000010 e92d4000     64 	stmfd	[sp]!,{lr}

                      65 ;20: {			


                      66 

                      67 ;21: 	bool value = entity->boolValue;


                      68 

00000014 e5d02030     69 	ldrb	r2,[r0,48]

00000018 e1a00001     70 	mov	r0,r1

0000001c e3a01083     71 	mov	r1,131

00000020 eb000000*    72 	bl	BufferView_encodeBoolean

00000024 e3500000     73 	cmp	r0,0

00000028 13a00001     74 	movne	r0,1

                      75 ;22: 


                      76 ;23: 	if(!BufferView_encodeBoolean(outBuf, IEC61850_BER_BOOLEAN, value))


                      77 

                      78 ;24: 	{


                      79 

                      80 ;25: 		ERROR_REPORT("Error encoding bool");


                      81 ;26: 		return false;


                      82 

                      83 ;27: 	}


                      84 ;28: 	return true;


                      85 

0000002c e8bd4000     86 	ldmfd	[sp]!,{lr}

00000030 e12fff1e*    87 	ret	

                      88 	.endf	encodeRead

                      89 	.align	4

                      90 ;value	r2	local

                      91 

                      92 ;entity	r0	param

                      93 ;outBuf	r1	param

                      94 

                      95 	.section ".bss","awb"

                      96 .L72:

                      97 	.data

                      98 	.text

                      99 

                     100 ;29: }


                     101 

                     102 ;30: 


                     103 ;31: static void updateFromDataSlice(IEDEntity entity)


                     104 	.align	4

                     105 	.align	4

                     106 updateFromDataSlice:

00000034 e92d4030    107 	stmfd	[sp]!,{r4-r5,lr}

00000038 e1a05000    108 	mov	r5,r0

                     109 ;32: {


                     110 

                     111 ;33: 	int offset  = entity->dataSliceOffset;



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4681.s
                     112 

0000003c e595002c    113 	ldr	r0,[r5,44]

                     114 ;34: 	bool value = offset != -1 && DataSlice_getBoolFastCurrDS(offset);


                     115 

00000040 e3a04000    116 	mov	r4,0

00000044 e3700001    117 	cmn	r0,1

00000048 0a000004    118 	beq	.L82

0000004c e1a00800    119 	mov	r0,r0 lsl 16

00000050 e1a00820    120 	mov	r0,r0 lsr 16

00000054 eb000000*   121 	bl	DataSlice_getBoolFastCurrDS

00000058 e3500000    122 	cmp	r0,0

0000005c 13a04001    123 	movne	r4,1

                     124 .L82:

00000060 e5d50030    125 	ldrb	r0,[r5,48]

00000064 e20410ff    126 	and	r1,r4,255

                     127 ;35: 


                     128 ;36: 	if(entity->boolValue == value)


                     129 

00000068 e1500001    130 	cmp	r0,r1

                     131 ;37: 	{


                     132 

                     133 ;38: 		entity->changed = TRGOP_NONE;


                     134 

0000006c 03a00000    135 	moveq	r0,0

00000070 05850028    136 	streq	r0,[r5,40]

00000074 0a000007    137 	beq	.L79

                     138 ;39: 	}


                     139 ;40: 	else


                     140 ;41: 	{


                     141 

                     142 ;42: 		entity->changed = entity->trgOps;


                     143 

00000078 e5950024    144 	ldr	r0,[r5,36]

0000007c e5c51030    145 	strb	r1,[r5,48]

                     146 ;44: 		IEDEntity_setTimeStamp(entity, dataSliceGetTimeStamp());


                     147 

00000080 e5850028    148 	str	r0,[r5,40]

                     149 ;43: 		entity->boolValue = value;


                     150 

00000084 eb000000*   151 	bl	dataSliceGetTimeStamp

00000088 e1a02001    152 	mov	r2,r1

0000008c e1a01000    153 	mov	r1,r0

00000090 e1a00005    154 	mov	r0,r5

00000094 eb000000*   155 	bl	IEDEntity_setTimeStamp

                     156 .L79:

00000098 e8bd4030    157 	ldmfd	[sp]!,{r4-r5,lr}

0000009c e12fff1e*   158 	ret	

                     159 	.endf	updateFromDataSlice

                     160 	.align	4

                     161 ;offset	r0	local

                     162 ;value	r1	local

                     163 

                     164 ;entity	r5	param

                     165 

                     166 	.section ".bss","awb"

                     167 .L150:

                     168 	.data

                     169 	.text

                     170 

                     171 ;45: 	}


                     172 ;46: }



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4681.s
                     173 

                     174 ;47: 


                     175 ;48: void IEDBool_init(IEDEntity entity)


                     176 	.align	4

                     177 	.align	4

                     178 IEDBool_init::

000000a0 e92d4070    179 	stmfd	[sp]!,{r4-r6,lr}

000000a4 e280405c    180 	add	r4,r0,92

000000a8 e5140004    181 	ldr	r0,[r4,-4]

                     182 ;51: 	IntBoolAccessInfo* accessInfo = extInfo->accessInfo;


                     183 

000000ac e5900000    184 	ldr	r0,[r0]

                     185 ;52: 


                     186 ;53: 	//Если будет ошибка, то запишется -1;


                     187 ;54: 	entity->dataSliceOffset = DataSlice_getBoolOffset(accessInfo->valueOffset);


                     188 

000000b0 e59f5028*   189 	ldr	r5,.L197

000000b4 e5900004    190 	ldr	r0,[r0,4]

000000b8 e59f6024*   191 	ldr	r6,.L198

                     192 ;49: {


                     193 

                     194 ;50: 	TerminalItem* extInfo = entity->extInfo;


                     195 

000000bc eb000000*   196 	bl	DataSlice_getBoolOffset

000000c0 e5040030    197 	str	r0,[r4,-48]

                     198 ;55: 


                     199 ;56: 	entity->calcReadLen = calcReadLen;


                     200 

                     201 ;57: 	entity->encodeRead = encodeRead;


                     202 

000000c4 e1a00006    203 	mov	r0,r6

000000c8 e8840021    204 	stmea	[r4],{r0,r5}

                     205 ;58: 	entity->updateFromDataSlice = updateFromDataSlice;


                     206 

000000cc e59f0014*   207 	ldr	r0,.L199

000000d0 e584000c    208 	str	r0,[r4,12]

                     209 ;59: 


                     210 ;60: 	IEDTree_addToCmpList(entity);


                     211 

000000d4 e244005c    212 	sub	r0,r4,92

000000d8 e8bd4070    213 	ldmfd	[sp]!,{r4-r6,lr}

000000dc ea000000*   214 	b	IEDTree_addToCmpList

                     215 	.endf	IEDBool_init

                     216 	.align	4

                     217 ;extInfo	r0	local

                     218 ;accessInfo	r0	local

                     219 

                     220 ;entity	r4	param

                     221 

                     222 	.section ".bss","awb"

                     223 .L190:

                     224 	.data

                     225 	.text

                     226 

                     227 ;61: }


                     228 	.align	4

                     229 .L197:

000000e0 00000000*   230 	.data.w	calcReadLen

                     231 	.type	.L197,$object

                     232 	.size	.L197,4

                     233 


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4681.s
                     234 .L198:

000000e4 00000000*   235 	.data.w	encodeRead

                     236 	.type	.L198,$object

                     237 	.size	.L198,4

                     238 

                     239 .L199:

000000e8 00000000*   240 	.data.w	updateFromDataSlice

                     241 	.type	.L199,$object

                     242 	.size	.L199,4

                     243 

                     244 	.align	4

                     245 

                     246 	.data

                     247 	.ghsnote version,6

                     248 	.ghsnote tools,3

                     249 	.ghsnote options,0

                     250 	.text

                     251 	.align	4

