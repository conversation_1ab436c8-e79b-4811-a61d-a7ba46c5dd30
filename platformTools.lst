                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bcg1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=platformTools.c -o gh_bcg1.o -list=platformTools.lst C:\Users\<USER>\AppData\Local\Temp\gh_bcg1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_bcg1.s
Source File: platformTools.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      10 ;		platformTools.c -o platformTools.o

                      11 ;Source File:   platformTools.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:55:51 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "platformTools.h"


                      21 ;2: #include "Clib.h"


                      22 ;3: 


                      23 ;4: int PTools_lockInterrupt(void)


                      24 	.text

                      25 	.align	4

                      26 PTools_lockInterrupt::

                      27 ;5: {


                      28 

                      29 ;6:     return lockInterrupt();


                      30 

00000000 ea000000*    31 	b	lockInterrupt

                      32 	.endf	PTools_lockInterrupt

                      33 	.align	4

                      34 

                      35 	.section ".bss","awb"

                      36 .L30:

                      37 	.data

                      38 	.text

                      39 

                      40 ;7: }


                      41 

                      42 ;8: 


                      43 ;9: void PTools_unlockInterrupt(int previousInterruptState)


                      44 	.align	4

                      45 	.align	4

                      46 PTools_unlockInterrupt::

                      47 ;10: {


                      48 

                      49 ;11:     unlockInterrupt(previousInterruptState);


                      50 


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bcg1.s
00000004 ea000000*    51 	b	unlockInterrupt

                      52 	.endf	PTools_unlockInterrupt

                      53 	.align	4

                      54 

                      55 ;previousInterruptState	none	param

                      56 

                      57 	.section ".bss","awb"

                      58 .L62:

                      59 	.data

                      60 	.text

                      61 

                      62 ;12: }


                      63 	.align	4

                      64 

                      65 	.data

                      66 	.ghsnote version,6

                      67 	.ghsnote tools,3

                      68 	.ghsnote options,0

                      69 	.text

                      70 	.align	4

