#pragma once

//Комментарий для проверки кодировки

#include <stdint.h>

#include "out_buffers.h"
#include "IsoConnectionForward.h"
#include "ReportQueue.h"
#include "iedTree/iedEntity.h"
#include "iedTree/DataSet.h"
#include "reportItems\RptDataSet.h"


struct ReporterStruct {
	RCB rcb;
	//! Счётчик миллисекунд для Integrity 
	uint32_t intgPdCounter;
	//! Сработал таймер Integrity
	bool intgTimerAlam;	
	IEDEntity dataSetEntity;	
	//! Набор данных
	DataSet *dataSet;
	//! Информация состоянии набора данных применительно к этому RCB
	RptDataSet rptDataSet; 
	IsoConnection* connection;	
	ReportQueue buffer;
	SessionOutBuffer sessionOutBuffer;
};

typedef struct ReporterStruct Reporter;

