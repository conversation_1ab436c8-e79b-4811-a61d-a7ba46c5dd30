                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8e41.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=DomainNameWriter.c -o gh_8e41.o -list=DomainNameWriter.lst C:\Users\<USER>\AppData\Local\Temp\gh_8e41.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_8e41.s
Source File: DomainNameWriter.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      10 ;		DomainNameWriter.c -o DomainNameWriter.o

                      11 ;Source File:   DomainNameWriter.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:56:04 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "DomainNameWriter.h"


                      21 ;2: #include "AsnEncoding.h"


                      22 ;3: #include <string.h>


                      23 ;4: 


                      24 ;5: static void processFastForward(DomainNameWriter* self)


                      25 

                      26 ;17: 	}


                      27 ;18: }


                      28 

                      29 ;19: 


                      30 ;20: void DomainNameWriter_init(DomainNameWriter* self,


                      31 	.text

                      32 	.align	4

                      33 DomainNameWriter_init::

                      34 ;21: 	uint8_t* outBuf, int outBufSize)


                      35 ;22: {


                      36 

                      37 ;23: 	self->stackDepth = -1;


                      38 

00000000 e3e0c000     39 	mvn	r12,0

                      40 ;24: 	self->currDomainName[0] = 0;


                      41 

00000004 e3a03000     42 	mov	r3,0

00000008 e5c0303c     43 	strb	r3,[r0,60]

                      44 ;25: 	self->outBuf = outBuf;


                      45 

0000000c e480100c     46 	str	r1,[r0],12

                      47 ;26: 	self->totalSize = 0;


                      48 

                      49 ;27: 	self->outBufSize = outBufSize;


                      50 


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8e41.s
00000010 e8801004     51 	stmea	[r0],{r2,r12}

                      52 ;28: 	self->totalSize = 0;


                      53 

00000014 e5003008     54 	str	r3,[r0,-8]

                      55 ;29: 	self->bufferFull = FALSE;


                      56 

00000018 e5403004     57 	strb	r3,[r0,-4]

                      58 ;30: 	self->fastForward = FALSE;


                      59 

0000001c e5c030b1     60 	strb	r3,[r0,177]

00000020 e12fff1e*    61 	ret	

                      62 	.endf	DomainNameWriter_init

                      63 	.align	4

                      64 

                      65 ;self	r0	param

                      66 ;outBuf	r1	param

                      67 ;outBufSize	r2	param

                      68 

                      69 	.section ".bss","awb"

                      70 .L49:

                      71 	.data

                      72 	.text

                      73 

                      74 ;31: }


                      75 

                      76 ;32: 


                      77 ;33: void DomainNameWriter_setStartName(DomainNameWriter* self, 


                      78 	.align	4

                      79 	.align	4

                      80 DomainNameWriter_setStartName::

00000024 e92d4070     81 	stmfd	[sp]!,{r4-r6,lr}

                      82 ;34: 	uint8_t* startName, int startNameSize)


                      83 ;35: {


                      84 

                      85 ;36: 	memcpy(self->startingName, startName, startNameSize);


                      86 

00000028 e1a05002     87 	mov	r5,r2

0000002c e1a04000     88 	mov	r4,r0

00000030 e28460be     89 	add	r6,r4,190

00000034 e1a00006     90 	mov	r0,r6

00000038 eb000000*    91 	bl	memcpy

                      92 ;37: 	self->startingName[startNameSize] = 0;


                      93 

0000003c e3a01000     94 	mov	r1,0

00000040 e7c51006     95 	strb	r1,[r5,r6]

                      96 ;38: 	self->startingNameSize = startNameSize;


                      97 

00000044 e5845140     98 	str	r5,[r4,320]

                      99 ;39: 	self->fastForward = TRUE;


                     100 

00000048 e3a00001    101 	mov	r0,1

0000004c e5c400bd    102 	strb	r0,[r4,189]

00000050 e8bd8070    103 	ldmfd	[sp]!,{r4-r6,pc}

                     104 	.endf	DomainNameWriter_setStartName

                     105 	.align	4

                     106 

                     107 ;self	r4	param

                     108 ;startName	none	param

                     109 ;startNameSize	r5	param

                     110 

                     111 	.section ".bss","awb"


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8e41.s
                     112 .L81:

                     113 	.data

                     114 	.text

                     115 

                     116 ;40: }


                     117 

                     118 ;41: 


                     119 ;42: void DomainNameWriter_pushName(DomainNameWriter* self, 


                     120 	.align	4

                     121 	.align	4

                     122 DomainNameWriter_pushName::

00000054 e92d40f0    123 	stmfd	[sp]!,{r4-r7,lr}

                     124 ;43: 	uint8_t* name, int nameLen)


                     125 ;44: {	


                     126 

00000058 e1a06002    127 	mov	r6,r2

0000005c e1a05000    128 	mov	r5,r0

00000060 e5950010    129 	ldr	r0,[r5,16]

00000064 e3a04000    130 	mov	r4,0

                     131 ;45: 	int currNameEnd = 0;


                     132 

                     133 ;46: 	if (self->stackDepth >= 0)


                     134 

00000068 e3500000    135 	cmp	r0,0

0000006c ba000005    136 	blt	.L90

                     137 ;47: 	{


                     138 

                     139 ;48: 		currNameEnd = self->nameEndStack[self->stackDepth];


                     140 

00000070 e0850100    141 	add	r0,r5,r0 lsl 2

00000074 e5904014    142 	ldr	r4,[r0,20]

                     143 ;49: 		self->currDomainName[currNameEnd++] = '$';


                     144 

00000078 e3a02024    145 	mov	r2,36

0000007c e0840005    146 	add	r0,r4,r5

00000080 e2844001    147 	add	r4,r4,1

00000084 e5c0203c    148 	strb	r2,[r0,60]

                     149 .L90:

                     150 ;50: 	}


                     151 ;51: 		


                     152 ;52: 	memcpy(self->currDomainName + currNameEnd, name, nameLen);


                     153 

00000088 e1a02006    154 	mov	r2,r6

0000008c e285703c    155 	add	r7,r5,60

00000090 e0840007    156 	add	r0,r4,r7

00000094 eb000000*   157 	bl	memcpy

                     158 ;53: 


                     159 ;54: 	currNameEnd += nameLen;


                     160 

00000098 e0842006    161 	add	r2,r4,r6

                     162 ;55: 	self->currDomainName[currNameEnd] = 0;	


                     163 

0000009c e3a01000    164 	mov	r1,0

000000a0 e7c21007    165 	strb	r1,[r2,r7]

                     166 ;56: 


                     167 ;57: 	self->stackDepth += 1;


                     168 

000000a4 e5950010    169 	ldr	r0,[r5,16]

000000a8 e2800001    170 	add	r0,r0,1

000000ac e5850010    171 	str	r0,[r5,16]

                     172 ;58: 	self->nameEndStack[self->stackDepth] = currNameEnd;



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8e41.s
                     173 

000000b0 e0850100    174 	add	r0,r5,r0 lsl 2

000000b4 e5802014    175 	str	r2,[r0,20]

000000b8 e8bd80f0    176 	ldmfd	[sp]!,{r4-r7,pc}

                     177 	.endf	DomainNameWriter_pushName

                     178 	.align	4

                     179 ;currNameEnd	r4	local

                     180 

                     181 ;self	r5	param

                     182 ;name	r1	param

                     183 ;nameLen	r6	param

                     184 

                     185 	.section ".bss","awb"

                     186 .L130:

                     187 	.data

                     188 	.text

                     189 

                     190 ;59: }


                     191 

                     192 ;60: 


                     193 ;61: void DomainNameWriter_discardName(DomainNameWriter* self)


                     194 	.align	4

                     195 	.align	4

                     196 DomainNameWriter_discardName::

                     197 ;62: {


                     198 

                     199 ;63: 	if (self->stackDepth < 0)


                     200 

000000bc e5901010    201 	ldr	r1,[r0,16]

000000c0 e3510000    202 	cmp	r1,0

000000c4 ba000007    203 	blt	.L141

                     204 ;64: 	{


                     205 

                     206 ;65: 		return;


                     207 

                     208 ;66: 	}


                     209 ;67: 	self->stackDepth--;


                     210 

000000c8 e2511001    211 	subs	r1,r1,1

000000cc e5801010    212 	str	r1,[r0,16]

                     213 ;68: 	if (self->stackDepth < 0)


                     214 

                     215 ;69: 	{


                     216 

                     217 ;70: 		self->currDomainName[0] = 0;


                     218 

000000d0 5280203c    219 	addpl	r2,r0,60

                     220 ;71: 	}


                     221 ;72: 	else


                     222 ;73: 	{


                     223 

                     224 ;74: 		int currNameEnd = self->nameEndStack[self->stackDepth];


                     225 

000000d4 50800101    226 	addpl	r0,r0,r1 lsl 2

000000d8 55900014    227 	ldrpl	r0,[r0,20]

                     228 ;75: 		self->currDomainName[currNameEnd] = 0;


                     229 

000000dc e3a01000    230 	mov	r1,0

000000e0 45c0103c    231 	strmib	r1,[r0,60]

000000e4 57c01002    232 	strplb	r1,[r0,r2]

                     233 .L141:


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8e41.s
000000e8 e12fff1e*   234 	ret	

                     235 	.endf	DomainNameWriter_discardName

                     236 	.align	4

                     237 ;currNameEnd	r0	local

                     238 

                     239 ;self	r0	param

                     240 

                     241 	.section ".bss","awb"

                     242 .L209:

                     243 	.data

                     244 	.text

                     245 

                     246 ;76: 	}


                     247 ;77: }


                     248 

                     249 ;78: 


                     250 ;79: int DomainNameWriter_encode(DomainNameWriter* self)


                     251 	.align	4

                     252 	.align	4

                     253 DomainNameWriter_encode::

000000ec e92d4030    254 	stmfd	[sp]!,{r4-r5,lr}

000000f0 e1a05000    255 	mov	r5,r0

                     256 ;80: {		


                     257 

                     258 ;81: 	int encodedSize;


                     259 ;82: 	if (self->bufferFull)


                     260 

000000f4 e5d50008    261 	ldrb	r0,[r5,8]

000000f8 e3500000    262 	cmp	r0,0

000000fc 1a00000f    263 	bne	.L232

                     264 ;83: 	{


                     265 

                     266 ;84: 		return 0;


                     267 

                     268 ;85: 	}


                     269 ;86: 


                     270 ;87: 	if (self->fastForward)


                     271 

00000100 e5d500bd    272 	ldrb	r0,[r5,189]

00000104 e3500000    273 	cmp	r0,0

00000108 0a00000e    274 	beq	.L230

                     275 ;88: 	{


                     276 

                     277 ;89: 		processFastForward(self);


                     278 

                     279 ;6: {


                     280 

                     281 ;7: 	int currNameEnd;


                     282 ;8: 	if (self->stackDepth == -1)


                     283 

0000010c e5950010    284 	ldr	r0,[r5,16]

00000110 e3700001    285 	cmn	r0,1

00000114 0a000009    286 	beq	.L232

                     287 ;9: 	{


                     288 

                     289 ;10: 		return;


                     290 

                     291 ;11: 	}


                     292 ;12: 	currNameEnd = self->nameEndStack[self->stackDepth];


                     293 

00000118 e0850100    294 	add	r0,r5,r0 lsl 2


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8e41.s
0000011c e5902014    295 	ldr	r2,[r0,20]

                     296 ;13: 	if (currNameEnd == self->startingNameSize


                     297 

00000120 e5950140    298 	ldr	r0,[r5,320]

00000124 e1520000    299 	cmp	r2,r0

00000128 1a000004    300 	bne	.L232

0000012c e28510be    301 	add	r1,r5,190

00000130 e285003c    302 	add	r0,r5,60

00000134 eb000000*   303 	bl	memcmp

00000138 e3500000    304 	cmp	r0,0

                     305 ;14: 		&& memcmp(self->currDomainName, self->startingName, currNameEnd) == 0)


                     306 ;15: 	{


                     307 

                     308 ;16: 		self->fastForward = FALSE;


                     309 

0000013c 05c500bd    310 	streqb	r0,[r5,189]

                     311 .L232:

                     312 ;90: 		return 0;


                     313 

00000140 e3a00000    314 	mov	r0,0

00000144 ea000014    315 	b	.L225

                     316 .L230:

                     317 ;91: 	}


                     318 ;92: 


                     319 ;93: 	encodedSize = BerEncoder_determineEncodedStringSize(


                     320 

00000148 e285003c    321 	add	r0,r5,60

0000014c eb000000*   322 	bl	BerEncoder_determineEncodedStringSize

00000150 e1a04000    323 	mov	r4,r0

                     324 ;94: 		(char*)self->currDomainName);


                     325 ;95: 


                     326 ;96: 	if (self->totalSize + encodedSize > self->outBufSize)


                     327 

00000154 e5953004    328 	ldr	r3,[r5,4]

00000158 e595100c    329 	ldr	r1,[r5,12]

0000015c e0840003    330 	add	r0,r4,r3

00000160 e1500001    331 	cmp	r0,r1

                     332 ;97: 	{


                     333 

                     334 ;98: 		self->bufferFull = TRUE;


                     335 

00000164 c3a00001    336 	movgt	r0,1

00000168 c5c50008    337 	strgtb	r0,[r5,8]

                     338 ;99: 		return 0;


                     339 

0000016c c3a00000    340 	movgt	r0,0

00000170 ca000009    341 	bgt	.L225

                     342 ;100: 	}


                     343 ;101: 


                     344 ;102: 	if (self->outBuf != NULL)


                     345 

00000174 e5952000    346 	ldr	r2,[r5]

00000178 e3520000    347 	cmp	r2,0

                     348 ;105: 			(char*)self->currDomainName, self->outBuf, self->totalSize);


                     349 ;106: 	}


                     350 ;107: 	else


                     351 ;108: 	{


                     352 

                     353 ;109: 		self->totalSize += encodedSize;


                     354 

                     355 ;110: 	}



                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8e41.s
                     356 ;111: 			


                     357 ;112: 	return encodedSize;


                     358 

0000017c 05850004    359 	streq	r0,[r5,4]

00000180 01a00004    360 	moveq	r0,r4

00000184 0a000004    361 	beq	.L225

                     362 ;103: 	{


                     363 

                     364 ;104: 		self->totalSize = BerEncoder_encodeStringWithTL(ASN_VISIBLE_STRING,


                     365 

00000188 e285103c    366 	add	r1,r5,60

0000018c e3a0001a    367 	mov	r0,26

00000190 eb000000*   368 	bl	BerEncoder_encodeStringWithTL

                     369 ;110: 	}


                     370 ;111: 			


                     371 ;112: 	return encodedSize;


                     372 

00000194 e5850004    373 	str	r0,[r5,4]

00000198 e1a00004    374 	mov	r0,r4

                     375 .L225:

0000019c e8bd8030    376 	ldmfd	[sp]!,{r4-r5,pc}

                     377 	.endf	DomainNameWriter_encode

                     378 	.align	4

                     379 ;encodedSize	r4	local

                     380 ;currNameEnd	r2	local

                     381 

                     382 ;self	r5	param

                     383 

                     384 	.section ".bss","awb"

                     385 .L403:

                     386 	.data

                     387 	.text

                     388 

                     389 ;113: }


                     390 	.align	4

                     391 

                     392 	.data

                     393 	.ghsnote version,6

                     394 	.ghsnote tools,3

                     395 	.ghsnote options,0

                     396 	.text

                     397 	.align	4

