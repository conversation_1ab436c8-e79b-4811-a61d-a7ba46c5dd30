                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1us1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=mms_write.c -o gh_1us1.o -list=mms_write.lst C:\Users\<USER>\AppData\Local\Temp\gh_1us1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_1us1.s
Source File: mms_write.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile mms_write.c -o

                      10 ;		mms_write.o

                      11 ;Source File:   mms_write.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:56:02 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "mms_write.h"


                      21 ;2: #include "debug.h"


                      22 ;3: #include "AsnEncoding.h"


                      23 ;4: #include "mms.h"


                      24 ;5: #include "iedmodel.h"


                      25 ;6: #include "mms_rcb.h"


                      26 ;7: #include "mms_gocb.h"


                      27 ;8: #include "mms_data.h"


                      28 ;9: #include "pwin_access.h"


                      29 ;10: #include "control.h"


                      30 ;11: #include "iedTree/iedTree.h"


                      31 ;12: #include "IEDCompile/InnerAttributeTypes.h"


                      32 ;13: #include "IEDCompile/AccessInfo.h"


                      33 ;14: #include <types.h>


                      34 ;15: #include <stddef.h>


                      35 ;16: 


                      36 ;17: static size_t calcAccessErrEncodedSize(MmsDataAccessError* errList,


                      37 

                      38 ;34: }


                      39 

                      40 ;35: 


                      41 ;36: static size_t encodeAccessErrors(MmsDataAccessError* errList,


                      42 

                      43 ;55: }


                      44 

                      45 ;56: 


                      46 ;57: MmsDataAccessError writeFloatSett(void* descrStruct, uint8_t* dataToWrite)


                      47 ;58: {


                      48 ;59: 	FloatAccsessInfo* pSettingInfo = descrStruct;


                      49 ;60: 	float value;


                      50 ;61: 	int intVal;



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1us1.s
                      51 ;62: 


                      52 ;63: 	if(!pSettingInfo->flags & ACCESS_FLAG_WRITABLE)


                      53 ;64: 	{


                      54 ;65: 		return DATA_ACCESS_ERROR_OBJECT_ACCESS_DENIED;


                      55 ;66: 	}


                      56 ;67: 	


                      57 ;68: 	if (dataToWrite[0] != IEC61850_BER_FLOAT)


                      58 ;69: 	{


                      59 ;70: 		return DATA_ACCESS_ERROR_OBJECT_ACCESS_UNSUPPORTED;


                      60 ;71: 	}


                      61 ;72: 	


                      62 ;73: 	value = BerDecoder_decodeFloat(dataToWrite, 2);


                      63 ;74:     intVal = (int)(value / pSettingInfo->multiplier);


                      64 ;75:     if(!pwaWriteFloatSett(pSettingInfo->valueOffset, intVal))


                      65 ;76: 	{


                      66 ;77: 		return DATA_ACCESS_ERROR_TYPE_INCONSISTENT;


                      67 ;78: 	}


                      68 ;79: 	return DATA_ACCESS_ERROR_SUCCESS;


                      69 ;80: }


                      70 ;81: 


                      71 ;82: MmsDataAccessError writeRealSett(void* descrStruct, uint8_t* dataToWrite)


                      72 ;83: {	


                      73 ;84:     FloatAccsessInfo* pSettingInfo = descrStruct;


                      74 ;85:     float value;


                      75 ;86: 


                      76 ;87: 	if(!pSettingInfo->flags & ACCESS_FLAG_WRITABLE)


                      77 ;88: 	{


                      78 ;89: 		return DATA_ACCESS_ERROR_OBJECT_ACCESS_DENIED;


                      79 ;90: 	}


                      80 ;91: 


                      81 ;92:     if (dataToWrite[0] != IEC61850_BER_FLOAT)


                      82 ;93:     {


                      83 ;94:         return DATA_ACCESS_ERROR_OBJECT_ACCESS_UNSUPPORTED;


                      84 ;95:     }


                      85 ;96:     value = BerDecoder_decodeFloat(dataToWrite, 2);


                      86 ;97:     value /= pSettingInfo->multiplier;


                      87 ;98: 	if(!pwaWriteRealSett(pSettingInfo->valueOffset, value))


                      88 ;99: 	{


                      89 ;100: 		return DATA_ACCESS_ERROR_TYPE_INCONSISTENT;


                      90 ;101: 	}


                      91 ;102: 	return DATA_ACCESS_ERROR_SUCCESS;    


                      92 ;103: }


                      93 ;104: 


                      94 ;105: MmsDataAccessError writeIntSett(void* descrStruct, uint8_t* dataToWrite)


                      95 ;106: {	


                      96 ;107: 	IntBoolAccessInfo* pAccessInfo = descrStruct;


                      97 ;108: 	uint32_t value;


                      98 ;109: 	int len;


                      99 ;110: 


                     100 ;111: 	if(!pAccessInfo->flags & ACCESS_FLAG_WRITABLE)


                     101 ;112: 	{


                     102 ;113: 		return DATA_ACCESS_ERROR_OBJECT_ACCESS_DENIED;


                     103 ;114: 	}


                     104 ;115: 


                     105 ;116: 	if (dataToWrite[0] != IEC61850_BER_INTEGER


                     106 ;117: 		&& dataToWrite[0] != IEC61850_BER_UNSIGNED_INTEGER)


                     107 ;118: 	{


                     108 ;119: 		return DATA_ACCESS_ERROR_OBJECT_ACCESS_UNSUPPORTED;


                     109 ;120: 	}


                     110 ;121: 	len = dataToWrite[1];


                     111 ;122: 	if (len > 8)



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1us1.s
                     112 ;123: 	{


                     113 ;124: 		return DATA_ACCESS_ERROR_OBJECT_VALUE_INVALID;


                     114 ;125: 	}


                     115 ;126: 	value = BerDecoder_decodeUint32(dataToWrite, len, 2);


                     116 ;127: 


                     117 ;128: 


                     118 ;129: 	if (pAccessInfo->enumTableSize != 0)


                     119 ;130: 	{


                     120 ;131: 		value = getEnumDataValue(value, pAccessInfo->enumTable,


                     121 ;132: 			pAccessInfo->enumTableSize);


                     122 ;133: 	}


                     123 ;134: 


                     124 ;135:     if(!pwaWriteIntSett(pAccessInfo->valueOffset,value))


                     125 ;136:     {


                     126 ;137:         return DATA_ACCESS_ERROR_TYPE_INCONSISTENT;


                     127 ;138:     }


                     128 ;139:     return DATA_ACCESS_ERROR_SUCCESS;


                     129 ;140: }


                     130 ;141: 


                     131 ;142: void writeBoolean(void* descrStruct, uint8_t* dataToWrite)


                     132 ;143: {	


                     133 ;144: 	IntBoolAccessInfo* pAccessInfo = descrStruct;


                     134 ;145: 	int value;


                     135 ;146: 


                     136 ;147: 	if(!pAccessInfo->flags & ACCESS_FLAG_WRITABLE)


                     137 ;148: 	{


                     138 ;149: 		return;


                     139 ;150: 	}


                     140 ;151: 


                     141 ;152: 


                     142 ;153: 	if (dataToWrite[0] != IEC61850_BER_BOOLEAN || dataToWrite[1] != 1)


                     143 ;154: 	{


                     144 ;155: 		return;


                     145 ;156: 	}


                     146 ;157: 	value = dataToWrite[2];	


                     147 ;158:     if(value)


                     148 ;159:     {


                     149 ;160:         writeTele(pAccessInfo->valueOffset);


                     150 ;161:     }


                     151 ;162: }


                     152 ;163: 


                     153 ;164: void writeCodedEnum(void* descrStruct, uint8_t* dataToWrite)


                     154 ;165: {	


                     155 ;166:     CodedEnumAccessInfo* pAccessInfo = descrStruct;


                     156 ;167:     int valIdx;


                     157 ;168:     int value;


                     158 ;169: 


                     159 ;170: 	if(!pAccessInfo->flags & ACCESS_FLAG_WRITABLE)


                     160 ;171: 	{


                     161 ;172: 		return;


                     162 ;173: 	}


                     163 ;174: 


                     164 ;175:     if (pAccessInfo->bitCount > 8)


                     165 ;176:     {


                     166 ;177:         ERROR_REPORT("More than 8 bits is not supported");


                     167 ;178:         return;


                     168 ;179:     }


                     169 ;180: 


                     170 ;181:     value = BerDecoder_DecodeBitStringTLToInt(dataToWrite, 0);


                     171 ;182: 	if (value == -1)


                     172 ;183: 	{



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1us1.s
                     173 ;184: 		ERROR_REPORT("Error decoding Coded Enum");


                     174 ;185: 		return;


                     175 ;186: 	}


                     176 ;187: 


                     177 ;188:     


                     178 ;189:     for (valIdx = pAccessInfo->bitCount-1;  valIdx >= 0  ; --valIdx)


                     179 ;190:     {


                     180 ;191:         int offset = pAccessInfo->valueOffsets[valIdx];


                     181 ;192:         


                     182 ;193:         if (offset != -1 && (value & 1))


                     183 ;194:         {


                     184 ;195: 			writeTele(offset);


                     185 ;196:         }


                     186 ;197: 		value >>= 1;


                     187 ;198:     }    


                     188 ;199: }


                     189 ;200: 


                     190 ;201: 


                     191 ;202: static int handleWriteRequest(unsigned int invokeId, uint8_t* outBuf,


                     192 

                     193 ;227: }


                     194 

                     195 	.text

                     196 	.align	4

                     197 writeFloatSett::

00000000 e92d4010    198 	stmfd	[sp]!,{r4,lr}

00000004 e1a04000    199 	mov	r4,r0

00000008 e5940000    200 	ldr	r0,[r4]

0000000c e3500000    201 	cmp	r0,0

00000010 03a00001    202 	moveq	r0,1

00000014 13a00000    203 	movne	r0,0

00000018 e3100001    204 	tst	r0,1

0000001c 13a00003    205 	movne	r0,3

00000020 1a00000f    206 	bne	.L113

00000024 e5d10000    207 	ldrb	r0,[r1]

00000028 e3500087    208 	cmp	r0,135

0000002c 13a00009    209 	movne	r0,9

00000030 1a00000b    210 	bne	.L113

00000034 e1a00001    211 	mov	r0,r1

00000038 e3a01002    212 	mov	r1,2

0000003c eb000000*   213 	bl	BerDecoder_decodeFloat

00000040 e5941008    214 	ldr	r1,[r4,8]

00000044 eb000000*   215 	bl	__fdiv

00000048 eb000000*   216 	bl	__ftoi

0000004c e1a01000    217 	mov	r1,r0

00000050 e1d400b4    218 	ldrh	r0,[r4,4]

00000054 eb000000*   219 	bl	pwaWriteFloatSett

00000058 e3500000    220 	cmp	r0,0

0000005c 13e00000    221 	mvnne	r0,0

00000060 03a00007    222 	moveq	r0,7

                     223 .L113:

00000064 e8bd8010    224 	ldmfd	[sp]!,{r4,pc}

                     225 	.endf	writeFloatSett

                     226 	.align	4

                     227 ;pSettingInfo	r4	local

                     228 

                     229 ;descrStruct	r0	param

                     230 ;dataToWrite	r1	param

                     231 

                     232 	.section ".bss","awb"

                     233 .L166:


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1us1.s
                     234 	.data

                     235 	.text

                     236 

                     237 

                     238 	.align	4

                     239 	.align	4

                     240 writeRealSett::

00000068 e92d4010    241 	stmfd	[sp]!,{r4,lr}

0000006c e1a04000    242 	mov	r4,r0

00000070 e5940000    243 	ldr	r0,[r4]

00000074 e3500000    244 	cmp	r0,0

00000078 03a00001    245 	moveq	r0,1

0000007c 13a00000    246 	movne	r0,0

00000080 e3100001    247 	tst	r0,1

00000084 13a00003    248 	movne	r0,3

00000088 1a00000e    249 	bne	.L189

0000008c e5d10000    250 	ldrb	r0,[r1]

00000090 e3500087    251 	cmp	r0,135

00000094 13a00009    252 	movne	r0,9

00000098 1a00000a    253 	bne	.L189

0000009c e1a00001    254 	mov	r0,r1

000000a0 e3a01002    255 	mov	r1,2

000000a4 eb000000*   256 	bl	BerDecoder_decodeFloat

000000a8 e5941008    257 	ldr	r1,[r4,8]

000000ac eb000000*   258 	bl	__fdiv

000000b0 e1a01000    259 	mov	r1,r0

000000b4 e1d400b4    260 	ldrh	r0,[r4,4]

000000b8 eb000000*   261 	bl	pwaWriteRealSett

000000bc e3500000    262 	cmp	r0,0

000000c0 13e00000    263 	mvnne	r0,0

000000c4 03a00007    264 	moveq	r0,7

                     265 .L189:

000000c8 e8bd8010    266 	ldmfd	[sp]!,{r4,pc}

                     267 	.endf	writeRealSett

                     268 	.align	4

                     269 ;pSettingInfo	r4	local

                     270 

                     271 ;descrStruct	r0	param

                     272 ;dataToWrite	r1	param

                     273 

                     274 	.section ".bss","awb"

                     275 .L246:

                     276 	.data

                     277 	.text

                     278 

                     279 

                     280 	.align	4

                     281 	.align	4

                     282 writeIntSett::

000000cc e92d4010    283 	stmfd	[sp]!,{r4,lr}

000000d0 e1a04000    284 	mov	r4,r0

000000d4 e5940000    285 	ldr	r0,[r4]

000000d8 e3500000    286 	cmp	r0,0

000000dc 03a00001    287 	moveq	r0,1

000000e0 13a00000    288 	movne	r0,0

000000e4 e3100001    289 	tst	r0,1

000000e8 13a00003    290 	movne	r0,3

000000ec 1a000018    291 	bne	.L269

000000f0 e1a02001    292 	mov	r2,r1

000000f4 e5d20000    293 	ldrb	r0,[r2]

000000f8 e3500085    294 	cmp	r0,133


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1us1.s
000000fc 13500086    295 	cmpne	r0,134

00000100 13a00009    296 	movne	r0,9

00000104 1a000012    297 	bne	.L269

00000108 e5d21001    298 	ldrb	r1,[r2,1]

0000010c e3510008    299 	cmp	r1,8

00000110 c3a0000b    300 	movgt	r0,11

00000114 ca00000e    301 	bgt	.L269

00000118 e1a00002    302 	mov	r0,r2

0000011c e3a02002    303 	mov	r2,2

00000120 eb000000*   304 	bl	BerDecoder_decodeUint32

00000124 e5942008    305 	ldr	r2,[r4,8]

00000128 e1a01000    306 	mov	r1,r0

0000012c e3520000    307 	cmp	r2,0

00000130 0a000002    308 	beq	.L281

00000134 e284100c    309 	add	r1,r4,12

00000138 eb000000*   310 	bl	getEnumDataValue

0000013c e1a01000    311 	mov	r1,r0

                     312 .L281:

00000140 e1d400b4    313 	ldrh	r0,[r4,4]

00000144 eb000000*   314 	bl	pwaWriteIntSett

00000148 e3500000    315 	cmp	r0,0

0000014c 13e00000    316 	mvnne	r0,0

00000150 03a00007    317 	moveq	r0,7

                     318 .L269:

00000154 e8bd8010    319 	ldmfd	[sp]!,{r4,pc}

                     320 	.endf	writeIntSett

                     321 	.align	4

                     322 ;pAccessInfo	r4	local

                     323 ;value	r1	local

                     324 ;len	r1	local

                     325 

                     326 ;descrStruct	r0	param

                     327 ;dataToWrite	r2	param

                     328 

                     329 	.section ".bss","awb"

                     330 .L366:

                     331 	.data

                     332 	.text

                     333 

                     334 

                     335 	.align	4

                     336 	.align	4

                     337 writeBoolean::

00000158 e5902000    338 	ldr	r2,[r0]

0000015c e3520000    339 	cmp	r2,0

00000160 03a02001    340 	moveq	r2,1

00000164 13a02000    341 	movne	r2,0

00000168 e3120001    342 	tst	r2,1

0000016c 05d12000    343 	ldreqb	r2,[r1]

00000170 03520083    344 	cmpeq	r2,131

00000174 05d12001    345 	ldreqb	r2,[r1,1]

00000178 03520001    346 	cmpeq	r2,1

0000017c 1a000003    347 	bne	.L401

00000180 e5d11002    348 	ldrb	r1,[r1,2]

00000184 e3510000    349 	cmp	r1,0

00000188 15900004    350 	ldrne	r0,[r0,4]

0000018c 1a000000*   351 	bne	writeTele

                     352 .L401:

00000190 e12fff1e*   353 	ret	

                     354 	.endf	writeBoolean

                     355 	.align	4


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1us1.s
                     356 ;pAccessInfo	r0	local

                     357 ;value	r1	local

                     358 

                     359 ;descrStruct	r0	param

                     360 ;dataToWrite	r1	param

                     361 

                     362 	.section ".bss","awb"

                     363 .L461:

                     364 	.data

                     365 	.text

                     366 

                     367 

                     368 	.align	4

                     369 	.align	4

                     370 writeCodedEnum::

00000194 e92d4df0    371 	stmfd	[sp]!,{r4-r8,r10-fp,lr}

00000198 e24dd004    372 	sub	sp,sp,4

0000019c e1a04000    373 	mov	r4,r0

000001a0 e5940000    374 	ldr	r0,[r4]

000001a4 e3500000    375 	cmp	r0,0

000001a8 03a00001    376 	moveq	r0,1

000001ac 13a00000    377 	movne	r0,0

000001b0 e3100001    378 	tst	r0,1

000001b4 1a00004a    379 	bne	.L478

000001b8 e5940004    380 	ldr	r0,[r4,4]

000001bc e3500008    381 	cmp	r0,8

000001c0 ca000047    382 	bgt	.L478

000001c4 e1a00001    383 	mov	r0,r1

000001c8 e3a01000    384 	mov	r1,0

000001cc eb000000*   385 	bl	BerDecoder_DecodeBitStringTLToInt

000001d0 e1a05000    386 	mov	r5,r0

000001d4 e3750001    387 	cmn	r5,1

000001d8 0a000041    388 	beq	.L478

000001dc e5940004    389 	ldr	r0,[r4,4]

000001e0 e3500000    390 	cmp	r0,0

000001e4 e240b001    391 	sub	fp,r0,1

000001e8 a1a08000    392 	movge	r8,r0

000001ec b3a08000    393 	movlt	r8,0

000001f0 e1b0a1a8    394 	movs	r10,r8 lsr 3

000001f4 0a00002e    395 	beq	.L520

000001f8 e2841008    396 	add	r1,r4,8

000001fc e2400008    397 	sub	r0,r0,8

00000200 e0816100    398 	add	r6,r1,r0 lsl 2

00000204 e3e07000    399 	mvn	r7,0

00000208 e04bb18a    400 	sub	fp,fp,r10 lsl 3

                     401 .L521:

0000020c e596001c    402 	ldr	r0,[r6,28]

00000210 e1500007    403 	cmp	r0,r7

00000214 13150001    404 	tstne	r5,1

00000218 1b000000*   405 	blne	writeTele

0000021c e5960018    406 	ldr	r0,[r6,24]

00000220 e1a050c5    407 	mov	r5,r5 asr 1

00000224 e1500007    408 	cmp	r0,r7

00000228 13150001    409 	tstne	r5,1

0000022c 1b000000*   410 	blne	writeTele

00000230 e5960014    411 	ldr	r0,[r6,20]

00000234 e1a050c5    412 	mov	r5,r5 asr 1

00000238 e1500007    413 	cmp	r0,r7

0000023c 13150001    414 	tstne	r5,1

00000240 1b000000*   415 	blne	writeTele

00000244 e5960010    416 	ldr	r0,[r6,16]


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1us1.s
00000248 e1a050c5    417 	mov	r5,r5 asr 1

0000024c e1500007    418 	cmp	r0,r7

00000250 13150001    419 	tstne	r5,1

00000254 1b000000*   420 	blne	writeTele

00000258 e596000c    421 	ldr	r0,[r6,12]

0000025c e1a050c5    422 	mov	r5,r5 asr 1

00000260 e1500007    423 	cmp	r0,r7

00000264 13150001    424 	tstne	r5,1

00000268 1b000000*   425 	blne	writeTele

0000026c e5960008    426 	ldr	r0,[r6,8]

00000270 e1a050c5    427 	mov	r5,r5 asr 1

00000274 e1500007    428 	cmp	r0,r7

00000278 13150001    429 	tstne	r5,1

0000027c 1b000000*   430 	blne	writeTele

00000280 e5960004    431 	ldr	r0,[r6,4]

00000284 e1a050c5    432 	mov	r5,r5 asr 1

00000288 e1500007    433 	cmp	r0,r7

0000028c 13150001    434 	tstne	r5,1

00000290 1b000000*   435 	blne	writeTele

00000294 e4160020    436 	ldr	r0,[r6],-32

00000298 e1a050c5    437 	mov	r5,r5 asr 1

0000029c e1500007    438 	cmp	r0,r7

000002a0 13150001    439 	tstne	r5,1

000002a4 e1a050c5    440 	mov	r5,r5 asr 1

000002a8 1b000000*   441 	blne	writeTele

000002ac e25aa001    442 	subs	r10,r10,1

000002b0 1affffd5    443 	bne	.L521

                     444 .L520:

000002b4 e218a007    445 	ands	r10,r8,7

000002b8 0a000009    446 	beq	.L478

000002bc e2840008    447 	add	r0,r4,8

000002c0 e080410b    448 	add	r4,r0,fp lsl 2

000002c4 e3e06000    449 	mvn	r6,0

                     450 .L563:

000002c8 e4140004    451 	ldr	r0,[r4],-4

000002cc e1500006    452 	cmp	r0,r6

000002d0 13150001    453 	tstne	r5,1

000002d4 e1a050c5    454 	mov	r5,r5 asr 1

000002d8 1b000000*   455 	blne	writeTele

000002dc e25aa001    456 	subs	r10,r10,1

000002e0 1afffff8    457 	bne	.L563

                     458 .L478:

000002e4 e28dd004    459 	add	sp,sp,4

000002e8 e8bd8df0    460 	ldmfd	[sp]!,{r4-r8,r10-fp,pc}

                     461 	.endf	writeCodedEnum

                     462 	.align	4

                     463 ;pAccessInfo	r4	local

                     464 ;valIdx	fp	local

                     465 ;value	r5	local

                     466 ;offset	r0	local

                     467 

                     468 ;descrStruct	r0	param

                     469 ;dataToWrite	r1	param

                     470 

                     471 	.section ".bss","awb"

                     472 .L991:

                     473 	.data

                     474 	.text

                     475 

                     476 

                     477 ;228: 



                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1us1.s
                     478 ;229: int mms_handleWriteRequest(IsoConnection* isoConn,


                     479 	.align	4

                     480 	.align	4

                     481 mms_handleWriteRequest::

000002ec e92d4ff0    482 	stmfd	[sp]!,{r4-fp,lr}

                     483 ;230:         unsigned char* inBuf, int bufPos, int maxBufPos, unsigned int invokeId,


                     484 ;231:         unsigned char* response)


                     485 ;232: {


                     486 

000002f0 e24dd03c    487 	sub	sp,sp,60

000002f4 e58d001c    488 	str	r0,[sp,28]

000002f8 e1a07003    489 	mov	r7,r3

000002fc e3a06080    490 	mov	r6,128

00000300 e3a0b000    491 	mov	fp,0

                     492 ;233:     StringView domainId;


                     493 ;234:     StringView itemId;


                     494 ;235:     uint8_t tag;


                     495 ;236:     int length;


                     496 ;237:     size_t valListLen;


                     497 ;238: 	int specLength;


                     498 ;239: 	int specEnd;


                     499 ;240: 	int specBufPos;	


                     500 ;241:     int valCount = 0;


                     501 

                     502 ;242:     BufferView valBuf;


                     503 ;243:     MmsConnection* mmsConn = &isoConn->mmsConn;


                     504 

00000304 e1a05001    505 	mov	r5,r1

00000308 e3a01f8f    506 	mov	r1,0x023c

0000030c e2611a49    507 	rsb	r1,r1,73<<12

00000310 e080a001    508 	add	r10,r0,r1

                     509 ;244: 


                     510 ;245: 	//==========Variable specification=========


                     511 ;246:     tag = inBuf[bufPos++]; //0xA0


                     512 

00000314 e7d51002    513 	ldrb	r1,[r5,r2]

00000318 e2824001    514 	add	r4,r2,1

0000031c e5cd1007    515 	strb	r1,[sp,7]

                     516 ;247:     if(tag != 0xA0)


                     517 

00000320 e35100a0    518 	cmp	r1,160

00000324 1a00001f    519 	bne	.L1085

                     520 ;248:     {


                     521 

                     522 ;249:         ERROR_REPORT("Unexpected tag %02X  instead of var spec", tag);


                     523 ;250:         return 0;


                     524 

                     525 ;251:     }


                     526 ;252:     bufPos = BerDecoder_decodeLength(inBuf, &specLength, bufPos, maxBufPos);


                     527 

00000328 e1a02004    528 	mov	r2,r4

0000032c e28d1010    529 	add	r1,sp,16

00000330 e1a00005    530 	mov	r0,r5

00000334 eb000000*   531 	bl	BerDecoder_decodeLength

00000338 e2504000    532 	subs	r4,r0,0

                     533 ;253:     if(bufPos < 1)


                     534 

0000033c da000019    535 	ble	.L1085

                     536 ;254:     {


                     537 

                     538 ;255:         ERROR_REPORT("Invalid length");



                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1us1.s
                     539 ;256:         return 0;


                     540 

                     541 ;257:     }


                     542 ;258: 	specEnd = bufPos + specLength;    


                     543 

00000340 e59d0010    544 	ldr	r0,[sp,16]

00000344 e1a02007    545 	mov	r2,r7

00000348 e0803004    546 	add	r3,r0,r4

0000034c e1a09003    547 	mov	r9,r3

                     548 ;259: 	specBufPos = bufPos;


                     549 

                     550 ;260: 


                     551 ;261:     BufferView_init(&valBuf, inBuf, maxBufPos, specEnd);


                     552 

00000350 e1a01005    553 	mov	r1,r5

00000354 e28d0020    554 	add	r0,sp,32

00000358 eb000000*   555 	bl	BufferView_init

                     556 ;262: 


                     557 ;263: 	//==========Value list=================


                     558 ;264: 


                     559 ;265:     if(!BufferView_decodeTL(&valBuf, &tag, &valListLen, NULL ))


                     560 

0000035c e28d200c    561 	add	r2,sp,12

00000360 e28d1007    562 	add	r1,sp,7

00000364 e28d0020    563 	add	r0,sp,32

00000368 e3a03000    564 	mov	r3,0

0000036c eb000000*   565 	bl	BufferView_decodeTL

00000370 e3500000    566 	cmp	r0,0

00000374 0a00000b    567 	beq	.L1085

                     568 ;266:     {


                     569 

                     570 ;267:         ERROR_REPORT("Invalid write data");


                     571 ;268:         return 0;


                     572 

                     573 ;269:     }


                     574 ;270: 


                     575 ;271: 	if (tag != 0xA0)


                     576 

00000378 e5dd1007    577 	ldrb	r1,[sp,7]

0000037c e35100a0    578 	cmp	r1,160

00000380 1a000008    579 	bne	.L1085

                     580 ;272: 	{


                     581 

                     582 ;273: 		ERROR_REPORT("Unexpected tag %d", tag);


                     583 ;274: 		return 0;


                     584 

                     585 ;275: 	}


                     586 ;276: 


                     587 ;277:     if (valListLen < 1)


                     588 

00000384 e59d100c    589 	ldr	r1,[sp,12]

00000388 e3510000    590 	cmp	r1,0

0000038c 0a000005    591 	beq	.L1085

00000390 e28a1c61    592 	add	r1,r10,97<<8

00000394 e281a0d8    593 	add	r10,r1,216

00000398 e1a0800a    594 	mov	r8,r10

0000039c e1540009    595 	cmp	r4,r9

000003a0 aa00002b    596 	bge	.L1088

000003a4 ea000001    597 	b	.L1089

                     598 .L1085:

                     599 ;278: 	{



                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1us1.s
                     600 

                     601 ;279:         ERROR_REPORT("Invalid length %d", valListLen);


                     602 ;280: 		return 0;


                     603 

000003a8 e3a00000    604 	mov	r0,0

000003ac ea0000eb    605 	b	.L1070

                     606 .L1089:

                     607 ;281: 	}


                     608 ;282: 


                     609 ;283: 	//specBufPos движется по именам объектов	


                     610 ;284: 	while(specBufPos < specEnd)


                     611 

                     612 ;285: 	{


                     613 

                     614 ;286: 		tag = inBuf[specBufPos++]; //0x30 Sequence


                     615 

000003b0 e7d51004    616 	ldrb	r1,[r5,r4]

000003b4 e2844001    617 	add	r4,r4,1

000003b8 e5cd1007    618 	strb	r1,[sp,7]

                     619 ;287: 		if (tag != 0x30)


                     620 

000003bc e3510030    621 	cmp	r1,48

000003c0 1afffff8    622 	bne	.L1085

                     623 ;288: 		{


                     624 

                     625 ;289: 			ERROR_REPORT("Unexpected tag %02X instead of sequence", tag);


                     626 ;290: 			return 0;


                     627 

                     628 ;291: 		}


                     629 ;292: 		specBufPos = BerDecoder_decodeLength(inBuf, &length, specBufPos, maxBufPos);


                     630 

000003c4 e1a03007    631 	mov	r3,r7

000003c8 e1a02004    632 	mov	r2,r4

000003cc e28d1008    633 	add	r1,sp,8

000003d0 e1a00005    634 	mov	r0,r5

000003d4 eb000000*   635 	bl	BerDecoder_decodeLength

000003d8 e2504000    636 	subs	r4,r0,0

                     637 ;293: 		if (specBufPos < 1)


                     638 

000003dc dafffff1    639 	ble	.L1085

                     640 ;294: 		{


                     641 

                     642 ;295: 			ERROR_REPORT("Invalid length %d", length);


                     643 ;296: 			return 0;


                     644 

                     645 ;297: 		}


                     646 ;298: 


                     647 ;299: 		tag = inBuf[specBufPos++]; //0xA0 Object name


                     648 

000003e0 e7d51004    649 	ldrb	r1,[r5,r4]

000003e4 e2844001    650 	add	r4,r4,1

000003e8 e5cd1007    651 	strb	r1,[sp,7]

                     652 ;300: 		if (tag != 0xA0)


                     653 

000003ec e35100a0    654 	cmp	r1,160

000003f0 1affffec    655 	bne	.L1085

                     656 ;301: 		{


                     657 

                     658 ;302: 			ERROR_REPORT("Unexpected tag %02X  instead of object name", tag);


                     659 ;303: 			return 0;


                     660 


                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1us1.s
                     661 ;304: 		}


                     662 ;305: 		specBufPos = BerDecoder_decodeLength(inBuf, &length, specBufPos, maxBufPos);


                     663 

000003f4 e1a03007    664 	mov	r3,r7

000003f8 e1a02004    665 	mov	r2,r4

000003fc e28d1008    666 	add	r1,sp,8

00000400 e1a00005    667 	mov	r0,r5

00000404 eb000000*   668 	bl	BerDecoder_decodeLength

00000408 e2504000    669 	subs	r4,r0,0

                     670 ;306: 		if (specBufPos < 1)


                     671 

0000040c daffffe5    672 	ble	.L1085

                     673 ;307: 		{


                     674 

                     675 ;308: 			ERROR_REPORT("Invalid length %d", length);


                     676 ;309: 			return 0;


                     677 

                     678 ;310: 		}


                     679 ;311: 


                     680 ;312: 		//Decode object name


                     681 ;313: 		specBufPos = BerDecoder_DecodeObjectNameToStringView(inBuf, specBufPos,


                     682 

00000410 e28d3034    683 	add	r3,sp,52

00000414 e1a02007    684 	mov	r2,r7

00000418 e1a01004    685 	mov	r1,r4

0000041c e28d002c    686 	add	r0,sp,44

00000420 e58d0000    687 	str	r0,[sp]

00000424 e1a00005    688 	mov	r0,r5

00000428 eb000000*   689 	bl	BerDecoder_DecodeObjectNameToStringView

0000042c e28d3020    690 	add	r3,sp,32

00000430 e59d201c    691 	ldr	r2,[sp,28]

00000434 e28d102c    692 	add	r1,sp,44

00000438 e1a04000    693 	mov	r4,r0

                     694 ;314: 			maxBufPos, &domainId, &itemId);


                     695 ;315: 


                     696 ;316:         //Write object


                     697 ;317:         mmsConn->wrResults[valCount] =                


                     698 

0000043c e28d0034    699 	add	r0,sp,52

00000440 eb000000*   700 	bl	IEDTree_write

00000444 e48a0004    701 	str	r0,[r10],4

                     702 ;318:                 IEDTree_write(&domainId, &itemId, isoConn, &valBuf );


                     703 ;319: 		valCount++;


                     704 

00000448 e28bb001    705 	add	fp,fp,1

0000044c e1540009    706 	cmp	r4,r9

00000450 baffffd6    707 	blt	.L1089

                     708 .L1088:

                     709 ;320: 	}    


                     710 ;321: 	


                     711 ;322:     return handleWriteRequest(invokeId, response, mmsConn->wrResults, valCount);


                     712 

00000454 e59d7060    713 	ldr	r7,[sp,96]

                     714 ;203:                        MmsDataAccessError* errList, size_t valCount)


                     715 ;204: {


                     716 

                     717 ;205: 	int bufPos = 0;


                     718 

                     719 ;206: 	//==============determine BER encoded message sizes==============


                     720 ;207:     int responseContentSize = calcAccessErrEncodedSize(errList, valCount);


                     721 


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1us1.s
                     722 ;18:                                 size_t itemCount)


                     723 ;19: {


                     724 

00000458 e3a04000    725 	mov	r4,0

                     726 ;20:     size_t i;


                     727 ;21:     size_t size = 0;


                     728 

                     729 ;22:     for (i = 0; i < itemCount; i++)


                     730 

0000045c e1a03004    731 	mov	r3,r4

00000460 e35b0000    732 	cmp	fp,0

00000464 a1a0c00b    733 	movge	r12,fp

00000468 b3a0c000    734 	movlt	r12,0

0000046c e1b021ac    735 	movs	r2,r12 lsr 3

00000470 0a000024    736 	beq	.L1258

00000474 e1a01008    737 	mov	r1,r8

00000478 e1a03182    738 	mov	r3,r2 lsl 3

                     739 .L1259:

0000047c e5910000    740 	ldr	r0,[r1]

00000480 e3500000    741 	cmp	r0,0

00000484 b2844002    742 	addlt	r4,r4,2

00000488 e5910004    743 	ldr	r0,[r1,4]

0000048c a2844003    744 	addge	r4,r4,3

00000490 e3500000    745 	cmp	r0,0

00000494 b2844002    746 	addlt	r4,r4,2

00000498 e5910008    747 	ldr	r0,[r1,8]

0000049c a2844003    748 	addge	r4,r4,3

000004a0 e3500000    749 	cmp	r0,0

000004a4 b2844002    750 	addlt	r4,r4,2

000004a8 e591000c    751 	ldr	r0,[r1,12]

000004ac a2844003    752 	addge	r4,r4,3

000004b0 e3500000    753 	cmp	r0,0

000004b4 b2844002    754 	addlt	r4,r4,2

000004b8 e5910010    755 	ldr	r0,[r1,16]

000004bc a2844003    756 	addge	r4,r4,3

000004c0 e3500000    757 	cmp	r0,0

000004c4 b2844002    758 	addlt	r4,r4,2

000004c8 e5910014    759 	ldr	r0,[r1,20]

000004cc a2844003    760 	addge	r4,r4,3

000004d0 e3500000    761 	cmp	r0,0

000004d4 b2844002    762 	addlt	r4,r4,2

000004d8 e5910018    763 	ldr	r0,[r1,24]

000004dc a2844003    764 	addge	r4,r4,3

000004e0 e3500000    765 	cmp	r0,0

000004e4 e591001c    766 	ldr	r0,[r1,28]

000004e8 e2811020    767 	add	r1,r1,32

000004ec b2844002    768 	addlt	r4,r4,2

000004f0 a2844003    769 	addge	r4,r4,3

000004f4 e3500000    770 	cmp	r0,0

000004f8 b2844002    771 	addlt	r4,r4,2

000004fc a2844003    772 	addge	r4,r4,3

00000500 e2522001    773 	subs	r2,r2,1

00000504 1affffdc    774 	bne	.L1259

                     775 .L1258:

00000508 e21c2007    776 	ands	r2,r12,7

0000050c 0a000006    777 	beq	.L1110

00000510 e0881103    778 	add	r1,r8,r3 lsl 2

                     779 .L1301:

00000514 e4913004    780 	ldr	r3,[r1],4

00000518 e3530000    781 	cmp	r3,0

0000051c b2844002    782 	addlt	r4,r4,2


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1us1.s
00000520 a2844003    783 	addge	r4,r4,3

00000524 e2522001    784 	subs	r2,r2,1

00000528 1afffff9    785 	bne	.L1301

                     786 .L1110:

                     787 ;31:         }


                     788 ;32:     }


                     789 ;33:     return size;


                     790 

                     791 ;208: 	int responseContentSizeTL = responseContentSize + 2;


                     792 

0000052c e284a002    793 	add	r10,r4,2

                     794 ;209: 	int invokeIdSize = BerEncoder_UInt32determineEncodedSize(invokeId) + 2;


                     795 

00000530 e1a00007    796 	mov	r0,r7

00000534 eb000000*   797 	bl	BerEncoder_UInt32determineEncodedSize

00000538 e2805002    798 	add	r5,r0,2

                     799 ;210: 	int responseSize = responseContentSizeTL + invokeIdSize;


                     800 

0000053c e085100a    801 	add	r1,r5,r10

                     802 ;211: 


                     803 ;212: 	//================ encode message ============================


                     804 ;213: 	bufPos = 0;


                     805 

                     806 ;214: 


                     807 ;215: 	// confirmed response PDU


                     808 ;216: 	bufPos = BerEncoder_encodeTL(0xa1, responseSize, outBuf, bufPos);


                     809 

00000540 e59d2064    810 	ldr	r2,[sp,100]

00000544 e3a03000    811 	mov	r3,0

00000548 e3a000a1    812 	mov	r0,161

0000054c eb000000*   813 	bl	BerEncoder_encodeTL

                     814 ;217: 	// invoke id


                     815 ;218: 	bufPos = BerEncoder_encodeTL(ASN_INTEGER, invokeIdSize - 2, outBuf, bufPos);


                     816 

00000550 e59d2064    817 	ldr	r2,[sp,100]

00000554 e2451002    818 	sub	r1,r5,2

00000558 e1a03000    819 	mov	r3,r0

0000055c e3a00002    820 	mov	r0,2

00000560 eb000000*   821 	bl	BerEncoder_encodeTL

                     822 ;219: 	bufPos = BerEncoder_encodeUInt32(invokeId, outBuf, bufPos);


                     823 

00000564 e1a02000    824 	mov	r2,r0

00000568 e59d1064    825 	ldr	r1,[sp,100]

0000056c e1a00007    826 	mov	r0,r7

00000570 eb000000*   827 	bl	BerEncoder_encodeUInt32

                     828 ;220: 


                     829 ;221: 	// confirmed-service-response write


                     830 ;222: 	bufPos = BerEncoder_encodeTL(0xa5, responseContentSize, outBuf, bufPos);


                     831 

00000574 e59d2064    832 	ldr	r2,[sp,100]

00000578 e1a01004    833 	mov	r1,r4

0000057c e1a03000    834 	mov	r3,r0

00000580 e3a000a5    835 	mov	r0,165

00000584 eb000000*   836 	bl	BerEncoder_encodeTL

                     837 ;223: 


                     838 ;224:     // encode access results


                     839 ;225:     bufPos = encodeAccessErrors(errList, valCount, outBuf, bufPos);


                     840 

00000588 e1a01008    841 	mov	r1,r8

0000058c e59d2064    842 	ldr	r2,[sp,100]

                     843 ;37:                    size_t itemCount, uint8_t* buf, size_t bufPos)



                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1us1.s
                     844 ;38: {


                     845 

                     846 ;39:     size_t i;


                     847 ;40:     for (i = 0; i < itemCount; i++)


                     848 

00000590 e3a0c000    849 	mov	r12,0

00000594 e35b0000    850 	cmp	fp,0

00000598 b3a0b000    851 	movlt	fp,0

0000059c e1b0512b    852 	movs	r5,fp lsr 2

000005a0 0a000053    853 	beq	.L1308

000005a4 e2814004    854 	add	r4,r1,4

000005a8 e3a07081    855 	mov	r7,129

000005ac e3a0a001    856 	mov	r10,1

                     857 .L1309:

000005b0 e791310c    858 	ldr	r3,[r1,r12 lsl 2]

000005b4 e3530000    859 	cmp	r3,0

000005b8 aa000008    860 	bge	.L1311

000005bc e3a03000    861 	mov	r3,0

000005c0 e7c27000    862 	strb	r7,[r2,r0]

000005c4 e2800001    863 	add	r0,r0,1

000005c8 e7c23000    864 	strb	r3,[r2,r0]

000005cc e5943000    865 	ldr	r3,[r4]

000005d0 e2800001    866 	add	r0,r0,1

000005d4 e3530000    867 	cmp	r3,0

000005d8 aa000013    868 	bge	.L1316

000005dc ea000009    869 	b	.L1315

                     870 .L1311:

000005e0 e7c26000    871 	strb	r6,[r2,r0]

000005e4 e2800001    872 	add	r0,r0,1

000005e8 e7c2a000    873 	strb	r10,[r2,r0]

000005ec e791310c    874 	ldr	r3,[r1,r12 lsl 2]

000005f0 e2800001    875 	add	r0,r0,1

000005f4 e7c23000    876 	strb	r3,[r2,r0]

000005f8 e5943000    877 	ldr	r3,[r4]

000005fc e2800001    878 	add	r0,r0,1

00000600 e3530000    879 	cmp	r3,0

00000604 aa000008    880 	bge	.L1316

                     881 .L1315:

00000608 e3a03000    882 	mov	r3,0

0000060c e7c27000    883 	strb	r7,[r2,r0]

00000610 e2800001    884 	add	r0,r0,1

00000614 e7c23000    885 	strb	r3,[r2,r0]

00000618 e5943004    886 	ldr	r3,[r4,4]

0000061c e2800001    887 	add	r0,r0,1

00000620 e3530000    888 	cmp	r3,0

00000624 aa000013    889 	bge	.L1321

00000628 ea000009    890 	b	.L1320

                     891 .L1316:

0000062c e7c26000    892 	strb	r6,[r2,r0]

00000630 e2800001    893 	add	r0,r0,1

00000634 e7c2a000    894 	strb	r10,[r2,r0]

00000638 e5943000    895 	ldr	r3,[r4]

0000063c e2800001    896 	add	r0,r0,1

00000640 e7c23000    897 	strb	r3,[r2,r0]

00000644 e5943004    898 	ldr	r3,[r4,4]

00000648 e2800001    899 	add	r0,r0,1

0000064c e3530000    900 	cmp	r3,0

00000650 aa000008    901 	bge	.L1321

                     902 .L1320:

00000654 e3a03000    903 	mov	r3,0

00000658 e7c27000    904 	strb	r7,[r2,r0]


                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1us1.s
0000065c e2800001    905 	add	r0,r0,1

00000660 e7c23000    906 	strb	r3,[r2,r0]

00000664 e5943008    907 	ldr	r3,[r4,8]

00000668 e2800001    908 	add	r0,r0,1

0000066c e3530000    909 	cmp	r3,0

00000670 aa000014    910 	bge	.L1326

00000674 ea000009    911 	b	.L1325

                     912 .L1321:

00000678 e7c26000    913 	strb	r6,[r2,r0]

0000067c e2800001    914 	add	r0,r0,1

00000680 e7c2a000    915 	strb	r10,[r2,r0]

00000684 e5943004    916 	ldr	r3,[r4,4]

00000688 e2800001    917 	add	r0,r0,1

0000068c e7c23000    918 	strb	r3,[r2,r0]

00000690 e5943008    919 	ldr	r3,[r4,8]

00000694 e2800001    920 	add	r0,r0,1

00000698 e3530000    921 	cmp	r3,0

0000069c aa000009    922 	bge	.L1326

                     923 .L1325:

000006a0 e3a03000    924 	mov	r3,0

000006a4 e7c27000    925 	strb	r7,[r2,r0]

000006a8 e2800001    926 	add	r0,r0,1

000006ac e7c23000    927 	strb	r3,[r2,r0]

000006b0 e2800001    928 	add	r0,r0,1

000006b4 e2844010    929 	add	r4,r4,16

000006b8 e28cc004    930 	add	r12,r12,4

000006bc e2555001    931 	subs	r5,r5,1

000006c0 1affffba    932 	bne	.L1309

000006c4 ea00000a    933 	b	.L1308

                     934 .L1326:

000006c8 e7c26000    935 	strb	r6,[r2,r0]

000006cc e2800001    936 	add	r0,r0,1

000006d0 e7c2a000    937 	strb	r10,[r2,r0]

000006d4 e5943008    938 	ldr	r3,[r4,8]

000006d8 e2800001    939 	add	r0,r0,1

000006dc e7c23000    940 	strb	r3,[r2,r0]

000006e0 e2800001    941 	add	r0,r0,1

000006e4 e2844010    942 	add	r4,r4,16

000006e8 e28cc004    943 	add	r12,r12,4

000006ec e2555001    944 	subs	r5,r5,1

000006f0 1affffae    945 	bne	.L1309

                     946 .L1308:

000006f4 e21b5003    947 	ands	r5,fp,3

000006f8 0a000018    948 	beq	.L1070

000006fc e3a04081    949 	mov	r4,129

00000700 e3a06001    950 	mov	r6,1

00000704 e3a07080    951 	mov	r7,128

                     952 .L1331:

00000708 e791310c    953 	ldr	r3,[r1,r12 lsl 2]

0000070c e3530000    954 	cmp	r3,0

00000710 aa000008    955 	bge	.L1333

00000714 e3a03000    956 	mov	r3,0

00000718 e7c24000    957 	strb	r4,[r2,r0]

0000071c e2800001    958 	add	r0,r0,1

00000720 e7c23000    959 	strb	r3,[r2,r0]

00000724 e2800001    960 	add	r0,r0,1

00000728 e28cc001    961 	add	r12,r12,1

0000072c e2555001    962 	subs	r5,r5,1

00000730 1afffff4    963 	bne	.L1331

00000734 ea000009    964 	b	.L1070

                     965 .L1333:


                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1us1.s
00000738 e7c27000    966 	strb	r7,[r2,r0]

0000073c e2800001    967 	add	r0,r0,1

00000740 e7c26000    968 	strb	r6,[r2,r0]

00000744 e791310c    969 	ldr	r3,[r1,r12 lsl 2]

00000748 e2800001    970 	add	r0,r0,1

0000074c e7c23000    971 	strb	r3,[r2,r0]

00000750 e2800001    972 	add	r0,r0,1

00000754 e28cc001    973 	add	r12,r12,1

00000758 e2555001    974 	subs	r5,r5,1

0000075c 1affffe9    975 	bne	.L1331

                     976 ;52:         }


                     977 ;53:     }


                     978 ;54:     return bufPos;


                     979 

                     980 ;226: 	return bufPos;


                     981 

                     982 .L1070:

00000760 e28dd03c    983 	add	sp,sp,60

00000764 e8bd8ff0    984 	ldmfd	[sp]!,{r4-fp,pc}

                     985 	.endf	mms_handleWriteRequest

                     986 	.align	4

                     987 ;domainId	[sp,52]	local

                     988 ;itemId	[sp,44]	local

                     989 ;tag	[sp,7]	local

                     990 ;length	[sp,8]	local

                     991 ;valListLen	[sp,12]	local

                     992 ;specLength	[sp,16]	local

                     993 ;specEnd	r9	local

                     994 ;specBufPos	r4	local

                     995 ;valCount	fp	local

                     996 ;valBuf	[sp,32]	local

                     997 ;mmsConn	r10	local

                     998 ;invokeId	r7	local

                     999 ;valCount	fp	local

                    1000 ;responseContentSizeTL	r10	local

                    1001 ;invokeIdSize	r5	local

                    1002 ;i	r3	local

                    1003 ;size	r4	local

                    1004 ;errList	r1	local

                    1005 ;buf	r2	local

                    1006 ;bufPos	r0	local

                    1007 ;i	r12	local

                    1008 

                    1009 ;isoConn	[sp,28]	param

                    1010 ;inBuf	r5	param

                    1011 ;bufPos	r4	param

                    1012 ;maxBufPos	r7	param

                    1013 ;invokeId	[sp,96]	param

                    1014 ;response	[sp,100]	param

                    1015 

                    1016 	.section ".bss","awb"

                    1017 .L2001:

                    1018 	.data

                    1019 	.text

                    1020 

                    1021 ;323: }


                    1022 	.align	4

                    1023 

                    1024 	.data

                    1025 	.ghsnote version,6

                    1026 	.ghsnote tools,3


                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1us1.s
                    1027 	.ghsnote options,0

                    1028 	.text

                    1029 	.align	4

