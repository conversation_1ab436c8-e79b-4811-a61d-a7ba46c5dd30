                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_alk1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=iedFloat.c -o iedTree\gh_alk1.o -list=iedTree/iedFloat.lst C:\Users\<USER>\AppData\Local\Temp\gh_alk1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_alk1.s
Source File: iedFloat.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      10 ;		iedTree/iedFloat.c -o iedTree/iedFloat.o

                      11 ;Source File:   iedTree/iedFloat.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:55:11 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "iedFloat.h"


                      21 ;2: 


                      22 ;3: #include "debug.h"


                      23 ;4: #include "iedFinalDA.h"


                      24 ;5: #include "iedEntity.h"


                      25 ;6: #include "iedTree.h"


                      26 ;7: #include "../DataSlice.h"


                      27 ;8: #include "../AsnEncoding.h"


                      28 ;9: #include "../BERCoder.h"


                      29 ;10: 


                      30 ;11: #include "fnan.h"


                      31 ;12: 


                      32 ;13: #define FLOAT_ENCODED_SIZE 7


                      33 ;14: 


                      34 ;15: static bool calcReadLen(IEDEntity entity, size_t* pLen )


                      35 	.text

                      36 	.align	4

                      37 calcReadLen:

                      38 ;16: {


                      39 

                      40 ;17:     *pLen = FLOAT_ENCODED_SIZE;


                      41 

00000000 e3a00007     42 	mov	r0,7

00000004 e5810000     43 	str	r0,[r1]

                      44 ;18:     return true;


                      45 

00000008 e3a00001     46 	mov	r0,1

0000000c e12fff1e*    47 	ret	

                      48 	.endf	calcReadLen

                      49 	.align	4

                      50 


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_alk1.s
                      51 ;entity	none	param

                      52 ;pLen	r1	param

                      53 

                      54 	.section ".bss","awb"

                      55 .L62:

                      56 	.data

                      57 	.text

                      58 

                      59 ;19: }


                      60 

                      61 ;20: 


                      62 ;21: //Общая инициализация для FLOAT и REAL


                      63 ;22: static void commonInit(IEDEntity entity)


                      64 	.align	4

                      65 	.align	4

                      66 commonInit:

00000010 e92d4030     67 	stmfd	[sp]!,{r4-r5,lr}

00000014 e1a04000     68 	mov	r4,r0

00000018 e5940058     69 	ldr	r0,[r4,88]

                      70 ;25:     FloatAccsessInfo* accessInfo = extInfo->accessInfo;


                      71 

0000001c e5901000     72 	ldr	r1,[r0]

                      73 ;26: 


                      74 ;27:     extInfo->f.multiplier = accessInfo->multiplier;


                      75 

00000020 e5912008     76 	ldr	r2,[r1,8]

00000024 e5802004     77 	str	r2,[r0,4]

                      78 ;28: 


                      79 ;29:     //Если будет ошибка, то запишется -1;


                      80 ;30:     entity->dataSliceOffset = DataSlice_getAnalogOffset(accessInfo->valueOffset);


                      81 

00000028 e5910004     82 	ldr	r0,[r1,4]

0000002c e59f5398*    83 	ldr	r5,.L104

                      84 ;23: {


                      85 

                      86 ;24:     TerminalItem* extInfo = entity->extInfo;


                      87 

00000030 eb000000*    88 	bl	DataSlice_getAnalogOffset

00000034 e5845060     89 	str	r5,[r4,96]

                      90 ;33: 


                      91 ;34:     IEDTree_addToCmpList(entity);


                      92 

00000038 e584002c     93 	str	r0,[r4,44]

                      94 ;31: 


                      95 ;32:     entity->calcReadLen = calcReadLen;


                      96 

0000003c e1a00004     97 	mov	r0,r4

00000040 eb000000*    98 	bl	IEDTree_addToCmpList

00000044 e8bd4030     99 	ldmfd	[sp]!,{r4-r5,lr}

00000048 e12fff1e*   100 	ret	

                     101 	.endf	commonInit

                     102 	.align	4

                     103 ;extInfo	r0	local

                     104 ;accessInfo	r1	local

                     105 

                     106 ;entity	r4	param

                     107 

                     108 	.section ".bss","awb"

                     109 .L97:

                     110 	.data

                     111 	.text


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_alk1.s
                     112 

                     113 ;35: }


                     114 

                     115 ;36: 


                     116 ;37: //===================REAL======================


                     117 ;38: 


                     118 ;39: static void realUpdateFromDataSlice(IEDEntity entity)


                     119 	.align	4

                     120 	.align	4

                     121 realUpdateFromDataSlice:

0000004c e92d4030    122 	stmfd	[sp]!,{r4-r5,lr}

00000050 e1a04000    123 	mov	r4,r0

                     124 ;40: {


                     125 

                     126 ;41:     int offset  = entity->dataSliceOffset;


                     127 

00000054 e594002c    128 	ldr	r0,[r4,44]

                     129 ;42:     float value;


                     130 ;43: 


                     131 ;44:     if(offset == -1)


                     132 

00000058 e3700001    133 	cmn	r0,1

0000005c 0a000014    134 	beq	.L105

                     135 ;45:     {


                     136 

                     137 ;46:         return;


                     138 

                     139 ;47:     }


                     140 ;48: 


                     141 ;49:     value = DataSlice_getRealFastCurrDS(offset);


                     142 

00000060 e1a00800    143 	mov	r0,r0 lsl 16

00000064 e1a00820    144 	mov	r0,r0 lsr 16

00000068 eb000000*   145 	bl	DataSlice_getRealFastCurrDS

0000006c e1a05000    146 	mov	r5,r0

                     147 ;50: 


                     148 ;51:     if(entity->realValue == value)


                     149 

00000070 e5940030    150 	ldr	r0,[r4,48]

00000074 e1a01005    151 	mov	r1,r5

00000078 eb000000*   152 	bl	__fcmp

0000007c e3500000    153 	cmp	r0,0

                     154 ;52:     {


                     155 

                     156 ;53:         entity->changed = TRGOP_NONE;


                     157 

00000080 03a00000    158 	moveq	r0,0

00000084 05840028    159 	streq	r0,[r4,40]

00000088 0a000009    160 	beq	.L105

                     161 ;54:     }


                     162 ;55:     else


                     163 ;56:     {


                     164 

                     165 ;57: 		//Кэш сбрасываем для RealAsInt64


                     166 ;58:         entity->cached = false;


                     167 

0000008c e3a00000    168 	mov	r0,0

00000090 e5c40034    169 	strb	r0,[r4,52]

                     170 ;59: 


                     171 ;60:         entity->changed = entity->trgOps;


                     172 


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_alk1.s
00000094 e5940024    173 	ldr	r0,[r4,36]

00000098 e5845030    174 	str	r5,[r4,48]

                     175 ;62:         IEDEntity_setTimeStamp(entity, dataSliceGetTimeStamp());


                     176 

0000009c e5840028    177 	str	r0,[r4,40]

                     178 ;61:         entity->realValue = value;


                     179 

000000a0 eb000000*   180 	bl	dataSliceGetTimeStamp

000000a4 e1a02001    181 	mov	r2,r1

000000a8 e1a01000    182 	mov	r1,r0

000000ac e1a00004    183 	mov	r0,r4

000000b0 eb000000*   184 	bl	IEDEntity_setTimeStamp

                     185 .L105:

000000b4 e8bd4030    186 	ldmfd	[sp]!,{r4-r5,lr}

000000b8 e12fff1e*   187 	ret	

                     188 	.endf	realUpdateFromDataSlice

                     189 	.align	4

                     190 ;offset	r0	local

                     191 ;value	r5	local

                     192 

                     193 ;entity	r4	param

                     194 

                     195 	.section ".bss","awb"

                     196 .L168:

                     197 	.data

                     198 	.text

                     199 

                     200 ;63:     }


                     201 ;64: }


                     202 

                     203 ;65: 


                     204 ;66: static bool realEncodeRead(IEDEntity entity, BufferView* outBuf)


                     205 	.align	4

                     206 	.align	4

                     207 realEncodeRead:

000000bc e92d4030    208 	stmfd	[sp]!,{r4-r5,lr}

000000c0 e24dd010    209 	sub	sp,sp,16

                     210 ;67: {


                     211 

                     212 ;68:     uint8_t* encodeBuf;


                     213 ;69:     TerminalItem* extInfo = entity->extInfo;


                     214 

000000c4 e5904030    215 	ldr	r4,[r0,48]

                     216 ;71: 


                     217 ;72:     if(!isfnan(value))


                     218 

000000c8 e1a05001    219 	mov	r5,r1

000000cc e58d400c    220 	str	r4,[sp,12]

000000d0 e5901058    221 	ldr	r1,[r0,88]

                     222 ;70:     float value = entity->realValue;


                     223 

000000d4 e3a005fe    224 	mov	r0,254<<22

000000d8 e2800440    225 	add	r0,r0,1<<30

000000dc e0042000    226 	and	r2,r4,r0

000000e0 e1520000    227 	cmp	r2,r0

000000e4 0a000003    228 	beq	.L187

                     229 ;73:     {


                     230 

                     231 ;74:         value *= extInfo->f.multiplier;


                     232 

000000e8 e5911004    233 	ldr	r1,[r1,4]


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_alk1.s
000000ec e1a00004    234 	mov	r0,r4

000000f0 eb000000*   235 	bl	__fmul

000000f4 e1a04000    236 	mov	r4,r0

                     237 .L187:

000000f8 e28d2008    238 	add	r2,sp,8

000000fc e1a00005    239 	mov	r0,r5

00000100 e3a01007    240 	mov	r1,7

00000104 eb000000*   241 	bl	BufferView_alloc

                     242 ;75:     }


                     243 ;76: 


                     244 ;77:     if(!BufferView_alloc(outBuf,FLOAT_ENCODED_SIZE, &encodeBuf))


                     245 

00000108 e3500000    246 	cmp	r0,0

                     247 ;78:     {


                     248 

                     249 ;79:         ERROR_REPORT("Unable to allocate buffer");


                     250 ;80:         return false;


                     251 

0000010c 0a00000b    252 	beq	.L185

                     253 ;81:     }


                     254 ;82: 


                     255 ;83:     // Возвращаемое значение не нужно,


                     256 ;84:     // потому что функция не возвращает ошибки, а размер известен заранее


                     257 ;85:     BerEncoder_EncodeFloatWithTL(0x87, value, 32, 8, encodeBuf, 0);


                     258 

00000110 e59d0008    259 	ldr	r0,[sp,8]

00000114 e3a01000    260 	mov	r1,0

00000118 e88d0003    261 	stmea	[sp],{r0-r1}

0000011c e1a01004    262 	mov	r1,r4

00000120 e3a03008    263 	mov	r3,8

00000124 e3a02020    264 	mov	r2,32

00000128 e3a00087    265 	mov	r0,135

0000012c eb000000*   266 	bl	BerEncoder_EncodeFloatWithTL

                     267 ;86:     outBuf->pos += FLOAT_ENCODED_SIZE;


                     268 

00000130 e5950004    269 	ldr	r0,[r5,4]

00000134 e2800007    270 	add	r0,r0,7

00000138 e5850004    271 	str	r0,[r5,4]

                     272 ;87:     return true;


                     273 

0000013c e3a00001    274 	mov	r0,1

                     275 .L185:

00000140 e28dd010    276 	add	sp,sp,16

00000144 e8bd4030    277 	ldmfd	[sp]!,{r4-r5,lr}

00000148 e12fff1e*   278 	ret	

                     279 	.endf	realEncodeRead

                     280 	.align	4

                     281 ;encodeBuf	[sp,8]	local

                     282 ;extInfo	r1	local

                     283 ;value	r4	local

                     284 ;value	[sp,12]	local

                     285 

                     286 ;entity	r0	param

                     287 ;outBuf	r5	param

                     288 

                     289 	.section ".bss","awb"

                     290 .L282:

                     291 	.data

                     292 	.text

                     293 

                     294 ;88: }



                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_alk1.s
                     295 

                     296 ;89: 


                     297 ;90: void IEDReal_init(IEDEntity entity)


                     298 	.align	4

                     299 	.align	4

                     300 IEDReal_init::

0000014c e92d4070    301 	stmfd	[sp]!,{r4-r6,lr}

00000150 e59f5278*   302 	ldr	r5,.L325

00000154 e59f6278*   303 	ldr	r6,.L326

                     304 ;91: {


                     305 

                     306 ;92:     commonInit(entity);


                     307 

00000158 e1a04000    308 	mov	r4,r0

0000015c ebffffab*   309 	bl	commonInit

                     310 ;93:     entity->encodeRead = realEncodeRead;


                     311 

00000160 e584505c    312 	str	r5,[r4,92]

                     313 ;94:     entity->updateFromDataSlice = realUpdateFromDataSlice;


                     314 

00000164 e5846068    315 	str	r6,[r4,104]

00000168 e8bd8070    316 	ldmfd	[sp]!,{r4-r6,pc}

                     317 	.endf	IEDReal_init

                     318 	.align	4

                     319 

                     320 ;entity	r4	param

                     321 

                     322 	.section ".bss","awb"

                     323 .L318:

                     324 	.data

                     325 	.text

                     326 

                     327 ;95: }


                     328 

                     329 ;96: 


                     330 ;97: //===================REAL AS INT64======================


                     331 ;98: static void realAsInt64FillCache(IEDEntity entity)


                     332 	.align	4

                     333 	.align	4

                     334 realAsInt64FillCache:

0000016c e92d4010    335 	stmfd	[sp]!,{r4,lr}

                     336 ;99: {


                     337 

                     338 ;100:     TerminalItem* extInfo = entity->extInfo;


                     339 

00000170 e24dd00c    340 	sub	sp,sp,12

00000174 e1a04000    341 	mov	r4,r0

00000178 e5940030    342 	ldr	r0,[r4,48]

0000017c e5942058    343 	ldr	r2,[r4,88]

                     344 ;101:     int64_t intValue;


                     345 ;102:     if(isfnan(entity->realValue))


                     346 

00000180 e58d0000    347 	str	r0,[sp]

00000184 e3a015fe    348 	mov	r1,254<<22

00000188 e2811440    349 	add	r1,r1,1<<30

0000018c e0003001    350 	and	r3,r0,r1

00000190 e1530001    351 	cmp	r3,r1

                     352 ;103:     {


                     353 

                     354 ;104:         intValue = -1;


                     355 


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_alk1.s
00000194 03e00000    356 	mvneq	r0,0

00000198 01a01000    357 	moveq	r1,r0

0000019c 0a000002    358 	beq	.L335

                     359 ;105:     }


                     360 ;106:     else


                     361 ;107:     {


                     362 

                     363 ;108:         float realValue = entity->realValue * extInfo->f.multiplier;


                     364 

000001a0 e5921004    365 	ldr	r1,[r2,4]

000001a4 eb000000*   366 	bl	__fmul

                     367 ;109:         intValue = (uint64_t)realValue;


                     368 

000001a8 eb000000*   369 	bl	__ftou64

                     370 .L335:

                     371 ;110:     }


                     372 ;111:     entity->cache.int64Value = intValue;


                     373 

000001ac e98d0003    374 	stmfa	[sp],{r0-r1}

000001b0 e584003c    375 	str	r0,[r4,60]

000001b4 e28d0004    376 	add	r0,sp,4

000001b8 e5841040    377 	str	r1,[r4,64]

                     378 ;112:     entity->cachedBERLen =


                     379 

000001bc e3a01008    380 	mov	r1,8

000001c0 eb000000*   381 	bl	BERCoder_calcIntEncodedLen

000001c4 e5840038    382 	str	r0,[r4,56]

                     383 ;113:         BERCoder_calcIntEncodedLen(&intValue, sizeof(intValue));


                     384 ;114:     entity->cached = true;


                     385 

000001c8 e3a00001    386 	mov	r0,1

000001cc e5c40034    387 	strb	r0,[r4,52]

000001d0 e28dd00c    388 	add	sp,sp,12

000001d4 e8bd4010    389 	ldmfd	[sp]!,{r4,lr}

000001d8 e12fff1e*   390 	ret	

                     391 	.endf	realAsInt64FillCache

                     392 	.align	4

                     393 ;extInfo	r2	local

                     394 ;intValue	[sp,4]	local

                     395 ;value	[sp]	local

                     396 

                     397 ;entity	r4	param

                     398 

                     399 	.section ".bss","awb"

                     400 .L380:

                     401 	.data

                     402 	.text

                     403 

                     404 ;115: }


                     405 

                     406 ;116: 


                     407 ;117: static bool realAsInt64CalcReadLen(IEDEntity entity, size_t* pLen )


                     408 	.align	4

                     409 	.align	4

                     410 realAsInt64CalcReadLen:

000001dc e92d4030    411 	stmfd	[sp]!,{r4-r5,lr}

000001e0 e1a04000    412 	mov	r4,r0

                     413 ;118: {


                     414 

                     415 ;119:     if(!entity->cached)


                     416 


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_alk1.s
000001e4 e5d40034    417 	ldrb	r0,[r4,52]

000001e8 e1a05001    418 	mov	r5,r1

000001ec e3500000    419 	cmp	r0,0

                     420 ;120:     {


                     421 

                     422 ;121:         realAsInt64FillCache(entity);


                     423 

000001f0 01a00004    424 	moveq	r0,r4

000001f4 0bffffdc*   425 	bleq	realAsInt64FillCache

                     426 ;122:     }


                     427 ;123:     *pLen = entity->cachedBERLen + 2;


                     428 

000001f8 e5940038    429 	ldr	r0,[r4,56]

000001fc e2800002    430 	add	r0,r0,2

00000200 e5850000    431 	str	r0,[r5]

                     432 ;124:     return true;


                     433 

00000204 e3a00001    434 	mov	r0,1

00000208 e8bd4030    435 	ldmfd	[sp]!,{r4-r5,lr}

0000020c e12fff1e*   436 	ret	

                     437 	.endf	realAsInt64CalcReadLen

                     438 	.align	4

                     439 

                     440 ;entity	r4	param

                     441 ;pLen	r5	param

                     442 

                     443 	.section ".bss","awb"

                     444 .L445:

                     445 	.data

                     446 	.text

                     447 

                     448 ;125: }


                     449 

                     450 ;126: 


                     451 ;127: static bool realAsInt64EncodeRead(IEDEntity entity, BufferView* outBuf)


                     452 	.align	4

                     453 	.align	4

                     454 realAsInt64EncodeRead:

00000210 e92d4030    455 	stmfd	[sp]!,{r4-r5,lr}

00000214 e1a04000    456 	mov	r4,r0

                     457 ;128: {


                     458 

                     459 ;129:     if(!entity->cached)


                     460 

00000218 e5d40034    461 	ldrb	r0,[r4,52]

0000021c e1a05001    462 	mov	r5,r1

00000220 e3500000    463 	cmp	r0,0

                     464 ;130:     {


                     465 

                     466 ;131:         realAsInt64FillCache(entity);


                     467 

00000224 01a00004    468 	moveq	r0,r4

00000228 0bffffcf*   469 	bleq	realAsInt64FillCache

                     470 ;132:     }


                     471 ;133: 


                     472 ;134:     if(!BufferView_encodeTL(outBuf, IEC61850_BER_INTEGER, entity->cachedBERLen))


                     473 

0000022c e5942038    474 	ldr	r2,[r4,56]

00000230 e1a00005    475 	mov	r0,r5

00000234 e3a01085    476 	mov	r1,133

00000238 eb000000*   477 	bl	BufferView_encodeTL


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_alk1.s
0000023c e3500000    478 	cmp	r0,0

00000240 0a000005    479 	beq	.L466

                     480 ;135:     {


                     481 

                     482 ;136:         ERROR_REPORT("Uable to write TL");


                     483 ;137:         return false;


                     484 

                     485 ;138:     }


                     486 ;139: 


                     487 ;140:     if(!BufferView_reverseWrite(outBuf, &entity->cache.int64Value,


                     488 

00000244 e5942038    489 	ldr	r2,[r4,56]

00000248 e284103c    490 	add	r1,r4,60

0000024c e1a00005    491 	mov	r0,r5

00000250 eb000000*   492 	bl	BufferView_reverseWrite

00000254 e3500000    493 	cmp	r0,0

                     494 ;145:     }


                     495 ;146: 


                     496 ;147:     return true;


                     497 

00000258 13a00001    498 	movne	r0,1

                     499 .L466:

                     500 ;141:         entity->cachedBERLen))


                     501 ;142:     {


                     502 

                     503 ;143:         ERROR_REPORT("Uable to write int64 value");


                     504 ;144:         return false;


                     505 

0000025c 03a00000    506 	moveq	r0,0

                     507 .L458:

00000260 e8bd4030    508 	ldmfd	[sp]!,{r4-r5,lr}

00000264 e12fff1e*   509 	ret	

                     510 	.endf	realAsInt64EncodeRead

                     511 	.align	4

                     512 

                     513 ;entity	r4	param

                     514 ;outBuf	r5	param

                     515 

                     516 	.section ".bss","awb"

                     517 .L546:

                     518 	.data

                     519 	.text

                     520 

                     521 ;148: }


                     522 

                     523 ;149: 


                     524 ;150: void IEDRealAsInt64_init(IEDEntity entity)


                     525 	.align	4

                     526 	.align	4

                     527 IEDRealAsInt64_init::

00000268 e92d4070    528 	stmfd	[sp]!,{r4-r6,lr}

0000026c e59f5164*   529 	ldr	r5,.L597

                     530 ;151: {


                     531 

                     532 ;152:     commonInit(entity);


                     533 

00000270 e280405c    534 	add	r4,r0,92

00000274 ebffff65*   535 	bl	commonInit

                     536 ;153:     entity->calcReadLen = realAsInt64CalcReadLen;


                     537 

                     538 ;154:     entity->encodeRead = realAsInt64EncodeRead;



                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_alk1.s
                     539 

00000278 e59f015c*   540 	ldr	r0,.L598

0000027c e8840021    541 	stmea	[r4],{r0,r5}

                     542 ;155:     entity->updateFromDataSlice = realUpdateFromDataSlice;


                     543 

00000280 e59f014c*   544 	ldr	r0,.L326

00000284 e584000c    545 	str	r0,[r4,12]

00000288 e8bd8070    546 	ldmfd	[sp]!,{r4-r6,pc}

                     547 	.endf	IEDRealAsInt64_init

                     548 	.align	4

                     549 

                     550 ;entity	r4	param

                     551 

                     552 	.section ".bss","awb"

                     553 .L590:

                     554 	.data

                     555 	.text

                     556 

                     557 ;156: }


                     558 

                     559 ;157: 


                     560 ;158: //===================FLOAT======================


                     561 ;159: static void floatUpdateFromDataSlice(IEDEntity entity)


                     562 	.align	4

                     563 	.align	4

                     564 floatUpdateFromDataSlice:

0000028c e92d4010    565 	stmfd	[sp]!,{r4,lr}

00000290 e1a04000    566 	mov	r4,r0

                     567 ;160: {


                     568 

                     569 ;161:     int offset  = entity->dataSliceOffset;


                     570 

00000294 e594002c    571 	ldr	r0,[r4,44]

                     572 ;162:     int value;


                     573 ;163: 


                     574 ;164:     if(offset == -1)


                     575 

00000298 e3700001    576 	cmn	r0,1

0000029c 0a00000f    577 	beq	.L599

                     578 ;165:     {


                     579 

                     580 ;166:         return;


                     581 

                     582 ;167:     }


                     583 ;168: 


                     584 ;169:     value = DataSlice_getFixedFastCurrDS(offset);


                     585 

000002a0 e1a00800    586 	mov	r0,r0 lsl 16

000002a4 e1a00820    587 	mov	r0,r0 lsr 16

000002a8 eb000000*   588 	bl	DataSlice_getFixedFastCurrDS

                     589 ;170: 


                     590 ;171:     if(entity->fixedValue == value)


                     591 

000002ac e5941030    592 	ldr	r1,[r4,48]

000002b0 e1510000    593 	cmp	r1,r0

                     594 ;172:     {


                     595 

                     596 ;173:         entity->changed = TRGOP_NONE;


                     597 

000002b4 03a00000    598 	moveq	r0,0

000002b8 05840028    599 	streq	r0,[r4,40]


                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_alk1.s
000002bc 0a000007    600 	beq	.L599

                     601 ;174:     }


                     602 ;175:     else


                     603 ;176:     {


                     604 

                     605 ;177:         entity->changed = entity->trgOps;


                     606 

000002c0 e5941024    607 	ldr	r1,[r4,36]

000002c4 e5840030    608 	str	r0,[r4,48]

                     609 ;179:         IEDEntity_setTimeStamp(entity, dataSliceGetTimeStamp());


                     610 

000002c8 e5841028    611 	str	r1,[r4,40]

                     612 ;178:         entity->fixedValue = value;


                     613 

000002cc eb000000*   614 	bl	dataSliceGetTimeStamp

000002d0 e1a02001    615 	mov	r2,r1

000002d4 e1a01000    616 	mov	r1,r0

000002d8 e1a00004    617 	mov	r0,r4

000002dc eb000000*   618 	bl	IEDEntity_setTimeStamp

                     619 .L599:

000002e0 e8bd4010    620 	ldmfd	[sp]!,{r4,lr}

000002e4 e12fff1e*   621 	ret	

                     622 	.endf	floatUpdateFromDataSlice

                     623 	.align	4

                     624 ;offset	r0	local

                     625 ;value	r0	local

                     626 

                     627 ;entity	r4	param

                     628 

                     629 	.section ".bss","awb"

                     630 .L648:

                     631 	.data

                     632 	.text

                     633 

                     634 ;180:     }


                     635 ;181: }


                     636 

                     637 ;182: 


                     638 ;183: static bool floatEncodeRead(IEDEntity entity, BufferView* outBuf)


                     639 	.align	4

                     640 	.align	4

                     641 floatEncodeRead:

000002e8 e92d40f0    642 	stmfd	[sp]!,{r4-r7,lr}

000002ec e24dd010    643 	sub	sp,sp,16

                     644 ;184: {


                     645 

                     646 ;185:     uint8_t* encodeBuf;


                     647 ;186:     TerminalItem* extInfo = entity->extInfo;


                     648 

                     649 ;187:     int fixedValue = entity->fixedValue;


                     650 

000002f0 e5907030    651 	ldr	r7,[r0,48]

                     652 ;188:     float floatValue;


                     653 ;189: 


                     654 ;190:     if(fixedValue != 0x7FFFFFFF)


                     655 

000002f4 e1a06001    656 	mov	r6,r1

000002f8 e3770360    657 	cmn	r7,0x80000001

000002fc 0a000012    658 	beq	.L672

00000300 e5901058    659 	ldr	r1,[r0,88]

                     660 ;191:     {



                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_alk1.s
                     661 

                     662 ;192: 		double doubleValue = fixedValue;        


                     663 

00000304 e5910004    664 	ldr	r0,[r1,4]

00000308 eb000000*   665 	bl	__ftod

0000030c e1a05001    666 	mov	r5,r1

00000310 e1a04000    667 	mov	r4,r0

00000314 e1a00007    668 	mov	r0,r7

00000318 eb000000*   669 	bl	__itod

0000031c e1a02004    670 	mov	r2,r4

00000320 e1a03005    671 	mov	r3,r5

00000324 eb000000*   672 	bl	__dmul

                     673 ;193:         doubleValue *= extInfo->f.multiplier;


                     674 

                     675 ;194: 		floatValue = (float)doubleValue;


                     676 

00000328 eb000000*   677 	bl	__dtof

0000032c e28d2008    678 	add	r2,sp,8

00000330 e1a04000    679 	mov	r4,r0

00000334 e1a00006    680 	mov	r0,r6

00000338 e3a01007    681 	mov	r1,7

0000033c eb000000*   682 	bl	BufferView_alloc

                     683 ;199:     }


                     684 ;200: 


                     685 ;201:     if(!BufferView_alloc(outBuf,FLOAT_ENCODED_SIZE, &encodeBuf))


                     686 

00000340 e3500000    687 	cmp	r0,0

00000344 0a000008    688 	beq	.L675

00000348 ea000008    689 	b	.L674

                     690 .L672:

                     691 ;195:     }


                     692 ;196:     else


                     693 ;197:     {


                     694 

                     695 ;198:         floatValue = fnan(0);


                     696 

0000034c e3a045ff    697 	mov	r4,255<<22

00000350 e2844440    698 	add	r4,r4,1<<30

00000354 e58d400c    699 	str	r4,[sp,12]

00000358 e28d2008    700 	add	r2,sp,8

0000035c e1a00006    701 	mov	r0,r6

00000360 e3a01007    702 	mov	r1,7

00000364 eb000000*   703 	bl	BufferView_alloc

                     704 ;199:     }


                     705 ;200: 


                     706 ;201:     if(!BufferView_alloc(outBuf,FLOAT_ENCODED_SIZE, &encodeBuf))


                     707 

00000368 e3500000    708 	cmp	r0,0

                     709 .L675:

                     710 ;202:     {


                     711 

                     712 ;203:         ERROR_REPORT("Unable to allocate buffer");


                     713 ;204:         return false;


                     714 

0000036c 0a00000b    715 	beq	.L665

                     716 .L674:

                     717 ;205:     }


                     718 ;206: 


                     719 ;207:     // Возвращаемое значение не нужно,


                     720 ;208:     // потому что функция не возвращает ошибки, а размер известен заранее


                     721 ;209:     BerEncoder_EncodeFloatWithTL(0x87, floatValue, 32, 8, encodeBuf, 0);



                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_alk1.s
                     722 

00000370 e59d0008    723 	ldr	r0,[sp,8]

00000374 e3a01000    724 	mov	r1,0

00000378 e88d0003    725 	stmea	[sp],{r0-r1}

0000037c e1a01004    726 	mov	r1,r4

00000380 e3a03008    727 	mov	r3,8

00000384 e3a02020    728 	mov	r2,32

00000388 e3a00087    729 	mov	r0,135

0000038c eb000000*   730 	bl	BerEncoder_EncodeFloatWithTL

                     731 ;210:     outBuf->pos += FLOAT_ENCODED_SIZE;


                     732 

00000390 e5960004    733 	ldr	r0,[r6,4]

00000394 e2800007    734 	add	r0,r0,7

00000398 e5860004    735 	str	r0,[r6,4]

                     736 ;211:     return true;


                     737 

0000039c e3a00001    738 	mov	r0,1

                     739 .L665:

000003a0 e28dd010    740 	add	sp,sp,16

000003a4 e8bd40f0    741 	ldmfd	[sp]!,{r4-r7,lr}

000003a8 e12fff1e*   742 	ret	

                     743 	.endf	floatEncodeRead

                     744 	.align	4

                     745 ;encodeBuf	[sp,8]	local

                     746 ;extInfo	r1	local

                     747 ;fixedValue	r7	local

                     748 ;floatValue	r4	local

                     749 ;inan	[sp,12]	local

                     750 

                     751 ;entity	r0	param

                     752 ;outBuf	r6	param

                     753 

                     754 	.section ".bss","awb"

                     755 .L762:

                     756 	.data

                     757 	.text

                     758 

                     759 ;212: }


                     760 

                     761 ;213: 


                     762 ;214: void IEDFloat_init(IEDEntity entity)


                     763 	.align	4

                     764 	.align	4

                     765 IEDFloat_init::

000003ac e92d4070    766 	stmfd	[sp]!,{r4-r6,lr}

000003b0 e59f5028*   767 	ldr	r5,.L805

000003b4 e59f6028*   768 	ldr	r6,.L806

                     769 ;215: {


                     770 

                     771 ;216:     commonInit(entity);


                     772 

000003b8 e1a04000    773 	mov	r4,r0

000003bc ebffff13*   774 	bl	commonInit

                     775 ;217:     entity->encodeRead = floatEncodeRead;


                     776 

000003c0 e584505c    777 	str	r5,[r4,92]

                     778 ;218:     entity->updateFromDataSlice = floatUpdateFromDataSlice;


                     779 

000003c4 e5846068    780 	str	r6,[r4,104]

000003c8 e8bd8070    781 	ldmfd	[sp]!,{r4-r6,pc}

                     782 	.endf	IEDFloat_init


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_alk1.s
                     783 	.align	4

                     784 

                     785 ;entity	r4	param

                     786 

                     787 	.section ".bss","awb"

                     788 .L798:

                     789 	.data

                     790 	.text

                     791 

                     792 ;219: }


                     793 	.align	4

                     794 .L104:

000003cc 00000000*   795 	.data.w	calcReadLen

                     796 	.type	.L104,$object

                     797 	.size	.L104,4

                     798 

                     799 .L325:

000003d0 00000000*   800 	.data.w	realEncodeRead

                     801 	.type	.L325,$object

                     802 	.size	.L325,4

                     803 

                     804 .L326:

000003d4 00000000*   805 	.data.w	realUpdateFromDataSlice

                     806 	.type	.L326,$object

                     807 	.size	.L326,4

                     808 

                     809 .L597:

000003d8 00000000*   810 	.data.w	realAsInt64CalcReadLen

                     811 	.type	.L597,$object

                     812 	.size	.L597,4

                     813 

                     814 .L598:

000003dc 00000000*   815 	.data.w	realAsInt64EncodeRead

                     816 	.type	.L598,$object

                     817 	.size	.L598,4

                     818 

                     819 .L805:

000003e0 00000000*   820 	.data.w	floatEncodeRead

                     821 	.type	.L805,$object

                     822 	.size	.L805,4

                     823 

                     824 .L806:

000003e4 00000000*   825 	.data.w	floatUpdateFromDataSlice

                     826 	.type	.L806,$object

                     827 	.size	.L806,4

                     828 

                     829 	.align	4

                     830 

                     831 	.data

                     832 	.ghsnote version,6

                     833 	.ghsnote tools,3

                     834 	.ghsnote options,0

                     835 	.text

                     836 	.align	4

