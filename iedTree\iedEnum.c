#include "iedInt.h"

#include "iedTree.h"
#include "iedFinalDA.h"

#include "../DataSlice.h"
#include "../AsnEncoding.h"
#include "../mms_data.h"

#include "debug.h"

#include "IEDCompile/AccessInfo.h"



static void updateFromDataSlice(IEDEntity entity)
{
    int offset  = entity->dataSliceOffset;
    int32_t value;

    if(offset == -1)
    {
        return;
    }

    value = DataSlice_getInt32FastCurrDS(offset);


    if(entity->intValue == value)
    {
        entity->changed = TRGOP_NONE;
    }
    else
    {
        entity->changed = entity->trgOps;
        entity->intValue = value;
        entity->cached = false;
        IEDEntity_setTimeStamp(entity, dataSliceGetTimeStamp());
    }
}


// Вычисляет и записывает в кэш длину BER и значение
static void calcAndCacheLenAndVal(IEDEntity entity)
{
    TerminalItem* extInfo = entity->extInfo;
    IntBoolAccessInfo* accessInfo = extInfo->accessInfo;
    int32_t enumValue;

    enumValue = getEnumValue(entity->intValue, accessInfo->enumTable,
                         accessInfo->enumTableSize);

    entity->cache.enumValue = enumValue;


    entity->cachedBERLen = BerEncoder_Int32DetermineEncodedSize(enumValue) + 2;
    entity->cached = true;

}

static bool calcReadLen(IEDEntity entity, size_t* pLen )
{
    if(entity->cached)
    {
        *pLen = entity->cachedBERLen;

        return true;
    }

    calcAndCacheLenAndVal(entity);

    *pLen = entity->cachedBERLen;
    return true;
}

static bool encodeRead(IEDEntity entity, BufferView* outBuf)
{

    uint8_t* encodeBuf;
    int fullEncodedLen;


    if(!entity->cached)
    {
        ERROR_REPORT("Length and value are not cached");
        calcAndCacheLenAndVal(entity);
    }

    if(!BufferView_alloc(outBuf,entity->cachedBERLen, &encodeBuf))
    {
        ERROR_REPORT("Unable to allocate buffer");
        return false;
    }

    //Функция возвращает новое смещение в буфере, но поскольку начальное
    //смещение 0, можно считать это размером.
    fullEncodedLen = BerEncoder_EncodeInt32WithTL(
                IEC61850_BER_INTEGER, entity->cache.enumValue, encodeBuf, 0);

    outBuf->pos += fullEncodedLen;
    return true;
}


void IEDEnum_init(IEDEntity entity)
{
    TerminalItem* extInfo = entity->extInfo;
    IntBoolAccessInfo* accessInfo = extInfo->accessInfo;

    //Если будет ошибка, то запишется -1;
    entity->dataSliceOffset = DataSlice_getIntOffset(accessInfo->valueOffset);


    entity->calcReadLen = calcReadLen;
    entity->encodeRead = encodeRead;
    entity->updateFromDataSlice = updateFromDataSlice;

    IEDTree_addToCmpList(entity);
}

