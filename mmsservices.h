#pragma once

#include "IsoConnectionForward.h"

//MMMS Services Ber Tags
#define	MMS_SERVICE_GET_NAME_LIST_CODE 0xa1
#define	MMS_SERVICE_READ_CODE 0xa4
#define	MMS_SERVICE_WRITE_CODE 0xA5
#define	MMS_SERVICE_GET_VARIABLE_ACCESS_ATRIBUTES_CODE 0xa6
#define MMS_SERVICE_GET_DATA_SET_ATTRIBUTES_CODE 0xAC
#define MMS_SERVICE_IDENTIFY_CODE 0x82

#define MMS_SERVICE_FILE_OPEN_REQUEST 0xBF48
#define MMS_SERVICE_FILE_READ_REQUEST 0x9F49
#define MMS_SERVICE_FILE_CLOSE_REQUEST 0x9F4A
#define MMS_SERVICE_FILE_DIRECTORY_REQUEST 0xBF4D


int handleConfirmedRequestPdu(IsoConnection* isoConn,
                               unsigned char* inBuf, int bufPos, int bufMaxPos,
                               unsigned char* outBuf, int maxBufSize);
