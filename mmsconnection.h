#pragma once



#include "MmsConst.h"
#include "DomainNameWriter.h"
#include "stringView.h"
#include "iedTree/iedEntity.h"

#include <stdint.h>

typedef struct {
    DomainNameWriter nameWriter;
    uint8_t infoReportDataBuf[DEFAULT_REPORT_BUFFER_SIZE];
    uint8_t infoReportBuf[DEFAULT_REPORT_BUFFER_SIZE];
    uint8_t infoReportPresentationBuf[DEFAULT_REPORT_BUFFER_SIZE];

    //! Сюда сохраняется DomainId записываемого объекта
    StringView wrDomainId;
    //! Сюда сохраняется ItemId записываемого объекта
    StringView wrItemId;
    //! Имя объекта управления (скорее всего pos) при записи
    //! используется для формирования information report
    uint8_t wrCrtlObjNameBuf[MAX_OBJECT_REFERENCE];

    //! Список результатов записи списка переменных
    //! Содержит коды ошибок или -1 если ошибки нет
    MmsDataAccessError wrResults[MAX_WR_RESULT_LIST];

    //! При чтении значений переменных по списку сюда складываются
    //! указатели на объекты информационной модели.
    //! После последнего элемента должен быть 0
    IEDEntity readVarObjList[MAX_READ_VARIABLE_OBJECT_LIST];

	uint8_t fileBuf[FILE_BUF_SIZE];
	uint8_t fileName[FILE_NAME_BUF_SIZE];
	bool isFileOpen;
	//! File Read State Mashine ID для текущего открытого файла.
	//! Валидно только если isFileOpen == TRUE;
	uint32_t frsmID;
} MmsConnection;


