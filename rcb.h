#pragma once

#include "local_types.h"
#include "stringView.h"
#include <stdbool.h>
#include <types.h>

#define OPTFLDS_CONF_REV (1 << 1)
#define OPTFLDS_ENTRY_ID (1 << 2)
#define OPTFLDS_BUFFER_OVERFLOW (1 << 3)
#define OPTFLDS_DATA_REF (1 << 4)
#define OPTFLDS_DATA_SET (1 << 5)
#define OPTFLDS_REASON_FOR_INCLUSION (1 << 6)
#define OPTFLDS_TIME_STAMP (1 << 7)
#define OPTFLDS_SEQ_NUM (1 << 8)


#define SUPPORTED_OPTFLDS (OPTFLDS_DATA_SET | OPTFLDS_TIME_STAMP \
	| OPTFLDS_REASON_FOR_INCLUSION | OPTFLDS_ENTRY_ID | OPTFLDS_CONF_REV \
	| OPTFLDS_BUFFER_OVERFLOW )

typedef struct {
	bool buffered;
	volatile bool rptEna;
    bool resv;
	StringView rptID;
	uint8_t* dataSetName;
	int dataSetNameLength;
	uint32_t confRev;
	uint16_t sqNum;
	uint8_t trgOps;
	//! Integrity interval
	uint32_t intgPd;
	uint16_t optFlds;
	//! General interrorgation
	bool gi;
	unsigned long long entryID;
} RCB;

bool getRCB(size_t index, RCB** pRCB);

void registerAllRCB(void);


