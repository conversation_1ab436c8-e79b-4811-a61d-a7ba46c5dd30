#pragma once

//Типы для файловой системы
#include "../stringView.h"
#include "OscReadFileContext.h"

typedef enum { FS_SUB_CFG = 0, FS_SUB_OSC = 1 } FSSybsystem;

typedef struct
{
	bool busy;
	//! Индекс директория фактически является номером подсистемы
	//! которая открыла файл.
	FSSybsystem subsystem;
	//! Эти поля подсистемы чтения используют по своему усмотрению.
	//! Они хранятся от момента открытия файла до закрытия
    void* ptr;
    uint8_t* start;
    size_t pos;
    size_t size;
	OscReadFileContext *readOscContext;
} FRSM;

typedef enum {
	FNAME_OK,
	FNAME_NOT_FOUND,
	FNAME_BUF_ERROR, //Имя не помещается в буфер
	FNAME_ERROR // Прочая ошибка
} FNameErrCode;

typedef struct {
	//! Размер файла. Допускается значение 0, если размер неизвестен
	size_t fileSize;
	uint32_t time;
	int ms;
} FSFileAttr;

typedef struct {
	//! Происходит получение списка коренвого директория
	bool rootDir;	
	size_t fileIndex;

	FSSybsystem subsystem;

	StringView fileName;
	FSFileAttr attr;
} FSFindData;
