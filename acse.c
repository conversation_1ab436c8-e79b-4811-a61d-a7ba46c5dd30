#include "acse.h"
#include "AsnEncoding.h"  

#include <string.h>

#pragma alignvar (4)
unsigned char appContextNameMms[] = { 0x28, 0xca, 0x22, 0x02, 0x03 };

void AcseConnection_init(AcseConnection* acse)
{
    //TODO acse->nextReference Что это?
    acse->nextReference = 3;
}

int AcseConnection_createAssociateResponseMessage(AcseConnection* acse,
        unsigned char* buf, unsigned char* userData, int userDataLen,unsigned char acseResult)
{
    int contentLength;
    int bufPos = 0;

    int appContextLength = 9;
    int resultLength = 5;
    int resultDiagnosticLength = 5;

    int fixedContentLength = appContextLength + resultLength + resultDiagnosticLength;

    int variableContentLength = 0;

    int assocDataLength;
    int userInfoLength;
    int nextRefLength;

    /* single ASN1 type tag */
    variableContentLength += userDataLen;
    variableContentLength += 1;
    variableContentLength += BerEncoder_determineLengthSize(userDataLen);

    /* indirect reference */
    nextRefLength = BerEncoder_UInt32determineEncodedSize(acse->nextReference);
    variableContentLength += nextRefLength;
    variableContentLength += 2;

    /* association data */
    assocDataLength = variableContentLength;
    variableContentLength += BerEncoder_determineLengthSize(assocDataLength);
    variableContentLength += 1;

    /* user information */
    userInfoLength = variableContentLength;
    variableContentLength += BerEncoder_determineLengthSize(userInfoLength);
    variableContentLength += 1;

    variableContentLength += 2;

    contentLength = fixedContentLength + variableContentLength;

    bufPos = BerEncoder_encodeTL(AARE_PACKET, contentLength, buf, bufPos);

    /* application context name */
    bufPos = BerEncoder_encodeTL(APPLICATION_CONTEXT_NAME, 7, buf, bufPos);
    bufPos = BerEncoder_encodeTL(ASN_OBJECT_IDENTIFIER, 5, buf, bufPos);
    memcpy(buf + bufPos, appContextNameMms, 5);
    bufPos += 5;

    /* result */
    bufPos = BerEncoder_encodeTL(AARE_PACKET_RESULT, 3, buf, bufPos);
    bufPos = BerEncoder_encodeTL(ASN_INTEGER, 1, buf, bufPos);
    buf[bufPos++] = acseResult;

    /* result source diagnostics */
    bufPos = BerEncoder_encodeTL(RESULT_SOURCE_DIAGNOSTICS, 5, buf, bufPos);
    bufPos = BerEncoder_encodeTL(0xa1, 3, buf, bufPos);
    bufPos = BerEncoder_encodeTL(ASN_INTEGER, 1, buf, bufPos);
    buf[bufPos++] = 0;

    /* user information */
    bufPos = BerEncoder_encodeTL(USER_INFORMATION, userInfoLength, buf, bufPos);

    /* association data */
    bufPos = BerEncoder_encodeTL(ACSE_USER_INFORMATION, assocDataLength, buf,
                                 bufPos);

    /* indirect-reference */
    bufPos = BerEncoder_encodeTL(ASN_INTEGER, nextRefLength, buf, bufPos);
    bufPos = BerEncoder_encodeUInt32(acse->nextReference, buf, bufPos);

    /* single ASN1 type */
    bufPos = BerEncoder_encodeTL(USER_INFORMATION_ENCODING, userDataLen,
                                 buf, bufPos);

    memcpy(buf + bufPos,userData,userDataLen);

    return bufPos + userDataLen;
}


