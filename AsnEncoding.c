#include "AsnEncoding.h"

#include "bufViewBER.h"
#include <string.h>
#include <stdlib.h>


int BerEncoder_encodeLength( unsigned int iLength, unsigned char* pBuffer, int iBufPos )
{
    if ( iLength < 128 )
    {
        pBuffer[iBufPos++] = (unsigned char)iLength;
    }
    else if ( iLength < 256 )
    {
        pBuffer[iBufPos++] = 0x81;
        pB<PERSON>er[iBufPos++] = (unsigned char)iLength;
    }
    else
    {
        pBuffer[iBufPos++] = 0x82;
        pBuffer[iBufPos++] = iLength / 256;
        pBuffer[iBufPos++] = iLength % 256;

    }

    return iBufPos;
}

int BerDecoder_decodeLength(unsigned char* buffer, int* length, int bufPos, int maxBufPos)
{
    unsigned char len1;
    int i;

    if (bufPos >= maxBufPos)
        return -1;

     len1 = buffer[bufPos++];

    if (len1 & 0x80) {
        int lenLength = len1 & 0x7f;

        if (lenLength == 0) { /* indefinite length form */
            *length = -1;
        }
        else {
            *length = 0;


            for (i = 0; i < lenLength; i++) {
                if (bufPos >= maxBufPos)
                    return -1;

                *length <<= 8;
                *length += buffer[bufPos++];
            }
        }

    }
    else {
        *length = len1;
    }

    if (*length < 0)
        return -1;

    /*
    Проверка на правдоподобность длины с учётом размера буфера мешает
    if (bufPos + (*length) > maxBufPos)
        return -1;
    */

    return bufPos;
}




bool BerDecoder_decodeTLFromBufferView(BufferView* buf, uint8_t* pTag, int* pLen, int* pFullLen)
{
    uint8_t tag;
    int len;
    int objPos = buf->pos;

    tag = BufferView_readTag(buf);
    buf->pos = BerDecoder_decodeLength(buf->p, &len, buf->pos, buf->len);
    if (buf->pos <= 0)
    {
        return FALSE;
    }

    if (pTag != NULL)
    {
        *pTag = tag;
    }

    if (pLen != NULL)
    {
        *pLen = len;
    }

    if (pFullLen != NULL)
    {
        *pFullLen = buf->pos - objPos + len;
    }

    return TRUE;
}

int32_t BerDecoder_decodeInt32(uint8_t* buffer, int intlen, int bufPos)
{
    int32_t value;
    int i;

    bool isNegative = ((buffer[bufPos] & 0x80) == 0x80);

    if (isNegative)
        value = -1;
    else
        value = 0;

    for (i = 0; i < intlen; i++) {
        value <<= 8;
        value += buffer[bufPos + i];
    }

    return value;
}

unsigned int BerDecoder_decodeUint32(unsigned char* buffer, int intLen, int bufPos)
{
    unsigned int value = 0;

    int i;
    for (i = 0; i < intLen; i++) {
        value <<= 8;
        value += buffer[bufPos + i];
    }

    return value;
}

int BerEncoder_encodeTL( unsigned char ucTag, unsigned int iLength,
                         unsigned char* pBuffer, int iBufPos )
{
    pBuffer[iBufPos++] = ucTag;
    iBufPos = BerEncoder_encodeLength( iLength, pBuffer, iBufPos );
    return iBufPos;
}

int BerDecoder_decodeString(uint8_t* buf, int bufPos, int maxPos, uint8_t** str, int* strLen)
{
    uint8_t tag = buf[bufPos++];
    bufPos = BerDecoder_decodeLength(buf, strLen, bufPos,  maxPos);
    if(bufPos == -1)
    {
        return -1;
    }
    *str = buf + bufPos;
    return bufPos + *strLen;
}

int EncodeBOOLEAN( unsigned char* pBuffer, unsigned char bValue )
{
    pBuffer[0] = ASN_BOOLEAN;	//идентификатор
    pBuffer[1] = 0x01;			//длина
    pBuffer[2] = 0x00;			//значение
    if( bValue > 0 )
    {
        pBuffer[2] = 0x01;
    }
    return ASN_BOOLEANTYPE_SIZE;
}

int DecodeBOOLEAN( unsigned char* pBuffer, int iBufPos, unsigned char* pValueOut )
{

    if( pBuffer[iBufPos] != ASN_BOOLEAN )
    {//не тип BOOLEAN
        return -1;
    }
    if( pBuffer[iBufPos+1] != 0x01 )
    {//размер не 0x01
        return -1;
    }


    *pValueOut = pBuffer[iBufPos+2];

    return iBufPos+3;
}

int EncodeBIT_STRINGPrimitive( unsigned char* pBuffer, unsigned char* pBitString, int iLengthInBit )
{
    unsigned char ucRemn = (unsigned char)( iLengthInBit & 0x07 );	//остаток от деления на 8
    unsigned char ucUnusedBitsQuant = 0;							//количество незначащих бит в конце
    int			  iSize;											//длина всей посылки ASN

    if( ucRemn > 0 )
    {
        ucUnusedBitsQuant = 8 - ucRemn;
    }

    iSize = ( iLengthInBit + ucUnusedBitsQuant ) / 8 + 1;	//общая длина в байтах ( +1 это головной байт для незначащих бит )


    pBuffer[0] = ASN_BIT_STRING;			//идентификатор BIT_STRING
    if( iSize > 127 )
    {
     //BIT_STRING такого размера не используются
        return 0;
    }
    else
    {
        pBuffer[1] = (unsigned char)iSize;				//длина
        pBuffer[2] = ucUnusedBitsQuant;					//количество незначащих бит в конце посылки
        memcpy( &pBuffer[3], pBitString, iSize - 1 );	//битовая строка
        return iSize + 2;
    }
}
int DecodeBIT_STRINGPrimitive( unsigned char* pBuffer, unsigned char* pBitStringOut,
                               unsigned char* pBitStringSizeOut, unsigned char* pUnusedBitsQuantOut )
{

    if( pBuffer[0] != ASN_BIT_STRING )
    {//не тип BIT_STRING
        return -1;
    }

    if( pBuffer[1] > 127 )
    {//неверный размер
        return -1;
    }

    if( pBuffer[1] == 1 )
    {//пустая битовая строка
        *pBitStringSizeOut = 0;
        return 0;
    }

    *pBitStringSizeOut = pBuffer[1] - 1;
    *pUnusedBitsQuantOut = pBuffer[2];
    memcpy( pBitStringOut, &pBuffer[3], (int)(*pBitStringSizeOut) );
    return 0;
}

int BerDecoder_DecodeBitStringTLToInt(uint8_t* inBuf, int inBufPos)
{
    int result = 0;
    int len;
    int padding;
    int i;
    //Skip tag
    inBufPos++;
    len = inBuf[inBufPos++];
    if (len > 5)
    {
        return -1;
    }
    len--;//padding не считаем
    padding = inBuf[inBufPos++];
    if (padding > 7)
    {
        return -1;
    }
    //Байт который лежит последним, должен быть младшим
    for (i = 0; i < len; ++i)
    {
        result <<= 8;
        result |= inBuf[inBufPos++];
    }
    result >>= padding;
    if (result < 0)
    {
        result = -1;
    }
    return result;
}

int BerDecoder_DecodeLengthOld(unsigned char* pBuffer, int iBufPos, int iMaxBufPos, int* pLength)
{

    unsigned char	ucLength;
    int				iLenLength;


    if( iBufPos >= iMaxBufPos )
    {

        return -1;

    }//if( iBufPos > iMaxBufPos )


    ucLength = pBuffer[iBufPos++];

    if ( ucLength & 0x80 )
    {//кодирование длины занимает больше одного байта

        iLenLength = ucLength & 0x7f;
        if( iLenLength == 0 )
        {//неопределенная форма длины

            *pLength = -1;

        }//if( iLenLength == 0 )
        else
        {
            int i;
            *pLength = 0;
            for( i = 0; i < iLenLength; i++ )
            {
                if( iBufPos >= iMaxBufPos )
                {

                    return -1;

                }//if( iBufPos > iMaxBufPos )

                *pLength <<= 8;
                *pLength += pBuffer[iBufPos++];

            }//for( i = 0; i < iLenLength; i++ )

        }//else if( iLenLength == 0 )

    }//if ( ucLength & 0x80 )
    else
    {

        *pLength = ucLength;

    }//else if ( ucLength & 0x80 )

    return iBufPos;
}

unsigned int BerDecoder_DecodeUint32Old( unsigned char* pBuffer, int iBufPos, int iLength )
{

    unsigned int Value = 0;
    int i;

    for( i = 0; i < iLength; i++ )
    {

        Value <<= 8;
        Value += pBuffer[iBufPos + i];

    }//for( i = 0; i < iLength; i++ )

    return Value;
}

int BerDecoder_DecodeObjectName(unsigned char* pBuffer, int bufPos, int iTotalLength,
    unsigned char** pItemId, int* itemIdLen, unsigned char** pDomainId, int* domainLen)
{
    unsigned char   tag=0xff;
    int             totalNameLength;

    tag = pBuffer[bufPos++];
    bufPos = BerDecoder_decodeLength(pBuffer, &totalNameLength, bufPos, iTotalLength);

    if (bufPos == -1)
    {
        return -1;
    }

    switch(tag)
    {
    case ASN_OBJECT_NAME_VMD_SPECIFIC:
        break;
    case ASN_OBJECT_NAME_DOMAIN_SPECIFIC:
        //Домен
        bufPos = BerDecoder_decodeString(pBuffer, bufPos, iTotalLength, pDomainId, domainLen);
        if (bufPos == -1)
        {
            break;
        }
        bufPos = BerDecoder_decodeString(pBuffer, bufPos, iTotalLength, pItemId, itemIdLen);
        if (bufPos == -1)
        {
            break;
        }
        return bufPos;
    case ASN_OBJECT_NAME_AA_SPECIFIC:
        break;
    default:
        break;
    }
    return -1;
}

int BerDecoder_DecodeObjectNameToStringView(unsigned char* pBuffer, int bufPos,
    int iTotalLength, StringView* domainId, StringView* itemId)
{
    return BerDecoder_DecodeObjectName(pBuffer, bufPos, iTotalLength,
        (uint8_t**)&itemId->p, (int*)&itemId->len,
        (uint8_t**)&domainId->p, (int*)&domainId->len);
}


int BerEncoder_determineLengthSize( unsigned int uLength )
{
    if( uLength < 128 )
    {
        return 1;
    }

    if( uLength < 256 )
    {
        return 2;
    }
    else
    {
        return 3;
    }
}

int BerEncoder_determineFullObjectSize(unsigned int uLength)
{
    return 1 + BerEncoder_determineLengthSize(uLength)
        + uLength;
}


int BerEncoder_UInt32determineEncodedSize( unsigned int iValue )
{

    unsigned char* pValueArray = (unsigned char*)&iValue;
    unsigned char ValueBuffer[5];

    int i;
    int iSize;

    ValueBuffer[0] = 0;


    for( i = 0; i < 4; i++ )
    {

       ValueBuffer[i + 1] = pValueArray[i];

    }//for( i = 0; i < 4; i++ )

    //если ORDER_LITTLE_ENDIAN
    BerEncoder_RevertByteOrder( ValueBuffer + 1, 4 );
    //если ORDER_LITTLE_ENDIAN
    iSize = BerEncoder_CompressInteger( ValueBuffer, 5 );
    return iSize;
}
size_t BerEncoder_uint32determineEncodedSizeTL(uint32_t value)
{
    return BerEncoder_UInt32determineEncodedSize(value) + 2;//+ размер и тэг
}


int BerEncoder_Int32DetermineEncodedSize(int iValue)
{
    unsigned char* pValueArray = (unsigned char*)&iValue;
    unsigned char ValueBuffer[4];

    int i;
    int iSize;


    for (i = 0; i < 4; i++)
    {
        ValueBuffer[i] = pValueArray[i];
    }
     //если ORDER_LITTLE_ENDIAN
    BerEncoder_RevertByteOrder(ValueBuffer, 4);
    //если ORDER_LITTLE_ENDIAN
    iSize = BerEncoder_CompressInteger(ValueBuffer, 4);
    return iSize;
}

int BerEncoder_CompressInteger( unsigned char* pInteger, int iOriginalSize )
{

    unsigned char* pIntegerEnd = pInteger + iOriginalSize - 1;
    unsigned char* pBytePosition;

    int iBytesToDelete;
    int iNewSize;

    for( pBytePosition = pInteger; pBytePosition < pIntegerEnd; pBytePosition++ )
    {

        if( pBytePosition[0] == 0x00 )
        {

            if ( ( pBytePosition[1] & 0x80 ) == 0 )
            {

                continue;

            }

        }
        else if ( pBytePosition[0] == 0xff )
        {

            if ( pBytePosition[1] & 0x80 )
            {
                continue;
            }
        }
        break;

    }

    iBytesToDelete = pBytePosition - pInteger;
    iNewSize = iOriginalSize;


    if( iBytesToDelete )
    {
        unsigned char* pNewEnd;
        unsigned char* pNewBytePosition;
        iNewSize -= iBytesToDelete;
        pNewEnd = pInteger + iNewSize;

        for( pNewBytePosition = pInteger; pNewBytePosition < pNewEnd; pNewBytePosition++ )
        {

            *pNewBytePosition = *pBytePosition;
             pBytePosition++;

        }//for( pNewBytePosition = pInteger; pNewBytePosition < pNewEnd; pNewBytePosition++ )


    }//if( iBytesToDelete )

    return iNewSize;

}

int BerEncoder_encodeUInt32( unsigned int iValue, unsigned char* pBuffer, int iBufPos )
{

    unsigned char* pValueArray = (unsigned char*)&iValue;
    unsigned char ValueBuffer[5];

    int i;
    int iSize;

    ValueBuffer[0] = 0;


    for( i = 0; i < 4; i++ )
    {

       ValueBuffer[i + 1] = pValueArray[i];

    }//for( i = 0; i < 4; i++ )

    //если ORDER_LITTLE_ENDIAN
    BerEncoder_RevertByteOrder( ValueBuffer + 1, 4 );
    //если ORDER_LITTLE_ENDIAN

    iSize = BerEncoder_CompressInteger( ValueBuffer, 5 );

    for( i = 0; i < iSize; i++ )
    {

        pBuffer[iBufPos++] = ValueBuffer[i];

    }
    return iBufPos;
}

int BerEncoder_EncodeInt32(int iValue, unsigned char* pBuffer, int iBufPos)
{
    unsigned char* pValueArray = (unsigned char*)&iValue;
    unsigned char ValueBuffer[4];

    int i;
    int iSize;

    for (i = 0; i < 4; i++)
    {
        ValueBuffer[i] = pValueArray[i];
    }
     //если ORDER_LITTLE_ENDIAN
    BerEncoder_RevertByteOrder(ValueBuffer, 4);
    //если ORDER_LITTLE_ENDIAN
    iSize = BerEncoder_CompressInteger(ValueBuffer, 4);
    for (i = 0; i < iSize; i++)
    {
        pBuffer[iBufPos++] = ValueBuffer[i];
    }
    return iBufPos;
}

int BerEncoder_EncodeFloatWithTL(unsigned char ucTag, float Value,
    unsigned char formatWidth, unsigned char exponentWidth,
    unsigned char* pBuffer, int iBufPos)
{
    unsigned char* pValueArray = (unsigned char*)&Value;
    unsigned char valueBuffer[9];
    int i;

    int byteSize = formatWidth / 8;

    valueBuffer[0] = exponentWidth;

    for (i = 0; i < byteSize; i++) {
        valueBuffer[i + 1] = pValueArray[i];
    }

    //если ORDER_LITTLE_ENDIAN
    BerEncoder_RevertByteOrder(valueBuffer + 1, byteSize);
    //если ORDER_LITTLE_ENDIAN

    pBuffer[iBufPos++] = ucTag;
    pBuffer[iBufPos++] = (unsigned char)byteSize+1;

    for (i = 0; i < byteSize + 1; i++) {
        pBuffer[iBufPos++] = valueBuffer[i];
    }

    return iBufPos;
}

int BerEncoder_encodeUInt32WithTL(unsigned char ucTag, unsigned int iValue,
        unsigned char* pBuffer, int iBufPos)
{

    unsigned char* pValueArray = (unsigned char*)&iValue;
    unsigned char ValueBuffer[5];

    int i;
    int iSize;

    ValueBuffer[0] = 0;


    for( i = 0; i < 4; i++ )
    {

       ValueBuffer[i + 1] = pValueArray[i];

    }//for( i = 0; i < 4; i++ )

    //если ORDER_LITTLE_ENDIAN
    BerEncoder_RevertByteOrder( ValueBuffer + 1, 4 );
    //если ORDER_LITTLE_ENDIAN

    iSize = BerEncoder_CompressInteger( ValueBuffer, 5 );

    pBuffer[iBufPos++] = ucTag;
    pBuffer[iBufPos++] = (unsigned char)iSize;

    for( i = 0; i < iSize; i++ )
    {

        pBuffer[iBufPos++] = ValueBuffer[i];

    }//    for( i = 0; i < iSize; i++ )

    return iBufPos;

}

int BerEncoder_EncodeInt32WithTL(unsigned char ucTag, int iValue,
        unsigned char* pBuffer, int iBufPos)
{
    unsigned char* pValueArray = (unsigned char*)&iValue;
    unsigned char ValueBuffer[4];

    int i;
    int iSize;

    for (i = 0; i < 4; i++)
    {

        ValueBuffer[i] = pValueArray[i];

    }//for( i = 0; i < 4; i++ )

     //если ORDER_LITTLE_ENDIAN
    BerEncoder_RevertByteOrder(ValueBuffer, 4);
    //если ORDER_LITTLE_ENDIAN

    iSize = BerEncoder_CompressInteger(ValueBuffer, 4);

    pBuffer[iBufPos++] = ucTag;
    pBuffer[iBufPos++] = (unsigned char)iSize;

    for (i = 0; i < iSize; i++)
    {

        pBuffer[iBufPos++] = ValueBuffer[i];

    }//    for( i = 0; i < iSize; i++ )

    return iBufPos;

}

int BerEncoder_encodeOctetString(uint8_t tag, const uint8_t *octetString, uint32_t octetStringSize,
                                 uint8_t *buffer, int bufPos)
{
    buffer[bufPos++] = tag;

    bufPos = BerEncoder_encodeLength(octetStringSize, buffer, bufPos);

    memcpy(buffer + bufPos, octetString, octetStringSize);
    bufPos += octetStringSize;

    return bufPos;
}

int BerEncoder_encodeBitString( unsigned char ucTag, int iBitStringSize, unsigned char* pBitString,
                                unsigned char* pBuffer, int iBufPos )
{
    int iByteSize = iBitStringSize / 8;
    int i;
    int iPadding;
    unsigned char ucPaddingMask = 0;

    pBuffer[iBufPos++] = ucTag;

    if ( iBitStringSize % 8 )
    {

        iByteSize++;

    }//if ( iBitStringSize % 8 )

    iPadding = ( iByteSize * 8 ) - iBitStringSize;

    iBufPos = BerEncoder_encodeLength( iByteSize + 1, pBuffer, iBufPos );

    pBuffer[iBufPos++] = iPadding;



    for ( i = 0; i < iByteSize; i++ )
    {

        pBuffer[iBufPos++] = pBitString[i];

    }//for ( i = 0; i < iByteSize; i++ )



    for( i = 0; i < iPadding; i++ )
    {

      ucPaddingMask += ( 1 << i );

    }//for( i = 0; i < iPadding; i++ )

    ucPaddingMask = ~ucPaddingMask;

    pBuffer[iBufPos -1] = pBuffer[iBufPos -1] & ucPaddingMask;

    return iBufPos;
}

int BerEncoder_encodeBitStringUshortBuf(uint8_t tag, int bitCount, uint16_t data,
    uint8_t* outBuf, int outBufPos)
{
    uint8_t padding = sizeof(uint16_t) * 8 - bitCount;
    outBuf[outBufPos++] = tag;
    outBuf[outBufPos++] = 3;//length
    outBuf[outBufPos++] = padding;
    outBuf[outBufPos++] = data & 0xFF;
    outBuf[outBufPos++] = data >> 8;
    return outBufPos;
}

int BerEncoder_encodeUshortBitString(uint8_t tag, int bitCount, uint16_t data,
    uint8_t* outBuf, int outBufPos)
{
    uint8_t padding = sizeof(uint16_t) * 8 - bitCount;
    outBuf[outBufPos++] = tag;
    outBuf[outBufPos++] = 3;//length
    outBuf[outBufPos++] = padding;
    //Сдвигаем чтобы неиспользуемые биты были младшими
    data <<= padding;
    outBuf[outBufPos++] = data >> 8;
    outBuf[outBufPos++] = data & 0xFF;
    return outBufPos;
}

int BerEncoder_encodeUcharBitString(uint8_t tag, int bitCount, uint8_t data,
    uint8_t* outBuf, int outBufPos)
{
    uint8_t padding = sizeof(uint8_t) * 8 - bitCount;
    outBuf[outBufPos++] = tag;
    outBuf[outBufPos++] = 2;//length
    outBuf[outBufPos++] = padding;
    //Сдвигаем чтобы неиспользуемые биты были младшими
    data <<= padding;
    outBuf[outBufPos++] = data;
    return outBufPos;
}


int BerEncoder_determineEncodedStringSize( const char* String )
{

     int iSize;
     int iLength;

     if( String != NULL )
     {
        iSize = 1; //размер тэга

        iLength = strlen( String );

        iSize += BerEncoder_determineLengthSize( iLength );

        iSize += iLength;

        return iSize;

    }//if( String != NULL )
    else
    {
        return 2;

     }

}

int BerEncoder_encodeStringWithTL( unsigned char ucTag, const char* pString,
                                    unsigned char* pBuffer, int iBufPos )
{

    int iLength;
    int i;

    pBuffer[iBufPos++] = ucTag;


    if( pString != NULL )
    {

        iLength = strlen( pString );

        iBufPos = BerEncoder_encodeLength( iLength, pBuffer, iBufPos );


        for( i = 0; i < iLength; i++ )
        {

            pBuffer[iBufPos++] = pString[i];

        }//for( i = 0; i < iLength; i++ )

    }//if( pString != NULL )
    else
    {

        pBuffer[iBufPos++] = 0;

    }//else if( pString != NULL )

    return iBufPos;

}

int BerEncoder_encodeBoolean( unsigned char ucTag, unsigned char ucValue,
                              unsigned char* pBuffer, int iBufPos )
{

    pBuffer[iBufPos++] = ucTag;
    pBuffer[iBufPos++] = 1;

    if( ucValue )
    {
        pBuffer[iBufPos++] = 0x01;
    }
    else
    {
        pBuffer[iBufPos++] = 0x00;
    }
    return iBufPos;
}

void BerEncoder_RevertByteOrder( unsigned char* pOctets, const int iSize )
{
    int i;
    unsigned char ucTemp;

    for( i = 0; i < iSize / 2; i++ )
    {
        ucTemp = pOctets[i];
        pOctets[i] = pOctets[ ( iSize - 1 ) - i ];
        pOctets[ ( iSize - 1 ) - i ] = ucTemp;

    }
}

int IsIncluded(unsigned char* str1, unsigned char* str2)
{
    int min_len;
    int len1, len2;
    int i;

    len1 = strlen((char*)str1);
    len2 = strlen((char*)str2);

    min_len = len1 < len2 ? len1 : len2;

    for ( i = 0; i < min_len; i++) {
        if (*(str1 + i) != *(str2 + i)) {
            return 0;
        }
    }
    return 1;
}

float BerDecoder_decodeFloat(unsigned char* buffer, int bufPos)
{
    float value;
    unsigned char* valueBuf = (unsigned char*)&value;

    int i;

    bufPos += 1; /* skip exponentWidth field */

    for (i = 3; i >= 0; i--) {
        valueBuf[i] = buffer[bufPos++];
    }

    return value;
}
