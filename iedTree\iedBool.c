#include "iedBool.h"

#include "iedFinalDA.h"
#include "iedTree.h"
#include "../AsnEncoding.h"
#include "../DataSlice.h"
#include "debug.h"

#include "IEDCompile/AccessInfo.h"

#define BOOL_ENCODED_SIZE 3

static bool calcReadLen(IEDEntity entity, size_t* pLen )
{
	*pLen = BOOL_ENCODED_SIZE;
	return true;
}

static bool encodeRead(IEDEntity entity, BufferView* outBuf)
{			
	bool value = entity->boolValue;

	if(!BufferView_encodeBoolean(outBuf, IEC61850_BER_BOOLEAN, value))
	{
		ERROR_REPORT("Error encoding bool");
		return false;
	}
	return true;
}

static void updateFromDataSlice(IEDEntity entity)
{
	int offset  = entity->dataSliceOffset;
	bool value = offset != -1 && DataSlice_getBoolFastCurrDS(offset);

	if(entity->boolValue == value)
	{
		entity->changed = TRGOP_NONE;
	}
	else
	{
		entity->changed = entity->trgOps;
		entity->boolValue = value;
		IEDEntity_setTimeStamp(entity, dataSliceGetTimeStamp());
	}
}

void IEDBool_init(IEDEntity entity)
{
	TerminalItem* extInfo = entity->extInfo;
	IntBoolAccessInfo* accessInfo = extInfo->accessInfo;

	//Если будет ошибка, то запишется -1;
	entity->dataSliceOffset = DataSlice_getBoolOffset(accessInfo->valueOffset);

	entity->calcReadLen = calcReadLen;
	entity->encodeRead = encodeRead;
	entity->updateFromDataSlice = updateFromDataSlice;

	IEDTree_addToCmpList(entity);
}
