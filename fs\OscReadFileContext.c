#include <stdbool.h>
#include <stdint.h>
#include <stdio.h>
#include <string.h>
#include "OscReadFileContext.h"
#include "OscInfo.h"
#include "OscFiles.h"

int zs_user_writeToStream(int fd, void *data, int size)
{
	OscReadFileContext *readFileContext = (OscReadFileContext*)fd;
	if (OscWriteBuffer_write(&readFileContext->streamBuffer, data, size))
	{
		return size;
	}
	else
	{
		return 0;
	}
}
static ZIPentry *initZipEntry(OscReadFileContext *readFileContext)
{
	int64_t writeStatus;
	return zs_entrybegin(readFileContext->zipStream,
		readFileContext->fileName,
		readFileContext->fileInfo.t,
		ZS_STORE, 
		&writeStatus);
}
static bool writeZipEntry(OscReadFileContext *readFileContext, ZIPentry *zipEntry,
	OscWriteBuffer *wb)
{
	unsigned char *data = OscWriteBuffer_data(wb);
	size_t dataLen = OscWriteBuffer_dataLen(wb);
	int64_t writeStatus;
	if (!zs_entrydata(readFileContext->zipStream, zipEntry, data, dataLen, &writeStatus))
	{
		return false;
	}
	OscWriteBuffer_reset(wb);
	return true;
}

static bool flushZipEntry(OscReadFileContext *readFileContext, ZIPentry *zipEntry)
{
	int64_t writeStatus;
	return zs_entryend(readFileContext->zipStream, zipEntry, &writeStatus) != NULL;
}	

OscReadFileContext * OscReadFileContext_create(PWFileInfo *fileInfo)
{	
	
	OscReadFileContext *readFileContext =  OscFiles_malloc(sizeof(OscReadFileContext));
	if (!readFileContext)
	{
		return NULL;
	}
	memset(readFileContext, 0, sizeof(OscReadFileContext));
	memcpy(&readFileContext->fileInfo, fileInfo, sizeof(PWFileInfo));

	readFileContext->freqCfgCount = ~0;

	// zip архив
	readFileContext->zipStream = zs_init((int)readFileContext, NULL);
	if (!readFileContext->zipStream)
	{
		OscReadFileContext_destroy(readFileContext);
		return NULL;
	}


	if (!OscWriteBuffer_create(&readFileContext->cfgBuffer, CFG_BUFFER_SIZE))
	{
		OscReadFileContext_destroy(readFileContext);
		return NULL;
	}
	
	if (!OscWriteBuffer_create(&readFileContext->freqCfgBuffer, FREQ_CFG_BUFFER_SIZE))
	{
		OscReadFileContext_destroy(readFileContext);
		return NULL;
	}

	if (!OscWriteBuffer_create(&readFileContext->datBuffer, DAT_PERIOD_BUFFER_SIZE))
	{
		OscReadFileContext_destroy(readFileContext);
		return NULL;
	}
	

	if (!OscWriteBuffer_create(&readFileContext->hdrBuffer, HDR_BUFFER_SIZE))
	{
		OscReadFileContext_destroy(readFileContext);
		return NULL;
	}
	

	if (!OscWriteBuffer_create(&readFileContext->streamBuffer, STREAM_BUFFER_SIZE))
	{
		OscReadFileContext_destroy(readFileContext);
		return NULL;
	}

	return readFileContext;
}

void OscReadFileContext_destroy(OscReadFileContext * readFileContext)
{
	zs_free(readFileContext->zipStream);

	OscWriteBuffer_destroy(&readFileContext->cfgBuffer);
	OscWriteBuffer_destroy(&readFileContext->freqCfgBuffer);
	OscWriteBuffer_destroy(&readFileContext->datBuffer);
	OscWriteBuffer_destroy(&readFileContext->streamBuffer);
	OscWriteBuffer_destroy(&readFileContext->hdrBuffer);
	OscFiles_free(readFileContext);
}


bool OscReadFileContext_writeCfgToStream(OscReadFileContext * readFileContext)
{

	if (!readFileContext->zipCfg)
	{
		snprintf(readFileContext->fileName, sizeof(readFileContext->fileName),
			"%d.cfg", readFileContext->fileInfo.name);
		readFileContext->zipCfg = initZipEntry(readFileContext);
		if (!readFileContext->zipCfg)
		{
			return false;
		}
	}

	return writeZipEntry(readFileContext, readFileContext->zipCfg, &readFileContext->cfgBuffer);
	
}

bool OscReadFileContext_closeCfg(OscReadFileContext * readFileContext)
{
	return flushZipEntry(readFileContext, readFileContext->zipCfg);
}

bool OscReadFileContext_writeDatToStream(OscReadFileContext * readFileContext)
{
	if (!readFileContext->zipDat)
	{
		snprintf(readFileContext->fileName, sizeof(readFileContext->fileName),
			"%d.dat", readFileContext->fileInfo.name);
		readFileContext->zipDat = initZipEntry(readFileContext);
		if (!readFileContext->zipDat)
		{
			return false;
		}
	}
	return writeZipEntry(readFileContext, readFileContext->zipDat, &readFileContext->datBuffer);
}
bool OscReadFileContext_closeDat(OscReadFileContext * readFileContext)
{
	return flushZipEntry(readFileContext, readFileContext->zipDat);
}

bool  OscReadFileContext_writeHdrToStream(OscReadFileContext * readFileContext)
{
	if (!readFileContext->zipHdr)
	{
		snprintf(readFileContext->fileName, sizeof(readFileContext->fileName),
			"%d.hdr", readFileContext->fileInfo.name);
		readFileContext->zipHdr = initZipEntry(readFileContext);
		if (!readFileContext->zipHdr)
		{
			return false;
		}
	}

	return writeZipEntry(readFileContext, readFileContext->zipHdr, &readFileContext->hdrBuffer);
}

bool OscReadFileContext_closeHdr(OscReadFileContext * readFileContext)
{
	return flushZipEntry(readFileContext, readFileContext->zipHdr);
}

bool OscReadFileContext_flushAndClose(OscReadFileContext * readFileContext)
{
	int64_t writestatus;
	return zs_finish(readFileContext->zipStream, &writestatus) == 0;
}

int OscReadFileContext_getPhistotyTime(OscReadFileContext * readFileContext)
{
	return (int)((readFileContext->phistoryTick + 0.5) / 1000000);
}

int OscReadFileContext_getPhistotyTimeMS(OscReadFileContext * readFileContext)
{
	return (int)((readFileContext->phistoryTick + 0.5) / 1000);
}

bool OscReadFileContext_writeFreq(OscReadFileContext * readFileContext, float freq,
	unsigned int sampleNum)
{
	
	// ни одной частоты не записано
	if (readFileContext->freqCfgCount == ~0)
	{
		readFileContext->lastFreq.sampleNum = sampleNum;
		readFileContext->lastFreq.freq = freq;
		readFileContext->freqCfgCount = 0;
		return TRUE;
	}
	
	// записывается только если частота изменилась 
	if (readFileContext->lastFreq.freq != freq) 
	{
		OscFreqCfg freqCfg;
		freqCfg = readFileContext->lastFreq;
		readFileContext->lastFreq.freq = freq;
		readFileContext->lastFreq.sampleNum = sampleNum;
		readFileContext->freqCfgCount++;

		return OscWriteBuffer_write(&readFileContext->freqCfgBuffer,
			&freqCfg, sizeof(OscFreqCfg));
	}
	else
	{
		readFileContext->lastFreq.sampleNum = sampleNum;
		return TRUE;
	}
}

int OscReadFileContext_getFreqCount(OscReadFileContext * readFileContext)
{
	return readFileContext->freqCfgCount + 1;
}

OscFreqCfg * OscReadFileContext_getFreqCfg(OscReadFileContext * readFileContext, int num)
{
	OscFreqCfg *result;
	
	// если не было изменений частоты или последняя частота
	if (readFileContext->freqCfgCount == 0 || 
		num == readFileContext->freqCfgCount)
	{
		return &readFileContext->lastFreq;
	}


	if (num >= readFileContext->freqCfgCount + 1)
	{
		return NULL;
	}

	result = OscWriteBuffer_data(&readFileContext->freqCfgBuffer);
	return &result[num];
}

