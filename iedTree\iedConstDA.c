#include "iedConstDA.h"


#include "iedControlModel.h"
#include "../iedmodel.h"


bool IEDConstDA_init(IEDEntity entity, BufferView* ber)
{
    // Некоторые константы на самом деле не константы,
    // а части модели управления
    if( StringView_cmpCStr(&entity->name, "orIdent") == 0 )
    {
        return IEDOrIdent_init(entity);
    }
    else if(StringView_cmpCStr(&entity->name, "orCat") == 0)
    {
        return IEDOrCat_init(entity);
    }
    else if(StringView_cmpCStr(&entity->name, "ctlNum") == 0)
    {
        return IEDCtlNum_init(entity, ber);
    }

    entity->type = IED_ENTITY_DA_CONST;    
    entity->readOnly = true;

    //Сохраняем позицию константы в информационной модели
    entity->extInfo = (void*)(ber->p + ber->pos - iedModel);
    return true;
}
