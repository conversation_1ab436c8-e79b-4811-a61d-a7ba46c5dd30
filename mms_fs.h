#pragma once

#include "mmsconnection.h"
#include "bufView.h"

//Файловые операции MMS

bool mms_handleFileDirRequest(MmsConnection* mmsConn, BufferView* inBuf,
	unsigned int invokeId, BufferView* outBuf);
bool mms_handleFileOpenRequest(MmsConnection* mmsConn, BufferView* inBuf,
	unsigned int invokeId, BufferView* outBuf);
bool mms_handleFileReadRequest(MmsConnection* mmsConn, BufferView* inBuf,
	unsigned int invokeID, BufferView* outBuf);
bool mms_handleFileCloseRequest(MmsConnection* mmsConn, BufferView* inBuf,
	unsigned int invokeID, BufferView* outBuf);

