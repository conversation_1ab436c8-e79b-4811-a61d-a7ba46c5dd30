                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1p01.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=out_buffers.c -o gh_1p01.o -list=out_buffers.lst C:\Users\<USER>\AppData\Local\Temp\gh_1p01.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_1p01.s
Source File: out_buffers.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile out_buffers.c

                      10 ;		-o out_buffers.o

                      11 ;Source File:   out_buffers.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:56:05 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "out_buffers.h"


                      21 ;2: 


                      22 ;3: #include <platform_critical_section.h>


                      23 ;4: #include <debug.h>


                      24 ;5: #include <stddef.h>


                      25 ;6: 


                      26 ;7: void initSessionOutBuffers(SessionBuffers* buffers)


                      27 	.text

                      28 	.align	4

                      29 initSessionOutBuffers::

00000000 e92d4030     30 	stmfd	[sp]!,{r4-r5,lr}

00000004 e3a04000     31 	mov	r4,0

00000008 e1a05000     32 	mov	r5,r0

0000000c eb000000*    33 	bl	CriticalSection_Init

00000010 e5c54004     34 	strb	r4,[r5,4]

00000014 e2850c60     35 	add	r0,r5,3<<13

00000018 e5c0400c     36 	strb	r4,[r0,12]

0000001c e2850cc0     37 	add	r0,r5,3<<14

00000020 e5c04014     38 	strb	r4,[r0,20]

00000024 e2850b48     39 	add	r0,r5,9<<13

00000028 e5c0401c     40 	strb	r4,[r0,28]

0000002c e2850b60     41 	add	r0,r5,3<<15

00000030 e5c04024     42 	strb	r4,[r0,36]

00000034 e2850b78     43 	add	r0,r5,15<<13

00000038 e5c0402c     44 	strb	r4,[r0,44]

0000003c e2850b90     45 	add	r0,r5,9<<14

00000040 e5c04034     46 	strb	r4,[r0,52]

00000044 e2850ba8     47 	add	r0,r5,42<<12

00000048 e5c0403c     48 	strb	r4,[r0,60]

0000004c e2850bc0     49 	add	r0,r5,3<<16

00000050 e5c04044     50 	strb	r4,[r0,68]


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1p01.s
                      51 ;8: {


                      52 

                      53 ;9:     int i;


                      54 ;10:     CriticalSection_Init(&buffers->cs);


                      55 

                      56 ;11:     for(i = 0; i < COTP_OUT_BUFFERS_COUNT; ++i)


                      57 

00000054 e2850bd8     58 	add	r0,r5,54<<12

00000058 e5c0404c     59 	strb	r4,[r0,76]

0000005c e8bd8030     60 	ldmfd	[sp]!,{r4-r5,pc}

                      61 	.endf	initSessionOutBuffers

                      62 	.align	4

                      63 

                      64 ;buffers	r5	param

                      65 

                      66 	.section ".bss","awb"

                      67 .L88:

                      68 	.data

                      69 	.text

                      70 

                      71 ;14:     }


                      72 ;15:     //CriticalSection_Unlock(&cs);


                      73 ;16: }


                      74 

                      75 ;17: 


                      76 ;18: void SessionBuffers_done(SessionBuffers* buffers)


                      77 	.align	4

                      78 	.align	4

                      79 SessionBuffers_done::

                      80 ;19: {


                      81 

                      82 ;20: 	CriticalSection_Done(&buffers->cs);


                      83 

00000060 ea000000*    84 	b	CriticalSection_Done

                      85 	.endf	SessionBuffers_done

                      86 	.align	4

                      87 

                      88 ;buffers	none	param

                      89 

                      90 	.section ".bss","awb"

                      91 .L126:

                      92 	.data

                      93 	.text

                      94 

                      95 ;21: }


                      96 

                      97 ;22: 


                      98 ;23: SessionOutBuffer* allocSessionOutBuffer(SessionBuffers* buffers, int size)


                      99 	.align	4

                     100 	.align	4

                     101 allocSessionOutBuffer::

00000064 e92d4070    102 	stmfd	[sp]!,{r4-r6,lr}

                     103 ;24: {


                     104 

                     105 ;25:     int i;


                     106 ;26:     SessionOutBuffer* result = NULL;


                     107 

                     108 ;27: 	if (size > SESSION_OUT_BUF_SIZE)


                     109 

00000068 e3510c60    110 	cmp	r1,3<<13

                     111 ;28: 	{



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1p01.s
                     112 

                     113 ;29: 		ERROR_REPORT("Requested buffer is too large");


                     114 ;30: 		return NULL;


                     115 

0000006c c3a00000    116 	movgt	r0,0

00000070 ca00006d    117 	bgt	.L133

00000074 e3a06001    118 	mov	r6,1

00000078 e3a04000    119 	mov	r4,0

0000007c e1a05000    120 	mov	r5,r0

00000080 eb000000*   121 	bl	CriticalSection_Lock

                     122 ;31: 	}


                     123 ;32: 


                     124 ;33:     CriticalSection_Lock(&buffers->cs);


                     125 

                     126 ;34:     for(i = 0; i < COTP_OUT_BUFFERS_COUNT; ++i)


                     127 

00000084 e5d50004    128 	ldrb	r0,[r5,4]

00000088 e2851004    129 	add	r1,r5,4

0000008c e3500000    130 	cmp	r0,0

00000090 1a000005    131 	bne	.L195

00000094 e1a04001    132 	mov	r4,r1

00000098 e5c46000    133 	strb	r6,[r4]

                     134 ;41:         }


                     135 ;42:     }


                     136 ;43:     CriticalSection_Unlock(&buffers->cs);


                     137 

0000009c e1a00005    138 	mov	r0,r5

000000a0 eb000000*   139 	bl	CriticalSection_Unlock

                     140 ;44:     return result;


                     141 

000000a4 e1a00004    142 	mov	r0,r4

000000a8 ea00005f    143 	b	.L133

                     144 .L195:

000000ac e3a00c60    145 	mov	r0,3<<13

000000b0 e2800008    146 	add	r0,r0,8

000000b4 e7f02001    147 	ldrb	r2,[r0,r1]!

000000b8 e3520000    148 	cmp	r2,0

000000bc 1a000005    149 	bne	.L198

000000c0 e1a04000    150 	mov	r4,r0

000000c4 e5c46000    151 	strb	r6,[r4]

                     152 ;41:         }


                     153 ;42:     }


                     154 ;43:     CriticalSection_Unlock(&buffers->cs);


                     155 

000000c8 e1a00005    156 	mov	r0,r5

000000cc eb000000*   157 	bl	CriticalSection_Unlock

                     158 ;44:     return result;


                     159 

000000d0 e1a00004    160 	mov	r0,r4

000000d4 ea000054    161 	b	.L133

                     162 .L198:

000000d8 e3a00cc0    163 	mov	r0,3<<14

000000dc e2800010    164 	add	r0,r0,16

000000e0 e7f02001    165 	ldrb	r2,[r0,r1]!

000000e4 e3520000    166 	cmp	r2,0

000000e8 1a000005    167 	bne	.L201

000000ec e1a04000    168 	mov	r4,r0

000000f0 e5c46000    169 	strb	r6,[r4]

                     170 ;41:         }


                     171 ;42:     }


                     172 ;43:     CriticalSection_Unlock(&buffers->cs);



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1p01.s
                     173 

000000f4 e1a00005    174 	mov	r0,r5

000000f8 eb000000*   175 	bl	CriticalSection_Unlock

                     176 ;44:     return result;


                     177 

000000fc e1a00004    178 	mov	r0,r4

00000100 ea000049    179 	b	.L133

                     180 .L201:

00000104 e3a00b48    181 	mov	r0,9<<13

00000108 e2800018    182 	add	r0,r0,24

0000010c e7f02001    183 	ldrb	r2,[r0,r1]!

00000110 e3520000    184 	cmp	r2,0

00000114 1a000005    185 	bne	.L204

00000118 e1a04000    186 	mov	r4,r0

0000011c e5c46000    187 	strb	r6,[r4]

                     188 ;41:         }


                     189 ;42:     }


                     190 ;43:     CriticalSection_Unlock(&buffers->cs);


                     191 

00000120 e1a00005    192 	mov	r0,r5

00000124 eb000000*   193 	bl	CriticalSection_Unlock

                     194 ;44:     return result;


                     195 

00000128 e1a00004    196 	mov	r0,r4

0000012c ea00003e    197 	b	.L133

                     198 .L204:

00000130 e3a00b60    199 	mov	r0,3<<15

00000134 e2800020    200 	add	r0,r0,32

00000138 e7f02001    201 	ldrb	r2,[r0,r1]!

0000013c e3520000    202 	cmp	r2,0

00000140 1a000005    203 	bne	.L207

00000144 e1a04000    204 	mov	r4,r0

00000148 e5c46000    205 	strb	r6,[r4]

                     206 ;41:         }


                     207 ;42:     }


                     208 ;43:     CriticalSection_Unlock(&buffers->cs);


                     209 

0000014c e1a00005    210 	mov	r0,r5

00000150 eb000000*   211 	bl	CriticalSection_Unlock

                     212 ;44:     return result;


                     213 

00000154 e1a00004    214 	mov	r0,r4

00000158 ea000033    215 	b	.L133

                     216 .L207:

0000015c e3a00b78    217 	mov	r0,15<<13

00000160 e2800028    218 	add	r0,r0,40

00000164 e7f02001    219 	ldrb	r2,[r0,r1]!

00000168 e3520000    220 	cmp	r2,0

0000016c 1a000005    221 	bne	.L210

00000170 e1a04000    222 	mov	r4,r0

00000174 e5c46000    223 	strb	r6,[r4]

                     224 ;41:         }


                     225 ;42:     }


                     226 ;43:     CriticalSection_Unlock(&buffers->cs);


                     227 

00000178 e1a00005    228 	mov	r0,r5

0000017c eb000000*   229 	bl	CriticalSection_Unlock

                     230 ;44:     return result;


                     231 

00000180 e1a00004    232 	mov	r0,r4

00000184 ea000028    233 	b	.L133


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1p01.s
                     234 .L210:

00000188 e3a00b90    235 	mov	r0,9<<14

0000018c e2800030    236 	add	r0,r0,48

00000190 e7f02001    237 	ldrb	r2,[r0,r1]!

00000194 e3520000    238 	cmp	r2,0

00000198 1a000005    239 	bne	.L213

0000019c e1a04000    240 	mov	r4,r0

000001a0 e5c46000    241 	strb	r6,[r4]

                     242 ;41:         }


                     243 ;42:     }


                     244 ;43:     CriticalSection_Unlock(&buffers->cs);


                     245 

000001a4 e1a00005    246 	mov	r0,r5

000001a8 eb000000*   247 	bl	CriticalSection_Unlock

                     248 ;44:     return result;


                     249 

000001ac e1a00004    250 	mov	r0,r4

000001b0 ea00001d    251 	b	.L133

                     252 .L213:

000001b4 e3a00ba8    253 	mov	r0,42<<12

000001b8 e2800038    254 	add	r0,r0,56

000001bc e7f02001    255 	ldrb	r2,[r0,r1]!

000001c0 e3520000    256 	cmp	r2,0

000001c4 1a000005    257 	bne	.L216

000001c8 e1a04000    258 	mov	r4,r0

000001cc e5c46000    259 	strb	r6,[r4]

                     260 ;41:         }


                     261 ;42:     }


                     262 ;43:     CriticalSection_Unlock(&buffers->cs);


                     263 

000001d0 e1a00005    264 	mov	r0,r5

000001d4 eb000000*   265 	bl	CriticalSection_Unlock

                     266 ;44:     return result;


                     267 

000001d8 e1a00004    268 	mov	r0,r4

000001dc ea000012    269 	b	.L133

                     270 .L216:

000001e0 e3a00bc0    271 	mov	r0,3<<16

000001e4 e2800040    272 	add	r0,r0,64

000001e8 e7f02001    273 	ldrb	r2,[r0,r1]!

000001ec e3520000    274 	cmp	r2,0

000001f0 1a000005    275 	bne	.L219

000001f4 e1a04000    276 	mov	r4,r0

000001f8 e5c46000    277 	strb	r6,[r4]

                     278 ;41:         }


                     279 ;42:     }


                     280 ;43:     CriticalSection_Unlock(&buffers->cs);


                     281 

000001fc e1a00005    282 	mov	r0,r5

00000200 eb000000*   283 	bl	CriticalSection_Unlock

                     284 ;44:     return result;


                     285 

00000204 e1a00004    286 	mov	r0,r4

00000208 ea000007    287 	b	.L133

                     288 .L219:

0000020c e2810bd8    289 	add	r0,r1,54<<12

00000210 e5f01048    290 	ldrb	r1,[r0,72]!

00000214 e3510000    291 	cmp	r1,0

00000218 01a04000    292 	moveq	r4,r0

0000021c 05c46000    293 	streqb	r6,[r4]

                     294 ;41:         }



                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1p01.s
                     295 ;42:     }


                     296 ;43:     CriticalSection_Unlock(&buffers->cs);


                     297 

00000220 e1a00005    298 	mov	r0,r5

00000224 eb000000*   299 	bl	CriticalSection_Unlock

                     300 ;44:     return result;


                     301 

00000228 e1a00004    302 	mov	r0,r4

                     303 .L133:

0000022c e8bd8070    304 	ldmfd	[sp]!,{r4-r6,pc}

                     305 	.endf	allocSessionOutBuffer

                     306 	.align	4

                     307 ;result	r4	local

                     308 

                     309 ;buffers	r5	param

                     310 ;size	r1	param

                     311 

                     312 	.section ".bss","awb"

                     313 .L388:

                     314 	.data

                     315 	.text

                     316 

                     317 ;45: }


                     318 

                     319 ;46: 


                     320 ;47: void freeSessionOutBuffer(SessionOutBuffer* buf)


                     321 	.align	4

                     322 	.align	4

                     323 freeSessionOutBuffer::

                     324 ;48: {


                     325 

                     326 ;49: 	buf->busy = FALSE;


                     327 

00000230 e3a01000    328 	mov	r1,0

00000234 e5c01000    329 	strb	r1,[r0]

00000238 e12fff1e*   330 	ret	

                     331 	.endf	freeSessionOutBuffer

                     332 	.align	4

                     333 

                     334 ;buf	r0	param

                     335 

                     336 	.section ".bss","awb"

                     337 .L478:

                     338 	.data

                     339 	.text

                     340 

                     341 ;50: }


                     342 	.align	4

                     343 

                     344 	.data

                     345 	.ghsnote version,6

                     346 	.ghsnote tools,3

                     347 	.ghsnote options,0

                     348 	.text

                     349 	.align	4

