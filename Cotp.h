//Connection Oriented Transport Protocol
//ISO Transport Service on top of the TCP Version: 3
#ifndef __COTP_H__
#define __COTP_H__

#include "platform_socket.h"

#define MAX_TPKT_TPDU_SIZE 1024

//Типы пакетов COTP
#define COTP_INITIATE_REQUEST 0xe0
#define COTP_INITIATE_RESPONSE 0xd0
#define COTP_DATA_TRANSFER 0xf0

typedef struct {
    unsigned char version;	//версия протокола TPKT - всегда 3
    unsigned char reserved;
    unsigned char packetLenH;
    unsigned char packetLenL;
} TPKTHeader;

//Первая часть заголовка пакета (одинаковая для всех пакетов)
//Включает в себя заголовок TPKT и первые два байта заголовка COTP
typedef struct {
    TPKTHeader tpktHeader;
    //Включены первые два байта заголовка COTP
    unsigned char li;
    unsigned char dt;
} PacketHeader;

//Вторая часть заголовка пакета для запроса на соединение.
//Первая часть находится в TPKTHeader
typedef struct {
    unsigned char dstRefL;
    unsigned char dstRefH;
    unsigned char srcRefL;
    unsigned char srcRefH;
    unsigned char protocolClass;
    unsigned char variablePart[249];
} ConnectRequest;



typedef struct {
    SERVER_SOCKET socket;
    unsigned short remoteRef;
    unsigned short localRef;
    unsigned char protocolClass;
    int inBufPos;
    PacketHeader resvTPKTHeader;
    ConnectRequest recvConnectRequest;
    //unsigned char resvCOTPbuf[MAX_TPKT_TPDU_SIZE - sizeof(TPKTHeader)];
    unsigned char outBuf[MAX_TPKT_TPDU_SIZE];
} COTPConnection;

void initCOTPConnection(COTPConnection* cotpConn, SERVER_SOCKET socket);

int cotpSendData(COTPConnection* cotpConn, void* data, int byteCount);
int cotpReceiveData(COTPConnection* cotpConn, void* recvBuf, int maxByteCount );

int cotp(COTPConnection* cotpConn, void* sendData, int sendByteCount, void* recvBuf,
         int maxByteCount);


//======================================================================

#define TPKT_RFC1006_HEADER_SIZE	4		//длина заголовка пакета TPKT (Transport packet)
#define	COTP_DATA_HEADER_SIZE		3		//длина заголовка пакета COTP Данные

//коды параметров Переменной части пакетов COTP
#define	COTP_TPDU_LENGTH	0xc0	//длина пакета
#define	COTP_TSELECTOR_SRC	0xc1	//идентификатор вызывающего ПДУТУ
#define	COTP_TSELECTOR_DST	0xc2	//идентификатор вызываемого ПДУТУ

//TPKT_Header						//заголовок пакета TPKT
typedef struct														
{																	

	unsigned char	Vrsn;			//vrsn - версия протокола - всегда 3
	unsigned char	Reserved;		//reserved - зарезервировано 
	unsigned short	PacketLength;	//packet length - длина пакета в октетах, включая заголовок
	
}TPKT_Header;						

//TSelector		ИД ПДУТУ (T-selector) - идентификатор пункта доступа к услугам транспортного уровня стр.99
typedef struct
{

    unsigned char	Size;		//размер -  0 .. 4 - 0 значит T-selector не присутствует
    unsigned char	Value[4];	//значение T-selector


}TSelector;

//COTP_Options		переменная часть пакета COTP Class 0 Запрос Соединения ПБДТ ЗС стр.99
typedef struct														
{

	unsigned char	TPDU_Length;	//длина ПБДТ - код параметра - 1100 0000 (0xC0) стр.100
	TSelector		TSelSrc;		//идентификатор вызывающего ПДУТУ - код параметра - 11000001 (0xC1)
	TSelector		TSelDst;		//идентификатор вызываемого ПДУТУ - код параметра - 11000010 (0xC2)	
}COTP_Options;

//COTP_InitiateRequest_Packet		пакет COTP Class 0 Запрос Соединения ПБДТ ЗС стр.99
typedef struct														
{																	
																	
	unsigned char	HeaderLength;	//УД-указатель длины - длина заголовка в октетах исключая УД
	unsigned char	CodeCredit;		//ЗС КРД - биты 8-5 - код ЗС - 1110 (0xE), биты 4-1 Кредит - 0000 (0x0) 
	unsigned short	DstRef;			//УКАЗ-ПОЛ - указатель получателя - устанавливается в 0
	unsigned short	SrcRef;			//УКАЗ-ОТП - указатель отправителя, выбранный логическим объектом
									//транспортного уровня, выдавшим ПБДТ ЗС для идентификации
									//запрашиваемого СТУ - соединение транспортного уровня
	unsigned char	ClassFF;		//КЛАСС и ФФ - биты 8-5 - класс - 0000 - класс 0
									//биты 4-1 - ФАКУЛЬТАТИВНЫЕ ФУНКЦИИ - 0000 - для класса 0
	COTP_Options	CotpOptions;	//переменная часть пакета			

	
}COTP_InitiateRequest_Packet;

//COTP_DataTransfer_Packet			пакет COTP Class 0 ПБДТ "данные" стр.109
typedef struct
{
    unsigned char headerLength;
    unsigned char IsLastDataUnit;	//>0 пакет последний, получается из поля НР-ПБДТ и КС
									//НР-ПБДТ (биты 1..7) и КС (бит 8) - номер ПБДТ ДН и метка "конец СБДТ"
									//НР-ПБДТ - порядковый номер, равен нулю в классе 0
									//КС - в значении 1 - текущий ПБДТ ДН - последний в полной последовательности
    int userDataPos;	//позиция данных пользователя в общем пакете COPT + TPKT
}COTP_DataTransfer_Packet;

typedef enum 
{

    TPKT_PACKET_COMPLETE =	0,
    TPKT_WAITING		 =	1,
    TPKT_ERROR			 =	2

}TPKT_State;

typedef enum 
{
    COTP_OK,
    COTP_ERROR,
    COTP_CONNECT_INDICATION,
    COTP_DATA_INDICATION,
    COTP_DISCONNECT_INDICATION,
    COTP_MORE_FRAGMENTS_FOLLOW

}COTP_Indication;

//COTP_Connection				//информация о COTP соединении
/*
typedef struct {
    SERVER_SOCKET							AcceptSocket;				//сокет, созданный при установке TCP-соединения
	TPKT_Header*					pTpktHeader;				//заголовок TPKT
	COTP_InitiateRequest_Packet*	pCOTPInitiateRequestPacket; //пакет Запрос Соединения
    COTP_DataTransfer_Packet*		pCOTPDataTransferPacket;	//пакет Данные    
}COTP_Connection;
*/

#endif // __COTP_H__
