                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_as41.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=iedFinalDA.c -o iedTree\gh_as41.o -list=iedTree/iedFinalDA.lst C:\Users\<USER>\AppData\Local\Temp\gh_as41.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_as41.s
Source File: iedFinalDA.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      10 ;		iedTree/iedFinalDA.c -o iedTree/iedFinalDA.o

                      11 ;Source File:   iedTree/iedFinalDA.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Oct 07 09:39:20 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "iedFinalDA.h"


                      21 ;2: 


                      22 ;3: #include "iedControlModel.h"


                      23 ;4: 


                      24 ;5: 


                      25 ;6: #include "iedQuality.h"


                      26 ;7: #include "iedFloat.h"


                      27 ;8: #include "iedInt.h"


                      28 ;9: #include "iedUInt.h"


                      29 ;10: #include "iedEnum.h"


                      30 ;11: #include "iedConstDA.h"


                      31 ;12: #include "../mms_data.h"


                      32 ;13: #include "../mms_rcb.h"


                      33 ;14: #include "../mms_gocb.h"


                      34 ;15: #include "../mms_write.h"


                      35 ;16: #include "../DataSlice.h"


                      36 ;17: 


                      37 ;18: #include "IEDCompile/AccessInfo.h"


                      38 ;19: #include "IEDCompile/InnerAttributeTypes.h"


                      39 ;20: 


                      40 ;21: #include "../iedmodel.h"


                      41 ;22: 


                      42 ;23: #include "../BaseAsnTypes.h"


                      43 ;24: 


                      44 ;25: //!!! Для отладки


                      45 ;26: int settCounter = 0;


                      46 ;27: void incSettCounter()


                      47 ;28: {


                      48 ;29:     settCounter++;


                      49 ;30: }


                      50 ;31: 



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_as41.s
                      51 ;32: 


                      52 ;33: //==================Инициализация TerminalItemDA==============


                      53 ;34: 


                      54 ;35: 


                      55 ;36: // Функция инициализирует специфические поля для Terminal Item DA.


                      56 ;37: // ber указывает на AccessInfo до выравнивания.


                      57 ;38: // По завершению функции состояние ber не сохраняется.


                      58 ;39: static bool initTermItemDA(IEDEntity entity, BufferView* ber)


                      59 

                      60 ;106: }


                      61 

                      62 ;107: 


                      63 ;108: static bool initTimeStampDA(IEDEntity entity)


                      64 

                      65 ;120: }


                      66 

                      67 	.text

                      68 	.align	4

                      69 incSettCounter::

00000000 e59f1650*    70 	ldr	r1,.L117

00000004 e5910000     71 	ldr	r0,[r1]

00000008 e2800001     72 	add	r0,r0,1

0000000c e5810000     73 	str	r0,[r1]

00000010 e12fff1e*    74 	ret	

                      75 	.endf	incSettCounter

                      76 	.align	4

                      77 

                      78 	.section ".bss","awb"

                      79 .L110:

                      80 	.data

                      81 	.text

                      82 

                      83 

                      84 ;121: 


                      85 ;122: bool IEDFinalDA_init(IEDEntity entity)


                      86 	.align	4

                      87 	.align	4

                      88 IEDFinalDA_init::

00000014 e92d4030     89 	stmfd	[sp]!,{r4-r5,lr}

                      90 ;123: {


                      91 

                      92 ;124:     enum InnerAttributeType attrType;


                      93 ;125:     BufferView ber = entity->ber;


                      94 

00000018 e24dd010     95 	sub	sp,sp,16

0000001c e28d3004     96 	add	r3,sp,4

00000020 e1a05000     97 	mov	r5,r0

00000024 e2850014     98 	add	r0,r5,20

00000028 e8900007     99 	ldmfd	[r0],{r0-r2}

0000002c e8830007    100 	stmea	[r3],{r0-r2}

                     101 ;126: 


                     102 ;127: 


                     103 ;128:     //Пропустить тэг и длину


                     104 ;129:     BufferView_decodeTL(&ber, NULL, NULL, NULL);


                     105 

00000030 e1a00003    106 	mov	r0,r3

00000034 e3a03000    107 	mov	r3,0

00000038 e1a02003    108 	mov	r2,r3

0000003c e1a01003    109 	mov	r1,r3

00000040 eb000000*   110 	bl	BufferView_decodeTL

                     111 ;130: 



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_as41.s
                     112 ;131:     if(!IEDModel_skipServiceInfo(&ber))


                     113 

00000044 e28d0004    114 	add	r0,sp,4

00000048 eb000000*   115 	bl	IEDModel_skipServiceInfo

0000004c e3500000    116 	cmp	r0,0

00000050 0a000004    117 	beq	.L124

                     118 ;132:     {


                     119 

                     120 ;133:         return false;


                     121 

                     122 ;134:     }


                     123 ;135:     if(!BufferView_decodeUInt32TL(&ber, ASN_INTEGER, (uint32_t*)&attrType))


                     124 

00000054 e1a0200d    125 	mov	r2,sp

00000058 e28d0004    126 	add	r0,sp,4

0000005c e3a01002    127 	mov	r1,2

00000060 eb000000*   128 	bl	BufferView_decodeUInt32TL

00000064 e3500000    129 	cmp	r0,0

                     130 .L124:

                     131 ;136:     {


                     132 

                     133 ;137:         ERROR_REPORT("Error decoding attribute type");


                     134 ;138:         return false;


                     135 

00000068 03a00000    136 	moveq	r0,0

0000006c 0a000059    137 	beq	.L118

                     138 .L123:

                     139 ;139:     }


                     140 ;140: 


                     141 ;141:     if(attrType == INNER_TYPE_TIME_STAMP)


                     142 

00000070 e59d0000    143 	ldr	r0,[sp]

00000074 e3500003    144 	cmp	r0,3

00000078 1a00000b    145 	bne	.L126

                     146 ;142:     {


                     147 

                     148 ;143:         // ----- t


                     149 ;144:         return initTimeStampDA(entity);


                     150 

                     151 ;109: {


                     152 

                     153 ;110:     TimeStamp* extInfo;


                     154 ;111:     entity->type = IED_ENTITY_DA_TIMESTAMP;


                     155 

0000007c e3a00009    156 	mov	r0,9

00000080 e5850050    157 	str	r0,[r5,80]

                     158 ;112:     extInfo = IEDEntity_alloc(sizeof(TimeStamp));


                     159 

00000084 e3a00008    160 	mov	r0,8

00000088 eb000000*   161 	bl	IEDEntity_alloc

0000008c e1b04000    162 	movs	r4,r0

                     163 ;113:     if(extInfo == NULL)


                     164 

                     165 ;114:     {


                     166 

                     167 ;115:         return false;


                     168 

00000090 020400ff    169 	andeq	r0,r4,255

00000094 0a00004f    170 	beq	.L118

                     171 ;116:     }


                     172 ;117:     entity->extInfo = extInfo;



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_as41.s
                     173 

00000098 e5854058    174 	str	r4,[r5,88]

                     175 ;118:     extInfo->timeStamp = dataSliceGetTimeStamp();


                     176 

0000009c eb000000*   177 	bl	dataSliceGetTimeStamp

000000a0 e8840003    178 	stmea	[r4],{r0-r1}

                     179 ;119:     return true;


                     180 

000000a4 e3a00001    181 	mov	r0,1

000000a8 ea00004a    182 	b	.L118

                     183 .L126:

                     184 ;145:     }


                     185 ;146:     else if(attrType == INNER_TYPE_CONST)


                     186 

000000ac e350001f    187 	cmp	r0,31

000000b0 1a000003    188 	bne	.L136

                     189 ;147:     {


                     190 

                     191 ;148:         // ----- const


                     192 ;149:         return IEDConstDA_init(entity, &ber);        


                     193 

000000b4 e28d1004    194 	add	r1,sp,4

000000b8 e1a00005    195 	mov	r0,r5

000000bc eb000000*   196 	bl	IEDConstDA_init

000000c0 ea000044    197 	b	.L118

                     198 .L136:

                     199 ;150:     }


                     200 ;151:     else


                     201 ;152:     {


                     202 

                     203 ;153:         // ----- Всё остальное


                     204 ;154: 


                     205 ;155:         entity->type = IED_ENTITY_DA_TERMINAL_ITEM;


                     206 

000000c4 e3a01008    207 	mov	r1,8

000000c8 e5851050    208 	str	r1,[r5,80]

                     209 ;156:         entity->subType = attrType;


                     210 

000000cc e5850054    211 	str	r0,[r5,84]

                     212 ;157:         return initTermItemDA(entity, &ber);


                     213 

                     214 ;40: {


                     215 

                     216 ;41:     TerminalItem* extInfo = IEDEntity_alloc(sizeof(TerminalItem));


                     217 

000000d0 e3a00040    218 	mov	r0,64

000000d4 eb000000*   219 	bl	IEDEntity_alloc

000000d8 e1b01000    220 	movs	r1,r0

                     221 ;42:     if(extInfo == NULL)


                     222 

000000dc 0a000003    223 	beq	.L145

                     224 ;43:     {


                     225 

                     226 ;44:         return false;


                     227 

                     228 ;45:     }


                     229 ;46:     entity->extInfo = extInfo;


                     230 

000000e0 e5851058    231 	str	r1,[r5,88]

                     232 ;47: 


                     233 ;48:     if(!IEDModel_getTermItemDescrStruct(ber, &extInfo->accessInfo))



                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_as41.s
                     234 

000000e4 e28d0004    235 	add	r0,sp,4

000000e8 eb000000*   236 	bl	IEDModel_getTermItemDescrStruct

000000ec e3500000    237 	cmp	r0,0

                     238 .L145:

                     239 ;49:     {


                     240 

                     241 ;50:         return false;


                     242 

000000f0 03a00000    243 	moveq	r0,0

000000f4 0a000037    244 	beq	.L118

                     245 .L146:

                     246 ;51:     }


                     247 ;52: 


                     248 ;53:     switch(entity->subType)


                     249 

000000f8 e5950054    250 	ldr	r0,[r5,84]

000000fc e2500007    251 	subs	r0,r0,7

00000100 2a000008    252 	bhs	.L420

00000104 e2900001    253 	adds	r0,r0,1

00000108 0a000029    254 	beq	.L153

0000010c e2900002    255 	adds	r0,r0,2

00000110 0a00001b    256 	beq	.L150

00000114 e2900002    257 	adds	r0,r0,2

00000118 0a000011    258 	beq	.L148

0000011c e2900002    259 	adds	r0,r0,2

00000120 1a00002b    260 	bne	.L156

00000124 ea00000a    261 	b	.L147

                     262 .L420:

                     263 

00000128 e2500001    264 	subs	r0,r0,1

0000012c 3a00001c    265 	blo	.L152

00000130 0a000017    266 	beq	.L151

00000134 e2500001    267 	subs	r0,r0,1

00000138 0a000021    268 	beq	.L154

0000013c e250001e    269 	subs	r0,r0,30

00000140 0a00000b    270 	beq	.L149

00000144 e3500002    271 	cmp	r0,2

                     272 ;81:             break;


                     273 ;82:         case INNER_TYPE_REAL_AS_INT64:


                     274 ;83:             IEDRealAsInt64_init(entity);


                     275 

00000148 01a00005    276 	moveq	r0,r5

0000014c 0b000000*   277 	bleq	IEDRealAsInt64_init

00000150 ea00001f    278 	b	.L156

                     279 .L147:

                     280 ;54:     {


                     281 ;55:         case INNER_TYPE_QUALITY:


                     282 ;56:             IEDQuality_init(entity);


                     283 

00000154 e1a00005    284 	mov	r0,r5

00000158 eb000000*   285 	bl	IEDQuality_init

                     286 ;84:             break;


                     287 ;85: 


                     288 ;86:         //Если неизвестный тип, то останется функция-заглушка


                     289 ;87: 


                     290 ;88:         /*!!!


                     291 ;89:         Это код для подсчёта уставок. Нужен для разработки DataSlice для уставок.


                     292 ;90:         Для нормальной работы не нужен.


                     293 ;91: 


                     294 ;92:         case INNER_TYPE_REAL_SETT:



                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_as41.s
                     295 ;93:         case INNER_TYPE_INT32_SETTS:


                     296 ;94:         case INNER_TYPE_ENUMERATED_SETTS:


                     297 ;95:         case INNER_TYPE_INT32U_SETTS:


                     298 ;96:         case INNER_TYPE_FLOAT_SETT:


                     299 ;97:             incSettCounter();


                     300 ;98: 


                     301 ;99: 


                     302 ;100:             break;


                     303 ;101:         */


                     304 ;102: 


                     305 ;103:     }


                     306 ;104: 


                     307 ;105:     return true;


                     308 

0000015c e3a00001    309 	mov	r0,1

00000160 ea00001c    310 	b	.L118

                     311 .L148:

                     312 ;57:             break;


                     313 ;58:         case INNER_TYPE_FLOAT_VALUE:


                     314 ;59:             IEDFloat_init(entity);


                     315 

00000164 e1a00005    316 	mov	r0,r5

00000168 eb000000*   317 	bl	IEDFloat_init

                     318 ;84:             break;


                     319 ;85: 


                     320 ;86:         //Если неизвестный тип, то останется функция-заглушка


                     321 ;87: 


                     322 ;88:         /*!!!


                     323 ;89:         Это код для подсчёта уставок. Нужен для разработки DataSlice для уставок.


                     324 ;90:         Для нормальной работы не нужен.


                     325 ;91: 


                     326 ;92:         case INNER_TYPE_REAL_SETT:


                     327 ;93:         case INNER_TYPE_INT32_SETTS:


                     328 ;94:         case INNER_TYPE_ENUMERATED_SETTS:


                     329 ;95:         case INNER_TYPE_INT32U_SETTS:


                     330 ;96:         case INNER_TYPE_FLOAT_SETT:


                     331 ;97:             incSettCounter();


                     332 ;98: 


                     333 ;99: 


                     334 ;100:             break;


                     335 ;101:         */


                     336 ;102: 


                     337 ;103:     }


                     338 ;104: 


                     339 ;105:     return true;


                     340 

0000016c e3a00001    341 	mov	r0,1

00000170 ea000018    342 	b	.L118

                     343 .L149:

                     344 ;60:             break;


                     345 ;61:         case INNER_TYPE_REAL_VALUE:


                     346 ;62:             IEDReal_init(entity);


                     347 

00000174 e1a00005    348 	mov	r0,r5

00000178 eb000000*   349 	bl	IEDReal_init

                     350 ;84:             break;


                     351 ;85: 


                     352 ;86:         //Если неизвестный тип, то останется функция-заглушка


                     353 ;87: 


                     354 ;88:         /*!!!


                     355 ;89:         Это код для подсчёта уставок. Нужен для разработки DataSlice для уставок.



                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_as41.s
                     356 ;90:         Для нормальной работы не нужен.


                     357 ;91: 


                     358 ;92:         case INNER_TYPE_REAL_SETT:


                     359 ;93:         case INNER_TYPE_INT32_SETTS:


                     360 ;94:         case INNER_TYPE_ENUMERATED_SETTS:


                     361 ;95:         case INNER_TYPE_INT32U_SETTS:


                     362 ;96:         case INNER_TYPE_FLOAT_SETT:


                     363 ;97:             incSettCounter();


                     364 ;98: 


                     365 ;99: 


                     366 ;100:             break;


                     367 ;101:         */


                     368 ;102: 


                     369 ;103:     }


                     370 ;104: 


                     371 ;105:     return true;


                     372 

0000017c e3a00001    373 	mov	r0,1

00000180 ea000014    374 	b	.L118

                     375 .L150:

                     376 ;63:             break;


                     377 ;64:         case INNER_TYPE_INT32:


                     378 ;65:             IEDInt32_init(entity);


                     379 

00000184 e1a00005    380 	mov	r0,r5

00000188 eb000000*   381 	bl	IEDInt32_init

                     382 ;84:             break;


                     383 ;85: 


                     384 ;86:         //Если неизвестный тип, то останется функция-заглушка


                     385 ;87: 


                     386 ;88:         /*!!!


                     387 ;89:         Это код для подсчёта уставок. Нужен для разработки DataSlice для уставок.


                     388 ;90:         Для нормальной работы не нужен.


                     389 ;91: 


                     390 ;92:         case INNER_TYPE_REAL_SETT:


                     391 ;93:         case INNER_TYPE_INT32_SETTS:


                     392 ;94:         case INNER_TYPE_ENUMERATED_SETTS:


                     393 ;95:         case INNER_TYPE_INT32U_SETTS:


                     394 ;96:         case INNER_TYPE_FLOAT_SETT:


                     395 ;97:             incSettCounter();


                     396 ;98: 


                     397 ;99: 


                     398 ;100:             break;


                     399 ;101:         */


                     400 ;102: 


                     401 ;103:     }


                     402 ;104: 


                     403 ;105:     return true;


                     404 

0000018c e3a00001    405 	mov	r0,1

00000190 ea000010    406 	b	.L118

                     407 .L151:

                     408 ;66:             break;


                     409 ;67:         case INNER_TYPE_INT32U:


                     410 ;68:             IEDUInt32_init(entity);


                     411 

00000194 e1a00005    412 	mov	r0,r5

00000198 eb000000*   413 	bl	IEDUInt32_init

                     414 ;84:             break;


                     415 ;85: 


                     416 ;86:         //Если неизвестный тип, то останется функция-заглушка



                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_as41.s
                     417 ;87: 


                     418 ;88:         /*!!!


                     419 ;89:         Это код для подсчёта уставок. Нужен для разработки DataSlice для уставок.


                     420 ;90:         Для нормальной работы не нужен.


                     421 ;91: 


                     422 ;92:         case INNER_TYPE_REAL_SETT:


                     423 ;93:         case INNER_TYPE_INT32_SETTS:


                     424 ;94:         case INNER_TYPE_ENUMERATED_SETTS:


                     425 ;95:         case INNER_TYPE_INT32U_SETTS:


                     426 ;96:         case INNER_TYPE_FLOAT_SETT:


                     427 ;97:             incSettCounter();


                     428 ;98: 


                     429 ;99: 


                     430 ;100:             break;


                     431 ;101:         */


                     432 ;102: 


                     433 ;103:     }


                     434 ;104: 


                     435 ;105:     return true;


                     436 

0000019c e3a00001    437 	mov	r0,1

000001a0 ea00000c    438 	b	.L118

                     439 .L152:

                     440 ;69:             break;


                     441 ;70:         case INNER_TYPE_INT8U:


                     442 ;71:             ERROR_REPORT("INNER_TYPE_INT8U values not implemented");


                     443 ;72:             break;


                     444 ;73:         case INNER_TYPE_ENUMERATED:


                     445 ;74:             IEDEnum_init(entity);


                     446 

000001a4 e1a00005    447 	mov	r0,r5

000001a8 eb000000*   448 	bl	IEDEnum_init

                     449 ;84:             break;


                     450 ;85: 


                     451 ;86:         //Если неизвестный тип, то останется функция-заглушка


                     452 ;87: 


                     453 ;88:         /*!!!


                     454 ;89:         Это код для подсчёта уставок. Нужен для разработки DataSlice для уставок.


                     455 ;90:         Для нормальной работы не нужен.


                     456 ;91: 


                     457 ;92:         case INNER_TYPE_REAL_SETT:


                     458 ;93:         case INNER_TYPE_INT32_SETTS:


                     459 ;94:         case INNER_TYPE_ENUMERATED_SETTS:


                     460 ;95:         case INNER_TYPE_INT32U_SETTS:


                     461 ;96:         case INNER_TYPE_FLOAT_SETT:


                     462 ;97:             incSettCounter();


                     463 ;98: 


                     464 ;99: 


                     465 ;100:             break;


                     466 ;101:         */


                     467 ;102: 


                     468 ;103:     }


                     469 ;104: 


                     470 ;105:     return true;


                     471 

000001ac e3a00001    472 	mov	r0,1

000001b0 ea000008    473 	b	.L118

                     474 .L153:

                     475 ;75:             break;


                     476 ;76:         case INNER_TYPE_BOOLEAN:


                     477 ;77:             IEDBool_init(entity);



                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_as41.s
                     478 

000001b4 e1a00005    479 	mov	r0,r5

000001b8 eb000000*   480 	bl	IEDBool_init

                     481 ;84:             break;


                     482 ;85: 


                     483 ;86:         //Если неизвестный тип, то останется функция-заглушка


                     484 ;87: 


                     485 ;88:         /*!!!


                     486 ;89:         Это код для подсчёта уставок. Нужен для разработки DataSlice для уставок.


                     487 ;90:         Для нормальной работы не нужен.


                     488 ;91: 


                     489 ;92:         case INNER_TYPE_REAL_SETT:


                     490 ;93:         case INNER_TYPE_INT32_SETTS:


                     491 ;94:         case INNER_TYPE_ENUMERATED_SETTS:


                     492 ;95:         case INNER_TYPE_INT32U_SETTS:


                     493 ;96:         case INNER_TYPE_FLOAT_SETT:


                     494 ;97:             incSettCounter();


                     495 ;98: 


                     496 ;99: 


                     497 ;100:             break;


                     498 ;101:         */


                     499 ;102: 


                     500 ;103:     }


                     501 ;104: 


                     502 ;105:     return true;


                     503 

000001bc e3a00001    504 	mov	r0,1

000001c0 ea000004    505 	b	.L118

                     506 .L154:

                     507 ;78:             break;


                     508 ;79:         case INNER_TYPE_CODEDENUM:


                     509 ;80:             IEDCodedEnum_init(entity);


                     510 

000001c4 e1a00005    511 	mov	r0,r5

000001c8 eb000000*   512 	bl	IEDCodedEnum_init

                     513 ;84:             break;


                     514 ;85: 


                     515 ;86:         //Если неизвестный тип, то останется функция-заглушка


                     516 ;87: 


                     517 ;88:         /*!!!


                     518 ;89:         Это код для подсчёта уставок. Нужен для разработки DataSlice для уставок.


                     519 ;90:         Для нормальной работы не нужен.


                     520 ;91: 


                     521 ;92:         case INNER_TYPE_REAL_SETT:


                     522 ;93:         case INNER_TYPE_INT32_SETTS:


                     523 ;94:         case INNER_TYPE_ENUMERATED_SETTS:


                     524 ;95:         case INNER_TYPE_INT32U_SETTS:


                     525 ;96:         case INNER_TYPE_FLOAT_SETT:


                     526 ;97:             incSettCounter();


                     527 ;98: 


                     528 ;99: 


                     529 ;100:             break;


                     530 ;101:         */


                     531 ;102: 


                     532 ;103:     }


                     533 ;104: 


                     534 ;105:     return true;


                     535 

000001cc e3a00001    536 	mov	r0,1

000001d0 ea000000    537 	b	.L118

                     538 .L156:


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_as41.s
                     539 ;84:             break;


                     540 ;85: 


                     541 ;86:         //Если неизвестный тип, то останется функция-заглушка


                     542 ;87: 


                     543 ;88:         /*!!!


                     544 ;89:         Это код для подсчёта уставок. Нужен для разработки DataSlice для уставок.


                     545 ;90:         Для нормальной работы не нужен.


                     546 ;91: 


                     547 ;92:         case INNER_TYPE_REAL_SETT:


                     548 ;93:         case INNER_TYPE_INT32_SETTS:


                     549 ;94:         case INNER_TYPE_ENUMERATED_SETTS:


                     550 ;95:         case INNER_TYPE_INT32U_SETTS:


                     551 ;96:         case INNER_TYPE_FLOAT_SETT:


                     552 ;97:             incSettCounter();


                     553 ;98: 


                     554 ;99: 


                     555 ;100:             break;


                     556 ;101:         */


                     557 ;102: 


                     558 ;103:     }


                     559 ;104: 


                     560 ;105:     return true;


                     561 

000001d4 e3a00001    562 	mov	r0,1

                     563 .L118:

000001d8 e28dd010    564 	add	sp,sp,16

000001dc e8bd8030    565 	ldmfd	[sp]!,{r4-r5,pc}

                     566 	.endf	IEDFinalDA_init

                     567 	.align	4

                     568 ;attrType	[sp]	local

                     569 ;ber	[sp,4]	local

                     570 ;extInfo	r4	local

                     571 ;extInfo	r1	local

                     572 

                     573 ;entity	r5	param

                     574 

                     575 	.section ".bss","awb"

                     576 .L419:

                     577 	.data

                     578 	.text

                     579 

                     580 ;158:     }


                     581 ;159: }


                     582 

                     583 ;160: 


                     584 ;161: bool IEDVarDA_calcReadLen(IEDEntity entity, size_t *pLen)


                     585 	.align	4

                     586 	.align	4

                     587 IEDVarDA_calcReadLen::

                     588 ;162: {


                     589 

                     590 ;163:     if(entity->type != IED_ENTITY_DA_VAR)


                     591 

000001e0 e5902050    592 	ldr	r2,[r0,80]

000001e4 e352000a    593 	cmp	r2,10

000001e8 1a000006    594 	bne	.L505

                     595 ;164:     {


                     596 

                     597 ;165:         return false;


                     598 

                     599 ;166:     }



                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_as41.s
                     600 ;167:     switch(entity->subType)


                     601 

000001ec e5902054    602 	ldr	r2,[r0,84]

000001f0 e2522002    603 	subs	r2,r2,2

000001f4 0a000002    604 	beq	.L501

000001f8 e3520001    605 	cmp	r2,1

                     606 ;171:     case DA_SUBTYPE_ORCAT:


                     607 ;172:         return IEDOrCat_calcReadLen(entity, pLen);


                     608 

000001fc 0a000000*   609 	beq	IEDOrCat_calcReadLen

00000200 ea000000    610 	b	.L505

                     611 .L501:

                     612 ;168:     {


                     613 ;169:     case DA_SUBTYPE_ORIDENT:


                     614 ;170:         return IEDOrIdent_calcReadLen(entity, pLen);


                     615 

00000204 ea000000*   616 	b	IEDOrIdent_calcReadLen

                     617 .L505:

                     618 ;173:     default:


                     619 ;174:         return false;


                     620 

00000208 e3a00000    621 	mov	r0,0

0000020c e12fff1e*   622 	ret	

                     623 	.endf	IEDVarDA_calcReadLen

                     624 	.align	4

                     625 

                     626 ;entity	r0	param

                     627 ;pLen	r1	param

                     628 

                     629 	.section ".bss","awb"

                     630 .L569:

                     631 	.data

                     632 	.text

                     633 

                     634 ;175:     }


                     635 ;176: }


                     636 

                     637 ;177: 


                     638 ;178: bool IEDVarDA_encodeRead(IEDEntity entity, BufferView *outBuf)


                     639 	.align	4

                     640 	.align	4

                     641 IEDVarDA_encodeRead::

                     642 ;179: {


                     643 

                     644 ;180:     if(entity->type != IED_ENTITY_DA_VAR)


                     645 

00000210 e5902050    646 	ldr	r2,[r0,80]

00000214 e352000a    647 	cmp	r2,10

00000218 1a000006    648 	bne	.L600

                     649 ;181:     {


                     650 

                     651 ;182:         return false;


                     652 

                     653 ;183:     }


                     654 ;184:     switch(entity->subType)


                     655 

0000021c e5902054    656 	ldr	r2,[r0,84]

00000220 e2522002    657 	subs	r2,r2,2

00000224 0a000002    658 	beq	.L596

00000228 e3520001    659 	cmp	r2,1

                     660 ;188:     case DA_SUBTYPE_ORCAT:



                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_as41.s
                     661 ;189:         return IEDOrCat_encodeRead(entity, outBuf);


                     662 

0000022c 0a000000*   663 	beq	IEDOrCat_encodeRead

00000230 ea000000    664 	b	.L600

                     665 .L596:

                     666 ;185:     {


                     667 ;186:     case DA_SUBTYPE_ORIDENT:


                     668 ;187:         return IEDOrIdent_encodeRead(entity, outBuf);


                     669 

00000234 ea000000*   670 	b	IEDOrIdent_encodeRead

                     671 .L600:

                     672 ;190:     default:


                     673 ;191:         return false;


                     674 

00000238 e3a00000    675 	mov	r0,0

0000023c e12fff1e*   676 	ret	

                     677 	.endf	IEDVarDA_encodeRead

                     678 	.align	4

                     679 

                     680 ;entity	r0	param

                     681 ;outBuf	r1	param

                     682 

                     683 	.section ".bss","awb"

                     684 .L665:

                     685 	.data

                     686 	.text

                     687 

                     688 ;192:     }


                     689 ;193: }


                     690 

                     691 ;194: 


                     692 ;195: MmsDataAccessError IEDVarDA_write(IEDEntity entity,


                     693 	.align	4

                     694 	.align	4

                     695 IEDVarDA_write::

                     696 ;196:                                            IsoConnection* isoConn, BufferView* value)


                     697 ;197: {


                     698 

                     699 ;198:     switch (entity->subType)


                     700 

00000240 e5903054    701 	ldr	r3,[r0,84]

00000244 e2533002    702 	subs	r3,r3,2

00000248 0a000002    703 	beq	.L689

0000024c e3530001    704 	cmp	r3,1

                     705 ;202:     case DA_SUBTYPE_ORCAT:


                     706 ;203:         return IEDOrCat_write(entity, isoConn, value);


                     707 

00000250 0a000000*   708 	beq	IEDOrCat_write

00000254 ea000000    709 	b	.L693

                     710 .L689:

                     711 ;199:     {


                     712 ;200:     case DA_SUBTYPE_ORIDENT:


                     713 ;201:         return IEDOrIdent_write(entity, isoConn, value);


                     714 

00000258 ea000000*   715 	b	IEDOrIdent_write

                     716 .L693:

                     717 ;204:     default:


                     718 ;205:         ERROR_REPORT("Invalid object to write");


                     719 ;206:         return DATA_ACCESS_ERROR_OBJECT_ACCESS_UNSUPPORTED;


                     720 

0000025c e3a00009    721 	mov	r0,9


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_as41.s
00000260 e12fff1e*   722 	ret	

                     723 	.endf	IEDVarDA_write

                     724 	.align	4

                     725 

                     726 ;entity	r0	param

                     727 ;isoConn	r1	param

                     728 ;value	r2	param

                     729 

                     730 	.section ".bss","awb"

                     731 .L730:

                     732 	.data

                     733 	.text

                     734 

                     735 ;207:     }


                     736 ;208: }


                     737 

                     738 ;209: 


                     739 ;210: void* IEDTermItemDA_getTerminalItemDescr(IEDEntity entity)


                     740 	.align	4

                     741 	.align	4

                     742 IEDTermItemDA_getTerminalItemDescr::

                     743 ;211: {


                     744 

                     745 ;212:     TerminalItem* extInfo;


                     746 ;213:     if(entity->type != IED_ENTITY_DA_TERMINAL_ITEM)


                     747 

00000264 e5901050    748 	ldr	r1,[r0,80]

00000268 e3510008    749 	cmp	r1,8

                     750 ;214:     {


                     751 

                     752 ;215:         return NULL;


                     753 

0000026c 13a00000    754 	movne	r0,0

                     755 ;216:     }


                     756 ;217:     extInfo = entity->extInfo;


                     757 

00000270 05900058    758 	ldreq	r0,[r0,88]

                     759 ;218:     return extInfo->accessInfo;


                     760 

00000274 05900000    761 	ldreq	r0,[r0]

00000278 e12fff1e*   762 	ret	

                     763 	.endf	IEDTermItemDA_getTerminalItemDescr

                     764 	.align	4

                     765 ;extInfo	r0	local

                     766 

                     767 ;entity	r0	param

                     768 

                     769 	.section ".bss","awb"

                     770 .L774:

                     771 	.data

                     772 	.text

                     773 

                     774 ;219: }


                     775 

                     776 ;220: 


                     777 ;221: bool IEDTermItemDA_calcReadLen(IEDEntity entity, size_t *pLen)


                     778 	.align	4

                     779 	.align	4

                     780 IEDTermItemDA_calcReadLen::

0000027c e92d4010    781 	stmfd	[sp]!,{r4,lr}

00000280 e1a04001    782 	mov	r4,r1


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_as41.s
                     783 ;222: {


                     784 

                     785 ;223:     void* pDescrStruct;


                     786 ;224:     int dataLen;


                     787 ;225:     TerminalItem* termItem;


                     788 ;226: 


                     789 ;227:     if(entity->type != IED_ENTITY_DA_TERMINAL_ITEM


                     790 

00000284 e5901050    791 	ldr	r1,[r0,80]

00000288 e3510008    792 	cmp	r1,8

0000028c 1a000047    793 	bne	.L805

00000290 e5902058    794 	ldr	r2,[r0,88]

00000294 e3520000    795 	cmp	r2,0

00000298 0a000044    796 	beq	.L805

                     797 ;228:             || entity->extInfo == NULL)


                     798 ;229:     {


                     799 

                     800 ;230:         ERROR_REPORT("Invalid terminal item DA");


                     801 ;231:         return false;


                     802 

                     803 ;232:     }


                     804 ;233: 


                     805 ;234:     termItem = entity->extInfo;


                     806 

                     807 ;235:     pDescrStruct = termItem->accessInfo;


                     808 

0000029c e5900054    809 	ldr	r0,[r0,84]

000002a0 e5922000    810 	ldr	r2,[r2]

                     811 ;236:     switch (entity->subType)


                     812 

000002a4 e2500023    813 	subs	r0,r0,35

000002a8 2a000007    814 	bhs	.L921

000002ac e2900002    815 	adds	r0,r0,2

000002b0 8a00002a    816 	bhi	.L799

000002b4 0a00001b    817 	beq	.L797

000002b8 e2900001    818 	adds	r0,r0,1

000002bc 0a000012    819 	beq	.L796

000002c0 e2900015    820 	adds	r0,r0,21

000002c4 1a000039    821 	bne	.L805

000002c8 ea000008    822 	b	.L795

                     823 .L921:

                     824 

000002cc e2500000    825 	subs	r0,r0,0

000002d0 0a000029    826 	beq	.L800

000002d4 e2500002    827 	subs	r0,r0,2

000002d8 0a000020    828 	beq	.L799

000002dc e2500001    829 	subs	r0,r0,1

000002e0 0a00002c    830 	beq	.L801

000002e4 e3500002    831 	cmp	r0,2

000002e8 0a000015    832 	beq	.L798

000002ec ea00002f    833 	b	.L805

                     834 .L795:

                     835 ;237:     {


                     836 ;238:     case INNER_TYPE_INT8U:


                     837 ;239:         dataLen = encodeReadInt32U(NULL, 0, pDescrStruct, true);


                     838 

000002f0 e3a03001    839 	mov	r3,1

000002f4 e3a01000    840 	mov	r1,0

000002f8 e1a00001    841 	mov	r0,r1

000002fc eb000000*   842 	bl	encodeReadInt32U

                     843 ;259:         break;



                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_as41.s
                     844 ;260:     default:


                     845 ;261:         ERROR_REPORT("Unsupported data type");


                     846 ;262:         return false;


                     847 

                     848 ;263:     }


                     849 ;264:     if(dataLen <=0)


                     850 

00000300 e3500000    851 	cmp	r0,0

00000304 ca00002b    852 	bgt	.L804

00000308 ea000028    853 	b	.L805

                     854 .L796:

                     855 ;240:         break;


                     856 ;241:     case INNER_TYPE_RCB:


                     857 ;242:         dataLen = encodeReadAttrRCB(NULL, 0, pDescrStruct, true);


                     858 

0000030c e3a03001    859 	mov	r3,1

00000310 e3a01000    860 	mov	r1,0

00000314 e1a00001    861 	mov	r0,r1

00000318 eb000000*   862 	bl	encodeReadAttrRCB

                     863 ;259:         break;


                     864 ;260:     default:


                     865 ;261:         ERROR_REPORT("Unsupported data type");


                     866 ;262:         return false;


                     867 

                     868 ;263:     }


                     869 ;264:     if(dataLen <=0)


                     870 

0000031c e3500000    871 	cmp	r0,0

00000320 ca000024    872 	bgt	.L804

00000324 ea000021    873 	b	.L805

                     874 .L797:

                     875 ;243:         break;


                     876 ;244:     case INNER_TYPE_FLOAT_SETT:


                     877 ;245:         dataLen = encodeReadFloatSett(NULL, 0, pDescrStruct, true);


                     878 

00000328 e3a03001    879 	mov	r3,1

0000032c e3a01000    880 	mov	r1,0

00000330 e1a00001    881 	mov	r0,r1

00000334 eb000000*   882 	bl	encodeReadFloatSett

                     883 ;259:         break;


                     884 ;260:     default:


                     885 ;261:         ERROR_REPORT("Unsupported data type");


                     886 ;262:         return false;


                     887 

                     888 ;263:     }


                     889 ;264:     if(dataLen <=0)


                     890 

00000338 e3500000    891 	cmp	r0,0

0000033c ca00001d    892 	bgt	.L804

00000340 ea00001a    893 	b	.L805

                     894 .L798:

                     895 ;246:         break;


                     896 ;247:     case INNER_TYPE_REAL_SETT:


                     897 ;248:         dataLen = encodeReadRealSett(NULL, 0, pDescrStruct, true);


                     898 

00000344 e3a03001    899 	mov	r3,1

00000348 e3a01000    900 	mov	r1,0

0000034c e1a00001    901 	mov	r0,r1

00000350 eb000000*   902 	bl	encodeReadRealSett

                     903 ;259:         break;


                     904 ;260:     default:



                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_as41.s
                     905 ;261:         ERROR_REPORT("Unsupported data type");


                     906 ;262:         return false;


                     907 

                     908 ;263:     }


                     909 ;264:     if(dataLen <=0)


                     910 

00000354 e3500000    911 	cmp	r0,0

00000358 ca000016    912 	bgt	.L804

0000035c ea000013    913 	b	.L805

                     914 .L799:

                     915 ;249:         break;


                     916 ;250:     case INNER_TYPE_INT32_SETTS:


                     917 ;251:     case INNER_TYPE_ENUMERATED_SETTS:


                     918 ;252:         dataLen = encodeReadInt32Sett(NULL, 0, pDescrStruct, true);


                     919 

00000360 e3a03001    920 	mov	r3,1

00000364 e3a01000    921 	mov	r1,0

00000368 e1a00001    922 	mov	r0,r1

0000036c eb000000*   923 	bl	encodeReadInt32Sett

                     924 ;259:         break;


                     925 ;260:     default:


                     926 ;261:         ERROR_REPORT("Unsupported data type");


                     927 ;262:         return false;


                     928 

                     929 ;263:     }


                     930 ;264:     if(dataLen <=0)


                     931 

00000370 e3500000    932 	cmp	r0,0

00000374 ca00000f    933 	bgt	.L804

00000378 ea00000c    934 	b	.L805

                     935 .L800:

                     936 ;253:         break;


                     937 ;254:     case INNER_TYPE_INT32U_SETTS:


                     938 ;255:         dataLen = encodeReadInt32USett(NULL, 0, pDescrStruct, true);


                     939 

0000037c e3a03001    940 	mov	r3,1

00000380 e3a01000    941 	mov	r1,0

00000384 e1a00001    942 	mov	r0,r1

00000388 eb000000*   943 	bl	encodeReadInt32USett

                     944 ;259:         break;


                     945 ;260:     default:


                     946 ;261:         ERROR_REPORT("Unsupported data type");


                     947 ;262:         return false;


                     948 

                     949 ;263:     }


                     950 ;264:     if(dataLen <=0)


                     951 

0000038c e3500000    952 	cmp	r0,0

00000390 ca000008    953 	bgt	.L804

00000394 ea000005    954 	b	.L805

                     955 .L801:

                     956 ;256:         break;


                     957 ;257:     case INNER_TYPE_GOCB:


                     958 ;258:         dataLen = encodeReadAttrGoCB(NULL, 0, pDescrStruct, true);


                     959 

00000398 e3a03001    960 	mov	r3,1

0000039c e3a01000    961 	mov	r1,0

000003a0 e1a00001    962 	mov	r0,r1

000003a4 eb000000*   963 	bl	encodeReadAttrGoCB

                     964 ;259:         break;


                     965 ;260:     default:



                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_as41.s
                     966 ;261:         ERROR_REPORT("Unsupported data type");


                     967 ;262:         return false;


                     968 

                     969 ;263:     }


                     970 ;264:     if(dataLen <=0)


                     971 

000003a8 e3500000    972 	cmp	r0,0

000003ac ca000001    973 	bgt	.L804

                     974 .L805:

                     975 ;265:     {


                     976 

                     977 ;266:         ERROR_REPORT("Invalid read length");


                     978 ;267:         return false;


                     979 

000003b0 e3a00000    980 	mov	r0,0

000003b4 ea000001    981 	b	.L787

                     982 .L804:

                     983 ;268:     }


                     984 ;269:     *pLen = dataLen;


                     985 

000003b8 e5840000    986 	str	r0,[r4]

                     987 ;270:     return true;


                     988 

000003bc e3a00001    989 	mov	r0,1

                     990 .L787:

000003c0 e8bd8010    991 	ldmfd	[sp]!,{r4,pc}

                     992 	.endf	IEDTermItemDA_calcReadLen

                     993 	.align	4

                     994 ;pDescrStruct	r2	local

                     995 ;dataLen	r0	local

                     996 ;termItem	r2	local

                     997 

                     998 ;entity	r0	param

                     999 ;pLen	r4	param

                    1000 

                    1001 	.section ".bss","awb"

                    1002 .L920:

                    1003 	.data

                    1004 	.text

                    1005 

                    1006 ;271: }


                    1007 

                    1008 ;272: 


                    1009 ;273: bool IEDTermItemDA_encodeRead(IEDEntity entity, BufferView *outBufView)


                    1010 	.align	4

                    1011 	.align	4

                    1012 IEDTermItemDA_encodeRead::

000003c4 e92d4010   1013 	stmfd	[sp]!,{r4,lr}

000003c8 e1a03000   1014 	mov	r3,r0

000003cc e1a04001   1015 	mov	r4,r1

                    1016 ;274: {


                    1017 

                    1018 ;275:     void* pDescrStruct;


                    1019 ;276:     uint8_t *writeBuf = outBufView->p + outBufView->pos;


                    1020 

000003d0 e8940006   1021 	ldmfd	[r4],{r1-r2}

000003d4 e0820001   1022 	add	r0,r2,r1

                    1023 ;277:     int dataLen;


                    1024 ;278:     TerminalItem* termItem;


                    1025 ;279: 


                    1026 ;280:     if(entity->type != IED_ENTITY_DA_TERMINAL_ITEM



                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_as41.s
                    1027 

000003d8 e5931050   1028 	ldr	r1,[r3,80]

000003dc e3510008   1029 	cmp	r1,8

000003e0 1a000045   1030 	bne	.L971

000003e4 e5932058   1031 	ldr	r2,[r3,88]

000003e8 e3520000   1032 	cmp	r2,0

000003ec 0a000042   1033 	beq	.L971

                    1034 ;281:             || entity->extInfo == NULL)


                    1035 ;282:     {


                    1036 

                    1037 ;283:         ERROR_REPORT("Invalid terminal item DA");


                    1038 ;284:         return false;


                    1039 

                    1040 ;285:     }


                    1041 ;286: 


                    1042 ;287:     termItem = entity->extInfo;


                    1043 

                    1044 ;288: 


                    1045 ;289:     pDescrStruct = termItem->accessInfo;


                    1046 

000003f0 e5931054   1047 	ldr	r1,[r3,84]

000003f4 e5922000   1048 	ldr	r2,[r2]

                    1049 ;290:     switch (entity->subType)


                    1050 

000003f8 e2511023   1051 	subs	r1,r1,35

000003fc 2a000007   1052 	bhs	.L1091

00000400 e2911002   1053 	adds	r1,r1,2

00000404 8a000026   1054 	bhi	.L962

00000408 0a000019   1055 	beq	.L960

0000040c e2911001   1056 	adds	r1,r1,1

00000410 0a000011   1057 	beq	.L959

00000414 e2911015   1058 	adds	r1,r1,21

00000418 1a000037   1059 	bne	.L971

0000041c ea000008   1060 	b	.L958

                    1061 .L1091:

                    1062 

00000420 e2511000   1063 	subs	r1,r1,0

00000424 0a000024   1064 	beq	.L963

00000428 e2511002   1065 	subs	r1,r1,2

0000042c 0a00001c   1066 	beq	.L962

00000430 e2511001   1067 	subs	r1,r1,1

00000434 0a000026   1068 	beq	.L964

00000438 e3510002   1069 	cmp	r1,2

0000043c 0a000012   1070 	beq	.L961

00000440 ea00002d   1071 	b	.L971

                    1072 .L958:

                    1073 ;291:     {


                    1074 ;292:     case INNER_TYPE_INT8U:


                    1075 ;293:         dataLen = encodeReadInt32U(writeBuf, 0, pDescrStruct, false);


                    1076 

00000444 e3a03000   1077 	mov	r3,0

00000448 e1a01003   1078 	mov	r1,r3

0000044c eb000000*  1079 	bl	encodeReadInt32U

00000450 e2501000   1080 	subs	r1,r0,0

                    1081 ;313:         break;


                    1082 ;314:     default:


                    1083 ;315:         ERROR_REPORT("Unsupported data type");


                    1084 ;316:         return false;


                    1085 

                    1086 ;317:     }


                    1087 ;318:     if(dataLen <=0)



                                                                      Page 19
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_as41.s
                    1088 

00000454 ca000023   1089 	bgt	.L967

00000458 ea000027   1090 	b	.L971

                    1091 .L959:

                    1092 ;294:         break;


                    1093 ;295:     case INNER_TYPE_RCB:


                    1094 ;296:         dataLen = encodeReadAttrRCB(writeBuf, 0, pDescrStruct, false);


                    1095 

0000045c e3a03000   1096 	mov	r3,0

00000460 e1a01003   1097 	mov	r1,r3

00000464 eb000000*  1098 	bl	encodeReadAttrRCB

00000468 e2501000   1099 	subs	r1,r0,0

                    1100 ;313:         break;


                    1101 ;314:     default:


                    1102 ;315:         ERROR_REPORT("Unsupported data type");


                    1103 ;316:         return false;


                    1104 

                    1105 ;317:     }


                    1106 ;318:     if(dataLen <=0)


                    1107 

0000046c ca00001d   1108 	bgt	.L967

00000470 ea000021   1109 	b	.L971

                    1110 .L960:

                    1111 ;297:         break;


                    1112 ;298:     case INNER_TYPE_FLOAT_SETT:


                    1113 ;299:         dataLen = encodeReadFloatSett(writeBuf, 0, pDescrStruct, false);


                    1114 

00000474 e3a03000   1115 	mov	r3,0

00000478 e1a01003   1116 	mov	r1,r3

0000047c eb000000*  1117 	bl	encodeReadFloatSett

00000480 e2501000   1118 	subs	r1,r0,0

                    1119 ;313:         break;


                    1120 ;314:     default:


                    1121 ;315:         ERROR_REPORT("Unsupported data type");


                    1122 ;316:         return false;


                    1123 

                    1124 ;317:     }


                    1125 ;318:     if(dataLen <=0)


                    1126 

00000484 ca000017   1127 	bgt	.L967

00000488 ea00001b   1128 	b	.L971

                    1129 .L961:

                    1130 ;300:         break;


                    1131 ;301:     case INNER_TYPE_REAL_SETT:


                    1132 ;302:         dataLen = encodeReadRealSett(writeBuf, 0, pDescrStruct, false);


                    1133 

0000048c e3a03000   1134 	mov	r3,0

00000490 e1a01003   1135 	mov	r1,r3

00000494 eb000000*  1136 	bl	encodeReadRealSett

00000498 e2501000   1137 	subs	r1,r0,0

                    1138 ;313:         break;


                    1139 ;314:     default:


                    1140 ;315:         ERROR_REPORT("Unsupported data type");


                    1141 ;316:         return false;


                    1142 

                    1143 ;317:     }


                    1144 ;318:     if(dataLen <=0)


                    1145 

0000049c ca000011   1146 	bgt	.L967

000004a0 ea000015   1147 	b	.L971

                    1148 .L962:


                                                                      Page 20
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_as41.s
                    1149 ;303:         break;


                    1150 ;304:     case INNER_TYPE_INT32_SETTS:


                    1151 ;305:     case INNER_TYPE_ENUMERATED_SETTS:


                    1152 ;306:         dataLen = encodeReadInt32Sett(writeBuf, 0, pDescrStruct, false);


                    1153 

000004a4 e3a03000   1154 	mov	r3,0

000004a8 e1a01003   1155 	mov	r1,r3

000004ac eb000000*  1156 	bl	encodeReadInt32Sett

000004b0 e2501000   1157 	subs	r1,r0,0

                    1158 ;313:         break;


                    1159 ;314:     default:


                    1160 ;315:         ERROR_REPORT("Unsupported data type");


                    1161 ;316:         return false;


                    1162 

                    1163 ;317:     }


                    1164 ;318:     if(dataLen <=0)


                    1165 

000004b4 ca00000b   1166 	bgt	.L967

000004b8 ea00000f   1167 	b	.L971

                    1168 .L963:

                    1169 ;307:         break;


                    1170 ;308:     case INNER_TYPE_INT32U_SETTS:


                    1171 ;309:         dataLen = encodeReadInt32USett(writeBuf, 0, pDescrStruct, false);


                    1172 

000004bc e3a03000   1173 	mov	r3,0

000004c0 e1a01003   1174 	mov	r1,r3

000004c4 eb000000*  1175 	bl	encodeReadInt32USett

000004c8 e2501000   1176 	subs	r1,r0,0

                    1177 ;313:         break;


                    1178 ;314:     default:


                    1179 ;315:         ERROR_REPORT("Unsupported data type");


                    1180 ;316:         return false;


                    1181 

                    1182 ;317:     }


                    1183 ;318:     if(dataLen <=0)


                    1184 

000004cc ca000005   1185 	bgt	.L967

000004d0 ea000009   1186 	b	.L971

                    1187 .L964:

                    1188 ;310:         break;


                    1189 ;311:     case INNER_TYPE_GOCB:


                    1190 ;312:         dataLen = encodeReadAttrGoCB(writeBuf, 0, pDescrStruct, false);


                    1191 

000004d4 e3a03000   1192 	mov	r3,0

000004d8 e1a01003   1193 	mov	r1,r3

000004dc eb000000*  1194 	bl	encodeReadAttrGoCB

000004e0 e2501000   1195 	subs	r1,r0,0

                    1196 ;313:         break;


                    1197 ;314:     default:


                    1198 ;315:         ERROR_REPORT("Unsupported data type");


                    1199 ;316:         return false;


                    1200 

                    1201 ;317:     }


                    1202 ;318:     if(dataLen <=0)


                    1203 

000004e4 da000004   1204 	ble	.L971

                    1205 .L967:

                    1206 ;319:     {


                    1207 

                    1208 ;320:         ERROR_REPORT("Invalid read length");


                    1209 ;321:         return false;



                                                                      Page 21
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_as41.s
                    1210 

                    1211 ;322:     }


                    1212 ;323: 


                    1213 ;324:     if(!BufferView_advance(outBufView, dataLen))


                    1214 

000004e8 e1a00004   1215 	mov	r0,r4

000004ec eb000000*  1216 	bl	BufferView_advance

000004f0 e3500000   1217 	cmp	r0,0

                    1218 ;328:     }


                    1219 ;329:     return true;


                    1220 

000004f4 13a00001   1221 	movne	r0,1

000004f8 1a000000   1222 	bne	.L950

                    1223 .L971:

                    1224 ;325:     {


                    1225 

                    1226 ;326:         ERROR_REPORT("Buffer overflow");


                    1227 ;327:         return false;


                    1228 

000004fc e3a00000   1229 	mov	r0,0

                    1230 .L950:

00000500 e8bd8010   1231 	ldmfd	[sp]!,{r4,pc}

                    1232 	.endf	IEDTermItemDA_encodeRead

                    1233 	.align	4

                    1234 ;pDescrStruct	r2	local

                    1235 ;writeBuf	r0	local

                    1236 ;dataLen	r1	local

                    1237 ;termItem	r2	local

                    1238 

                    1239 ;entity	r3	param

                    1240 ;outBufView	r4	param

                    1241 

                    1242 	.section ".bss","awb"

                    1243 .L1090:

                    1244 	.data

                    1245 	.text

                    1246 

                    1247 ;330: }


                    1248 

                    1249 ;331: 


                    1250 ;332: MmsDataAccessError IEDTermItemDA_write(IEDEntity entity,


                    1251 	.align	4

                    1252 	.align	4

                    1253 IEDTermItemDA_write::

00000504 e92d44f0   1254 	stmfd	[sp]!,{r4-r7,r10,lr}

00000508 e24dd004   1255 	sub	sp,sp,4

0000050c e1a07000   1256 	mov	r7,r0

                    1257 ;333:                                            IsoConnection* isoConn, BufferView* value)


                    1258 ;334: {


                    1259 

                    1260 ;335:     TerminalItem* termItem = entity->extInfo;


                    1261 

00000510 e5970058   1262 	ldr	r0,[r7,88]

                    1263 ;336:     void *pDescrStruct = termItem->accessInfo;


                    1264 

00000514 e5904000   1265 	ldr	r4,[r0]

                    1266 ;337:     size_t len;


                    1267 ;338:     uint8_t *pValue;


                    1268 ;339: 


                    1269 ;340:     if(entity->type != IED_ENTITY_DA_TERMINAL_ITEM)


                    1270 


                                                                      Page 22
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_as41.s
00000518 e5970050   1271 	ldr	r0,[r7,80]

0000051c e3500008   1272 	cmp	r0,8

                    1273 ;341:     {


                    1274 

                    1275 ;342:         return DATA_ACCESS_ERROR_TYPE_INCONSISTENT;


                    1276 

00000520 13a00007   1277 	movne	r0,7

00000524 1a000049   1278 	bne	.L1135

00000528 e1a0a001   1279 	mov	r10,r1

0000052c e1a06002   1280 	mov	r6,r2

                    1281 ;343:     }


                    1282 ;344:     if(BufferView_endOfBuf(value))


                    1283 

00000530 e9960003   1284 	ldmed	[r6],{r0-r1}

00000534 e1500001   1285 	cmp	r0,r1

00000538 0a00000c   1286 	beq	.L1148

                    1287 ;345:     {


                    1288 

                    1289 ;346:         ERROR_REPORT("Empty write value");


                    1290 ;347:         return DATA_ACCESS_ERROR_OBJECT_VALUE_INVALID;


                    1291 

                    1292 ;348:     }


                    1293 ;349:     pValue = value->p + value->pos;


                    1294 

0000053c e5961000   1295 	ldr	r1,[r6]

00000540 e1a0200d   1296 	mov	r2,sp

00000544 e0805001   1297 	add	r5,r0,r1

                    1298 ;350:     if(!BufferView_decodeTL(value, NULL, &len, NULL))


                    1299 

00000548 e1a00006   1300 	mov	r0,r6

0000054c e3a03000   1301 	mov	r3,0

00000550 e1a01003   1302 	mov	r1,r3

00000554 eb000000*  1303 	bl	BufferView_decodeTL

00000558 e3500000   1304 	cmp	r0,0

0000055c 0a000003   1305 	beq	.L1148

                    1306 ;351:     {


                    1307 

                    1308 ;352:         ERROR_REPORT("Invalid write value");


                    1309 ;353:         return DATA_ACCESS_ERROR_OBJECT_VALUE_INVALID;


                    1310 

                    1311 ;354:     }


                    1312 ;355:     if(!BufferView_advance(value, len))


                    1313 

00000560 e59d1000   1314 	ldr	r1,[sp]

00000564 e1a00006   1315 	mov	r0,r6

00000568 eb000000*  1316 	bl	BufferView_advance

0000056c e3500000   1317 	cmp	r0,0

                    1318 .L1148:

                    1319 ;356:     {


                    1320 

                    1321 ;357:         ERROR_REPORT("Invalid write value");


                    1322 ;358:         return DATA_ACCESS_ERROR_OBJECT_VALUE_INVALID;


                    1323 

00000570 03a0000b   1324 	moveq	r0,11

00000574 0a000035   1325 	beq	.L1135

                    1326 .L1147:

                    1327 ;359:     }


                    1328 ;360:     switch (entity->subType)


                    1329 

00000578 e5970054   1330 	ldr	r0,[r7,84]

0000057c e2500022   1331 	subs	r0,r0,34


                                                                      Page 23
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_as41.s
00000580 2a000007   1332 	bhs	.L1285

00000584 e2900002   1333 	adds	r0,r0,2

00000588 8a000023   1334 	bhi	.L1156

0000058c 0a00000d   1335 	beq	.L1152

00000590 e2900017   1336 	adds	r0,r0,23

00000594 0a00001b   1337 	beq	.L1155

00000598 e2900003   1338 	adds	r0,r0,3

0000059c 0a000014   1339 	beq	.L1154

000005a0 ea000029   1340 	b	.L1150

                    1341 .L1285:

                    1342 

000005a4 e2500001   1343 	subs	r0,r0,1

000005a8 9a000023   1344 	bls	.L1160

000005ac e2500002   1345 	subs	r0,r0,2

000005b0 0a000021   1346 	beq	.L1160

000005b4 e2500001   1347 	subs	r0,r0,1

000005b8 0a000008   1348 	beq	.L1153

000005bc e3500002   1349 	cmp	r0,2

000005c0 0a000019   1350 	beq	.L1158

000005c4 ea000020   1351 	b	.L1150

                    1352 .L1152:

                    1353 ;361:     {


                    1354 ;362:     case INNER_TYPE_RCB:


                    1355 ;363:         writeAttrRCB(isoConn, pDescrStruct, pValue);


                    1356 

000005c8 e1a02005   1357 	mov	r2,r5

000005cc e1a01004   1358 	mov	r1,r4

000005d0 e1a0000a   1359 	mov	r0,r10

000005d4 eb000000*  1360 	bl	writeAttrRCB

                    1361 ;382:     default:


                    1362 ;383:         ERROR_REPORT("Write to unsupported type %d ignored", entity->subType);


                    1363 ;384:         //Для неподдерживаемых типов имитируем успешную запись


                    1364 ;385:     }


                    1365 ;386:     return DATA_ACCESS_ERROR_SUCCESS;


                    1366 

000005d8 e3e00000   1367 	mvn	r0,0

000005dc ea00001b   1368 	b	.L1135

                    1369 .L1153:

                    1370 ;364:         break;


                    1371 ;365:     case INNER_TYPE_GOCB:


                    1372 ;366:         writeAttrGoCB(pDescrStruct, pValue);


                    1373 

000005e0 e1a01005   1374 	mov	r1,r5

000005e4 e1a00004   1375 	mov	r0,r4

000005e8 eb000000*  1376 	bl	writeAttrGoCB

                    1377 ;382:     default:


                    1378 ;383:         ERROR_REPORT("Write to unsupported type %d ignored", entity->subType);


                    1379 ;384:         //Для неподдерживаемых типов имитируем успешную запись


                    1380 ;385:     }


                    1381 ;386:     return DATA_ACCESS_ERROR_SUCCESS;


                    1382 

000005ec e3e00000   1383 	mvn	r0,0

000005f0 ea000016   1384 	b	.L1135

                    1385 .L1154:

                    1386 ;367:         break;


                    1387 ;368:     case INNER_TYPE_BOOLEAN:


                    1388 ;369:         writeBoolean(pDescrStruct, pValue);


                    1389 

000005f4 e1a01005   1390 	mov	r1,r5

000005f8 e1a00004   1391 	mov	r0,r4

000005fc eb000000*  1392 	bl	writeBoolean


                                                                      Page 24
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_as41.s
                    1393 ;382:     default:


                    1394 ;383:         ERROR_REPORT("Write to unsupported type %d ignored", entity->subType);


                    1395 ;384:         //Для неподдерживаемых типов имитируем успешную запись


                    1396 ;385:     }


                    1397 ;386:     return DATA_ACCESS_ERROR_SUCCESS;


                    1398 

00000600 e3e00000   1399 	mvn	r0,0

00000604 ea000011   1400 	b	.L1135

                    1401 .L1155:

                    1402 ;370:         break;


                    1403 ;371:     case INNER_TYPE_CODEDENUM:


                    1404 ;372:         writeCodedEnum(pDescrStruct, pValue);


                    1405 

00000608 e1a01005   1406 	mov	r1,r5

0000060c e1a00004   1407 	mov	r0,r4

00000610 eb000000*  1408 	bl	writeCodedEnum

                    1409 ;382:     default:


                    1410 ;383:         ERROR_REPORT("Write to unsupported type %d ignored", entity->subType);


                    1411 ;384:         //Для неподдерживаемых типов имитируем успешную запись


                    1412 ;385:     }


                    1413 ;386:     return DATA_ACCESS_ERROR_SUCCESS;


                    1414 

00000614 e3e00000   1415 	mvn	r0,0

00000618 ea00000c   1416 	b	.L1135

                    1417 .L1156:

                    1418 ;373:         break;


                    1419 ;374:     case INNER_TYPE_FLOAT_SETT:


                    1420 ;375:         return writeFloatSett(pDescrStruct, pValue);


                    1421 

0000061c e1a01005   1422 	mov	r1,r5

00000620 e1a00004   1423 	mov	r0,r4

00000624 eb000000*  1424 	bl	writeFloatSett

00000628 ea000008   1425 	b	.L1135

                    1426 .L1158:

                    1427 ;376:     case INNER_TYPE_REAL_SETT:


                    1428 ;377:         return writeRealSett(pDescrStruct, pValue);        


                    1429 

0000062c e1a01005   1430 	mov	r1,r5

00000630 e1a00004   1431 	mov	r0,r4

00000634 eb000000*  1432 	bl	writeRealSett

00000638 ea000004   1433 	b	.L1135

                    1434 .L1160:

                    1435 ;378:     case INNER_TYPE_INT32_SETTS:


                    1436 ;379:     case INNER_TYPE_INT32U_SETTS:


                    1437 ;380:     case INNER_TYPE_ENUMERATED_SETTS:


                    1438 ;381:         return writeIntSett(pDescrStruct, pValue);        


                    1439 

0000063c e1a01005   1440 	mov	r1,r5

00000640 e1a00004   1441 	mov	r0,r4

00000644 eb000000*  1442 	bl	writeIntSett

00000648 ea000000   1443 	b	.L1135

                    1444 .L1150:

                    1445 ;382:     default:


                    1446 ;383:         ERROR_REPORT("Write to unsupported type %d ignored", entity->subType);


                    1447 ;384:         //Для неподдерживаемых типов имитируем успешную запись


                    1448 ;385:     }


                    1449 ;386:     return DATA_ACCESS_ERROR_SUCCESS;


                    1450 

0000064c e3e00000   1451 	mvn	r0,0

                    1452 .L1135:

00000650 e28dd004   1453 	add	sp,sp,4


                                                                      Page 25
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_as41.s
00000654 e8bd84f0   1454 	ldmfd	[sp]!,{r4-r7,r10,pc}

                    1455 	.endf	IEDTermItemDA_write

                    1456 	.align	4

                    1457 ;termItem	r0	local

                    1458 ;pDescrStruct	r4	local

                    1459 ;len	[sp]	local

                    1460 ;pValue	r5	local

                    1461 

                    1462 ;entity	r7	param

                    1463 ;isoConn	r10	param

                    1464 ;value	r6	param

                    1465 

                    1466 	.section ".bss","awb"

                    1467 .L1284:

                    1468 	.data

                    1469 	.text

                    1470 

                    1471 ;387: }


                    1472 	.align	4

                    1473 .L117:

00000658 00000000*  1474 	.data.w	settCounter

                    1475 	.type	.L117,$object

                    1476 	.size	.L117,4

                    1477 

                    1478 	.align	4

                    1479 

                    1480 	.data

                    1481 .L1348:

                    1482 	.globl	settCounter

00000000 00000000   1483 settCounter:	.data.b	0,0,0,0

                    1484 	.type	settCounter,$object

                    1485 	.size	settCounter,4

                    1486 	.ghsnote version,6

                    1487 	.ghsnote tools,3

                    1488 	.ghsnote options,0

                    1489 	.text

                    1490 	.align	4

                    1491 	.data

                    1492 	.align	4

                    1493 	.text

