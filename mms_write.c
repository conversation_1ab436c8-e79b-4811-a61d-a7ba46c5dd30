#include "mms_write.h"
#include "debug.h"
#include "AsnEncoding.h"
#include "mms.h"
#include "iedmodel.h"
#include "mms_rcb.h"
#include "mms_gocb.h"
#include "mms_data.h"
#include "pwin_access.h"
#include "control.h"
#include "iedTree/iedTree.h"
#include "IEDCompile/InnerAttributeTypes.h"
#include "IEDCompile/AccessInfo.h"
#include <types.h>
#include <stddef.h>

static size_t calcAccessErrEncodedSize(MmsDataAccessError* errList,
                                size_t itemCount)
{
    size_t i;
    size_t size = 0;
    for (i = 0; i < itemCount; i++)
    {
        if (errList[i] < 0)
        {
            size += 2;
        }
        else
        {
            size += 3;
        }
    }
    return size;
}

static size_t encodeAccessErrors(MmsDataAccessError* errList,
                   size_t itemCount, uint8_t* buf, size_t bufPos)
{
    size_t i;
    for (i = 0; i < itemCount; i++)
    {
        if (errList[i] < 0)
        {
            buf[bufPos++] = 0x81;
            buf[bufPos++] = 0x00;
        }
        else
        {
            buf[bufPos++] = 0x80;
            buf[bufPos++] = 0x01;
            buf[bufPos++] = (uint8_t) errList[i];
        }
    }
    return bufPos;
}

MmsDataAccessError writeFloatSett(void* descrStruct, uint8_t* dataToWrite)
{
	FloatAccsessInfo* pSettingInfo = descrStruct;
	float value;
	int intVal;

	if(!pSettingInfo->flags & ACCESS_FLAG_WRITABLE)
	{
		return DATA_ACCESS_ERROR_OBJECT_ACCESS_DENIED;
	}
	
	if (dataToWrite[0] != IEC61850_BER_FLOAT)
	{
		return DATA_ACCESS_ERROR_OBJECT_ACCESS_UNSUPPORTED;
	}
	
	value = BerDecoder_decodeFloat(dataToWrite, 2);
    intVal = (int)(value / pSettingInfo->multiplier);
    if(!pwaWriteFloatSett(pSettingInfo->valueOffset, intVal))
	{
		return DATA_ACCESS_ERROR_TYPE_INCONSISTENT;
	}
	return DATA_ACCESS_ERROR_SUCCESS;
}

MmsDataAccessError writeRealSett(void* descrStruct, uint8_t* dataToWrite)
{	
    FloatAccsessInfo* pSettingInfo = descrStruct;
    float value;

	if(!pSettingInfo->flags & ACCESS_FLAG_WRITABLE)
	{
		return DATA_ACCESS_ERROR_OBJECT_ACCESS_DENIED;
	}

    if (dataToWrite[0] != IEC61850_BER_FLOAT)
    {
        return DATA_ACCESS_ERROR_OBJECT_ACCESS_UNSUPPORTED;
    }
    value = BerDecoder_decodeFloat(dataToWrite, 2);
    value /= pSettingInfo->multiplier;
	if(!pwaWriteRealSett(pSettingInfo->valueOffset, value))
	{
		return DATA_ACCESS_ERROR_TYPE_INCONSISTENT;
	}
	return DATA_ACCESS_ERROR_SUCCESS;    
}

MmsDataAccessError writeIntSett(void* descrStruct, uint8_t* dataToWrite)
{	
	IntBoolAccessInfo* pAccessInfo = descrStruct;
	uint32_t value;
	int len;

	if(!pAccessInfo->flags & ACCESS_FLAG_WRITABLE)
	{
		return DATA_ACCESS_ERROR_OBJECT_ACCESS_DENIED;
	}

	if (dataToWrite[0] != IEC61850_BER_INTEGER
		&& dataToWrite[0] != IEC61850_BER_UNSIGNED_INTEGER)
	{
		return DATA_ACCESS_ERROR_OBJECT_ACCESS_UNSUPPORTED;
	}
	len = dataToWrite[1];
	if (len > 8)
	{
		return DATA_ACCESS_ERROR_OBJECT_VALUE_INVALID;
	}
	value = BerDecoder_decodeUint32(dataToWrite, len, 2);


	if (pAccessInfo->enumTableSize != 0)
	{
		value = getEnumDataValue(value, pAccessInfo->enumTable,
			pAccessInfo->enumTableSize);
	}

    if(!pwaWriteIntSett(pAccessInfo->valueOffset,value))
    {
        return DATA_ACCESS_ERROR_TYPE_INCONSISTENT;
    }
    return DATA_ACCESS_ERROR_SUCCESS;
}

void writeBoolean(void* descrStruct, uint8_t* dataToWrite)
{	
	IntBoolAccessInfo* pAccessInfo = descrStruct;
	int value;

	if(!pAccessInfo->flags & ACCESS_FLAG_WRITABLE)
	{
		return;
	}


	if (dataToWrite[0] != IEC61850_BER_BOOLEAN || dataToWrite[1] != 1)
	{
		return;
	}
	value = dataToWrite[2];	
    if(value)
    {
        writeTele(pAccessInfo->valueOffset);
    }
}

void writeCodedEnum(void* descrStruct, uint8_t* dataToWrite)
{	
    CodedEnumAccessInfo* pAccessInfo = descrStruct;
    int valIdx;
    int value;

	if(!pAccessInfo->flags & ACCESS_FLAG_WRITABLE)
	{
		return;
	}

    if (pAccessInfo->bitCount > 8)
    {
        ERROR_REPORT("More than 8 bits is not supported");
        return;
    }

    value = BerDecoder_DecodeBitStringTLToInt(dataToWrite, 0);
	if (value == -1)
	{
		ERROR_REPORT("Error decoding Coded Enum");
		return;
	}

    
    for (valIdx = pAccessInfo->bitCount-1;  valIdx >= 0  ; --valIdx)
    {
        int offset = pAccessInfo->valueOffsets[valIdx];
        
        if (offset != -1 && (value & 1))
        {
			writeTele(offset);
        }
		value >>= 1;
    }    
}


static int handleWriteRequest(unsigned int invokeId, uint8_t* outBuf,
                       MmsDataAccessError* errList, size_t valCount)
{
	int bufPos = 0;
	//==============determine BER encoded message sizes==============
    int responseContentSize = calcAccessErrEncodedSize(errList, valCount);
	int responseContentSizeTL = responseContentSize + 2;
	int invokeIdSize = BerEncoder_UInt32determineEncodedSize(invokeId) + 2;
	int responseSize = responseContentSizeTL + invokeIdSize;

	//================ encode message ============================
	bufPos = 0;

	// confirmed response PDU
	bufPos = BerEncoder_encodeTL(0xa1, responseSize, outBuf, bufPos);
	// invoke id
	bufPos = BerEncoder_encodeTL(ASN_INTEGER, invokeIdSize - 2, outBuf, bufPos);
	bufPos = BerEncoder_encodeUInt32(invokeId, outBuf, bufPos);

	// confirmed-service-response write
	bufPos = BerEncoder_encodeTL(0xa5, responseContentSize, outBuf, bufPos);

    // encode access results
    bufPos = encodeAccessErrors(errList, valCount, outBuf, bufPos);
	return bufPos;
}

int mms_handleWriteRequest(IsoConnection* isoConn,
        unsigned char* inBuf, int bufPos, int maxBufPos, unsigned int invokeId,
        unsigned char* response)
{
    StringView domainId;
    StringView itemId;
    uint8_t tag;
    int length;
    size_t valListLen;
	int specLength;
	int specEnd;
	int specBufPos;	
    int valCount = 0;
    BufferView valBuf;
    MmsConnection* mmsConn = &isoConn->mmsConn;

	//==========Variable specification=========
    tag = inBuf[bufPos++]; //0xA0
    if(tag != 0xA0)
    {
        ERROR_REPORT("Unexpected tag %02X  instead of var spec", tag);
        return 0;
    }
    bufPos = BerDecoder_decodeLength(inBuf, &specLength, bufPos, maxBufPos);
    if(bufPos < 1)
    {
        ERROR_REPORT("Invalid length");
        return 0;
    }
	specEnd = bufPos + specLength;    
	specBufPos = bufPos;

    BufferView_init(&valBuf, inBuf, maxBufPos, specEnd);

	//==========Value list=================

    if(!BufferView_decodeTL(&valBuf, &tag, &valListLen, NULL ))
    {
        ERROR_REPORT("Invalid write data");
        return 0;
    }

	if (tag != 0xA0)
	{
		ERROR_REPORT("Unexpected tag %d", tag);
		return 0;
	}

    if (valListLen < 1)
	{
        ERROR_REPORT("Invalid length %d", valListLen);
		return 0;
	}

	//specBufPos движется по именам объектов	
	while(specBufPos < specEnd)
	{
		tag = inBuf[specBufPos++]; //0x30 Sequence
		if (tag != 0x30)
		{
			ERROR_REPORT("Unexpected tag %02X instead of sequence", tag);
			return 0;
		}
		specBufPos = BerDecoder_decodeLength(inBuf, &length, specBufPos, maxBufPos);
		if (specBufPos < 1)
		{
			ERROR_REPORT("Invalid length %d", length);
			return 0;
		}

		tag = inBuf[specBufPos++]; //0xA0 Object name
		if (tag != 0xA0)
		{
			ERROR_REPORT("Unexpected tag %02X  instead of object name", tag);
			return 0;
		}
		specBufPos = BerDecoder_decodeLength(inBuf, &length, specBufPos, maxBufPos);
		if (specBufPos < 1)
		{
			ERROR_REPORT("Invalid length %d", length);
			return 0;
		}

		//Decode object name
		specBufPos = BerDecoder_DecodeObjectNameToStringView(inBuf, specBufPos,
			maxBufPos, &domainId, &itemId);

        //Write object
        mmsConn->wrResults[valCount] =                
                IEDTree_write(&domainId, &itemId, isoConn, &valBuf );
		valCount++;
	}    
	
    return handleWriteRequest(invokeId, response, mmsConn->wrResults, valCount);
}
