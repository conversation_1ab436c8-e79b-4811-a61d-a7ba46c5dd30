#ifndef ACSE_H_		
#define ACSE_H_

#include "MmsConst.h"

//Результаты ACSE
#define ACSE_RESULT_ACCEPT 0
#define ACSE_RESULT_REJECT_PERMANENT 1
#define ACSE_RESULT_REJECT_TRANSIENT 2

typedef struct
{
    long nextReference;
    unsigned char outBuf[DEFAULT_BUFFER_SIZE];
} AcseConnection;

void AcseConnection_init(AcseConnection* acse);

//! Возвращает размер сообщения
int AcseConnection_createAssociateResponseMessage(AcseConnection* acse,
        unsigned char* buf, unsigned char* userData, int userDataLen,unsigned char acseResult);


//типы пакетов ACSE
#define	AARQ_PACKET	0x60	//A-ASSOCIATE-REQUEST APDU - Запрос A-ASSOCIATE
#define	AARE_PACKET	0x61	//A-ASSOCIATE-RESPOND APDU - Ответ A-ASSOCIATE

//теги параметров
#define APPLICATION_CONTEXT_NAME	0xa1	//Имя прикладного контекста
#define CALLED_AP_TITLE				0xa2	//Имя вызываевамого Прикладного Процесса
#define CALLED_AE_QUALIFIER			0xa3	//Квалификатор вызываемого Прикладного Логического Объекта
#define CALLING_AP_TITLE			0xa6	//Имя вызываевающего Прикладного Процесса
#define CALLING_AE_QUALIFIER		0xa7	//Квалификатор вызываеющего Прикладного Логического Объекта
#define	USER_INFORMATION			0xbe	//Информация Пользователя
#define	ACSE_USER_INFORMATION		0x28	//Информация Пользователя ACSE
#define USER_INFORMATION_ENCODING	0xa0	//кодирование Информации Пользователя

#define AARE_PACKET_RESULT			0xa2	//Результат пакета AARE		
#define RESULT_SOURCE_DIAGNOSTICS	0xa3	//Источник результата - диагностика пакет AARE

//ACSE_Associate_Packet		пакет Установление Ассоциации ACSE пункт 7.1 
typedef struct 
{

	//AcseConnectionState state;
    long NextReference;			//ссылка на MMS
    //AcseAuthenticationParameter authentication;
    //AcseAuthenticator authenticator;
    //void* authenticatorParameter;
    //void* securityToken;
	int				UserDataPos;	//позиция данных пользователя в общем пакете Presentation
	int				UserDataSize;	//размер данных пользователя
	


}ACSE_Associate_Packet;

typedef enum
{
    ACSE_ERROR,
    ACSE_ASSOCIATE,
    ACSE_ASSOCIATE_FAILED,
    ACSE_OK,
    ACSE_ABORT,
    ACSE_RELEASE_REQUEST,
    ACSE_RELEASE_RESPONSE

}ACSE_Indication;

#endif //ACSE_H_
