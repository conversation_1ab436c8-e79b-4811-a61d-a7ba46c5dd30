                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_d201.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=timers_iednexus.c -o gh_d201.o -list=timers_iednexus.lst C:\Users\<USER>\AppData\Local\Temp\gh_d201.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_d201.s
Source File: timers_iednexus.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      10 ;		timers_iednexus.c -o timers_iednexus.o

                      11 ;Source File:   timers_iednexus.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:55:51 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1:  #include "timers.h"


                      21 ;2: #include "Clib.h"


                      22 ;3: #include "string.h"


                      23 ;4: #include <hw/sama5dx.h>


                      24 ;5: 


                      25 ;6: //Проверка кодировки


                      26 ;7: 


                      27 ;8: //Для аппартного таймера GOOSE


                      28 ;9: #define TC_PERIOD	1000  // период таймера в мкс


                      29 ;10: #define TIMER_ID	(26)  // see at91core\core\timer\src\timer.asm


                      30 ;11: #define TC_TIMER_BASE (volatile unsigned long*)0xF0010080


                      31 ;12: #define AIC_BASE	0xFFFFF000


                      32 ;13: #define PMC_BASE	0xFFFFFC00


                      33 ;14: #define MCK	138000000


                      34 ;15: 


                      35 ;16: static STIMER g_softTimer;


                      36 ;17: static uint32_t tickCounter32 = 0;


                      37 ;18: 


                      38 ;19: // предыдущий обработчик аппаратного таймера (или dummyCallback)


                      39 ;20: void (*oldHWTmrIsrHandlerPtr)(unsigned int id) = NULL;


                      40 ;21: 


                      41 ;22: static void(*goose1msCallbackFunc)(void) = NULL;


                      42 ;23: static void(*integrity1msCallbackFunc)(void) = NULL;


                      43 ;24: static void(*netBusCheck1msCallbackFunc)(void) = NULL;


                      44 ;25: 


                      45 ;26: static void softTimerProc(void)


                      46 ;27: {    


                      47 ;28:     if (integrity1msCallbackFunc)


                      48 ;29:     {


                      49 ;30:         integrity1msCallbackFunc();


                      50 ;31:     }



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_d201.s
                      51 ;32: 	if (netBusCheck1msCallbackFunc)


                      52 ;33: 	{


                      53 ;34: 		netBusCheck1msCallbackFunc();


                      54 ;35: 	}	


                      55 ;36:     tickCounter32++;


                      56 ;37: }


                      57 ;38: 


                      58 ;39: void Timers_setGoose1msCallBack(void(*func)(void))


                      59 ;40: {


                      60 ;41:     goose1msCallbackFunc = func;


                      61 ;42: }


                      62 ;43: 


                      63 ;44: void Timers_setIntegrity1msCallBack(void(*func)(void))


                      64 ;45: {


                      65 ;46:     integrity1msCallbackFunc = func;


                      66 ;47: }


                      67 ;48: 


                      68 ;49: void Timers_setNetBusChek1msCallback(void(*func)(void))


                      69 ;50: {


                      70 ;51: 	netBusCheck1msCallbackFunc = func;


                      71 ;52: }


                      72 ;53: 


                      73 ;54: static void gooseTimerIsr(unsigned int id)


                      74 ;55: {


                      75 ;56:     volatile unsigned int *TC = (volatile unsigned int*)TC_TIMER_BASE;


                      76 ;57:     unsigned int status =  TC[TC_SR] & TC[TC_IMR];


                      77 ;58:     // если прерывание от нужного таймера


                      78 ;59:     if (status & TC_SR_CPCS)


                      79 ;60:     {


                      80 ;61:         // пользовательский обработчик


                      81 ;62:         if (goose1msCallbackFunc)


                      82 ;63:         {


                      83 ;64:             goose1msCallbackFunc();


                      84 ;65:         }


                      85 ;66:     }


                      86 ;67:     // вызываем следующий обработчик


                      87 ;68:     oldHWTmrIsrHandlerPtr(id);


                      88 ;69: }


                      89 ;70: 


                      90 ;71: static void initGOOSETimer(void)


                      91 

                      92 ;105: }


                      93 

                      94 ;106: 


                      95 ;107: void Timers_init(void)


                      96 ;108: {    


                      97 ;109:     memset(&g_softTimer, 0, sizeof(g_softTimer));


                      98 ;110:     CreateTimer(&g_softTimer);


                      99 ;111:     g_softTimer.TimerProc = softTimerProc;


                     100 ;112:     g_softTimer.AlarmTime = 1;


                     101 ;113:     g_softTimer.Precision = 1;


                     102 ;114:     g_softTimer.Started = 1;


                     103 ;115: 


                     104 ;116:     initGOOSETimer();


                     105 ;117: }


                     106 ;118: 


                     107 ;119: uint32_t Timers_getTickCount(void)


                     108 

                     109 ;122: }


                     110 

                     111 	.text


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_d201.s
                     112 	.align	4

                     113 softTimerProc:

00000000 e92d4100    114 	stmfd	[sp]!,{r8,lr}

00000004 e59f81c8*   115 	ldr	r8,.L110

00000008 e598c000    116 	ldr	r12,[r8]

0000000c e35c0000    117 	cmp	r12,0

00000010 11a0e00f    118 	movne	lr,pc

00000014 112fff1c*   119 	bxne	r12

00000018 e59f81b8*   120 	ldr	r8,.L111

0000001c e598c000    121 	ldr	r12,[r8]

00000020 e35c0000    122 	cmp	r12,0

00000024 11a0e00f    123 	movne	lr,pc

00000028 112fff1c*   124 	bxne	r12

0000002c e59f11a8*   125 	ldr	r1,.L112

00000030 e5910000    126 	ldr	r0,[r1]

00000034 e2800001    127 	add	r0,r0,1

00000038 e5810000    128 	str	r0,[r1]

0000003c e8bd4100    129 	ldmfd	[sp]!,{r8,lr}

00000040 e12fff1e*   130 	ret	

                     131 	.endf	softTimerProc

                     132 	.align	4

                     133 

                     134 	.section ".bss","awb"

                     135 .L89:

                     136 	.data

                     137 .L90:

00000000 00000000    138 tickCounter32:	.data.b	0,0,0,0

                     139 	.type	tickCounter32,$object

                     140 	.size	tickCounter32,4

                     141 .L91:

00000004 00000000    142 goose1msCallbackFunc:	.data.b	0,0,0,0

                     143 	.type	goose1msCallbackFunc,$object

                     144 	.size	goose1msCallbackFunc,4

                     145 .L92:

00000008 00000000    146 integrity1msCallbackFunc:	.data.b	0,0,0,0

                     147 	.type	integrity1msCallbackFunc,$object

                     148 	.size	integrity1msCallbackFunc,4

                     149 .L93:

0000000c 00000000    150 netBusCheck1msCallbackFunc:	.data.b	0,0,0,0

                     151 	.type	netBusCheck1msCallbackFunc,$object

                     152 	.size	netBusCheck1msCallbackFunc,4

                     153 	.section ".bss","awb"

00000000 00000000    154 g_softTimer:	.space	20

00000004 00000000 
00000008 00000000 
0000000c 00000000 
00000010 00000000 
                     155 	.data

                     156 	.text

                     157 

                     158 

                     159 	.align	4

                     160 	.align	4

                     161 Timers_setGoose1msCallBack::

00000044 e59fc194*   162 	ldr	r12,.L148

00000048 e58c0000    163 	str	r0,[r12]

0000004c e12fff1e*   164 	ret	

                     165 	.endf	Timers_setGoose1msCallBack

                     166 	.align	4

                     167 

                     168 ;func	r0	param


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_d201.s
                     169 

                     170 	.data

                     171 	.text

                     172 

                     173 

                     174 	.align	4

                     175 	.align	4

                     176 Timers_setIntegrity1msCallBack::

00000050 e59fc17c*   177 	ldr	r12,.L110

00000054 e58c0000    178 	str	r0,[r12]

00000058 e12fff1e*   179 	ret	

                     180 	.endf	Timers_setIntegrity1msCallBack

                     181 	.align	4

                     182 

                     183 ;func	r0	param

                     184 

                     185 	.data

                     186 	.text

                     187 

                     188 

                     189 	.align	4

                     190 	.align	4

                     191 Timers_setNetBusChek1msCallback::

0000005c e59fc174*   192 	ldr	r12,.L111

00000060 e58c0000    193 	str	r0,[r12]

00000064 e12fff1e*   194 	ret	

                     195 	.endf	Timers_setNetBusChek1msCallback

                     196 	.align	4

                     197 

                     198 ;func	r0	param

                     199 

                     200 	.data

                     201 	.text

                     202 

                     203 

                     204 	.align	4

                     205 	.align	4

                     206 gooseTimerIsr:

00000068 e92d4070    207 	stmfd	[sp]!,{r4-r6,lr}

0000006c e59f6170*   208 	ldr	r6,.L274

00000070 e596c000    209 	ldr	r12,[r6]

00000074 e516600c    210 	ldr	r6,[r6,-12]

00000078 e59f5160*   211 	ldr	r5,.L148

0000007c e00cc006    212 	and	r12,r12,r6

00000080 e31c0010    213 	tst	r12,16

00000084 1595c000    214 	ldrne	r12,[r5]

00000088 e1a04000    215 	mov	r4,r0

0000008c 135c0000    216 	cmpne	r12,0

00000090 11a0e00f    217 	movne	lr,pc

00000094 112fff1c*   218 	bxne	r12

00000098 e59f0148*   219 	ldr	r0,.L275

0000009c e590c000    220 	ldr	r12,[r0]

000000a0 e1a00004    221 	mov	r0,r4

000000a4 e1a0e00f    222 	mov	lr,pc

000000a8 e12fff1c*   223 	bx	r12

000000ac e8bd4070    224 	ldmfd	[sp]!,{r4-r6,lr}

000000b0 e12fff1e*   225 	ret	

                     226 	.endf	gooseTimerIsr

                     227 	.align	4

                     228 ;status	r12	local

                     229 


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_d201.s
                     230 ;id	r4	param

                     231 

                     232 	.data

                     233 	.text

                     234 

                     235 

                     236 	.align	4

                     237 	.align	4

                     238 Timers_init::

000000b4 e92d4070    239 	stmfd	[sp]!,{r4-r6,lr}

000000b8 e59f612c*   240 	ldr	r6,.L308

000000bc e3a02014    241 	mov	r2,20

000000c0 e1a00006    242 	mov	r0,r6

000000c4 e3a01000    243 	mov	r1,0

000000c8 eb000000*   244 	bl	memset

000000cc e1a00006    245 	mov	r0,r6

000000d0 eb000000*   246 	bl	CreateTimer

000000d4 e59f0114*   247 	ldr	r0,.L309

000000d8 e59f4114*   248 	ldr	r4,.L310

000000dc e5860010    249 	str	r0,[r6,16]

000000e0 e3a05001    250 	mov	r5,1

000000e4 e5865008    251 	str	r5,[r6,8]

000000e8 e5c65007    252 	strb	r5,[r6,7]

000000ec e5c65005    253 	strb	r5,[r6,5]

                     254 ;72: {


                     255 

                     256 ;73:     int prev = lockInterrupt();


                     257 

000000f0 eb000000*   258 	bl	lockInterrupt

000000f4 e1a06000    259 	mov	r6,r0

                     260 ;74:     volatile unsigned long *TC = TC_TIMER_BASE;


                     261 

                     262 ;75: 


                     263 ;76:     volatile unsigned int *AIC = (volatile unsigned int*)AIC_BASE;


                     264 

                     265 ;77:     volatile unsigned int *PMC = (volatile unsigned int*)PMC_BASE;


                     266 

                     267 ;78:     // старый обработчик


                     268 ;79:     GetIntCallBack(TIMER_ID,(void (**)())&oldHWTmrIsrHandlerPtr);


                     269 

000000f8 e59f10e8*   270 	ldr	r1,.L275

000000fc e3a0001a    271 	mov	r0,26

00000100 eb000000*   272 	bl	GetIntCallBack

                     273 ;80:     // включаем таймер


                     274 ;81:     PMC[PMC_PCER0]=1 << TIMER_ID;


                     275 

00000104 e3a00640    276 	mov	r0,1<<26

00000108 e3e01fc0    277 	mvn	r1,3<<8

0000010c e22110ef    278 	eor	r1,r1,239

00000110 e5810000    279 	str	r0,[r1]

                     280 ;82:     AIC[AIC_SSR] = TIMER_ID;


                     281 

00000114 e3a0001a    282 	mov	r0,26

00000118 e3e01ef0    283 	mvn	r1,15<<8

0000011c e22110ff    284 	eor	r1,r1,255

00000120 e5810000    285 	str	r0,[r1]

                     286 ;83:     AIC[AIC_SMR] = (0<<5) | 3;


                     287 

00000124 e3a01003    288 	mov	r1,3

00000128 e3e02ef0    289 	mvn	r2,15<<8

0000012c e22220fb    290 	eor	r2,r2,251


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_d201.s
00000130 e5821000    291 	str	r1,[r2]

                     292 ;84:     AIC[AIC_IECR] = 1;


                     293 

00000134 e3e01ef0    294 	mvn	r1,15<<8

00000138 e22110bf    295 	eor	r1,r1,191

0000013c e5815000    296 	str	r5,[r1]

                     297 ;85: 


                     298 ;86: 


                     299 ;87:     SetIntCallBack(TIMER_ID,gooseTimerIsr);


                     300 

00000140 e59f10b0*   301 	ldr	r1,.L311

00000144 eb000000*   302 	bl	SetIntCallBack

                     303 ;88:     // Disable CLK


                     304 ;89:     TC[TC_CCR]=TC_CCR_CLKDIS;


                     305 

00000148 e3a00002    306 	mov	r0,2

0000014c e5840000    307 	str	r0,[r4]

                     308 ;90: 


                     309 ;91:     // Disable all timer interrupts


                     310 ;92:     TC[TC_IDR] = 0xFF;


                     311 

00000150 e3a000ff    312 	mov	r0,255

00000154 e5840028    313 	str	r0,[r4,40]

                     314 ;93: 


                     315 ;94:     TC[TC_CMR] = TC_CMR_WAVE | TC_CMR_CPCTRG | TC_CMR_TIMER_CLOCK2;  // MCK/8


                     316 

00000158 e3a00cc0    317 	mov	r0,3<<14

0000015c e2800001    318 	add	r0,r0,1

00000160 e5840004    319 	str	r0,[r4,4]

                     320 ;95: 


                     321 ;96: 


                     322 ;97:     TC[TC_SR]; // read status


                     323 

00000164 e5940020    324 	ldr	r0,[r4,32]

                     325 ;98:     TC[TC_IER] = TC_IER_CPCS; // enable interrupt


                     326 

00000168 e3a00010    327 	mov	r0,16

0000016c e5840024    328 	str	r0,[r4,36]

                     329 ;99:     TC[TC_RC] = MCK/8/(1000000/TC_PERIOD);


                     330 

00000170 e3a00c43    331 	mov	r0,67<<8

00000174 e2800062    332 	add	r0,r0,98

00000178 e584001c    333 	str	r0,[r4,28]

                     334 ;100: 


                     335 ;101: 


                     336 ;102:     // Reset timer and enable


                     337 ;103:     TC[TC_CCR]=TC_CCR_SWTRG | TC_CCR_CLKEN;


                     338 

0000017c e3a00005    339 	mov	r0,5

00000180 e5840000    340 	str	r0,[r4]

                     341 ;104:     unlockInterrupt(prev);


                     342 

00000184 e1a00006    343 	mov	r0,r6

00000188 e8bd4070    344 	ldmfd	[sp]!,{r4-r6,lr}

0000018c ea000000*   345 	b	unlockInterrupt

                     346 	.endf	Timers_init

                     347 	.align	4

                     348 ;prev	r6	local

                     349 

                     350 	.data

                     351 	.text


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_d201.s
                     352 

                     353 

                     354 ;123: 


                     355 ;124: bool Timers_isTimeout(uint32_t startTime, uint32_t timeout)


                     356 	.align	4

                     357 	.align	4

                     358 Timers_isTimeout::

                     359 ;125: {


                     360 

                     361 ;126:     uint32_t tickCount = Timers_getTickCount();


                     362 

                     363 ;120: {


                     364 

                     365 ;121:     return tickCounter32;


                     366 

00000190 e59f3044*   367 	ldr	r3,.L112

00000194 e5932000    368 	ldr	r2,[r3]

                     369 ;127:     uint32_t timePassed;


                     370 ;128: 


                     371 ;129:     // сколько прошло времени с момента запуска


                     372 ;130:     if (startTime  <= tickCount)


                     373 

00000198 e1500002    374 	cmp	r0,r2

0000019c e0423000    375 	sub	r3,r2,r0

000001a0 8a000003    376 	bhi	.L318

                     377 ;131:     {


                     378 

                     379 ;132:         timePassed = tickCount - startTime;


                     380 

                     381 ;137:     }


                     382 ;138:     return timePassed > timeout;


                     383 

000001a4 e1530001    384 	cmp	r3,r1

000001a8 83a00001    385 	movhi	r0,1

000001ac 93a00000    386 	movls	r0,0

000001b0 ea000003    387 	b	.L312

                     388 .L318:

                     389 ;133:     }


                     390 ;134:     else // переполнение


                     391 ;135:     {


                     392 

                     393 ;136:         timePassed = UINT32_MAX - startTime + tickCount;


                     394 

000001b4 e2430001    395 	sub	r0,r3,1

                     396 ;137:     }


                     397 ;138:     return timePassed > timeout;


                     398 

000001b8 e1500001    399 	cmp	r0,r1

000001bc 83a00001    400 	movhi	r0,1

000001c0 93a00000    401 	movls	r0,0

                     402 .L312:

000001c4 e12fff1e*   403 	ret	

                     404 	.endf	Timers_isTimeout

                     405 	.align	4

                     406 ;tickCount	r2	local

                     407 ;timePassed	r0	local

                     408 

                     409 ;startTime	r0	param

                     410 ;timeout	r1	param

                     411 

                     412 	.section ".bss","awb"


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_d201.s
                     413 .L368:

                     414 	.data

                     415 	.text

                     416 

                     417 ;139: }


                     418 	.align	4

                     419 	.align	4

                     420 Timers_getTickCount::

                     421 ;120: {


                     422 

                     423 ;121:     return tickCounter32;


                     424 

000001c8 e59fc00c*   425 	ldr	r12,.L112

000001cc e59c0000    426 	ldr	r0,[r12]

000001d0 e12fff1e*   427 	ret	

                     428 	.endf	Timers_getTickCount

                     429 	.align	4

                     430 

                     431 	.data

                     432 	.text

                     433 	.align	4

                     434 .L110:

000001d4 00000000*   435 	.data.w	.L92

                     436 	.type	.L110,$object

                     437 	.size	.L110,4

                     438 

                     439 .L111:

000001d8 00000000*   440 	.data.w	.L93

                     441 	.type	.L111,$object

                     442 	.size	.L111,4

                     443 

                     444 .L112:

000001dc 00000000*   445 	.data.w	.L90

                     446 	.type	.L112,$object

                     447 	.size	.L112,4

                     448 

                     449 .L148:

000001e0 00000000*   450 	.data.w	.L91

                     451 	.type	.L148,$object

                     452 	.size	.L148,4

                     453 

                     454 .L274:

000001e4 f00100ac    455 	.data.w	0xf00100ac

                     456 	.type	.L274,$object

                     457 	.size	.L274,4

                     458 

                     459 .L275:

000001e8 00000000*   460 	.data.w	oldHWTmrIsrHandlerPtr

                     461 	.type	.L275,$object

                     462 	.size	.L275,4

                     463 

                     464 .L308:

000001ec 00000000*   465 	.data.w	g_softTimer

                     466 	.type	.L308,$object

                     467 	.size	.L308,4

                     468 

                     469 .L309:

000001f0 00000000*   470 	.data.w	softTimerProc

                     471 	.type	.L309,$object

                     472 	.size	.L309,4

                     473 


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_d201.s
                     474 .L310:

000001f4 f0010080    475 	.data.w	0xf0010080

                     476 	.type	.L310,$object

                     477 	.size	.L310,4

                     478 

                     479 .L311:

000001f8 00000000*   480 	.data.w	gooseTimerIsr

                     481 	.type	.L311,$object

                     482 	.size	.L311,4

                     483 

                     484 	.align	4

                     485 ;g_softTimer	g_softTimer	static

                     486 ;tickCounter32	.L90	static

                     487 ;goose1msCallbackFunc	.L91	static

                     488 ;integrity1msCallbackFunc	.L92	static

                     489 ;netBusCheck1msCallbackFunc	.L93	static

                     490 

                     491 	.data

                     492 .L420:

                     493 	.globl	oldHWTmrIsrHandlerPtr

00000010 00000000    494 oldHWTmrIsrHandlerPtr:	.data.b	0,0,0,0

                     495 	.type	oldHWTmrIsrHandlerPtr,$object

                     496 	.size	oldHWTmrIsrHandlerPtr,4

                     497 	.ghsnote version,6

                     498 	.ghsnote tools,3

                     499 	.ghsnote options,0

                     500 	.text

                     501 	.align	4

                     502 	.data

                     503 	.align	4

                     504 	.section ".bss","awb"

                     505 	.align	4

                     506 	.text

