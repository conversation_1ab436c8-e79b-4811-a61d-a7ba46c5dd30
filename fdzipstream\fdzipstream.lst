                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1201.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=fdzipstream.c -o fdzipstream\gh_1201.o -list=fdzipstream/fdzipstream.lst C:\Users\<USER>\AppData\Local\Temp\gh_1201.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_1201.s
Source File: fdzipstream.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      10 ;		fdzipstream/fdzipstream.c -o fdzipstream/fdzipstream.o

                      11 ;Source File:   fdzipstream/fdzipstream.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Oct 07 09:39:32 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: /***************************************************************************

                      21 ;2:  * fdzipstream.c

                      22 ;3:  *

                      23 ;4:  * Create ZIP archives in streaming fashion, writing to a file

                      24 ;5:  * descriptor.  The output stream (file descriptor) does not need to

                      25 ;6:  * be seekable and can be a pipe or a network socket.  The entire

                      26 ;7:  * archive contents does not need to be in memory at once.

                      27 ;8:  *

                      28 ;9:  * zlib is required for deflate compression: http://www.zlib.net/

                      29 ;10:  *

                      30 ;11:  * What this will do for you:

                      31 ;12:  *

                      32 ;13:  * - Create a ZIP archive in a streaming fashion, writing to an output

                      33 ;14:  *   stream (file descriptor, pipe, network socket) without seeking.

                      34 ;15:  * - Compress the archive entries (using zlib).  Support for the STORE

                      35 ;16:  *   and DEFLATE methods is included, others may be implemented through

                      36 ;17:  *   callback functions.

                      37 ;18:  * - Add ZIP64 structures as needed to support large (>4GB) archives.

                      38 ;19:  * - Simple creation of ZIP archives even if not streaming.

                      39 ;20:  *

                      40 ;21:  * What this will NOT do for you:

                      41 ;22:  *

                      42 ;23:  * - Open/close files or sockets.

                      43 ;24:  * - Support advanced ZIP archive features (e.g. file attributes, encryption).

                      44 ;25:  * - Allow archiving of individual files/entries larger than 4GB, the total

                      45 ;26:  *    of all files can be larger than 4GB but not individual entries.

                      46 ;27:  *

                      47 ;28:  * ZIP archive file/entry modifiation times are stored in UTC.

                      48 ;29:  *

                      49 ;30:  * Usage pattern

                      50 ;31:  *


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1201.s
                      51 ;32:  * Creating a ZIP archive when entire files/entries are in memory:

                      52 ;33:  *  zs_init ()

                      53 ;34:  *    for each entry:

                      54 ;35:  *      zs_writeentry ()

                      55 ;36:  *  zs_finish ()

                      56 ;37:  *  zs_free ()

                      57 ;38:  *

                      58 ;39:  * Creating a ZIP archive when files/entries are chunked:

                      59 ;40:  *  zs_init ()

                      60 ;41:  *    for each entry:

                      61 ;42:  *      zs_entrybegin ()

                      62 ;43:  *        for each chunk of entry:

                      63 ;44:  *          zs_entrydata()

                      64 ;45:  *      zs_entryend()

                      65 ;46:  *  zs_finish ()

                      66 ;47:  *  zs_free ()

                      67 ;48:  *

                      68 ;49:  ****

                      69 ;50:  * To use archive entry compression methods other than the included

                      70 ;51:  * STORE and DEFLATE methods you must create and register callback

                      71 ;52:  * funtions as follows:

                      72 ;53:  *

                      73 ;54:  * int32_t init (ZIPstream *zstream, ZIPentry *zentry)

                      74 ;55:  *

                      75 ;56:  *   This optional function is called at the beginning of each entry.

                      76 ;57:  *   Return: 0 on success and non-zero on error.

                      77 ;58:  *

                      78 ;59:  * int32_t process (ZIPstream *zstream, ZIPentry *zentry,

                      79 ;60:  *                  uint8_t *entry, int64_t entrySize, int64_t *entryConsumed,

                      80 ;61:  *                  uint8_t* writeBuffer, int64_t writeBufferSize)

                      81 ;62:  *

                      82 ;63:  *   This required function is called to process entry content data.

                      83 ;64:  *   Data to write into the archive should be returned in writeBuffer.

                      84 ;65:  *   When entry is NULL internal buffers should be flushed.

                      85 ;66:  *   Return: Count of bytes ready in writeBuffer, 0 on completion and <0 on error

                      86 ;67:  *

                      87 ;68:  * int32_t finish (ZIPstream *zstream, ZIPentry *zentry)

                      88 ;69:  *

                      89 ;70:  *   This optional function is called at the end of each entry.

                      90 ;71:  *   Return: 0 on success and non-zero on error.

                      91 ;72:  *

                      92 ;73:  * These three functions must be registered, through zs_registermethod(),

                      93 ;74:  * with any ZIPstream that will use them.

                      94 ;75:  ****

                      95 ;76:  * LICENSE

                      96 ;77:  *

                      97 ;78:  * Copyright 2019 CTrabant

                      98 ;79:  *

                      99 ;80:  * Licensed under the Apache License, Version 2.0 (the "License");

                     100 ;81:  * you may not use this file except in compliance with the License.

                     101 ;82:  * You may obtain a copy of the License at

                     102 ;83:  *

                     103 ;84:  *     http://www.apache.org/licenses/LICENSE-2.0

                     104 ;85:  *

                     105 ;86:  * Unless required by applicable law or agreed to in writing, software

                     106 ;87:  * distributed under the License is distributed on an "AS IS" BASIS,

                     107 ;88:  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

                     108 ;89:  * See the License for the specific language governing permissions and

                     109 ;90:  * limitations under the License.

                     110 ;91:  ***************************************************************************/

                     111 ;93: /* Allow this code to be skipped by declaring NOFDZIP */


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1201.s
                     112 ;94: #ifndef NOFDZIP

                     113 ;96: #define FDZIPVERSION 2.2

                     114 ;98: #define fprintf(...)

                     115 ;100: #include <stdlib.h>

                     116 ;101: #include <stdint.h>

                     117 ;102: #include <string.h>

                     118 ;105: #include "fdzipstream.h"

                     119 ;106: #include "crc32.h"

                     120 ;107: #include <time.h>

                     121 ;108: #include <string.h>

                     122 ;109: #include "../fs/OscFiles.h"

                     123 ;110: #include "../timetools.h"

                     124 ;112: #define BIT_SET(a,b) ((a) |= (1<<(b)))

                     125 ;114: static int64_t zs_writedata ( ZIPstream *zstream, uint8_t *writeBuffer, int64_t writeBufferSize );

                     126 ;115: static uint32_t zs_datetime_unixtodos ( time_t t );

                     127 ;116: static void zs_packunit16 (ZIPstream *ZS, int *O, uint16_t V);

                     128 ;117: static void zs_packunit32 (ZIPstream *ZS, int *O, uint32_t V);

                     129 ;118: static void zs_packunit64 (ZIPstream *ZS, int *O, uint64_t V);

                     130 ;120: static void * allocMemory(size_t elsize)

                     131 ;121: {

                     132 ;122:     void *p;

                     133 ;124:     p = OscFiles_malloc(elsize);

                     134 ;125:     if (p == 0)

                     135 ;126:         return (p);

                     136 ;128:     memset (p, 0, elsize);

                     137 ;129:     return (p);

                     138 ;130: }

                     139 ;132: void freeMemory(void *p)

                     140 ;133: {

                     141 ;134: 	OscFiles_free(p);

                     142 ;135: }

                     143 ;137: /***************************************************************************

                     144 ;138:  * zs_store_process:

                     145 ;139:  *

                     146 ;140:  * The process() callback for the STORE method.

                     147 ;141:  *

                     148 ;142:  * @return number of bytes ready for writing in writeBuffer or <0 on error.

                     149 ;143:  ***************************************************************************/

                     150 ;144: static int32_t

                     151 ;145: zs_store_process ( ZIPstream *zstream, ZIPentry *zentry,

                     152 ;146:                    uint8_t *entry, int64_t entrySize, int64_t *entryConsumed,

                     153 ;147:                    uint8_t *writeBuffer, int64_t writeBufferSize )

                     154 ;148: {

                     155 ;149:   if ( ! entry || entrySize <= 0 )

                     156 ;150:     return 0;

                     157 ;152:   if ( entrySize < writeBufferSize )

                     158 ;153:     {

                     159 ;154:       writeBufferSize = entrySize;

                     160 ;155:     }

                     161 ;157:   memcpy ( writeBuffer, entry, (size_t)writeBufferSize );

                     162 ;159:   if ( entryConsumed )

                     163 ;160:     {

                     164 ;161:       *entryConsumed = writeBufferSize;

                     165 ;162:     }

                     166 ;164:   return (int32_t)writeBufferSize;

                     167 ;165: }  /* End of zs_store_process() */

                     168 ;169: /***************************************************************************

                     169 ;170:  * zs_registermethod:

                     170 ;171:  *

                     171 ;172:  * Initialize a new ZIPmethod entry and add it to the method list for

                     172 ;173:  * the supplied ZIPstream.


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1201.s
                     173 ;174:  *

                     174 ;175:  * Each method requires an ID, mind that the ZIP APPNOTE defines some

                     175 ;176:  * specific IDs already.  Each method is also required to provide

                     176 ;177:  * three functions:

                     177 ;178:  *

                     178 ;179:  * init()     : Initialization to start an entry, optional.

                     179 ;180:  * process()  : Process new data and/or flush to finalize an entry, required.

                     180 ;181:  * finish()   : Finalize an entry, cleanup, optional.

                     181 ;182:  *

                     182 ;183:  * Optional function pointers should NULL if no action is needed.

                     183 ;184:  *

                     184 ;185:  * @return a pointer to a ZIPmethod struct on success or NULL on error.

                     185 ;186:  ***************************************************************************/

                     186 ;187: ZIPmethod *

                     187 ;188: zs_registermethod ( ZIPstream *zs, int32_t methodID,

                     188 ;189:                     int32_t (*init)( ZIPstream*, ZIPentry* ),

                     189 ;190:                     int32_t (*process)( ZIPstream*, ZIPentry*,

                     190 ;191:                                         uint8_t*, int64_t, int64_t*,

                     191 ;192:                                         uint8_t*, int64_t ),

                     192 ;193:                     int32_t (*finish)( ZIPstream*, ZIPentry* )

                     193 ;194:                     )

                     194 ;195: {

                     195 ;196:   ZIPmethod *method = zs->firstMethod;

                     196 ;198:   /* Require a process() callback for the method */

                     197 ;199:   if ( ! process )

                     198 ;200:     {

                     199 ;201:       fprintf (stderr, "Compression method (%d) must provide a process() callback\n",

                     200 ;202:                methodID);

                     201 ;203:       return NULL;

                     202 ;204:     }

                     203 ;206:   /* Search for existing method */

                     204 ;207:   while ( method )

                     205 ;208:     {

                     206 ;209:       if ( method->ID == methodID )

                     207 ;210:         {

                     208 ;211:           fprintf (stderr, "Compression method (%d) already registered\n",

                     209 ;212:                    methodID);

                     210 ;213:           return NULL;

                     211 ;214:         }

                     212 ;216:       method = method->next;

                     213 ;217:     }

                     214 ;219:   /* Allocate and initialize new method */

                     215 ;220:   method = (ZIPmethod *)allocMemory(sizeof(ZIPmethod));

                     216 ;222:   if ( method == NULL )

                     217 ;223:     {

                     218 ;224:       fprintf (stderr, "Cannot allocate memory for method\n");

                     219 ;225:       return NULL;

                     220 ;226:     }

                     221 ;228:   method->ID = methodID;

                     222 ;229:   method->init = init;

                     223 ;230:   method->process = process;

                     224 ;231:   method->finish = finish;

                     225 ;233:   /* Add new method to ZIPstream list */

                     226 ;234:   method->next = zs->firstMethod;

                     227 ;235:   zs->firstMethod = method;

                     228 ;237:   return method;

                     229 ;238: }  /* End of zs_registermethod() */

                     230 ;241: /***************************************************************************

                     231 ;242:  * zs_init:

                     232 ;243:  *

                     233 ;244:  * Initialize and return an ZIPstream struct. If a pointer to an


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1201.s
                     234 ;245:  * existing ZIPstream is supplied it will be re-initizlied, otherwise

                     235 ;246:  * memory will be allocated.

                     236 ;247:  *

                     237 ;248:  * @return a pointer to a ZIPstream struct on success or NULL on error.

                     238 ;249:  ***************************************************************************/

                     239 ;250: ZIPstream *

                     240 ;251: zs_init ( int fd, ZIPstream *zs )

                     241 ;252: {

                     242 ;253:   ZIPentry *zentry, *zefree;

                     243 ;254:   ZIPmethod *method, *mfree;

                     244 ;256:   if ( ! zs )

                     245 ;257:     {

                     246 ;258:       zs = (ZIPstream *) allocMemory (sizeof(ZIPstream));

                     247 ;259:     }

                     248 ;260:   else

                     249 ;261:     {

                     250 ;262:       zentry = zs->FirstEntry;

                     251 ;263:       while ( zentry )

                     252 ;264:         {

                     253 ;265:           zefree = zentry;

                     254 ;266:           zentry = zentry->next;

                     255 ;267:           freeMemory (zefree);

                     256 ;268:         }

                     257 ;270:       method = zs->firstMethod;

                     258 ;271:       while ( method )

                     259 ;272:         {

                     260 ;273:           mfree = method;

                     261 ;274:           method = method->next;

                     262 ;275: 		  freeMemory(mfree);

                     263 ;276:         }

                     264 ;277:     }

                     265 ;279:   if ( zs == NULL )

                     266 ;280:     {

                     267 ;281:       fprintf (stderr, "zs_init: Cannot allocate memory for ZIPstream\n");

                     268 ;282:       return NULL;

                     269 ;283:     }

                     270 ;285:   memset (zs, 0, sizeof (ZIPstream));

                     271 ;287:   zs->fd = fd;

                     272 ;289:   /* Register the included ZS_STORE and ZS_DEFLATE compression methods */

                     273 ;290:   if ( ! zs_registermethod ( zs, ZS_STORE,

                     274 ;291:                              NULL,

                     275 ;292:                              zs_store_process,

                     276 ;293:                              NULL ) )

                     277 ;294:     {

                     278 ;295: 	  freeMemory(zs);

                     279 ;296:       return NULL;

                     280 ;297:     }

                     281 ;299:   return zs;

                     282 ;300: }  /* End of zs_init() */

                     283 ;303: /***************************************************************************

                     284 ;304:  * zs_free:

                     285 ;305:  *

                     286 ;306:  * Free all memory associated with a ZIPstream including all ZIPentry

                     287 ;307:  * structures.

                     288 ;308:  ***************************************************************************/

                     289 ;309: void

                     290 ;310: zs_free ( ZIPstream *zs )

                     291 ;311: {

                     292 ;312:   ZIPentry *zentry, *zefree;

                     293 ;313:   ZIPmethod *method, *mfree;

                     294 ;315:   if ( ! zs )


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1201.s
                     295 ;316:     return;

                     296 ;318:   zentry = zs->FirstEntry;

                     297 ;319:   while ( zentry )

                     298 ;320:     {

                     299 ;321:       zefree = zentry;

                     300 ;322:       zentry = zentry->next;

                     301 ;323: 	  freeMemory(zefree);

                     302 ;324:     }

                     303 ;326:   method = zs->firstMethod;

                     304 ;327:   while ( method )

                     305 ;328:     {

                     306 ;329:       mfree = method;

                     307 ;330:       method = method->next;

                     308 ;331: 	  freeMemory(mfree);

                     309 ;332:     }

                     310 ;334:   freeMemory(zs);

                     311 ;336: }  /* End of zs_free() */

                     312 ;339: /***************************************************************************

                     313 ;340:  * zs_writeentry:

                     314 ;341:  *

                     315 ;342:  * Write ZIP archive entry contained in a memory buffer using the

                     316 ;343:  * specified compression methodID.

                     317 ;344:  *

                     318 ;345:  * The methodID argument specifies the compression methodID to be used

                     319 ;346:  * for this entry.  Included methods are:

                     320 ;347:  *   Z_STORE   - no compression

                     321 ;348:  *   Z_DEFLATE - deflate compression

                     322 ;349:  *

                     323 ;350:  * The entry modified time (modtime) is stored in UTC.

                     324 ;351:  *

                     325 ;352:  * If specified, writestatus will be set to the output of write() when

                     326 ;353:  * a write error occurs, otherwise it will be set to 0.

                     327 ;354:  *

                     328 ;355:  * @return pointer to ZIPentry on success and NULL on error.

                     329 ;356:  ***************************************************************************/

                     330 ;357: ZIPentry *

                     331 ;358: zs_writeentry ( ZIPstream *zstream, uint8_t *entry, int64_t entrySize,

                     332 ;359:                 char *name, time_t modtime, int methodID, int64_t *writestatus )

                     333 ;360: {

                     334 ;361:   ZIPentry *zentry = NULL;

                     335 ;363:   if ( writestatus )

                     336 ;364:     *writestatus = 0;

                     337 ;366:   if ( ! zstream )

                     338 ;367:     return NULL;

                     339 ;369:   if ( entrySize > 0xFFFFFFFF )

                     340 ;370:     {

                     341 ;371:       fprintf (stderr, "zs_writeentry(%s): Individual entries cannot exceed %lld bytes\n",

                     342 ;372:                (name) ? name : "", (long long) 0xFFFFFFFF);

                     343 ;373:       return NULL;

                     344 ;374:     }

                     345 ;376:   /* Begin entry */

                     346 ;377:   if ( ! (zentry = zs_entrybegin ( zstream, name, modtime, methodID, writestatus )) )

                     347 ;378:     {

                     348 ;379:       return NULL;

                     349 ;380:     }

                     350 ;382:   /* Process entry data and flush */

                     351 ;383:   if ( ! zs_entrydata (zstream, zentry, entry, entrySize, writestatus) )

                     352 ;384:     {

                     353 ;385:       return NULL;

                     354 ;386:     }

                     355 ;388:   /* End entry */


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1201.s
                     356 ;389:   if ( ! zs_entryend (zstream, zentry, writestatus) )

                     357 ;390:     {

                     358 ;391:       return NULL;

                     359 ;392:     }

                     360 ;394:   return zentry;

                     361 ;395: }  /* End of zs_writeentry() */

                     362 ;399: /***************************************************************************

                     363 ;400:  * zs_entrybegin:

                     364 ;401:  *

                     365 ;402:  * Begin a streaming entry by writing a Local File Header to the

                     366 ;403:  * output stream.  The modtime argument sets the modification time

                     367 ;404:  * stamp for the entry.

                     368 ;405:  *

                     369 ;406:  * The methodID argument specifies the compression method to be used

                     370 ;407:  * for this entry.  Included methods are:

                     371 ;408:  *   Z_STORE   - no compression

                     372 ;409:  *   Z_DEFLATE - deflate compression

                     373 ;410:  *

                     374 ;411:  * The entry modified time (modtime) is stored in UTC.

                     375 ;412:  *

                     376 ;413:  * If specified, writestatus will be set to the output of write() when

                     377 ;414:  * a write error occurs, otherwise it will be set to 0.

                     378 ;415:  *

                     379 ;416:  * @return pointer to ZIPentry on success and NULL on error.

                     380 ;417:  ***************************************************************************/

                     381 ;418: ZIPentry *

                     382 ;419: zs_entrybegin ( ZIPstream *zstream, char *name, time_t modtime, int methodID,

                     383 ;420:                 int64_t *writestatus )

                     384 ;421: {

                     385 ;422:   ZIPentry *zentry;

                     386 ;423:   ZIPmethod *method;

                     387 ;424:   int64_t lwritestatus;

                     388 ;425:   int32_t packed;

                     389 ;426:   uint32_t u32;

                     390 ;428:   if ( writestatus )

                     391 ;429:     *writestatus = 0;

                     392 ;431:   if ( ! zstream || ! name )

                     393 ;432:     return NULL;

                     394 ;434:   /* Search for method ID */

                     395 ;435:   method = zstream->firstMethod;

                     396 ;436:   while ( method )

                     397 ;437:     {

                     398 ;438:       if ( method->ID == methodID )

                     399 ;439:         break;

                     400 ;441:       method = method->next;

                     401 ;442:     }

                     402 ;444:   if ( ! method )

                     403 ;445:     {

                     404 ;446:       fprintf (stderr, "Cannot find method ID %d\n", methodID);

                     405 ;447:       return NULL;

                     406 ;448:     }

                     407 ;450:   /* Allocate and initialize new entry */

                     408 ;451:   zentry = (ZIPentry *)allocMemory(sizeof(ZIPentry));

                     409 ;452:   if ( zentry == NULL )

                     410 ;453:     {

                     411 ;454:       fprintf (stderr, "Cannot allocate memory for entry\n");

                     412 ;455:       return NULL;

                     413 ;456:     }

                     414 ;458:   zentry->ZipVersion = 20;  /* Default version for extraction (2.0) */

                     415 ;459:   zentry->GeneralFlag = 0;

                     416 ;460:   u32 = zs_datetime_unixtodos (modtime);


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1201.s
                     417 ;461:   zentry->CompressionMethod = methodID;

                     418 ;462:   zentry->DOSDate = (uint16_t) (u32 >> 16);

                     419 ;463:   zentry->DOSTime = (uint16_t) (u32 & 0xFFFF);

                     420 ;464:   zentry->CRC32 = crc32 (0L, 0, 0); //Z_NULL

                     421 ;465:   zentry->CompressedSize = 0;

                     422 ;466:   zentry->UncompressedSize = 0;

                     423 ;467:   zentry->LocalHeaderOffset = zstream->WriteOffset;

                     424 ;468:   strncpy (zentry->Name, (name)?name:"", ZENTRY_NAME_LENGTH - 1);

                     425 ;469:   zentry->NameLength = (int16_t)strlen (zentry->Name);

                     426 ;470:   zentry->method = method;

                     427 ;471:   zentry->methoddata = NULL;

                     428 ;473:   /* Add new entry to stream list */

                     429 ;474:   if ( ! zstream->FirstEntry )

                     430 ;475:     {

                     431 ;476:       zstream->FirstEntry = zentry;

                     432 ;477:       zstream->LastEntry = zentry;

                     433 ;478:     }

                     434 ;479:   else

                     435 ;480:     {

                     436 ;481:       zstream->LastEntry->next = zentry;

                     437 ;482:       zstream->LastEntry = zentry;

                     438 ;483:     }

                     439 ;485:   zstream->EntryCount++;

                     440 ;487:   /* Set bit to denote streaming */

                     441 ;488:   BIT_SET (zentry->GeneralFlag, 3);

                     442 ;490:   /* Method initialization callback */

                     443 ;491:   if ( zentry->method->init &&

                     444 ;492:        zentry->method->init (zstream, zentry) )

                     445 ;493:     {

                     446 ;494:       fprintf (stderr, "Error with method (%d) init callback\n",

                     447 ;495:                zentry->method->ID);

                     448 ;496:       return NULL;

                     449 ;497:     }

                     450 ;499:   /* Write the Local File Header, with zero'd CRC and sizes (for streaming) */

                     451 ;500:   packed = 0;

                     452 ;501:   zs_packunit32 (zstream, &packed, LOCALHEADERSIG);              /* Data Description signature */

                     453 ;502:   zs_packunit16 (zstream, &packed, zentry->ZipVersion);

                     454 ;503:   zs_packunit16 (zstream, &packed, zentry->GeneralFlag);

                     455 ;504:   zs_packunit16 (zstream, &packed, zentry->CompressionMethod);

                     456 ;505:   zs_packunit16 (zstream, &packed, zentry->DOSTime);             /* DOS file modification time */

                     457 ;506:   zs_packunit16 (zstream, &packed, zentry->DOSDate);             /* DOS file modification date */

                     458 ;507:   zs_packunit32 (zstream, &packed, zentry->CRC32);               /* CRC-32 value of entry */

                     459 ;508:   zs_packunit32 (zstream, &packed, (int32_t)zentry->CompressedSize);      /* Compressed entry size */

                     460 ;509:   zs_packunit32 (zstream, &packed, (int32_t)zentry->UncompressedSize);    /* Uncompressed entry size */

                     461 ;510:   zs_packunit16 (zstream, &packed, zentry->NameLength);          /* File/entry name length */

                     462 ;511:   zs_packunit16 (zstream, &packed, 0);                           /* Extra field length */

                     463 ;512:   /* File/entry name */

                     464 ;513:   memcpy (zstream->buffer+packed, zentry->Name, zentry->NameLength); packed += zentry->NameLength;

                     465 ;515:   lwritestatus = zs_writedata (zstream, zstream->buffer, packed);

                     466 ;516:   if ( lwritestatus != packed )

                     467 ;517:     {

                     468 ;518:       fprintf (stderr, "Error writing ZIP local header: %s\n", strerror(errno));

                     469 ;520:       if ( writestatus )

                     470 ;521:         *writestatus = lwritestatus;

                     471 ;523:       return NULL;

                     472 ;524:     }

                     473 ;526:   return zentry;

                     474 ;527: }  /* End of zs_entrybegin() */

                     475 ;530: /***************************************************************************

                     476 ;531:  * zs_entrydata:

                     477 ;532:  *


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1201.s
                     478 ;533:  * Write a chunk of entry data, of size entrySize, to the output

                     479 ;534:  * stream according to the parameters already set for the stream and

                     480 ;535:  * entry.

                     481 ;536:  *

                     482 ;537:  * When entry is NULL this signals a flush of any internal buffers.

                     483 ;538:  * No further data is expected after this.

                     484 ;539:  *

                     485 ;540:  * If specified, writestatus will be set to the output of write() when

                     486 ;541:  * a write error occurs, otherwise it will be set to 0.

                     487 ;542:  *

                     488 ;543:  * @return pointer to ZIPentry on success and NULL on error.

                     489 ;544:  ***************************************************************************/

                     490 ;545: ZIPentry *

                     491 ;546: zs_entrydata ( ZIPstream *zstream, ZIPentry *zentry, uint8_t *entry,

                     492 ;547:                int64_t entrySize, int64_t *writestatus )

                     493 ;548: {

                     494 ;549:   int32_t writeSize = 0;

                     495 ;550:   int64_t lwritestatus;

                     496 ;551:   int64_t consumed = 0;

                     497 ;552:   int64_t remaining = 0;

                     498 ;554:   if ( writestatus )

                     499 ;555:     *writestatus = 0;

                     500 ;557:   if ( ! zstream || ! zentry )

                     501 ;558:     return NULL;

                     502 ;560:   if ( entry )

                     503 ;561:     {

                     504 ;562:       /* Calculate, or continue calculation of, CRC32 */

                     505 ;563:       zentry->CRC32 = crc32 (zentry->CRC32, (uint8_t *)entry, (int32_t)entrySize);

                     506 ;565:       remaining = entrySize;

                     507 ;566:     }

                     508 ;568:   /* Call method callback for processing data until all input is consumed */

                     509 ;569:   while ( (writeSize = zentry->method->process( zstream, zentry,

                     510 ;570:                                                 entry, remaining, &consumed,

                     511 ;571:                                                 zstream->buffer,

                     512 ;572:                                                 sizeof(zstream->buffer)) ) > 0 )

                     513 ;573:     {

                     514 ;574:       /* Write processed data to stream */

                     515 ;575:       lwritestatus = zs_writedata (zstream, zstream->buffer, writeSize);

                     516 ;576:       if ( lwritestatus != writeSize )

                     517 ;577:         {

                     518 ;578:           fprintf (stderr, "zs_entrydata: Error writing ZIP entry data (%d): %s\n",

                     519 ;579:                    zstream->fd, strerror(errno));

                     520 ;581:           if ( writestatus )

                     521 ;582:             *writestatus = lwritestatus;

                     522 ;584:           return NULL;

                     523 ;585:         }

                     524 ;587:       zentry->CompressedSize += writeSize;

                     525 ;589:       if ( entry )

                     526 ;590:         {

                     527 ;591:           entry += consumed;

                     528 ;592:           remaining -= consumed;

                     529 ;594:           if ( remaining <= 0 )

                     530 ;595:             break;

                     531 ;596:         }

                     532 ;597:     }

                     533 ;599:   if ( writeSize < 0 )

                     534 ;600:     {

                     535 ;601:       fprintf (stderr, "zs_entrydata: Process callback failed\n");

                     536 ;602:       return NULL;

                     537 ;603:     }

                     538 ;605:   if ( entry )


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1201.s
                     539 ;606:     {

                     540 ;607:       zentry->UncompressedSize += entrySize;

                     541 ;608:     }

                     542 ;610:   return zentry;

                     543 ;611: }  /* End of zs_entrydata() */

                     544 ;614: /***************************************************************************

                     545 ;615:  * zs_entryend:

                     546 ;616:  *

                     547 ;617:  * End a streaming entry by writing a Data Description record to

                     548 ;618:  * output stream.

                     549 ;619:  *

                     550 ;620:  * If specified, writestatus will be set to the output of write() when

                     551 ;621:  * a write error occurs, otherwise it will be set to 0.

                     552 ;622:  *

                     553 ;623:  * @return pointer to ZIPentry on success and NULL on error.

                     554 ;624:  ***************************************************************************/

                     555 ;625: ZIPentry *

                     556 ;626: zs_entryend ( ZIPstream *zstream, ZIPentry *zentry, int64_t *writestatus)

                     557 ;627: {

                     558 ;628:   int64_t lwritestatus;

                     559 ;629:   int32_t packed;

                     560 ;631:   if ( writestatus )

                     561 ;632:     *writestatus = 0;

                     562 ;634:   if ( ! zstream || ! zentry )

                     563 ;635:     return NULL;

                     564 ;637:   /* Flush the entry */

                     565 ;638:   if ( ! zs_entrydata (zstream, zentry, NULL, 0, writestatus) )

                     566 ;639:     {

                     567 ;640:       fprintf (stderr, "Error flushing entry (writestatus: %lld)\n",

                     568 ;641:                (long long int)*writestatus);

                     569 ;642:       return NULL;

                     570 ;643:     }

                     571 ;645:   /* Method finish callback */

                     572 ;646:   if ( zentry->method->finish &&

                     573 ;647:        zentry->method->finish (zstream, zentry) )

                     574 ;648:     {

                     575 ;649:       fprintf (stderr, "Error with method (%d) finish callback\n",

                     576 ;650:                zentry->method->ID);

                     577 ;651:       return NULL;

                     578 ;652:     }

                     579 ;654:   /* Write Data Description */

                     580 ;655:   packed = 0;

                     581 ;656:   zs_packunit32 (zstream, &packed, DATADESCRIPTIONSIG);       /* Data Description signature */

                     582 ;657:   zs_packunit32 (zstream, &packed, zentry->CRC32);            /* CRC-32 value of entry */

                     583 ;658:   zs_packunit32 (zstream, &packed, (int32_t)zentry->CompressedSize);   /* Compressed entry size */

                     584 ;659:   zs_packunit32 (zstream, &packed, (int32_t)zentry->UncompressedSize); /* Uncompressed entry size */

                     585 ;661:   lwritestatus = zs_writedata (zstream, zstream->buffer, packed);

                     586 ;662:   if ( lwritestatus != packed )

                     587 ;663:     {

                     588 ;664:       fprintf (stderr, "Error writing streaming ZIP data description: %s\n", strerror(errno));

                     589 ;666:       if ( writestatus )

                     590 ;667:         *writestatus = lwritestatus;

                     591 ;669:       return NULL;

                     592 ;670:     }

                     593 ;672:   return zentry;

                     594 ;673: }  /* End of zs_entryend() */

                     595 ;676: /***************************************************************************

                     596 ;677:  * zs_finish:

                     597 ;678:  *

                     598 ;679:  * Write end of ZIP archive structures (Central Directory, etc.).

                     599 ;680:  *


                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1201.s
                     600 ;681:  * ZIP64 structures will be added to the Central Directory when the

                     601 ;682:  * total length of the archive exceeds 0xFFFFFFFF bytes.

                     602 ;683:  *

                     603 ;684:  * If specified, writestatus will be set to the output of write() when

                     604 ;685:  * a write error occurs, otherwise it will be set to 0.

                     605 ;686:  *

                     606 ;687:  * @return 0 on success and non-zero on error.

                     607 ;688:  ***************************************************************************/

                     608 ;689: int

                     609 ;690: zs_finish ( ZIPstream *zstream, int64_t *writestatus )

                     610 ;691: {

                     611 ;692:   ZIPentry *zentry;

                     612 ;693:   int64_t lwritestatus;

                     613 ;694:   int packed;

                     614 ;696:   uint64_t cdsize;

                     615 ;697:   uint64_t zip64endrecord;

                     616 ;698:   int zip64 = 0;

                     617 ;700:   if ( writestatus )

                     618 ;701:     *writestatus = 0;

                     619 ;703:   if ( ! zstream )

                     620 ;704:     return -1;

                     621 ;706:   /* Store offset of Central Directory */

                     622 ;707:   zstream->CentralDirectoryOffset = zstream->WriteOffset;

                     623 ;709:   zentry = zstream->FirstEntry;

                     624 ;710:   while ( zentry )

                     625 ;711:     {

                     626 ;712:       zip64 = ( zentry->LocalHeaderOffset > 0xFFFFFFFF ) ? 1 : 0;

                     627 ;714:       /* Write Central Directory Header, packing into write buffer and swapped to little-endian order */

                     628 ;715:       packed = 0;

                     629 ;716:       zs_packunit32 (zstream, &packed, CENTRALHEADERSIG);    /* Central File Header signature */

                     630 ;717:       zs_packunit16 (zstream, &packed, 0);                   /* Version made by */

                     631 ;718:       zs_packunit16 (zstream, &packed, zentry->ZipVersion);  /* Version needed to extract */

                     632 ;719:       zs_packunit16 (zstream, &packed, zentry->GeneralFlag); /* General purpose bit flag */

                     633 ;720:       zs_packunit16 (zstream, &packed, zentry->CompressionMethod); /* Compression method */

                     634 ;721:       zs_packunit16 (zstream, &packed, zentry->DOSTime);     /* DOS file modification time */

                     635 ;722:       zs_packunit16 (zstream, &packed, zentry->DOSDate);     /* DOS file modification date */

                     636 ;723:       zs_packunit32 (zstream, &packed, zentry->CRC32);       /* CRC-32 value of entry */

                     637 ;724:       zs_packunit32 (zstream, &packed, (int32_t)zentry->CompressedSize); /* Compressed entry size */

                     638 ;725:       zs_packunit32 (zstream, &packed, (int32_t)zentry->UncompressedSize); /* Uncompressed entry size */

                     639 ;726:       zs_packunit16 (zstream, &packed, zentry->NameLength);  /* File/entry name length */

                     640 ;727:       zs_packunit16 (zstream, &packed, ( zip64 ) ? 12 : 0 ); /* Extra field length, switch for ZIP64 */

                     641 ;728:       zs_packunit16 (zstream, &packed, 0);                   /* File/entry comment length */

                     642 ;729:       zs_packunit16 (zstream, &packed, 0);                   /* Disk number start */

                     643 ;730:       zs_packunit16 (zstream, &packed, 0);                   /* Internal file attributes */

                     644 ;731:       zs_packunit32 (zstream, &packed, 0);                   /* External file attributes */

                     645 ;732:       zs_packunit32 (zstream, &packed, ( zip64 ) ?

                     646 ;733:                      0xFFFFFFFF : (int32_t)zentry->LocalHeaderOffset); /* Relative offset of Local Header */

                     647 ;735:       /* File/entry name */

                     648 ;736:       memcpy (zstream->buffer+packed, zentry->Name, zentry->NameLength);

                     649 ;737:       packed += zentry->NameLength;

                     650 ;739:       if ( zip64 )  /* ZIP64 Extra Field */

                     651 ;740:         {

                     652 ;741:           zs_packunit16 (zstream, &packed, 1);      /* Extra field ID, 1 = ZIP64 */

                     653 ;742:           zs_packunit16 (zstream, &packed, 8);      /* Extra field data length */

                     654 ;743:           zs_packunit64 (zstream, &packed, zentry->LocalHeaderOffset); /* Offset to Local Header */

                     655 ;744:         }

                     656 ;746:       lwritestatus = zs_writedata (zstream, zstream->buffer, packed);

                     657 ;747:       if ( lwritestatus != packed )

                     658 ;748:         {

                     659 ;749:           fprintf (stderr, "Error writing ZIP central directory header: %s\n", strerror(errno));

                     660 ;751:           if ( writestatus )


                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1201.s
                     661 ;752:             *writestatus = lwritestatus;

                     662 ;754:           return -1;

                     663 ;755:         }

                     664 ;757:       zentry = zentry->next;

                     665 ;758:     }

                     666 ;760:   /* Calculate size of Central Directory */

                     667 ;761:   cdsize = zstream->WriteOffset - zstream->CentralDirectoryOffset;

                     668 ;763:   /* Add ZIP64 structures if offset to Central Directory is beyond limit */

                     669 ;764:   if ( zstream->CentralDirectoryOffset > 0xFFFFFFFF )

                     670 ;765:     {

                     671 ;766:       /* Note offset of ZIP64 End of Central Directory Record */

                     672 ;767:       zip64endrecord = zstream->WriteOffset;

                     673 ;769:       /* Write ZIP64 End of Central Directory Record, packing into write buffer and swapped to little-endian order */

                     674 ;770:       packed = 0;

                     675 ;771:       zs_packunit32 (zstream, &packed, ZIP64ENDRECORDSIG); /* ZIP64 End of Central Dir record */

                     676 ;772:       zs_packunit64 (zstream, &packed, 44);                /* Size of this record after this field */

                     677 ;773:       zs_packunit16 (zstream, &packed, 30);                /* Version made by */

                     678 ;774:       zs_packunit16 (zstream, &packed, 45);                /* Version needed to extract */

                     679 ;775:       zs_packunit32 (zstream, &packed, 0);                 /* Number of this disk */

                     680 ;776:       zs_packunit32 (zstream, &packed, 0);                 /* Disk with start of the CD */

                     681 ;777:       zs_packunit64 (zstream, &packed, zstream->EntryCount); /* Number of CD entries on this disk */

                     682 ;778:       zs_packunit64 (zstream, &packed, zstream->EntryCount); /* Total number of CD entries */

                     683 ;779:       zs_packunit64 (zstream, &packed, cdsize);            /* Size of Central Directory */

                     684 ;780:       zs_packunit64 (zstream, &packed, zstream->CentralDirectoryOffset); /* Offset to Central Directory */

                     685 ;782:       lwritestatus = zs_writedata (zstream, zstream->buffer, packed);

                     686 ;783:       if ( lwritestatus != packed )

                     687 ;784:         {

                     688 ;785:           fprintf (stderr, "Error writing ZIP64 end of central directory record: %s\n", strerror(errno));

                     689 ;787:           if ( writestatus )

                     690 ;788:             *writestatus = lwritestatus;

                     691 ;790:           return -1;

                     692 ;791:         }

                     693 ;793:       /* Write ZIP64 End of Central Directory Locator, packing into write buffer and swapped to little-endian order */

                     694 ;794:       packed = 0;

                     695 ;795:       zs_packunit32 (zstream, &packed, ZIP64ENDLOCATORSIG); /* ZIP64 End of Central Dir Locator */

                     696 ;796:       zs_packunit32 (zstream, &packed, 0);                  /* Number of disk w/ ZIP64 End of CD */

                     697 ;797:       zs_packunit64 (zstream, &packed, zip64endrecord);     /* Offset to ZIP64 End of CD */

                     698 ;798:       zs_packunit32 (zstream, &packed, 1);                  /* Total number of disks */

                     699 ;800:       lwritestatus = zs_writedata (zstream, zstream->buffer, packed);

                     700 ;801:       if ( lwritestatus != packed )

                     701 ;802:         {

                     702 ;803:           fprintf (stderr, "Error writing ZIP64 end of central directory locator: %s\n", strerror(errno));

                     703 ;805:           if ( writestatus )

                     704 ;806:             *writestatus = lwritestatus;

                     705 ;808:           return -1;

                     706 ;809:         }

                     707 ;810:     }

                     708 ;812:   /* Write End of Central Directory Record, packing into write buffer and swapped to little-endian order */

                     709 ;813:   packed = 0;

                     710 ;814:   zs_packunit32 (zstream, &packed, ENDHEADERSIG);     /* End of Central Dir signature */

                     711 ;815:   zs_packunit16 (zstream, &packed, 0);                /* Number of this disk */

                     712 ;816:   zs_packunit16 (zstream, &packed, 0);                /* Number of disk with CD */

                     713 ;817:   zs_packunit16 (zstream, &packed, zstream->EntryCount); /* Number of entries in CD this disk */

                     714 ;818:   zs_packunit16 (zstream, &packed, zstream->EntryCount); /* Number of entries in CD */

                     715 ;819:   zs_packunit32 (zstream, &packed, (int32_t)cdsize);           /* Size of Central Directory */

                     716 ;820:   zs_packunit32 (zstream, &packed, (zstream->CentralDirectoryOffset > 0xFFFFFFFF) ?

                     717 ;821:                  0xFFFFFFFF : (int32_t)zstream->CentralDirectoryOffset); /* Offset to start of CD */

                     718 ;822:   zs_packunit16 (zstream, &packed, 0);                /* ZIP file comment length */

                     719 ;824:   lwritestatus = zs_writedata (zstream, zstream->buffer, packed);

                     720 ;825:   if ( lwritestatus != packed )

                     721 ;826:     {


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1201.s
                     722 ;827:       fprintf (stderr, "Error writing end of central directory record: %s\n", strerror(errno));

                     723 ;829:       if ( writestatus )

                     724 ;830:         *writestatus = lwritestatus;

                     725 ;832:       return -1;

                     726 ;833:     }

                     727 ;835:   return 0;

                     728 ;836: }  /* End of zs_finish() */

                     729 ;839: /***************************************************************************

                     730 ;840:  * zs_writedata:

                     731 ;841:  *

                     732 ;842:  * Write data to output descriptor in blocks of ZS_WRITE_SIZE bytes.

                     733 ;843:  *

                     734 ;844:  * The ZIPstream.WriteOffset value will be incremented accordingly.

                     735 ;845:  *

                     736 ;846:  * @return number of bytes written on success and return value of write() on error.

                     737 ;847:  ***************************************************************************/

                     738 ;848: static int64_t

                     739 ;849: zs_writedata ( ZIPstream *zstream, uint8_t *writeBuffer, int64_t writeBufferSize )

                     740 ;850: {

                     741 ;851:   int64_t lwritestatus;

                     742 ;852:   size_t writeLength;

                     743 ;853:   int64_t written;

                     744 ;855:   if ( ! zstream || ! writeBuffer )

                     745 ;856:     return 0;

                     746 ;858:   /* Write blocks of ZS_WRITE_SIZE until done */

                     747 ;859:   written = 0;

                     748 ;860:   while ( written < writeBufferSize )

                     749 ;861:     {

                     750 ;862:       writeLength = (int32_t)( (writeBufferSize - (int32_t)written) > ZS_WRITE_SIZE ) ?

                     751 ;863:         ZS_WRITE_SIZE : ((int32_t)writeBufferSize - (int32_t)written);

                     752 ;865:       lwritestatus = zs_user_writeToStream (zstream->fd, writeBuffer+written, writeLength);

                     753 ;867:       if ( lwritestatus <= 0 )

                     754 ;868:         {

                     755 ;869:           return lwritestatus;

                     756 ;870:         }

                     757 ;872:       zstream->WriteOffset += lwritestatus;

                     758 ;873:       written += lwritestatus;

                     759 ;874:     }

                     760 ;876:   return written;

                     761 ;877: }  /* End of zs_writedata() */

                     762 ;880: /* DOS time start date is January 1, 1980 */

                     763 ;881: #define DOSTIME_STARTDATE  0x00210000L

                     764 ;883: /***************************************************************************

                     765 ;884:  * zs_datetime_unixtodos:

                     766 ;885:  *

                     767 ;886:  * Convert Unix time_t to 4 byte DOS date and time.

                     768 ;887:  *

                     769 ;888:  * Routine adapted from sources:

                     770 ;889:  *  Copyright (C) 2006 Michael Liebscher <<EMAIL>>

                     771 ;890:  *

                     772 ;891:  * @return converted 4-byte quantity on success and 0 on error.

                     773 ;892:  ***************************************************************************/

                     774 ;893: static uint32_t zs_datetime_unixtodos ( time_t t )

                     775 

                     776 ;901:            (((uint32_t)(s.tm_year) - 1980) << 25) |

                     777 ;902:            ((uint32_t)(s.tm_mon) << 21) |

                     778 ;903:            ((uint32_t)(s.tm_mday) << 16) |

                     779 ;904:            ((uint32_t)(s.tm_hour) << 11) |

                     780 ;905:            ((uint32_t)(s.tm_min) << 5) |

                     781 ;906:            ((uint32_t)(s.tm_sec) >> 1) );

                     782 ;907: }


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1201.s
                     783 

                     784 	.text

                     785 	.align	4

                     786 allocMemory:

00000000 e92d4030    787 	stmfd	[sp]!,{r4-r5,lr}

00000004 e1a05000    788 	mov	r5,r0

00000008 eb000000*   789 	bl	OscFiles_malloc

0000000c e1b04000    790 	movs	r4,r0

00000010 11a02005    791 	movne	r2,r5

00000014 13a01000    792 	movne	r1,0

00000018 1b000000*   793 	blne	memset

0000001c e1a00004    794 	mov	r0,r4

00000020 e8bd4030    795 	ldmfd	[sp]!,{r4-r5,lr}

00000024 e12fff1e*   796 	ret	

                     797 	.endf	allocMemory

                     798 	.align	4

                     799 ;p	r4	local

                     800 

                     801 ;elsize	r5	param

                     802 

                     803 	.section ".bss","awb"

                     804 .L67:

                     805 	.data

                     806 	.text

                     807 

                     808 

                     809 	.align	4

                     810 	.align	4

                     811 freeMemory::

00000028 ea000000*   812 	b	OscFiles_free

                     813 	.endf	freeMemory

                     814 	.align	4

                     815 

                     816 ;p	none	param

                     817 

                     818 	.section ".bss","awb"

                     819 .L110:

                     820 	.data

                     821 	.text

                     822 

                     823 

                     824 	.align	4

                     825 	.align	4

                     826 zs_store_process:

0000002c e1a0c00d    827 	mov	r12,sp

00000030 e92d0008    828 	stmfd	[sp]!,{r3}

00000034 e92d5730    829 	stmfd	[sp]!,{r4-r5,r8-r10,r12,lr}

00000038 e1b0c002    830 	movs	r12,r2

0000003c e59d4024    831 	ldr	r4,[sp,36]

00000040 e59d5028    832 	ldr	r5,[sp,40]

00000044 0a000004    833 	beq	.L120

00000048 e59d201c    834 	ldr	r2,[sp,28]

0000004c e59d1020    835 	ldr	r1,[sp,32]

00000050 e3520001    836 	cmp	r2,1

00000054 e2d10000    837 	sbcs	r0,r1,0

00000058 aa000001    838 	bge	.L119

                     839 .L120:

0000005c e3a00000    840 	mov	r0,0

00000060 ea000012    841 	b	.L117

                     842 .L119:

00000064 e1a00002    843 	mov	r0,r2


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1201.s
00000068 e59d202c    844 	ldr	r2,[sp,44]

0000006c e59d3030    845 	ldr	r3,[sp,48]

00000070 e1500002    846 	cmp	r0,r2

00000074 e0d10003    847 	sbcs	r0,r1,r3

00000078 b28d001c    848 	addlt	r0,sp,28

0000007c b8900600    849 	ldmltfd	[r0],{r9-r10}

00000080 b28d002c    850 	addlt	r0,sp,44

00000084 b8800600    851 	stmltea	[r0],{r9-r10}

00000088 b59d202c    852 	ldrlt	r2,[sp,44]

0000008c e1a0100c    853 	mov	r1,r12

00000090 e1a00005    854 	mov	r0,r5

00000094 eb000000*   855 	bl	memcpy

00000098 e3540000    856 	cmp	r4,0

0000009c 159d002c    857 	ldrne	r0,[sp,44]

000000a0 15840000    858 	strne	r0,[r4,0]

000000a4 159d0030    859 	ldrne	r0,[sp,48]

000000a8 15840004    860 	strne	r0,[r4,4]

000000ac e59d002c    861 	ldr	r0,[sp,44]

                     862 .L117:

000000b0 e89d6730    863 	ldmfd	[sp],{r4-r5,r8-r10,sp-lr}

000000b4 e12fff1e*   864 	ret	

                     865 	.endf	zs_store_process

                     866 	.align	4

                     867 

                     868 ;zstream	none	param

                     869 ;zentry	none	param

                     870 ;entry	r12	param

                     871 ;entrySize	[sp,28]	param

                     872 ;entryConsumed	r4	param

                     873 ;writeBuffer	r5	param

                     874 ;writeBufferSize	[sp,44]	param

                     875 

                     876 	.section ".bss","awb"

                     877 .L188:

                     878 	.data

                     879 	.text

                     880 

                     881 

                     882 	.align	4

                     883 	.align	4

                     884 zs_registermethod::

000000b8 e92d44f0    885 	stmfd	[sp]!,{r4-r7,r10,lr}

000000bc e1a04001    886 	mov	r4,r1

000000c0 e1a07002    887 	mov	r7,r2

000000c4 e1b06003    888 	movs	r6,r3

000000c8 e59da018    889 	ldr	r10,[sp,24]

000000cc e1a05000    890 	mov	r5,r0

000000d0 e5950020    891 	ldr	r0,[r5,32]

000000d4 0a00000a    892 	beq	.L226

000000d8 e3500000    893 	cmp	r0,0

000000dc 0a000005    894 	beq	.L220

                     895 .L221:

000000e0 e5901000    896 	ldr	r1,[r0]

000000e4 e1510004    897 	cmp	r1,r4

000000e8 0a000005    898 	beq	.L226

000000ec e5900010    899 	ldr	r0,[r0,16]

000000f0 e3500000    900 	cmp	r0,0

000000f4 1afffff9    901 	bne	.L221

                     902 .L220:

000000f8 e3a00014    903 	mov	r0,20

000000fc ebffffbf*   904 	bl	allocMemory


                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1201.s
00000100 e3500000    905 	cmp	r0,0

                     906 .L226:

00000104 03a00000    907 	moveq	r0,0

00000108 0a000006    908 	beq	.L214

                     909 .L225:

0000010c e1a0c006    910 	mov	r12,r6

00000110 e1a06007    911 	mov	r6,r7

00000114 e1a0700c    912 	mov	r7,r12

00000118 e88004d0    913 	stmea	[r0],{r4,r6-r7,r10}

0000011c e5951020    914 	ldr	r1,[r5,32]

00000120 e5801010    915 	str	r1,[r0,16]

00000124 e5850020    916 	str	r0,[r5,32]

                     917 .L214:

00000128 e8bd84f0    918 	ldmfd	[sp]!,{r4-r7,r10,pc}

                     919 	.endf	zs_registermethod

                     920 	.align	4

                     921 ;method	r0	local

                     922 

                     923 ;zs	r5	param

                     924 ;methodID	r4	param

                     925 ;init	r7	param

                     926 ;process	r6	param

                     927 ;finish	r10	param

                     928 

                     929 	.section ".bss","awb"

                     930 .L324:

                     931 	.data

                     932 	.text

                     933 

                     934 

                     935 	.align	4

                     936 	.align	4

                     937 zs_init::

0000012c e92d4070    938 	stmfd	[sp]!,{r4-r6,lr}

00000130 e24dd004    939 	sub	sp,sp,4

00000134 e1a06000    940 	mov	r6,r0

00000138 e1b04001    941 	movs	r4,r1

0000013c 1a000005    942 	bne	.L346

00000140 e3a00c40    943 	mov	r0,1<<14

00000144 e2800024    944 	add	r0,r0,36

00000148 ebffffac*   945 	bl	allocMemory

0000014c e1b04000    946 	movs	r4,r0

00000150 1a000012    947 	bne	.L355

00000154 ea000023    948 	b	.L358

                     949 .L346:

00000158 e5945018    950 	ldr	r5,[r4,24]

0000015c e3550000    951 	cmp	r5,0

00000160 0a000004    952 	beq	.L350

                     953 .L351:

00000164 e1a00005    954 	mov	r0,r5

00000168 e5955134    955 	ldr	r5,[r5,308]

0000016c ebffffad*   956 	bl	freeMemory

00000170 e3550000    957 	cmp	r5,0

00000174 1afffffa    958 	bne	.L351

                     959 .L350:

00000178 e5945020    960 	ldr	r5,[r4,32]

0000017c e3550000    961 	cmp	r5,0

00000180 0a000004    962 	beq	.L348

                     963 .L354:

00000184 e1a00005    964 	mov	r0,r5

00000188 e5955010    965 	ldr	r5,[r5,16]


                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1201.s
0000018c ebffffa5*   966 	bl	freeMemory

00000190 e3550000    967 	cmp	r5,0

00000194 1afffffa    968 	bne	.L354

                     969 .L348:

00000198 e3540000    970 	cmp	r4,0

0000019c 0a000011    971 	beq	.L358

                     972 .L355:

000001a0 e3a02c40    973 	mov	r2,1<<14

000001a4 e2822024    974 	add	r2,r2,36

000001a8 e1a00004    975 	mov	r0,r4

000001ac e3a01000    976 	mov	r1,0

000001b0 eb000000*   977 	bl	memset

000001b4 e5846000    978 	str	r6,[r4]

000001b8 e3a02000    979 	mov	r2,0

000001bc e58d2000    980 	str	r2,[sp]

000001c0 e59f3d80*   981 	ldr	r3,.L504

000001c4 e1a00004    982 	mov	r0,r4

000001c8 e1a01002    983 	mov	r1,r2

000001cc ebffffb9*   984 	bl	zs_registermethod

000001d0 e3500000    985 	cmp	r0,0

000001d4 1a000003    986 	bne	.L358

000001d8 e1a00004    987 	mov	r0,r4

000001dc ebffff91*   988 	bl	freeMemory

000001e0 e3a00000    989 	mov	r0,0

000001e4 ea000000    990 	b	.L344

                     991 .L358:

000001e8 e1a00004    992 	mov	r0,r4

                     993 .L344:

000001ec e28dd004    994 	add	sp,sp,4

000001f0 e8bd8070    995 	ldmfd	[sp]!,{r4-r6,pc}

                     996 	.endf	zs_init

                     997 	.align	4

                     998 ;zentry	r5	local

                     999 ;method	r5	local

                    1000 

                    1001 ;fd	r6	param

                    1002 ;zs	r4	param

                    1003 

                    1004 	.section ".bss","awb"

                    1005 .L477:

                    1006 	.data

                    1007 	.text

                    1008 

                    1009 

                    1010 	.align	4

                    1011 	.align	4

                    1012 zs_free::

000001f4 e92d4030   1013 	stmfd	[sp]!,{r4-r5,lr}

000001f8 e1b05000   1014 	movs	r5,r0

000001fc 0a000012   1015 	beq	.L505

00000200 e5954018   1016 	ldr	r4,[r5,24]

00000204 e3540000   1017 	cmp	r4,0

00000208 0a000004   1018 	beq	.L511

                    1019 .L512:

0000020c e1a00004   1020 	mov	r0,r4

00000210 e5944134   1021 	ldr	r4,[r4,308]

00000214 ebffff83*  1022 	bl	freeMemory

00000218 e3540000   1023 	cmp	r4,0

0000021c 1afffffa   1024 	bne	.L512

                    1025 .L511:

00000220 e5954020   1026 	ldr	r4,[r5,32]


                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1201.s
00000224 e3540000   1027 	cmp	r4,0

00000228 0a000004   1028 	beq	.L514

                    1029 .L515:

0000022c e1a00004   1030 	mov	r0,r4

00000230 e5944010   1031 	ldr	r4,[r4,16]

00000234 ebffff7b*  1032 	bl	freeMemory

00000238 e3540000   1033 	cmp	r4,0

0000023c 1afffffa   1034 	bne	.L515

                    1035 .L514:

00000240 e1a00005   1036 	mov	r0,r5

00000244 e8bd4030   1037 	ldmfd	[sp]!,{r4-r5,lr}

00000248 eaffff76*  1038 	b	freeMemory

                    1039 .L505:

0000024c e8bd8030   1040 	ldmfd	[sp]!,{r4-r5,pc}

                    1041 	.endf	zs_free

                    1042 	.align	4

                    1043 ;zentry	r4	local

                    1044 ;method	r4	local

                    1045 

                    1046 ;zs	r5	param

                    1047 

                    1048 	.section ".bss","awb"

                    1049 .L582:

                    1050 	.data

                    1051 	.text

                    1052 

                    1053 

                    1054 	.align	4

                    1055 	.align	4

                    1056 zs_writeentry::

00000250 e92d48f0   1057 	stmfd	[sp]!,{r4-r7,fp,lr}

00000254 e24dd010   1058 	sub	sp,sp,16

00000258 e58d2008   1059 	str	r2,[sp,8]

0000025c e58d300c   1060 	str	r3,[sp,12]

00000260 e59d7028   1061 	ldr	r7,[sp,40]

00000264 e59dc02c   1062 	ldr	r12,[sp,44]

00000268 e59d6030   1063 	ldr	r6,[sp,48]

0000026c e1a0b001   1064 	mov	fp,r1

00000270 e59d4034   1065 	ldr	r4,[sp,52]

00000274 e1a05000   1066 	mov	r5,r0

00000278 e3540000   1067 	cmp	r4,0

0000027c 13a00000   1068 	movne	r0,0

00000280 11a01000   1069 	movne	r1,r0

00000284 18840003   1070 	stmneea	[r4],{r0-r1}

00000288 e3550000   1071 	cmp	r5,0

0000028c 0a000020   1072 	beq	.L618

00000290 e3a01000   1073 	mov	r1,0

00000294 e3e00000   1074 	mvn	r0,0

00000298 e59d2008   1075 	ldr	r2,[sp,8]

0000029c e59d300c   1076 	ldr	r3,[sp,12]

000002a0 e1500002   1077 	cmp	r0,r2

000002a4 e0d10003   1078 	sbcs	r0,r1,r3

000002a8 ba000019   1079 	blt	.L618

000002ac e58d4000   1080 	str	r4,[sp]

000002b0 e1a03006   1081 	mov	r3,r6

000002b4 e1a0200c   1082 	mov	r2,r12

000002b8 e1a01007   1083 	mov	r1,r7

000002bc e1a00005   1084 	mov	r0,r5

000002c0 eb000016*  1085 	bl	zs_entrybegin

000002c4 e1b06000   1086 	movs	r6,r0

000002c8 0a000011   1087 	beq	.L618


                                                                      Page 19
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1201.s
000002cc e24dd004   1088 	sub	sp,sp,4

000002d0 e59d2010   1089 	ldr	r2,[sp,16]

000002d4 e59d100c   1090 	ldr	r1,[sp,12]

000002d8 e88d0016   1091 	stmea	[sp],{r1-r2,r4}

000002dc e8bd0008   1092 	ldmfd	[sp]!,{r3}

000002e0 e1a0200b   1093 	mov	r2,fp

000002e4 e1a01006   1094 	mov	r1,r6

000002e8 e1a00005   1095 	mov	r0,r5

000002ec eb0000c8*  1096 	bl	zs_entrydata

000002f0 e3500000   1097 	cmp	r0,0

000002f4 0a000006   1098 	beq	.L618

000002f8 e1a02004   1099 	mov	r2,r4

000002fc e1a01006   1100 	mov	r1,r6

00000300 e1a00005   1101 	mov	r0,r5

00000304 eb00012b*  1102 	bl	zs_entryend

00000308 e3500000   1103 	cmp	r0,0

0000030c 11a00006   1104 	movne	r0,r6

00000310 1a000000   1105 	bne	.L601

                    1106 .L618:

00000314 e3a00000   1107 	mov	r0,0

                    1108 .L601:

00000318 e28dd010   1109 	add	sp,sp,16

0000031c e8bd88f0   1110 	ldmfd	[sp]!,{r4-r7,fp,pc}

                    1111 	.endf	zs_writeentry

                    1112 	.align	4

                    1113 ;zentry	r6	local

                    1114 

                    1115 ;zstream	r5	param

                    1116 ;entry	fp	param

                    1117 ;entrySize	[sp,8]	param

                    1118 ;name	r7	param

                    1119 ;modtime	r12	param

                    1120 ;methodID	r6	param

                    1121 ;writestatus	r4	param

                    1122 

                    1123 	.section ".bss","awb"

                    1124 .L712:

                    1125 	.data

                    1126 	.text

                    1127 

                    1128 

                    1129 	.align	4

                    1130 	.align	4

                    1131 zs_entrybegin::

00000320 e92d48f6   1132 	stmfd	[sp]!,{r1-r2,r4-r7,fp,lr}

00000324 e24dd02c   1133 	sub	sp,sp,44

00000328 e58d2030   1134 	str	r2,[sp,48]

0000032c e1a07003   1135 	mov	r7,r3

00000330 e58d102c   1136 	str	r1,[sp,44]

00000334 e59db04c   1137 	ldr	fp,[sp,76]

00000338 e1a06000   1138 	mov	r6,r0

0000033c e35b0000   1139 	cmp	fp,0

00000340 13a00000   1140 	movne	r0,0

00000344 11a01000   1141 	movne	r1,r0

00000348 188b0003   1142 	stmneea	[fp],{r0-r1}

0000034c e3560000   1143 	cmp	r6,0

00000350 159d002c   1144 	ldrne	r0,[sp,44]

00000354 13500000   1145 	cmpne	r0,0

00000358 15965020   1146 	ldrne	r5,[r6,32]

0000035c 13550000   1147 	cmpne	r5,0

00000360 0a0000a8   1148 	beq	.L782


                                                                      Page 20
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1201.s
                    1149 .L752:

00000364 e5950000   1150 	ldr	r0,[r5]

00000368 e1500007   1151 	cmp	r0,r7

0000036c 0a000003   1152 	beq	.L757

00000370 e5955010   1153 	ldr	r5,[r5,16]

00000374 e3550000   1154 	cmp	r5,0

00000378 1afffff9   1155 	bne	.L752

0000037c ea0000a1   1156 	b	.L782

                    1157 .L757:

00000380 e3a00f4e   1158 	mov	r0,0x0138

00000384 ebffff1d*  1159 	bl	allocMemory

00000388 e1b04000   1160 	movs	r4,r0

0000038c 0a00009d   1161 	beq	.L782

00000390 e3a00014   1162 	mov	r0,20

00000394 e1c400b0   1163 	strh	r0,[r4]

00000398 e3a00000   1164 	mov	r0,0

0000039c e1c400b2   1165 	strh	r0,[r4,2]

000003a0 e59d0030   1166 	ldr	r0,[sp,48]

000003a4 e28d1004   1167 	add	r1,sp,4

000003a8 e58d0004   1168 	str	r0,[sp,4]

                    1169 ;894: {

                    1170 

                    1171 ;895:   struct tm s;

                    1172 ;896:   TimeTools_gmtime32(&s, (__time32_t*)&t);

                    1173 

000003ac e28d0008   1174 	add	r0,sp,8

000003b0 eb000000*  1175 	bl	TimeTools_gmtime32

                    1176 ;897:   s.tm_year += 1900;

                    1177 

000003b4 e59d001c   1178 	ldr	r0,[sp,28]

000003b8 e3a0ce70   1179 	mov	r12,7<<8

000003bc e2803e70   1180 	add	r3,r0,7<<8

000003c0 e283006c   1181 	add	r0,r3,108

000003c4 e59d3018   1182 	ldr	r3,[sp,24]

000003c8 e58d001c   1183 	str	r0,[sp,28]

                    1184 ;898:   s.tm_mon += 1;

                    1185 

000003cc e2833001   1186 	add	r3,r3,1

000003d0 e58d3018   1187 	str	r3,[sp,24]

                    1188 ;900:   return ( ((s.tm_year) < 1980) ? DOSTIME_STARTDATE :

                    1189 

000003d4 e28cc0bc   1190 	add	r12,r12,188

000003d8 e150000c   1191 	cmp	r0,r12

000003dc b3a00984   1192 	movlt	r0,33<<16

000003e0 ba000008   1193 	blt	.L772

000003e4 e1a00c80   1194 	mov	r0,r0 lsl 25

000003e8 e2400478   1195 	sub	r0,r0,15<<27

000003ec e1800a83   1196 	orr	r0,r0,r3 lsl 21

000003f0 e28d3008   1197 	add	r3,sp,8

000003f4 e8935006   1198 	ldmfd	[r3],{r1-r2,r12,lr}

000003f8 e180080e   1199 	orr	r0,r0,lr lsl 16

000003fc e180058c   1200 	orr	r0,r0,r12 lsl 11

00000400 e1800282   1201 	orr	r0,r0,r2 lsl 5

00000404 e18000a1   1202 	orr	r0,r0,r1 lsr 1

                    1203 .L772:

00000408 e1c470b4   1204 	strh	r7,[r4,4]

0000040c e1a03820   1205 	mov	r3,r0 lsr 16

00000410 e1c430b6   1206 	strh	r3,[r4,6]

00000414 e3a02000   1207 	mov	r2,0

00000418 e1a01002   1208 	mov	r1,r2

0000041c e1c400b8   1209 	strh	r0,[r4,8]


                                                                      Page 21
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1201.s
00000420 e1a00002   1210 	mov	r0,r2

00000424 eb000000*  1211 	bl	crc32

00000428 e3a02000   1212 	mov	r2,0

0000042c e1a03002   1213 	mov	r3,r2

00000430 e1a07002   1214 	mov	r7,r2

00000434 e1a0c002   1215 	mov	r12,r2

00000438 e1a01000   1216 	mov	r1,r0

0000043c e2840008   1217 	add	r0,r4,8

00000440 e980108e   1218 	stmfa	[r0],{r1-r3,r7,r12}

00000444 e9960009   1219 	ldmed	[r6],{r0,r3}

00000448 e5840020   1220 	str	r0,[r4,32]

0000044c e5843024   1221 	str	r3,[r4,36]

00000450 e59d102c   1222 	ldr	r1,[sp,44]

00000454 e284002a   1223 	add	r0,r4,42

00000458 e3a020ff   1224 	mov	r2,255

0000045c eb000000*  1225 	bl	strncpy

00000460 e284002a   1226 	add	r0,r4,42

00000464 eb000000*  1227 	bl	strlen

00000468 e1c402b8   1228 	strh	r0,[r4,40]

0000046c e3a00000   1229 	mov	r0,0

00000470 e5840130   1230 	str	r0,[r4,304]

00000474 e5960018   1231 	ldr	r0,[r6,24]

00000478 e584512c   1232 	str	r5,[r4,300]

0000047c e3500000   1233 	cmp	r0,0

00000480 1a00000b   1234 	bne	.L773

00000484 e5960014   1235 	ldr	r0,[r6,20]

00000488 e5864018   1236 	str	r4,[r6,24]

0000048c e2800001   1237 	add	r0,r0,1

00000490 e5860014   1238 	str	r0,[r6,20]

00000494 e1d400b2   1239 	ldrh	r0,[r4,2]

00000498 e586401c   1240 	str	r4,[r6,28]

0000049c e3800008   1241 	orr	r0,r0,8

000004a0 e595c004   1242 	ldr	r12,[r5,4]

000004a4 e1c400b2   1243 	strh	r0,[r4,2]

000004a8 e35c0000   1244 	cmp	r12,0

000004ac 0a000012   1245 	beq	.L776

000004b0 ea00000b   1246 	b	.L778

                    1247 .L773:

000004b4 e596001c   1248 	ldr	r0,[r6,28]

000004b8 e595c004   1249 	ldr	r12,[r5,4]

000004bc e5804134   1250 	str	r4,[r0,308]

000004c0 e5960014   1251 	ldr	r0,[r6,20]

000004c4 e586401c   1252 	str	r4,[r6,28]

000004c8 e2800001   1253 	add	r0,r0,1

000004cc e5860014   1254 	str	r0,[r6,20]

000004d0 e1d400b2   1255 	ldrh	r0,[r4,2]

000004d4 e35c0000   1256 	cmp	r12,0

000004d8 e3800008   1257 	orr	r0,r0,8

000004dc e1c400b2   1258 	strh	r0,[r4,2]

000004e0 0a000005   1259 	beq	.L776

                    1260 .L778:

000004e4 e1a01004   1261 	mov	r1,r4

000004e8 e1a00006   1262 	mov	r0,r6

000004ec e1a0e00f   1263 	mov	lr,pc

000004f0 e12fff1c*  1264 	bx	r12

000004f4 e3500000   1265 	cmp	r0,0

000004f8 1a000042   1266 	bne	.L782

                    1267 .L776:

000004fc e3a07000   1268 	mov	r7,0

00000500 e58d7000   1269 	str	r7,[sp]

00000504 e59f2a40*  1270 	ldr	r2,.L1156


                                                                      Page 22
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1201.s
00000508 e1a0100d   1271 	mov	r1,sp

0000050c e1a00006   1272 	mov	r0,r6

00000510 eb00027b*  1273 	bl	zs_packunit32

00000514 e1d420b0   1274 	ldrh	r2,[r4]

00000518 e1a0100d   1275 	mov	r1,sp

0000051c e1a00006   1276 	mov	r0,r6

00000520 eb000263*  1277 	bl	zs_packunit16

00000524 e1d420b2   1278 	ldrh	r2,[r4,2]

00000528 e1a0100d   1279 	mov	r1,sp

0000052c e1a00006   1280 	mov	r0,r6

00000530 eb00025f*  1281 	bl	zs_packunit16

00000534 e1d420b4   1282 	ldrh	r2,[r4,4]

00000538 e1a0100d   1283 	mov	r1,sp

0000053c e1a00006   1284 	mov	r0,r6

00000540 eb00025b*  1285 	bl	zs_packunit16

00000544 e1d420b8   1286 	ldrh	r2,[r4,8]

00000548 e1a0100d   1287 	mov	r1,sp

0000054c e1a00006   1288 	mov	r0,r6

00000550 eb000257*  1289 	bl	zs_packunit16

00000554 e1d420b6   1290 	ldrh	r2,[r4,6]

00000558 e1a0100d   1291 	mov	r1,sp

0000055c e1a00006   1292 	mov	r0,r6

00000560 eb000253*  1293 	bl	zs_packunit16

00000564 e594200c   1294 	ldr	r2,[r4,12]

00000568 e1a0100d   1295 	mov	r1,sp

0000056c e1a00006   1296 	mov	r0,r6

00000570 eb000263*  1297 	bl	zs_packunit32

00000574 e5942010   1298 	ldr	r2,[r4,16]

00000578 e1a0100d   1299 	mov	r1,sp

0000057c e1a00006   1300 	mov	r0,r6

00000580 eb00025f*  1301 	bl	zs_packunit32

00000584 e5942018   1302 	ldr	r2,[r4,24]

00000588 e1a0100d   1303 	mov	r1,sp

0000058c e1a00006   1304 	mov	r0,r6

00000590 eb00025b*  1305 	bl	zs_packunit32

00000594 e1d422b8   1306 	ldrh	r2,[r4,40]

00000598 e1a0100d   1307 	mov	r1,sp

0000059c e1a00006   1308 	mov	r0,r6

000005a0 eb000243*  1309 	bl	zs_packunit16

000005a4 e1a0100d   1310 	mov	r1,sp

000005a8 e1a00006   1311 	mov	r0,r6

000005ac e1a02007   1312 	mov	r2,r7

000005b0 eb00023f*  1313 	bl	zs_packunit16

000005b4 e1d422b8   1314 	ldrh	r2,[r4,40]

000005b8 e284102a   1315 	add	r1,r4,42

000005bc e59d0000   1316 	ldr	r0,[sp]

000005c0 e2865024   1317 	add	r5,r6,36

000005c4 e0800005   1318 	add	r0,r0,r5

000005c8 eb000000*  1319 	bl	memcpy

000005cc e1d432b8   1320 	ldrh	r3,[r4,40]

000005d0 e59d0000   1321 	ldr	r0,[sp]

000005d4 e1a01005   1322 	mov	r1,r5

000005d8 e0802003   1323 	add	r2,r0,r3

000005dc e58d2000   1324 	str	r2,[sp]

000005e0 e1a03fc2   1325 	mov	r3,r2 asr 31

000005e4 e1a00006   1326 	mov	r0,r6

000005e8 eb0001d0*  1327 	bl	zs_writedata

000005ec e59d3000   1328 	ldr	r3,[sp]

000005f0 e1510fc3   1329 	cmp	r1,r3 asr 31

000005f4 01500003   1330 	cmpeq	r0,r3

000005f8 01a00004   1331 	moveq	r0,r4


                                                                      Page 23
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1201.s
000005fc 0a000002   1332 	beq	.L742

00000600 e35b0000   1333 	cmp	fp,0

00000604 188b0003   1334 	stmneea	[fp],{r0-r1}

                    1335 .L782:

00000608 e3a00000   1336 	mov	r0,0

                    1337 .L742:

0000060c e28dd02c   1338 	add	sp,sp,44

00000610 e8bd88f6   1339 	ldmfd	[sp]!,{r1-r2,r4-r7,fp,pc}

                    1340 	.endf	zs_entrybegin

                    1341 	.align	4

                    1342 ;zentry	r4	local

                    1343 ;method	r5	local

                    1344 ;lwritestatus	r0	local

                    1345 ;packed	[sp]	local

                    1346 ;t	[sp,4]	local

                    1347 ;s	[sp,8]	local

                    1348 

                    1349 ;zstream	r6	param

                    1350 ;name	[sp,44]	param

                    1351 ;modtime	[sp,48]	param

                    1352 ;methodID	r7	param

                    1353 ;writestatus	fp	param

                    1354 

                    1355 	.section ".bss","awb"

                    1356 .L1096:

                    1357 	.data

                    1358 	.text

                    1359 

                    1360 

                    1361 	.align	4

                    1362 	.align	4

                    1363 zs_entrydata::

00000614 e1a0c00d   1364 	mov	r12,sp

00000618 e92d0008   1365 	stmfd	[sp]!,{r3}

0000061c e92d5ff0   1366 	stmfd	[sp]!,{r4-r12,lr}

00000620 e1a07001   1367 	mov	r7,r1

00000624 e3a01000   1368 	mov	r1,0

00000628 e1a03001   1369 	mov	r3,r1

0000062c e1a0c001   1370 	mov	r12,r1

00000630 e1a0b002   1371 	mov	fp,r2

00000634 e1a02001   1372 	mov	r2,r1

00000638 e24dd02c   1373 	sub	sp,sp,44

0000063c e28d5018   1374 	add	r5,sp,24

00000640 e885100f   1375 	stmea	[r5],{r0-r3,r12}

00000644 e59d005c   1376 	ldr	r0,[sp,92]

00000648 e3500000   1377 	cmp	r0,0

0000064c 13a02000   1378 	movne	r2,0

00000650 18800006   1379 	stmneea	[r0],{r1-r2}

00000654 e59d0018   1380 	ldr	r0,[sp,24]

00000658 e3500000   1381 	cmp	r0,0

0000065c 13570000   1382 	cmpne	r7,0

00000660 0a000045   1383 	beq	.L1182

00000664 e35b0000   1384 	cmp	fp,0

00000668 0a00002a   1385 	beq	.L1167

0000066c e59d2054   1386 	ldr	r2,[sp,84]

00000670 e597000c   1387 	ldr	r0,[r7,12]

00000674 e1a0100b   1388 	mov	r1,fp

00000678 eb000000*  1389 	bl	crc32

0000067c e587000c   1390 	str	r0,[r7,12]

00000680 e28d0054   1391 	add	r0,sp,84

00000684 e8900600   1392 	ldmfd	[r0],{r9-r10}


                                                                      Page 24
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1201.s
00000688 e28d001c   1393 	add	r0,sp,28

0000068c e8800600   1394 	stmea	[r0],{r9-r10}

00000690 ea000020   1395 	b	.L1167

                    1396 .L1169:

00000694 e1a02000   1397 	mov	r2,r0

00000698 e59d0018   1398 	ldr	r0,[sp,24]

0000069c e1a03fc2   1399 	mov	r3,r2 asr 31

000006a0 e2801024   1400 	add	r1,r0,36

000006a4 eb0001a1*  1401 	bl	zs_writedata

000006a8 e1a02004   1402 	mov	r2,r4

000006ac e1510fc2   1403 	cmp	r1,r2 asr 31

000006b0 01500002   1404 	cmpeq	r0,r2

000006b4 0a000005   1405 	beq	.L1170

000006b8 e59d205c   1406 	ldr	r2,[sp,92]

000006bc e3520000   1407 	cmp	r2,0

000006c0 18820003   1408 	stmneea	[r2],{r0-r1}

000006c4 13a00000   1409 	movne	r0,0

000006c8 1a000038   1410 	bne	.L1157

000006cc ea00002a   1411 	b	.L1182

                    1412 .L1170:

000006d0 e5970010   1413 	ldr	r0,[r7,16]

000006d4 e5971014   1414 	ldr	r1,[r7,20]

000006d8 e0923000   1415 	adds	r3,r2,r0

000006dc e5873010   1416 	str	r3,[r7,16]

000006e0 e0a14fc2   1417 	adc	r4,r1,r2 asr 31

000006e4 e5874014   1418 	str	r4,[r7,20]

000006e8 e35b0000   1419 	cmp	fp,0

000006ec 0a000009   1420 	beq	.L1167

000006f0 e28d101c   1421 	add	r1,sp,28

000006f4 e8910071   1422 	ldmfd	[r1],{r0,r4-r6}

000006f8 e095b00b   1423 	adds	fp,r5,fp

000006fc e0503005   1424 	subs	r3,r0,r5

00000700 e58d301c   1425 	str	r3,[sp,28]

00000704 e0c40006   1426 	sbc	r0,r4,r6

00000708 e58d0020   1427 	str	r0,[sp,32]

0000070c e3530001   1428 	cmp	r3,1

00000710 e2d01000   1429 	sbcs	r1,r0,0

00000714 ba000015   1430 	blt	.L1179

                    1431 .L1167:

00000718 e3a05c40   1432 	mov	r5,1<<14

0000071c e597112c   1433 	ldr	r1,[r7,300]

00000720 e3a06000   1434 	mov	r6,0

00000724 e591c008   1435 	ldr	r12,[r1,8]

00000728 e28d1018   1436 	add	r1,sp,24

0000072c e8910105   1437 	ldmfd	[r1],{r0,r2,r8}

00000730 e2804024   1438 	add	r4,r0,36

00000734 e28d3024   1439 	add	r3,sp,36

00000738 e24dd004   1440 	sub	sp,sp,4

0000073c e1a01002   1441 	mov	r1,r2

00000740 e1a02008   1442 	mov	r2,r8

00000744 e88d007e   1443 	stmea	[sp],{r1-r6}

00000748 e8bd0008   1444 	ldmfd	[sp]!,{r3}

0000074c e1a0200b   1445 	mov	r2,fp

00000750 e59d0018   1446 	ldr	r0,[sp,24]

00000754 e1a01007   1447 	mov	r1,r7

00000758 e1a0e00f   1448 	mov	lr,pc

0000075c e12fff1c*  1449 	bx	r12

00000760 e58d0014   1450 	str	r0,[sp,20]

00000764 e1a04000   1451 	mov	r4,r0

00000768 e3500000   1452 	cmp	r0,0

0000076c caffffc8   1453 	bgt	.L1169


                                                                      Page 25
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1201.s
                    1454 .L1179:

00000770 e59d0014   1455 	ldr	r0,[sp,20]

00000774 e3500000   1456 	cmp	r0,0

00000778 aa000001   1457 	bge	.L1181

                    1458 .L1182:

0000077c e3a00000   1459 	mov	r0,0

00000780 ea00000a   1460 	b	.L1157

                    1461 .L1181:

00000784 e35b0000   1462 	cmp	fp,0

00000788 0a000007   1463 	beq	.L1184

0000078c e59d1054   1464 	ldr	r1,[sp,84]

00000790 e5973018   1465 	ldr	r3,[r7,24]

00000794 e597001c   1466 	ldr	r0,[r7,28]

00000798 e0911003   1467 	adds	r1,r1,r3

0000079c e59dc058   1468 	ldr	r12,[sp,88]

000007a0 e5871018   1469 	str	r1,[r7,24]

000007a4 e0ac0000   1470 	adc	r0,r12,r0

000007a8 e587001c   1471 	str	r0,[r7,28]

                    1472 .L1184:

000007ac e1a00007   1473 	mov	r0,r7

                    1474 .L1157:

000007b0 e28dd02c   1475 	add	sp,sp,44

000007b4 e89daff0   1476 	ldmfd	[sp],{r4-fp,sp,pc}

                    1477 	.endf	zs_entrydata

                    1478 	.align	4

                    1479 ;writeSize	[sp,20]	local

                    1480 ;lwritestatus	r0	local

                    1481 ;consumed	[sp,36]	local

                    1482 ;remaining	[sp,28]	local

                    1483 

                    1484 ;zstream	[sp,24]	param

                    1485 ;zentry	r7	param

                    1486 ;entry	fp	param

                    1487 ;entrySize	[sp,84]	param

                    1488 ;writestatus	[sp,92]	param

                    1489 

                    1490 	.section ".bss","awb"

                    1491 .L1342:

                    1492 	.data

                    1493 	.text

                    1494 

                    1495 

                    1496 	.align	4

                    1497 	.align	4

                    1498 zs_entryend::

000007b8 e92d4070   1499 	stmfd	[sp]!,{r4-r6,lr}

000007bc e24dd00c   1500 	sub	sp,sp,12

000007c0 e1a04001   1501 	mov	r4,r1

000007c4 e1a06000   1502 	mov	r6,r0

000007c8 e1b05002   1503 	movs	r5,r2

000007cc 13a00000   1504 	movne	r0,0

000007d0 11a01000   1505 	movne	r1,r0

000007d4 18850003   1506 	stmneea	[r5],{r0-r1}

000007d8 e3560000   1507 	cmp	r6,0

000007dc 13540000   1508 	cmpne	r4,0

000007e0 0a000031   1509 	beq	.L1411

000007e4 e3a00000   1510 	mov	r0,0

000007e8 e52d0004   1511 	str	r0,[sp,-4]!

000007ec e98d0021   1512 	stmfa	[sp],{r0,r5}

000007f0 e8bd0008   1513 	ldmfd	[sp]!,{r3}

000007f4 e1a01004   1514 	mov	r1,r4


                                                                      Page 26
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1201.s
000007f8 e1a00006   1515 	mov	r0,r6

000007fc e3a02000   1516 	mov	r2,0

00000800 ebffff83*  1517 	bl	zs_entrydata

00000804 e3500000   1518 	cmp	r0,0

00000808 0a000027   1519 	beq	.L1411

0000080c e594012c   1520 	ldr	r0,[r4,300]

00000810 e590c00c   1521 	ldr	r12,[r0,12]

00000814 e35c0000   1522 	cmp	r12,0

00000818 0a000005   1523 	beq	.L1405

0000081c e1a01004   1524 	mov	r1,r4

00000820 e1a00006   1525 	mov	r0,r6

00000824 e1a0e00f   1526 	mov	lr,pc

00000828 e12fff1c*  1527 	bx	r12

0000082c e3500000   1528 	cmp	r0,0

00000830 1a00001d   1529 	bne	.L1411

                    1530 .L1405:

00000834 e59f2714*  1531 	ldr	r2,.L1568

00000838 e28d1008   1532 	add	r1,sp,8

0000083c e3a00000   1533 	mov	r0,0

00000840 e58d0008   1534 	str	r0,[sp,8]

00000844 e1a00006   1535 	mov	r0,r6

00000848 eb0001ad*  1536 	bl	zs_packunit32

0000084c e594200c   1537 	ldr	r2,[r4,12]

00000850 e28d1008   1538 	add	r1,sp,8

00000854 e1a00006   1539 	mov	r0,r6

00000858 eb0001a9*  1540 	bl	zs_packunit32

0000085c e5942010   1541 	ldr	r2,[r4,16]

00000860 e28d1008   1542 	add	r1,sp,8

00000864 e1a00006   1543 	mov	r0,r6

00000868 eb0001a5*  1544 	bl	zs_packunit32

0000086c e5942018   1545 	ldr	r2,[r4,24]

00000870 e28d1008   1546 	add	r1,sp,8

00000874 e1a00006   1547 	mov	r0,r6

00000878 eb0001a1*  1548 	bl	zs_packunit32

0000087c e59d2008   1549 	ldr	r2,[sp,8]

00000880 e2861024   1550 	add	r1,r6,36

00000884 e1a03fc2   1551 	mov	r3,r2 asr 31

00000888 e1a00006   1552 	mov	r0,r6

0000088c eb000127*  1553 	bl	zs_writedata

00000890 e59d2008   1554 	ldr	r2,[sp,8]

00000894 e1510fc2   1555 	cmp	r1,r2 asr 31

00000898 01500002   1556 	cmpeq	r0,r2

0000089c 01a00004   1557 	moveq	r0,r4

000008a0 0a000002   1558 	beq	.L1394

000008a4 e3550000   1559 	cmp	r5,0

000008a8 18850003   1560 	stmneea	[r5],{r0-r1}

                    1561 .L1411:

000008ac e3a00000   1562 	mov	r0,0

                    1563 .L1394:

000008b0 e28dd00c   1564 	add	sp,sp,12

000008b4 e8bd8070   1565 	ldmfd	[sp]!,{r4-r6,pc}

                    1566 	.endf	zs_entryend

                    1567 	.align	4

                    1568 ;lwritestatus	r0	local

                    1569 ;packed	[sp,8]	local

                    1570 

                    1571 ;zstream	r6	param

                    1572 ;zentry	r4	param

                    1573 ;writestatus	r5	param

                    1574 

                    1575 	.section ".bss","awb"


                                                                      Page 27
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1201.s
                    1576 .L1532:

                    1577 	.data

                    1578 	.text

                    1579 

                    1580 

                    1581 	.align	4

                    1582 	.align	4

                    1583 zs_finish::

000008b8 e92d48f2   1584 	stmfd	[sp]!,{r1,r4-r7,fp,lr}

000008bc e24dd00c   1585 	sub	sp,sp,12

000008c0 e58d100c   1586 	str	r1,[sp,12]

000008c4 e1a03001   1587 	mov	r3,r1

000008c8 e1a04000   1588 	mov	r4,r0

000008cc e3510000   1589 	cmp	r1,0

000008d0 13a00000   1590 	movne	r0,0

000008d4 11a01000   1591 	movne	r1,r0

000008d8 18830003   1592 	stmneea	[r3],{r0-r1}

000008dc e3540000   1593 	cmp	r4,0

000008e0 0a00010f   1594 	beq	.L1608

000008e4 e9940005   1595 	ldmed	[r4],{r0,r2}

000008e8 e584000c   1596 	str	r0,[r4,12]

000008ec e5842010   1597 	str	r2,[r4,16]

000008f0 e1a0600d   1598 	mov	r6,sp

000008f4 e5945018   1599 	ldr	r5,[r4,24]

000008f8 e3a0b000   1600 	mov	fp,0

000008fc e3550000   1601 	cmp	r5,0

00000900 0a000075   1602 	beq	.L1577

                    1603 .L1578:

00000904 e5950020   1604 	ldr	r0,[r5,32]

00000908 e5951024   1605 	ldr	r1,[r5,36]

0000090c e3e02000   1606 	mvn	r2,0

00000910 e3510000   1607 	cmp	r1,0

00000914 01500002   1608 	cmpeq	r0,r2

00000918 83a07001   1609 	movhi	r7,1

0000091c 93a07000   1610 	movls	r7,0

00000920 e58db000   1611 	str	fp,[sp]

00000924 e59f2628*  1612 	ldr	r2,.L1917

00000928 e1a01006   1613 	mov	r1,r6

0000092c e1a00004   1614 	mov	r0,r4

00000930 eb000173*  1615 	bl	zs_packunit32

00000934 e1a01006   1616 	mov	r1,r6

00000938 e1a00004   1617 	mov	r0,r4

0000093c e3a02000   1618 	mov	r2,0

00000940 eb00015b*  1619 	bl	zs_packunit16

00000944 e1d520b0   1620 	ldrh	r2,[r5]

00000948 e1a01006   1621 	mov	r1,r6

0000094c e1a00004   1622 	mov	r0,r4

00000950 eb000157*  1623 	bl	zs_packunit16

00000954 e1d520b2   1624 	ldrh	r2,[r5,2]

00000958 e1a01006   1625 	mov	r1,r6

0000095c e1a00004   1626 	mov	r0,r4

00000960 eb000153*  1627 	bl	zs_packunit16

00000964 e1d520b4   1628 	ldrh	r2,[r5,4]

00000968 e1a01006   1629 	mov	r1,r6

0000096c e1a00004   1630 	mov	r0,r4

00000970 eb00014f*  1631 	bl	zs_packunit16

00000974 e1d520b8   1632 	ldrh	r2,[r5,8]

00000978 e1a01006   1633 	mov	r1,r6

0000097c e1a00004   1634 	mov	r0,r4

00000980 eb00014b*  1635 	bl	zs_packunit16

00000984 e1d520b6   1636 	ldrh	r2,[r5,6]


                                                                      Page 28
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1201.s
00000988 e1a01006   1637 	mov	r1,r6

0000098c e1a00004   1638 	mov	r0,r4

00000990 eb000147*  1639 	bl	zs_packunit16

00000994 e595200c   1640 	ldr	r2,[r5,12]

00000998 e1a01006   1641 	mov	r1,r6

0000099c e1a00004   1642 	mov	r0,r4

000009a0 eb000157*  1643 	bl	zs_packunit32

000009a4 e5952010   1644 	ldr	r2,[r5,16]

000009a8 e1a01006   1645 	mov	r1,r6

000009ac e1a00004   1646 	mov	r0,r4

000009b0 eb000153*  1647 	bl	zs_packunit32

000009b4 e5952018   1648 	ldr	r2,[r5,24]

000009b8 e1a01006   1649 	mov	r1,r6

000009bc e1a00004   1650 	mov	r0,r4

000009c0 eb00014f*  1651 	bl	zs_packunit32

000009c4 e1d522b8   1652 	ldrh	r2,[r5,40]

000009c8 e1a01006   1653 	mov	r1,r6

000009cc e1a00004   1654 	mov	r0,r4

000009d0 eb000137*  1655 	bl	zs_packunit16

000009d4 e3a02000   1656 	mov	r2,0

000009d8 e3570000   1657 	cmp	r7,0

000009dc 13a0200c   1658 	movne	r2,12

000009e0 e1a01006   1659 	mov	r1,r6

000009e4 e1a00004   1660 	mov	r0,r4

000009e8 eb000131*  1661 	bl	zs_packunit16

000009ec e1a01006   1662 	mov	r1,r6

000009f0 e1a00004   1663 	mov	r0,r4

000009f4 e3a02000   1664 	mov	r2,0

000009f8 eb00012d*  1665 	bl	zs_packunit16

000009fc e1a01006   1666 	mov	r1,r6

00000a00 e1a00004   1667 	mov	r0,r4

00000a04 e3a02000   1668 	mov	r2,0

00000a08 eb000129*  1669 	bl	zs_packunit16

00000a0c e1a01006   1670 	mov	r1,r6

00000a10 e1a00004   1671 	mov	r0,r4

00000a14 e3a02000   1672 	mov	r2,0

00000a18 eb000125*  1673 	bl	zs_packunit16

00000a1c e1a01006   1674 	mov	r1,r6

00000a20 e1a00004   1675 	mov	r0,r4

00000a24 e3a02000   1676 	mov	r2,0

00000a28 eb000135*  1677 	bl	zs_packunit32

00000a2c e3570000   1678 	cmp	r7,0

00000a30 13e02000   1679 	mvnne	r2,0

00000a34 05952020   1680 	ldreq	r2,[r5,32]

00000a38 e1a01006   1681 	mov	r1,r6

00000a3c e1a00004   1682 	mov	r0,r4

00000a40 eb00012f*  1683 	bl	zs_packunit32

00000a44 e1d522b8   1684 	ldrh	r2,[r5,40]

00000a48 e285102a   1685 	add	r1,r5,42

00000a4c e59d3000   1686 	ldr	r3,[sp]

00000a50 e2840024   1687 	add	r0,r4,36

00000a54 e0830000   1688 	add	r0,r3,r0

00000a58 eb000000*  1689 	bl	memcpy

00000a5c e1d522b8   1690 	ldrh	r2,[r5,40]

00000a60 e59d0000   1691 	ldr	r0,[sp]

00000a64 e3570000   1692 	cmp	r7,0

00000a68 e0800002   1693 	add	r0,r0,r2

00000a6c e58d0000   1694 	str	r0,[sp]

00000a70 0a00000d   1695 	beq	.L1584

00000a74 e1a01006   1696 	mov	r1,r6

00000a78 e1a00004   1697 	mov	r0,r4


                                                                      Page 29
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1201.s
00000a7c e3a02001   1698 	mov	r2,1

00000a80 eb00010b*  1699 	bl	zs_packunit16

00000a84 e1a01006   1700 	mov	r1,r6

00000a88 e1a00004   1701 	mov	r0,r4

00000a8c e3a02008   1702 	mov	r2,8

00000a90 eb000107*  1703 	bl	zs_packunit16

00000a94 e5952020   1704 	ldr	r2,[r5,32]

00000a98 e5953024   1705 	ldr	r3,[r5,36]

00000a9c e1a01006   1706 	mov	r1,r6

00000aa0 e1a00004   1707 	mov	r0,r4

00000aa4 eb00012f*  1708 	bl	zs_packunit64

00000aa8 e59d0000   1709 	ldr	r0,[sp]

                    1710 .L1584:

00000aac e1a02000   1711 	mov	r2,r0

00000ab0 e1a03fc2   1712 	mov	r3,r2 asr 31

00000ab4 e2841024   1713 	add	r1,r4,36

00000ab8 e1a00004   1714 	mov	r0,r4

00000abc eb00009b*  1715 	bl	zs_writedata

00000ac0 e59d2000   1716 	ldr	r2,[sp]

00000ac4 e1510fc2   1717 	cmp	r1,r2 asr 31

00000ac8 01500002   1718 	cmpeq	r0,r2

00000acc 1a000091   1719 	bne	.L1607

00000ad0 e5955134   1720 	ldr	r5,[r5,308]

00000ad4 e3550000   1721 	cmp	r5,0

00000ad8 1affff89   1722 	bne	.L1578

                    1723 .L1577:

00000adc e99408e0   1724 	ldmed	[r4],{r5-r7,fp}

00000ae0 e0550007   1725 	subs	r0,r5,r7

00000ae4 e0c6200b   1726 	sbc	r2,r6,fp

00000ae8 e98d0005   1727 	stmfa	[sp],{r0,r2}

00000aec e3a03000   1728 	mov	r3,0

00000af0 e3e02000   1729 	mvn	r2,0

00000af4 e1520007   1730 	cmp	r2,r7

00000af8 e0d3000b   1731 	sbcs	r0,r3,fp

00000afc aa000053   1732 	bge	.L1591

00000b00 e1a07006   1733 	mov	r7,r6

00000b04 e1a06005   1734 	mov	r6,r5

00000b08 e3a05000   1735 	mov	r5,0

00000b0c e58d5000   1736 	str	r5,[sp]

00000b10 e59f2440*  1737 	ldr	r2,.L1918

00000b14 e1a0100d   1738 	mov	r1,sp

00000b18 e1a00004   1739 	mov	r0,r4

00000b1c eb0000f8*  1740 	bl	zs_packunit32

00000b20 e1a0100d   1741 	mov	r1,sp

00000b24 e1a00004   1742 	mov	r0,r4

00000b28 e3a0202c   1743 	mov	r2,44

00000b2c e1a03005   1744 	mov	r3,r5

00000b30 eb00010c*  1745 	bl	zs_packunit64

00000b34 e1a0100d   1746 	mov	r1,sp

00000b38 e1a00004   1747 	mov	r0,r4

00000b3c e3a0201e   1748 	mov	r2,30

00000b40 eb0000db*  1749 	bl	zs_packunit16

00000b44 e1a0100d   1750 	mov	r1,sp

00000b48 e1a00004   1751 	mov	r0,r4

00000b4c e3a0202d   1752 	mov	r2,45

00000b50 eb0000d7*  1753 	bl	zs_packunit16

00000b54 e1a0100d   1754 	mov	r1,sp

00000b58 e1a00004   1755 	mov	r0,r4

00000b5c e1a02005   1756 	mov	r2,r5

00000b60 eb0000e7*  1757 	bl	zs_packunit32

00000b64 e1a0100d   1758 	mov	r1,sp


                                                                      Page 30
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1201.s
00000b68 e1a00004   1759 	mov	r0,r4

00000b6c e1a02005   1760 	mov	r2,r5

00000b70 eb0000e3*  1761 	bl	zs_packunit32

00000b74 e5942014   1762 	ldr	r2,[r4,20]

00000b78 e1a0100d   1763 	mov	r1,sp

00000b7c e1a03fc2   1764 	mov	r3,r2 asr 31

00000b80 e1a00004   1765 	mov	r0,r4

00000b84 eb0000f7*  1766 	bl	zs_packunit64

00000b88 e5942014   1767 	ldr	r2,[r4,20]

00000b8c e1a0100d   1768 	mov	r1,sp

00000b90 e1a03fc2   1769 	mov	r3,r2 asr 31

00000b94 e1a00004   1770 	mov	r0,r4

00000b98 eb0000f2*  1771 	bl	zs_packunit64

00000b9c e99d000c   1772 	ldmed	[sp],{r2-r3}

00000ba0 e1a0100d   1773 	mov	r1,sp

00000ba4 e1a00004   1774 	mov	r0,r4

00000ba8 eb0000ee*  1775 	bl	zs_packunit64

00000bac e594200c   1776 	ldr	r2,[r4,12]

00000bb0 e5943010   1777 	ldr	r3,[r4,16]

00000bb4 e1a0100d   1778 	mov	r1,sp

00000bb8 e1a00004   1779 	mov	r0,r4

00000bbc eb0000e9*  1780 	bl	zs_packunit64

00000bc0 e59d2000   1781 	ldr	r2,[sp]

00000bc4 e2841024   1782 	add	r1,r4,36

00000bc8 e1a03fc2   1783 	mov	r3,r2 asr 31

00000bcc e1a00004   1784 	mov	r0,r4

00000bd0 eb000056*  1785 	bl	zs_writedata

00000bd4 e59d2000   1786 	ldr	r2,[sp]

00000bd8 e1510fc2   1787 	cmp	r1,r2 asr 31

00000bdc 01500002   1788 	cmpeq	r0,r2

00000be0 1a00004c   1789 	bne	.L1607

00000be4 e58d5000   1790 	str	r5,[sp]

00000be8 e59f236c*  1791 	ldr	r2,.L1919

00000bec e1a0100d   1792 	mov	r1,sp

00000bf0 e1a00004   1793 	mov	r0,r4

00000bf4 eb0000c2*  1794 	bl	zs_packunit32

00000bf8 e1a0100d   1795 	mov	r1,sp

00000bfc e1a00004   1796 	mov	r0,r4

00000c00 e1a02005   1797 	mov	r2,r5

00000c04 eb0000be*  1798 	bl	zs_packunit32

00000c08 e1a02006   1799 	mov	r2,r6

00000c0c e1a03007   1800 	mov	r3,r7

00000c10 e1a0100d   1801 	mov	r1,sp

00000c14 e1a00004   1802 	mov	r0,r4

00000c18 eb0000d2*  1803 	bl	zs_packunit64

00000c1c e1a0100d   1804 	mov	r1,sp

00000c20 e1a00004   1805 	mov	r0,r4

00000c24 e3a02001   1806 	mov	r2,1

00000c28 eb0000b5*  1807 	bl	zs_packunit32

00000c2c e59d2000   1808 	ldr	r2,[sp]

00000c30 e2841024   1809 	add	r1,r4,36

00000c34 e1a03fc2   1810 	mov	r3,r2 asr 31

00000c38 e1a00004   1811 	mov	r0,r4

00000c3c eb00003b*  1812 	bl	zs_writedata

00000c40 e59d2000   1813 	ldr	r2,[sp]

00000c44 e1510fc2   1814 	cmp	r1,r2 asr 31

00000c48 01500002   1815 	cmpeq	r0,r2

00000c4c 1a000031   1816 	bne	.L1607

                    1817 .L1591:

00000c50 e3a05000   1818 	mov	r5,0

00000c54 e58d5000   1819 	str	r5,[sp]


                                                                      Page 31
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1201.s
00000c58 e59f2300*  1820 	ldr	r2,.L1920

00000c5c e1a0100d   1821 	mov	r1,sp

00000c60 e1a00004   1822 	mov	r0,r4

00000c64 eb0000a6*  1823 	bl	zs_packunit32

00000c68 e1a0100d   1824 	mov	r1,sp

00000c6c e1a00004   1825 	mov	r0,r4

00000c70 e1a02005   1826 	mov	r2,r5

00000c74 eb00008e*  1827 	bl	zs_packunit16

00000c78 e1a0100d   1828 	mov	r1,sp

00000c7c e1a00004   1829 	mov	r0,r4

00000c80 e1a02005   1830 	mov	r2,r5

00000c84 eb00008a*  1831 	bl	zs_packunit16

00000c88 e1d421b4   1832 	ldrh	r2,[r4,20]

00000c8c e1a0100d   1833 	mov	r1,sp

00000c90 e1a00004   1834 	mov	r0,r4

00000c94 eb000086*  1835 	bl	zs_packunit16

00000c98 e1d421b4   1836 	ldrh	r2,[r4,20]

00000c9c e1a0100d   1837 	mov	r1,sp

00000ca0 e1a00004   1838 	mov	r0,r4

00000ca4 eb000082*  1839 	bl	zs_packunit16

00000ca8 e59d2004   1840 	ldr	r2,[sp,4]

00000cac e1a0100d   1841 	mov	r1,sp

00000cb0 e1a00004   1842 	mov	r0,r4

00000cb4 eb000092*  1843 	bl	zs_packunit32

00000cb8 e594000c   1844 	ldr	r0,[r4,12]

00000cbc e5941010   1845 	ldr	r1,[r4,16]

00000cc0 e3e02000   1846 	mvn	r2,0

00000cc4 e1520000   1847 	cmp	r2,r0

00000cc8 e0d52001   1848 	sbcs	r2,r5,r1

00000ccc e1a0100d   1849 	mov	r1,sp

00000cd0 b3e02000   1850 	mvnlt	r2,0

00000cd4 a1a02000   1851 	movge	r2,r0

00000cd8 e1a00004   1852 	mov	r0,r4

00000cdc eb000088*  1853 	bl	zs_packunit32

00000ce0 e1a0100d   1854 	mov	r1,sp

00000ce4 e1a00004   1855 	mov	r0,r4

00000ce8 e1a02005   1856 	mov	r2,r5

00000cec eb000070*  1857 	bl	zs_packunit16

00000cf0 e59d2000   1858 	ldr	r2,[sp]

00000cf4 e2841024   1859 	add	r1,r4,36

00000cf8 e1a03fc2   1860 	mov	r3,r2 asr 31

00000cfc e1a00004   1861 	mov	r0,r4

00000d00 eb00000a*  1862 	bl	zs_writedata

00000d04 e59d2000   1863 	ldr	r2,[sp]

00000d08 e1510fc2   1864 	cmp	r1,r2 asr 31

00000d0c 01500002   1865 	cmpeq	r0,r2

00000d10 03a00000   1866 	moveq	r0,0

00000d14 0a000003   1867 	beq	.L1569

                    1868 .L1607:

00000d18 e59d200c   1869 	ldr	r2,[sp,12]

00000d1c e3520000   1870 	cmp	r2,0

00000d20 18820003   1871 	stmneea	[r2],{r0-r1}

                    1872 .L1608:

00000d24 e3e00000   1873 	mvn	r0,0

                    1874 .L1569:

00000d28 e28dd00c   1875 	add	sp,sp,12

00000d2c e8bd88f2   1876 	ldmfd	[sp]!,{r1,r4-r7,fp,pc}

                    1877 	.endf	zs_finish

                    1878 	.align	4

                    1879 ;zentry	r5	local

                    1880 ;lwritestatus	r0	local


                                                                      Page 32
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1201.s
                    1881 ;packed	[sp]	local

                    1882 ;cdsize	[sp,4]	local

                    1883 ;zip64endrecord	r6	local

                    1884 ;zip64	r7	local

                    1885 

                    1886 ;zstream	r4	param

                    1887 ;writestatus	[sp,12]	param

                    1888 

                    1889 	.section ".bss","awb"

                    1890 .L1850:

                    1891 	.data

                    1892 	.text

                    1893 

                    1894 

                    1895 	.align	4

                    1896 	.align	4

                    1897 zs_writedata:

00000d30 e92d4870   1898 	stmfd	[sp]!,{r4-r6,fp,lr}

00000d34 e24dd014   1899 	sub	sp,sp,20

00000d38 e1b0b000   1900 	movs	fp,r0

00000d3c e1a04001   1901 	mov	r4,r1

00000d40 e98d000c   1902 	stmfa	[sp],{r2-r3}

00000d44 13510000   1903 	cmpne	r1,0

00000d48 03a00000   1904 	moveq	r0,0

00000d4c 01a01000   1905 	moveq	r1,r0

00000d50 0a000026   1906 	beq	.L1921

00000d54 e3a05000   1907 	mov	r5,0

00000d58 e58d500c   1908 	str	r5,[sp,12]

00000d5c e58d5010   1909 	str	r5,[sp,16]

00000d60 e99d000c   1910 	ldmed	[sp],{r2-r3}

00000d64 e1550002   1911 	cmp	r5,r2

00000d68 e0d50003   1912 	sbcs	r0,r5,r3

00000d6c aa00001d   1913 	bge	.L1928

                    1914 .L1929:

00000d70 e1a00003   1915 	mov	r0,r3

00000d74 e1a03005   1916 	mov	r3,r5

00000d78 e1a05002   1917 	mov	r5,r2

00000d7c e0552003   1918 	subs	r2,r5,r3

00000d80 e0c01fc3   1919 	sbc	r1,r0,r3 asr 31

00000d84 e3a00c40   1920 	mov	r0,1<<14

00000d88 e2800001   1921 	add	r0,r0,1

00000d8c e1520000   1922 	cmp	r2,r0

00000d90 e2d10000   1923 	sbcs	r0,r1,0

00000d94 a3a02c40   1924 	movge	r2,1<<14

00000d98 b0452003   1925 	sublt	r2,r5,r3

00000d9c e59b0000   1926 	ldr	r0,[fp]

00000da0 e0841003   1927 	add	r1,r4,r3

00000da4 eb000000*  1928 	bl	zs_user_writeToStream

00000da8 e1a01fc0   1929 	mov	r1,r0 asr 31

00000dac e3500001   1930 	cmp	r0,1

00000db0 e2d1c000   1931 	sbcs	r12,r1,0

00000db4 ba00000d   1932 	blt	.L1921

00000db8 e99b000c   1933 	ldmed	[fp],{r2-r3}

00000dbc e0902002   1934 	adds	r2,r0,r2

00000dc0 e0a13003   1935 	adc	r3,r1,r3

00000dc4 e98b000c   1936 	stmfa	[fp],{r2-r3}

00000dc8 e99d500c   1937 	ldmed	[sp],{r2-r3,r12,lr}

00000dcc e090500c   1938 	adds	r5,r0,r12

00000dd0 e58d500c   1939 	str	r5,[sp,12]

00000dd4 e0a1000e   1940 	adc	r0,r1,lr

00000dd8 e58d0010   1941 	str	r0,[sp,16]


                                                                      Page 33
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1201.s
00000ddc e1550002   1942 	cmp	r5,r2

00000de0 e0d00003   1943 	sbcs	r0,r0,r3

00000de4 baffffe1   1944 	blt	.L1929

                    1945 .L1928:

00000de8 e59d000c   1946 	ldr	r0,[sp,12]

00000dec e59d1010   1947 	ldr	r1,[sp,16]

                    1948 .L1921:

00000df0 e28dd014   1949 	add	sp,sp,20

00000df4 e8bd4870   1950 	ldmfd	[sp]!,{r4-r6,fp,lr}

00000df8 e12fff1e*  1951 	ret	

                    1952 	.endf	zs_writedata

                    1953 	.align	4

                    1954 ;lwritestatus	r0	local

                    1955 ;written	[sp,12]	local

                    1956 

                    1957 ;zstream	fp	param

                    1958 ;writeBuffer	r4	param

                    1959 ;writeBufferSize	[sp,4]	param

                    1960 

                    1961 	.section ".bss","awb"

                    1962 .L2012:

                    1963 	.data

                    1964 	.text

                    1965 

                    1966 

                    1967 ;910: /***************************************************************************

                    1968 ;911:  * Byte swapping routine:

                    1969 ;912:  *

                    1970 ;913:  * Functions for generalized, in-place byte swapping from host order

                    1971 ;914:  * to little-endian.  A run-time test of byte order is conducted on

                    1972 ;915:  * the first usage and a static variable is used to store the result

                    1973 ;916:  * for later use.

                    1974 ;917:  *

                    1975 ;918:  * The byte-swapping requires memory-aligned quantities.

                    1976 ;919:  *

                    1977 ;920:  ***************************************************************************/

                    1978 ;921: static void

                    1979 ;922: zs_htolx ( void *data, int size )

                    1980 	.align	4

                    1981 	.align	4

                    1982 zs_htolx:

00000dfc e92d0100   1983 	stmfd	[sp]!,{r8}

                    1984 ;923: {

                    1985 

00000e00 e24dd004   1986 	sub	sp,sp,4

00000e04 e3a0c001   1987 	mov	r12,1

00000e08 e59f3154*  1988 	ldr	r3,.L2161

00000e0c e1cdc0b2   1989 	strh	r12,[sp,2]

                    1990 ;924:   static int le = -1;

                    1991 ;925:   int16_t host = 1;

                    1992 

                    1993 ;927:   uint16_t *data2;

                    1994 ;928:   uint32_t *data4;

                    1995 ;929:   uint32_t h0, h1;

                    1996 ;931:   /* Determine byte order, test for little-endianness */

                    1997 ;932:   if ( le < 0 )

                    1998 

00000e10 e5932000   1999 	ldr	r2,[r3]

00000e14 e3520000   2000 	cmp	r2,0

                    2001 ;933:     {

                    2002 


                                                                      Page 34
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1201.s
                    2003 ;934:       le = (*((int8_t *)(&host)));

                    2004 

00000e18 b1a0200c   2005 	movlt	r2,r12

00000e1c b5832000   2006 	strlt	r2,[r3]

                    2007 ;935:     }

                    2008 ;937:   /* Swap bytes if not little-endian, requires memory-aligned quantities */

                    2009 ;938:   if ( le == 0 )

                    2010 

00000e20 e3520000   2011 	cmp	r2,0

00000e24 1a00001f   2012 	bne	.L2044

                    2013 ;939:     {

                    2014 

                    2015 ;940:       switch ( size )

                    2016 

00000e28 e2511002   2017 	subs	r1,r1,2

00000e2c 0a000004   2018 	beq	.L2052

00000e30 e2511002   2019 	subs	r1,r1,2

00000e34 0a000009   2020 	beq	.L2053

00000e38 e3510004   2021 	cmp	r1,4

00000e3c 0a00000e   2022 	beq	.L2054

00000e40 ea000018   2023 	b	.L2044

                    2024 .L2052:

                    2025 ;941:         {

                    2026 ;942:         case 2:

                    2027 ;943:           data2 = (uint16_t *) data;

                    2028 

                    2029 ;944:           *data2=(((*data2>>8)&0xff) | ((*data2&0xff)<<8));

                    2030 

00000e44 e1d010b0   2031 	ldrh	r1,[r0]

00000e48 e1a02801   2032 	mov	r2,r1 lsl 16

00000e4c e1a01c01   2033 	mov	r1,r1 lsl 24

00000e50 e1a01821   2034 	mov	r1,r1 lsr 16

00000e54 e1811c22   2035 	orr	r1,r1,r2 lsr 24

00000e58 e1c010b0   2036 	strh	r1,[r0]

00000e5c ea000011   2037 	b	.L2044

                    2038 .L2053:

                    2039 ;945:           break;

                    2040 ;946:         case 4:

                    2041 ;947:           data4 = (uint32_t *) data;

                    2042 

                    2043 ;948:           *data4=(((*data4>>24)&0xff) | ((*data4&0xff)<<24) |

                    2044 

00000e60 e5901000   2045 	ldr	r1,[r0]

00000e64 e1a02461   2046 	mov	r2,r1 ror 8

00000e68 e0211861   2047 	eor	r1,r1,r1 ror 16

00000e6c e3c118ff   2048 	bic	r1,r1,255<<16

00000e70 e0221421   2049 	eor	r1,r2,r1 lsr 8

00000e74 e5801000   2050 	str	r1,[r0]

00000e78 ea00000a   2051 	b	.L2044

                    2052 .L2054:

                    2053 ;949:                   ((*data4>>8)&0xff00) | ((*data4&0xff00)<<8));

                    2054 ;950:           break;

                    2055 ;951:         case 8:

                    2056 ;952:           data4 = (uint32_t *) data;

                    2057 

                    2058 ;954:           h0 = data4[0];

                    2059 

00000e7c e890000a   2060 	ldmfd	[r0],{r1,r3}

                    2061 ;955:           h0 = (((h0>>24)&0xff) | ((h0&0xff)<<24) |

                    2062 

00000e80 e1a02461   2063 	mov	r2,r1 ror 8


                                                                      Page 35
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1201.s
00000e84 e0211861   2064 	eor	r1,r1,r1 ror 16

00000e88 e3c118ff   2065 	bic	r1,r1,255<<16

00000e8c e0222421   2066 	eor	r2,r2,r1 lsr 8

                    2067 ;956:                 ((h0>>8)&0xff00) | ((h0&0xff00)<<8));

                    2068 ;958:           h1 = data4[1];

                    2069 

00000e90 e1a01003   2070 	mov	r1,r3

                    2071 ;959:           h1 = (((h1>>24)&0xff) | ((h1&0xff)<<24) |

                    2072 

00000e94 e1a03461   2073 	mov	r3,r1 ror 8

00000e98 e0211861   2074 	eor	r1,r1,r1 ror 16

00000e9c e3c118ff   2075 	bic	r1,r1,255<<16

00000ea0 e0231421   2076 	eor	r1,r3,r1 lsr 8

                    2077 ;960:                 ((h1>>8)&0xff00) | ((h1&0xff00)<<8));

                    2078 ;962:           data4[0] = h1;

                    2079 

00000ea4 e8800006   2080 	stmea	[r0],{r1-r2}

                    2081 ;963:           data4[1] = h0;

                    2082 

                    2083 .L2044:

00000ea8 e28dd004   2084 	add	sp,sp,4

00000eac e8bd0100   2085 	ldmfd	[sp]!,{r8}

00000eb0 e12fff1e*  2086 	ret	

                    2087 	.endf	zs_htolx

                    2088 	.align	4

                    2089 ;le	.L2138	static

                    2090 ;host	[sp,2]	local

                    2091 ;h0	r1	local

                    2092 ;h1	r1	local

                    2093 

                    2094 ;data	r0	param

                    2095 ;size	r1	param

                    2096 

                    2097 	.section ".bss","awb"

                    2098 .L2135:

                    2099 	.data

00000000 ffffffff   2100 .L2138:	.data.b	255,255,255,255

                    2101 	.text

                    2102 

                    2103 ;964:           break;

                    2104 ;965:         }

                    2105 ;966:     }

                    2106 ;967: }

                    2107 

                    2108 ;970: /***************************************************************************

                    2109 ;971:  *

                    2110 ;972:  * Helper functions to write little-endian integer values to a

                    2111 ;973:  * specified offset in the ZIPstream buffer and increment offset.

                    2112 ;974:  *

                    2113 ;975:  ***************************************************************************/

                    2114 ;976: static void zs_packunit16 (ZIPstream *ZS, int *O, uint16_t V)

                    2115 	.align	4

                    2116 	.align	4

                    2117 zs_packunit16:

00000eb4 e92d4030   2118 	stmfd	[sp]!,{r4-r5,lr}

                    2119 ;977: {

                    2120 

                    2121 ;978:   memcpy (ZS->buffer+*O, &V, 2);

                    2122 

00000eb8 e24dd004   2123 	sub	sp,sp,4

00000ebc e1a04001   2124 	mov	r4,r1


                                                                      Page 36
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1201.s
00000ec0 e2805024   2125 	add	r5,r0,36

00000ec4 e5940000   2126 	ldr	r0,[r4]

00000ec8 e28d1002   2127 	add	r1,sp,2

00000ecc e0800005   2128 	add	r0,r0,r5

00000ed0 e1cd20b2   2129 	strh	r2,[sp,2]

00000ed4 e3a02002   2130 	mov	r2,2

00000ed8 eb000000*  2131 	bl	memcpy

                    2132 ;979:   zs_htolx(ZS->buffer+*O, 2);

                    2133 

00000edc e5940000   2134 	ldr	r0,[r4]

00000ee0 e3a01002   2135 	mov	r1,2

00000ee4 e0800005   2136 	add	r0,r0,r5

00000ee8 ebffffc3*  2137 	bl	zs_htolx

                    2138 ;980:   *O += 2;

                    2139 

00000eec e5940000   2140 	ldr	r0,[r4]

00000ef0 e2800002   2141 	add	r0,r0,2

00000ef4 e5840000   2142 	str	r0,[r4]

00000ef8 e28dd004   2143 	add	sp,sp,4

00000efc e8bd4030   2144 	ldmfd	[sp]!,{r4-r5,lr}

00000f00 e12fff1e*  2145 	ret	

                    2146 	.endf	zs_packunit16

                    2147 	.align	4

                    2148 

                    2149 ;ZS	r0	param

                    2150 ;O	r4	param

                    2151 ;V	[sp,2]	param

                    2152 

                    2153 	.section ".bss","awb"

                    2154 .L2193:

                    2155 	.data

                    2156 	.text

                    2157 

                    2158 ;981: }

                    2159 

                    2160 ;982: static void zs_packunit32 (ZIPstream *ZS, int *O, uint32_t V)

                    2161 	.align	4

                    2162 	.align	4

                    2163 zs_packunit32:

00000f04 e92d4034   2164 	stmfd	[sp]!,{r2,r4-r5,lr}

                    2165 ;983: {

                    2166 

                    2167 ;984:   memcpy (ZS->buffer+*O, &V, 4);

                    2168 

00000f08 e1a04001   2169 	mov	r4,r1

00000f0c e2805024   2170 	add	r5,r0,36

00000f10 e5940000   2171 	ldr	r0,[r4]

00000f14 e1a0100d   2172 	mov	r1,sp

00000f18 e0800005   2173 	add	r0,r0,r5

00000f1c e3a02004   2174 	mov	r2,4

00000f20 eb000000*  2175 	bl	memcpy

                    2176 ;985:   zs_htolx(ZS->buffer+*O, 4);

                    2177 

00000f24 e5940000   2178 	ldr	r0,[r4]

00000f28 e3a01004   2179 	mov	r1,4

00000f2c e0800005   2180 	add	r0,r0,r5

00000f30 ebffffb1*  2181 	bl	zs_htolx

                    2182 ;986:   *O += 4;

                    2183 

00000f34 e5940000   2184 	ldr	r0,[r4]

00000f38 e2800004   2185 	add	r0,r0,4


                                                                      Page 37
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1201.s
00000f3c e5840000   2186 	str	r0,[r4]

00000f40 e8bd4034   2187 	ldmfd	[sp]!,{r2,r4-r5,lr}

00000f44 e12fff1e*  2188 	ret	

                    2189 	.endf	zs_packunit32

                    2190 	.align	4

                    2191 .L504:

00000f48 00000000*  2192 	.data.w	zs_store_process

                    2193 	.type	.L504,$object

                    2194 	.size	.L504,4

                    2195 

                    2196 .L1156:

00000f4c 04034b50   2197 	.data.w	0x04034b50

                    2198 	.type	.L1156,$object

                    2199 	.size	.L1156,4

                    2200 

                    2201 .L1568:

00000f50 08074b50   2202 	.data.w	0x08074b50

                    2203 	.type	.L1568,$object

                    2204 	.size	.L1568,4

                    2205 

                    2206 .L1917:

00000f54 02014b50   2207 	.data.w	0x02014b50

                    2208 	.type	.L1917,$object

                    2209 	.size	.L1917,4

                    2210 

                    2211 .L1918:

00000f58 06064b50   2212 	.data.w	0x06064b50

                    2213 	.type	.L1918,$object

                    2214 	.size	.L1918,4

                    2215 

                    2216 .L1919:

00000f5c 07064b50   2217 	.data.w	0x07064b50

                    2218 	.type	.L1919,$object

                    2219 	.size	.L1919,4

                    2220 

                    2221 .L1920:

00000f60 06054b50   2222 	.data.w	0x06054b50

                    2223 	.type	.L1920,$object

                    2224 	.size	.L1920,4

                    2225 

                    2226 .L2161:

00000f64 00000000*  2227 	.data.w	.L2138

                    2228 	.type	.L2161,$object

                    2229 	.size	.L2161,4

                    2230 

                    2231 	.align	4

                    2232 

                    2233 ;ZS	r0	param

                    2234 ;O	r4	param

                    2235 ;V	[sp]	param

                    2236 

                    2237 	.section ".bss","awb"

                    2238 .L2225:

                    2239 	.data

                    2240 	.text

                    2241 

                    2242 ;987: }

                    2243 

                    2244 ;988: static void zs_packunit64 (ZIPstream *ZS, int *O, uint64_t V)

                    2245 	.align	4

                    2246 	.align	4


                                                                      Page 38
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1201.s
                    2247 zs_packunit64:

00000f68 e92d4030   2248 	stmfd	[sp]!,{r4-r5,lr}

00000f6c e24dd008   2249 	sub	sp,sp,8

00000f70 e1a04001   2250 	mov	r4,r1

00000f74 e88d000c   2251 	stmea	[sp],{r2-r3}

                    2252 ;989: {

                    2253 

                    2254 ;990:   memcpy (ZS->buffer+*O, &V, 8);

                    2255 

00000f78 e2805024   2256 	add	r5,r0,36

00000f7c e5940000   2257 	ldr	r0,[r4]

00000f80 e1a0100d   2258 	mov	r1,sp

00000f84 e0800005   2259 	add	r0,r0,r5

00000f88 e3a02008   2260 	mov	r2,8

00000f8c eb000000*  2261 	bl	memcpy

                    2262 ;991:   zs_htolx(ZS->buffer+*O, 8);

                    2263 

00000f90 e5940000   2264 	ldr	r0,[r4]

00000f94 e3a01008   2265 	mov	r1,8

00000f98 e0800005   2266 	add	r0,r0,r5

00000f9c ebffff96*  2267 	bl	zs_htolx

                    2268 ;992:   *O += 8;

                    2269 

00000fa0 e5940000   2270 	ldr	r0,[r4]

00000fa4 e2800008   2271 	add	r0,r0,8

00000fa8 e5840000   2272 	str	r0,[r4]

00000fac e28dd008   2273 	add	sp,sp,8

00000fb0 e8bd4030   2274 	ldmfd	[sp]!,{r4-r5,lr}

00000fb4 e12fff1e*  2275 	ret	

                    2276 	.endf	zs_packunit64

                    2277 	.align	4

                    2278 

                    2279 ;ZS	r0	param

                    2280 ;O	r4	param

                    2281 ;V	[sp]	param

                    2282 

                    2283 	.section ".bss","awb"

                    2284 .L2257:

                    2285 	.data

                    2286 	.text

                    2287 

                    2288 ;993: }

                    2289 	.align	4

                    2290 

                    2291 	.data

                    2292 	.ghsnote version,6

                    2293 	.ghsnote tools,3

                    2294 	.ghsnote options,0

                    2295 	.text

                    2296 	.align	4

                    2297 	.data

                    2298 	.align	4

                    2299 	.text

