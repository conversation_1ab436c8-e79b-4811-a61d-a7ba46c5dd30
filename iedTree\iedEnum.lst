                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_88g1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=iedEnum.c -o iedTree\gh_88g1.o -list=iedTree/iedEnum.lst C:\Users\<USER>\AppData\Local\Temp\gh_88g1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_88g1.s
Source File: iedEnum.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      10 ;		iedTree/iedEnum.c -o iedTree/iedEnum.o

                      11 ;Source File:   iedTree/iedEnum.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Oct 07 09:39:21 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "iedInt.h"


                      21 ;2: 


                      22 ;3: #include "iedTree.h"


                      23 ;4: #include "iedFinalDA.h"


                      24 ;5: 


                      25 ;6: #include "../DataSlice.h"


                      26 ;7: #include "../AsnEncoding.h"


                      27 ;8: #include "../mms_data.h"


                      28 ;9: 


                      29 ;10: #include "debug.h"


                      30 ;11: 


                      31 ;12: #include "IEDCompile/AccessInfo.h"


                      32 ;13: 


                      33 ;14: 


                      34 ;15: 


                      35 ;16: static void updateFromDataSlice(IEDEntity entity)


                      36 	.text

                      37 	.align	4

                      38 updateFromDataSlice:

00000000 e92d4010     39 	stmfd	[sp]!,{r4,lr}

00000004 e1a04000     40 	mov	r4,r0

                      41 ;17: {


                      42 

                      43 ;18:     int offset  = entity->dataSliceOffset;


                      44 

00000008 e594002c     45 	ldr	r0,[r4,44]

                      46 ;19:     int32_t value;


                      47 ;20: 


                      48 ;21:     if(offset == -1)


                      49 

0000000c e3700001     50 	cmn	r0,1


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_88g1.s
00000010 0a000011     51 	beq	.L2

                      52 ;22:     {


                      53 

                      54 ;23:         return;


                      55 

                      56 ;24:     }


                      57 ;25: 


                      58 ;26:     value = DataSlice_getInt32FastCurrDS(offset);


                      59 

00000014 e1a00800     60 	mov	r0,r0 lsl 16

00000018 e1a00820     61 	mov	r0,r0 lsr 16

0000001c eb000000*    62 	bl	DataSlice_getInt32FastCurrDS

                      63 ;27: 


                      64 ;28: 


                      65 ;29:     if(entity->intValue == value)


                      66 

00000020 e5941030     67 	ldr	r1,[r4,48]

00000024 e1510000     68 	cmp	r1,r0

                      69 ;30:     {


                      70 

                      71 ;31:         entity->changed = TRGOP_NONE;


                      72 

00000028 03a00000     73 	moveq	r0,0

0000002c 05840028     74 	streq	r0,[r4,40]

00000030 0a000009     75 	beq	.L2

                      76 ;32:     }


                      77 ;33:     else


                      78 ;34:     {


                      79 

                      80 ;35:         entity->changed = entity->trgOps;


                      81 

00000034 e5941024     82 	ldr	r1,[r4,36]

00000038 e5840030     83 	str	r0,[r4,48]

                      84 ;37:         entity->cached = false;


                      85 

0000003c e5841028     86 	str	r1,[r4,40]

                      87 ;36:         entity->intValue = value;


                      88 

00000040 e3a00000     89 	mov	r0,0

00000044 e5c40034     90 	strb	r0,[r4,52]

                      91 ;38:         IEDEntity_setTimeStamp(entity, dataSliceGetTimeStamp());


                      92 

00000048 eb000000*    93 	bl	dataSliceGetTimeStamp

0000004c e1a02001     94 	mov	r2,r1

00000050 e1a01000     95 	mov	r1,r0

00000054 e1a00004     96 	mov	r0,r4

00000058 eb000000*    97 	bl	IEDEntity_setTimeStamp

                      98 .L2:

0000005c e8bd4010     99 	ldmfd	[sp]!,{r4,lr}

00000060 e12fff1e*   100 	ret	

                     101 	.endf	updateFromDataSlice

                     102 	.align	4

                     103 ;offset	r0	local

                     104 ;value	r0	local

                     105 

                     106 ;entity	r4	param

                     107 

                     108 	.section ".bss","awb"

                     109 .L56:

                     110 	.data

                     111 	.text


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_88g1.s
                     112 

                     113 ;39:     }


                     114 ;40: }


                     115 

                     116 ;41: 


                     117 ;42: 


                     118 ;43: // Вычисляет и записывает в кэш длину BER и значение


                     119 ;44: static void calcAndCacheLenAndVal(IEDEntity entity)


                     120 	.align	4

                     121 	.align	4

                     122 calcAndCacheLenAndVal:

00000064 e92d4010    123 	stmfd	[sp]!,{r4,lr}

00000068 e1a04000    124 	mov	r4,r0

                     125 ;45: {


                     126 

                     127 ;46:     TerminalItem* extInfo = entity->extInfo;


                     128 

0000006c e5940058    129 	ldr	r0,[r4,88]

                     130 ;47:     IntBoolAccessInfo* accessInfo = extInfo->accessInfo;


                     131 

00000070 e5900000    132 	ldr	r0,[r0]

                     133 ;48:     int32_t enumValue;


                     134 ;49: 


                     135 ;50:     enumValue = getEnumValue(entity->intValue, accessInfo->enumTable,


                     136 

00000074 e280100c    137 	add	r1,r0,12

00000078 e5902008    138 	ldr	r2,[r0,8]

0000007c e5940030    139 	ldr	r0,[r4,48]

00000080 eb000000*   140 	bl	getEnumValue

                     141 ;51:                          accessInfo->enumTableSize);


                     142 ;52: 


                     143 ;53:     entity->cache.enumValue = enumValue;


                     144 

00000084 e584003c    145 	str	r0,[r4,60]

                     146 ;54: 


                     147 ;55: 


                     148 ;56:     entity->cachedBERLen = BerEncoder_Int32DetermineEncodedSize(enumValue) + 2;


                     149 

00000088 eb000000*   150 	bl	BerEncoder_Int32DetermineEncodedSize

0000008c e2800002    151 	add	r0,r0,2

00000090 e5840038    152 	str	r0,[r4,56]

                     153 ;57:     entity->cached = true;


                     154 

00000094 e3a00001    155 	mov	r0,1

00000098 e5c40034    156 	strb	r0,[r4,52]

0000009c e8bd4010    157 	ldmfd	[sp]!,{r4,lr}

000000a0 e12fff1e*   158 	ret	

                     159 	.endf	calcAndCacheLenAndVal

                     160 	.align	4

                     161 ;extInfo	r0	local

                     162 ;accessInfo	r0	local

                     163 

                     164 ;entity	r4	param

                     165 

                     166 	.section ".bss","awb"

                     167 .L97:

                     168 	.data

                     169 	.text

                     170 

                     171 ;58: 


                     172 ;59: }



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_88g1.s
                     173 

                     174 ;60: 


                     175 ;61: static bool calcReadLen(IEDEntity entity, size_t* pLen )


                     176 	.align	4

                     177 	.align	4

                     178 calcReadLen:

000000a4 e92d4030    179 	stmfd	[sp]!,{r4-r5,lr}

000000a8 e1a04000    180 	mov	r4,r0

                     181 ;62: {


                     182 

                     183 ;63:     if(entity->cached)


                     184 

000000ac e5d40034    185 	ldrb	r0,[r4,52]

000000b0 e1a05001    186 	mov	r5,r1

000000b4 e3500000    187 	cmp	r0,0

                     188 ;64:     {


                     189 

                     190 ;65:         *pLen = entity->cachedBERLen;


                     191 

                     192 ;66: 


                     193 ;67:         return true;


                     194 

                     195 ;68:     }


                     196 ;69: 


                     197 ;70:     calcAndCacheLenAndVal(entity);


                     198 

000000b8 01a00004    199 	moveq	r0,r4

000000bc 0bffffe8*   200 	bleq	calcAndCacheLenAndVal

                     201 ;71: 


                     202 ;72:     *pLen = entity->cachedBERLen;


                     203 

                     204 ;73:     return true;


                     205 

000000c0 e5940038    206 	ldr	r0,[r4,56]

000000c4 e5850000    207 	str	r0,[r5]

000000c8 e3a00001    208 	mov	r0,1

000000cc e8bd4030    209 	ldmfd	[sp]!,{r4-r5,lr}

000000d0 e12fff1e*   210 	ret	

                     211 	.endf	calcReadLen

                     212 	.align	4

                     213 

                     214 ;entity	r4	param

                     215 ;pLen	r5	param

                     216 

                     217 	.section ".bss","awb"

                     218 .L147:

                     219 	.data

                     220 	.text

                     221 

                     222 ;74: }


                     223 

                     224 ;75: 


                     225 ;76: static bool encodeRead(IEDEntity entity, BufferView* outBuf)


                     226 	.align	4

                     227 	.align	4

                     228 encodeRead:

000000d4 e92d4030    229 	stmfd	[sp]!,{r4-r5,lr}

000000d8 e24dd004    230 	sub	sp,sp,4

000000dc e1a04000    231 	mov	r4,r0

                     232 ;77: {


                     233 


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_88g1.s
                     234 ;78: 


                     235 ;79:     uint8_t* encodeBuf;


                     236 ;80:     int fullEncodedLen;


                     237 ;81: 


                     238 ;82: 


                     239 ;83:     if(!entity->cached)


                     240 

000000e0 e5d40034    241 	ldrb	r0,[r4,52]

000000e4 e1a05001    242 	mov	r5,r1

000000e8 e3500000    243 	cmp	r0,0

                     244 ;84:     {


                     245 

                     246 ;85:         ERROR_REPORT("Length and value are not cached");


                     247 ;86:         calcAndCacheLenAndVal(entity);


                     248 

000000ec 01a00004    249 	moveq	r0,r4

000000f0 0bffffdb*   250 	bleq	calcAndCacheLenAndVal

000000f4 e1a0200d    251 	mov	r2,sp

000000f8 e5941038    252 	ldr	r1,[r4,56]

000000fc e1a00005    253 	mov	r0,r5

00000100 eb000000*   254 	bl	BufferView_alloc

                     255 ;87:     }


                     256 ;88: 


                     257 ;89:     if(!BufferView_alloc(outBuf,entity->cachedBERLen, &encodeBuf))


                     258 

00000104 e3500000    259 	cmp	r0,0

                     260 ;90:     {


                     261 

                     262 ;91:         ERROR_REPORT("Unable to allocate buffer");


                     263 ;92:         return false;


                     264 

00000108 0a000008    265 	beq	.L160

                     266 ;93:     }


                     267 ;94: 


                     268 ;95:     //Функция возвращает новое смещение в буфере, но поскольку начальное


                     269 ;96:     //смещение 0, можно считать это размером.


                     270 ;97:     fullEncodedLen = BerEncoder_EncodeInt32WithTL(


                     271 

0000010c e59d2000    272 	ldr	r2,[sp]

00000110 e594103c    273 	ldr	r1,[r4,60]

00000114 e3a03000    274 	mov	r3,0

00000118 e3a00085    275 	mov	r0,133

0000011c eb000000*   276 	bl	BerEncoder_EncodeInt32WithTL

                     277 ;98:                 IEC61850_BER_INTEGER, entity->cache.enumValue, encodeBuf, 0);


                     278 ;99: 


                     279 ;100:     outBuf->pos += fullEncodedLen;


                     280 

00000120 e5951004    281 	ldr	r1,[r5,4]

00000124 e0811000    282 	add	r1,r1,r0

00000128 e5851004    283 	str	r1,[r5,4]

                     284 ;101:     return true;


                     285 

0000012c e3a00001    286 	mov	r0,1

                     287 .L160:

00000130 e28dd004    288 	add	sp,sp,4

00000134 e8bd4030    289 	ldmfd	[sp]!,{r4-r5,lr}

00000138 e12fff1e*   290 	ret	

                     291 	.endf	encodeRead

                     292 	.align	4

                     293 ;encodeBuf	[sp]	local

                     294 


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_88g1.s
                     295 ;entity	r4	param

                     296 ;outBuf	r5	param

                     297 

                     298 	.section ".bss","awb"

                     299 .L243:

                     300 	.data

                     301 	.text

                     302 

                     303 ;102: }


                     304 

                     305 ;103: 


                     306 ;104: 


                     307 ;105: void IEDEnum_init(IEDEntity entity)


                     308 	.align	4

                     309 	.align	4

                     310 IEDEnum_init::

0000013c e92d4070    311 	stmfd	[sp]!,{r4-r6,lr}

00000140 e280405c    312 	add	r4,r0,92

00000144 e5140004    313 	ldr	r0,[r4,-4]

                     314 ;108:     IntBoolAccessInfo* accessInfo = extInfo->accessInfo;


                     315 

00000148 e5900000    316 	ldr	r0,[r0]

                     317 ;109: 


                     318 ;110:     //Если будет ошибка, то запишется -1;


                     319 ;111:     entity->dataSliceOffset = DataSlice_getIntOffset(accessInfo->valueOffset);


                     320 

0000014c e59f5028*   321 	ldr	r5,.L293

00000150 e5900004    322 	ldr	r0,[r0,4]

00000154 e59f6024*   323 	ldr	r6,.L294

                     324 ;106: {


                     325 

                     326 ;107:     TerminalItem* extInfo = entity->extInfo;


                     327 

00000158 eb000000*   328 	bl	DataSlice_getIntOffset

0000015c e5040030    329 	str	r0,[r4,-48]

                     330 ;112: 


                     331 ;113: 


                     332 ;114:     entity->calcReadLen = calcReadLen;


                     333 

                     334 ;115:     entity->encodeRead = encodeRead;


                     335 

00000160 e1a00006    336 	mov	r0,r6

00000164 e8840021    337 	stmea	[r4],{r0,r5}

                     338 ;116:     entity->updateFromDataSlice = updateFromDataSlice;


                     339 

00000168 e59f0014*   340 	ldr	r0,.L295

0000016c e584000c    341 	str	r0,[r4,12]

                     342 ;117: 


                     343 ;118:     IEDTree_addToCmpList(entity);


                     344 

00000170 e244005c    345 	sub	r0,r4,92

00000174 e8bd4070    346 	ldmfd	[sp]!,{r4-r6,lr}

00000178 ea000000*   347 	b	IEDTree_addToCmpList

                     348 	.endf	IEDEnum_init

                     349 	.align	4

                     350 ;extInfo	r0	local

                     351 ;accessInfo	r0	local

                     352 

                     353 ;entity	r4	param

                     354 

                     355 	.section ".bss","awb"


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_88g1.s
                     356 .L286:

                     357 	.data

                     358 	.text

                     359 

                     360 ;119: }


                     361 	.align	4

                     362 .L293:

0000017c 00000000*   363 	.data.w	calcReadLen

                     364 	.type	.L293,$object

                     365 	.size	.L293,4

                     366 

                     367 .L294:

00000180 00000000*   368 	.data.w	encodeRead

                     369 	.type	.L294,$object

                     370 	.size	.L294,4

                     371 

                     372 .L295:

00000184 00000000*   373 	.data.w	updateFromDataSlice

                     374 	.type	.L295,$object

                     375 	.size	.L295,4

                     376 

                     377 	.align	4

                     378 

                     379 	.data

                     380 	.ghsnote version,6

                     381 	.ghsnote tools,3

                     382 	.ghsnote options,0

                     383 	.text

                     384 	.align	4

