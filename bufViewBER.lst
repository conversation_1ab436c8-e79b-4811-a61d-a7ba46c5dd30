                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9h41.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=bufViewBER.c -o gh_9h41.o -list=bufViewBER.lst C:\Users\<USER>\AppData\Local\Temp\gh_9h41.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_9h41.s
Source File: bufViewBER.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile bufViewBER.c

                      10 ;		-o bufViewBER.o

                      11 ;Source File:   bufViewBER.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:55:55 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "bufViewBER.h"


                      21 ;2: #include "AsnEncoding.h"


                      22 ;3: 


                      23 ;4: bool BufferView_peekTag(BufferView* bv, uint8_t* tag)


                      24 	.text

                      25 	.align	4

                      26 BufferView_peekTag::

                      27 ;5: {


                      28 

                      29 ;6:     if (bv->pos < bv->len)


                      30 

00000000 e990000c     31 	ldmed	[r0],{r2-r3}

00000004 e1520003     32 	cmp	r2,r3

                      33 ;7:     {


                      34 

                      35 ;8:         *tag = bv->p[bv->pos];


                      36 

00000008 35900000     37 	ldrlo	r0,[r0]

0000000c 37d00002     38 	ldrlob	r0,[r0,r2]

00000010 35c10000     39 	strlob	r0,[r1]

                      40 ;9:         return TRUE;


                      41 

00000014 33a00001     42 	movlo	r0,1

                      43 ;10:     }


                      44 ;11:     else


                      45 ;12:     {


                      46 

                      47 ;13:         return FALSE;


                      48 

00000018 23a00000     49 	movhs	r0,0

0000001c e12fff1e*    50 	ret	


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9h41.s
                      51 	.endf	BufferView_peekTag

                      52 	.align	4

                      53 

                      54 ;bv	r0	param

                      55 ;tag	r1	param

                      56 

                      57 	.section ".bss","awb"

                      58 .L54:

                      59 	.data

                      60 	.text

                      61 

                      62 ;14:     }


                      63 ;15: }


                      64 

                      65 ;16: 


                      66 ;17: bool BufferView_skipObject(BufferView* bv, uint8_t tag, bool strict)


                      67 	.align	4

                      68 	.align	4

                      69 BufferView_skipObject::

00000020 e92d4070     70 	stmfd	[sp]!,{r4-r6,lr}

00000024 e1a06002     71 	mov	r6,r2

00000028 e24dd008     72 	sub	sp,sp,8

0000002c e1a04001     73 	mov	r4,r1

00000030 e28d1003     74 	add	r1,sp,3

00000034 e1a05000     75 	mov	r5,r0

00000038 ebfffff0*    76 	bl	BufferView_peekTag

                      77 ;18: {


                      78 

                      79 ;19:     uint8_t objTag;


                      80 ;20:     size_t len;


                      81 ;21: 


                      82 ;22:     if (!BufferView_peekTag(bv, &objTag))


                      83 

0000003c e3500000     84 	cmp	r0,0

                      85 ;23:     {


                      86 

                      87 ;24:         return FALSE;


                      88 

00000040 0a000014     89 	beq	.L67

                      90 ;25:     }


                      91 ;26:     if (objTag != tag)


                      92 

00000044 e5dd0003     93 	ldrb	r0,[sp,3]

00000048 e1500004     94 	cmp	r0,r4

0000004c 0a000003     95 	beq	.L72

                      96 ;27:     {


                      97 

                      98 ;28:         return !strict;


                      99 

00000050 e3560000    100 	cmp	r6,0

00000054 03a00001    101 	moveq	r0,1

00000058 13a00000    102 	movne	r0,0

0000005c ea00000d    103 	b	.L67

                     104 .L72:

                     105 ;29:     }


                     106 ;30:     return BufferView_decodeTL(bv, NULL, &len, NULL)


                     107 

00000060 e28d2004    108 	add	r2,sp,4

00000064 e1a00005    109 	mov	r0,r5

00000068 e3a04000    110 	mov	r4,0

0000006c e1a03004    111 	mov	r3,r4


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9h41.s
00000070 e1a01004    112 	mov	r1,r4

00000074 eb00001b*   113 	bl	BufferView_decodeTL

00000078 e3500000    114 	cmp	r0,0

0000007c 0a000004    115 	beq	.L76

00000080 e59d1004    116 	ldr	r1,[sp,4]

00000084 e1a00005    117 	mov	r0,r5

00000088 eb000000*   118 	bl	BufferView_advance

0000008c e3500000    119 	cmp	r0,0

00000090 13a04001    120 	movne	r4,1

                     121 .L76:

00000094 e20400ff    122 	and	r0,r4,255

                     123 .L67:

00000098 e28dd008    124 	add	sp,sp,8

0000009c e8bd8070    125 	ldmfd	[sp]!,{r4-r6,pc}

                     126 	.endf	BufferView_skipObject

                     127 	.align	4

                     128 ;objTag	[sp,3]	local

                     129 ;len	[sp,4]	local

                     130 

                     131 ;bv	r5	param

                     132 ;tag	r4	param

                     133 ;strict	r6	param

                     134 

                     135 	.section ".bss","awb"

                     136 .L176:

                     137 	.data

                     138 	.text

                     139 

                     140 ;31:         && BufferView_advance(bv, len);


                     141 ;32: }


                     142 

                     143 ;33: 


                     144 ;34: bool BufferView_skipAnyObject(BufferView* bv)


                     145 	.align	4

                     146 	.align	4

                     147 BufferView_skipAnyObject::

000000a0 e92d4030    148 	stmfd	[sp]!,{r4-r5,lr}

                     149 ;35: {


                     150 

                     151 ;36:     size_t len;


                     152 ;37:     return BufferView_decodeTL(bv, NULL, &len, NULL)


                     153 

000000a4 e24dd004    154 	sub	sp,sp,4

000000a8 e1a0200d    155 	mov	r2,sp

000000ac e1a05000    156 	mov	r5,r0

000000b0 e3a04000    157 	mov	r4,0

000000b4 e1a03004    158 	mov	r3,r4

000000b8 e1a01004    159 	mov	r1,r4

000000bc eb000009*   160 	bl	BufferView_decodeTL

000000c0 e3500000    161 	cmp	r0,0

000000c4 0a000004    162 	beq	.L207

000000c8 e59d1000    163 	ldr	r1,[sp]

000000cc e1a00005    164 	mov	r0,r5

000000d0 eb000000*   165 	bl	BufferView_advance

000000d4 e3500000    166 	cmp	r0,0

000000d8 13a04001    167 	movne	r4,1

                     168 .L207:

000000dc e20400ff    169 	and	r0,r4,255

000000e0 e28dd004    170 	add	sp,sp,4

000000e4 e8bd8030    171 	ldmfd	[sp]!,{r4-r5,pc}

                     172 	.endf	BufferView_skipAnyObject


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9h41.s
                     173 	.align	4

                     174 ;len	[sp]	local

                     175 

                     176 ;bv	r5	param

                     177 

                     178 	.section ".bss","awb"

                     179 .L266:

                     180 	.data

                     181 	.text

                     182 

                     183 ;38:         && BufferView_advance(bv, len);


                     184 ;39: }


                     185 

                     186 ;40: 


                     187 ;41: bool BufferView_decodeTL(BufferView* bv, uint8_t* pTag, size_t* pLen, size_t* pFullLen)


                     188 	.align	4

                     189 	.align	4

                     190 BufferView_decodeTL::

000000e8 e92d4cf0    191 	stmfd	[sp]!,{r4-r7,r10-fp,lr}

000000ec e24dd004    192 	sub	sp,sp,4

000000f0 e1a04000    193 	mov	r4,r0

000000f4 e1a06001    194 	mov	r6,r1

000000f8 e1a07002    195 	mov	r7,r2

000000fc e1a0a003    196 	mov	r10,r3

                     197 ;42: {


                     198 

                     199 ;43:     uint8_t tag;


                     200 ;44:     int len;


                     201 ;45:     int objPos = bv->pos;


                     202 

00000100 e9940009    203 	ldmed	[r4],{r0,r3}

00000104 e1a05000    204 	mov	r5,r0

                     205 ;46: 


                     206 ;47:     if (bv->len - bv->pos < 2)


                     207 

00000108 e0430005    208 	sub	r0,r3,r5

0000010c e3500002    209 	cmp	r0,2

00000110 3a000008    210 	blo	.L290

                     211 ;48:     {


                     212 

                     213 ;49:         return FALSE;


                     214 

                     215 ;50:     }


                     216 ;51: 


                     217 ;52:     tag = BufferView_readTag(bv);


                     218 

00000114 e5940000    219 	ldr	r0,[r4]

00000118 e2852001    220 	add	r2,r5,1

0000011c e5842004    221 	str	r2,[r4,4]

00000120 e7d0b005    222 	ldrb	fp,[r0,r5]

                     223 ;53:     bv->pos = BerDecoder_decodeLength(bv->p, &len, bv->pos, bv->len);


                     224 

00000124 e1a0100d    225 	mov	r1,sp

00000128 eb000000*   226 	bl	BerDecoder_decodeLength

0000012c e5840004    227 	str	r0,[r4,4]

                     228 ;54:     if (bv->pos <= 0)


                     229 

00000130 e3500000    230 	cmp	r0,0

00000134 1a000001    231 	bne	.L289

                     232 .L290:

                     233 ;55:     {



                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9h41.s
                     234 

                     235 ;56:         return FALSE;


                     236 

00000138 e3a00000    237 	mov	r0,0

0000013c ea00000b    238 	b	.L283

                     239 .L289:

                     240 ;57:     }


                     241 ;58: 


                     242 ;59:     if (pTag != NULL)


                     243 

00000140 e3560000    244 	cmp	r6,0

                     245 ;60:     {


                     246 

                     247 ;61:         *pTag = tag;


                     248 

00000144 15c6b000    249 	strneb	fp,[r6]

                     250 ;62:     }


                     251 ;63: 


                     252 ;64:     if (pLen != NULL)


                     253 

00000148 e3570000    254 	cmp	r7,0

                     255 ;65:     {


                     256 

                     257 ;66:         *pLen = len;


                     258 

0000014c 159d0000    259 	ldrne	r0,[sp]

00000150 15870000    260 	strne	r0,[r7]

                     261 ;67:     }


                     262 ;68: 


                     263 ;69:     if (pFullLen != NULL)


                     264 

00000154 e35a0000    265 	cmp	r10,0

                     266 ;70:     {


                     267 

                     268 ;71:         *pFullLen = bv->pos - objPos + len;


                     269 

00000158 15940004    270 	ldrne	r0,[r4,4]

0000015c 159d1000    271 	ldrne	r1,[sp]

00000160 10400005    272 	subne	r0,r0,r5

00000164 10810000    273 	addne	r0,r1,r0

00000168 158a0000    274 	strne	r0,[r10]

                     275 ;72:     }


                     276 ;73: 


                     277 ;74:     return TRUE;


                     278 

0000016c e3a00001    279 	mov	r0,1

                     280 .L283:

00000170 e28dd004    281 	add	sp,sp,4

00000174 e8bd8cf0    282 	ldmfd	[sp]!,{r4-r7,r10-fp,pc}

                     283 	.endf	BufferView_decodeTL

                     284 	.align	4

                     285 ;tag	fp	local

                     286 ;len	[sp]	local

                     287 ;objPos	r5	local

                     288 

                     289 ;bv	r4	param

                     290 ;pTag	r6	param

                     291 ;pLen	r7	param

                     292 ;pFullLen	r10	param

                     293 

                     294 	.section ".bss","awb"


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9h41.s
                     295 .L390:

                     296 	.data

                     297 	.text

                     298 

                     299 ;75: }


                     300 

                     301 ;76: 


                     302 ;77: bool BufferView_decodeExtTL(BufferView* bv, uint32_t* pTag, size_t* pLen, size_t* pFullLen)


                     303 	.align	4

                     304 	.align	4

                     305 BufferView_decodeExtTL::

00000178 e92d4cf0    306 	stmfd	[sp]!,{r4-r7,r10-fp,lr}

0000017c e24dd004    307 	sub	sp,sp,4

00000180 e1a04000    308 	mov	r4,r0

00000184 e1a0b001    309 	mov	fp,r1

00000188 e1a07002    310 	mov	r7,r2

0000018c e1a0a003    311 	mov	r10,r3

                     312 ;78: {


                     313 

                     314 ;79:     uint32_t tag;


                     315 ;80:     //Запоминаем начало объекта чтобы потом посчитать полную длину


                     316 ;81:     size_t objPos = bv->pos;


                     317 

00000190 e9940009    318 	ldmed	[r4],{r0,r3}

00000194 e1a06000    319 	mov	r6,r0

                     320 ;82:     size_t len;


                     321 ;83:     int newPos;


                     322 ;84: 


                     323 ;85: 


                     324 ;86:     if (bv->len - bv->pos < 2)


                     325 

00000198 e0430006    326 	sub	r0,r3,r6

0000019c e3500002    327 	cmp	r0,2

000001a0 3a000011    328 	blo	.L433

                     329 ;87:     {


                     330 

                     331 ;88:         return false;


                     332 

                     333 ;89:     }


                     334 ;90: 


                     335 ;91:     tag = BufferView_readTag(bv);


                     336 

000001a4 e5940000    337 	ldr	r0,[r4]

000001a8 e2861001    338 	add	r1,r6,1

000001ac e5841004    339 	str	r1,[r4,4]

000001b0 e7d05006    340 	ldrb	r5,[r0,r6]

                     341 ;92:     if ((tag & 0x1f) == 0x1f)


                     342 

000001b4 e205201f    343 	and	r2,r5,31

000001b8 e352001f    344 	cmp	r2,31

000001bc 1a000005    345 	bne	.L426

                     346 ;93:     {


                     347 

                     348 ;94:         //Multibyte tag


                     349 ;95:         tag <<= 8;


                     350 

                     351 ;96:         tag |= BufferView_readTag(bv);


                     352 

000001c0 e2812001    353 	add	r2,r1,1

000001c4 e5842004    354 	str	r2,[r4,4]

000001c8 e7d01001    355 	ldrb	r1,[r0,r1]


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9h41.s
000001cc e1815405    356 	orr	r5,r1,r5 lsl 8

                     357 ;97:         if (tag & 0x80)


                     358 

000001d0 e3150080    359 	tst	r5,128

000001d4 1a000004    360 	bne	.L433

                     361 .L426:

                     362 ;98:         {


                     363 

                     364 ;99:             //Тэг больше двух байт. С такими пока не работаем.


                     365 ;100:             return false;


                     366 

                     367 ;101:         }


                     368 ;102:     }


                     369 ;103: 


                     370 ;104:     newPos = BerDecoder_decodeLength(bv->p, (int*)&len, bv->pos, bv->len);


                     371 

000001d8 e5942004    372 	ldr	r2,[r4,4]

000001dc e1a0100d    373 	mov	r1,sp

000001e0 eb000000*   374 	bl	BerDecoder_decodeLength

                     375 ;105:     if (newPos <= 0)


                     376 

000001e4 e3500000    377 	cmp	r0,0

000001e8 ca000001    378 	bgt	.L432

                     379 .L433:

                     380 ;106:     {


                     381 

                     382 ;107:         return false;


                     383 

000001ec e3a00000    384 	mov	r0,0

000001f0 ea00000c    385 	b	.L420

                     386 .L432:

                     387 ;108:     }


                     388 ;109:     bv->pos = newPos;


                     389 

000001f4 e5840004    390 	str	r0,[r4,4]

                     391 ;110: 


                     392 ;111:     if (pTag != NULL)


                     393 

000001f8 e35b0000    394 	cmp	fp,0

                     395 ;112:     {


                     396 

                     397 ;113:         *pTag = tag;


                     398 

000001fc 158b5000    399 	strne	r5,[fp]

                     400 ;114:     }


                     401 ;115: 


                     402 ;116:     if (pLen != NULL)


                     403 

00000200 e3570000    404 	cmp	r7,0

                     405 ;117:     {


                     406 

                     407 ;118:         *pLen = len;


                     408 

00000204 159d0000    409 	ldrne	r0,[sp]

00000208 15870000    410 	strne	r0,[r7]

                     411 ;119:     }


                     412 ;120: 


                     413 ;121:     if (pFullLen != NULL)


                     414 

0000020c e35a0000    415 	cmp	r10,0

                     416 ;122:     {



                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9h41.s
                     417 

                     418 ;123:         *pFullLen = bv->pos - objPos + len;


                     419 

00000210 15940004    420 	ldrne	r0,[r4,4]

00000214 10401006    421 	subne	r1,r0,r6

00000218 159d0000    422 	ldrne	r0,[sp]

0000021c 10800001    423 	addne	r0,r0,r1

00000220 158a0000    424 	strne	r0,[r10]

                     425 ;124:     }


                     426 ;125: 


                     427 ;126:     return true;


                     428 

00000224 e3a00001    429 	mov	r0,1

                     430 .L420:

00000228 e28dd004    431 	add	sp,sp,4

0000022c e8bd8cf0    432 	ldmfd	[sp]!,{r4-r7,r10-fp,pc}

                     433 	.endf	BufferView_decodeExtTL

                     434 	.align	4

                     435 ;tag	r5	local

                     436 ;objPos	r6	local

                     437 ;len	[sp]	local

                     438 ;newPos	r0	local

                     439 

                     440 ;bv	r4	param

                     441 ;pTag	fp	param

                     442 ;pLen	r7	param

                     443 ;pFullLen	r10	param

                     444 

                     445 	.section ".bss","awb"

                     446 .L554:

                     447 	.data

                     448 	.text

                     449 

                     450 ;127: }


                     451 

                     452 ;128: 


                     453 ;129: bool BufferView_decodeInt32(BufferView* bv, size_t intLen, int32_t* result)


                     454 	.align	4

                     455 	.align	4

                     456 BufferView_decodeInt32::

00000230 e92d4070    457 	stmfd	[sp]!,{r4-r6,lr}

00000234 e1a04000    458 	mov	r4,r0

00000238 e1a05001    459 	mov	r5,r1

0000023c e1a06002    460 	mov	r6,r2

                     461 ;130: {


                     462 

                     463 ;131:     if (bv->pos + intLen > bv->len)


                     464 

00000240 e994000c    465 	ldmed	[r4],{r2-r3}

00000244 e0820005    466 	add	r0,r2,r5

00000248 e1500003    467 	cmp	r0,r3

                     468 ;132:     {


                     469 

                     470 ;133:         return false;


                     471 

0000024c 83a00000    472 	movhi	r0,0

00000250 8a000006    473 	bhi	.L590

                     474 ;134:     }


                     475 ;135:     *result = BerDecoder_decodeInt32(bv->p, intLen, bv->pos);


                     476 

00000254 e5940000    477 	ldr	r0,[r4]


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9h41.s
00000258 eb000000*   478 	bl	BerDecoder_decodeInt32

0000025c e5860000    479 	str	r0,[r6]

                     480 ;136:     bv->pos += intLen;


                     481 

00000260 e5940004    482 	ldr	r0,[r4,4]

00000264 e0800005    483 	add	r0,r0,r5

00000268 e5840004    484 	str	r0,[r4,4]

                     485 ;137:     return true;


                     486 

0000026c e3a00001    487 	mov	r0,1

                     488 .L590:

00000270 e8bd8070    489 	ldmfd	[sp]!,{r4-r6,pc}

                     490 	.endf	BufferView_decodeInt32

                     491 	.align	4

                     492 

                     493 ;bv	r4	param

                     494 ;intLen	r5	param

                     495 ;result	r6	param

                     496 

                     497 	.section ".bss","awb"

                     498 .L630:

                     499 	.data

                     500 	.text

                     501 

                     502 ;138: }


                     503 

                     504 ;139: 


                     505 ;140: bool BufferView_decodeUInt32(BufferView* bv, size_t intLen, uint32_t* result)


                     506 	.align	4

                     507 	.align	4

                     508 BufferView_decodeUInt32::

00000274 e92d4070    509 	stmfd	[sp]!,{r4-r6,lr}

00000278 e1a04000    510 	mov	r4,r0

0000027c e1a05001    511 	mov	r5,r1

00000280 e1a06002    512 	mov	r6,r2

                     513 ;141: {


                     514 

                     515 ;142:     if (bv->pos + intLen > bv->len)


                     516 

00000284 e994000c    517 	ldmed	[r4],{r2-r3}

00000288 e0820005    518 	add	r0,r2,r5

0000028c e1500003    519 	cmp	r0,r3

                     520 ;143:     {


                     521 

                     522 ;144:         return FALSE;


                     523 

00000290 83a00000    524 	movhi	r0,0

00000294 8a000006    525 	bhi	.L644

                     526 ;145:     }


                     527 ;146:     *result = BerDecoder_decodeUint32(bv->p, intLen, bv->pos);


                     528 

00000298 e5940000    529 	ldr	r0,[r4]

0000029c eb000000*   530 	bl	BerDecoder_decodeUint32

000002a0 e5860000    531 	str	r0,[r6]

                     532 ;147:     bv->pos += intLen;


                     533 

000002a4 e5940004    534 	ldr	r0,[r4,4]

000002a8 e0800005    535 	add	r0,r0,r5

000002ac e5840004    536 	str	r0,[r4,4]

                     537 ;148:     return TRUE;


                     538 


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9h41.s
000002b0 e3a00001    539 	mov	r0,1

                     540 .L644:

000002b4 e8bd8070    541 	ldmfd	[sp]!,{r4-r6,pc}

                     542 	.endf	BufferView_decodeUInt32

                     543 	.align	4

                     544 

                     545 ;bv	r4	param

                     546 ;intLen	r5	param

                     547 ;result	r6	param

                     548 

                     549 	.section ".bss","awb"

                     550 .L678:

                     551 	.data

                     552 	.text

                     553 

                     554 ;149: }


                     555 

                     556 ;150: 


                     557 ;151: bool BufferView_decodeInt32TL(BufferView* bv, uint8_t expectedTag,


                     558 	.align	4

                     559 	.align	4

                     560 BufferView_decodeInt32TL::

000002b8 e92d4070    561 	stmfd	[sp]!,{r4-r6,lr}

                     562 ;152:     int32_t* result)


                     563 ;153: {


                     564 

                     565 ;154:     uint8_t tag;


                     566 ;155:     size_t intLen;


                     567 ;156:     if (!BufferView_decodeTL(bv, &tag, &intLen, NULL) || tag != expectedTag)


                     568 

000002bc e1a06002    569 	mov	r6,r2

000002c0 e24dd008    570 	sub	sp,sp,8

000002c4 e28d2004    571 	add	r2,sp,4

000002c8 e1a05001    572 	mov	r5,r1

000002cc e28d1003    573 	add	r1,sp,3

000002d0 e1a04000    574 	mov	r4,r0

000002d4 e3a03000    575 	mov	r3,0

000002d8 ebffff82*   576 	bl	BufferView_decodeTL

000002dc e3500000    577 	cmp	r0,0

000002e0 0a000002    578 	beq	.L695

000002e4 e5dd0003    579 	ldrb	r0,[sp,3]

000002e8 e1500005    580 	cmp	r0,r5

000002ec 0a000001    581 	beq	.L694

                     582 .L695:

                     583 ;157:     {


                     584 

                     585 ;158:         return false;


                     586 

000002f0 e3a00000    587 	mov	r0,0

000002f4 ea000003    588 	b	.L692

                     589 .L694:

                     590 ;159:     }


                     591 ;160: 


                     592 ;161:     return BufferView_decodeInt32(bv, intLen, result);


                     593 

000002f8 e1a02006    594 	mov	r2,r6

000002fc e59d1004    595 	ldr	r1,[sp,4]

00000300 e1a00004    596 	mov	r0,r4

00000304 ebffffc9*   597 	bl	BufferView_decodeInt32

                     598 .L692:

00000308 e28dd008    599 	add	sp,sp,8


                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9h41.s
0000030c e8bd8070    600 	ldmfd	[sp]!,{r4-r6,pc}

                     601 	.endf	BufferView_decodeInt32TL

                     602 	.align	4

                     603 ;tag	[sp,3]	local

                     604 ;intLen	[sp,4]	local

                     605 

                     606 ;bv	r4	param

                     607 ;expectedTag	r5	param

                     608 ;result	r6	param

                     609 

                     610 	.section ".bss","awb"

                     611 .L746:

                     612 	.data

                     613 	.text

                     614 

                     615 ;162: }


                     616 

                     617 ;163: 


                     618 ;164: bool BufferView_decodeUInt32TL(BufferView* bv, uint8_t expectedTag,


                     619 	.align	4

                     620 	.align	4

                     621 BufferView_decodeUInt32TL::

00000310 e92d4070    622 	stmfd	[sp]!,{r4-r6,lr}

                     623 ;165:     uint32_t* result)


                     624 ;166: {


                     625 

                     626 ;167:     uint8_t tag;


                     627 ;168:     size_t intLen;


                     628 ;169:     if (!BufferView_decodeTL(bv, &tag, &intLen, NULL) || tag != expectedTag)


                     629 

00000314 e1a06002    630 	mov	r6,r2

00000318 e24dd008    631 	sub	sp,sp,8

0000031c e28d2004    632 	add	r2,sp,4

00000320 e1a05001    633 	mov	r5,r1

00000324 e28d1003    634 	add	r1,sp,3

00000328 e1a04000    635 	mov	r4,r0

0000032c e3a03000    636 	mov	r3,0

00000330 ebffff6c*   637 	bl	BufferView_decodeTL

00000334 e3500000    638 	cmp	r0,0

00000338 0a000002    639 	beq	.L762

0000033c e5dd0003    640 	ldrb	r0,[sp,3]

00000340 e1500005    641 	cmp	r0,r5

00000344 0a000001    642 	beq	.L761

                     643 .L762:

                     644 ;170:     {


                     645 

                     646 ;171:         return FALSE;


                     647 

00000348 e3a00000    648 	mov	r0,0

0000034c ea000003    649 	b	.L759

                     650 .L761:

                     651 ;172:     }


                     652 ;173: 


                     653 ;174:     return BufferView_decodeUInt32(bv, intLen, result);


                     654 

00000350 e1a02006    655 	mov	r2,r6

00000354 e59d1004    656 	ldr	r1,[sp,4]

00000358 e1a00004    657 	mov	r0,r4

0000035c ebffffc4*   658 	bl	BufferView_decodeUInt32

                     659 .L759:

00000360 e28dd008    660 	add	sp,sp,8


                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9h41.s
00000364 e8bd8070    661 	ldmfd	[sp]!,{r4-r6,pc}

                     662 	.endf	BufferView_decodeUInt32TL

                     663 	.align	4

                     664 ;tag	[sp,3]	local

                     665 ;intLen	[sp,4]	local

                     666 

                     667 ;bv	r4	param

                     668 ;expectedTag	r5	param

                     669 ;result	r6	param

                     670 

                     671 	.section ".bss","awb"

                     672 .L810:

                     673 	.data

                     674 	.text

                     675 

                     676 ;175: }


                     677 

                     678 ;176: 


                     679 ;177: bool BufferView_decodeStringViewTL(BufferView* bv, uint8_t expectedTag,


                     680 	.align	4

                     681 	.align	4

                     682 BufferView_decodeStringViewTL::

00000368 e92d4070    683 	stmfd	[sp]!,{r4-r6,lr}

                     684 ;178:     StringView* result)


                     685 ;179: {


                     686 

                     687 ;180:     uint8_t tag;


                     688 ;181:     size_t len;


                     689 ;182:     if (!BufferView_decodeTL(bv, &tag, &len, NULL) || tag != expectedTag)


                     690 

0000036c e1a05002    691 	mov	r5,r2

00000370 e24dd008    692 	sub	sp,sp,8

00000374 e28d2004    693 	add	r2,sp,4

00000378 e1a06001    694 	mov	r6,r1

0000037c e28d1003    695 	add	r1,sp,3

00000380 e1a04000    696 	mov	r4,r0

00000384 e3a03000    697 	mov	r3,0

00000388 ebffff56*   698 	bl	BufferView_decodeTL

0000038c e3500000    699 	cmp	r0,0

00000390 0a000002    700 	beq	.L826

00000394 e5dd0003    701 	ldrb	r0,[sp,3]

00000398 e1500006    702 	cmp	r0,r6

0000039c 0a000001    703 	beq	.L825

                     704 .L826:

                     705 ;183:     {


                     706 

                     707 ;184:         return FALSE;


                     708 

000003a0 e3a00000    709 	mov	r0,0

000003a4 ea000009    710 	b	.L823

                     711 .L825:

                     712 ;185:     }


                     713 ;186:     StringView_init(result, (const char*)bv->p + bv->pos, len);


                     714 

000003a8 e59d2004    715 	ldr	r2,[sp,4]

000003ac e894000a    716 	ldmfd	[r4],{r1,r3}

000003b0 e1a00005    717 	mov	r0,r5

000003b4 e0831001    718 	add	r1,r3,r1

000003b8 eb000000*   719 	bl	StringView_init

                     720 ;187:     bv->pos += len;


                     721 


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9h41.s
000003bc e5940004    722 	ldr	r0,[r4,4]

000003c0 e59d1004    723 	ldr	r1,[sp,4]

000003c4 e0800001    724 	add	r0,r0,r1

000003c8 e5840004    725 	str	r0,[r4,4]

                     726 ;188:     return TRUE;


                     727 

000003cc e3a00001    728 	mov	r0,1

                     729 .L823:

000003d0 e28dd008    730 	add	sp,sp,8

000003d4 e8bd8070    731 	ldmfd	[sp]!,{r4-r6,pc}

                     732 	.endf	BufferView_decodeStringViewTL

                     733 	.align	4

                     734 ;tag	[sp,3]	local

                     735 ;len	[sp,4]	local

                     736 

                     737 ;bv	r4	param

                     738 ;expectedTag	r6	param

                     739 ;result	r5	param

                     740 

                     741 	.section ".bss","awb"

                     742 .L868:

                     743 	.data

                     744 	.text

                     745 

                     746 ;189: }


                     747 

                     748 ;190: 


                     749 ;191: bool BufferView_writeTag(BufferView* bv, uint8_t tag)


                     750 	.align	4

                     751 	.align	4

                     752 BufferView_writeTag::

000003d8 e92d0100    753 	stmfd	[sp]!,{r8}

                     754 ;192: {


                     755 

                     756 ;193:     if (bv->pos + 1 > bv->len)


                     757 

000003dc e9901100    758 	ldmed	[r0],{r8,r12}

000003e0 e2882001    759 	add	r2,r8,1

000003e4 e1a0300c    760 	mov	r3,r12

000003e8 e1520003    761 	cmp	r2,r3

                     762 ;194:     {


                     763 

                     764 ;195:         //Не лезет в буфер


                     765 ;196:         return FALSE;


                     766 

000003ec 83a00000    767 	movhi	r0,0

                     768 ;197:     }


                     769 ;198:     bv->p[bv->pos++] = tag;


                     770 

000003f0 95903000    771 	ldrls	r3,[r0]

000003f4 95802004    772 	strls	r2,[r0,4]

000003f8 97c31008    773 	strlsb	r1,[r3,r8]

                     774 ;199:     return TRUE;


                     775 

000003fc 93a00001    776 	movls	r0,1

00000400 e8bd0100    777 	ldmfd	[sp]!,{r8}

00000404 e12fff1e*   778 	ret	

                     779 	.endf	BufferView_writeTag

                     780 	.align	4

                     781 

                     782 ;bv	r0	param


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9h41.s
                     783 ;tag	r1	param

                     784 

                     785 	.section ".bss","awb"

                     786 .L918:

                     787 	.data

                     788 	.text

                     789 

                     790 ;200: }


                     791 

                     792 ;201: 


                     793 ;202: bool BufferView_encodeStringView(BufferView* bv, uint8_t tag,


                     794 	.align	4

                     795 	.align	4

                     796 BufferView_encodeStringView::

00000408 e92d4070    797 	stmfd	[sp]!,{r4-r6,lr}

0000040c e1a04000    798 	mov	r4,r0

00000410 e1a05002    799 	mov	r5,r2

                     800 ;203:     const StringView* strView)


                     801 ;204: {


                     802 

                     803 ;205:     size_t fullSize = BerEncoder_determineFullObjectSize(strView->len);


                     804 

00000414 e5950000    805 	ldr	r0,[r5]

00000418 e1a06001    806 	mov	r6,r1

0000041c eb000000*   807 	bl	BerEncoder_determineFullObjectSize

                     808 ;206:     size_t freeSpace = bv->len - bv->pos;


                     809 

00000420 e9940006    810 	ldmed	[r4],{r1-r2}

00000424 e0422001    811 	sub	r2,r2,r1

                     812 ;207:     if (fullSize > freeSpace)


                     813 

00000428 e1500002    814 	cmp	r0,r2

                     815 ;208:     {


                     816 

                     817 ;209:         return FALSE;


                     818 

0000042c 83a00000    819 	movhi	r0,0

00000430 8a000014    820 	bhi	.L931

                     821 ;210:     }


                     822 ;211:     //Пишем тэг


                     823 ;212:     bv->p[bv->pos++] = tag;


                     824 

00000434 e5940000    825 	ldr	r0,[r4]

00000438 e2812001    826 	add	r2,r1,1

0000043c e5842004    827 	str	r2,[r4,4]

00000440 e7c06001    828 	strb	r6,[r0,r1]

00000444 e5950000    829 	ldr	r0,[r5]

00000448 e1b03000    830 	movs	r3,r0

                     831 ;213: 


                     832 ;214:     if (strView->len == 0)


                     833 

0000044c e8940006    834 	ldmfd	[r4],{r1-r2}

00000450 1a000006    835 	bne	.L936

                     836 ;215:     {


                     837 

                     838 ;216:         //Пишем длину


                     839 ;217:         bv->p[bv->pos++] = 0;


                     840 

00000454 e2820001    841 	add	r0,r2,1

00000458 e5840004    842 	str	r0,[r4,4]

0000045c e7c13002    843 	strb	r3,[r1,r2]


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9h41.s
                     844 ;224:     }


                     845 ;225:     return BufferView_writeStringView(bv, strView);


                     846 

00000460 e1a01005    847 	mov	r1,r5

00000464 e1a00004    848 	mov	r0,r4

00000468 e8bd4070    849 	ldmfd	[sp]!,{r4-r6,lr}

0000046c ea000000*   850 	b	BufferView_writeStringView

                     851 .L936:

                     852 ;218:         //Больше писать нечего - пустая строка


                     853 ;219:     }


                     854 ;220:     else


                     855 ;221:     {


                     856 

                     857 ;222:         //Пишем длину


                     858 ;223:         bv->pos = BerEncoder_encodeLength(strView->len, bv->p, bv->pos);


                     859 

00000470 eb000000*   860 	bl	BerEncoder_encodeLength

00000474 e1a01005    861 	mov	r1,r5

00000478 e5840004    862 	str	r0,[r4,4]

                     863 ;224:     }


                     864 ;225:     return BufferView_writeStringView(bv, strView);


                     865 

0000047c e1a00004    866 	mov	r0,r4

00000480 e8bd4070    867 	ldmfd	[sp]!,{r4-r6,lr}

00000484 ea000000*   868 	b	BufferView_writeStringView

                     869 .L931:

00000488 e8bd8070    870 	ldmfd	[sp]!,{r4-r6,pc}

                     871 	.endf	BufferView_encodeStringView

                     872 	.align	4

                     873 ;freeSpace	r2	local

                     874 

                     875 ;bv	r4	param

                     876 ;tag	r6	param

                     877 ;strView	r5	param

                     878 

                     879 	.section ".bss","awb"

                     880 .L1010:

                     881 	.data

                     882 	.text

                     883 

                     884 ;226: }


                     885 

                     886 ;227: 


                     887 ;228: bool BufferView_encodeStr(BufferView* bv, uint8_t tag, const char* str)


                     888 	.align	4

                     889 	.align	4

                     890 BufferView_encodeStr::

0000048c e92d4030    891 	stmfd	[sp]!,{r4-r5,lr}

                     892 ;229: {


                     893 

                     894 ;230:     StringView strView;


                     895 ;231:     StringView_fromCStr(&strView, (char*)str);


                     896 

00000490 e1a05001    897 	mov	r5,r1

00000494 e1a01002    898 	mov	r1,r2

00000498 e24dd008    899 	sub	sp,sp,8

0000049c e1a04000    900 	mov	r4,r0

000004a0 e1a0000d    901 	mov	r0,sp

000004a4 eb000000*   902 	bl	StringView_fromCStr

                     903 ;232:     return BufferView_encodeStringView(bv, tag, &strView);


                     904 


                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9h41.s
000004a8 e1a0200d    905 	mov	r2,sp

000004ac e1a01005    906 	mov	r1,r5

000004b0 e1a00004    907 	mov	r0,r4

000004b4 ebffffd3*   908 	bl	BufferView_encodeStringView

000004b8 e28dd008    909 	add	sp,sp,8

000004bc e8bd8030    910 	ldmfd	[sp]!,{r4-r5,pc}

                     911 	.endf	BufferView_encodeStr

                     912 	.align	4

                     913 ;strView	[sp]	local

                     914 

                     915 ;bv	r4	param

                     916 ;tag	r5	param

                     917 ;str	r2	param

                     918 

                     919 	.section ".bss","awb"

                     920 .L1057:

                     921 	.data

                     922 	.text

                     923 

                     924 ;233: }


                     925 

                     926 ;234: 


                     927 ;235: bool BufferView_encodeTL(BufferView* bv, uint8_t tag, size_t length)


                     928 	.align	4

                     929 	.align	4

                     930 BufferView_encodeTL::

000004c0 e92d4070    931 	stmfd	[sp]!,{r4-r6,lr}

                     932 ;236: {


                     933 

                     934 ;237:     size_t lenSize = BerEncoder_determineLengthSize(length);


                     935 

000004c4 e1a06001    936 	mov	r6,r1

000004c8 e1a04000    937 	mov	r4,r0

000004cc e1a05002    938 	mov	r5,r2

000004d0 e1a00005    939 	mov	r0,r5

000004d4 eb000000*   940 	bl	BerEncoder_determineLengthSize

                     941 ;238:     if (1 + lenSize > bv->len - bv->pos)


                     942 

000004d8 e9941008    943 	ldmed	[r4],{r3,r12}

000004dc e2800001    944 	add	r0,r0,1

000004e0 e04c1003    945 	sub	r1,r12,r3

000004e4 e1500001    946 	cmp	r0,r1

                     947 ;239:     {


                     948 

                     949 ;240:         return FALSE;


                     950 

000004e8 83a00000    951 	movhi	r0,0

000004ec 8a000005    952 	bhi	.L1064

                     953 ;241:     }


                     954 ;242:     bv->pos = BerEncoder_encodeTL(tag, length, bv->p, bv->pos);


                     955 

000004f0 e5942000    956 	ldr	r2,[r4]

000004f4 e1a01005    957 	mov	r1,r5

000004f8 e1a00006    958 	mov	r0,r6

000004fc eb000000*   959 	bl	BerEncoder_encodeTL

00000500 e5840004    960 	str	r0,[r4,4]

                     961 ;243:     return TRUE;


                     962 

00000504 e3a00001    963 	mov	r0,1

                     964 .L1064:

00000508 e8bd8070    965 	ldmfd	[sp]!,{r4-r6,pc}


                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9h41.s
                     966 	.endf	BufferView_encodeTL

                     967 	.align	4

                     968 

                     969 ;bv	r4	param

                     970 ;tag	r6	param

                     971 ;length	r5	param

                     972 

                     973 	.section ".bss","awb"

                     974 .L1094:

                     975 	.data

                     976 	.text

                     977 

                     978 ;244: }


                     979 

                     980 ;245: 


                     981 ;246: bool BufferView_encodeExtTL(BufferView* bv, uint16_t extTag, size_t length)


                     982 	.align	4

                     983 	.align	4

                     984 BufferView_encodeExtTL::

0000050c e92d4070    985 	stmfd	[sp]!,{r4-r6,lr}

                     986 ;247: {


                     987 

                     988 ;248:     size_t lenSize = BerEncoder_determineLengthSize(length);


                     989 

00000510 e1a05001    990 	mov	r5,r1

00000514 e1a04000    991 	mov	r4,r0

00000518 e1a06002    992 	mov	r6,r2

0000051c e1a00006    993 	mov	r0,r6

00000520 eb000000*   994 	bl	BerEncoder_determineLengthSize

                     995 ;249:     if (2 + lenSize > bv->len - bv->pos)


                     996 

00000524 e2801002    997 	add	r1,r0,2

00000528 e9940005    998 	ldmed	[r4],{r0,r2}

0000052c e0422000    999 	sub	r2,r2,r0

00000530 e1510002   1000 	cmp	r1,r2

                    1001 ;250:     {


                    1002 

                    1003 ;251:         return FALSE;


                    1004 

00000534 83a00000   1005 	movhi	r0,0

00000538 8a00000a   1006 	bhi	.L1108

                    1007 ;252:     }


                    1008 ;253:     bv->p[bv->pos++] = extTag >> 8;


                    1009 

0000053c e5941000   1010 	ldr	r1,[r4]

00000540 e2802001   1011 	add	r2,r0,1

00000544 e5842004   1012 	str	r2,[r4,4]

00000548 e1a02445   1013 	mov	r2,r5 asr 8

0000054c e7c12000   1014 	strb	r2,[r1,r0]

                    1015 ;254:     bv->pos = BerEncoder_encodeTL(extTag & 0xFF, length, bv->p, bv->pos);


                    1016 

00000550 e894000c   1017 	ldmfd	[r4],{r2-r3}

00000554 e1a01006   1018 	mov	r1,r6

00000558 e20500ff   1019 	and	r0,r5,255

0000055c eb000000*  1020 	bl	BerEncoder_encodeTL

00000560 e5840004   1021 	str	r0,[r4,4]

                    1022 ;255:     return TRUE;


                    1023 

00000564 e3a00001   1024 	mov	r0,1

                    1025 .L1108:

00000568 e8bd8070   1026 	ldmfd	[sp]!,{r4-r6,pc}


                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9h41.s
                    1027 	.endf	BufferView_encodeExtTL

                    1028 	.align	4

                    1029 

                    1030 ;bv	r4	param

                    1031 ;extTag	r5	param

                    1032 ;length	r6	param

                    1033 

                    1034 	.section ".bss","awb"

                    1035 .L1142:

                    1036 	.data

                    1037 	.text

                    1038 

                    1039 ;256: }


                    1040 

                    1041 ;257: 


                    1042 ;258: bool BufferView_encodeInt32(BufferView* bv, uint8_t tag, int32_t value)


                    1043 	.align	4

                    1044 	.align	4

                    1045 BufferView_encodeInt32::

0000056c e92d4070   1046 	stmfd	[sp]!,{r4-r6,lr}

                    1047 ;259: {


                    1048 

                    1049 ;260:     int encodedLen = 2 //тэг + длина


                    1050 

00000570 e1a06001   1051 	mov	r6,r1

00000574 e1a04000   1052 	mov	r4,r0

00000578 e1a05002   1053 	mov	r5,r2

0000057c e1a00005   1054 	mov	r0,r5

00000580 eb000000*  1055 	bl	BerEncoder_Int32DetermineEncodedSize

00000584 e2800002   1056 	add	r0,r0,2

                    1057 ;261:         + BerEncoder_Int32DetermineEncodedSize(value);


                    1058 ;262:     if (bv->pos + encodedLen > bv->len)


                    1059 

00000588 e9941008   1060 	ldmed	[r4],{r3,r12}

0000058c e0800003   1061 	add	r0,r0,r3

00000590 e150000c   1062 	cmp	r0,r12

                    1063 ;263:     {


                    1064 

                    1065 ;264:         //Не лезет в буфер


                    1066 ;265:         return false;


                    1067 

00000594 83a00000   1068 	movhi	r0,0

00000598 8a000005   1069 	bhi	.L1156

                    1070 ;266:     }


                    1071 ;267:     bv->pos = BerEncoder_EncodeInt32WithTL(tag, value, bv->p, bv->pos);


                    1072 

0000059c e5942000   1073 	ldr	r2,[r4]

000005a0 e1a01005   1074 	mov	r1,r5

000005a4 e1a00006   1075 	mov	r0,r6

000005a8 eb000000*  1076 	bl	BerEncoder_EncodeInt32WithTL

000005ac e5840004   1077 	str	r0,[r4,4]

                    1078 ;268:     return true;


                    1079 

000005b0 e3a00001   1080 	mov	r0,1

                    1081 .L1156:

000005b4 e8bd8070   1082 	ldmfd	[sp]!,{r4-r6,pc}

                    1083 	.endf	BufferView_encodeInt32

                    1084 	.align	4

                    1085 ;encodedLen	r0	local

                    1086 

                    1087 ;bv	r4	param


                                                                      Page 19
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9h41.s
                    1088 ;tag	r6	param

                    1089 ;value	r5	param

                    1090 

                    1091 	.section ".bss","awb"

                    1092 .L1190:

                    1093 	.data

                    1094 	.text

                    1095 

                    1096 ;269: }


                    1097 

                    1098 ;270: 


                    1099 ;271: bool BufferView_encodeUInt32(BufferView* bv, uint8_t tag, uint32_t value)


                    1100 	.align	4

                    1101 	.align	4

                    1102 BufferView_encodeUInt32::

000005b8 e92d4070   1103 	stmfd	[sp]!,{r4-r6,lr}

                    1104 ;272: {


                    1105 

                    1106 ;273:     int encodedLen = 2 //тэг + длина


                    1107 

000005bc e1a06001   1108 	mov	r6,r1

000005c0 e1a04000   1109 	mov	r4,r0

000005c4 e1a05002   1110 	mov	r5,r2

000005c8 e1a00005   1111 	mov	r0,r5

000005cc eb000000*  1112 	bl	BerEncoder_UInt32determineEncodedSize

000005d0 e2800002   1113 	add	r0,r0,2

                    1114 ;274:         + BerEncoder_UInt32determineEncodedSize(value);


                    1115 ;275:     if (bv->pos + encodedLen > bv->len)


                    1116 

000005d4 e9941008   1117 	ldmed	[r4],{r3,r12}

000005d8 e0800003   1118 	add	r0,r0,r3

000005dc e150000c   1119 	cmp	r0,r12

                    1120 ;276:     {


                    1121 

                    1122 ;277:         //Не лезет в буфер


                    1123 ;278:         return FALSE;


                    1124 

000005e0 83a00000   1125 	movhi	r0,0

000005e4 8a000005   1126 	bhi	.L1204

                    1127 ;279:     }


                    1128 ;280:     bv->pos = BerEncoder_encodeUInt32WithTL(tag, value, bv->p, bv->pos);


                    1129 

000005e8 e5942000   1130 	ldr	r2,[r4]

000005ec e1a01005   1131 	mov	r1,r5

000005f0 e1a00006   1132 	mov	r0,r6

000005f4 eb000000*  1133 	bl	BerEncoder_encodeUInt32WithTL

000005f8 e5840004   1134 	str	r0,[r4,4]

                    1135 ;281:     return TRUE;


                    1136 

000005fc e3a00001   1137 	mov	r0,1

                    1138 .L1204:

00000600 e8bd8070   1139 	ldmfd	[sp]!,{r4-r6,pc}

                    1140 	.endf	BufferView_encodeUInt32

                    1141 	.align	4

                    1142 ;encodedLen	r0	local

                    1143 

                    1144 ;bv	r4	param

                    1145 ;tag	r6	param

                    1146 ;value	r5	param

                    1147 

                    1148 	.section ".bss","awb"


                                                                      Page 20
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9h41.s
                    1149 .L1238:

                    1150 	.data

                    1151 	.text

                    1152 

                    1153 ;282: }


                    1154 

                    1155 ;283: 


                    1156 ;284: bool BufferView_encodeBoolean(BufferView* bv, uint8_t tag, bool val)


                    1157 	.align	4

                    1158 	.align	4

                    1159 BufferView_encodeBoolean::

00000604 e92d4030   1160 	stmfd	[sp]!,{r4-r5,lr}

                    1161 ;285: {


                    1162 

                    1163 ;286:     if (bv->pos + 3 > bv->len)


                    1164 

00000608 e1a0c001   1165 	mov	r12,r1

0000060c e1a05000   1166 	mov	r5,r0

00000610 e5953004   1167 	ldr	r3,[r5,4]

00000614 e5951008   1168 	ldr	r1,[r5,8]

00000618 e2830003   1169 	add	r0,r3,3

0000061c e1500001   1170 	cmp	r0,r1

                    1171 ;287:     {


                    1172 

                    1173 ;288:         //Не лезет в буфер


                    1174 ;289:         return FALSE;


                    1175 

00000620 83a00000   1176 	movhi	r0,0

00000624 8a000005   1177 	bhi	.L1252

00000628 e1a01002   1178 	mov	r1,r2

                    1179 ;290:     }


                    1180 ;291:     bv->pos = BerEncoder_encodeBoolean(tag, val, bv->p, bv->pos);


                    1181 

0000062c e5952000   1182 	ldr	r2,[r5]

00000630 e1a0000c   1183 	mov	r0,r12

00000634 eb000000*  1184 	bl	BerEncoder_encodeBoolean

00000638 e5850004   1185 	str	r0,[r5,4]

                    1186 ;292:     return TRUE;


                    1187 

0000063c e3a00001   1188 	mov	r0,1

                    1189 .L1252:

00000640 e8bd8030   1190 	ldmfd	[sp]!,{r4-r5,pc}

                    1191 	.endf	BufferView_encodeBoolean

                    1192 	.align	4

                    1193 

                    1194 ;bv	r5	param

                    1195 ;tag	r12	param

                    1196 ;val	r4	param

                    1197 

                    1198 	.section ".bss","awb"

                    1199 .L1286:

                    1200 	.data

                    1201 	.text

                    1202 

                    1203 ;293: }


                    1204 

                    1205 ;294: 


                    1206 ;295: bool BufferView_encodeOctetString(BufferView* bv, uint8_t tag, void* data,


                    1207 	.align	4

                    1208 	.align	4

                    1209 BufferView_encodeOctetString::


                                                                      Page 21
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9h41.s
00000644 e92d40f0   1210 	stmfd	[sp]!,{r4-r7,lr}

                    1211 ;296:     size_t dataLen)


                    1212 ;297: {


                    1213 

                    1214 ;298:     int encodedLen = BerEncoder_determineFullObjectSize(dataLen);


                    1215 

00000648 e24dd004   1216 	sub	sp,sp,4

0000064c e1a06001   1217 	mov	r6,r1

00000650 e1a07002   1218 	mov	r7,r2

00000654 e1a04000   1219 	mov	r4,r0

00000658 e1a05003   1220 	mov	r5,r3

0000065c e1a00005   1221 	mov	r0,r5

00000660 eb000000*  1222 	bl	BerEncoder_determineFullObjectSize

                    1223 ;299:     if (bv->pos + encodedLen > bv->len)


                    1224 

00000664 e9940006   1225 	ldmed	[r4],{r1-r2}

00000668 e0800001   1226 	add	r0,r0,r1

0000066c e1500002   1227 	cmp	r0,r2

                    1228 ;300:     {


                    1229 

                    1230 ;301:         //Не лезет в буфер


                    1231 ;302:         return FALSE;


                    1232 

00000670 83a00000   1233 	movhi	r0,0

00000674 8a000007   1234 	bhi	.L1300

                    1235 ;303:     }


                    1236 ;304:     bv->pos = BerEncoder_encodeOctetString(tag, data, dataLen, bv->p, bv->pos);


                    1237 

00000678 e58d1000   1238 	str	r1,[sp]

0000067c e5943000   1239 	ldr	r3,[r4]

00000680 e1a02005   1240 	mov	r2,r5

00000684 e1a01007   1241 	mov	r1,r7

00000688 e1a00006   1242 	mov	r0,r6

0000068c eb000000*  1243 	bl	BerEncoder_encodeOctetString

00000690 e5840004   1244 	str	r0,[r4,4]

                    1245 ;305:     return TRUE;


                    1246 

00000694 e3a00001   1247 	mov	r0,1

                    1248 .L1300:

00000698 e28dd004   1249 	add	sp,sp,4

0000069c e8bd80f0   1250 	ldmfd	[sp]!,{r4-r7,pc}

                    1251 	.endf	BufferView_encodeOctetString

                    1252 	.align	4

                    1253 

                    1254 ;bv	r4	param

                    1255 ;tag	r6	param

                    1256 ;data	r7	param

                    1257 ;dataLen	r5	param

                    1258 

                    1259 	.section ".bss","awb"

                    1260 .L1334:

                    1261 	.data

                    1262 	.text

                    1263 

                    1264 ;306: }


                    1265 

                    1266 ;307: 


                    1267 ;308: bool BufferView_encodeBufferView(BufferView* bv, uint8_t tag, BufferView* data)


                    1268 	.align	4

                    1269 	.align	4

                    1270 BufferView_encodeBufferView::


                                                                      Page 22
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9h41.s
                    1271 ;309: {


                    1272 

                    1273 ;310:     return BufferView_encodeOctetString(bv, tag, data->p, data->pos);


                    1274 

000006a0 e892000c   1275 	ldmfd	[r2],{r2-r3}

000006a4 eaffffe6*  1276 	b	BufferView_encodeOctetString

                    1277 	.endf	BufferView_encodeBufferView

                    1278 	.align	4

                    1279 

                    1280 ;bv	none	param

                    1281 ;tag	none	param

                    1282 ;data	r2	param

                    1283 

                    1284 	.section ".bss","awb"

                    1285 .L1377:

                    1286 	.data

                    1287 	.text

                    1288 

                    1289 ;311: }


                    1290 

                    1291 ;312: 


                    1292 ;313: bool BufferView_reverseWrite(BufferView *bv, const void *src, size_t len)


                    1293 	.align	4

                    1294 	.align	4

                    1295 BufferView_reverseWrite::

000006a8 e92d0030   1296 	stmfd	[sp]!,{r4-r5}

                    1297 ;314: {


                    1298 

                    1299 ;315:     size_t i;


                    1300 ;316:     const uint8_t* pSrc = src;


                    1301 

                    1302 ;317: 


                    1303 ;318:     if (bv->pos + len > bv->len)


                    1304 

000006ac e9901008   1305 	ldmed	[r0],{r3,r12}

000006b0 e0823003   1306 	add	r3,r2,r3

000006b4 e153000c   1307 	cmp	r3,r12

                    1308 ;319:     {


                    1309 

                    1310 ;320:         return false;


                    1311 

000006b8 83a00000   1312 	movhi	r0,0

000006bc 8a00003c   1313 	bhi	.L1384

                    1314 ;321:     }


                    1315 ;322: 


                    1316 ;323:     pSrc += len - 1;


                    1317 

000006c0 e0823001   1318 	add	r3,r2,r1

000006c4 e2431001   1319 	sub	r1,r3,1

                    1320 ;324: 


                    1321 ;325:     for (i = 0; i < len; i++)


                    1322 

000006c8 e3520000   1323 	cmp	r2,0

000006cc a1a03002   1324 	movge	r3,r2

000006d0 b3a03000   1325 	movlt	r3,0

000006d4 e1b021a3   1326 	movs	r2,r3 lsr 3

000006d8 0a000029   1327 	beq	.L1426

                    1328 .L1442:

000006dc e8901010   1329 	ldmfd	[r0],{r4,r12}

000006e0 e28c5001   1330 	add	r5,r12,1

000006e4 e5805004   1331 	str	r5,[r0,4]


                                                                      Page 23
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9h41.s
000006e8 e4515008   1332 	ldrb	r5,[r1],-8

000006ec e7c4500c   1333 	strb	r5,[r4,r12]

000006f0 e8901010   1334 	ldmfd	[r0],{r4,r12}

000006f4 e28c5001   1335 	add	r5,r12,1

000006f8 e5805004   1336 	str	r5,[r0,4]

000006fc e5d15007   1337 	ldrb	r5,[r1,7]

00000700 e7c4500c   1338 	strb	r5,[r4,r12]

00000704 e8901010   1339 	ldmfd	[r0],{r4,r12}

00000708 e28c5001   1340 	add	r5,r12,1

0000070c e5805004   1341 	str	r5,[r0,4]

00000710 e5d15006   1342 	ldrb	r5,[r1,6]

00000714 e7c4500c   1343 	strb	r5,[r4,r12]

00000718 e8901010   1344 	ldmfd	[r0],{r4,r12}

0000071c e28c5001   1345 	add	r5,r12,1

00000720 e5805004   1346 	str	r5,[r0,4]

00000724 e5d15005   1347 	ldrb	r5,[r1,5]

00000728 e7c4500c   1348 	strb	r5,[r4,r12]

0000072c e8901010   1349 	ldmfd	[r0],{r4,r12}

00000730 e28c5001   1350 	add	r5,r12,1

00000734 e5805004   1351 	str	r5,[r0,4]

00000738 e5d15004   1352 	ldrb	r5,[r1,4]

0000073c e7c4500c   1353 	strb	r5,[r4,r12]

00000740 e8901010   1354 	ldmfd	[r0],{r4,r12}

00000744 e28c5001   1355 	add	r5,r12,1

00000748 e5805004   1356 	str	r5,[r0,4]

0000074c e5d15003   1357 	ldrb	r5,[r1,3]

00000750 e7c4500c   1358 	strb	r5,[r4,r12]

00000754 e8901010   1359 	ldmfd	[r0],{r4,r12}

00000758 e28c5001   1360 	add	r5,r12,1

0000075c e5805004   1361 	str	r5,[r0,4]

00000760 e5d15002   1362 	ldrb	r5,[r1,2]

00000764 e7c4500c   1363 	strb	r5,[r4,r12]

00000768 e8901010   1364 	ldmfd	[r0],{r4,r12}

0000076c e28c5001   1365 	add	r5,r12,1

00000770 e5805004   1366 	str	r5,[r0,4]

00000774 e5d15001   1367 	ldrb	r5,[r1,1]

00000778 e2522001   1368 	subs	r2,r2,1

0000077c e7c4500c   1369 	strb	r5,[r4,r12]

00000780 1affffd5   1370 	bne	.L1442

                    1371 .L1426:

00000784 e2132007   1372 	ands	r2,r3,7

00000788 0a000008   1373 	beq	.L1389

                    1374 .L1446:

0000078c e8900018   1375 	ldmfd	[r0],{r3-r4}

00000790 e1a0c003   1376 	mov	r12,r3

00000794 e1a03004   1377 	mov	r3,r4

00000798 e2834001   1378 	add	r4,r3,1

0000079c e5804004   1379 	str	r4,[r0,4]

000007a0 e4514001   1380 	ldrb	r4,[r1],-1

000007a4 e2522001   1381 	subs	r2,r2,1

000007a8 e7cc4003   1382 	strb	r4,[r12,r3]

000007ac 1afffff6   1383 	bne	.L1446

                    1384 .L1389:

                    1385 ;328:     }


                    1386 ;329:     return true;


                    1387 

000007b0 e3a00001   1388 	mov	r0,1

                    1389 .L1384:

000007b4 e8bd0030   1390 	ldmfd	[sp]!,{r4-r5}

000007b8 e12fff1e*  1391 	ret	

                    1392 	.endf	BufferView_reverseWrite


                                                                      Page 24
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9h41.s
                    1393 	.align	4

                    1394 ;pSrc	r1	local

                    1395 

                    1396 ;bv	r0	param

                    1397 ;src	r1	param

                    1398 ;len	r2	param

                    1399 

                    1400 	.section ".bss","awb"

                    1401 .L1617:

                    1402 	.data

                    1403 	.text

                    1404 

                    1405 ;330: }


                    1406 	.align	4

                    1407 

                    1408 	.data

                    1409 	.ghsnote version,6

                    1410 	.ghsnote tools,3

                    1411 	.ghsnote options,0

                    1412 	.text

                    1413 	.align	4

