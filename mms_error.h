#pragma once

#include "bufView.h"

#include <stdint.h>
#include <stdbool.h>
#include <stdint.h>



typedef enum
{
    /* generic error codes */
    MMS_ERROR_NONE = 0,
    MMS_ERROR_CONNECTION_REJECTED = 1,
    <PERSON><PERSON>_ERROR_CONNECTION_LOST = 2,
    MMS_ERROR_SERVICE_TIMEOUT = 3,
    MMS_ERROR_PARSING_RESPONSE = 4,
    M<PERSON>_ERROR_HARDWARE_FAULT = 5,
    MMS_ERROR_CONCLUDE_REJECTED = 6,
    MMS_ERROR_INVALID_ARGUMENTS = 7,
    MMS_ERROR_OUTSTANDING_CALL_LIMIT = 8,

    M<PERSON>_ERROR_OTHER = 9,

    /* confirmed error PDU codes */
    MMS_ERROR_VMDSTATE_OTHER = 10,

    MMS_ERROR_APPLICATION_REFERENCE_OTHER = 20,

    MMS_ERROR_DEFINITION_OTHER = 30,
    <PERSON><PERSON>_ERROR_DEFINITION_INVALID_ADDRESS = 31,
    M<PERSON>_ERROR_DEFINITION_TYPE_UNSUPPORTED = 32,
    <PERSON><PERSON>_ERROR_DEFINITION_TYPE_INCONSISTENT = 33,
    MMS_ERROR_DEFINITION_OBJECT_UNDEFINED = 34,
    MMS_ERROR_DEFINITION_OBJECT_EXISTS = 35,
    MMS_ERROR_DEFINITION_OBJECT_ATTRIBUTE_INCONSISTENT = 36,

    MMS_ERROR_RESOURCE_OTHER = 40,
    MMS_ERROR_RESOURCE_CAPABILITY_UNAVAILABLE = 41,

    MMS_ERROR_SERVICE_OTHER = 50,
    MMS_ERROR_SERVICE_OBJECT_CONSTRAINT_CONFLICT = 55,

    MMS_ERROR_SERVICE_PREEMPT_OTHER = 60,

    MMS_ERROR_TIME_RESOLUTION_OTHER = 70,

    MMS_ERROR_ACCESS_OTHER = 80,
    MMS_ERROR_ACCESS_OBJECT_NON_EXISTENT = 81,
    MMS_ERROR_ACCESS_OBJECT_ACCESS_UNSUPPORTED = 82,
    MMS_ERROR_ACCESS_OBJECT_ACCESS_DENIED = 83,
    MMS_ERROR_ACCESS_OBJECT_INVALIDATED = 84,
    MMS_ERROR_ACCESS_OBJECT_VALUE_INVALID = 85, /* for DataAccessError 11 */
    MMS_ERROR_ACCESS_TEMPORARILY_UNAVAILABLE = 86, /* for DataAccessError 2 */

    MMS_ERROR_FILE_OTHER = 90,
    MMS_ERROR_FILE_FILENAME_AMBIGUOUS = 91,
    MMS_ERROR_FILE_FILE_BUSY = 92,
    MMS_ERROR_FILE_FILENAME_SYNTAX_ERROR = 93,
    MMS_ERROR_FILE_CONTENT_TYPE_INVALID = 94,
    MMS_ERROR_FILE_POSITION_INVALID = 95,
    MMS_ERROR_FILE_FILE_ACCESS_DENIED = 96,
    MMS_ERROR_FILE_FILE_NON_EXISTENT = 97,
    MMS_ERROR_FILE_DUPLICATE_FILENAME = 98,
    MMS_ERROR_FILE_INSUFFICIENT_SPACE_IN_FILESTORE = 99,

    /* reject codes */
    MMS_ERROR_REJECT_OTHER = 100,
    MMS_ERROR_REJECT_UNKNOWN_PDU_TYPE = 101,
    MMS_ERROR_REJECT_INVALID_PDU = 102,
    MMS_ERROR_REJECT_UNRECOGNIZED_SERVICE = 103,
    MMS_ERROR_REJECT_UNRECOGNIZED_MODIFIER = 104,
    MMS_ERROR_REJECT_REQUEST_INVALID_ARGUMENT = 105

} MmsError;

int mms_createMmsRejectPdu(unsigned int* invokeId, int reason, uint8_t* outBuf);


//Creating MMS ConfirmedErrorPdu
//iInvokeId			- InvokeId
//pResponseBuffer	- Response Buffer
//ErrorType			- MMS Error
//
//return		- PDU length or (<0) if error
int CreateMmsConfirmedErrorPdu( unsigned int iInvokeId, unsigned char* pResponseBuffer,
                                MmsError ErrorType );


// Функция делает то же, что и CreateMmsConfirmedErrorPdu,
// но использует BufferView
bool MMSError_createConfirmedErrorPdu(uint32_t invokeId, MmsError errorType,
    BufferView* outBufView);
