#include "stringView.h"
#include <stddef.h>
#include <string.h>

void StringView_init(StringView* strView, const char * str, size_t len)
{
	strView->p = str;
	strView->len = len;
}

void StringView_fromStringView(StringView* strView, StringView* src)
{
	StringView_init(strView, src->p, src->len);
}

void StringView_fromCStr(StringView* strView, char * str)
{
    StringView_init(strView, str, strlen(str));
}

int StringView_findChar(StringView * strView, unsigned char ch)
{
	size_t pos;

	if (strView->p == NULL)
	{
		return -1;
	}

	for (pos = 0; pos < strView->len; ++pos)
	{
		if (strView->p[pos] == ch)
		{
			return pos;
		}
	}
	return -1;
}

int StringView_findCharBack(StringView * strView, unsigned char ch)
{
    int pos;

    if (strView->p == NULL)
    {
        return -1;
    }

    for (pos = strView->len - 1; pos >= 0; --pos)
    {
        if (strView->p[pos] == ch)
        {
            return pos;
        }
    }
    return -1;
}

int StringView_splitChar(StringView* src, unsigned char splitChar,
	StringView* dst1, StringView* dst2)
{
	int splitPos = StringView_findChar(src, splitChar);
	if (splitPos == -1)
	{
		return 0;
	}
	dst1->len = splitPos;
	dst1->p = src->p;
	dst2->len = src->len - splitPos - 1;
	dst2->p = src->p + splitPos + 1;
	return 1;
}

int StringView_cmp(StringView * strView1, StringView * strView2)
{
	int result;
    int lenDiff;
    lenDiff = strView1->len - strView2->len;
	if (lenDiff != 0)
	{
		return lenDiff;
	}
	result = memcmp(strView1->p, strView2->p, strView1->len);
	return result;
}

int StringView_cmpCStr(StringView * strView, const char* cstr)
{
	StringView cstrView;

    StringView_init(&cstrView, cstr, strlen(cstr));
	return StringView_cmp(strView, &cstrView);
}
