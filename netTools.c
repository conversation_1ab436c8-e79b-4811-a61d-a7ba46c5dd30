#include "netTools.h"

#include <nw.h>
#include <string.h>
#include <Clib.h> // Idle
#include <ethbuslib.h>
#include <process.h>
#include <system/System.h>
#include "debug.h"
#include <stdbool.h>

// в зависимости от режима работы инициализируются указатели
static bool (*_getIfFuncPtr)(unsigned char ifNum, void **pNetIf);
static bool (*_getMacFuncPtr)(void* netIf, uint8_t mac[6]);
static bool (*_sendFuncPtr)(void* netIf, void* data, size_t byteCount);

// для межпроцессорной шины
static EthBusInterface *_ethbus;
static int _gooseReceiverSlotNum;
static int _gooseReceiverDescr = -1;

//Контроллер присоединения или что-то другое
static bool _isBC;

// =========== интерфейс межпроцессорной шины =================================
static bool ethBusGetIf(unsigned char ifNum, void **pNetIf)
{
	
	// интерфейс уже проинициализирован
	if (_gooseReceiverDescr >= 0)
	{
		return TRUE;
	}
	
	// TODO: здесь нужно получить номер слота модуля связи на шине процесса
	// пока отправляется по широковещательному
	_gooseReceiverSlotNum = BA_GOOSE_REPEATER;
	
	_gooseReceiverDescr = _ethbus->open(PROTO_GOOSE_REPEATER);
	if (_gooseReceiverDescr < 0)
	{
		return FALSE;
	}
	
	return TRUE;
}
static bool ethBusGetMac(void* netIf, uint8_t mac[6])
{
	memset(mac,0,6);
	return TRUE;	
}

static bool ethBusSend(void* netIf, void* data, size_t byteCount)
{
	_ethbus->sendTo(_gooseReceiverDescr, _gooseReceiverSlotNum,data,byteCount);
	return TRUE;
}

// ============================================================================




// =========== сетевой интерфейс ==============================================
static bool networkGetIf(unsigned char ifNum, void **pNetIf)
{
	NetifT*  netIf;
    // сетевые интерфейсы добавляются "пачкой" в критической секции,
    // поэтому ждем пока добавится интерфейс по-умолчанию и соответственно
    // добавятся остальные
    while(nwGetDefaultNetif() == NULL) Idle();

    // здесь уже все возможные интерфейсы должны быть добавлены
    netIf = nwGetNetif(ifNum);
    if(netIf == NULL)
    {
        return FALSE;
    }
    *pNetIf = netIf;
    return TRUE;
}

static bool networkGetMac(void* netIf, uint8_t mac[6])
{
	//unsigned char *nwGetMac(NetifT *netif);
    uint8_t* result  = nwGetMac(netIf);
    memcpy(mac, result, 6);
    return TRUE;	
}

static bool networkSend(void* netIf, void* data, size_t byteCount)
{
	 return 0 == nwEthSend(netIf, data, byteCount);
}
// ============================================================================

//Комментарий для проверки кодировки
bool NetTools_init(void)
{
	_HANDLE hModule = _GetModuleHandle("SYSTEM");
	_FindRomModule findRomModule; 
	if (!hModule)
	{
		TRACE("NetTools_init: SYSTEM not found");
		return FALSE;
	}
	
	findRomModule = (_FindRomModule)_GetProcAddress(hModule,(char*)SYS_FRM);
	if (!findRomModule)
	{
		TRACE("NetTools_init: findRomModule not found");
		return FALSE;
	}
	
	// ethbus инициализируется всегда
	_ethbus = 	ethBusGetInterface();
	_isBC = _ethbus != NULL;
	if (!_ethbus)
	{
		TRACE("NetTools_init: ethbus not found");
		
	}
	// в зависимости от того, в каком модуле связи находится MMServer,
	// по-разному инициализируются интерфейсы посылки
	
	// модуль находится в "дополнительной плате" (только для КП)
	if (_isBC && findRomModule(NULL,'GFCN'))
	{
		_getIfFuncPtr = ethBusGetIf;
		_getMacFuncPtr = ethBusGetMac;
		_sendFuncPtr = ethBusSend;
	}
	else // модуль находится в плате с шиной процесса или УСО
	{
		_getIfFuncPtr = networkGetIf;
		_getMacFuncPtr = networkGetMac;
		_sendFuncPtr = networkSend;
	}
	
	return TRUE;
}

bool NetTools_getIf(uint8_t ifNum, void** pNetIf)
{	
   return _getIfFuncPtr(ifNum, pNetIf);
}

bool NetTools_getMac(void* netIf, uint8_t mac[6])
{
    return _getMacFuncPtr(netIf,mac);
}

bool NetTools_send(void* netIf, void* data, size_t byteCount)
{
   return _sendFuncPtr(netIf,data,byteCount);
}


bool NetTools_busOK(void)
{
	EthBusModuleState *moduleState;

	if(!_isBC)
	{
		return true;
	}

	// шина еще не инициализирована
	if (!_ethbus)
	{
		return false;
	}

	// не может быть NULL
	moduleState = _ethbus->getModuleState(-1);
	return moduleState->readyFlag;
}
