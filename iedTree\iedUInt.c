#include "iedUInt.h"

#include "iedTree.h"
#include "iedFinalDA.h"

#include "../DataSlice.h"
#include "../AsnEncoding.h"

#include "debug.h"

#include "IEDCompile/AccessInfo.h"

#define MAX_UINT32_ENCODED_SIZE 7

static void updateFromDataSlice(IEDEntity entity)
{
	int offset  = entity->dataSliceOffset;
    uint32_t value;

	if(offset == -1)
	{
		return;
	}

    value = DataSlice_getUInt32FastCurrDS(offset);

    if(entity->uintValue == value)
	{
		entity->changed = TRGOP_NONE;
	}
	else
	{
		entity->changed = entity->trgOps;
        entity->uintValue = value;
		IEDEntity_setTimeStamp(entity, dataSliceGetTimeStamp());
	}
}

static bool calcReadLen(IEDEntity entity, size_t* pLen )
{
	// +2 for tag and length
    *pLen = BerEncoder_UInt32determineEncodedSize(entity->uintValue) + 2;

	return true;
}

static bool encodeRead(IEDEntity entity, BufferView* outBuf)
{
	uint8_t* encodeBuf;
	int fullEncodedLen;

	//Запрашиваем в буфере максимум места чтобы не вычислять.
	//Это фактически только проверка, поэтому небольшая жадность не повредит.
    if(!BufferView_alloc(outBuf,MAX_UINT32_ENCODED_SIZE, &encodeBuf))
	{
		ERROR_REPORT("Unable to allocate buffer");
		return false;
	}

	//Функция возвращает новое смещение в буфере, но поскольку начальное
	//смещение 0, можно считать это размером.
    fullEncodedLen = BerEncoder_encodeUInt32WithTL(
                IEC61850_BER_UNSIGNED_INTEGER, entity->uintValue, encodeBuf, 0);

	outBuf->pos += fullEncodedLen;
	return true;
}


void IEDUInt32_init(IEDEntity entity)
{
	TerminalItem* extInfo = entity->extInfo;
	IntBoolAccessInfo* accessInfo = extInfo->accessInfo;

	//Если будет ошибка, то запишется -1;
	entity->dataSliceOffset = DataSlice_getIntOffset(accessInfo->valueOffset);

	entity->calcReadLen = calcReadLen;
	entity->encodeRead = encodeRead;
	entity->updateFromDataSlice = updateFromDataSlice;

	IEDTree_addToCmpList(entity);
}

