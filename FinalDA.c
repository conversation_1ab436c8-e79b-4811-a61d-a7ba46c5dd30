//В этом файле FinalDA исключительно для GOOSE

#include "FinalDA.h"
#include "MemoryManager.h"
#include "IEDCompile/AccessInfo.h"
#include "IEDCompile/InnerAttributeTypes.h"
#include "iedmodel.h"
#include "BaseAsnTypes.h"
#include "AsnEncoding.h"
#include "mms_data.h"
#include "DataSlice.h"
#include <debug.h>
#include <stdint.h>

#define QUALITY_ENCODED_SIZE 5
#define CODED_ENUM_ENCODED_SIZE 4

struct FDAQualityStruct
{
	enum InnerAttributeType attrType;
    QualityAccsessInfo accessInfo;
	uint16_t value;
	uint8_t* fixedEncodeBuf;
};

typedef struct FDAQualityStruct* FDAQuality;

static void FDAQuality_init(FDAQuality q, QualityAccsessInfo* accessInfo)
{	
    QualityAccsessInfo* info = &q->accessInfo;
	q->attrType = INNER_TYPE_QUALITY;
    *info = *accessInfo;
    //Заменяем абсолютные смещения на смещения в DataSlice
    info->goodInvalidOffset =
            DataSlice_getBoolOffset(info->goodInvalidOffset);
    info->reservedQuestionableOffset =
            DataSlice_getBoolOffset(info->reservedQuestionableOffset);
    info->overflowOffset =
            DataSlice_getBoolOffset(info->overflowOffset);
    info->outOfRangeOffset =
            DataSlice_getBoolOffset(info->outOfRangeOffset);
    info->badReferenceOffset =
            DataSlice_getBoolOffset(info->badReferenceOffset);
    info->oscillatoryOffset =
            DataSlice_getBoolOffset(info->oscillatoryOffset);
    info->failureOffset =
            DataSlice_getBoolOffset(info->failureOffset);
    info->oldDataOffset =
            DataSlice_getBoolOffset(info->oldDataOffset);
    info->inconsistentOffset =
            DataSlice_getBoolOffset(info->inconsistentOffset);
    info->inaccurateOffset =
            DataSlice_getBoolOffset(info->inaccurateOffset);
    info->processSubstitutedOffset =
            DataSlice_getBoolOffset(info->processSubstitutedOffset);
    info->testOffset =
            DataSlice_getBoolOffset(info->testOffset);
    info->operatorBlockedOffset =
            DataSlice_getBoolOffset(info->operatorBlockedOffset);
}

FDAQuality FDAQuality_create(QualityAccsessInfo* accessInfo)
{
	FDAQuality q = MM_alloc(sizeof(struct FDAQualityStruct));
	if (q == NULL)
	{
		return NULL;
	}
	FDAQuality_init(q, accessInfo);
	return q;
}

bool FDAQuality_getFixedEncodedSize(FDAQuality q, size_t* size)
{
	*size = QUALITY_ENCODED_SIZE;
	return TRUE;
}

bool FDAQuality_encodeGOOSETemplate(FDAQuality q, BufferView* outBuf)
{
	uint16_t dummy = 0;
	uint8_t *pEncodeBuf;	

	if (!BufferView_alloc(outBuf, QUALITY_ENCODED_SIZE, &pEncodeBuf))
	{
		ERROR_REPORT("Trouble creating Quality template");
		return FALSE;
	}

	q->fixedEncodeBuf = pEncodeBuf;

	BerEncoder_encodeBitStringUshortBuf(ASN_TYPEDESCRIPTION_BIT_STRING,
		13, dummy, pEncodeBuf, 0);
	if (!BufferView_advance(outBuf, QUALITY_ENCODED_SIZE))
	{
		ERROR_REPORT("Trouble creating Quality template");
		return FALSE;
	}
	return TRUE;
}


bool FDAQuality_readAndCompare(FDAQuality q, void* dataSliceWnd,
                                 bool* changed)
{
    uint16_t newValue = qualityFromBitsFast(dataSliceWnd, &q->accessInfo);
	*changed = (q->value != newValue);
	q->value = newValue;
	return TRUE;
}


void FDAQuality_encodeFixedData(FDAQuality q)
{
	BerEncoder_encodeBitStringUshortBuf(ASN_TYPEDESCRIPTION_BIT_STRING,
		13, q->value, q->fixedEncodeBuf, 0);
}

//=========================Boolean===================================
struct FDABooleanStruct
{
	enum InnerAttributeType attrType;
    IntBoolAccessInfo* accessInfo;
    //Смещение в окне DataSlice
    int dataSliceOffset;
	bool value;
	uint8_t* fixedEncodeBuf;
};

typedef struct FDABooleanStruct* FDABoolean;


static void FDABoolean_init(FDABoolean daBool, IntBoolAccessInfo* accessInfo)
{
	daBool->attrType = INNER_TYPE_BOOLEAN;
    daBool->accessInfo = accessInfo;

    daBool->dataSliceOffset = DataSlice_getBoolOffset(accessInfo->valueOffset);
}

static FDABoolean FDABoolean_create(IntBoolAccessInfo* accessInfo)
{
	FDABoolean da = MM_alloc(sizeof(struct FDABooleanStruct));
	if (da == NULL)
	{
		return NULL;
	}
	FDABoolean_init(da, accessInfo);
	return da;
}

static bool FDABoolean_getFixedEncodedSize(FDABoolean da, size_t* size)
{
	*size = 3;
	return TRUE;
}

static bool FDABoolean_encodeGOOSETemplate(FDABoolean da, BufferView* outBuf)
{	
	//В рабочем режиме будем кодировать только значение, без длины и тэга
	da->fixedEncodeBuf = outBuf->p + outBuf->pos + 2;
	
	if (!BufferView_encodeBoolean(outBuf, IEC61850_BER_BOOLEAN, FALSE))
	{
		return FALSE;
	}	
	return TRUE;
}

static bool FDABoolean_readAndCompare(FDABoolean da,
                                        void* dataSliceWnd, bool* changed)
{
    bool newValue = 0;

    if(da->dataSliceOffset != -1)
    {
        newValue = DataSlice_getBoolFast(dataSliceWnd, da->dataSliceOffset);
    }

	*changed = (da->value != newValue);
	da->value = newValue;
	return TRUE;	
}

static void FDABoolean_encodeFixedData(FDABoolean da)
{
	*da->fixedEncodeBuf = da->value;
}

//================================CODEDENUM===================================
//Поддерживает только два бита. Например, DPS.stVal

struct FDACodedEnumStruct
{
	enum InnerAttributeType attrType;
	CodedEnumAccessInfo* accessInfo;	
    //Смещения в окне DataSlice
    int dataSliceOffset0;
    int dataSliceOffset1;
	uint8_t value;
	uint8_t* fixedEncodeBuf;
};

typedef struct FDACodedEnumStruct* FDACodedEnum;

static void FDACodedEnum_init(FDACodedEnum da, CodedEnumAccessInfo* accessInfo)
{
	da->attrType = INNER_TYPE_CODEDENUM;
	da->accessInfo = accessInfo;
    da->dataSliceOffset0 = DataSlice_getBoolOffset(accessInfo->valueOffsets[0]);
    da->dataSliceOffset1 = DataSlice_getBoolOffset(accessInfo->valueOffsets[1]);
}

static FDACodedEnum FDACodedEnum_create(CodedEnumAccessInfo* accessInfo)
{
	FDACodedEnum da;
	if (accessInfo->bitCount != 2)
	{
		ERROR_REPORT("Only two-bit coded enum supported (like DPS.stVal)");
		return NULL;
	}

	da = MM_alloc(sizeof(struct FDACodedEnumStruct));
	if (da == NULL)
	{
		return NULL;
	}
	FDACodedEnum_init(da, accessInfo);
	return da;
}

static bool FDACodedEnum_getFixedEncodedSize(FDACodedEnum da, size_t* size)
{
	*size = CODED_ENUM_ENCODED_SIZE;
	return TRUE;
}

static bool FDACodedEnum_encodeGOOSETemplate(FDACodedEnum da, BufferView* outBuf)
{	
	uint8_t dummy = 0;
	uint8_t *pEncodeBuf;

	if (!BufferView_alloc(outBuf, CODED_ENUM_ENCODED_SIZE, &pEncodeBuf))
	{
		ERROR_REPORT("Trouble creating double point value template");
		return FALSE;
	}

	da->fixedEncodeBuf = pEncodeBuf;

	BerEncoder_encodeUcharBitString(ASN_TYPEDESCRIPTION_BIT_STRING,
		2, dummy, pEncodeBuf, 0);
	if (!BufferView_advance(outBuf, CODED_ENUM_ENCODED_SIZE))
	{
		ERROR_REPORT("Trouble creating double point value template");
		return FALSE;
	}
	return TRUE;
}

static bool FDACodedEnum_readAndCompare(FDACodedEnum da,
                                          void* dataSliceWnd, bool* changed)
{	
	uint8_t newValue = 0;

    if(da->dataSliceOffset0 != -1)
    {
        newValue |= DataSlice_getBoolFast(dataSliceWnd, da->dataSliceOffset0);
    }
    newValue <<= 1;
    if(da->dataSliceOffset1 != -1)
    {
        newValue |= DataSlice_getBoolFast(dataSliceWnd, da->dataSliceOffset1);
    }
					
	*changed = (da->value != newValue);
	da->value = newValue;
	return true;	
}


static void FDACodedEnum_encodeFixedData(FDACodedEnum da)
{
	BerEncoder_encodeUcharBitString(ASN_TYPEDESCRIPTION_BIT_STRING,
		2, da->value, da->fixedEncodeBuf, 0);
}

//===================================================================

void* FDA_create(enum InnerAttributeType attrType, void* accessInfo)
{	
	switch (attrType)
	{
	case INNER_TYPE_QUALITY:
		return FDAQuality_create(accessInfo);
	case INNER_TYPE_BOOLEAN:
		return FDABoolean_create(accessInfo);
	case INNER_TYPE_CODEDENUM:
		return FDACodedEnum_create(accessInfo);
	default:
        ERROR_REPORT("GOOSE: Unsupported DA type");
		return NULL;
	}	
}

bool FDA_getFixedEncodedSize(void* da, size_t* size)
{
	enum InnerAttributeType* attrType = da;

	//Проверка на всякий случай
	if ((((uint32_t)attrType) & 3) != 0)
	{
		ERROR_REPORT("Alignment error");
		return false;
	}


	switch (*attrType)
	{
	case INNER_TYPE_QUALITY:
		return FDAQuality_getFixedEncodedSize(da, size);
	case INNER_TYPE_BOOLEAN:
		return FDABoolean_getFixedEncodedSize(da, size);
	case INNER_TYPE_CODEDENUM:
		return FDACodedEnum_getFixedEncodedSize(da, size);
	default:
		ERROR_REPORT("Unsupported attribute type");
		return FALSE;
	}
}

bool FDA_encodeGOOSETemplate(void* da, BufferView* templateBuf)
{
	enum InnerAttributeType* attrType = da;
	switch (*attrType)
	{
	case INNER_TYPE_QUALITY:
		return FDAQuality_encodeGOOSETemplate(da, templateBuf);
	case INNER_TYPE_BOOLEAN:
		return FDABoolean_encodeGOOSETemplate(da, templateBuf);
	case INNER_TYPE_CODEDENUM:
		return FDACodedEnum_encodeGOOSETemplate(da, templateBuf);
	default:
		ERROR_REPORT("Unsupported attribute type");
		return FALSE;
	}
}

bool FDA_readAndCompare(void* da, void *dataSliceWnd,
                          bool* changed)
{
	enum InnerAttributeType* attrType = da;
	switch (*attrType)
	{
	case INNER_TYPE_QUALITY:
        return FDAQuality_readAndCompare(da, dataSliceWnd, changed);
	case INNER_TYPE_BOOLEAN:
        return FDABoolean_readAndCompare(da, dataSliceWnd, changed);
	case INNER_TYPE_CODEDENUM:
        return FDACodedEnum_readAndCompare(da, dataSliceWnd, changed);
	default:
		ERROR_REPORT("Unsupported attribute type");
		*changed = FALSE;
		return FALSE;
	}
}

void FDA_encodeFixedData(void* da)
{
	enum InnerAttributeType* attrType = da;
	switch (*attrType)
	{
	case INNER_TYPE_QUALITY:
        FDAQuality_encodeFixedData(da);
        return;
	case INNER_TYPE_BOOLEAN:
		FDABoolean_encodeFixedData(da);
		return;
	case INNER_TYPE_CODEDENUM:
		FDACodedEnum_encodeFixedData(da);
		return;
	default:
		ERROR_REPORT("Unsupported attribute type");	
        return;
	}
}
