                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_f601.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=server.c -o gh_f601.o -list=server.lst C:\Users\<USER>\AppData\Local\Temp\gh_f601.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_f601.s
Source File: server.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile server.c -o

                      10 ;		server.o

                      11 ;Source File:   server.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:56:07 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "server.h"


                      21 ;2: #include "MmsConst.h"


                      22 ;3: #include "platform_socket.h"


                      23 ;4: #include <platform_socket_def.h>


                      24 ;5: #include <platform_critical_section.h>


                      25 ;6: #include <debug.h>


                      26 ;7: 


                      27 ;8: extern SOCKET listenSocket;


                      28 ;9: 


                      29 ;10: static int connCounter = 0;


                      30 ;11: static CriticalSection connCounterCS;


                      31 ;12: 


                      32 ;13: 


                      33 ;14: void handleIncomingConnections(TCPConnectionHandler handleConnection)


                      34 	.text

                      35 	.align	4

                      36 handleIncomingConnections::

00000000 e92d4030     37 	stmfd	[sp]!,{r4-r5,lr}

                      38 ;15: {


                      39 

00000004 e24dd018     40 	sub	sp,sp,24

00000008 e1a04000     41 	mov	r4,r0

0000000c e59f00b8*    42 	ldr	r0,.L66

00000010 e28dc008     43 	add	r12,sp,8

00000014 e890000f     44 	ldmfd	[r0],{r0-r3}

00000018 e88c000f     45 	stmea	[r12],{r0-r3}

0000001c e3a00010     46 	mov	r0,16

00000020 e59f50a8*    47 	ldr	r5,.L67

00000024 e58d0000     48 	str	r0,[sp]

                      49 ;16: 	SOCKADDR_IN_T fromAddr = { 0 };


                      50 


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_f601.s
                      51 ;17: 	int fromAddrLen = sizeof(fromAddr);


                      52 

                      53 ;18: 	SOCKET connectionSocket;


                      54 ;19: 


                      55 ;20: 


                      56 ;21: 	CriticalSection_Init(&connCounterCS);


                      57 

00000028 e1a00005     58 	mov	r0,r5

0000002c eb000000*    59 	bl	CriticalSection_Init

                      60 ;22: 


                      61 ;23: 	while (1)


                      62 

                      63 .L6:

                      64 ;24: 	{                		


                      65 

                      66 ;25:         if (!acceptConnection(&connectionSocket, (struct sockaddr *)&fromAddr, &fromAddrLen))


                      67 

00000030 e1a0200d     68 	mov	r2,sp

00000034 e28d1008     69 	add	r1,sp,8

00000038 e28d0004     70 	add	r0,sp,4

0000003c eb000000*    71 	bl	acceptConnection

00000040 e3500000     72 	cmp	r0,0

00000044 0afffff9     73 	beq	.L6

                      74 ;26: 		{			


                      75 

                      76 ;27: 			ERROR_REPORT("'accept' function has returned an error");


                      77 ;28: 			continue;


                      78 

                      79 ;29: 		}


                      80 ;30: 		CriticalSection_Lock(&connCounterCS);


                      81 

00000048 e1a00005     82 	mov	r0,r5

0000004c eb000000*    83 	bl	CriticalSection_Lock

                      84 ;31: 		if (connCounter == MAX_CONN_COUNT)


                      85 

00000050 e59f107c*    86 	ldr	r1,.L68

00000054 e5910000     87 	ldr	r0,[r1]

00000058 e3500004     88 	cmp	r0,4

0000005c 1a000004     89 	bne	.L11

                      90 ;32: 		{


                      91 

                      92 ;33: 			closesocket(connectionSocket);


                      93 

00000060 e59d0004     94 	ldr	r0,[sp,4]

00000064 eb000000*    95 	bl	lwip_close

                      96 ;34: 			CriticalSection_Unlock(&connCounterCS);


                      97 

00000068 e1a00005     98 	mov	r0,r5

0000006c eb000000*    99 	bl	CriticalSection_Unlock

                     100 ;35: 			continue;


                     101 

00000070 eaffffee    102 	b	.L6

                     103 .L11:

                     104 ;36: 		}		


                     105 ;37: 		connCounter++;


                     106 

00000074 e2800001    107 	add	r0,r0,1

00000078 e5810000    108 	str	r0,[r1]

                     109 ;38: 		CriticalSection_Unlock(&connCounterCS);


                     110 

0000007c e1a00005    111 	mov	r0,r5


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_f601.s
00000080 eb000000*   112 	bl	CriticalSection_Unlock

                     113 ;39: 


                     114 ;40: 		handleConnection((SERVER_SOCKET)connectionSocket);


                     115 

00000084 e59d0004    116 	ldr	r0,[sp,4]

00000088 e1a0e00f    117 	mov	lr,pc

0000008c e12fff14*   118 	bx	r4

00000090 eaffffe6    119 	b	.L6

                     120 	.endf	handleIncomingConnections

                     121 	.align	4

                     122 ;fromAddr	[sp,8]	local

                     123 ;.L51	.L55	static

                     124 ;fromAddrLen	[sp]	local

                     125 ;connectionSocket	[sp,4]	local

                     126 

                     127 ;handleConnection	r4	param

                     128 

                     129 	.section ".bss","awb"

                     130 .L50:

                     131 	.data

                     132 .L52:

00000000 00000000    133 connCounter:	.data.b	0,0,0,0

                     134 	.type	connCounter,$object

                     135 	.size	connCounter,4

                     136 	.section ".bss","awb"

00000000 00         137 connCounterCS:	.space	1

                     138 	.section ".rodata","a"

00000000 00         139 .L55:	.space	1

00000001 00         140 	.space	1

00000002 0000       141 	.space	2

00000004 00000000    142 	.space	4

00000008 00000000    143 	.space	8

0000000c 00000000 
                     144 	.type	.L55,$object

                     145 	.size	.L55,16

                     146 	.data

                     147 	.text

                     148 

                     149 ;41: 	}


                     150 ;42: }


                     151 

                     152 ;43: 


                     153 ;44: void closeServerSocket(SERVER_SOCKET socket)


                     154 	.align	4

                     155 	.align	4

                     156 closeServerSocket::

00000094 e92d4030    157 	stmfd	[sp]!,{r4-r5,lr}

                     158 ;45: {


                     159 

                     160 ;46: 	CriticalSection_Lock(&connCounterCS);


                     161 

00000098 e59f4030*   162 	ldr	r4,.L67

0000009c e1a05000    163 	mov	r5,r0

000000a0 e1a00004    164 	mov	r0,r4

000000a4 eb000000*   165 	bl	CriticalSection_Lock

                     166 ;47: 	connCounter--;


                     167 

000000a8 e59f2024*   168 	ldr	r2,.L68

000000ac e5921000    169 	ldr	r1,[r2]

000000b0 e1a00004    170 	mov	r0,r4

000000b4 e2411001    171 	sub	r1,r1,1


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_f601.s
000000b8 e5821000    172 	str	r1,[r2]

                     173 ;48: 	VERIFY(connCounter >= 0);


                     174 ;49: 	CriticalSection_Unlock(&connCounterCS);


                     175 

000000bc eb000000*   176 	bl	CriticalSection_Unlock

                     177 ;50: 	closesocket((SOCKET)socket);	


                     178 

000000c0 e1a00005    179 	mov	r0,r5

000000c4 e8bd4030    180 	ldmfd	[sp]!,{r4-r5,lr}

000000c8 ea000000*   181 	b	lwip_close

                     182 	.endf	closeServerSocket

                     183 	.align	4

                     184 

                     185 ;socket	r5	param

                     186 

                     187 	.data

                     188 	.text

                     189 

                     190 ;51: }


                     191 	.align	4

                     192 .L66:

000000cc 00000000*   193 	.data.w	.L55

                     194 	.type	.L66,$object

                     195 	.size	.L66,4

                     196 

                     197 .L67:

000000d0 00000000*   198 	.data.w	connCounterCS

                     199 	.type	.L67,$object

                     200 	.size	.L67,4

                     201 

                     202 .L68:

000000d4 00000000*   203 	.data.w	.L52

                     204 	.type	.L68,$object

                     205 	.size	.L68,4

                     206 

                     207 	.align	4

                     208 ;connCounter	.L52	static

                     209 ;connCounterCS	connCounterCS	static

                     210 

                     211 	.data

                     212 	.ghsnote version,6

                     213 	.ghsnote tools,3

                     214 	.ghsnote options,0

                     215 	.text

                     216 	.align	4

                     217 	.data

                     218 	.align	4

                     219 	.section ".rodata","a"

                     220 	.align	4

                     221 	.text

