                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9781.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=iedFC.c -o iedTree\gh_9781.o -list=iedTree/iedFC.lst C:\Users\<USER>\AppData\Local\Temp\gh_9781.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_9781.s
Source File: iedFC.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      10 ;		iedTree/iedFC.c -o iedTree/iedFC.o

                      11 ;Source File:   iedTree/iedFC.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Oct 07 09:39:19 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "iedFC.h"


                      21 ;2: 


                      22 ;3: 


                      23 ;4: enum IEDFCType


                      24 ;5: {    


                      25 ;6:     IED_FC_TYPE_ST = 0,


                      26 ;7:     IED_FC_TYPE_MX,


                      27 ;8:     IED_FC_TYPE_SP,


                      28 ;9:     IED_FC_TYPE_SV,


                      29 ;10:     IED_FC_TYPE_CF,


                      30 ;11:     IED_FC_TYPE_DC,


                      31 ;12:     IED_FC_TYPE_SG,


                      32 ;13:     IED_FC_TYPE_SE,


                      33 ;14:     IED_FC_TYPE_SR,


                      34 ;15:     IED_FC_TYPE_OR,


                      35 ;16:     IED_FC_TYPE_BL,


                      36 ;17:     IED_FC_TYPE_EX,


                      37 ;18: 


                      38 ;19:     // Не упоминаются в 7.3 Annex B


                      39 ;20:     IED_FC_TYPE_CO,


                      40 ;21:     IED_FC_TYPE_US,


                      41 ;22:     IED_FC_TYPE_MS,


                      42 ;23:     IED_FC_TYPE_RP,


                      43 ;24:     IED_FC_TYPE_BR,


                      44 ;25:     IED_FC_TYPE_GO,


                      45 ;26: 


                      46 ;27:     IED_FC_TYPE_COUNT,


                      47 ;28:     IED_FC_TYPE_UNKNOWN = 0xFF,


                      48 ;29: };


                      49 ;30: 


                      50 ;31: const char* IEDFCTypeNames[IED_FC_TYPE_COUNT] = {



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9781.s
                      51 ;32:     "ST", 


                      52 ;33:     "MX", 


                      53 ;34:     "SP",


                      54 ;35:     "SV",


                      55 ;36:     "CF",


                      56 ;37:     "DC",


                      57 ;38:     "SG",


                      58 ;39:     "SE",


                      59 ;40:     "SR",


                      60 ;41:     "OR",


                      61 ;42:     "BL",


                      62 ;43:     "EX",    


                      63 ;44:     "CO",


                      64 ;45:     "US",


                      65 ;46:     "MS",


                      66 ;47:     "RP",


                      67 ;48:     "BR",


                      68 ;49:     "GO"


                      69 ;50: };


                      70 ;51: 


                      71 ;52: static enum IEDFCType getFCType(StringView* fcName)


                      72 

                      73 ;67: }


                      74 

                      75 ;68: 


                      76 ;69: static bool isReadOnlyFC(enum IEDFCType fcType)


                      77 

                      78 ;82:     }


                      79 ;83: }


                      80 

                      81 ;84: 


                      82 ;85: 


                      83 ;86: bool IEDFC_init(IEDEntity entity)


                      84 	.text

                      85 	.align	4

                      86 IEDFC_init::

00000000 e92d44f0     87 	stmfd	[sp]!,{r4-r7,r10,lr}

00000004 e1a05000     88 	mov	r5,r0

                      89 ;87: {


                      90 

                      91 ;88:     enum IEDFCType fcType;


                      92 ;89:     entity->type = IED_ENTITY_FC;


                      93 

00000008 e3a00004     94 	mov	r0,4

0000000c e5850050     95 	str	r0,[r5,80]

                      96 ;90:     fcType = getFCType(&entity->name);


                      97 

                      98 ;53: {    


                      99 

                     100 ;54:     int i;


                     101 ;55:     if(fcName->len != 2)


                     102 

00000010 e5950048    103 	ldr	r0,[r5,72]

00000014 e3500002    104 	cmp	r0,2

00000018 1a00004c    105 	bne	.L112

                     106 ;56:     {


                     107 

                     108 ;57:         return IED_FC_TYPE_UNKNOWN;


                     109 

                     110 ;58:     }


                     111 ;59:     for(i = 0; i < IED_FC_TYPE_COUNT; i++)



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9781.s
                     112 

0000001c e3a04000    113 	mov	r4,0

00000020 e59fa148*   114 	ldr	r10,.L522

00000024 e3a07002    115 	mov	r7,2

00000028 e1a0600a    116 	mov	r6,r10

                     117 .L161:

0000002c e7961104    118 	ldr	r1,[r6,r4 lsl 2]

00000030 e2850048    119 	add	r0,r5,72

00000034 eb000000*   120 	bl	StringView_cmpCStr

00000038 e3500000    121 	cmp	r0,0

0000003c 0a000038    122 	beq	.L57

00000040 e2844001    123 	add	r4,r4,1

00000044 e7961104    124 	ldr	r1,[r6,r4 lsl 2]

00000048 e2850048    125 	add	r0,r5,72

0000004c eb000000*   126 	bl	StringView_cmpCStr

00000050 e3500000    127 	cmp	r0,0

00000054 0a000032    128 	beq	.L57

00000058 e2844001    129 	add	r4,r4,1

0000005c e7961104    130 	ldr	r1,[r6,r4 lsl 2]

00000060 e2850048    131 	add	r0,r5,72

00000064 eb000000*   132 	bl	StringView_cmpCStr

00000068 e3500000    133 	cmp	r0,0

0000006c 0a00002c    134 	beq	.L57

00000070 e2844001    135 	add	r4,r4,1

00000074 e7961104    136 	ldr	r1,[r6,r4 lsl 2]

00000078 e2850048    137 	add	r0,r5,72

0000007c eb000000*   138 	bl	StringView_cmpCStr

00000080 e3500000    139 	cmp	r0,0

00000084 0a000026    140 	beq	.L57

00000088 e2844001    141 	add	r4,r4,1

0000008c e7961104    142 	ldr	r1,[r6,r4 lsl 2]

00000090 e2850048    143 	add	r0,r5,72

00000094 eb000000*   144 	bl	StringView_cmpCStr

00000098 e3500000    145 	cmp	r0,0

0000009c 0a000020    146 	beq	.L57

000000a0 e2844001    147 	add	r4,r4,1

000000a4 e7961104    148 	ldr	r1,[r6,r4 lsl 2]

000000a8 e2850048    149 	add	r0,r5,72

000000ac eb000000*   150 	bl	StringView_cmpCStr

000000b0 e3500000    151 	cmp	r0,0

000000b4 0a00001a    152 	beq	.L57

000000b8 e2844001    153 	add	r4,r4,1

000000bc e7961104    154 	ldr	r1,[r6,r4 lsl 2]

000000c0 e2850048    155 	add	r0,r5,72

000000c4 eb000000*   156 	bl	StringView_cmpCStr

000000c8 e3500000    157 	cmp	r0,0

000000cc 0a000014    158 	beq	.L57

000000d0 e2844001    159 	add	r4,r4,1

000000d4 e7961104    160 	ldr	r1,[r6,r4 lsl 2]

000000d8 e2850048    161 	add	r0,r5,72

000000dc eb000000*   162 	bl	StringView_cmpCStr

000000e0 e3500000    163 	cmp	r0,0

000000e4 0a00000e    164 	beq	.L57

000000e8 e2844001    165 	add	r4,r4,1

000000ec e2577001    166 	subs	r7,r7,1

000000f0 1affffcd    167 	bne	.L161

000000f4 e3a07002    168 	mov	r7,2

000000f8 e08a6104    169 	add	r6,r10,r4 lsl 2

                     170 .L185:

000000fc e5961000    171 	ldr	r1,[r6]

00000100 e2850048    172 	add	r0,r5,72


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9781.s
00000104 eb000000*   173 	bl	StringView_cmpCStr

00000108 e3500000    174 	cmp	r0,0

0000010c 0a000004    175 	beq	.L57

00000110 e2866004    176 	add	r6,r6,4

00000114 e2844001    177 	add	r4,r4,1

00000118 e2577001    178 	subs	r7,r7,1

0000011c 1afffff6    179 	bne	.L185

00000120 ea00000a    180 	b	.L112

                     181 .L57:

                     182 ;64:         }


                     183 ;65:     }


                     184 ;66:     return IED_FC_TYPE_UNKNOWN;


                     185 

                     186 ;91:     entity->subType = fcType;


                     187 

00000124 e5854054    188 	str	r4,[r5,84]

                     189 ;92:     if(isReadOnlyFC(fcType))


                     190 

                     191 ;70: {


                     192 

                     193 ;71:     switch(fcType)


                     194 

00000128 e2540001    195 	subs	r0,r4,1

0000012c 9a00000a    196 	bls	.L68

00000130 e2500002    197 	subs	r0,r0,2

00000134 0a000008    198 	beq	.L68

00000138 e2500002    199 	subs	r0,r0,2

0000013c 0a000006    200 	beq	.L68

00000140 e2500005    201 	subs	r0,r0,5

00000144 e3500001    202 	cmp	r0,1

00000148 9a000003    203 	bls	.L68

0000014c ea000005    204 	b	.L55

                     205 .L112:

00000150 e3a000ff    206 	mov	r0,255

00000154 e5850054    207 	str	r0,[r5,84]

                     208 ;95:     }


                     209 ;96:     return true;


                     210 

00000158 ea000002    211 	b	.L55

                     212 .L68:

                     213 ;72:     {


                     214 ;73:     case IED_FC_TYPE_ST:


                     215 ;74:     case IED_FC_TYPE_MX:


                     216 ;75:     case IED_FC_TYPE_SV:    


                     217 ;76:     case IED_FC_TYPE_DC:// У нас пока description ReadOnly        


                     218 ;77:     case IED_FC_TYPE_BL:


                     219 ;78:     case IED_FC_TYPE_EX:


                     220 ;79:         return true;


                     221 

                     222 ;80:     default:


                     223 ;81:         return false;


                     224 

                     225 ;93:     {


                     226 

                     227 ;94:         IEDEntity_setReadOnlyRecursive(entity, true);


                     228 

0000015c e1a00005    229 	mov	r0,r5

00000160 e3a01001    230 	mov	r1,1

00000164 eb000000*   231 	bl	IEDEntity_setReadOnlyRecursive

                     232 ;95:     }


                     233 ;96:     return true;



                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9781.s
                     234 

                     235 .L55:

00000168 e3a00001    236 	mov	r0,1

0000016c e8bd84f0    237 	ldmfd	[sp]!,{r4-r7,r10,pc}

                     238 	.endf	IEDFC_init

                     239 	.align	4

                     240 ;i	r4	local

                     241 

                     242 ;entity	r5	param

                     243 

                     244 	.section ".bss","awb"

                     245 .L446:

                     246 	.section ".rodata","a"

                     247 .L447:;	"ST\000"

00000000 5453       248 	.data.b	83,84

00000002 00         249 	.data.b	0

                     250 .L448:;	"MX\000"

00000003 584d       251 	.data.b	77,88

00000005 00         252 	.data.b	0

                     253 .L449:;	"SP\000"

00000006 5053       254 	.data.b	83,80

00000008 00         255 	.data.b	0

                     256 .L450:;	"SV\000"

00000009 5653       257 	.data.b	83,86

0000000b 00         258 	.data.b	0

                     259 .L451:;	"CF\000"

0000000c 4643       260 	.data.b	67,70

0000000e 00         261 	.data.b	0

                     262 .L452:;	"DC\000"

0000000f 4344       263 	.data.b	68,67

00000011 00         264 	.data.b	0

                     265 .L453:;	"SG\000"

00000012 4753       266 	.data.b	83,71

00000014 00         267 	.data.b	0

                     268 .L454:;	"SE\000"

00000015 4553       269 	.data.b	83,69

00000017 00         270 	.data.b	0

                     271 .L455:;	"SR\000"

00000018 5253       272 	.data.b	83,82

0000001a 00         273 	.data.b	0

                     274 .L456:;	"OR\000"

0000001b 524f       275 	.data.b	79,82

0000001d 00         276 	.data.b	0

                     277 .L457:;	"BL\000"

0000001e 4c42       278 	.data.b	66,76

00000020 00         279 	.data.b	0

                     280 .L458:;	"EX\000"

00000021 5845       281 	.data.b	69,88

00000023 00         282 	.data.b	0

                     283 .L459:;	"CO\000"

00000024 4f43       284 	.data.b	67,79

00000026 00         285 	.data.b	0

                     286 .L460:;	"US\000"

00000027 5355       287 	.data.b	85,83

00000029 00         288 	.data.b	0

                     289 .L461:;	"MS\000"

0000002a 534d       290 	.data.b	77,83

0000002c 00         291 	.data.b	0

                     292 .L462:;	"RP\000"

0000002d 5052       293 	.data.b	82,80

0000002f 00         294 	.data.b	0


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9781.s
                     295 .L463:;	"BR\000"

00000030 5242       296 	.data.b	66,82

00000032 00         297 	.data.b	0

                     298 .L464:;	"GO\000"

00000033 4f47       299 	.data.b	71,79

00000035 00         300 	.data.b	0

                     301 	.data

                     302 	.text

                     303 

                     304 ;97: }


                     305 	.align	4

                     306 .L522:

00000170 00000000*   307 	.data.w	IEDFCTypeNames

                     308 	.type	.L522,$object

                     309 	.size	.L522,4

                     310 

                     311 	.align	4

                     312 ;.L529	.L447	static

                     313 ;.L530	.L448	static

                     314 ;.L531	.L449	static

                     315 ;.L532	.L450	static

                     316 ;.L533	.L451	static

                     317 ;.L534	.L452	static

                     318 ;.L535	.L453	static

                     319 ;.L536	.L454	static

                     320 ;.L537	.L455	static

                     321 ;.L538	.L456	static

                     322 ;.L539	.L457	static

                     323 ;.L540	.L458	static

                     324 ;.L541	.L459	static

                     325 ;.L542	.L460	static

                     326 ;.L543	.L461	static

                     327 ;.L544	.L462	static

                     328 ;.L545	.L463	static

                     329 ;.L546	.L464	static

                     330 

                     331 	.data

                     332 .L550:

                     333 	.globl	IEDFCTypeNames

00000000 00000000*   334 IEDFCTypeNames:	.data.w	.L447

00000004 00000000*   335 	.data.w	.L448

00000008 00000000*   336 	.data.w	.L449

0000000c 00000000*   337 	.data.w	.L450

00000010 00000000*   338 	.data.w	.L451

00000014 00000000*   339 	.data.w	.L452

00000018 00000000*   340 	.data.w	.L453

0000001c 00000000*   341 	.data.w	.L454

00000020 00000000*   342 	.data.w	.L455

00000024 00000000*   343 	.data.w	.L456

00000028 00000000*   344 	.data.w	.L457

0000002c 00000000*   345 	.data.w	.L458

00000030 00000000*   346 	.data.w	.L459

00000034 00000000*   347 	.data.w	.L460

00000038 00000000*   348 	.data.w	.L461

0000003c 00000000*   349 	.data.w	.L462

00000040 00000000*   350 	.data.w	.L463

00000044 00000000*   351 	.data.w	.L464

                     352 	.type	IEDFCTypeNames,$object

                     353 	.size	IEDFCTypeNames,72

                     354 	.ghsnote version,6

                     355 	.ghsnote tools,3


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9781.s
                     356 	.ghsnote options,0

                     357 	.text

                     358 	.align	4

                     359 	.data

                     360 	.align	4

                     361 	.text

