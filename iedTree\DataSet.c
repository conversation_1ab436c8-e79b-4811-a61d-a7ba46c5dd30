#include "DataSet.h"

#include "../stringView.h"
#include "iedEntity.h"
#include "iedTree.h"
#include "../bufViewBER.h"
#include "../AsnEncoding.h"

#include <debug.h>


bool DataSet_init(IEDEntity entity)
{
    uint8_t tag;
    BufferView dataSetBER;
    DataSet* dataSet;
    DataSetItem** pNextItem;
    entity->type = IED_ENTITY_DATASET;
    dataSet = IEDEntity_alloc(sizeof(DataSet));
    if(dataSet == NULL)
    {
        return false;
    }
    entity->extInfo = dataSet;

    dataSetBER = entity->ber;

    //Пропускаем тэг и длину
    if(!BufferView_decodeTL(&dataSetBER, NULL, NULL, NULL))
    {
        ERROR_REPORT("DataSet init error");
        return false;
    }

    //Пропускаем имя
    if(!BufferView_skipObject(&dataSetBER, ASN_VISIBLE_STRING, true))
    {
        ERROR_REPORT("DataSet init error");
        return false;
    }

    //Пропускаем описание
    if(!BufferView_skipObject(&dataSetBER, ASN_OCTET_STRING, true))
    {
        ERROR_REPORT("DataSet init error");
        return false;
    }

    pNextItem = &dataSet->firstItem;

    //Получаем список элементов
    while(!BufferView_endOfBuf(&dataSetBER))
    {
        StringView ldName;
        StringView objName;        
        DataSetItem* dataSetItem;

        //Читаем TL
        if(!BufferView_decodeTL(&dataSetBER, &tag, NULL, NULL)
                || tag != ASN_SEQUENCE)
        {
            return false;
        }
        //Читаем имя переменной
        if(!BufferView_decodeStringViewTL(&dataSetBER, ASN_VISIBLE_STRING,
                                          &ldName)
             || !BufferView_decodeStringViewTL(&dataSetBER, ASN_VISIBLE_STRING,
                                               &objName))
        {
            return false;
        }
        //Пропускаем позицию
        if(!BufferView_skipObject(&dataSetBER, ASN_INTEGER, true))
        {
            return false;
        }

        dataSetItem = IEDEntity_alloc(sizeof(DataSetItem));
        if(dataSetItem == NULL)
        {
            ERROR_REPORT("DataSet item alloc error");
            return false;
        }

        dataSetItem->domainID = ldName;
        dataSetItem->itemID = objName;        		

        *pNextItem = dataSetItem;
        pNextItem = &dataSetItem->next;

		dataSet->itemCount++;
    }
    return true;
}

bool DataSet_postCreate(IEDEntity entity)
{
    DataSetItem* dataSetItem;
    DataSet* dataSet = entity->extInfo;

    if(dataSet == NULL)
    {
        return false;
    }

    dataSetItem = dataSet->firstItem;

    while(dataSetItem != NULL)
    {
        IEDEntity obj;
        obj = IEDTree_findDataByFullName(
                    &dataSetItem->domainID, &dataSetItem->itemID);
        if(obj == NULL)
        {
            ERROR_REPORT("DataSet item is not found");
            return false;
        }
        dataSetItem->obj = obj;

        dataSetItem = dataSetItem->next;
    }
    return true;
}


DataSet *DataSet_getDataSetObj(IEDEntity entity)
{
	if(entity->type != IED_ENTITY_DATASET)
	{
		ERROR_REPORT("Not DataSet");
		return NULL;
	}
	return entity->extInfo;
}


DataSetItem *DataSet_getFirstItem(IEDEntity entity)
{
	DataSet* dataSet;

	if(entity->type != IED_ENTITY_DATASET)
	{
		ERROR_REPORT("Not DataSet");
		return NULL;
	}

	dataSet =  entity->extInfo;

	return dataSet->firstItem;
}


bool DataSet_calcReadLen(IEDEntity dsEntity, size_t *pLen)
{
	DataSet* dataSet;
	DataSetItem* dataSetItem;

	if(dsEntity->type != IED_ENTITY_DATASET)
	{
		ERROR_REPORT("Not DataSet");
		return false;
	}

	dataSet =  dsEntity->extInfo;
	dataSetItem = dataSet->firstItem;

	*pLen = 0;
	while(dataSetItem != NULL)
	{
		size_t entityReadLen;
		IEDEntity entity = dataSetItem->obj;

		if(!entity->calcReadLen(entity, &entityReadLen))
		{
			ERROR_REPORT("Unable to calc DataSet itrm len");
			return false;
		}

		*pLen += entityReadLen;
		dataSetItem = dataSetItem->next;
	}

	return true;
}

bool DataSet_encodeRead(IEDEntity dsEntity, BufferView *outBuf)
{
	DataSet* dataSet;
	DataSetItem* dataSetItem;

	if(dsEntity->type != IED_ENTITY_DATASET)
	{
		ERROR_REPORT("Not DataSet");
		return false;
	}

	dataSet =  dsEntity->extInfo;
	dataSetItem = dataSet->firstItem;

	while(dataSetItem != NULL)
	{
		IEDEntity entity = dataSetItem->obj;
		if(!entity->encodeRead(entity,outBuf))
		{
			ERROR_REPORT("Unable to read DataSet item");
			return false;
		}
		dataSetItem = dataSetItem->next;
	}
	return true;
}
