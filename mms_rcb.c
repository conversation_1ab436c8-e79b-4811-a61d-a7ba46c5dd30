#include "mms_rcb.h"
#include "iedmodel.h"
#include "mms_data.h"
#include "rcb.h"
#include "reports.h"
#include "IEDCompile/AccessInfo.h"
#include "IEDCompile/InnerAttributeTypes.h"

#include "debug.h"
#include "AsnEncoding.h"

#include <stddef.h>
#include <stdint.h>

int encodeAccessAttrRCB(uint8_t* outBuf, int bufPos, int accessDataPos, bool determineSize)
{
    CBAttrAccessInfo* pAccessInfo =
            (CBAttrAccessInfo*)getAlignedDescrStruct(accessDataPos);
    if(pAccessInfo == NULL)
    {
        ERROR_REPORT("Unable to get access info struct");
        return 0;
    }

    switch(pAccessInfo->attrCode)
    {
        case RptEna:
		case GI:
        case Resv:
            return encodeAccessAttrBoolean( outBuf, bufPos, determineSize);
		case SqNum:
			return encodeAccessAttrUInt(outBuf, bufPos, 16, determineSize);
		case IntgPd:
			return encodeAccessAttrUInt(outBuf, bufPos, 32, determineSize);
		case EntryID:
			return encodeAccessAttrString(outBuf, bufPos,
				IEC61850_BER_OCTET_STRING, 8, determineSize);
		case ConfRev:
			return encodeAccessAttrUInt(outBuf, bufPos, 32, determineSize);
        default :
            ERROR_REPORT("Invalid RCB DA code");
            return 0;
    }
}

/*
static int determineAttrRCBReadSize(CBAttrAccessInfo* pAccessInfo)
{
    switch(pAccessInfo->attrCode){
        case RptEna:
		case GI:
            return 3; // boolean
		case SqNum:
			!!
			return encodeUInt32Value(uint8_t* outBuf, int bufPos, uint32_t value,
				TRUE)
		case EntryID:
			!!!
        default:
            ERROR_REPORT("Invalid RCB DA code");
            return 0;
    }
}
*/

static int encodeReadRptEna(uint8_t* outBuf, int bufPos, 
	CBAttrAccessInfo* descrStruct, bool determineSize)
{
	RCB* pRCB;
	int rcbIndex = descrStruct->rcbIndex;

	bool value = false;

	if (getRCB(rcbIndex, &pRCB))
	{
		value = pRCB->rptEna;
	}

	return encodeBoolValue(outBuf, bufPos, value, determineSize);    
}

static int encodeReadResv(uint8_t* outBuf, int bufPos,
    CBAttrAccessInfo* descrStruct, bool determineSize)
{
    RCB* pRCB;
    int rcbIndex = descrStruct->rcbIndex;

    bool value = false;

    if (getRCB(rcbIndex, &pRCB))
    {
        value = pRCB->resv;
    }

    return encodeBoolValue(outBuf, bufPos, value, determineSize);
}

static int encodeReadGI(uint8_t* outBuf, int bufPos,
	CBAttrAccessInfo* descrStruct, bool determineSize)
{
	RCB* pRCB;
	int rcbIndex = descrStruct->rcbIndex;

	bool value = false;

	if (getRCB(rcbIndex, &pRCB))
	{
		value = pRCB->gi;
	}

	return encodeBoolValue(outBuf, bufPos, value, determineSize);
}

static int encodeReadSqNum(uint8_t* outBuf, int bufPos,
	CBAttrAccessInfo* descrStruct, bool determineSize)
{
	RCB* pRCB;
	int rcbIndex = descrStruct->rcbIndex;

	uint16_t value = 0;

	if (getRCB(rcbIndex, &pRCB))
	{
		value = pRCB->sqNum;
	}

	return encodeUInt32Value(outBuf, bufPos, value, determineSize);
}

static int encodeReadIntgPd(uint8_t* outBuf, int bufPos,
	CBAttrAccessInfo* descrStruct, bool determineSize)
{
	RCB* pRCB;
	int rcbIndex = descrStruct->rcbIndex;

	uint32_t value = 0;

	if (getRCB(rcbIndex, &pRCB))
	{
		value = pRCB->intgPd;
	}

	return encodeUInt32Value(outBuf, bufPos, value, determineSize);
}

static int encodeReadConfRev(uint8_t* outBuf, int bufPos,
	CBAttrAccessInfo* descrStruct, bool determineSize)
{
	RCB* pRCB;
	int rcbIndex = descrStruct->rcbIndex;

	uint32_t value = 0;

	if (getRCB(rcbIndex, &pRCB))
	{
		value = pRCB->confRev;
	}

	return encodeUInt32Value(outBuf, bufPos, value, determineSize);
}

static int encodeReadEntryID(uint8_t* outBuf, int bufPos,
	CBAttrAccessInfo* descrStruct, bool determineSize)
{
	RCB* pRCB;
	int rcbIndex = descrStruct->rcbIndex;

	uint64_t value = 0;

	if (getRCB(rcbIndex, &pRCB))
	{
		value = pRCB->entryID;
	}
	
	return encodeOctetString8Value(outBuf, bufPos, &value, determineSize);
}

int encodeReadAttrRCB(uint8_t* outBuf, int bufPos, void* descrStruct, bool determineSize)
{
    CBAttrAccessInfo* pAccessInfo =  descrStruct;
    
    switch (pAccessInfo->attrCode) {
    case RptEna:
        return encodeReadRptEna(outBuf, bufPos, pAccessInfo, determineSize);
    case Resv:
        return encodeReadResv(outBuf, bufPos, pAccessInfo, determineSize);
	case GI:
		return encodeReadGI(outBuf, bufPos, pAccessInfo, determineSize);
	case SqNum:
		return encodeReadSqNum(outBuf, bufPos, pAccessInfo, determineSize);
	case IntgPd:
		return encodeReadIntgPd(outBuf, bufPos, pAccessInfo, determineSize);
	case EntryID:
		return encodeReadEntryID(outBuf, bufPos, pAccessInfo, determineSize);
	case ConfRev:
		return encodeReadConfRev(outBuf, bufPos, pAccessInfo, determineSize);
    default:
        ERROR_REPORT("Invalid RCB DA code");
        return 0;
    }
}

static void writeRptEna(IsoConnection* isoConn, int rcbIndex, uint8_t* dataToWrite)
{	
	bool value;
	PReporter pReporter;

	if (dataToWrite[0] != IEC61850_BER_BOOLEAN || dataToWrite[1] != 1)
	{
		return;
	}
	value = dataToWrite[2] != 0;
	
	pReporter = getReporterByIndex(rcbIndex);
	if (pReporter == NULL)
	{
		ERROR_REPORT("Writing to unregistered report");
		return;
	}
	Reporter_setEnable(pReporter, isoConn, value);	
}

static void writeResv(IsoConnection* isoConn, int rcbIndex, uint8_t* dataToWrite)
{
    bool value;
    PReporter pReporter;

    if (dataToWrite[0] != IEC61850_BER_BOOLEAN || dataToWrite[1] != 1)
    {
        return;
    }
    value = dataToWrite[2] != 0;

    pReporter = getReporterByIndex(rcbIndex);
    if (pReporter == NULL)
    {
        ERROR_REPORT("Writing to unregistered report");
        return;
    }
    Reporter_setResv(pReporter, isoConn, value);
}

void writeGI(IsoConnection* isoConn, int rcbIndex, uint8_t* dataToWrite)
{
    PReporter pReporter;

    //Сюда надо ещё проверку включен ли GI в TrgOp

	if (dataToWrite[0] != IEC61850_BER_BOOLEAN || dataToWrite[1] != 1)
	{
		return;
	}	

    pReporter = getReporterByIndex(rcbIndex);
    if(pReporter == NULL)
    {
        ERROR_REPORT("Writing to unregistered report");
        return;
    }
    
    if(!isRCBConnected(pReporter)
            || Reporter_isOwnerConnection(pReporter, isoConn))
    {
        Reporter_setGI(pReporter, dataToWrite[2]);
    }

}

void writeIntgPd(IsoConnection* isoConn, int rcbIndex, uint8_t* dataToWrite)
{
	PReporter pReporter;
	size_t len;
	uint32_t intgPd;

	if (dataToWrite[0] != IEC61850_BER_UNSIGNED_INTEGER)
	{
		return;
	}

	len = dataToWrite[1];	
	intgPd = BerDecoder_decodeUint32(dataToWrite, len, 2);

	pReporter = getReporterByIndex(rcbIndex);
	if (pReporter == NULL)
	{
		ERROR_REPORT("Writing to unregistered report");
		return;
	}
    
    if(!isRCBConnected(pReporter)
            || Reporter_isOwnerConnection(pReporter, isoConn))
    {
        Reporter_setIntgPd(pReporter, intgPd);
    }
}

void writeAttrRCB(struct IsoConnection* isoConn, void* descrStruct, uint8_t* dataToWrite)
{
	CBAttrAccessInfo* pAccessInfo = descrStruct;

	switch (pAccessInfo->attrCode)
	{
	case RptEna:
		writeRptEna(isoConn, pAccessInfo->rcbIndex, dataToWrite);
		break;
    case Resv:
        writeResv(isoConn, pAccessInfo->rcbIndex, dataToWrite);
        break;
	case GI:
		writeGI(isoConn, pAccessInfo->rcbIndex, dataToWrite);
		break;
	case IntgPd:
        writeIntgPd(isoConn, pAccessInfo->rcbIndex, dataToWrite);
		break;
	case EntryID:
	case SqNum:
		break;
	default:
		ERROR_REPORT("Unsupported RCB attribute");
	}
	
}
