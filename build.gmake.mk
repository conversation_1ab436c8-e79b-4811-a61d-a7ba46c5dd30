# ==============
# Using GNU Make
# ==============
# This Makefile is intended for Windows with cmd.exe (not Unix shell)

# принудительно указывается интерпретатор (чтобы не подхватился bash.exe)
SHELL = cmd.exe

define TEMPLATE

-map
-e _init00
-Y UL,$(AT91CORE_DIR)\bin
-L $(AT91CORE_DIR)\bin
-a
-Q n
$(AT91CORE_DIR)\bin\c0_bss.obj
$(AT91CORE_DIR)\bin\gmalloc.lib
$(AT91CORE_DIR)\bin\ARMLIBV4.lib
$(AT91CORE_DIR)\bin\CLib.lib
$(LWIP_LIBS)
$(SYSLOG_LIB)
$(ETHBUS_LIB)
$(AT91CORE_DIR)\GHSLIB\lib\libansi.a
$(AT91CORE_DIR)\GHSLIB\lib\libarch.a
$(AT91CORE_DIR)\GHSLIB\lib\libind.a
$(AT91CORE_DIR)\GHSLIB\lib\indarchk.o
$(AT91CORE_DIR)\GHSLIB\lib\indarchj.o
$(AT91CORE_DIR)\GHSLIB\lib\ccvsprnt.o
$(AT91CORE_DIR)\GHSLIB\lib\ccllout.o
$(AT91CORE_DIR)\GHSLIB\lib\ccefgout.o


MEMORY
{        
        SRAM1 : ORIGIN = 0x70528644 , LENGTH = 5000K
}

SECTIONS
{
	.text      ALIGN ( 4 )  : > SRAM1
	.interfunc ALIGN ( 4 )  : > .
	.rodata    ALIGN ( 4 )  : > .
	.fixaddr   ALIGN ( 4 )  : > .
	.fixtype   ALIGN ( 4 )  : > .	
	.data      ALIGN ( 4 )  : > .
	.bss       ALIGN ( 4 )  : > .
	.ghsinfo   ALIGN ( 4 )  : > .
	.stack	   ALIGN ( 4 ) MIN_SIZE ( __STACK_SIZE ) : > .
}
endef

$(file > build.cmd.tmp, $(TEMPLATE))

# Это фиктивная цель, чтобы запустить make
create:
	@echo Creating build.cmd.tmp
