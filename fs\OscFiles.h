#pragma once

#include "../stringView.h"
#include "../bufView.h"
#include "../frsm.h"
#include "FSTypes.h"
#include <types.h>

//Доступ к осциллограммам

bool OSCFS_init(void);
//! выделение памяти в пуле
void *OscFiles_malloc(size_t size);
void *OscFiles_realloc(void *p, size_t size);
//! освобождение памяти
void OscFiles_free(void *p);



FNameErrCode OSCFS_findFirst(StringView* startFileName, FSFindData* findData, 
	BufferView* fnameBuf);
FNameErrCode OSCFS_findNext(FSFindData* findData, BufferView* fnameBuf);
void OSCFS_findClose(FSFindData* findData);
bool OSCFS_openFile(StringView* fileName, FRSM* frsm, FSFileAttr* attr);
bool OSCFS_closeFile(FRSM* frsm);
bool OSCFS_readFile(FRSM* frsm, BufferView* readBuf, bool* moreFollows);

