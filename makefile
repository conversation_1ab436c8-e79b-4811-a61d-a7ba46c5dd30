# ==============
# Using GNU Make
# ==============
# This Makefile is intended for Windows with cmd.exe (not Unix shell)

# принудительно указывается интерпретатор (чтобы не подхватился bash.exe)
SHELL = cmd.exe

# GHS Multi глючит при параллельной сборке, поэтому принудительно запрещаем
.NOTPARALLEL:

MAKEFLAGS += --no-builtin-rules

PNAME = MMSServer

GHS_PATH = c:/GHS/arm423
CC = $(GHS_PATH)/ccarm.exe
LD = $(GHS_PATH)/elxr.exe
POSTLINK = postlink2


#SYSLOG=1
#__DEBUG=1

LWIPPORT_DIR = ../../../../lwipport
include $(LWIPPORT_DIR)/lwiplib.mk
export LWIP_INCLUDE LWIP_LIBS

ifdef SYSLOG
SYSLOG_DEF = -DSYSLOG
export SYSLOG_LIB = ../../../../loggers/syslog/syslog.lib
endif




ifeq ($(PLATFORM),IEDNEXUS)
#Модуль связи контроллера присоединения
TIMERS = timers_iednexus
DATA_SLICE = DataSlice
DESC = MMSServer-IEDNEXUS

else ifeq ($(PLATFORM),DRP150)
#DRP150
TIMERS = timers_other
DATA_SLICE = DataSliceDRP150
DESC = MMSServer-DRP150

else ifeq ($(PLATFORM),OTHER)
#Другая плата интерфейсов (PPI5 для УСО и DRP100 )
TIMERS = timers_other
DATA_SLICE = DataSlice
DESC = MMSServer-OTHER

else
PLATFORM_ERROR=1
endif

export AT91CORE_DIR = ../../../../AT91CORE
CDIR = $(AT91CORE_DIR)


export ETHBUS_LIB = $(CDIR)/modules/BayController/ethbus/ethbus.lib
ETHBUS_DEF = -I$(CDIR)/modules/BayController/ethbus

CFLAGS = -bigswitch  -c -list -cpu=arm7tm -passsource  -nostdinc \
-no_discard_zero_initializers \
--prototype_errors \
-I$(CDIR)/CLIB -I$(CDIR)/CLIB/ansi \
-I$(CDIR)/modules/SYSDLL/include \
-I$(CDIR)/modules/net/include -I../../.. -Iplatform \
 $(LWIP_INCLUDE) $(SOCKET_DEF) $(SYSLOG_DEF) $(ETHBUS_DEF) \
 -I../../../../loggers/syslog

 # CFLAGS += --quit_after_warnings

ifdef __DEBUG
CFLAGS = -G $(CFLAGS) -D__DEBUG
BUILDMK = build.gmake.mk
else
CFLAGS += -Ospeed -Oslowcompile
BUILDMK = build.gmake.mk
endif



LFLAGS = -cmd=build.cmd.tmp
STACK = CONSTANTS { __STACK_SIZE = 0x1000 }

DEPH =  \
	iedTree/iedTree.h \
	iedTree/iedEntity.h \
	iedTree/iedObjects.h \
	iedTree/iedFC.h \
	iedTree/iedControlModel.h \
	iedTree/iedFinalDA.h \
	iedTree/iedQuality.h \
	iedTree/iedBool.h \
	iedTree/iedFloat.h \
	iedTree/iedCodedEnum.h \
	iedTree/iedInt.h \
	iedTree/iedUInt.h \
	iedTree/iedEnum.h \
	iedTree/iedTimeStamp.h \
	iedTree/iedConstDA.h \
	iedTree/iedNoEntity.h \
	iedTree/DataSet.h \
	\
	reportItems/RptItemTypes.h \
	reportItems/RptItem.h \
	reportItems/RptDataSet.h \
	reportItems/RptBool.h \
	\
	RptProcContext.h \
	\
	ObjectNameParser.h \
	infoReport.h \
	control.h \
	fast_memcpy.h \
	platformTools.h \
	timers.h \
	trgOps.h \
	tools.h \
	BERCoder.h \
	IsoConnectionForward.h reporter.h \
	fs/ConfigFiles.h fs/OscFiles.h \
	MemoryManager.h mms_gocb.h FinalDA.h \
	fs/OscConverter.h fs/OscDescr.h fs/OscInfo.h fs/OscReadFileContext.h fs/OscWriteBuffer.h \
	frsm.h goose.h netTools.h \
	BusError.h \
	stringView.h bufView.h bufViewBER.h bufViewMMS.h\
	file_system.h platform_socket.h platform_thread.h \
	tlsf/tlsf.h \
	timetools.h \
	fdzipstream/crc32.h  fdzipstream/fdzipstream.h \
	main.h BaseAsnTypes.h AssociationAsnTypes.h AsnEncoding.h Cotp.h session.h \
	presentation.h acse.h mms.h \
	mms_device.h mms_get_name_list.h mms_rcb.h \
	mms_get_variable_access_attributes.h mms_get_data_set_access_attr.h \
	iso_connection.h \
	platform/debug.h platform/platform_critical_section.h \
	mms_read.h mms_write.h mms_data.h mms_fs.h \
	iedmodel.h mmsConnection.h \
	mmsServices.h mms_error.h MmsConst.h DomainNameWriter.h pwin_access.h \
	DataSlice.h out_queue.h send_thread.h out_buffers.h rcb.h reports.h \
	ReportQueue.h connections.h server.h platform/platform_socket_def.h local_types.h \
	../../../IEDCompile/InnerAttributeTypes.h ../../../IEDCompile/AccessInfo.h

DEPO = \
	iedTree/iedTree.o \
	iedTree/iedEntity.o \
	iedTree/iedObjects.o \
	iedTree/iedFC.o \
	iedTree/iedControlModel.o \
	iedTree/iedFinalDA.o \
	iedTree/iedQuality.o \
	iedTree/iedBool.o \
	iedTree/iedFloat.o \
	iedTree/iedCodedEnum.o \
	iedTree/iedInt.o \
	iedTree/iedUInt.o \
	iedTree/iedEnum.o \
	iedTree/iedTimeStamp.o \
	iedTree/iedConstDA.o \
	iedTree/iedNoEntity.o \
	iedTree/DataSet.o \
	\
	reportItems/RptItem.o \
	reportItems/RptDataSet.o \
	reportItems/RptBool.o \
	\
	ObjectNameParser.o \
	infoReport.o \
	control.o \
	fast_memcpy.o \
	BERCoder.o \
	\
	lwip_socket.o \
	platformTools.o \
	$(TIMERS).o \
	main.o \
	frsm.o goose.o netTools.o \
	BusError.o \
	fs/ConfigFiles.o fs/OscFiles.o \
	MemoryManager.o mms_gocb.o FinalDA.o \
	stringView.o bufView.o bufViewBER.o bufViewMMS.o \
	file_system.o \
	platform_thread.o \
	fs/OscConverter.o fs/OscDescr.o fs/OscInfo.o fs/OscReadFileContext.o fs/OscWriteBuffer.o \
	tlsf/tlsf.o \
	timetools.o \
	fdzipstream/crc32.o  fdzipstream/fdzipstream.o \
	AsnEncoding.o Cotp.o session.o presentation.o acse.o mms.o mms_device.o \
	mms_get_variable_access_attributes.o mms_get_data_set_access_attr.o \
	mms_get_name_list.o mms_rcb.o \
	iso_connection.o debug.o platform_critical_section.o mms_read.o mms_write.o mms_data.o \
	mms_fs.o \
	iedmodel.o mmsServices.o mms_error.o DomainNameWriter.o pwin_access.o \
	$(DATA_SLICE).o out_queue.o send_thread.o out_buffers.o rcb.o reports.o \
	ReportQueue.o connections.o server.o \
	MMSServer.o



all: platform_check $(PNAME).ldm


USAGE_MSG = Usage: make PLATFORM=(IEDNEXUS ^| DRP150 ^| OTHER)

platform_check:
ifndef PLATFORM
	@echo Platform not specified
	@echo $(USAGE_MSG)
	@exit 1
endif
ifdef PLATFORM_ERROR
	@echo Invalid platform: "$(PLATFORM)"
	@echo $(USAGE_MSG)
	@exit 1
endif


$(PNAME).ldm: $(PNAME).out makefile platform_check
	@$(POSTLINK) -n -i $(PNAME).out -o $(PNAME).ldm --descr $(DESC)


build.cmd.tmp: $(BUILDMK) makefile
	@echo Making build.cmd.tmp	
	@$(MAKE) --no-print-directory -f $(BUILDMK)

$(PNAME).out : $(DEPO) makefile build.cmd.tmp platform_check
	$(LD) $(LFLAGS) $(DEPO) -o $(PNAME).out $(STACK)

debug.o: debug.c platform/debug.h makefile
	@echo Compiling debug.c
	@$(CC) $(CFLAGS) debug.c

platform_critical_section.o: platform_critical_section.c platform/platform_critical_section.h \
makefile
	@echo Compiling platform_critical_section.c
	@$(CC) $(CFLAGS) platform_critical_section.c

$(DEPO): $(DEPH) makefile

%.o : %.c
	@echo $<	
	@$(CC) -c $(CFLAGS) $< -o $@


CLEANFILES = \
	$(PNAME).ldm *.map *.out *.dbo *.dla *.dnm *.cmd.tmp *.o *.lst *.ti *.d

clean:
	-del $(CLEANFILES)
	-cd fs & del $(CLEANFILES)
	-cd iedTree & del $(CLEANFILES)
	-cd tlsf & del $(CLEANFILES)
	-cd fdzipstream & del $(CLEANFILES)
