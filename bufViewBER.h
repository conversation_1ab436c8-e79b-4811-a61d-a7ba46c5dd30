#pragma once

#include "bufView.h"
#include <stdbool.h>
#include <stdint.h>

//=================Функции декодирования BER==============

inline uint8_t BufferView_readTag(BufferView* bv)
{
    return bv->p[bv->pos++];
}

//! Возвращает в tag значение тэга в текущей позиции буфера.
//! При этом текущая позиция не изменяется.
//! Если буфер кончился, возвращает FALSE
bool BufferView_peekTag(BufferView* bv, uint8_t* tag);

//! Пропускает объект, если у объекта указанный тэг
//! Если strict = true, то при несовпадении тэга возвращается ошибка
bool BufferView_skipObject(BufferView* bv, uint8_t tag, bool strict);

//! Пропускает любой объект
bool BufferView_skipAnyObject(BufferView* bv);

//! Проверяет что текущая позиция указывает на нужный тэг.
//! Текущая позиция не сдвигается.
//! Возвращает FALSE если тэг не тот или достигнут конец буфера
inline bool BufferView_checkTag(BufferView* bv, uint8_t tag)
{
    return bv->pos < bv->len && bv->p[bv->pos] == tag;

}


bool BufferView_decodeTL(BufferView* bv, uint8_t* pTag, size_t* pLen,
    size_t* pFullLen);

//! Читает тэг и длину с поддержкой многобайтных тэгов.
//! Сейчас поддерживает максимум двухбайтные теги, но интерфейс
//! допускает расширение до четырёхбайтных.
bool BufferView_decodeExtTL(BufferView* bv, uint32_t* pTag,
    size_t* pLen, size_t* pFullLen);

bool BufferView_decodeInt32(BufferView* bv, size_t intLen, int32_t* result);

bool BufferView_decodeUInt32(BufferView* bv, size_t intLen, uint32_t* result);

bool BufferView_decodeInt32TL(BufferView* bv, uint8_t expectedTag,
    int32_t* result);
bool BufferView_decodeUInt32TL(BufferView* bv, uint8_t expectedTag,
    uint32_t* result);

//! Возвращает StringView, который содержит данные BER-объекта
//! BER-объект может быть не только строкой, но и вообще любым.
bool BufferView_decodeStringViewTL(BufferView* bv, uint8_t expectedTag,
    StringView* result);

//=================Функции кодирования BER==============

//! Записывает тэг(1 байт) в буфер. Если не влезло, возвращает FALSE
bool BufferView_writeTag(BufferView* bv, uint8_t tag);

//! BER - Кодирует StringView в буфер. Возвращает FALSE если не помещается.
bool BufferView_encodeStringView(BufferView* bv, uint8_t tag,
    const StringView* strView);

//! BER - Кодирует строку в буфер. Возвращает FALSE если не помещается.
bool BufferView_encodeStr(BufferView* bv, uint8_t tag, const char* str);

//! BER - Кодирует тэг и длину в буфер. Возвращает FALSE если не помещается.
bool BufferView_encodeTL(BufferView* bv, uint8_t tag, size_t length);

//! BER - Кодирует двухбайтный тэг и длину в буфер.
//! Возвращает FALSE если не помещается.
//! тэг пишется big endian, т.е  0х1234 записывается как 0x12, 0x32
bool BufferView_encodeExtTL(BufferView* bv, uint16_t extTag, size_t length);

//! BER - Кодирует int32_t в буфер. Возвращает false если не помещается.
bool BufferView_encodeInt32(BufferView* bv, uint8_t tag, int32_t value);

//! BER - Кодирует uint32_t в буфер. Возвращает FALSE если не помещается.
bool BufferView_encodeUInt32(BufferView* bv, uint8_t tag, uint32_t value);

bool BufferView_encodeBoolean(BufferView* bv, uint8_t tag, bool val);

//! BER - Кодирует octet_string в буфер. Возвращает FALSE если не помещается.
bool BufferView_encodeOctetString(BufferView* bv, uint8_t tag, void* data,
    size_t dataLen);


//! BER - Кодирует данные из буфера в буфер.
//! Данные из data берутся от начала буфера до текущей позиции
//! Возвращает FALSE если не помещается.
bool BufferView_encodeBufferView(BufferView* bv, uint8_t tag, BufferView* data);

//! Функция копирует len байт из src в буфер в обратном порядке.
//! Предназначена для кодирования int разной разрядности в BER.
//! Пример кодирования:
//! size_t len = BERCoder_calcIntEncodedLen(&value, sizeof(value));
//! BufferView_reverseWrite(buffer, &value, len);
bool BufferView_reverseWrite(BufferView* bv, const void* src, size_t len);
