                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_90k1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=DataSet.c -o iedTree\gh_90k1.o -list=iedTree/DataSet.lst C:\Users\<USER>\AppData\Local\Temp\gh_90k1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_90k1.s
Source File: DataSet.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      10 ;		iedTree/DataSet.c -o iedTree/DataSet.o

                      11 ;Source File:   iedTree/DataSet.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:55:13 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "DataSet.h"


                      21 ;2: 


                      22 ;3: #include "../stringView.h"


                      23 ;4: #include "iedEntity.h"


                      24 ;5: #include "iedTree.h"


                      25 ;6: #include "../bufViewBER.h"


                      26 ;7: #include "../AsnEncoding.h"


                      27 ;8: 


                      28 ;9: #include <debug.h>


                      29 ;10: 


                      30 ;11: 


                      31 ;12: bool DataSet_init(IEDEntity entity)


                      32 	.text

                      33 	.align	4

                      34 DataSet_init::

00000000 e92d4070     35 	stmfd	[sp]!,{r4-r6,lr}

                      36 ;13: {


                      37 

                      38 ;14:     uint8_t tag;


                      39 ;15:     BufferView dataSetBER;


                      40 ;16:     DataSet* dataSet;


                      41 ;17:     DataSetItem** pNextItem;


                      42 ;18:     entity->type = IED_ENTITY_DATASET;


                      43 

00000004 e24dd020     44 	sub	sp,sp,32

00000008 e1a04000     45 	mov	r4,r0

0000000c e3a0000b     46 	mov	r0,11

00000010 e5840050     47 	str	r0,[r4,80]

                      48 ;19:     dataSet = IEDEntity_alloc(sizeof(DataSet));


                      49 

00000014 e3a00008     50 	mov	r0,8


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_90k1.s
00000018 eb000000*    51 	bl	IEDEntity_alloc

0000001c e1b05000     52 	movs	r5,r0

                      53 ;20:     if(dataSet == NULL)


                      54 

00000020 0a000017     55 	beq	.L33

                      56 ;21:     {


                      57 

                      58 ;22:         return false;


                      59 

                      60 ;23:     }


                      61 ;24:     entity->extInfo = dataSet;


                      62 

00000024 e5845058     63 	str	r5,[r4,88]

                      64 ;25: 


                      65 ;26:     dataSetBER = entity->ber;


                      66 

00000028 e28d3014     67 	add	r3,sp,20

0000002c e2840014     68 	add	r0,r4,20

00000030 e8900007     69 	ldmfd	[r0],{r0-r2}

00000034 e8830007     70 	stmea	[r3],{r0-r2}

                      71 ;27: 


                      72 ;28:     //Пропускаем тэг и длину


                      73 ;29:     if(!BufferView_decodeTL(&dataSetBER, NULL, NULL, NULL))


                      74 

00000038 e1a00003     75 	mov	r0,r3

0000003c e3a03000     76 	mov	r3,0

00000040 e1a02003     77 	mov	r2,r3

00000044 e1a01003     78 	mov	r1,r3

00000048 eb000000*    79 	bl	BufferView_decodeTL

0000004c e3500000     80 	cmp	r0,0

00000050 0a00000b     81 	beq	.L33

                      82 ;30:     {


                      83 

                      84 ;31:         ERROR_REPORT("DataSet init error");


                      85 ;32:         return false;


                      86 

                      87 ;33:     }


                      88 ;34: 


                      89 ;35:     //Пропускаем имя


                      90 ;36:     if(!BufferView_skipObject(&dataSetBER, ASN_VISIBLE_STRING, true))


                      91 

00000054 e28d0014     92 	add	r0,sp,20

00000058 e3a02001     93 	mov	r2,1

0000005c e3a0101a     94 	mov	r1,26

00000060 eb000000*    95 	bl	BufferView_skipObject

00000064 e3500000     96 	cmp	r0,0

00000068 0a000005     97 	beq	.L33

                      98 ;37:     {


                      99 

                     100 ;38:         ERROR_REPORT("DataSet init error");


                     101 ;39:         return false;


                     102 

                     103 ;40:     }


                     104 ;41: 


                     105 ;42:     //Пропускаем описание


                     106 ;43:     if(!BufferView_skipObject(&dataSetBER, ASN_OCTET_STRING, true))


                     107 

0000006c e28d0014    108 	add	r0,sp,20

00000070 e3a02001    109 	mov	r2,1

00000074 e3a01004    110 	mov	r1,4

00000078 eb000000*   111 	bl	BufferView_skipObject


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_90k1.s
0000007c e3500000    112 	cmp	r0,0

00000080 1a000001    113 	bne	.L32

                     114 .L33:

                     115 ;44:     {


                     116 

                     117 ;45:         ERROR_REPORT("DataSet init error");


                     118 ;46:         return false;


                     119 

00000084 e3a00000    120 	mov	r0,0

00000088 ea000037    121 	b	.L21

                     122 .L32:

                     123 ;47:     }


                     124 ;48: 


                     125 ;49:     pNextItem = &dataSet->firstItem;


                     126 

0000008c e1a06005    127 	mov	r6,r5

                     128 ;50: 


                     129 ;51:     //Получаем список элементов


                     130 ;52:     while(!BufferView_endOfBuf(&dataSetBER))


                     131 

00000090 e59d201c    132 	ldr	r2,[sp,28]

00000094 e59d1018    133 	ldr	r1,[sp,24]

00000098 e28d4014    134 	add	r4,sp,20

0000009c e1510002    135 	cmp	r1,r2

000000a0 0a000030    136 	beq	.L36

                     137 .L37:

                     138 ;53:     {


                     139 

                     140 ;54:         StringView ldName;


                     141 ;55:         StringView objName;        


                     142 ;56:         DataSetItem* dataSetItem;


                     143 ;57: 


                     144 ;58:         //Читаем TL


                     145 ;59:         if(!BufferView_decodeTL(&dataSetBER, &tag, NULL, NULL)


                     146 

000000a4 e28d1003    147 	add	r1,sp,3

000000a8 e1a00004    148 	mov	r0,r4

000000ac e3a03000    149 	mov	r3,0

000000b0 e1a02003    150 	mov	r2,r3

000000b4 eb000000*   151 	bl	BufferView_decodeTL

000000b8 e3500000    152 	cmp	r0,0

000000bc 0afffff0    153 	beq	.L33

000000c0 e5dd1003    154 	ldrb	r1,[sp,3]

000000c4 e3510030    155 	cmp	r1,48

000000c8 1affffed    156 	bne	.L33

                     157 ;60:                 || tag != ASN_SEQUENCE)


                     158 ;61:         {


                     159 

                     160 ;62:             return false;


                     161 

                     162 ;63:         }


                     163 ;64:         //Читаем имя переменной


                     164 ;65:         if(!BufferView_decodeStringViewTL(&dataSetBER, ASN_VISIBLE_STRING,


                     165 

000000cc e28d200c    166 	add	r2,sp,12

000000d0 e1a00004    167 	mov	r0,r4

000000d4 e3a0101a    168 	mov	r1,26

000000d8 eb000000*   169 	bl	BufferView_decodeStringViewTL

000000dc e3500000    170 	cmp	r0,0

000000e0 0affffe7    171 	beq	.L33

000000e4 e28d2004    172 	add	r2,sp,4


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_90k1.s
000000e8 e1a00004    173 	mov	r0,r4

000000ec e3a0101a    174 	mov	r1,26

000000f0 eb000000*   175 	bl	BufferView_decodeStringViewTL

000000f4 e3500000    176 	cmp	r0,0

000000f8 0affffe1    177 	beq	.L33

                     178 ;66:                                           &ldName)


                     179 ;67:              || !BufferView_decodeStringViewTL(&dataSetBER, ASN_VISIBLE_STRING,


                     180 ;68:                                                &objName))


                     181 ;69:         {


                     182 

                     183 ;70:             return false;


                     184 

                     185 ;71:         }


                     186 ;72:         //Пропускаем позицию


                     187 ;73:         if(!BufferView_skipObject(&dataSetBER, ASN_INTEGER, true))


                     188 

000000fc e1a00004    189 	mov	r0,r4

00000100 e3a02001    190 	mov	r2,1

00000104 e3a01002    191 	mov	r1,2

00000108 eb000000*   192 	bl	BufferView_skipObject

0000010c e3500000    193 	cmp	r0,0

00000110 0affffdb    194 	beq	.L33

                     195 ;74:         {


                     196 

                     197 ;75:             return false;


                     198 

                     199 ;76:         }


                     200 ;77: 


                     201 ;78:         dataSetItem = IEDEntity_alloc(sizeof(DataSetItem));


                     202 

00000114 e3a00018    203 	mov	r0,24

00000118 eb000000*   204 	bl	IEDEntity_alloc

                     205 ;79:         if(dataSetItem == NULL)


                     206 

0000011c e3500000    207 	cmp	r0,0

00000120 0affffd7    208 	beq	.L33

                     209 ;80:         {


                     210 

                     211 ;81:             ERROR_REPORT("DataSet item alloc error");


                     212 ;82:             return false;


                     213 

                     214 ;83:         }


                     215 ;84: 


                     216 ;85:         dataSetItem->domainID = ldName;


                     217 

00000124 e59d300c    218 	ldr	r3,[sp,12]

00000128 e5803004    219 	str	r3,[r0,4]

0000012c e59d1010    220 	ldr	r1,[sp,16]

00000130 e5801008    221 	str	r1,[r0,8]

                     222 ;86:         dataSetItem->itemID = objName;        		


                     223 

00000134 e59d3004    224 	ldr	r3,[sp,4]

00000138 e580300c    225 	str	r3,[r0,12]

0000013c e59d1008    226 	ldr	r1,[sp,8]

00000140 e5801010    227 	str	r1,[r0,16]

                     228 ;87: 


                     229 ;88:         *pNextItem = dataSetItem;


                     230 

00000144 e5951004    231 	ldr	r1,[r5,4]

00000148 e5860000    232 	str	r0,[r6]

                     233 ;89:         pNextItem = &dataSetItem->next;



                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_90k1.s
                     234 

0000014c e2811001    235 	add	r1,r1,1

00000150 e5851004    236 	str	r1,[r5,4]

00000154 e59d201c    237 	ldr	r2,[sp,28]

00000158 e59d1018    238 	ldr	r1,[sp,24]

0000015c e1a06000    239 	mov	r6,r0

                     240 ;90: 


                     241 ;91: 		dataSet->itemCount++;


                     242 

00000160 e1510002    243 	cmp	r1,r2

00000164 1affffce    244 	bne	.L37

                     245 .L36:

                     246 ;92:     }


                     247 ;93:     return true;


                     248 

00000168 e3a00001    249 	mov	r0,1

                     250 .L21:

0000016c e28dd020    251 	add	sp,sp,32

00000170 e8bd8070    252 	ldmfd	[sp]!,{r4-r6,pc}

                     253 	.endf	DataSet_init

                     254 	.align	4

                     255 ;tag	[sp,3]	local

                     256 ;dataSetBER	[sp,20]	local

                     257 ;dataSet	r5	local

                     258 ;pNextItem	r6	local

                     259 ;ldName	[sp,12]	local

                     260 ;objName	[sp,4]	local

                     261 ;dataSetItem	r0	local

                     262 

                     263 ;entity	r4	param

                     264 

                     265 	.section ".bss","awb"

                     266 .L202:

                     267 	.data

                     268 	.text

                     269 

                     270 ;94: }


                     271 

                     272 ;95: 


                     273 ;96: bool DataSet_postCreate(IEDEntity entity)


                     274 	.align	4

                     275 	.align	4

                     276 DataSet_postCreate::

00000174 e92d4010    277 	stmfd	[sp]!,{r4,lr}

                     278 ;97: {


                     279 

                     280 ;98:     DataSetItem* dataSetItem;


                     281 ;99:     DataSet* dataSet = entity->extInfo;


                     282 

00000178 e5900058    283 	ldr	r0,[r0,88]

                     284 ;100: 


                     285 ;101:     if(dataSet == NULL)


                     286 

0000017c e3500000    287 	cmp	r0,0

                     288 .L239:

                     289 ;102:     {


                     290 

                     291 ;103:         return false;


                     292 

00000180 03a00000    293 	moveq	r0,0

00000184 0a00000c    294 	beq	.L236


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_90k1.s
                     295 .L238:

                     296 ;104:     }


                     297 ;105: 


                     298 ;106:     dataSetItem = dataSet->firstItem;


                     299 

00000188 e5904000    300 	ldr	r4,[r0]

                     301 ;107: 


                     302 ;108:     while(dataSetItem != NULL)


                     303 

0000018c e3540000    304 	cmp	r4,0

00000190 0a000008    305 	beq	.L242

                     306 .L243:

                     307 ;109:     {


                     308 

                     309 ;110:         IEDEntity obj;


                     310 ;111:         obj = IEDTree_findDataByFullName(


                     311 

00000194 e284100c    312 	add	r1,r4,12

00000198 e2840004    313 	add	r0,r4,4

0000019c eb000000*   314 	bl	IEDTree_findDataByFullName

                     315 ;112:                     &dataSetItem->domainID, &dataSetItem->itemID);


                     316 ;113:         if(obj == NULL)


                     317 

000001a0 e3500000    318 	cmp	r0,0

000001a4 0afffff5    319 	beq	.L239

                     320 ;114:         {


                     321 

                     322 ;115:             ERROR_REPORT("DataSet item is not found");


                     323 ;116:             return false;


                     324 

                     325 ;117:         }


                     326 ;118:         dataSetItem->obj = obj;


                     327 

000001a8 e5840014    328 	str	r0,[r4,20]

                     329 ;119: 


                     330 ;120:         dataSetItem = dataSetItem->next;


                     331 

000001ac e5944000    332 	ldr	r4,[r4]

000001b0 e3540000    333 	cmp	r4,0

000001b4 1afffff6    334 	bne	.L243

                     335 .L242:

                     336 ;121:     }


                     337 ;122:     return true;


                     338 

000001b8 e3a00001    339 	mov	r0,1

                     340 .L236:

000001bc e8bd8010    341 	ldmfd	[sp]!,{r4,pc}

                     342 	.endf	DataSet_postCreate

                     343 	.align	4

                     344 ;dataSetItem	r4	local

                     345 ;dataSet	r0	local

                     346 ;obj	r0	local

                     347 

                     348 ;entity	r0	param

                     349 

                     350 	.section ".bss","awb"

                     351 .L318:

                     352 	.data

                     353 	.text

                     354 

                     355 ;123: }



                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_90k1.s
                     356 

                     357 ;124: 


                     358 ;125: 


                     359 ;126: DataSet *DataSet_getDataSetObj(IEDEntity entity)


                     360 	.align	4

                     361 	.align	4

                     362 DataSet_getDataSetObj::

                     363 ;127: {


                     364 

                     365 ;128: 	if(entity->type != IED_ENTITY_DATASET)


                     366 

000001c0 e5901050    367 	ldr	r1,[r0,80]

000001c4 e351000b    368 	cmp	r1,11

                     369 ;129: 	{


                     370 

                     371 ;130: 		ERROR_REPORT("Not DataSet");


                     372 ;131: 		return NULL;


                     373 

000001c8 13a00000    374 	movne	r0,0

                     375 ;132: 	}


                     376 ;133: 	return entity->extInfo;


                     377 

000001cc 05900058    378 	ldreq	r0,[r0,88]

000001d0 e12fff1e*   379 	ret	

                     380 	.endf	DataSet_getDataSetObj

                     381 	.align	4

                     382 

                     383 ;entity	r0	param

                     384 

                     385 	.section ".bss","awb"

                     386 .L374:

                     387 	.data

                     388 	.text

                     389 

                     390 ;134: }


                     391 

                     392 ;135: 


                     393 ;136: 


                     394 ;137: DataSetItem *DataSet_getFirstItem(IEDEntity entity)


                     395 	.align	4

                     396 	.align	4

                     397 DataSet_getFirstItem::

                     398 ;138: {


                     399 

                     400 ;139: 	DataSet* dataSet;


                     401 ;140: 


                     402 ;141: 	if(entity->type != IED_ENTITY_DATASET)


                     403 

000001d4 e5901050    404 	ldr	r1,[r0,80]

000001d8 e351000b    405 	cmp	r1,11

                     406 ;142: 	{


                     407 

                     408 ;143: 		ERROR_REPORT("Not DataSet");


                     409 ;144: 		return NULL;


                     410 

000001dc 13a00000    411 	movne	r0,0

                     412 ;145: 	}


                     413 ;146: 


                     414 ;147: 	dataSet =  entity->extInfo;


                     415 

000001e0 05900058    416 	ldreq	r0,[r0,88]


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_90k1.s
                     417 ;148: 


                     418 ;149: 	return dataSet->firstItem;


                     419 

000001e4 05900000    420 	ldreq	r0,[r0]

000001e8 e12fff1e*   421 	ret	

                     422 	.endf	DataSet_getFirstItem

                     423 	.align	4

                     424 ;dataSet	r0	local

                     425 

                     426 ;entity	r0	param

                     427 

                     428 	.section ".bss","awb"

                     429 .L422:

                     430 	.data

                     431 	.text

                     432 

                     433 ;150: }


                     434 

                     435 ;151: 


                     436 ;152: 


                     437 ;153: bool DataSet_calcReadLen(IEDEntity dsEntity, size_t *pLen)


                     438 	.align	4

                     439 	.align	4

                     440 DataSet_calcReadLen::

000001ec e92d4070    441 	stmfd	[sp]!,{r4-r6,lr}

000001f0 e24dd004    442 	sub	sp,sp,4

000001f4 e1a05001    443 	mov	r5,r1

                     444 ;154: {


                     445 

                     446 ;155: 	DataSet* dataSet;


                     447 ;156: 	DataSetItem* dataSetItem;


                     448 ;157: 


                     449 ;158: 	if(dsEntity->type != IED_ENTITY_DATASET)


                     450 

000001f8 e5901050    451 	ldr	r1,[r0,80]

000001fc e351000b    452 	cmp	r1,11

00000200 0a000001    453 	beq	.L437

                     454 .L438:

                     455 ;159: 	{


                     456 

                     457 ;160: 		ERROR_REPORT("Not DataSet");


                     458 ;161: 		return false;


                     459 

00000204 e3a00000    460 	mov	r0,0

00000208 ea000015    461 	b	.L435

                     462 .L437:

                     463 ;162: 	}


                     464 ;163: 


                     465 ;164: 	dataSet =  dsEntity->extInfo;


                     466 

0000020c e5900058    467 	ldr	r0,[r0,88]

                     468 ;165: 	dataSetItem = dataSet->firstItem;


                     469 

00000210 e1a0600d    470 	mov	r6,sp

00000214 e5904000    471 	ldr	r4,[r0]

                     472 ;166: 


                     473 ;167: 	*pLen = 0;


                     474 

00000218 e3a00000    475 	mov	r0,0

0000021c e5850000    476 	str	r0,[r5]

                     477 ;168: 	while(dataSetItem != NULL)



                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_90k1.s
                     478 

00000220 e3540000    479 	cmp	r4,0

00000224 0a00000d    480 	beq	.L441

                     481 .L442:

                     482 ;169: 	{


                     483 

                     484 ;170: 		size_t entityReadLen;


                     485 ;171: 		IEDEntity entity = dataSetItem->obj;


                     486 

00000228 e5940014    487 	ldr	r0,[r4,20]

                     488 ;172: 


                     489 ;173: 		if(!entity->calcReadLen(entity, &entityReadLen))


                     490 

0000022c e590c060    491 	ldr	r12,[r0,96]

00000230 e1a01006    492 	mov	r1,r6

00000234 e1a0e00f    493 	mov	lr,pc

00000238 e12fff1c*   494 	bx	r12

0000023c e3500000    495 	cmp	r0,0

00000240 0affffef    496 	beq	.L438

                     497 ;174: 		{


                     498 

                     499 ;175: 			ERROR_REPORT("Unable to calc DataSet itrm len");


                     500 ;176: 			return false;


                     501 

                     502 ;177: 		}


                     503 ;178: 


                     504 ;179: 		*pLen += entityReadLen;


                     505 

00000244 e59d1000    506 	ldr	r1,[sp]

00000248 e5950000    507 	ldr	r0,[r5]

0000024c e5944000    508 	ldr	r4,[r4]

00000250 e0800001    509 	add	r0,r0,r1

00000254 e5850000    510 	str	r0,[r5]

                     511 ;180: 		dataSetItem = dataSetItem->next;


                     512 

00000258 e3540000    513 	cmp	r4,0

0000025c 1afffff1    514 	bne	.L442

                     515 .L441:

                     516 ;181: 	}


                     517 ;182: 


                     518 ;183: 	return true;


                     519 

00000260 e3a00001    520 	mov	r0,1

                     521 .L435:

00000264 e28dd004    522 	add	sp,sp,4

00000268 e8bd8070    523 	ldmfd	[sp]!,{r4-r6,pc}

                     524 	.endf	DataSet_calcReadLen

                     525 	.align	4

                     526 ;dataSet	r0	local

                     527 ;dataSetItem	r4	local

                     528 ;entityReadLen	[sp]	local

                     529 

                     530 ;dsEntity	r0	param

                     531 ;pLen	r5	param

                     532 

                     533 	.section ".bss","awb"

                     534 .L510:

                     535 	.data

                     536 	.text

                     537 

                     538 ;184: }



                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_90k1.s
                     539 

                     540 ;185: 


                     541 ;186: bool DataSet_encodeRead(IEDEntity dsEntity, BufferView *outBuf)


                     542 	.align	4

                     543 	.align	4

                     544 DataSet_encodeRead::

0000026c e92d4030    545 	stmfd	[sp]!,{r4-r5,lr}

00000270 e1a04001    546 	mov	r4,r1

                     547 ;187: {


                     548 

                     549 ;188: 	DataSet* dataSet;


                     550 ;189: 	DataSetItem* dataSetItem;


                     551 ;190: 


                     552 ;191: 	if(dsEntity->type != IED_ENTITY_DATASET)


                     553 

00000274 e5901050    554 	ldr	r1,[r0,80]

00000278 e351000b    555 	cmp	r1,11

0000027c 0a000001    556 	beq	.L530

                     557 .L531:

                     558 ;192: 	{


                     559 

                     560 ;193: 		ERROR_REPORT("Not DataSet");


                     561 ;194: 		return false;


                     562 

00000280 e3a00000    563 	mov	r0,0

00000284 ea00000e    564 	b	.L528

                     565 .L530:

                     566 ;195: 	}


                     567 ;196: 


                     568 ;197: 	dataSet =  dsEntity->extInfo;


                     569 

00000288 e5900058    570 	ldr	r0,[r0,88]

                     571 ;198: 	dataSetItem = dataSet->firstItem;


                     572 

0000028c e5905000    573 	ldr	r5,[r0]

                     574 ;199: 


                     575 ;200: 	while(dataSetItem != NULL)


                     576 

00000290 e3550000    577 	cmp	r5,0

00000294 0a000009    578 	beq	.L534

                     579 .L535:

                     580 ;201: 	{


                     581 

                     582 ;202: 		IEDEntity entity = dataSetItem->obj;


                     583 

00000298 e5950014    584 	ldr	r0,[r5,20]

                     585 ;203: 		if(!entity->encodeRead(entity,outBuf))


                     586 

0000029c e590c05c    587 	ldr	r12,[r0,92]

000002a0 e1a01004    588 	mov	r1,r4

000002a4 e1a0e00f    589 	mov	lr,pc

000002a8 e12fff1c*   590 	bx	r12

000002ac e3500000    591 	cmp	r0,0

000002b0 0afffff2    592 	beq	.L531

                     593 ;204: 		{


                     594 

                     595 ;205: 			ERROR_REPORT("Unable to read DataSet item");


                     596 ;206: 			return false;


                     597 

                     598 ;207: 		}


                     599 ;208: 		dataSetItem = dataSetItem->next;



                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_90k1.s
                     600 

000002b4 e5955000    601 	ldr	r5,[r5]

000002b8 e3550000    602 	cmp	r5,0

000002bc 1afffff5    603 	bne	.L535

                     604 .L534:

                     605 ;209: 	}


                     606 ;210: 	return true;


                     607 

000002c0 e3a00001    608 	mov	r0,1

                     609 .L528:

000002c4 e8bd8030    610 	ldmfd	[sp]!,{r4-r5,pc}

                     611 	.endf	DataSet_encodeRead

                     612 	.align	4

                     613 ;dataSet	r0	local

                     614 ;dataSetItem	r5	local

                     615 

                     616 ;dsEntity	r0	param

                     617 ;outBuf	r4	param

                     618 

                     619 	.section ".bss","awb"

                     620 .L606:

                     621 	.data

                     622 	.text

                     623 

                     624 ;211: }


                     625 	.align	4

                     626 

                     627 	.data

                     628 	.ghsnote version,6

                     629 	.ghsnote tools,3

                     630 	.ghsnote options,0

                     631 	.text

                     632 	.align	4

