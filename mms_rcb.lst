                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2pk1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=mms_rcb.c -o gh_2pk1.o -list=mms_rcb.lst C:\Users\<USER>\AppData\Local\Temp\gh_2pk1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_2pk1.s
Source File: mms_rcb.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile mms_rcb.c -o

                      10 ;		mms_rcb.o

                      11 ;Source File:   mms_rcb.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Oct 07 09:39:35 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "mms_rcb.h"


                      21 ;2: #include "iedmodel.h"


                      22 ;3: #include "mms_data.h"


                      23 ;4: #include "rcb.h"


                      24 ;5: #include "reports.h"


                      25 ;6: #include "IEDCompile/AccessInfo.h"


                      26 ;7: #include "IEDCompile/InnerAttributeTypes.h"


                      27 ;8: 


                      28 ;9: #include "debug.h"


                      29 ;10: #include "AsnEncoding.h"


                      30 ;11: 


                      31 ;12: #include <stddef.h>


                      32 ;13: #include <stdint.h>


                      33 ;14: 


                      34 ;15: int encodeAccessAttrRCB(uint8_t* outBuf, int bufPos, int accessDataPos, bool determineSize)


                      35 ;16: {


                      36 ;17:     CBAttrAccessInfo* pAccessInfo =


                      37 ;18:             (CBAttrAccessInfo*)getAlignedDescrStruct(accessDataPos);


                      38 ;19:     if(pAccessInfo == NULL)


                      39 ;20:     {


                      40 ;21:         ERROR_REPORT("Unable to get access info struct");


                      41 ;22:         return 0;


                      42 ;23:     }


                      43 ;24: 


                      44 ;25:     switch(pAccessInfo->attrCode)


                      45 ;26:     {


                      46 ;27:         case RptEna:


                      47 ;28: 		case GI:


                      48 ;29:         case Resv:


                      49 ;30:             return encodeAccessAttrBoolean( outBuf, bufPos, determineSize);


                      50 ;31: 		case SqNum:



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2pk1.s
                      51 ;32: 			return encodeAccessAttrUInt(outBuf, bufPos, 16, determineSize);


                      52 ;33: 		case IntgPd:


                      53 ;34: 			return encodeAccessAttrUInt(outBuf, bufPos, 32, determineSize);


                      54 ;35: 		case EntryID:


                      55 ;36: 			return encodeAccessAttrString(outBuf, bufPos,


                      56 ;37: 				IEC61850_BER_OCTET_STRING, 8, determineSize);


                      57 ;38: 		case ConfRev:


                      58 ;39: 			return encodeAccessAttrUInt(outBuf, bufPos, 32, determineSize);


                      59 ;40:         default :


                      60 ;41:             ERROR_REPORT("Invalid RCB DA code");


                      61 ;42:             return 0;


                      62 ;43:     }


                      63 ;44: }


                      64 ;45: 


                      65 ;46: /*


                      66 ;47: static int determineAttrRCBReadSize(CBAttrAccessInfo* pAccessInfo)


                      67 ;48: {


                      68 ;49:     switch(pAccessInfo->attrCode){


                      69 ;50:         case RptEna:


                      70 ;51: 		case GI:


                      71 ;52:             return 3; // boolean


                      72 ;53: 		case SqNum:


                      73 ;54: 			!!


                      74 ;55: 			return encodeUInt32Value(uint8_t* outBuf, int bufPos, uint32_t value,


                      75 ;56: 				TRUE)


                      76 ;57: 		case EntryID:


                      77 ;58: 			!!!


                      78 ;59:         default:


                      79 ;60:             ERROR_REPORT("Invalid RCB DA code");


                      80 ;61:             return 0;


                      81 ;62:     }


                      82 ;63: }


                      83 ;64: */


                      84 ;65: 


                      85 ;66: static int encodeReadRptEna(uint8_t* outBuf, int bufPos, 


                      86 

                      87 ;80: }


                      88 

                      89 ;81: 


                      90 ;82: static int encodeReadResv(uint8_t* outBuf, int bufPos,


                      91 

                      92 ;96: }


                      93 

                      94 ;97: 


                      95 ;98: static int encodeReadGI(uint8_t* outBuf, int bufPos,


                      96 

                      97 ;112: }


                      98 

                      99 ;113: 


                     100 ;114: static int encodeReadSqNum(uint8_t* outBuf, int bufPos,


                     101 

                     102 ;128: }


                     103 

                     104 ;129: 


                     105 ;130: static int encodeReadIntgPd(uint8_t* outBuf, int bufPos,


                     106 

                     107 ;144: }


                     108 

                     109 ;145: 


                     110 ;146: static int encodeReadConfRev(uint8_t* outBuf, int bufPos,


                     111 


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2pk1.s
                     112 ;160: }


                     113 

                     114 ;161: 


                     115 ;162: static int encodeReadEntryID(uint8_t* outBuf, int bufPos,


                     116 

                     117 ;176: }


                     118 

                     119 ;177: 


                     120 ;178: int encodeReadAttrRCB(uint8_t* outBuf, int bufPos, void* descrStruct, bool determineSize)


                     121 ;179: {


                     122 ;180:     CBAttrAccessInfo* pAccessInfo =  descrStruct;


                     123 ;181:     


                     124 ;182:     switch (pAccessInfo->attrCode) {


                     125 ;183:     case RptEna:


                     126 ;184:         return encodeReadRptEna(outBuf, bufPos, pAccessInfo, determineSize);


                     127 ;185:     case Resv:


                     128 ;186:         return encodeReadResv(outBuf, bufPos, pAccessInfo, determineSize);


                     129 ;187: 	case GI:


                     130 ;188: 		return encodeReadGI(outBuf, bufPos, pAccessInfo, determineSize);


                     131 ;189: 	case SqNum:


                     132 ;190: 		return encodeReadSqNum(outBuf, bufPos, pAccessInfo, determineSize);


                     133 ;191: 	case IntgPd:


                     134 ;192: 		return encodeReadIntgPd(outBuf, bufPos, pAccessInfo, determineSize);


                     135 ;193: 	case EntryID:


                     136 ;194: 		return encodeReadEntryID(outBuf, bufPos, pAccessInfo, determineSize);


                     137 ;195: 	case ConfRev:


                     138 ;196: 		return encodeReadConfRev(outBuf, bufPos, pAccessInfo, determineSize);


                     139 ;197:     default:


                     140 ;198:         ERROR_REPORT("Invalid RCB DA code");


                     141 ;199:         return 0;


                     142 ;200:     }


                     143 ;201: }


                     144 ;202: 


                     145 ;203: static void writeRptEna(IsoConnection* isoConn, int rcbIndex, uint8_t* dataToWrite)


                     146 

                     147 ;221: }


                     148 

                     149 ;222: 


                     150 ;223: static void writeResv(IsoConnection* isoConn, int rcbIndex, uint8_t* dataToWrite)


                     151 

                     152 ;241: }


                     153 

                     154 	.text

                     155 	.align	4

                     156 encodeAccessAttrRCB::

00000000 e92d4070    157 	stmfd	[sp]!,{r4-r6,lr}

00000004 e24dd004    158 	sub	sp,sp,4

00000008 e1a05001    159 	mov	r5,r1

0000000c e1a06003    160 	mov	r6,r3

00000010 e1a04000    161 	mov	r4,r0

00000014 e1a00002    162 	mov	r0,r2

00000018 eb000000*   163 	bl	getAlignedDescrStruct

0000001c e3500000    164 	cmp	r0,0

00000020 0a000021    165 	beq	.L185

00000024 e5900004    166 	ldr	r0,[r0,4]

00000028 e2500001    167 	subs	r0,r0,1

0000002c e2500002    168 	subs	r0,r0,2

00000030 3a000005    169 	blo	.L175

00000034 0a000009    170 	beq	.L177

00000038 e2500001    171 	subs	r0,r0,1

0000003c 0a00000d    172 	beq	.L181


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2pk1.s
00000040 e3500003    173 	cmp	r0,3

00000044 3a000012    174 	blo	.L183

00000048 1a000017    175 	bne	.L185

                     176 .L175:

0000004c e1a02006    177 	mov	r2,r6

00000050 e1a01005    178 	mov	r1,r5

00000054 e1a00004    179 	mov	r0,r4

00000058 eb000000*   180 	bl	encodeAccessAttrBoolean

0000005c ea000013    181 	b	.L168

                     182 .L177:

00000060 e1a03006    183 	mov	r3,r6

00000064 e1a01005    184 	mov	r1,r5

00000068 e1a00004    185 	mov	r0,r4

0000006c e3a02010    186 	mov	r2,16

00000070 eb000000*   187 	bl	encodeAccessAttrUInt

00000074 ea00000d    188 	b	.L168

                     189 .L181:

00000078 e58d6000    190 	str	r6,[sp]

0000007c e1a01005    191 	mov	r1,r5

00000080 e1a00004    192 	mov	r0,r4

00000084 e3a03008    193 	mov	r3,8

00000088 e3a02089    194 	mov	r2,137

0000008c eb000000*   195 	bl	encodeAccessAttrString

00000090 ea000006    196 	b	.L168

                     197 .L183:

00000094 e1a03006    198 	mov	r3,r6

00000098 e1a01005    199 	mov	r1,r5

0000009c e1a00004    200 	mov	r0,r4

000000a0 e3a02020    201 	mov	r2,32

000000a4 eb000000*   202 	bl	encodeAccessAttrUInt

000000a8 ea000000    203 	b	.L168

                     204 .L185:

000000ac e3a00000    205 	mov	r0,0

                     206 .L168:

000000b0 e28dd004    207 	add	sp,sp,4

000000b4 e8bd8070    208 	ldmfd	[sp]!,{r4-r6,pc}

                     209 	.endf	encodeAccessAttrRCB

                     210 	.align	4

                     211 ;pAccessInfo	r0	local

                     212 

                     213 ;outBuf	r4	param

                     214 ;bufPos	r5	param

                     215 ;accessDataPos	r2	param

                     216 ;determineSize	r6	param

                     217 

                     218 	.section ".bss","awb"

                     219 .L256:

                     220 	.data

                     221 	.text

                     222 

                     223 

                     224 	.align	4

                     225 	.align	4

                     226 encodeReadAttrRCB::

000000b8 e92d40f0    227 	stmfd	[sp]!,{r4-r7,lr}

000000bc e24dd024    228 	sub	sp,sp,36

000000c0 e1a06000    229 	mov	r6,r0

000000c4 e5920004    230 	ldr	r0,[r2,4]

000000c8 e3500007    231 	cmp	r0,7

000000cc 8a000078    232 	bhi	.L335

000000d0 eaffffff    233 	b	.L564


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2pk1.s
                     234 	.align	4

                     235 .L564:

                     236 

000000d4 e08ff100    237 	add	pc,pc,r0 lsl 2

                     238 .L503:

                     239 

000000d8 e1a00000    240 	nop	

000000dc ea000074    241 	b	.L335

000000e0 ea000005    242 	b	.L279

000000e4 ea000022    243 	b	.L295

000000e8 ea000030    244 	b	.L303

000000ec ea00004d    245 	b	.L319

000000f0 ea000060    246 	b	.L327

000000f4 ea00003c    247 	b	.L311

000000f8 ea00000e    248 	b	.L287

                     249 .L279:

000000fc e1a05003    250 	mov	r5,r3

                     251 ;67: 	CBAttrAccessInfo* descrStruct, bool determineSize)


                     252 ;68: {


                     253 

00000100 e1a04001    254 	mov	r4,r1

00000104 e5920008    255 	ldr	r0,[r2,8]

                     256 ;71: 


                     257 ;72: 	bool value = false;


                     258 

                     259 ;73: 


                     260 ;74: 	if (getRCB(rcbIndex, &pRCB))


                     261 

00000108 e1a0100d    262 	mov	r1,sp

0000010c eb000000*   263 	bl	getRCB

00000110 e3500000    264 	cmp	r0,0

                     265 ;75: 	{


                     266 

                     267 ;76: 		value = pRCB->rptEna;


                     268 

00000114 159d0000    269 	ldrne	r0,[sp]

00000118 e3a07000    270 	mov	r7,0

                     271 ;69: 	RCB* pRCB;


                     272 ;70: 	int rcbIndex = descrStruct->rcbIndex;


                     273 

0000011c 15d07001    274 	ldrneb	r7,[r0,1]

                     275 ;77: 	}


                     276 ;78: 


                     277 ;79: 	return encodeBoolValue(outBuf, bufPos, value, determineSize);    


                     278 

00000120 e1a03005    279 	mov	r3,r5

00000124 e1a02007    280 	mov	r2,r7

00000128 e1a01004    281 	mov	r1,r4

0000012c e1a00006    282 	mov	r0,r6

00000130 eb000000*   283 	bl	encodeBoolValue

00000134 ea00005f    284 	b	.L275

                     285 .L287:

00000138 e1a05003    286 	mov	r5,r3

                     287 ;83:     CBAttrAccessInfo* descrStruct, bool determineSize)


                     288 ;84: {


                     289 

0000013c e1a07001    290 	mov	r7,r1

00000140 e5920008    291 	ldr	r0,[r2,8]

                     292 ;87: 


                     293 ;88:     bool value = false;


                     294 


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2pk1.s
                     295 ;89: 


                     296 ;90:     if (getRCB(rcbIndex, &pRCB))


                     297 

00000144 e28d1004    298 	add	r1,sp,4

00000148 eb000000*   299 	bl	getRCB

0000014c e3500000    300 	cmp	r0,0

                     301 ;91:     {


                     302 

                     303 ;92:         value = pRCB->resv;


                     304 

00000150 159d0004    305 	ldrne	r0,[sp,4]

00000154 e3a04000    306 	mov	r4,0

                     307 ;85:     RCB* pRCB;


                     308 ;86:     int rcbIndex = descrStruct->rcbIndex;


                     309 

00000158 15d04002    310 	ldrneb	r4,[r0,2]

                     311 ;93:     }


                     312 ;94: 


                     313 ;95:     return encodeBoolValue(outBuf, bufPos, value, determineSize);


                     314 

0000015c e1a03005    315 	mov	r3,r5

00000160 e1a02004    316 	mov	r2,r4

00000164 e1a01007    317 	mov	r1,r7

00000168 e1a00006    318 	mov	r0,r6

0000016c eb000000*   319 	bl	encodeBoolValue

00000170 ea000050    320 	b	.L275

                     321 .L295:

00000174 e1a05003    322 	mov	r5,r3

                     323 ;99: 	CBAttrAccessInfo* descrStruct, bool determineSize)


                     324 ;100: {


                     325 

00000178 e1a07001    326 	mov	r7,r1

0000017c e5920008    327 	ldr	r0,[r2,8]

                     328 ;103: 


                     329 ;104: 	bool value = false;


                     330 

                     331 ;105: 


                     332 ;106: 	if (getRCB(rcbIndex, &pRCB))


                     333 

00000180 e28d1008    334 	add	r1,sp,8

00000184 eb000000*   335 	bl	getRCB

00000188 e3500000    336 	cmp	r0,0

                     337 ;107: 	{


                     338 

                     339 ;108: 		value = pRCB->gi;


                     340 

0000018c 159d0008    341 	ldrne	r0,[sp,8]

00000190 e3a04000    342 	mov	r4,0

                     343 ;101: 	RCB* pRCB;


                     344 ;102: 	int rcbIndex = descrStruct->rcbIndex;


                     345 

00000194 15d04022    346 	ldrneb	r4,[r0,34]

                     347 ;109: 	}


                     348 ;110: 


                     349 ;111: 	return encodeBoolValue(outBuf, bufPos, value, determineSize);


                     350 

00000198 e1a03005    351 	mov	r3,r5

0000019c e1a02004    352 	mov	r2,r4

000001a0 e1a01007    353 	mov	r1,r7

000001a4 e1a00006    354 	mov	r0,r6

000001a8 eb000000*   355 	bl	encodeBoolValue


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2pk1.s
000001ac ea000041    356 	b	.L275

                     357 .L303:

000001b0 e1a05003    358 	mov	r5,r3

                     359 ;115: 	CBAttrAccessInfo* descrStruct, bool determineSize)


                     360 ;116: {


                     361 

000001b4 e1a07001    362 	mov	r7,r1

000001b8 e5920008    363 	ldr	r0,[r2,8]

                     364 ;119: 


                     365 ;120: 	uint16_t value = 0;


                     366 

                     367 ;121: 


                     368 ;122: 	if (getRCB(rcbIndex, &pRCB))


                     369 

000001bc e28d100c    370 	add	r1,sp,12

000001c0 eb000000*   371 	bl	getRCB

000001c4 e3500000    372 	cmp	r0,0

                     373 ;123: 	{


                     374 

                     375 ;124: 		value = pRCB->sqNum;


                     376 

000001c8 159d000c    377 	ldrne	r0,[sp,12]

000001cc e3a04000    378 	mov	r4,0

                     379 ;117: 	RCB* pRCB;


                     380 ;118: 	int rcbIndex = descrStruct->rcbIndex;


                     381 

000001d0 11d041b8    382 	ldrneh	r4,[r0,24]

                     383 ;125: 	}


                     384 ;126: 


                     385 ;127: 	return encodeUInt32Value(outBuf, bufPos, value, determineSize);


                     386 

000001d4 e1a03005    387 	mov	r3,r5

000001d8 e1a02004    388 	mov	r2,r4

000001dc e1a01007    389 	mov	r1,r7

000001e0 e1a00006    390 	mov	r0,r6

000001e4 eb000000*   391 	bl	encodeUInt32Value

000001e8 ea000032    392 	b	.L275

                     393 .L311:

000001ec e1a05003    394 	mov	r5,r3

                     395 ;131: 	CBAttrAccessInfo* descrStruct, bool determineSize)


                     396 ;132: {


                     397 

000001f0 e1a07001    398 	mov	r7,r1

000001f4 e5920008    399 	ldr	r0,[r2,8]

                     400 ;135: 


                     401 ;136: 	uint32_t value = 0;


                     402 

                     403 ;137: 


                     404 ;138: 	if (getRCB(rcbIndex, &pRCB))


                     405 

000001f8 e28d1010    406 	add	r1,sp,16

000001fc eb000000*   407 	bl	getRCB

00000200 e3500000    408 	cmp	r0,0

                     409 ;139: 	{


                     410 

                     411 ;140: 		value = pRCB->intgPd;


                     412 

00000204 159d0010    413 	ldrne	r0,[sp,16]

00000208 e3a04000    414 	mov	r4,0

                     415 ;133: 	RCB* pRCB;


                     416 ;134: 	int rcbIndex = descrStruct->rcbIndex;



                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2pk1.s
                     417 

0000020c 1590401c    418 	ldrne	r4,[r0,28]

                     419 ;141: 	}


                     420 ;142: 


                     421 ;143: 	return encodeUInt32Value(outBuf, bufPos, value, determineSize);


                     422 

00000210 e1a03005    423 	mov	r3,r5

00000214 e1a02004    424 	mov	r2,r4

00000218 e1a01007    425 	mov	r1,r7

0000021c e1a00006    426 	mov	r0,r6

00000220 eb000000*   427 	bl	encodeUInt32Value

00000224 ea000023    428 	b	.L275

                     429 .L319:

00000228 e1a05003    430 	mov	r5,r3

                     431 ;163: 	CBAttrAccessInfo* descrStruct, bool determineSize)


                     432 ;164: {


                     433 

0000022c e1a07001    434 	mov	r7,r1

00000230 e3a00000    435 	mov	r0,0

00000234 e58d001c    436 	str	r0,[sp,28]

00000238 e58d0020    437 	str	r0,[sp,32]

                     438 ;165: 	RCB* pRCB;


                     439 ;166: 	int rcbIndex = descrStruct->rcbIndex;


                     440 

0000023c e5920008    441 	ldr	r0,[r2,8]

                     442 ;167: 


                     443 ;168: 	uint64_t value = 0;


                     444 

                     445 ;169: 


                     446 ;170: 	if (getRCB(rcbIndex, &pRCB))


                     447 

00000240 e28d1014    448 	add	r1,sp,20

00000244 eb000000*   449 	bl	getRCB

00000248 e1a03005    450 	mov	r3,r5

0000024c e3500000    451 	cmp	r0,0

                     452 ;171: 	{


                     453 

                     454 ;172: 		value = pRCB->entryID;


                     455 

00000250 159d0014    456 	ldrne	r0,[sp,20]

00000254 e28d201c    457 	add	r2,sp,28

00000258 15901024    458 	ldrne	r1,[r0,36]

0000025c 15900028    459 	ldrne	r0,[r0,40]

00000260 158d101c    460 	strne	r1,[sp,28]

00000264 e1a01007    461 	mov	r1,r7

00000268 158d0020    462 	strne	r0,[sp,32]

                     463 ;173: 	}


                     464 ;174: 	


                     465 ;175: 	return encodeOctetString8Value(outBuf, bufPos, &value, determineSize);


                     466 

0000026c e1a00006    467 	mov	r0,r6

00000270 eb000000*   468 	bl	encodeOctetString8Value

00000274 ea00000f    469 	b	.L275

                     470 .L327:

00000278 e1a05003    471 	mov	r5,r3

                     472 ;147: 	CBAttrAccessInfo* descrStruct, bool determineSize)


                     473 ;148: {


                     474 

0000027c e1a07001    475 	mov	r7,r1

00000280 e5920008    476 	ldr	r0,[r2,8]

                     477 ;151: 



                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2pk1.s
                     478 ;152: 	uint32_t value = 0;


                     479 

                     480 ;153: 


                     481 ;154: 	if (getRCB(rcbIndex, &pRCB))


                     482 

00000284 e28d1018    483 	add	r1,sp,24

00000288 eb000000*   484 	bl	getRCB

0000028c e3500000    485 	cmp	r0,0

                     486 ;155: 	{


                     487 

                     488 ;156: 		value = pRCB->confRev;


                     489 

00000290 159d0018    490 	ldrne	r0,[sp,24]

00000294 e3a04000    491 	mov	r4,0

                     492 ;149: 	RCB* pRCB;


                     493 ;150: 	int rcbIndex = descrStruct->rcbIndex;


                     494 

00000298 15904014    495 	ldrne	r4,[r0,20]

                     496 ;157: 	}


                     497 ;158: 


                     498 ;159: 	return encodeUInt32Value(outBuf, bufPos, value, determineSize);


                     499 

0000029c e1a03005    500 	mov	r3,r5

000002a0 e1a02004    501 	mov	r2,r4

000002a4 e1a01007    502 	mov	r1,r7

000002a8 e1a00006    503 	mov	r0,r6

000002ac eb000000*   504 	bl	encodeUInt32Value

000002b0 ea000000    505 	b	.L275

                     506 .L335:

000002b4 e3a00000    507 	mov	r0,0

                     508 .L275:

000002b8 e28dd024    509 	add	sp,sp,36

000002bc e8bd80f0    510 	ldmfd	[sp]!,{r4-r7,pc}

                     511 	.endf	encodeReadAttrRCB

                     512 	.align	4

                     513 ;pAccessInfo	r2	local

                     514 ;bufPos	r4	local

                     515 ;determineSize	r5	local

                     516 ;pRCB	[sp]	local

                     517 ;value	r7	local

                     518 ;bufPos	r7	local

                     519 ;determineSize	r5	local

                     520 ;pRCB	[sp,4]	local

                     521 ;value	r4	local

                     522 ;bufPos	r7	local

                     523 ;determineSize	r5	local

                     524 ;pRCB	[sp,8]	local

                     525 ;value	r4	local

                     526 ;bufPos	r7	local

                     527 ;determineSize	r5	local

                     528 ;pRCB	[sp,12]	local

                     529 ;value	r4	local

                     530 ;bufPos	r7	local

                     531 ;determineSize	r5	local

                     532 ;pRCB	[sp,16]	local

                     533 ;value	r4	local

                     534 ;bufPos	r7	local

                     535 ;determineSize	r5	local

                     536 ;pRCB	[sp,20]	local

                     537 ;value	[sp,28]	local

                     538 ;bufPos	r7	local


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2pk1.s
                     539 ;determineSize	r5	local

                     540 ;pRCB	[sp,24]	local

                     541 ;value	r4	local

                     542 

                     543 ;outBuf	r6	param

                     544 ;bufPos	r1	param

                     545 ;descrStruct	r2	param

                     546 ;determineSize	r3	param

                     547 

                     548 	.section ".bss","awb"

                     549 .L502:

                     550 	.data

                     551 	.ghsnote jtable,5,.L503,.L503,.L503,9

                     552 	.text

                     553 

                     554 

                     555 ;242: 


                     556 ;243: void writeGI(IsoConnection* isoConn, int rcbIndex, uint8_t* dataToWrite)


                     557 	.align	4

                     558 	.align	4

                     559 writeGI::

000002c0 e92d4070    560 	stmfd	[sp]!,{r4-r6,lr}

000002c4 e1a05000    561 	mov	r5,r0

000002c8 e1a04002    562 	mov	r4,r2

                     563 ;244: {


                     564 

                     565 ;245:     PReporter pReporter;


                     566 ;246: 


                     567 ;247:     //Сюда надо ещё проверку включен ли GI в TrgOp


                     568 ;248: 


                     569 ;249: 	if (dataToWrite[0] != IEC61850_BER_BOOLEAN || dataToWrite[1] != 1)


                     570 

000002cc e5d40000    571 	ldrb	r0,[r4]

000002d0 e3500083    572 	cmp	r0,131

000002d4 05d40001    573 	ldreqb	r0,[r4,1]

000002d8 03500001    574 	cmpeq	r0,1

000002dc 1a00000f    575 	bne	.L565

                     576 ;250: 	{


                     577 

                     578 ;251: 		return;


                     579 

                     580 ;252: 	}	


                     581 ;253: 


                     582 ;254:     pReporter = getReporterByIndex(rcbIndex);


                     583 

000002e0 e1a00001    584 	mov	r0,r1

000002e4 eb000000*   585 	bl	getReporterByIndex

000002e8 e1b06000    586 	movs	r6,r0

                     587 ;255:     if(pReporter == NULL)


                     588 

000002ec 0a00000b    589 	beq	.L565

                     590 ;256:     {


                     591 

                     592 ;257:         ERROR_REPORT("Writing to unregistered report");


                     593 ;258:         return;


                     594 

                     595 ;259:     }


                     596 ;260:     


                     597 ;261:     if(!isRCBConnected(pReporter)


                     598 

000002f0 eb000000*   599 	bl	isRCBConnected


                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2pk1.s
000002f4 e3500000    600 	cmp	r0,0

000002f8 0a000004    601 	beq	.L575

000002fc e1a01005    602 	mov	r1,r5

00000300 e1a00006    603 	mov	r0,r6

00000304 eb000000*   604 	bl	Reporter_isOwnerConnection

00000308 e3500000    605 	cmp	r0,0

0000030c 0a000003    606 	beq	.L565

                     607 .L575:

                     608 ;262:             || Reporter_isOwnerConnection(pReporter, isoConn))


                     609 ;263:     {


                     610 

                     611 ;264:         Reporter_setGI(pReporter, dataToWrite[2]);


                     612 

00000310 e5d41002    613 	ldrb	r1,[r4,2]

00000314 e1a00006    614 	mov	r0,r6

00000318 e8bd4070    615 	ldmfd	[sp]!,{r4-r6,lr}

0000031c ea000000*   616 	b	Reporter_setGI

                     617 .L565:

00000320 e8bd8070    618 	ldmfd	[sp]!,{r4-r6,pc}

                     619 	.endf	writeGI

                     620 	.align	4

                     621 ;pReporter	r6	local

                     622 

                     623 ;isoConn	r5	param

                     624 ;rcbIndex	r1	param

                     625 ;dataToWrite	r4	param

                     626 

                     627 	.section ".bss","awb"

                     628 .L646:

                     629 	.data

                     630 	.text

                     631 

                     632 ;265:     }


                     633 ;266: 


                     634 ;267: }


                     635 

                     636 ;268: 


                     637 ;269: void writeIntgPd(IsoConnection* isoConn, int rcbIndex, uint8_t* dataToWrite)


                     638 	.align	4

                     639 	.align	4

                     640 writeIntgPd::

00000324 e92d4070    641 	stmfd	[sp]!,{r4-r6,lr}

00000328 e1a04000    642 	mov	r4,r0

                     643 ;270: {


                     644 

                     645 ;271: 	PReporter pReporter;


                     646 ;272: 	size_t len;


                     647 ;273: 	uint32_t intgPd;


                     648 ;274: 


                     649 ;275: 	if (dataToWrite[0] != IEC61850_BER_UNSIGNED_INTEGER)


                     650 

0000032c e5d20000    651 	ldrb	r0,[r2]

00000330 e1a05001    652 	mov	r5,r1

00000334 e3500086    653 	cmp	r0,134

00000338 1a000014    654 	bne	.L662

                     655 ;276: 	{


                     656 

                     657 ;277: 		return;


                     658 

                     659 ;278: 	}


                     660 ;279: 



                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2pk1.s
                     661 ;280: 	len = dataToWrite[1];	


                     662 

0000033c e5d21001    663 	ldrb	r1,[r2,1]

                     664 ;281: 	intgPd = BerDecoder_decodeUint32(dataToWrite, len, 2);


                     665 

00000340 e1a00002    666 	mov	r0,r2

00000344 e3a02002    667 	mov	r2,2

00000348 eb000000*   668 	bl	BerDecoder_decodeUint32

0000034c e1a06000    669 	mov	r6,r0

                     670 ;282: 


                     671 ;283: 	pReporter = getReporterByIndex(rcbIndex);


                     672 

00000350 e1a00005    673 	mov	r0,r5

00000354 eb000000*   674 	bl	getReporterByIndex

00000358 e1b05000    675 	movs	r5,r0

                     676 ;284: 	if (pReporter == NULL)


                     677 

0000035c 0a00000b    678 	beq	.L662

                     679 ;285: 	{


                     680 

                     681 ;286: 		ERROR_REPORT("Writing to unregistered report");


                     682 ;287: 		return;


                     683 

                     684 ;288: 	}


                     685 ;289:     


                     686 ;290:     if(!isRCBConnected(pReporter)


                     687 

00000360 eb000000*   688 	bl	isRCBConnected

00000364 e3500000    689 	cmp	r0,0

00000368 0a000004    690 	beq	.L671

0000036c e1a01004    691 	mov	r1,r4

00000370 e1a00005    692 	mov	r0,r5

00000374 eb000000*   693 	bl	Reporter_isOwnerConnection

00000378 e3500000    694 	cmp	r0,0

0000037c 0a000003    695 	beq	.L662

                     696 .L671:

                     697 ;291:             || Reporter_isOwnerConnection(pReporter, isoConn))


                     698 ;292:     {


                     699 

                     700 ;293:         Reporter_setIntgPd(pReporter, intgPd);


                     701 

00000380 e1a01006    702 	mov	r1,r6

00000384 e1a00005    703 	mov	r0,r5

00000388 e8bd4070    704 	ldmfd	[sp]!,{r4-r6,lr}

0000038c ea000000*   705 	b	Reporter_setIntgPd

                     706 .L662:

00000390 e8bd8070    707 	ldmfd	[sp]!,{r4-r6,pc}

                     708 	.endf	writeIntgPd

                     709 	.align	4

                     710 ;pReporter	r5	local

                     711 ;intgPd	r6	local

                     712 

                     713 ;isoConn	r4	param

                     714 ;rcbIndex	r5	param

                     715 ;dataToWrite	r2	param

                     716 

                     717 	.section ".bss","awb"

                     718 .L735:

                     719 	.data

                     720 	.text

                     721 


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2pk1.s
                     722 ;294:     }


                     723 ;295: }


                     724 

                     725 ;296: 


                     726 ;297: void writeAttrRCB(struct IsoConnection* isoConn, void* descrStruct, uint8_t* dataToWrite)


                     727 	.align	4

                     728 	.align	4

                     729 writeAttrRCB::

00000394 e92d4030    730 	stmfd	[sp]!,{r4-r5,lr}

                     731 ;298: {


                     732 

                     733 ;299: 	CBAttrAccessInfo* pAccessInfo = descrStruct;


                     734 

                     735 ;300: 


                     736 ;301: 	switch (pAccessInfo->attrCode)


                     737 

00000398 e5913004    738 	ldr	r3,[r1,4]

0000039c e2533001    739 	subs	r3,r3,1

000003a0 0a000006    740 	beq	.L754

000003a4 e2533001    741 	subs	r3,r3,1

000003a8 0a000026    742 	beq	.L770

000003ac e2533004    743 	subs	r3,r3,4

000003b0 0a000027    744 	beq	.L771

000003b4 e3530001    745 	cmp	r3,1

000003b8 0a000011    746 	beq	.L762

000003bc ea000027    747 	b	.L750

                     748 .L754:

                     749 ;302: 	{


                     750 ;303: 	case RptEna:


                     751 ;304: 		writeRptEna(isoConn, pAccessInfo->rcbIndex, dataToWrite);


                     752 

000003c0 e1a04000    753 	mov	r4,r0

000003c4 e5910008    754 	ldr	r0,[r1,8]

                     755 ;204: {	


                     756 

                     757 ;205: 	bool value;


                     758 ;206: 	PReporter pReporter;


                     759 ;207: 


                     760 ;208: 	if (dataToWrite[0] != IEC61850_BER_BOOLEAN || dataToWrite[1] != 1)


                     761 

000003c8 e5d21000    762 	ldrb	r1,[r2]

000003cc e3510083    763 	cmp	r1,131

000003d0 05d21001    764 	ldreqb	r1,[r2,1]

000003d4 03510001    765 	cmpeq	r1,1

000003d8 1a000020    766 	bne	.L750

                     767 ;209: 	{


                     768 

                     769 ;210: 		return;


                     770 

                     771 ;211: 	}


                     772 ;212: 	value = dataToWrite[2] != 0;


                     773 

000003dc e5d21002    774 	ldrb	r1,[r2,2]

000003e0 e1b05001    775 	movs	r5,r1

000003e4 13a05001    776 	movne	r5,1

                     777 ;213: 	


                     778 ;214: 	pReporter = getReporterByIndex(rcbIndex);


                     779 

000003e8 eb000000*   780 	bl	getReporterByIndex

                     781 ;215: 	if (pReporter == NULL)


                     782 


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2pk1.s
000003ec e3500000    783 	cmp	r0,0

                     784 ;216: 	{


                     785 

                     786 ;217: 		ERROR_REPORT("Writing to unregistered report");


                     787 ;218: 		return;


                     788 

                     789 ;219: 	}


                     790 ;220: 	Reporter_setEnable(pReporter, isoConn, value);	


                     791 

000003f0 11a02005    792 	movne	r2,r5

000003f4 11a01004    793 	movne	r1,r4

000003f8 18bd4030    794 	ldmnefd	[sp]!,{r4-r5,lr}

000003fc 1a000000*   795 	bne	Reporter_setEnable

00000400 ea000016    796 	b	.L750

                     797 .L762:

                     798 ;305: 		break;


                     799 ;306:     case Resv:


                     800 ;307:         writeResv(isoConn, pAccessInfo->rcbIndex, dataToWrite);


                     801 

00000404 e1a04000    802 	mov	r4,r0

00000408 e5910008    803 	ldr	r0,[r1,8]

                     804 ;224: {


                     805 

                     806 ;225:     bool value;


                     807 ;226:     PReporter pReporter;


                     808 ;227: 


                     809 ;228:     if (dataToWrite[0] != IEC61850_BER_BOOLEAN || dataToWrite[1] != 1)


                     810 

0000040c e5d21000    811 	ldrb	r1,[r2]

00000410 e3510083    812 	cmp	r1,131

00000414 05d21001    813 	ldreqb	r1,[r2,1]

00000418 03510001    814 	cmpeq	r1,1

0000041c 1a00000f    815 	bne	.L750

                     816 ;229:     {


                     817 

                     818 ;230:         return;


                     819 

                     820 ;231:     }


                     821 ;232:     value = dataToWrite[2] != 0;


                     822 

00000420 e5d21002    823 	ldrb	r1,[r2,2]

00000424 e1b05001    824 	movs	r5,r1

00000428 13a05001    825 	movne	r5,1

                     826 ;233: 


                     827 ;234:     pReporter = getReporterByIndex(rcbIndex);


                     828 

0000042c eb000000*   829 	bl	getReporterByIndex

                     830 ;235:     if (pReporter == NULL)


                     831 

00000430 e3500000    832 	cmp	r0,0

                     833 ;236:     {


                     834 

                     835 ;237:         ERROR_REPORT("Writing to unregistered report");


                     836 ;238:         return;


                     837 

                     838 ;239:     }


                     839 ;240:     Reporter_setResv(pReporter, isoConn, value);


                     840 

00000434 11a02005    841 	movne	r2,r5

00000438 11a01004    842 	movne	r1,r4

0000043c 18bd4030    843 	ldmnefd	[sp]!,{r4-r5,lr}


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2pk1.s
00000440 1a000000*   844 	bne	Reporter_setResv

00000444 ea000005    845 	b	.L750

                     846 .L770:

                     847 ;308:         break;


                     848 ;309: 	case GI:


                     849 ;310: 		writeGI(isoConn, pAccessInfo->rcbIndex, dataToWrite);


                     850 

00000448 e5911008    851 	ldr	r1,[r1,8]

0000044c e8bd4030    852 	ldmfd	[sp]!,{r4-r5,lr}

00000450 eaffff9a*   853 	b	writeGI

                     854 .L771:

                     855 ;311: 		break;


                     856 ;312: 	case IntgPd:


                     857 ;313:         writeIntgPd(isoConn, pAccessInfo->rcbIndex, dataToWrite);


                     858 

00000454 e5911008    859 	ldr	r1,[r1,8]

00000458 e8bd4030    860 	ldmfd	[sp]!,{r4-r5,lr}

0000045c eaffffb0*   861 	b	writeIntgPd

                     862 .L750:

00000460 e8bd8030    863 	ldmfd	[sp]!,{r4-r5,pc}

                     864 	.endf	writeAttrRCB

                     865 	.align	4

                     866 ;pAccessInfo	r1	local

                     867 ;isoConn	r4	local

                     868 ;rcbIndex	r0	local

                     869 ;value	r5	local

                     870 ;pReporter	r1	local

                     871 ;isoConn	r4	local

                     872 ;rcbIndex	r0	local

                     873 ;value	r5	local

                     874 ;pReporter	r1	local

                     875 

                     876 ;isoConn	r0	param

                     877 ;descrStruct	r1	param

                     878 ;dataToWrite	r2	param

                     879 

                     880 	.section ".bss","awb"

                     881 .L878:

                     882 	.data

                     883 	.text

                     884 

                     885 ;314: 		break;


                     886 ;315: 	case EntryID:


                     887 ;316: 	case SqNum:


                     888 ;317: 		break;


                     889 ;318: 	default:


                     890 ;319: 		ERROR_REPORT("Unsupported RCB attribute");


                     891 ;320: 	}


                     892 ;321: 	


                     893 ;322: }


                     894 	.align	4

                     895 

                     896 	.data

                     897 	.ghsnote version,6

                     898 	.ghsnote tools,3

                     899 	.ghsnote options,0

                     900 	.text

                     901 	.align	4

