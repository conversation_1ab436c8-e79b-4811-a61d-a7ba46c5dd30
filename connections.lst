                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c8k1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=connections.c -o gh_c8k1.o -list=connections.lst C:\Users\<USER>\AppData\Local\Temp\gh_c8k1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_c8k1.s
Source File: connections.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile connections.c

                      10 ;		-o connections.o

                      11 ;Source File:   connections.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:56:07 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "connections.h"


                      21 ;2: 


                      22 ;3: #include "mms.h"


                      23 ;4: 


                      24 ;5: #include <stddef.h>


                      25 ;6: #include <stdlib.h>


                      26 ;7: #include <string.h>


                      27 ;8: 


                      28 ;9: #include <debug.h>


                      29 ;10: 


                      30 ;11: IsoConnection* allocateConnection(void)


                      31 	.text

                      32 	.align	4

                      33 allocateConnection::

00000000 e92d4010     34 	stmfd	[sp]!,{r4,lr}

                      35 ;12: {


                      36 

                      37 ;13: 	IsoConnection* allocated = malloc(sizeof(IsoConnection));


                      38 

00000004 e3a00fd8     39 	mov	r0,0x0360

00000008 e2800a56     40 	add	r0,r0,86<<12

0000000c eb000000*    41 	bl	malloc

00000010 e1b04000     42 	movs	r4,r0

                      43 ;14: 	if (allocated == NULL)


                      44 

                      45 ;15: 	{


                      46 

                      47 ;16: 		return NULL;


                      48 

                      49 ;17: 	}


                      50 ;18: 	memset(allocated, 0, sizeof(IsoConnection));



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c8k1.s
                      51 

00000014 13a02fd8     52 	movne	r2,0x0360

00000018 12822a56     53 	addne	r2,r2,86<<12

0000001c 13a01000     54 	movne	r1,0

00000020 1b000000*    55 	blne	memset

                      56 ;19: 	return allocated;


                      57 

00000024 e1a00004     58 	mov	r0,r4

00000028 e8bd8010     59 	ldmfd	[sp]!,{r4,pc}

                      60 	.endf	allocateConnection

                      61 	.align	4

                      62 ;allocated	r4	local

                      63 

                      64 	.section ".bss","awb"

                      65 .L56:

                      66 	.data

                      67 	.text

                      68 

                      69 ;20: }


                      70 

                      71 ;21: 


                      72 ;22: void freeConnection(IsoConnection* conn)


                      73 	.align	4

                      74 	.align	4

                      75 freeConnection::

                      76 ;23: {


                      77 

                      78 ;24: 	TRACE("Free connection memory");


                      79 ;25: 	free(conn);


                      80 

0000002c ea000000*    81 	b	free

                      82 	.endf	freeConnection

                      83 	.align	4

                      84 

                      85 ;conn	none	param

                      86 

                      87 	.section ".bss","awb"

                      88 .L94:

                      89 	.data

                      90 	.text

                      91 

                      92 ;26: }


                      93 	.align	4

                      94 

                      95 	.data

                      96 	.ghsnote version,6

                      97 	.ghsnote tools,3

                      98 	.ghsnote options,0

                      99 	.text

                     100 	.align	4

