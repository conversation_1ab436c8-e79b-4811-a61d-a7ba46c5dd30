                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c2s1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=bufView.c -o gh_c2s1.o -list=bufView.lst C:\Users\<USER>\AppData\Local\Temp\gh_c2s1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_c2s1.s
Source File: bufView.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile bufView.c -o

                      10 ;		bufView.o

                      11 ;Source File:   bufView.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:55:54 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "bufView.h"


                      21 ;2: #include "stringView.h"


                      22 ;3: #include <string.h>


                      23 ;4: #include <stddef.h>


                      24 ;5: 


                      25 ;6: void BufferView_init(BufferView* bv, uint8_t* buf, size_t len, size_t pos)


                      26 	.text

                      27 	.align	4

                      28 BufferView_init::

                      29 ;7: {


                      30 

                      31 ;8:     bv->p = buf;


                      32 

00000000 e1a0c002     33 	mov	r12,r2

00000004 e880100a     34 	stmea	[r0],{r1,r3,r12}

                      35 ;9:     bv->len = len;


                      36 

                      37 ;10:     bv->pos = pos;


                      38 

00000008 e12fff1e*    39 	ret	

                      40 	.endf	BufferView_init

                      41 	.align	4

                      42 

                      43 ;bv	r0	param

                      44 ;buf	r1	param

                      45 ;len	r2	param

                      46 ;pos	r3	param

                      47 

                      48 	.section ".bss","awb"

                      49 .L30:

                      50 	.data


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c2s1.s
                      51 	.text

                      52 

                      53 ;11: }


                      54 

                      55 ;12: 


                      56 ;13: bool BufferView_alloc(BufferView* bv, size_t byteCount, uint8_t** pFreeSpace)


                      57 	.align	4

                      58 	.align	4

                      59 BufferView_alloc::

                      60 ;14: {


                      61 

                      62 ;15:     size_t freeSpace = bv->len - bv->pos;


                      63 

0000000c e9901008     64 	ldmed	[r0],{r3,r12}

00000010 e04cc003     65 	sub	r12,r12,r3

                      66 ;16:     if (freeSpace < byteCount)


                      67 

00000014 e15c0001     68 	cmp	r12,r1

                      69 ;17:     {


                      70 

                      71 ;18:         return false;


                      72 

00000018 33a00000     73 	movlo	r0,0

                      74 ;19:     }


                      75 ;20:     *pFreeSpace = bv->p + bv->pos;


                      76 

0000001c 25900000     77 	ldrhs	r0,[r0]

00000020 20830000     78 	addhs	r0,r3,r0

00000024 25820000     79 	strhs	r0,[r2]

                      80 ;21:     return true;


                      81 

00000028 23a00001     82 	movhs	r0,1

0000002c e12fff1e*    83 	ret	

                      84 	.endf	BufferView_alloc

                      85 	.align	4

                      86 ;freeSpace	r12	local

                      87 

                      88 ;bv	r0	param

                      89 ;byteCount	r1	param

                      90 ;pFreeSpace	r2	param

                      91 

                      92 	.section ".bss","awb"

                      93 .L70:

                      94 	.data

                      95 	.text

                      96 

                      97 ;22: }


                      98 

                      99 ;23: 


                     100 ;24: 


                     101 ;25: bool BufferView_writeStringView(BufferView* bv, const StringView* strView)


                     102 	.align	4

                     103 	.align	4

                     104 BufferView_writeStringView::

00000030 e92d4030    105 	stmfd	[sp]!,{r4-r5,lr}

00000034 e1a05001    106 	mov	r5,r1

                     107 ;26: {


                     108 

                     109 ;27:     if (strView->len == 0)


                     110 

00000038 e5952000    111 	ldr	r2,[r5]


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c2s1.s
0000003c e3520000    112 	cmp	r2,0

                     113 ;28:     {


                     114 

                     115 ;29:         return true;


                     116 

00000040 03a00001    117 	moveq	r0,1

00000044 0a00000e    118 	beq	.L83

00000048 e1a04000    119 	mov	r4,r0

                     120 ;30:     }


                     121 ;31:     if (strView->len > bv->len - bv->pos)


                     122 

0000004c e9940003    123 	ldmed	[r4],{r0-r1}

00000050 e0411000    124 	sub	r1,r1,r0

00000054 e1520001    125 	cmp	r2,r1

                     126 ;32:     {


                     127 

                     128 ;33:         return false;


                     129 

00000058 83a00000    130 	movhi	r0,0

0000005c 8a000008    131 	bhi	.L83

                     132 ;34:     }


                     133 ;35:     memcpy(bv->p + bv->pos, strView->p, strView->len);


                     134 

00000060 e5943000    135 	ldr	r3,[r4]

00000064 e5951004    136 	ldr	r1,[r5,4]

00000068 e0800003    137 	add	r0,r0,r3

0000006c eb000000*   138 	bl	memcpy

                     139 ;36:     bv->pos += strView->len;


                     140 

00000070 e5951000    141 	ldr	r1,[r5]

00000074 e5940004    142 	ldr	r0,[r4,4]

00000078 e0800001    143 	add	r0,r0,r1

0000007c e5840004    144 	str	r0,[r4,4]

                     145 ;37:     return true;


                     146 

00000080 e3a00001    147 	mov	r0,1

                     148 .L83:

00000084 e8bd8030    149 	ldmfd	[sp]!,{r4-r5,pc}

                     150 	.endf	BufferView_writeStringView

                     151 	.align	4

                     152 

                     153 ;bv	r4	param

                     154 ;strView	r5	param

                     155 

                     156 	.section ".bss","awb"

                     157 .L126:

                     158 	.data

                     159 	.text

                     160 

                     161 ;38: }


                     162 

                     163 ;39: 


                     164 ;40: bool BufferView_writeStr(BufferView* bv, const char* str)


                     165 	.align	4

                     166 	.align	4

                     167 BufferView_writeStr::

00000088 e92d4010    168 	stmfd	[sp]!,{r4,lr}

0000008c e24dd008    169 	sub	sp,sp,8

00000090 e1a04000    170 	mov	r4,r0

                     171 ;41: {


                     172 


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c2s1.s
                     173 ;42:     StringView strView;


                     174 ;43:     StringView_fromCStr(&strView, (char*)str);


                     175 

00000094 e1a0000d    176 	mov	r0,sp

00000098 eb000000*   177 	bl	StringView_fromCStr

                     178 ;44:     return BufferView_writeStringView(bv, &strView);


                     179 

0000009c e1a0100d    180 	mov	r1,sp

000000a0 e1a00004    181 	mov	r0,r4

000000a4 ebffffe1*   182 	bl	BufferView_writeStringView

000000a8 e28dd008    183 	add	sp,sp,8

000000ac e8bd8010    184 	ldmfd	[sp]!,{r4,pc}

                     185 	.endf	BufferView_writeStr

                     186 	.align	4

                     187 ;strView	[sp]	local

                     188 

                     189 ;bv	r4	param

                     190 ;str	none	param

                     191 

                     192 	.section ".bss","awb"

                     193 .L177:

                     194 	.data

                     195 	.text

                     196 

                     197 ;45: }


                     198 

                     199 ;46: 


                     200 ;47: bool BufferView_writeUshortBE(BufferView* bv, uint16_t data)


                     201 	.align	4

                     202 	.align	4

                     203 BufferView_writeUshortBE::

000000b0 e92d0100    204 	stmfd	[sp]!,{r8}

                     205 ;48: {


                     206 

                     207 ;49:     if (bv->pos + sizeof(uint16_t) > bv->len)


                     208 

000000b4 e990000c    209 	ldmed	[r0],{r2-r3}

000000b8 e1a08002    210 	mov	r8,r2

000000bc e2882002    211 	add	r2,r8,2

000000c0 e1520003    212 	cmp	r2,r3

                     213 ;50:     {


                     214 

                     215 ;51:         return false;


                     216 

000000c4 83a00000    217 	movhi	r0,0

000000c8 8a000009    218 	bhi	.L184

                     219 ;52:     }


                     220 ;53:     bv->p[bv->pos++] = data >> 8;


                     221 

000000cc e5903000    222 	ldr	r3,[r0]

000000d0 e288c001    223 	add	r12,r8,1

000000d4 e580c004    224 	str	r12,[r0,4]

000000d8 e1a0c441    225 	mov	r12,r1 asr 8

000000dc e7c3c008    226 	strb	r12,[r3,r8]

                     227 ;54:     bv->p[bv->pos++] = data & 0xFF;


                     228 

000000e0 e8900108    229 	ldmfd	[r0],{r3,r8}

000000e4 e288c001    230 	add	r12,r8,1

000000e8 e580c004    231 	str	r12,[r0,4]

000000ec e7c31008    232 	strb	r1,[r3,r8]

                     233 ;55:     return true;



                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c2s1.s
                     234 

000000f0 e3a00001    235 	mov	r0,1

                     236 .L184:

000000f4 e8bd0100    237 	ldmfd	[sp]!,{r8}

000000f8 e12fff1e*   238 	ret	

                     239 	.endf	BufferView_writeUshortBE

                     240 	.align	4

                     241 

                     242 ;bv	r0	param

                     243 ;data	r1	param

                     244 

                     245 	.section ".bss","awb"

                     246 .L214:

                     247 	.data

                     248 	.text

                     249 

                     250 ;56: }


                     251 

                     252 ;57: 


                     253 ;58: size_t BufferView_writeData(BufferView* bv, void* data, size_t byteCount)


                     254 	.align	4

                     255 	.align	4

                     256 BufferView_writeData::

000000fc e92d4030    257 	stmfd	[sp]!,{r4-r5,lr}

00000100 e1a05000    258 	mov	r5,r0

                     259 ;59: {


                     260 

                     261 ;60:     size_t freeSpace = bv->len - bv->pos;


                     262 

00000104 e9950018    263 	ldmed	[r5],{r3-r4}

00000108 e0544003    264 	subs	r4,r4,r3

                     265 ;61:     if (freeSpace == 0)


                     266 

                     267 ;62:     {


                     268 

                     269 ;63:         return 0;


                     270 

0000010c 0a000008    271 	beq	.L228

00000110 e1540002    272 	cmp	r4,r2

00000114 81a04002    273 	movhi	r4,r2

                     274 ;64:     }


                     275 ;65:     if (freeSpace < byteCount)


                     276 

                     277 

                     278 

                     279 ;68:     }


                     280 ;69:     memcpy(bv->p + bv->pos, data, byteCount);


                     281 

00000118 e595c000    282 	ldr	r12,[r5]

0000011c e1a02004    283 	mov	r2,r4

00000120 e083000c    284 	add	r0,r3,r12

00000124 eb000000*   285 	bl	memcpy

                     286 ;70:     bv->pos += byteCount;


                     287 

00000128 e5951004    288 	ldr	r1,[r5,4]

0000012c e0811004    289 	add	r1,r1,r4

00000130 e5851004    290 	str	r1,[r5,4]

                     291 ;71:     return byteCount;


                     292 

                     293 .L228:

00000134 e1a00004    294 	mov	r0,r4


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c2s1.s
00000138 e8bd8030    295 	ldmfd	[sp]!,{r4-r5,pc}

                     296 	.endf	BufferView_writeData

                     297 	.align	4

                     298 ;freeSpace	r4	local

                     299 

                     300 ;bv	r5	param

                     301 ;data	r1	param

                     302 ;byteCount	r2	param

                     303 

                     304 	.section ".bss","awb"

                     305 .L289:

                     306 	.data

                     307 	.text

                     308 

                     309 ;72: }


                     310 

                     311 ;73: 


                     312 ;74: bool BufferView_readStringView(BufferView* bv, size_t strLen,


                     313 	.align	4

                     314 	.align	4

                     315 BufferView_readStringView::

0000013c e92d4030    316 	stmfd	[sp]!,{r4-r5,lr}

00000140 e1a04000    317 	mov	r4,r0

00000144 e1a05001    318 	mov	r5,r1

00000148 e1a03002    319 	mov	r3,r2

                     320 ;75:     StringView* result)


                     321 ;76: {


                     322 

                     323 ;77:     if (bv->pos + strLen > bv->len)


                     324 

0000014c e9940005    325 	ldmed	[r4],{r0,r2}

00000150 e0801005    326 	add	r1,r0,r5

00000154 e1510002    327 	cmp	r1,r2

                     328 ;78:     {


                     329 

                     330 ;79:         return false;


                     331 

00000158 83a00000    332 	movhi	r0,0

0000015c 8a000008    333 	bhi	.L303

                     334 ;80:     }


                     335 ;81:     StringView_init(result, (const char*)bv->p + bv->pos, strLen);


                     336 

00000160 e5941000    337 	ldr	r1,[r4]

00000164 e1a02005    338 	mov	r2,r5

00000168 e0801001    339 	add	r1,r0,r1

0000016c e1a00003    340 	mov	r0,r3

00000170 eb000000*   341 	bl	StringView_init

                     342 ;82:     bv->pos += strLen;


                     343 

00000174 e5940004    344 	ldr	r0,[r4,4]

00000178 e0800005    345 	add	r0,r0,r5

0000017c e5840004    346 	str	r0,[r4,4]

                     347 ;83:     return true;


                     348 

00000180 e3a00001    349 	mov	r0,1

                     350 .L303:

00000184 e8bd8030    351 	ldmfd	[sp]!,{r4-r5,pc}

                     352 	.endf	BufferView_readStringView

                     353 	.align	4

                     354 

                     355 ;bv	r4	param


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c2s1.s
                     356 ;strLen	r5	param

                     357 ;result	r3	param

                     358 

                     359 	.section ".bss","awb"

                     360 .L342:

                     361 	.data

                     362 	.text

                     363 

                     364 ;84: }


                     365 

                     366 ;85: 


                     367 ;86: bool BufferView_advance(BufferView* bv, size_t len)


                     368 	.align	4

                     369 	.align	4

                     370 BufferView_advance::

                     371 ;87: {


                     372 

                     373 ;88:     if (bv->pos + len > bv->len)


                     374 

00000188 e990000c    375 	ldmed	[r0],{r2-r3}

0000018c e0811002    376 	add	r1,r1,r2

00000190 e1510003    377 	cmp	r1,r3

                     378 ;89:     {


                     379 

                     380 ;90:         return false;


                     381 

00000194 83a00000    382 	movhi	r0,0

                     383 ;91:     }


                     384 ;92:     bv->pos += len;


                     385 

00000198 95801004    386 	strls	r1,[r0,4]

                     387 ;93:     return true;


                     388 

0000019c 93a00001    389 	movls	r0,1

000001a0 e12fff1e*   390 	ret	

                     391 	.endf	BufferView_advance

                     392 	.align	4

                     393 

                     394 ;bv	r0	param

                     395 ;len	r1	param

                     396 

                     397 	.section ".bss","awb"

                     398 .L390:

                     399 	.data

                     400 	.text

                     401 

                     402 ;94: }


                     403 	.align	4

                     404 

                     405 	.data

                     406 	.ghsnote version,6

                     407 	.ghsnote tools,3

                     408 	.ghsnote options,0

                     409 	.text

                     410 	.align	4

