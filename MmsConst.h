#pragma once

#define MAX_CONN_COUNT 4

// TODO MAX_DOMAIN_NAME
#define MAX_MMS_DOMAIN_NAME 128

//! Максимальное полное имя объекта
#define MAX_OBJECT_REFERENCE 129

// TODO MAX_MMS_APDU
#define MAX_MMS_RESPONSE_SIZE 4096

#define FILE_NAME_BUF_SIZE 255
//-100 - запаc на шапку. Назначен от балды
#define FILE_BUF_SIZE (MAX_MMS_RESPONSE_SIZE - 100)



#define DEFAULT_BUFFER_SIZE (1024 * 24)
#define DEFAULT_REPORT_BUFFER_SIZE 8192

//! Максимальный размер списка переменных для чтения
#define MAX_READ_VARIABLE_OBJECT_LIST 100

//! Максимальный размер списка результатов записи
#define MAX_WR_RESULT_LIST 100


#define ASN_VARIABLE_SPECIFICATION_OBJECT_NAME 0xa0

//Типы пакетов MMS
#define	MMS_INITIATE_REQUEST_PDU	0xa8		//Запрос Инициирование Ассоциации
#define	MMS_INITIATE_RESPONSE_PDU	0xa9		//Ответ Инициирование Ассоциации
#define	MMS_CONFIRMED_REQUEST_PDU	0xa0		//Запрос Услуги MMS
#define	MMS_CONFIRMED_RESPONSE_PDU	0xa1		//Ответ Услуги MMS
#define	MMS_CONFIRMED_ERRROR_PDU	0xa2		//Подтверждаемая ошибка MMS

//Тэг для getAccessAttributes
#define VAR_DELETABLE 0x80

typedef enum {
    DATA_ACCESS_ERROR_SUCCESS = -1,
    DATA_ACCESS_ERROR_OBJECT_INVALIDATED = 0,
    DATA_ACCESS_ERROR_HARDWARE_FAULT = 1,
    DATA_ACCESS_ERROR_TEMPORARILY_UNAVAILABLE = 2,
    DATA_ACCESS_ERROR_OBJECT_ACCESS_DENIED = 3,
    DATA_ACCESS_ERROR_OBJECT_UNDEFINED = 4,
    DATA_ACCESS_ERROR_INVALID_ADDRESS = 5,
    DATA_ACCESS_ERROR_TYPE_UNSUPPORTED = 6,
    DATA_ACCESS_ERROR_TYPE_INCONSISTENT = 7,
    DATA_ACCESS_ERROR_OBJECT_ATTRIBUTE_INCONSISTENT = 8,
    DATA_ACCESS_ERROR_OBJECT_ACCESS_UNSUPPORTED = 9,
    DATA_ACCESS_ERROR_OBJECT_NONE_EXISTENT = 10,
    DATA_ACCESS_ERROR_OBJECT_VALUE_INVALID = 11,
    DATA_ACCESS_ERROR_UNKNOWN = 12
} MmsDataAccessError;
