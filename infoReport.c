#include "infoReport.h"

#include "AsnEncoding.h"
#include "bufViewBER.h"
#include "mms.h"
#include "iedTree/iedEntity.h"
#include "iedTree/iedObjects.h"
#include <debug.h>

#pragma alignvar (4)

//ApplError information report variable access specification
//Эта информация неизменна и всегда содержит
//строку "LastApplError"
uint8_t applErrorVarSpec[] = {
0xA0, 0x13, 0x30, 0x11, 0xA0, 0x0F, 0x80, 0x0D,
0x4C, 0x61, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6C,
0x45, 0x72, 0x72, 0x6F, 0x72 };

char *lastApplErrorName = "LastApplError";


typedef struct
{
    size_t cntrlObjName;
    size_t error;
    size_t originFields;
    size_t origin;
    size_t ctlNum;
    size_t addCause;
    size_t allFields;
    size_t fullStruct;
} LastApplErrorSizes;

// Имя объекта управления (скорее всего pos) при при передаче
// negative CommandTermination.
// Используется из потока отчётов reportsThread
static uint8_t ctrlObjNameBuf[MAX_OBJECT_REFERENCE];

static void calcLastApplErrSizes(LastApplErrorSizes* sizes,
                                 StringView* cntrlObj, uint8_t error, uint8_t orCat,
                                 StringView* orIdent, uint8_t ctlNum, uint8_t addCause)
{
    size_t orIdentSize;
    size_t orCatSize;

    sizes->addCause = BerEncoder_uint32determineEncodedSizeTL(addCause);
    sizes->ctlNum = BerEncoder_uint32determineEncodedSizeTL(ctlNum);

    orIdentSize = BerEncoder_determineFullObjectSize(orIdent->len);
    orCatSize = BerEncoder_uint32determineEncodedSizeTL(orCat);
    sizes->originFields = orIdentSize + orCatSize;

    sizes->origin = BerEncoder_determineFullObjectSize(orCatSize + orIdentSize);
    sizes->error = BerEncoder_uint32determineEncodedSizeTL(error);
    sizes->cntrlObjName = BerEncoder_determineFullObjectSize(cntrlObj->len);

    sizes->allFields = sizes->cntrlObjName + sizes->error + sizes->origin
            + sizes->ctlNum + sizes->addCause;
    sizes->fullStruct = BerEncoder_determineFullObjectSize(sizes->allFields);
}

static bool encodeLastApplErrErr(BufferView* wrBuf, LastApplErrorSizes* sizes,
                              StringView* cntrlObjName, uint8_t error, uint8_t orCat,
                              StringView* orIdent, uint8_t ctlNum, uint8_t addCause)
{
    //Структура
    if(!BufferView_encodeTL(wrBuf, 0xA2,  sizes->allFields))
    {
        return false;
    }
    //Поля структуры
    //cntrlObj
    if(!BufferView_encodeStringView(wrBuf, IEC61850_BER_VISIBLE_STRING,
                                    cntrlObjName))
    {
        return false;
    }
    //error
    if(!BufferView_encodeUInt32(wrBuf, IEC61850_BER_INTEGER, error))
    {
        return false;
    }

    //origin
    if(!BufferView_encodeTL(wrBuf, 0xA2, sizes->originFields))
    {
        return false;
    }
    //orCat
    if(!BufferView_encodeUInt32(wrBuf, IEC61850_BER_INTEGER, orCat))
    {
        return false;
    }
    //orIdent
    if(!BufferView_encodeStringView(wrBuf, IEC61850_BER_OCTET_STRING,
                                    orIdent))
    {
        return false;
    }
    //ctlNum
    if(!BufferView_encodeUInt32(wrBuf, IEC61850_BER_UNSIGNED_INTEGER,
                                ctlNum))
    {
        return false;
    }
    //addCause
    if(!BufferView_encodeUInt32(wrBuf, IEC61850_BER_INTEGER, addCause))
    {
        return false;
    }
    return true;
}

bool InfoReport_createLastApplErrorReport(BufferView* wrBuf,
                                      StringView* cntrlObj, uint8_t error, uint8_t orCat,
                                      StringView* orIdent, uint8_t ctlNum, uint8_t addCause)
{
    LastApplErrorSizes errSizes;

    //Variable acess specification (0xA0)
    //Эта часть для этого вида отчёта всегда одинаковая
    if(!BufferView_writeData(wrBuf, applErrorVarSpec, sizeof(applErrorVarSpec)))
    {
        return false;
    }

    //Вычисляем размеры для List of access result (0xA0)
    calcLastApplErrSizes(&errSizes, cntrlObj, error, orCat, orIdent, ctlNum, addCause);


    // Кодируем List of access result (0xA0)
    if(!BufferView_encodeTL(wrBuf, 0xA0, errSizes.fullStruct))
    {
        return false;
    }

    return encodeLastApplErrErr(wrBuf, &errSizes, cntrlObj, error, orCat, orIdent,
                                ctlNum, addCause);
}


bool InfoReport_createPositiveCmdTermReport( IEDEntity cntrlObj,
    BufferView* wrBuf, StringView* domainId, StringView* itemId)
{
    size_t cntrlObjLen;
    size_t domainSpecificLen;
    size_t nameObjLen;
    size_t listOfVarLen;
    size_t varAccessSpec;

    //===============Вычисляем размеры=============
    //Variable acess specification (0xA0)
    domainSpecificLen = BerEncoder_determineFullObjectSize(domainId->len)
            + BerEncoder_determineFullObjectSize(itemId->len);
    nameObjLen = BerEncoder_determineFullObjectSize(domainSpecificLen);
    listOfVarLen = BerEncoder_determineFullObjectSize(nameObjLen);
    varAccessSpec = BerEncoder_determineFullObjectSize(listOfVarLen);

    //Control object
    if(!cntrlObj->calcReadLen(cntrlObj, &cntrlObjLen))
    {
        return false;
    }

    //===================Кодируем==================
    //List of varibles (0xA0)
    if(!BufferView_encodeTL(wrBuf, 0xA0, varAccessSpec))
    {
        return false;
    }
    //Имя всегда в тэге SEQUENCE (0x30)
    if(!BufferView_encodeTL(wrBuf, ASN_SEQUENCE, listOfVarLen))
    {
        return false;
    }
    //Name(0xA0)
    if(!BufferView_encodeTL(wrBuf, 0xA0, nameObjLen))
    {
        return false;
    }
    //Domain specific(0xA1)
    if(!BufferView_encodeTL(wrBuf, 0xA1, domainSpecificLen))
    {
        return false;
    }
    //Domain ID
    if(!BufferView_encodeStringView(wrBuf, ASN_VISIBLE_STRING, domainId))
    {
        return false;
    }
    //Item ID
    if(!BufferView_encodeStringView(wrBuf, ASN_VISIBLE_STRING, itemId))
    {
        return false;
    }

    // Кодируем List of access result (0xA0)
    if(!BufferView_encodeTL(wrBuf, 0xA0, cntrlObjLen))
    {
        return false;
    }
    //Control object
    return cntrlObj->encodeRead(cntrlObj, wrBuf);
}

bool InfoReport_createNegativeCmdTermReport( IEDEntity ctrlObj,
    BufferView* wrBuf, StringView* domainId, StringView* itemId, uint8_t addCause)
{
    LastApplErrorSizes errSizes;
    StringView ctrlObjParentFullName;
    BufferView ctrlObjParentNameBuf;
    int32_t orCat;
    StringView orIdent;
    size_t ctrlObjLen;
    size_t errNameLen;
    size_t errNameVmdSpecificLen;
    size_t errNameStrucLen;
    size_t operDomainSpecificLen;
    size_t operVarSpecName;
    size_t operVarNameStrucLen;
    size_t allVarSpec;
    size_t operNameLen;
    uint8_t error = 0;
    uint8_t ctlNum = 0;




    //Получаем полное имя родителя
    BufferView_init(&ctrlObjParentNameBuf, ctrlObjNameBuf,
                    sizeof(ctrlObjNameBuf), 0);
    if(!IEDEntity_getFullName(ctrlObj->parent, &ctrlObjParentNameBuf ))
    {
        ERROR_REPORT("Unable to get full object name");
        return false;
    }
    StringView_init(&ctrlObjParentFullName, (char*)ctrlObjParentNameBuf.p,
                    ctrlObjParentNameBuf.pos);


    if(!IEDControlDA_getOrCat(ctrlObj, &orCat))
    {
        ERROR_REPORT("Unable to get orCat");
        return false;
    }

    if(!IEDControlDA_getOrIdent(ctrlObj, &orIdent))
    {
        ERROR_REPORT("Unable to get orIdent");
        return false;
    }

    //===============Вычисляем размеры=============
    //Variable acess specification (0xA0)
    //LastApplError
    errNameLen = BerEncoder_determineFullObjectSize(strlen(lastApplErrorName));
    errNameVmdSpecificLen = BerEncoder_determineFullObjectSize(errNameLen);
    errNameStrucLen = BerEncoder_determineFullObjectSize(errNameVmdSpecificLen);

    //Oper
    operNameLen = BerEncoder_determineFullObjectSize(domainId->len)
            + BerEncoder_determineFullObjectSize(itemId->len);
    operDomainSpecificLen = BerEncoder_determineFullObjectSize(operNameLen);
    operVarSpecName = BerEncoder_determineFullObjectSize(operDomainSpecificLen);
    operVarNameStrucLen = BerEncoder_determineFullObjectSize(operVarSpecName);

    //allVarSpec = BerEncoder_determineFullObjectSize(errNameStrucLen + operVarNameStrucLen);
    allVarSpec = errNameStrucLen + operVarNameStrucLen;

    //Размер данных
    //LastApplError
    calcLastApplErrSizes(&errSizes, &ctrlObjParentFullName, error, orCat, &orIdent,
                         ctlNum, addCause);

    //Control object
    if(!ctrlObj->calcReadLen(ctrlObj, &ctrlObjLen))
    {
        return false;
    }

    //===================Кодируем==================
    //List of varibles (0xA0)
    if(!BufferView_encodeTL(wrBuf, 0xA0, allVarSpec))
    {
        return false;
    }
    //LastApplError
    //Имя всегда в тэге SEQUENCE (0x30)
    if(!BufferView_encodeTL(wrBuf, ASN_SEQUENCE, errNameVmdSpecificLen))
    {
        return false;
    }
    //0xA0
    if(!BufferView_encodeTL(wrBuf, 0xA0, errNameLen ))
    {
        return false;
    }
    //0x80
    if(!BufferView_encodeStr(wrBuf, 0x80 ,lastApplErrorName))
    {
        return false;
    }

    //Oper
    //Имя всегда в тэге SEQUENCE (0x30)
    if(!BufferView_encodeTL(wrBuf, ASN_SEQUENCE, operVarSpecName))
    {
        return false;
    }
    //Name(0xA0)
    if(!BufferView_encodeTL(wrBuf, 0xA0, operDomainSpecificLen))
    {
        return false;
    }
    //Domain specific(0xA1)
    if(!BufferView_encodeTL(wrBuf, 0xA1, operNameLen))
    {
        return false;
    }
    //Domain ID
    if(!BufferView_encodeStringView(wrBuf, ASN_VISIBLE_STRING, domainId))
    {
        return false;
    }
    //Item ID
    if(!BufferView_encodeStringView(wrBuf, ASN_VISIBLE_STRING, itemId))
    {
        return false;
    }


    // Кодируем List of access result (0xA0)
    if(!BufferView_encodeTL(wrBuf, 0xA0, errSizes.fullStruct + ctrlObjLen))
    {
        return false;
    }

    //LastApplError
    if(!encodeLastApplErrErr(wrBuf, &errSizes,&ctrlObjParentFullName, error, orCat,
                             &orIdent, ctlNum, addCause))
    {
        return false;
    }
    //Oper
    if(!ctrlObj->encodeRead(ctrlObj, wrBuf))
    {
        return false;
    }
    return true;
}


//Оформляет и отправляет отчёт
void InfoReport_send(IsoConnection* isoConn, uint8_t * infoReport,
                     size_t byteCount, uint8_t* reportMmsBuf,
                     uint8_t* reportPresentationBuf)
{
    int sessionDataLen;
    int presentationDataLen;
    int bufPos = 0;
    int reportLen;

    SessionOutBuffer* sessionOutBuf = allocSessionOutBuffer(
        &isoConn->outBuffers, SESSION_OUT_BUF_SIZE);
    if (!sessionOutBuf)
    {
        ERROR_REPORT("Unable to allocate buffer for the report");
        return;
    }

    //Определяем длины

    //A0 Information report
    reportLen = 1 +
        BerEncoder_determineLengthSize(byteCount) + byteCount;

    // Кодируем
    //Unconfirmed PDU
    bufPos = BerEncoder_encodeTL(0xA3, reportLen, reportMmsBuf, bufPos);
    bufPos = BerEncoder_encodeTL(0xA0, byteCount, reportMmsBuf, bufPos);
    memcpy(reportMmsBuf + bufPos, infoReport, byteCount);
    bufPos += byteCount;
    byteCount = bufPos;

    presentationDataLen = IsoPresentation_createUserData(&isoConn->presentation,
        reportPresentationBuf, reportMmsBuf, byteCount);

    sessionDataLen = isoSession_createDataSpdu(sessionOutBuf->cotpOutBuf,
        SESSION_OUT_BUF_SIZE, reportPresentationBuf, presentationDataLen);
    sessionOutBuf->byteCount = sessionDataLen;
    if (!OutQueue_insert(&isoConn->outQueue, sessionOutBuf))
    {
        ERROR_REPORT("Out queue overflow");
        return;
    }
}






