#include "control.h"

#include "bufViewBER.h"
#include "BaseAsnTypes.h"
#include "iedmodel.h"
#include "mms_data.h"
#include "pwin_access.h"
#include "infoReport.h"
#include "mms.h"
#include "iedTree/iedEntity.h"
#include "iedTree/iedObjects.h"

#include <Clib.h>
#include <stdbool.h>
#include <stddef.h>

#define MAX_CTRL_OBJ_COUNT 40

static size_t ctrlObjCnt =0;
IEDEntity ctrlObjects[MAX_CTRL_OBJ_COUNT];


// Имя объекта управления (скорее всего pos) при записи
// используется для формирования CommandTermination information report
uint8_t cmdTermObjNameBuf[MAX_OBJECT_REFERENCE];
//Используются из потока отчетов

//Буфера для посылки CommandTermination InformationReport
//Используются из потока отчетов
static uint8_t cmdTermDataBuf[DEFAULT_REPORT_BUFFER_SIZE];
static uint8_t cmdTermMmsBuf[DEFAULT_REPORT_BUFFER_SIZE];
static uint8_t cmdTermPresentationBuf[DEFAULT_REPORT_BUFFER_SIZE];

bool Control_registerCtrlObj(IEDEntity entity)
{
    if(ctrlObjCnt >= MAX_CTRL_OBJ_COUNT)
    {
        return false;
    }
    ctrlObjects[ctrlObjCnt] = entity;
    ctrlObjCnt++;
    return true;
}

void Control_processCtrlObjects(void)
{
    size_t i;
    for(i = 0; i < ctrlObjCnt; i++)
    {
        IEDControlDA_checkTerminate(ctrlObjects[i]);
    }
}

void Control_disableWaitingObjects(void)
{
    size_t i;
    for(i = 0; i < ctrlObjCnt; i++)
    {
        IEDControlDA_disconnect(ctrlObjects[i]);
    }
}

bool Control_sendServiceErrorReport(IsoConnection* isoConn,
                                    IEDEntity controlObject, uint8_t addCause)
{
    MmsConnection* mmsConn = &isoConn->mmsConn;
    BufferView wrBuf;
    StringView cntrlObjName;
    BufferView cntrlObjNameBuf;
    StringView orIdent;
    int32_t orCat;

    //Имя объекта управления
    if(controlObject->parent == NULL)
    {
        ERROR_REPORT("Invalid parent");
        return false;
    }


    BufferView_init(&cntrlObjNameBuf, mmsConn->wrCrtlObjNameBuf,
                    sizeof(mmsConn->wrCrtlObjNameBuf), 0);

    if(!IEDEntity_getFullName(controlObject->parent, &cntrlObjNameBuf  ))
    {
        return false;
    }

    //Получаем имя объекта из буфера
    StringView_init(&cntrlObjName, (const char*)cntrlObjNameBuf.p, cntrlObjNameBuf.pos);

    BufferView_init(&wrBuf, mmsConn->infoReportDataBuf,
                    sizeof(mmsConn->infoReportDataBuf), 0);

    if(!IEDControlDA_getOrIdent(controlObject, &orIdent))
    {
        ERROR_REPORT("Unable to get orIdent");
        return false;
    }

    if(!IEDControlDA_getOrCat(controlObject, &orCat))
    {
        ERROR_REPORT("Unable to get orCat");
        return false;
    }

    if(!InfoReport_createLastApplErrorReport( &wrBuf,
                                          &cntrlObjName, 0, orCat, &orIdent, 0, addCause))
    {
        return false;
    }

    InfoReport_send(isoConn, wrBuf.p, wrBuf.pos,
                    isoConn->mmsConn.infoReportBuf,
                    isoConn->mmsConn.infoReportPresentationBuf);
    return true;
}

//функция вызывается из потока отчётов, поэтому использует глобальные
//буфера
bool Control_sendPositiveCmdTermReport(IsoConnection* isoConn,
                                    IEDEntity controlObject)
{
    StringView cntrlObjItemId;
    BufferView cntrlObjItemIdBuf;
    StringView* cntrlObjDomainId;
    BufferView wrBuf;


    //Получаем itemId
    BufferView_init(&cntrlObjItemIdBuf, cmdTermObjNameBuf,
                    sizeof(cmdTermObjNameBuf), 0);

    if(!IEDEntity_getFullItemId(controlObject, &cntrlObjItemIdBuf ))
    {
        return false;
    }

    //Получаем itemId объекта из буфера
    StringView_init(&cntrlObjItemId, (const char*)cntrlObjItemIdBuf.p, cntrlObjItemIdBuf.pos);

    //Получаем domainId
    if(!IEDEntity_getDomainId(controlObject, &cntrlObjDomainId ))
    {
        return false;
    }

    BufferView_init(&wrBuf, cmdTermDataBuf, sizeof(cmdTermDataBuf), 0);

    if(!InfoReport_createPositiveCmdTermReport(controlObject, &wrBuf, cntrlObjDomainId,
                                               &cntrlObjItemId))
    {
        return false;
    }

    InfoReport_send(isoConn, wrBuf.p, wrBuf.pos, cmdTermMmsBuf,
                    cmdTermPresentationBuf);
    return true;
}

//функция вызывается из потока отчётов, поэтому использует глобальные
//буфера
bool Control_sendNegativeCmdTermReport(IsoConnection* isoConn,
                                    IEDEntity controlObject, uint8_t addCause)
{
    StringView cntrlObjItemId;
    BufferView cntrlObjItemIdBuf;
    StringView* cntrlObjDomainId;
    BufferView wrBuf;

    //Получаем itemId
    BufferView_init(&cntrlObjItemIdBuf, cmdTermObjNameBuf,
                    sizeof(cmdTermObjNameBuf), 0);

    if(!IEDEntity_getFullItemId(controlObject, &cntrlObjItemIdBuf ))
    {
        return false;
    }

    //Получаем itemId объекта из буфера
    StringView_init(&cntrlObjItemId, (const char*)cntrlObjItemIdBuf.p, cntrlObjItemIdBuf.pos);

    //Получаем domainId
    if(!IEDEntity_getDomainId(controlObject, &cntrlObjDomainId ))
    {
        return false;
    }

    BufferView_init(&wrBuf, cmdTermDataBuf, sizeof(cmdTermDataBuf), 0);

    if(!InfoReport_createNegativeCmdTermReport(controlObject, &wrBuf,
                                               cntrlObjDomainId, &cntrlObjItemId, addCause))
    {
        return false;
    }
    InfoReport_send(isoConn, wrBuf.p, wrBuf.pos, cmdTermMmsBuf,
                    cmdTermPresentationBuf);
    return true;
}
