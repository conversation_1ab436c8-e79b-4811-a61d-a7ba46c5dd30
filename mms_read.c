#include "mms_read.h"
#include "AsnEncoding.h"
#include "mms_data.h"
#include "mmsservices.h"
#include "iedmodel.h"
#include "iedTree/DataSet.h"
#include "iedTree/iedTree.h"
#include "iedTree/iedEntity.h"
#include "iedTree/iedNoEntity.h"
#include "ObjectNameParser.h"
#include "DataSlice.h"
#include "mms_error.h"
#include "bufViewMMS.h"
#include <debug.h>
#include <string.h>
#include <stddef.h>

// objectName должен содержать только имя объекта
// и обязательно с позиции 0
static bool encodeReadDataSetResponse(uint32_t invokeId, IEDEntity dataSet,
	BufferView *objectName, BufferView *outBuf)
{
	bool result;
	size_t readDataSize;
	size_t readDataSizeWithTL;
	int objectSpecSize;
	int objectSpecRespSize;
	int readResponseSize;
	int invokeIdSize;
	int confirmedResponseContentSize;

	dataSliceCapture();

	//===============Получаем размеры===================

	//Размер результатов чтения

	if(!DataSet_calcReadLen(dataSet, &readDataSize))
	{
		ERROR_REPORT("Unable to determine dataset read size")	;		
		return false;
	}

	//Размер результатов чтения с тэгом и длиной
	readDataSizeWithTL = BerEncoder_determineFullObjectSize(readDataSize);

	//Размер спецификации объекта ( А1)
	objectSpecSize = BerEncoder_determineFullObjectSize(objectName->len);

	//Весь А0
	objectSpecRespSize = BerEncoder_determineFullObjectSize(objectSpecSize);

	//Размер размер ответа на чтение (A4)
	readResponseSize = BerEncoder_determineFullObjectSize(
		readDataSizeWithTL + objectSpecRespSize);

	//Размер invokeId
	invokeIdSize = BerEncoder_UInt32determineEncodedSize(invokeId) + 2;

	//Полный размер ответа (для A1)
	confirmedResponseContentSize = invokeIdSize + readResponseSize;

	//================Кодируем=====================

	// confirmed response PDU
	BufferView_encodeTL(outBuf, 0xA1, confirmedResponseContentSize );

	// invoke id	
	BufferView_encodeUInt32(outBuf, ASN_INTEGER, invokeId);

	// confirmed-service-response read	
	BufferView_encodeTL(outBuf, 0xA4, objectSpecRespSize + readDataSizeWithTL);

	//A0	
	BufferView_encodeTL(outBuf, 0xA0,objectSpecSize);
		
	//Гарантируем, что запишем полное имя объекта
	objectName->pos = objectName->len;

	//Object name
	BufferView_encodeBufferView(outBuf, 0xA1, objectName);	

	// encode list of access results TL		
	BufferView_encodeTL(outBuf, 0xA1, readDataSize);

	// encode access results	
	result = DataSet_encodeRead(dataSet, outBuf);

	dataSliceRelease();	
	return result;

}


static bool encodeReadResponse(unsigned int invokeId, IEDEntity* objList,
                               BufferView *outBuf)
{    
    size_t accessResultSize = 0;
    size_t varAccessSpecSize = 0;
    size_t listOfAccessResultsLength;
    size_t confirmedServiceResponseContentLength;
    size_t confirmedServiceResponseLength;
    size_t invokeIdSize;
    size_t confirmedResponseContentSize;
    size_t objIndex;
	IEDEntity entity;

    dataSliceCapture();

    //==============determine BER encoded message sizes==============	

    //Общий размер всех значений
	accessResultSize = 0;
    for(objIndex = 0; objList[objIndex] != NULL; ++objIndex)
	{
        size_t objReadLen;			
		entity = objList[objIndex];
		if(!entity->calcReadLen(entity,  &objReadLen))
        {			
            return 0;
        }
        accessResultSize += objReadLen;
	}

    listOfAccessResultsLength = 1 +
                BerEncoder_determineLengthSize(accessResultSize) +
                accessResultSize;

    confirmedServiceResponseContentLength = listOfAccessResultsLength + varAccessSpecSize;

    confirmedServiceResponseLength = 1 +
            BerEncoder_determineLengthSize(confirmedServiceResponseContentLength) +
            confirmedServiceResponseContentLength;

    invokeIdSize = BerEncoder_UInt32determineEncodedSize(invokeId) + 2;

    confirmedResponseContentSize = confirmedServiceResponseLength + invokeIdSize;

    //================ encode message ============================
    // confirmed response PDU
    if(!BufferView_encodeTL(outBuf, 0xA1, confirmedResponseContentSize))
    {		
        return false;
    }

    // invoke id
    if(!BufferView_encodeUInt32(outBuf, ASN_INTEGER, invokeId))
    {		
        return false;
    }

    // confirmed-service-response read
    if(!BufferView_encodeTL(outBuf, 0xA4, confirmedServiceResponseContentLength))
    {		
        return false;
    }

    // encode variable access specification
    //if (accessSpec != NULL)
    //    bufPos = encodeVariableAccessSpecification(accessSpec, outBuf, bufPos, true);

    // encode list of access results    
    if(!BufferView_encodeTL(outBuf, 0xA1, accessResultSize))
	{
        return false;
    }

    // encode access results            
    for(objIndex = 0; objList[objIndex] != NULL; ++objIndex)
	{
		entity = objList[objIndex];
		if(!entity->encodeRead(entity, outBuf))
        {			
            return false;
        }
	}

    dataSliceRelease();	
    return true;
}

/*
static int handleReadNamedVariableListRequestOld(unsigned int invokeId,
	uint8_t* pObjectName, int objectNameLen,	unsigned char* outBuf)
{
	StringView domainId;
	StringView itemId;
	int dataSetPos;
	int bufPos;

	bufPos = BerDecoder_DecodeObjectNameToStringView(pObjectName, 0, 
		objectNameLen, &domainId, &itemId);
	RET_IF_NOT(bufPos > 1, "Unable to decode dataset name");

	dataSetPos = findObjectByFullName(IED_VMD_DATA_SET_SECTION,
		&domainId, &itemId);
	RET_IF_NOT(dataSetPos, "DataSet is not found");

	return encodeReadDataSetResponse(invokeId, dataSetPos,
		pObjectName, objectNameLen, outBuf);
}
*/

// Чтение DataSet по имени
// objectName должен содержать только имя объекта
// и обязательно с позиции 0.
static bool handleReadNamedVariableListRequest(unsigned int invokeId,
											   BufferView *objectName, BufferView *outBuf)
{
	StringView domainId;
	StringView itemId;
	IEDEntity dataSetEntity;

	//Гарантируем, что pos будет указывать на имя объекта для декодирования
	objectName->pos = 0;
	if(!BufView_decodeObjectName(objectName, &domainId, &itemId))
	{
		ERROR_REPORT("Unable to decode dataset name")	;
		return false;
	}	

	dataSetEntity =
			IEDTree_findDataSetByFullName(&domainId, &itemId);

	if(dataSetEntity == NULL)
	{
		ERROR_REPORT("Unable to find dataset");
		return false;
	}

	return encodeReadDataSetResponse(invokeId, dataSetEntity, objectName,outBuf);
}

//Заполняет список объектов для чтения
//После последнего объекта записывается NULL
static bool fillReadObjList(BufferView *varSpecList, IEDEntity* objList)
{        
    IEDEntity object;       
    size_t objIndex = 0;

    while (!BufferView_endOfBuf(varSpecList))
    {
        object = ObjectNameParser_parse(varSpecList);
        if(object == NULL)
        {
            ERROR_REPORT("Unable to parse object name");
            return false;
        }
        
        objList[objIndex++] = object;
        if(objIndex >= MAX_READ_VARIABLE_OBJECT_LIST)
        {
            ERROR_REPORT("Too many objects in the list");
            return false;
        }                
    }
    objList[objIndex] = 0;
    return true;
}

int mms_handleReadRequest(MmsConnection* mmsConn,
        unsigned char* inBuf, int bufPos, int maxBufPos, unsigned int invokeId,
        unsigned char* response, size_t maxRespSize)
{   	    
    int result;
	BufferView objectName;
    //Чтение dataset
    bool namedVariableListRequest = FALSE;
    unsigned char	tag;
    int				iLength;    
    BufferView specListBer;
	BufferView responseBuf;
	BufferView_init(&responseBuf, response, maxRespSize, 0);
    //Список объектов для чтения пустой
    mmsConn->readVarObjList[0] = NULL;

    while (bufPos < maxBufPos)
    {

        tag = inBuf[bufPos++];

        bufPos = BerDecoder_DecodeLengthOld(inBuf, bufPos, maxBufPos, &iLength);

        if (bufPos < 0)
        {
            return 0;
        }

        switch (tag)
        {
            case MMS_READ_SPECIFICATION_WITH_RESULT:
                if (iLength != 1)
                {
                    return 0;
                }
                //Пропускаем specification
                bufPos++;
                break;
            case MMS_READ_VARIABLE_ACCESS_SPECIFICATION:
                tag = inBuf[bufPos++];
                bufPos = BerDecoder_DecodeLengthOld(inBuf, bufPos,
                    maxBufPos, &iLength);
                if (bufPos < 0)
                {
                    return 0;
                }
                switch (tag)
                {
				case MMS_READ_VARIABLE_LIST_NAME://DataSet										
					//StringView_init(&objectName, inBuf + bufPos, iLength);
					BufferView_init(&objectName, inBuf + bufPos, iLength, 0);

					bufPos += iLength;
					namedVariableListRequest = TRUE;
					break;
                case MMS_READ_LIST_OF_VARIABLE:
                    BufferView_init(&specListBer, inBuf, bufPos + iLength,  bufPos);
                    if(!fillReadObjList(&specListBer,mmsConn->readVarObjList))
                    {
                        return CreateMmsConfirmedErrorPdu(invokeId, response,
                            MMS_ERROR_ACCESS_OBJECT_NON_EXISTENT);
                    }
                    bufPos = specListBer.pos;
					break;
                default:
                    return 0;
                }
                break;
            default:
                return 0;
        }
    }


	IEDTree_lock();

	if (namedVariableListRequest)
	{		
		

		//Чтение DataSet
		if(!handleReadNamedVariableListRequest(invokeId, &objectName, &responseBuf))
		{
			ERROR_REPORT("handleReadNamedVariableListRequest error");
			result = 0;
		}
		else
		{			
			result = responseBuf.pos;
		}
	}
	else
	{        
		//Чтение переменных по списку
		if(!encodeReadResponse(invokeId, mmsConn->readVarObjList, &responseBuf))
        {
			ERROR_REPORT("encodeReadResponse error");
            result = 0;
        }
        else
        {
            result = responseBuf.pos;
        }
	}
	IEDTree_unlock();

	if(result < 1)
	{
		return CreateMmsConfirmedErrorPdu(invokeId, response, 
			MMS_ERROR_ACCESS_OBJECT_NON_EXISTENT);
	}	
		
	//TODO Обработать случай когда domainIdStr и itemIdStr неинициализированы
	return result;
}
