                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_93c1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=timetools.c -o gh_93c1.o -list=timetools.lst C:\Users\<USER>\AppData\Local\Temp\gh_93c1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_93c1.s
Source File: timetools.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile timetools.c -o

                      10 ;		timetools.o

                      11 ;Source File:   timetools.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:55:58 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #pragma once


                      21 ;2: #include "timetools.h"


                      22 ;3: 


                      23 ;4: bool TimeTools_gmtime32(struct tm* tmDest, const __time32_t* sourceTime)


                      24 	.text

                      25 	.align	4

                      26 TimeTools_gmtime32::

00000000 e92d4000     27 	stmfd	[sp]!,{lr}

00000004 e1a02001     28 	mov	r2,r1

                      29 ;5: {


                      30 

                      31 ;6: 	__gmtime(*sourceTime,tmDest);


                      32 

00000008 e1a01000     33 	mov	r1,r0

0000000c e5920000     34 	ldr	r0,[r2]

00000010 eb000000*    35 	bl	__gmtime

                      36 ;7: 	return true;


                      37 

00000014 e3a00001     38 	mov	r0,1

00000018 e8bd8000     39 	ldmfd	[sp]!,{pc}

                      40 	.endf	TimeTools_gmtime32

                      41 	.align	4

                      42 

                      43 ;tmDest	r0	param

                      44 ;sourceTime	r2	param

                      45 

                      46 	.section ".bss","awb"

                      47 .L30:

                      48 	.data

                      49 	.text

                      50 


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_93c1.s
                      51 ;8: }


                      52 

                      53 ;9: 


                      54 ;10: bool TimeTools_localtime32(tm *tmDest, const __time32_t *sourceTime)


                      55 	.align	4

                      56 	.align	4

                      57 TimeTools_localtime32::

0000001c e92d4000     58 	stmfd	[sp]!,{lr}

00000020 e1a02001     59 	mov	r2,r1

                      60 ;11: {


                      61 

                      62 ;12: 	__localtime(*sourceTime,tmDest);


                      63 

00000024 e1a01000     64 	mov	r1,r0

00000028 e5920000     65 	ldr	r0,[r2]

0000002c eb000000*    66 	bl	__localtime

                      67 ;13: 	return true;


                      68 

00000030 e3a00001     69 	mov	r0,1

00000034 e8bd8000     70 	ldmfd	[sp]!,{pc}

                      71 	.endf	TimeTools_localtime32

                      72 	.align	4

                      73 

                      74 ;tmDest	r0	param

                      75 ;sourceTime	r2	param

                      76 

                      77 	.section ".bss","awb"

                      78 .L62:

                      79 	.data

                      80 	.text

                      81 

                      82 ;14: }


                      83 	.align	4

                      84 

                      85 	.data

                      86 	.ghsnote version,6

                      87 	.ghsnote tools,3

                      88 	.ghsnote options,0

                      89 	.text

                      90 	.align	4

