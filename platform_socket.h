#pragma once


#include <platform_socket_def.h>
#include <stdbool.h>

typedef void* SERVER_SOCKET;

bool acceptConnection(SOCKET* pConnSocket, struct sockaddr*addr, int *addrlen);

bool socketInit(void);

//! Читает указанное число байтов из сокета.
//! Возвращает 1 если успешно и 0 при ошибке
int readSocket(SERVER_SOCKET socket, void* buf, int byteCount);

int writeSocket(SERVER_SOCKET socket, void* buf, int byteCount);

int startListening(void);
