#pragma once

#include "..\trgOps.h"
#include "..\stringView.h"
#include "..\bufViewBER.h"
#include "..\MmsConst.h"
#include "..\IsoConnectionForward.h"

#include <stddef.h>
#include <stdbool.h>

typedef struct IEDEntityStruct* IEDEntity;

typedef enum
{
    IED_ENTITY_NONE = 0,
    IED_ENTITY_LD,
    IED_ENTITY_DATA_SECTION,
    IED_ENTITY_LN,
    IED_ENTITY_FC,
    IED_ENTITY_DO,
    IED_ENTITY_DA,
    IED_ENTITY_DA_CONST,
    IED_ENTITY_DA_TERMINAL_ITEM,
    IED_ENTITY_DA_TIMESTAMP,
    //! Final DA, который просто хранит записанные в него данные
    IED_ENTITY_DA_VAR,
    IED_ENTITY_DATASET
}IEDEntityType;

//! Тип функции для чтения и кодирования данных в буфер
typedef bool (*IEDEntity_EncodeRead)(
        IEDEntity entity, BufferView* outBuf);

//! Тип функции для определения размера данных при кодировании
typedef bool (*IEDEntity_СalcReadLen)(
        IEDEntity entity, size_t* pLen );

//! Тип функции для записи данных из буфера
typedef MmsDataAccessError (*IEDEntity_Write)(IEDEntity entity, 
    IsoConnection* isoConn, BufferView* value);

//! Тип функции, которая вызывается при получении нового DataSlice
//! Имеет смысл только для TerminalItemDA.
//! Ожидается, что функция получит новые данные из DataSlice,
//! и если они изменились, обновит внутренние данные DA и TimeStamp.
//! Также функция должна установить состояние результатов сравнения
//! (есть/нет изменения)
typedef void (*IEDEntity_UpdateFromDataSlice)(IEDEntity da);

typedef struct {
    uint64_t timeStamp;
} TimeStamp ;


struct IEDEntityStruct {
    IEDEntity parent;
    IEDEntity firstChild;
    IEDEntity lastChild;
    //! Следующий элемент в списке
    IEDEntity next;

    //! Следующий элемент в списке сравнения
    IEDEntity nextCompare;

    //! Представление объекта в виде BER, из которого он создан.
    BufferView ber;
    uint8_t tag;

    //! Переменная для отладки. Конкретного назаначения нет.
    //! Можно, например,  по какому-то признаку включить, а потом,
    //! если включено, что-то выводить,
    bool debug;

    bool readOnly;

    //! Используется при обнаружении изменений.
    //! Имеет смысл только для Final DA.
    //! Может содержать только те trgOps, которые требуют сравнения
    //! старого и нового значения
    TrgOps trgOps;

    //! Результат сравнения
    TrgOps changed;

    //! Смещение в DataSlice для Final DA.
    //! Может быть невалидно (-1)
    //! Для других объектов не имеет смысла.
    int dataSliceOffset;

    //! Текущее значение для Final DA.
    //! Для других объектов не имеет смысла.
    union {
        bool boolValue;
        uint16_t qualityValue;
        uint8_t codedEnumValue;
        float realValue;
        //! Для элементов блока в формате 16.16 или 32.0
        int fixedValue;
        int32_t intValue;
        uint32_t uintValue;
    };

    //! Для элементов, использующих кэшировние, состояние кэша.
    //! Сбрасывается при обновлении значения элемента.
    bool cached;

    //! Для элементов, использующих кэшировние вычисленной длины
    //! закодированного в BER значения
    size_t cachedBERLen;

    union {
        int32_t enumValue;
        int64_t int64Value;
    } cache;



    TimeStamp* timeStamp;

    StringView name;
    IEDEntityType type;
    // Сюда обычно кладётся 
    // Для FC - IEDFCType
    // Для Final DA - InnerAttributeType
    // Для DO - DOSubType    
    int subType;
    void* extInfo;

    //=== Указатели на функции==========
    IEDEntity_EncodeRead encodeRead;
    IEDEntity_СalcReadLen calcReadLen;
    IEDEntity_Write write;
    IEDEntity_UpdateFromDataSlice updateFromDataSlice;
};


void* IEDEntity_alloc(size_t size);
bool IEDEntity_createFromBER(IEDEntity* iedEntity, BufferView* ber,
                             IEDEntity parent);

//! Дополнительная инициализация элементов, требующих
//! Наличия всего дерева. Например, инициализация ссылок DataSet.
//! Вызывается для  каждого элемента после создания всего дерева.
bool IEDEntity_postCreate(IEDEntity entity);

//! Находит непосредственный child по имени в виде StringView
IEDEntity IEDEntity_getChildByName(IEDEntity parent, StringView* name);
//! Находит непосредственный child по имени в виде c-string
IEDEntity IEDEntity_getChildByCStrName(IEDEntity parent, const char* name);

//! Возвращает child по имени вида "xxx$yyy$zzzz"
IEDEntity IEDEntity_getChildByFullName(IEDEntity parent,  StringView* name);

//! Возвращает первый child с подходящим тэгом
IEDEntity IEDEntity_getChildByTag(IEDEntity parent,  uint8_t tag);

MmsDataAccessError IEDEntity_write(IEDEntity entity, IsoConnection* isoConn,
                                   BufferView* value);

bool IEDEntity_getFullName(IEDEntity entity, BufferView* nameBuf);

bool IEDEntity_getFullItemId(IEDEntity entity, BufferView* nameBuf);

bool IEDEntity_getDomainId(IEDEntity entity, StringView** name);

// Устанавливает указатель на TimeStamp для объекта
// и всех его вложенных подобъектов рекурсивно,
// исключая объекты IED_ENTITY_DA_TIMESTAMP,
void IEDEntity_attachTimeStamp(IEDEntity entity, TimeStamp* timeStamp);

//! Функция для отладки. Пишет в BufferView полное имя (с родителями)
//! Функция рекурсивная, но стек использует только для аргументов.
void IEDEntity_writeFullName(IEDEntity entity, BufferView* bv );

//! Функция для отладки.
void IEDEntity_printFullName(IEDEntity entity);

//! Записывает время в присоединённый к entity TimeStamp
//! Если TimeStamp не присоединён, не делает ничего
void IEDEntity_setTimeStamp(IEDEntity entity, uint64_t timeStamp);

//! Обходит рекурсивно entity, находит, есть ли подходщие флаги изменений
//! и возвращает их.
//! trgOps - маска подходящих флагов изменений
TrgOps IEDEntity_findChanges(IEDEntity entity, TrgOps trgOps);

//! Устанавливает флаг readOnly для entity и всех его детей рекурсивно
void IEDEntity_setReadOnlyRecursive(IEDEntity entity, bool readOnly);

//! Возвращает true, если entity - FinalDA
bool IEDEntity_isFinalDA(IEDEntity entity);
