                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g6k1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=mms_fs.c -o gh_g6k1.o -list=mms_fs.lst C:\Users\<USER>\AppData\Local\Temp\gh_g6k1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_g6k1.s
Source File: mms_fs.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile mms_fs.c -o

                      10 ;		mms_fs.o

                      11 ;Source File:   mms_fs.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:56:03 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "mms_fs.h"


                      21 ;2: #include "stringView.h"


                      22 ;3: 


                      23 ;4: #include "BaseAsnTypes.h"


                      24 ;5: #include "AsnEncoding.h"


                      25 ;6: #include "file_system.h"


                      26 ;7: #include "tools.h"


                      27 ;8: #include "local_types.h"


                      28 ;9: #include "bufViewBER.h"


                      29 ;10: #include <stddef.h>


                      30 ;11: #include <stdio.h>


                      31 ;12: #include "timetools.h"


                      32 ;13: //Файловые операции MMS


                      33 ;14: 


                      34 ;15: #define MMS_DIRECTORY_NAME 0xA0


                      35 ;16: #define MMS_FILE_NAME 0xA1


                      36 ;17: 


                      37 ;18: //GeneralizedTime


                      38 ;19: // год, месяц(1 - 12), число(1 - 32), часы, минуты, cекунды, точка, доли секунды, Z


                      39 ;20: // strlen("19851106210627.123Z") + 1 = 20


                      40 ;21: #define GENERZLISED_TIME_SIZE	(sizeof(GeneralizedTime_t) -1 ) // размер без '0'


                      41 ;22: typedef char GeneralizedTime_t[20];


                      42 ;23: 


                      43 ;24: 


                      44 ;25: // Информация о длине закодированных атрибутов файла


                      45 ;26: typedef struct {


                      46 ;27:     size_t timeLen;


                      47 ;28:     size_t sizeLen;


                      48 ;29:     //Полный размер закодированных атрибутов файла включая тэг и длину


                      49 ;30:     size_t fullAttrLen;//A1


                      50 ;31: }  EncodedAttrLen;



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g6k1.s
                      51 ;32: 


                      52 ;33: // Информация для записи заголовка ответа на файловую операцию


                      53 ;34: typedef struct {


                      54 ;35:     uint32_t invokeID;


                      55 ;36:     //Двухбайтный тэг big endian


                      56 ;37:     uint16_t extTag;


                      57 ;38:     //Размер всего ответа внутри тэга A1, включая invokeID


                      58 ;39:     size_t responseLen;


                      59 ;40:     //Размер данных внутри расширенного тэга (BF 4X)


                      60 ;41:     size_t dataLen;


                      61 ;42: } FileRespHeadInfo;


                      62 ;43: 


                      63 ;44: 


                      64 ;45: 


                      65 ;46: static void determineEncodedAttrLen(EncodedAttrLen* encAttrLen, size_t fileSize)


                      66 ;47: {


                      67 ;48:     encAttrLen->timeLen = 2 //тэг + длина


                      68 ;49:         + GENERZLISED_TIME_SIZE;


                      69 ;50:     encAttrLen->sizeLen = 2 //тэг + длина


                      70 ;51:         + BerEncoder_UInt32determineEncodedSize(fileSize);


                      71 ;52:     encAttrLen->fullAttrLen = BerEncoder_determineFullObjectSize(


                      72 ;53:         encAttrLen->sizeLen + encAttrLen->timeLen);


                      73 ;54: }


                      74 ;55: 


                      75 ;56: // Определяет размер ответа на операцию с файлом,


                      76 ;57: // включая размер данных, двухбайтный тэг, invokeID.


                      77 ;58: // Исключая корневой тэг(A1) и его размер.


                      78 ;59: static size_t determineResponseLen(uint32_t invokeID, size_t responseDataLen)


                      79 ;60: {


                      80 ;61:     return BerEncoder_UInt32determineEncodedSize(invokeID)


                      81 ;62:         + 2 //Тэг и длина InvokeID


                      82 ;63:         + BerEncoder_determineFullObjectSize(responseDataLen)


                      83 ;64:         + 1; //Ещё один байт поскольку тэг двухбайтовый (BF 4D)


                      84 ;65: }


                      85 ;66: 


                      86 ;67: static bool makeGeneralizedTime(FSFileAttr* attr, GeneralizedTime_t generalizedTime)


                      87 

                      88 ;85: }


                      89 

                      90 ;86: static bool encodeFileAttr(BufferView* outBuf, EncodedAttrLen* encAttrLen,


                      91 ;87:     FSFileAttr* attr)


                      92 ;88: {


                      93 ;89:     GeneralizedTime_t generalizedTime;


                      94 ;90:     //Атрибуты


                      95 ;91:     if (!BufferView_encodeTL(outBuf, 0xA1,


                      96 ;92:         encAttrLen->sizeLen + encAttrLen->timeLen))


                      97 ;93:     {


                      98 ;94:         return FALSE;


                      99 ;95:     }


                     100 ;96:     //Размер


                     101 ;97:     if (!BufferView_encodeUInt32(outBuf, 0x80, attr->fileSize))


                     102 ;98:     {


                     103 ;99:         return FALSE;


                     104 ;100:     }


                     105 ;101: 


                     106 ;102:     //Время


                     107 ;103:     if (!makeGeneralizedTime(attr, generalizedTime))


                     108 ;104:     {


                     109 ;105:         return FALSE;


                     110 ;106:     }


                     111 ;107: 



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g6k1.s
                     112 ;108:     if (!BufferView_encodeOctetString(outBuf, 0x81, generalizedTime,


                     113 ;109:         GENERZLISED_TIME_SIZE))


                     114 ;110:     {


                     115 ;111:         return FALSE;


                     116 ;112:     }


                     117 ;113:     return TRUE;


                     118 ;114: }


                     119 ;115: 


                     120 ;116: static bool encodeFileInfo(FSFindData* fileInfo, BufferView* outBuf)


                     121 

                     122 ;164: }


                     123 

                     124 ;165: 


                     125 ;166: static bool encodeFileResponseHead(BufferView* outBuf,


                     126 ;167:     FileRespHeadInfo* headerInfo)


                     127 ;168: {


                     128 ;169:     if (!BufferView_encodeTL(outBuf, 0xA1, headerInfo->responseLen))


                     129 ;170:     {


                     130 ;171:         return FALSE;


                     131 ;172:     }


                     132 ;173:     //InvokeID


                     133 ;174:     if (!BufferView_encodeUInt32(outBuf, ASN_INTEGER, headerInfo->invokeID))


                     134 ;175:     {


                     135 ;176:         return FALSE;


                     136 ;177:     }


                     137 ;178:     if (!BufferView_encodeExtTL(outBuf, headerInfo->extTag,


                     138 ;179:         headerInfo->dataLen))


                     139 ;180:     {


                     140 ;181:         return FALSE;


                     141 ;182:     }


                     142 ;183:     return TRUE;


                     143 ;184: }


                     144 ;185: 


                     145 ;186: //


                     146 ;187: static bool fileList(MmsConnection* mmsConn, StringView* dirName,


                     147 

                     148 ;235: }


                     149 

                     150 ;236: 


                     151 ;237: // Создаёт ответ на запрос списка файлов


                     152 ;238: static bool encodeFileDirRequest(MmsConnection* mmsConn, uint32_t invokeID,


                     153 

                     154 ;303: }


                     155 

                     156 ;304: 


                     157 ;305: bool mms_handleFileDirRequest(MmsConnection* mmsConn, BufferView* inBuf,


                     158 ;306:     unsigned int invokeId, BufferView* outBuf)


                     159 ;307: {


                     160 ;308:     uint8_t tag;


                     161 ;309:     int length;


                     162 ;310:     StringView dir;


                     163 ;311:     StringView startFile;


                     164 ;312:     bool thereIsStartFile = FALSE;


                     165 ;313:     StringView_fromCStr(&dir, "/");


                     166 ;314: 


                     167 ;315:     while (inBuf->pos < inBuf->len)


                     168 ;316:     {


                     169 ;317:         bool result = BerDecoder_decodeTLFromBufferView(inBuf, &tag, &length, NULL);


                     170 ;318:         RET_IF_NOT(result, "Error reading tag and length");


                     171 ;319:         switch (tag) {


                     172 ;320:         case MMS_DIRECTORY_NAME:



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g6k1.s
                     173 ;321:             result = BerDecoder_decodeTLFromBufferView(inBuf, &tag, &length, NULL);


                     174 ;322:             RET_IF_NOT(result, "Error reading tag and length");


                     175 ;323:             RET_IF_NOT(tag == ASN_GRAPHIC_STRING, "Unexpected tag");


                     176 ;324:             StringView_init(&dir, (char*)inBuf->p + inBuf->pos, length);


                     177 ;325:             inBuf->pos += length;


                     178 ;326:             break;


                     179 ;327:         case MMS_FILE_NAME:


                     180 ;328:             result = BerDecoder_decodeTLFromBufferView(inBuf, &tag, &length, NULL);


                     181 ;329:             RET_IF_NOT(result, "Error reading tag and length");


                     182 ;330:             RET_IF_NOT(tag == ASN_GRAPHIC_STRING, "Unexpected tag");


                     183 ;331:             StringView_init(&startFile, (char*)inBuf->p + inBuf->pos, length);


                     184 ;332:             inBuf->pos += length;


                     185 ;333:             thereIsStartFile = TRUE;


                     186 ;334:             break;


                     187 ;335:         default:


                     188 ;336:             ERROR_REPORT("Unexpectded tag");


                     189 ;337:             return FALSE;


                     190 ;338:         }


                     191 ;339:     }


                     192 ;340:     RET_IF_NOT(dir.p != NULL, "Directory name is not found");


                     193 ;341: 


                     194 ;342:     //dir содержит путь к запрошенному директорию


                     195 ;343:     return encodeFileDirRequest(mmsConn, invokeId, &dir,


                     196 ;344:         thereIsStartFile? &startFile: NULL,


                     197 ;345:         outBuf);


                     198 ;346: }


                     199 ;347: 


                     200 ;348: 


                     201 ;349: static bool encodeFileOpenRequest(MmsConnection* mmsConn, uint32_t invokeID,


                     202 

                     203 ;402: }


                     204 

                     205 ;403: 


                     206 ;404: bool mms_handleFileOpenRequest(MmsConnection* mmsConn, BufferView* inBuf,


                     207 ;405:     unsigned int invokeId, BufferView* outBuf)


                     208 ;406: {


                     209 ;407:     /*


                     210 ;408:     A0


                     211 ;409:         19 Имя файла


                     212 ;410:     81 Начальная позиция


                     213 ;411:     */


                     214 ;412:     uint8_t tag;


                     215 ;413:     int length;


                     216 ;414:     StringView fileName;


                     217 ;415:     size_t startPos;


                     218 ;416: 


                     219 ;417:     bool result = BerDecoder_decodeTLFromBufferView(inBuf, &tag, &length, NULL);


                     220 ;418:     RET_IF_NOT(result, "Error reading tag and length");


                     221 ;419:     RET_IF_NOT(tag == 0xA0, "Unexpected tag");


                     222 ;420:     //Имя файла


                     223 ;421:     result = BerDecoder_decodeTLFromBufferView(inBuf, &tag, &length, NULL);


                     224 ;422:     RET_IF_NOT(result, "Error reading file name tag and length");


                     225 ;423:     RET_IF_NOT(tag == ASN_GRAPHIC_STRING, "Unexpected tag");


                     226 ;424:     //Создаём StringView на имя файла


                     227 ;425:     result = BufferView_readStringView(inBuf, length, &fileName);


                     228 ;426:     RET_IF_NOT(result, "Error reading file Name");


                     229 ;427:     //Начальная позиция


                     230 ;428:     result = BerDecoder_decodeTLFromBufferView(inBuf, &tag, &length, NULL);


                     231 ;429:     RET_IF_NOT(result, "Error reading position tag and length");


                     232 ;430:     result = BufferView_decodeUInt32(inBuf, length, (uint32_t*)&startPos);


                     233 ;431:     RET_IF_NOT(result, "Error reading start position");



                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g6k1.s
                     234 ;432: 


                     235 ;433:     return encodeFileOpenRequest(mmsConn, invokeId,


                     236 ;434:         &fileName, startPos, outBuf);


                     237 ;435: }


                     238 ;436: 


                     239 ;437: 


                     240 ;438: static bool encodeFileReadRequest(MmsConnection* mmsConn, uint32_t invokeID,


                     241 

                     242 ;483: }


                     243 

                     244 ;484: 


                     245 ;485: bool mms_handleFileReadRequest(MmsConnection* mmsConn, BufferView* inBuf,


                     246 ;486:     unsigned int invokeID, BufferView* outBuf)


                     247 ;487: {


                     248 ;488:     /*


                     249 ;489:         inBuf содержит только frsmID


                     250 ;490:     */


                     251 ;491:     uint32_t frsmID;


                     252 ;492:     bool result = BufferView_decodeUInt32(inBuf, inBuf->len, &frsmID);


                     253 ;493:     RET_IF_NOT(result, "Error reading FRSM ID");


                     254 ;494:     return encodeFileReadRequest(mmsConn, invokeID, frsmID, outBuf);


                     255 ;495: }


                     256 ;496: 


                     257 ;497: static bool encodeFileCloseRequest(MmsConnection* mmsConn, uint32_t invokeID,


                     258 

                     259 ;519: }


                     260 

                     261 	.text

                     262 	.align	4

                     263 determineEncodedAttrLen:

00000000 e92d4010    264 	stmfd	[sp]!,{r4,lr}

00000004 e1a04000    265 	mov	r4,r0

00000008 e3a00015    266 	mov	r0,21

0000000c e5840000    267 	str	r0,[r4]

00000010 e1a00001    268 	mov	r0,r1

00000014 eb000000*   269 	bl	BerEncoder_UInt32determineEncodedSize

00000018 e2800002    270 	add	r0,r0,2

0000001c e5941000    271 	ldr	r1,[r4]

00000020 e5840004    272 	str	r0,[r4,4]

00000024 e0800001    273 	add	r0,r0,r1

00000028 eb000000*   274 	bl	BerEncoder_determineFullObjectSize

0000002c e5840008    275 	str	r0,[r4,8]

00000030 e8bd4010    276 	ldmfd	[sp]!,{r4,lr}

00000034 e12fff1e*   277 	ret	

                     278 	.endf	determineEncodedAttrLen

                     279 	.align	4

                     280 

                     281 ;encAttrLen	r4	param

                     282 ;fileSize	r1	param

                     283 

                     284 	.section ".bss","awb"

                     285 .L334:

                     286 	.data

                     287 	.text

                     288 

                     289 

                     290 	.align	4

                     291 	.align	4

                     292 determineResponseLen:

00000038 e92d4030    293 	stmfd	[sp]!,{r4-r5,lr}

0000003c e1a04001    294 	mov	r4,r1


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g6k1.s
00000040 eb000000*   295 	bl	BerEncoder_UInt32determineEncodedSize

00000044 e1a05000    296 	mov	r5,r0

00000048 e1a00004    297 	mov	r0,r4

0000004c eb000000*   298 	bl	BerEncoder_determineFullObjectSize

00000050 e0800005    299 	add	r0,r0,r5

00000054 e2800003    300 	add	r0,r0,3

00000058 e8bd4030    301 	ldmfd	[sp]!,{r4-r5,lr}

0000005c e12fff1e*   302 	ret	

                     303 	.endf	determineResponseLen

                     304 	.align	4

                     305 

                     306 ;invokeID	none	param

                     307 ;responseDataLen	r4	param

                     308 

                     309 	.section ".bss","awb"

                     310 .L366:

                     311 	.data

                     312 	.text

                     313 

                     314 

                     315 	.align	4

                     316 	.align	4

                     317 	.align	4

                     318 encodeFileAttr:

00000060 e92d4030    319 	stmfd	[sp]!,{r4-r5,lr}

00000064 e24dd054    320 	sub	sp,sp,84

00000068 e1a04002    321 	mov	r4,r2

0000006c e891000c    322 	ldmfd	[r1],{r2-r3}

00000070 e1a05000    323 	mov	r5,r0

00000074 e0822003    324 	add	r2,r2,r3

00000078 e3a010a1    325 	mov	r1,161

0000007c eb000000*   326 	bl	BufferView_encodeTL

00000080 e3500000    327 	cmp	r0,0

00000084 0a000025    328 	beq	.L391

00000088 e5942000    329 	ldr	r2,[r4]

0000008c e1a00005    330 	mov	r0,r5

00000090 e3a01080    331 	mov	r1,128

00000094 eb000000*   332 	bl	BufferView_encodeUInt32

00000098 e3500000    333 	cmp	r0,0

0000009c 0a00001f    334 	beq	.L391

                     335 ;68: {


                     336 

                     337 ;69:     struct tm tmTime;


                     338 ;70:     __time32_t t = attr->time;


                     339 

000000a0 e5940004    340 	ldr	r0,[r4,4]

000000a4 e28d1018    341 	add	r1,sp,24

000000a8 e58d0018    342 	str	r0,[sp,24]

000000ac e28d001c    343 	add	r0,sp,28

000000b0 eb000000*   344 	bl	TimeTools_gmtime32

                     345 ;71: 


                     346 ;72:     if (!TimeTools_gmtime32(&tmTime, &t))


                     347 

000000b4 e3500000    348 	cmp	r0,0

000000b8 0a000018    349 	beq	.L391

000000bc e28d0028    350 	add	r0,sp,40

000000c0 e8905004    351 	ldmfd	[r0],{r2,r12,lr}

000000c4 e28c0001    352 	add	r0,r12,1

000000c8 e28e1e70    353 	add	r1,lr,7<<8

000000cc e281306c    354 	add	r3,r1,108

000000d0 e58d3030    355 	str	r3,[sp,48]


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g6k1.s
000000d4 e58d002c    356 	str	r0,[sp,44]

000000d8 e594c008    357 	ldr	r12,[r4,8]

000000dc e59d401c    358 	ldr	r4,[sp,28]

000000e0 e59d1020    359 	ldr	r1,[sp,32]

000000e4 e28de00c    360 	add	lr,sp,12

000000e8 e88e1012    361 	stmea	[lr],{r1,r4,r12}

000000ec e59d4024    362 	ldr	r4,[sp,36]

000000f0 e88d0015    363 	stmea	[sp],{r0,r2,r4}

000000f4 e59f2320*   364 	ldr	r2,.L530

000000f8 e28d0040    365 	add	r0,sp,64

000000fc e3a01014    366 	mov	r1,20

00000100 eb000000*   367 	bl	snprintf

                     368 ;73:     {


                     369 

                     370 ;74:         return false;


                     371 

                     372 ;75:     }


                     373 ;76: 


                     374 ;77:     tmTime.tm_year += 1900;


                     375 

                     376 ;78:     tmTime.tm_mon += 1;


                     377 

                     378 ;79:     snprintf(generalizedTime, sizeof(GeneralizedTime_t),


                     379 

                     380 ;80:         "%d%02d%02d%02d%02d%02d.%03dZ", // 19851106210627.300Z


                     381 ;81:         tmTime.tm_year, tmTime.tm_mon, tmTime.tm_mday,


                     382 ;82:         tmTime.tm_hour, tmTime.tm_min, tmTime.tm_sec, attr->ms);


                     383 ;83: 


                     384 ;84:     return true;


                     385 

00000104 e28d2040    386 	add	r2,sp,64

00000108 e1a00005    387 	mov	r0,r5

0000010c e3a03013    388 	mov	r3,19

00000110 e3a01081    389 	mov	r1,129

00000114 eb000000*   390 	bl	BufferView_encodeOctetString

00000118 e3500000    391 	cmp	r0,0

0000011c 13a00001    392 	movne	r0,1

                     393 .L391:

00000120 03a00000    394 	moveq	r0,0

                     395 .L373:

00000124 e28dd054    396 	add	sp,sp,84

00000128 e8bd4030    397 	ldmfd	[sp]!,{r4-r5,lr}

0000012c e12fff1e*   398 	ret	

                     399 	.endf	encodeFileAttr

                     400 	.align	4

                     401 ;generalizedTime	[sp,64]	local

                     402 ;tmTime	[sp,28]	local

                     403 ;t	[sp,24]	local

                     404 

                     405 ;outBuf	r5	param

                     406 ;encAttrLen	r1	param

                     407 ;attr	r4	param

                     408 

                     409 	.section ".bss","awb"

                     410 .L506:

                     411 	.section ".rodata","a"

                     412 .L507:

                     413 __UNNAMED_1_static_in_makeGeneralizedTime:;	"%d%02d%02d%02d%02d%02d.%03dZ\000"

00000000 30256425    414 	.data.b	37,100,37,48

00000004 30256432    415 	.data.b	50,100,37,48

00000008 30256432    416 	.data.b	50,100,37,48


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g6k1.s
0000000c 30256432    417 	.data.b	50,100,37,48

00000010 30256432    418 	.data.b	50,100,37,48

00000014 252e6432    419 	.data.b	50,100,46,37

00000018 5a643330    420 	.data.b	48,51,100,90

0000001c 00         421 	.data.b	0

0000001d 000000     422 	.space	3

                     423 	.type	__UNNAMED_1_static_in_makeGeneralizedTime,$object

                     424 	.size	__UNNAMED_1_static_in_makeGeneralizedTime,32

                     425 	.data

                     426 	.text

                     427 

                     428 

                     429 	.align	4

                     430 	.align	4

                     431 encodeFileResponseHead:

00000130 e92d4030    432 	stmfd	[sp]!,{r4-r5,lr}

00000134 e1a04001    433 	mov	r4,r1

00000138 e5942008    434 	ldr	r2,[r4,8]

0000013c e1a05000    435 	mov	r5,r0

00000140 e3a010a1    436 	mov	r1,161

00000144 eb000000*   437 	bl	BufferView_encodeTL

00000148 e3500000    438 	cmp	r0,0

0000014c 0a00000b    439 	beq	.L540

00000150 e5942000    440 	ldr	r2,[r4]

00000154 e1a00005    441 	mov	r0,r5

00000158 e3a01002    442 	mov	r1,2

0000015c eb000000*   443 	bl	BufferView_encodeUInt32

00000160 e3500000    444 	cmp	r0,0

00000164 0a000005    445 	beq	.L540

00000168 e594200c    446 	ldr	r2,[r4,12]

0000016c e1d410b4    447 	ldrh	r1,[r4,4]

00000170 e1a00005    448 	mov	r0,r5

00000174 eb000000*   449 	bl	BufferView_encodeExtTL

00000178 e3500000    450 	cmp	r0,0

0000017c 13a00001    451 	movne	r0,1

                     452 .L540:

00000180 03a00000    453 	moveq	r0,0

                     454 .L531:

00000184 e8bd4030    455 	ldmfd	[sp]!,{r4-r5,lr}

00000188 e12fff1e*   456 	ret	

                     457 	.endf	encodeFileResponseHead

                     458 	.align	4

                     459 

                     460 ;outBuf	r5	param

                     461 ;headerInfo	r4	param

                     462 

                     463 	.section ".bss","awb"

                     464 .L598:

                     465 	.data

                     466 	.text

                     467 

                     468 

                     469 	.align	4

                     470 	.align	4

                     471 mms_handleFileDirRequest::

0000018c e92d4cf4    472 	stmfd	[sp]!,{r2,r4-r7,r10-fp,lr}

00000190 e3a06000    473 	mov	r6,0

00000194 e24dd06c    474 	sub	sp,sp,108

00000198 e58d206c    475 	str	r2,[sp,108]

0000019c e1a0b003    476 	mov	fp,r3

000001a0 e1a07000    477 	mov	r7,r0


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g6k1.s
000001a4 e1a04001    478 	mov	r4,r1

000001a8 e28f1000*   479 	adr	r1,.L1287

000001ac e28d0064    480 	add	r0,sp,100

000001b0 eb000000*   481 	bl	StringView_fromCStr

000001b4 e28d5004    482 	add	r5,sp,4

000001b8 e9940003    483 	ldmed	[r4],{r0-r1}

000001bc e1500001    484 	cmp	r0,r1

000001c0 2a000038    485 	bhs	.L621

                     486 .L622:

000001c4 e1a02005    487 	mov	r2,r5

000001c8 e28d1003    488 	add	r1,sp,3

000001cc e1a00004    489 	mov	r0,r4

000001d0 e3a03000    490 	mov	r3,0

000001d4 eb000000*   491 	bl	BerDecoder_decodeTLFromBufferView

000001d8 e3500000    492 	cmp	r0,0

000001dc 0a000034    493 	beq	.L645

000001e0 e5dd0003    494 	ldrb	r0,[sp,3]

000001e4 e25000a0    495 	subs	r0,r0,160

000001e8 0a000002    496 	beq	.L628

000001ec e3500001    497 	cmp	r0,1

000001f0 0a000016    498 	beq	.L635

000001f4 ea00002e    499 	b	.L645

                     500 .L628:

000001f8 e1a02005    501 	mov	r2,r5

000001fc e28d1003    502 	add	r1,sp,3

00000200 e1a00004    503 	mov	r0,r4

00000204 e3a03000    504 	mov	r3,0

00000208 eb000000*   505 	bl	BerDecoder_decodeTLFromBufferView

0000020c e3500000    506 	cmp	r0,0

00000210 0a000027    507 	beq	.L645

00000214 e5dd0003    508 	ldrb	r0,[sp,3]

00000218 e3500019    509 	cmp	r0,25

0000021c 1a000024    510 	bne	.L645

00000220 e59d2004    511 	ldr	r2,[sp,4]

00000224 e894000a    512 	ldmfd	[r4],{r1,r3}

00000228 e28d0064    513 	add	r0,sp,100

0000022c e0831001    514 	add	r1,r3,r1

00000230 eb000000*   515 	bl	StringView_init

00000234 e9940005    516 	ldmed	[r4],{r0,r2}

00000238 e59d1004    517 	ldr	r1,[sp,4]

0000023c e0800001    518 	add	r0,r0,r1

00000240 e5840004    519 	str	r0,[r4,4]

00000244 e1500002    520 	cmp	r0,r2

00000248 3affffdd    521 	blo	.L622

0000024c ea000015    522 	b	.L621

                     523 .L635:

00000250 e1a02005    524 	mov	r2,r5

00000254 e28d1003    525 	add	r1,sp,3

00000258 e1a00004    526 	mov	r0,r4

0000025c e3a03000    527 	mov	r3,0

00000260 eb000000*   528 	bl	BerDecoder_decodeTLFromBufferView

00000264 e3500000    529 	cmp	r0,0

00000268 0a000011    530 	beq	.L645

0000026c e5dd0003    531 	ldrb	r0,[sp,3]

00000270 e3500019    532 	cmp	r0,25

00000274 1a00000e    533 	bne	.L645

00000278 e59d2004    534 	ldr	r2,[sp,4]

0000027c e894000a    535 	ldmfd	[r4],{r1,r3}

00000280 e28d005c    536 	add	r0,sp,92

00000284 e0831001    537 	add	r1,r3,r1

00000288 eb000000*   538 	bl	StringView_init


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g6k1.s
0000028c e9940005    539 	ldmed	[r4],{r0,r2}

00000290 e59d1004    540 	ldr	r1,[sp,4]

00000294 e3a06001    541 	mov	r6,1

00000298 e0800001    542 	add	r0,r0,r1

0000029c e5840004    543 	str	r0,[r4,4]

000002a0 e1500002    544 	cmp	r0,r2

000002a4 3affffc6    545 	blo	.L622

                     546 .L621:

000002a8 e59d0068    547 	ldr	r0,[sp,104]

000002ac e3500000    548 	cmp	r0,0

000002b0 1a000001    549 	bne	.L644

                     550 .L645:

000002b4 e3a00000    551 	mov	r0,0

000002b8 ea00008b    552 	b	.L618

                     553 .L644:

000002bc e3560000    554 	cmp	r6,0

000002c0 e2870c64    555 	add	r0,r7,25<<10

000002c4 e28010f8    556 	add	r1,r0,248

000002c8 e28d0050    557 	add	r0,sp,80

000002cc e1a06000    558 	mov	r6,r0

000002d0 e1a05006    559 	mov	r5,r6

000002d4 e3a04000    560 	mov	r4,0

000002d8 128d405c    561 	addne	r4,sp,92

                     562 ;239:     StringView* dirName, StringView* startFileName, BufferView* outBuf)


                     563 ;240: {


                     564 

                     565 ;241:     /*


                     566 ;242:         A1


                     567 ;243:             02 InvokeID


                     568 ;244:             BF 4D


                     569 ;245:                 A0


                     570 ;246:                     30


                     571 ;247:                         Список


                     572 ;248:                 More follows


                     573 ;249:     */


                     574 ;250: 


                     575 ;251:     bool moreFollows;


                     576 ;252:     BufferView fileListBuf;


                     577 ;253:     size_t fileListSeqSize;


                     578 ;254:     FileRespHeadInfo headInfo;


                     579 ;255: 


                     580 ;256:     //Получаем закодированый список файлов с атрибутами


                     581 ;257:     BufferView_init(&fileListBuf, mmsConn->fileBuf, FILE_BUF_SIZE, 0);


                     582 

000002dc e3a02ef0    583 	mov	r2,15<<8

000002e0 e282209c    584 	add	r2,r2,156

000002e4 e3a03000    585 	mov	r3,0

000002e8 eb000000*   586 	bl	BufferView_init

                     587 ;258:     if (!fileList(mmsConn, dirName, startFileName, &fileListBuf, &moreFollows))


                     588 

                     589 ;188:     StringView* startFileName, BufferView* outBuf, bool* moreFollows)


                     590 ;189: {


                     591 

                     592 ;190:     FSFindData fileInfo;


                     593 ;191:     BufferView nameBuf;


                     594 ;192:     FNameErrCode findResult;


                     595 ;193:     size_t oldOutBufPos;


                     596 ;194:     //Инициализируем буфер имени


                     597 ;195:     BufferView_init(&nameBuf, mmsConn->fileName, FILE_NAME_BUF_SIZE, 0);


                     598 

000002ec e2870c74    599 	add	r0,r7,29<<10


                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g6k1.s
000002f0 e2801094    600 	add	r1,r0,148

000002f4 e28d0014    601 	add	r0,sp,20

000002f8 e3a03000    602 	mov	r3,0

000002fc e3a020ff    603 	mov	r2,255

00000300 eb000000*   604 	bl	BufferView_init

                     605 ;196: 


                     606 ;197:     findResult = fs_findFirst(dirName, startFileName, &fileInfo, &nameBuf);


                     607 

00000304 e28d3014    608 	add	r3,sp,20

00000308 e28d2020    609 	add	r2,sp,32

0000030c e1a01004    610 	mov	r1,r4

00000310 e1a04002    611 	mov	r4,r2

00000314 e28d0064    612 	add	r0,sp,100

00000318 eb000000*   613 	bl	fs_findFirst

                     614 ;198:     while (findResult != FNAME_NOT_FOUND)


                     615 

0000031c e1a07000    616 	mov	r7,r0

00000320 e3570001    617 	cmp	r7,1

00000324 0a000034    618 	beq	.L669

                     619 .L652:

                     620 ;199:     {


                     621 

                     622 ;200:         if (findResult == FNAME_BUF_ERROR)


                     623 

00000328 e3570002    624 	cmp	r7,2

                     625 ;201:         {


                     626 

                     627 ;202:             ERROR_REPORT("The file name does not fit in the buffer");


                     628 ;203:             fs_findClose(&fileInfo);


                     629 

                     630 ;204:             return findResult;


                     631 

                     632 ;205:         }


                     633 ;206: 


                     634 ;207:         if (findResult == FNAME_ERROR)


                     635 

0000032c 13570003    636 	cmpne	r7,3

00000330 0a00003b    637 	beq	.L670

                     638 ;208:         {


                     639 

                     640 ;209:             ERROR_REPORT("Unknown error");


                     641 ;210:             fs_findClose(&fileInfo);


                     642 

                     643 ;211:             return findResult;


                     644 

                     645 ;212:         }


                     646 ;213: 


                     647 ;214:         //encodeFileInfo пишет инфу в буфер. Если инфа не влезла,


                     648 ;215:         //то надо откатить буфер к состоянию до вызова, и вернуть More Follows


                     649 ;216:         oldOutBufPos = outBuf->pos;


                     650 

00000334 e5967004    651 	ldr	r7,[r6,4]

                     652 ;217:         if (!encodeFileInfo(&fileInfo, outBuf))


                     653 

                     654 ;117: {


                     655 

                     656 ;118:     /*


                     657 ;119:         30


                     658 ;120:             A0


                     659 ;121:                 19 Имя файла


                     660 ;122:             A1



                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g6k1.s
                     661 ;123:                 80 Размер


                     662 ;124:                 81 Время


                     663 ;125:     */


                     664 ;126: 


                     665 ;127:     EncodedAttrLen encAttrLen;


                     666 ;128:     size_t encodedNameStringLen;//19


                     667 ;129:     size_t encodedNameLen;//A0


                     668 ;130: 


                     669 ;131: 


                     670 ;132:     //Определяем размеры


                     671 ;133:     determineEncodedAttrLen(&encAttrLen, fileInfo->attr.fileSize);


                     672 

00000338 e5941014    673 	ldr	r1,[r4,20]

0000033c e28d0008    674 	add	r0,sp,8

00000340 ebffff2e*   675 	bl	determineEncodedAttrLen

                     676 ;134:     encodedNameStringLen = BerEncoder_determineFullObjectSize(


                     677 

00000344 e594000c    678 	ldr	r0,[r4,12]

00000348 eb000000*   679 	bl	BerEncoder_determineFullObjectSize

                     680 ;135:         fileInfo->fileName.len);


                     681 ;136:     encodedNameLen = BerEncoder_determineFullObjectSize(encodedNameStringLen);


                     682 

0000034c e1a0a000    683 	mov	r10,r0

00000350 eb000000*   684 	bl	BerEncoder_determineFullObjectSize

                     685 ;137: 


                     686 ;138:     //Кодируем


                     687 ;139:     //Sequence


                     688 ;140:     if (!BufferView_encodeTL(


                     689 

00000354 e59d1010    690 	ldr	r1,[sp,16]

00000358 e0812000    691 	add	r2,r1,r0

0000035c e1a00005    692 	mov	r0,r5

00000360 e3a01030    693 	mov	r1,48

00000364 eb000000*   694 	bl	BufferView_encodeTL

00000368 e3500000    695 	cmp	r0,0

0000036c 0a000011    696 	beq	.L666

                     697 ;141:         outBuf, ASN_SEQUENCE, encodedNameLen + encAttrLen.fullAttrLen))


                     698 ;142:     {


                     699 

                     700 ;143:         return FALSE;


                     701 

                     702 ;144:     }


                     703 ;145:     //Имя файла


                     704 ;146: 


                     705 ;147:     if (!BufferView_encodeTL(outBuf, 0xA0, encodedNameStringLen))


                     706 

00000370 e1a0200a    707 	mov	r2,r10

00000374 e1a00005    708 	mov	r0,r5

00000378 e3a010a0    709 	mov	r1,160

0000037c eb000000*   710 	bl	BufferView_encodeTL

00000380 e3500000    711 	cmp	r0,0

00000384 0a00000b    712 	beq	.L666

                     713 ;148:     {


                     714 

                     715 ;149:         return FALSE;


                     716 

                     717 ;150:     }


                     718 ;151: 


                     719 ;152:     if (!BufferView_encodeStringView(outBuf, ASN_GRAPHIC_STRING,


                     720 

00000388 e284200c    721 	add	r2,r4,12


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g6k1.s
0000038c e1a00005    722 	mov	r0,r5

00000390 e3a01019    723 	mov	r1,25

00000394 eb000000*   724 	bl	BufferView_encodeStringView

00000398 e3500000    725 	cmp	r0,0

0000039c 0a000005    726 	beq	.L666

                     727 ;153:         &fileInfo->fileName))


                     728 ;154:     {


                     729 

                     730 ;155:         return FALSE;


                     731 

                     732 ;156:     }


                     733 ;157: 


                     734 ;158:     //Атрибуты


                     735 ;159:     if (!encodeFileAttr(outBuf, &encAttrLen, &fileInfo->attr))


                     736 

000003a0 e2842014    737 	add	r2,r4,20

000003a4 e28d1008    738 	add	r1,sp,8

000003a8 e1a00005    739 	mov	r0,r5

000003ac ebffff2b*   740 	bl	encodeFileAttr

000003b0 e3500000    741 	cmp	r0,0

000003b4 1a000008    742 	bne	.L667

                     743 .L666:

                     744 ;160:     {


                     745 

                     746 ;161:         return FALSE;


                     747 

                     748 ;162:     }


                     749 ;163:     return TRUE;


                     750 

                     751 ;218:         {


                     752 

                     753 ;219:             fs_findClose(&fileInfo);


                     754 

000003b8 e28d0020    755 	add	r0,sp,32

000003bc eb000000*   756 	bl	fs_findClose

                     757 ;220:             //Восстанавливаем позицию буфера до вызова


                     758 ;221:             outBuf->pos = oldOutBufPos;


                     759 

000003c0 e5867004    760 	str	r7,[r6,4]

                     761 ;222:             *moreFollows = TRUE;


                     762 

000003c4 e59d0054    763 	ldr	r0,[sp,84]

000003c8 e3a04001    764 	mov	r4,1

                     765 ;223:             return TRUE;


                     766 

                     767 ;259:     {


                     768 

                     769 ;260:         return FALSE;


                     770 

                     771 ;261:     }


                     772 ;262: 


                     773 ;263:     //===================Определяем размеры=====================


                     774 ;264:     // Размер списка файлов с размером и тэгом SEQUENCE(0x30) для тэга 0xA0


                     775 ;265:     fileListSeqSize = BerEncoder_determineFullObjectSize(fileListBuf.pos);


                     776 

000003cc eb000000*   777 	bl	BerEncoder_determineFullObjectSize

000003d0 e1a05000    778 	mov	r5,r0

                     779 ;266: 


                     780 ;267:     // Полный размер списка с размером и тэгом A0 и флагом More Follows


                     781 ;268:     // для тэга BF 4D


                     782 ;269:     headInfo.dataLen = BerEncoder_determineFullObjectSize(fileListSeqSize)



                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g6k1.s
                     783 

000003d4 e3a06000    784 	mov	r6,0

000003d8 ea00001b    785 	b	.L673

                     786 .L667:

                     787 ;224:         }


                     788 ;225: 


                     789 ;226:         //Освобождаем буфер имени


                     790 ;227:         nameBuf.pos = 0;


                     791 

000003dc e28d1014    792 	add	r1,sp,20

000003e0 e3a00000    793 	mov	r0,0

000003e4 e58d0018    794 	str	r0,[sp,24]

                     795 ;228: 


                     796 ;229:         findResult = fs_findNext(&fileInfo, &nameBuf);


                     797 

000003e8 e28d0020    798 	add	r0,sp,32

000003ec eb000000*   799 	bl	fs_findNext

000003f0 e1a07000    800 	mov	r7,r0

000003f4 e3570001    801 	cmp	r7,1

000003f8 1affffca    802 	bne	.L652

                     803 .L669:

                     804 ;230:     }


                     805 ;231: 


                     806 ;232:     fs_findClose(&fileInfo);


                     807 

000003fc e28d0020    808 	add	r0,sp,32

00000400 eb000000*   809 	bl	fs_findClose

                     810 ;233:     *moreFollows = FALSE;


                     811 

00000404 e59d0054    812 	ldr	r0,[sp,84]

00000408 e3a04000    813 	mov	r4,0

                     814 ;234:     return TRUE;


                     815 

                     816 ;259:     {


                     817 

                     818 ;260:         return FALSE;


                     819 

                     820 ;261:     }


                     821 ;262: 


                     822 ;263:     //===================Определяем размеры=====================


                     823 ;264:     // Размер списка файлов с размером и тэгом SEQUENCE(0x30) для тэга 0xA0


                     824 ;265:     fileListSeqSize = BerEncoder_determineFullObjectSize(fileListBuf.pos);


                     825 

0000040c eb000000*   826 	bl	BerEncoder_determineFullObjectSize

00000410 e1a05000    827 	mov	r5,r0

                     828 ;266: 


                     829 ;267:     // Полный размер списка с размером и тэгом A0 и флагом More Follows


                     830 ;268:     // для тэга BF 4D


                     831 ;269:     headInfo.dataLen = BerEncoder_determineFullObjectSize(fileListSeqSize)


                     832 

00000414 e1a06004    833 	mov	r6,r4

00000418 ea00000c    834 	b	.L674

                     835 	.align	4

                     836 .L530:

0000041c 00000000*   837 	.data.w	.L507

                     838 	.type	.L530,$object

                     839 	.size	.L530,4

                     840 

                     841 .L1287:

                     842 ;	"/\000"

00000420 002f       843 	.data.b	47,0


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g6k1.s
00000422 0000       844 	.align 4

                     845 

                     846 	.type	.L1287,$object

                     847 	.size	.L1287,4

                     848 

                     849 .L670:

00000424 e28d0020    850 	add	r0,sp,32

00000428 eb000000*   851 	bl	fs_findClose

0000042c e31700ff    852 	tst	r7,255

00000430 0a00002a    853 	beq	.L682

                     854 ;259:     {


                     855 

                     856 ;260:         return FALSE;


                     857 

                     858 ;261:     }


                     859 ;262: 


                     860 ;263:     //===================Определяем размеры=====================


                     861 ;264:     // Размер списка файлов с размером и тэгом SEQUENCE(0x30) для тэга 0xA0


                     862 ;265:     fileListSeqSize = BerEncoder_determineFullObjectSize(fileListBuf.pos);


                     863 

00000434 e59d0054    864 	ldr	r0,[sp,84]

00000438 e3a06000    865 	mov	r6,0

0000043c eb000000*   866 	bl	BerEncoder_determineFullObjectSize

00000440 e1a05000    867 	mov	r5,r0

                     868 ;266: 


                     869 ;267:     // Полный размер списка с размером и тэгом A0 и флагом More Follows


                     870 ;268:     // для тэга BF 4D


                     871 ;269:     headInfo.dataLen = BerEncoder_determineFullObjectSize(fileListSeqSize)


                     872 

00000444 e3540000    873 	cmp	r4,0

00000448 0a000000    874 	beq	.L674

                     875 .L673:

0000044c e3a06003    876 	mov	r6,3

                     877 .L674:

00000450 e1a00005    878 	mov	r0,r5

00000454 eb000000*   879 	bl	BerEncoder_determineFullObjectSize

00000458 e0861000    880 	add	r1,r6,r0

0000045c e59d006c    881 	ldr	r0,[sp,108]

00000460 e58d104c    882 	str	r1,[sp,76]

                     883 ;270:         + (moreFollows ? 3 : 0);


                     884 ;271: 


                     885 ;272:     //Размер ответа, включая InvokeID для тэга A1


                     886 ;273:     headInfo.responseLen = determineResponseLen(invokeID, headInfo.dataLen);


                     887 

00000464 ebfffef3*   888 	bl	determineResponseLen

00000468 e58d0048    889 	str	r0,[sp,72]

                     890 ;274: 


                     891 ;275:     //====================Кодируем================================


                     892 ;276:     headInfo.extTag = 0xBF4D;


                     893 

0000046c e3a00cbf    894 	mov	r0,191<<8

00000470 e280004d    895 	add	r0,r0,77

00000474 e1cd04b4    896 	strh	r0,[sp,68]

                     897 ;277:     headInfo.invokeID = invokeID;


                     898 

00000478 e59d006c    899 	ldr	r0,[sp,108]

0000047c e28d1040    900 	add	r1,sp,64

00000480 e58d0040    901 	str	r0,[sp,64]

                     902 ;278: 


                     903 ;279:     if (!encodeFileResponseHead(outBuf, &headInfo))


                     904 


                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g6k1.s
00000484 e1a0000b    905 	mov	r0,fp

00000488 ebffff28*   906 	bl	encodeFileResponseHead

0000048c e3500000    907 	cmp	r0,0

00000490 0a000012    908 	beq	.L682

                     909 ;280:     {


                     910 

                     911 ;281:         return FALSE;


                     912 

                     913 ;282:     }


                     914 ;283: 


                     915 ;284:     if (!BufferView_encodeTL(outBuf, 0xA0, fileListSeqSize))


                     916 

00000494 e1a02005    917 	mov	r2,r5

00000498 e1a0000b    918 	mov	r0,fp

0000049c e3a010a0    919 	mov	r1,160

000004a0 eb000000*   920 	bl	BufferView_encodeTL

000004a4 e3500000    921 	cmp	r0,0

000004a8 0a00000c    922 	beq	.L682

                     923 ;285:     {


                     924 

                     925 ;286:         return FALSE;


                     926 

                     927 ;287:     }


                     928 ;288:     // Cписок файлов из временного буфера в исходящий


                     929 ;289:     if (!BufferView_encodeBufferView(outBuf, ASN_SEQUENCE,&fileListBuf))


                     930 

000004ac e28d2050    931 	add	r2,sp,80

000004b0 e1a0000b    932 	mov	r0,fp

000004b4 e3a01030    933 	mov	r1,48

000004b8 eb000000*   934 	bl	BufferView_encodeBufferView

000004bc e3500000    935 	cmp	r0,0

000004c0 0a000006    936 	beq	.L682

                     937 ;290:     {


                     938 

                     939 ;291:         return FALSE;


                     940 

                     941 ;292:     }


                     942 ;293:     //More Follows


                     943 ;294:     if (moreFollows)


                     944 

000004c4 e3540000    945 	cmp	r4,0

000004c8 0a000006    946 	beq	.L683

                     947 ;295:     {


                     948 

                     949 ;296:         if (!BufferView_encodeBoolean(outBuf, 0x81, moreFollows))


                     950 

000004cc e1a02004    951 	mov	r2,r4

000004d0 e1a0000b    952 	mov	r0,fp

000004d4 e3a01081    953 	mov	r1,129

000004d8 eb000000*   954 	bl	BufferView_encodeBoolean

000004dc e3500000    955 	cmp	r0,0

                     956 .L682:

                     957 ;297:         {


                     958 

                     959 ;298:             return FALSE;


                     960 

000004e0 03a00000    961 	moveq	r0,0

000004e4 0a000000    962 	beq	.L618

                     963 .L683:

                     964 ;299:         }


                     965 ;300: 



                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g6k1.s
                     966 ;301:     }


                     967 ;302:     return TRUE;


                     968 

000004e8 e3a00001    969 	mov	r0,1

                     970 .L618:

000004ec e28dd06c    971 	add	sp,sp,108

000004f0 e8bd8cf4    972 	ldmfd	[sp]!,{r2,r4-r7,r10-fp,pc}

                     973 	.endf	mms_handleFileDirRequest

                     974 	.align	4

                     975 ;tag	[sp,3]	local

                     976 ;length	[sp,4]	local

                     977 ;dir	[sp,100]	local

                     978 ;startFile	[sp,92]	local

                     979 ;thereIsStartFile	r6	local

                     980 ;.L1183	.L1186	static

                     981 ;moreFollows	r4	local

                     982 ;fileListBuf	[sp,80]	local

                     983 ;fileListSeqSize	r5	local

                     984 ;headInfo	[sp,64]	local

                     985 ;fileInfo	[sp,32]	local

                     986 ;nameBuf	[sp,20]	local

                     987 ;findResult	r7	local

                     988 ;oldOutBufPos	r7	local

                     989 ;encAttrLen	[sp,8]	local

                     990 ;encodedNameStringLen	r10	local

                     991 

                     992 ;mmsConn	r7	param

                     993 ;inBuf	r4	param

                     994 ;invokeId	[sp,108]	param

                     995 ;outBuf	fp	param

                     996 

                     997 	.section ".bss","awb"

                     998 .L1182:

                     999 	.data

                    1000 	.text

                    1001 

                    1002 

                    1003 	.align	4

                    1004 	.align	4

                    1005 mms_handleFileOpenRequest::

000004f4 e92d40f0   1006 	stmfd	[sp]!,{r4-r7,lr}

000004f8 e1a06002   1007 	mov	r6,r2

000004fc e24dd040   1008 	sub	sp,sp,64

00000500 e28d2004   1009 	add	r2,sp,4

00000504 e1a04001   1010 	mov	r4,r1

00000508 e28d1003   1011 	add	r1,sp,3

0000050c e1a07000   1012 	mov	r7,r0

00000510 e1a00004   1013 	mov	r0,r4

00000514 e1a05003   1014 	mov	r5,r3

00000518 e3a03000   1015 	mov	r3,0

0000051c eb000000*  1016 	bl	BerDecoder_decodeTLFromBufferView

00000520 e3500000   1017 	cmp	r0,0

00000524 0a00001f   1018 	beq	.L1309

00000528 e5dd0003   1019 	ldrb	r0,[sp,3]

0000052c e35000a0   1020 	cmp	r0,160

00000530 1a00001c   1021 	bne	.L1309

00000534 e28d2004   1022 	add	r2,sp,4

00000538 e28d1003   1023 	add	r1,sp,3

0000053c e1a00004   1024 	mov	r0,r4

00000540 e3a03000   1025 	mov	r3,0

00000544 eb000000*  1026 	bl	BerDecoder_decodeTLFromBufferView


                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g6k1.s
00000548 e3500000   1027 	cmp	r0,0

0000054c 0a000015   1028 	beq	.L1309

00000550 e5dd0003   1029 	ldrb	r0,[sp,3]

00000554 e3500019   1030 	cmp	r0,25

00000558 1a000012   1031 	bne	.L1309

0000055c e28d2038   1032 	add	r2,sp,56

00000560 e59d1004   1033 	ldr	r1,[sp,4]

00000564 e1a00004   1034 	mov	r0,r4

00000568 eb000000*  1035 	bl	BufferView_readStringView

0000056c e3500000   1036 	cmp	r0,0

00000570 0a00000c   1037 	beq	.L1309

00000574 e28d2004   1038 	add	r2,sp,4

00000578 e28d1003   1039 	add	r1,sp,3

0000057c e1a00004   1040 	mov	r0,r4

00000580 e3a03000   1041 	mov	r3,0

00000584 eb000000*  1042 	bl	BerDecoder_decodeTLFromBufferView

00000588 e3500000   1043 	cmp	r0,0

0000058c 0a000005   1044 	beq	.L1309

00000590 e28d2008   1045 	add	r2,sp,8

00000594 e59d1004   1046 	ldr	r1,[sp,4]

00000598 e1a00004   1047 	mov	r0,r4

0000059c eb000000*  1048 	bl	BufferView_decodeUInt32

000005a0 e3500000   1049 	cmp	r0,0

000005a4 1a000001   1050 	bne	.L1313

                    1051 .L1309:

000005a8 e3a00000   1052 	mov	r0,0

000005ac ea00002d   1053 	b	.L1288

                    1054 .L1313:

                    1055 ;350:     StringView* fileName, size_t startPos, BufferView* outBuf)


                    1056 ;351: {


                    1057 

                    1058 ;352:     /*


                    1059 ;353:         A1


                    1060 ;354:             02 InvokeID


                    1061 ;355:             BF 48


                    1062 ;356:                 80 FRSM ID


                    1063 ;357:                 A1 (содержит атрибуты)


                    1064 ;358:                     80 Размер


                    1065 ;359:                     81 Время


                    1066 ;360:     */


                    1067 ;361:     EncodedAttrLen encAttrLen;


                    1068 ;362:     size_t encodedFRSMIDlen;


                    1069 ;363:     FileRespHeadInfo respHeadInfo;


                    1070 ;364:     FSFileAttr attr;


                    1071 ;365:     uint32_t frsmID;


                    1072 ;366: 


                    1073 ;367:     if (!fs_fileOpen(fileName, startPos, &frsmID, &attr))


                    1074 

000005b0 e28d3010   1075 	add	r3,sp,16

000005b4 e28d200c   1076 	add	r2,sp,12

000005b8 e59d1008   1077 	ldr	r1,[sp,8]

000005bc e28d0038   1078 	add	r0,sp,56

000005c0 eb000000*  1079 	bl	fs_fileOpen

000005c4 e3500000   1080 	cmp	r0,0

000005c8 0a000025   1081 	beq	.L1320

                    1082 ;368:     {


                    1083 

                    1084 ;369:         return FALSE;


                    1085 

                    1086 ;370:     }


                    1087 ;371:     mmsConn->isFileOpen = TRUE;



                                                                      Page 19
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g6k1.s
                    1088 

000005cc e2870c75   1089 	add	r0,r7,117<<8

000005d0 e3a01001   1090 	mov	r1,1

000005d4 e5c01093   1091 	strb	r1,[r0,147]

                    1092 ;372:     mmsConn->frsmID = frsmID;


                    1093 

000005d8 e59d100c   1094 	ldr	r1,[sp,12]

000005dc e5801094   1095 	str	r1,[r0,148]

                    1096 ;373: 


                    1097 ;374:     //===================Определяем размеры=====================


                    1098 ;375:     determineEncodedAttrLen(&encAttrLen, attr.fileSize);


                    1099 

000005e0 e59d1010   1100 	ldr	r1,[sp,16]

000005e4 e28d002c   1101 	add	r0,sp,44

000005e8 ebfffe84*  1102 	bl	determineEncodedAttrLen

                    1103 ;376:     encodedFRSMIDlen = 2 //тэг + длина


                    1104 

000005ec e59d000c   1105 	ldr	r0,[sp,12]

000005f0 eb000000*  1106 	bl	BerEncoder_UInt32determineEncodedSize

000005f4 e59d1034   1107 	ldr	r1,[sp,52]

000005f8 e2800002   1108 	add	r0,r0,2

                    1109 ;377:         + BerEncoder_UInt32determineEncodedSize(frsmID);


                    1110 ;378:     //данные для тэга BF 48


                    1111 ;379:     respHeadInfo.dataLen = encAttrLen.fullAttrLen + encodedFRSMIDlen;


                    1112 

000005fc e0801001   1113 	add	r1,r0,r1

00000600 e58d1028   1114 	str	r1,[sp,40]

                    1115 ;380:     respHeadInfo.responseLen = determineResponseLen(invokeID,


                    1116 

00000604 e1a00006   1117 	mov	r0,r6

00000608 ebfffe8a*  1118 	bl	determineResponseLen

0000060c e58d601c   1119 	str	r6,[sp,28]

                    1120 ;386:     if (!encodeFileResponseHead(outBuf, &respHeadInfo))


                    1121 

00000610 e28d101c   1122 	add	r1,sp,28

00000614 e58d0024   1123 	str	r0,[sp,36]

                    1124 ;381:         respHeadInfo.dataLen);


                    1125 ;382: 


                    1126 ;383:     //===================Кодируем=====================


                    1127 ;384:     respHeadInfo.extTag = 0xBF48;


                    1128 

00000618 e3a00cbf   1129 	mov	r0,191<<8

0000061c e2800048   1130 	add	r0,r0,72

00000620 e1cd02b0   1131 	strh	r0,[sp,32]

                    1132 ;385:     respHeadInfo.invokeID = invokeID;


                    1133 

00000624 e1a00005   1134 	mov	r0,r5

00000628 ebfffec0*  1135 	bl	encodeFileResponseHead

0000062c e3500000   1136 	cmp	r0,0

00000630 0a00000b   1137 	beq	.L1320

                    1138 ;387:     {


                    1139 

                    1140 ;388:         return FALSE;


                    1141 

                    1142 ;389:     }


                    1143 ;390:     //FRSM ID


                    1144 ;391:     if (!BufferView_encodeUInt32(outBuf, 0x80, frsmID))


                    1145 

00000634 e59d200c   1146 	ldr	r2,[sp,12]

00000638 e1a00005   1147 	mov	r0,r5

0000063c e3a01080   1148 	mov	r1,128


                                                                      Page 20
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g6k1.s
00000640 eb000000*  1149 	bl	BufferView_encodeUInt32

00000644 e3500000   1150 	cmp	r0,0

00000648 0a000005   1151 	beq	.L1320

                    1152 ;392:     {


                    1153 

                    1154 ;393:         return FALSE;


                    1155 

                    1156 ;394:     }


                    1157 ;395: 


                    1158 ;396:     //Атрибуты


                    1159 ;397:     if (!encodeFileAttr(outBuf, &encAttrLen, &attr))


                    1160 

0000064c e28d2010   1161 	add	r2,sp,16

00000650 e28d102c   1162 	add	r1,sp,44

00000654 e1a00005   1163 	mov	r0,r5

00000658 ebfffe80*  1164 	bl	encodeFileAttr

0000065c e3500000   1165 	cmp	r0,0

                    1166 ;400:     }


                    1167 ;401:     return TRUE;


                    1168 

00000660 13a00001   1169 	movne	r0,1

                    1170 .L1320:

                    1171 ;398:     {


                    1172 

                    1173 ;399:         return FALSE;


                    1174 

00000664 03a00000   1175 	moveq	r0,0

                    1176 .L1288:

00000668 e28dd040   1177 	add	sp,sp,64

0000066c e8bd80f0   1178 	ldmfd	[sp]!,{r4-r7,pc}

                    1179 	.endf	mms_handleFileOpenRequest

                    1180 	.align	4

                    1181 ;tag	[sp,3]	local

                    1182 ;length	[sp,4]	local

                    1183 ;fileName	[sp,56]	local

                    1184 ;startPos	[sp,8]	local

                    1185 ;encAttrLen	[sp,44]	local

                    1186 ;encodedFRSMIDlen	r0	local

                    1187 ;respHeadInfo	[sp,28]	local

                    1188 ;attr	[sp,16]	local

                    1189 ;frsmID	[sp,12]	local

                    1190 

                    1191 ;mmsConn	r7	param

                    1192 ;inBuf	r4	param

                    1193 ;invokeId	r6	param

                    1194 ;outBuf	r5	param

                    1195 

                    1196 	.section ".bss","awb"

                    1197 .L1489:

                    1198 	.data

                    1199 	.text

                    1200 

                    1201 

                    1202 	.align	4

                    1203 	.align	4

                    1204 mms_handleFileReadRequest::

00000670 e92d4070   1205 	stmfd	[sp]!,{r4-r6,lr}

00000674 e1a04003   1206 	mov	r4,r3

00000678 e1a05002   1207 	mov	r5,r2

0000067c e24dd024   1208 	sub	sp,sp,36

00000680 e1a06000   1209 	mov	r6,r0


                                                                      Page 21
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g6k1.s
00000684 e1a00001   1210 	mov	r0,r1

00000688 e5901008   1211 	ldr	r1,[r0,8]

0000068c e28d2004   1212 	add	r2,sp,4

00000690 eb000000*  1213 	bl	BufferView_decodeUInt32

00000694 e3500000   1214 	cmp	r0,0

00000698 0a000029   1215 	beq	.L1536

                    1216 ;439:     uint32_t frsmID, BufferView* outBuf)


                    1217 ;440: {


                    1218 

                    1219 ;441:     /*


                    1220 ;442:         A1


                    1221 ;443:             02 InvokeID


                    1222 ;444:             BF 49


                    1223 ;445:                 80 file data


                    1224 ;446:                 81 more follows


                    1225 ;447:     */


                    1226 ;448:     BufferView fileReadBuf;


                    1227 ;449:     bool moreFollows;


                    1228 ;450:     size_t moreFollowsLen = 3;


                    1229 

                    1230 ;451:     size_t encodedFileDataLen;


                    1231 ;452:     FileRespHeadInfo respHeadInfo;


                    1232 ;453: 


                    1233 ;454:     BufferView_init(&fileReadBuf, mmsConn->fileBuf, FILE_BUF_SIZE, 0);


                    1234 

0000069c e3a02ef0   1235 	mov	r2,15<<8

000006a0 e282209c   1236 	add	r2,r2,156

000006a4 e2860c64   1237 	add	r0,r6,25<<10

000006a8 e28010f8   1238 	add	r1,r0,248

000006ac e28d0018   1239 	add	r0,sp,24

000006b0 e3a03000   1240 	mov	r3,0

000006b4 eb000000*  1241 	bl	BufferView_init

                    1242 ;455:     if (!fs_fileRead(frsmID, &fileReadBuf, &moreFollows))


                    1243 

000006b8 e28d2003   1244 	add	r2,sp,3

000006bc e59d0004   1245 	ldr	r0,[sp,4]

000006c0 e28d1018   1246 	add	r1,sp,24

000006c4 eb000000*  1247 	bl	fs_fileRead

000006c8 e3500000   1248 	cmp	r0,0

000006cc 0a00001b   1249 	beq	.L1550

                    1250 ;456:     {


                    1251 

                    1252 ;457:         return FALSE;


                    1253 

                    1254 ;458:     }


                    1255 ;459:     //===================Определяем размеры=====================


                    1256 ;460:     encodedFileDataLen = BerEncoder_determineFullObjectSize(fileReadBuf.pos);


                    1257 

000006d0 e59d001c   1258 	ldr	r0,[sp,28]

000006d4 eb000000*  1259 	bl	BerEncoder_determineFullObjectSize

                    1260 ;461:     respHeadInfo.dataLen = encodedFileDataLen + moreFollowsLen;


                    1261 

000006d8 e2801003   1262 	add	r1,r0,3

000006dc e58d1014   1263 	str	r1,[sp,20]

                    1264 ;462:     respHeadInfo.responseLen = determineResponseLen(invokeID,


                    1265 

000006e0 e1a00005   1266 	mov	r0,r5

000006e4 ebfffe53*  1267 	bl	determineResponseLen

000006e8 e58d5008   1268 	str	r5,[sp,8]

                    1269 ;468:     if (!encodeFileResponseHead(outBuf, &respHeadInfo))


                    1270 


                                                                      Page 22
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g6k1.s
000006ec e28d1008   1271 	add	r1,sp,8

000006f0 e58d0010   1272 	str	r0,[sp,16]

                    1273 ;463:         respHeadInfo.dataLen);


                    1274 ;464: 


                    1275 ;465:     //===================Кодируем=====================


                    1276 ;466:     respHeadInfo.extTag = 0xBF49;


                    1277 

000006f4 e3a00cbf   1278 	mov	r0,191<<8

000006f8 e2800049   1279 	add	r0,r0,73

000006fc e1cd00bc   1280 	strh	r0,[sp,12]

                    1281 ;467:     respHeadInfo.invokeID = invokeID;


                    1282 

00000700 e1a00004   1283 	mov	r0,r4

00000704 ebfffe89*  1284 	bl	encodeFileResponseHead

00000708 e3500000   1285 	cmp	r0,0

0000070c 0a00000b   1286 	beq	.L1550

                    1287 ;469:     {


                    1288 

                    1289 ;470:         return FALSE;


                    1290 

                    1291 ;471:     }


                    1292 ;472:     //Данные


                    1293 ;473:     if (!BufferView_encodeBufferView(outBuf, 0x80, &fileReadBuf))


                    1294 

00000710 e28d2018   1295 	add	r2,sp,24

00000714 e1a00004   1296 	mov	r0,r4

00000718 e3a01080   1297 	mov	r1,128

0000071c eb000000*  1298 	bl	BufferView_encodeBufferView

00000720 e3500000   1299 	cmp	r0,0

00000724 0a000005   1300 	beq	.L1550

                    1301 ;474:     {


                    1302 

                    1303 ;475:         return FALSE;


                    1304 

                    1305 ;476:     }


                    1306 ;477:     //More follows


                    1307 ;478:     if (!BufferView_encodeBoolean(outBuf, 0x81, moreFollows))


                    1308 

00000728 e5dd2003   1309 	ldrb	r2,[sp,3]

0000072c e1a00004   1310 	mov	r0,r4

00000730 e3a01081   1311 	mov	r1,129

00000734 eb000000*  1312 	bl	BufferView_encodeBoolean

00000738 e3500000   1313 	cmp	r0,0

                    1314 ;481:     }


                    1315 ;482:     return TRUE;


                    1316 

0000073c 13a00001   1317 	movne	r0,1

                    1318 .L1550:

                    1319 ;479:     {


                    1320 

                    1321 ;480:         return FALSE;


                    1322 

00000740 03a00000   1323 	moveq	r0,0

                    1324 .L1536:

00000744 e28dd024   1325 	add	sp,sp,36

00000748 e8bd8070   1326 	ldmfd	[sp]!,{r4-r6,pc}

                    1327 	.endf	mms_handleFileReadRequest

                    1328 	.align	4

                    1329 ;frsmID	[sp,4]	local

                    1330 ;result	r0	local

                    1331 ;fileReadBuf	[sp,24]	local


                                                                      Page 23
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g6k1.s
                    1332 ;moreFollows	[sp,3]	local

                    1333 ;respHeadInfo	[sp,8]	local

                    1334 

                    1335 ;mmsConn	r6	param

                    1336 ;inBuf	r12	param

                    1337 ;invokeID	r5	param

                    1338 ;outBuf	r4	param

                    1339 

                    1340 	.section ".bss","awb"

                    1341 .L1683:

                    1342 	.data

                    1343 	.text

                    1344 

                    1345 

                    1346 ;520: 


                    1347 ;521: bool mms_handleFileCloseRequest(MmsConnection* mmsConn, BufferView* inBuf,


                    1348 	.align	4

                    1349 	.align	4

                    1350 mms_handleFileCloseRequest::

0000074c e92d4070   1351 	stmfd	[sp]!,{r4-r6,lr}

                    1352 ;522:     unsigned int invokeID, BufferView* outBuf)


                    1353 ;523: {


                    1354 

                    1355 ;524:     /*


                    1356 ;525:         inBuf содержит только frsmID


                    1357 ;526:     */


                    1358 ;527:     uint32_t frsmID;


                    1359 ;528:     bool result = BufferView_decodeUInt32(inBuf, inBuf->len, &frsmID);


                    1360 

00000750 e1a06003   1361 	mov	r6,r3

00000754 e1a04002   1362 	mov	r4,r2

00000758 e24dd014   1363 	sub	sp,sp,20

0000075c e1a05000   1364 	mov	r5,r0

00000760 e1a00001   1365 	mov	r0,r1

00000764 e5901008   1366 	ldr	r1,[r0,8]

00000768 e1a0200d   1367 	mov	r2,sp

0000076c eb000000*  1368 	bl	BufferView_decodeUInt32

                    1369 ;529:     RET_IF_NOT(result, "Error reading FRSM ID");


                    1370 

00000770 e3500000   1371 	cmp	r0,0

00000774 0a000012   1372 	beq	.L1711

00000778 e59d0000   1373 	ldr	r0,[sp]

0000077c eb000000*  1374 	bl	fs_fileClose

                    1375 ;530:     return encodeFileCloseRequest(mmsConn, invokeID, frsmID, outBuf);


                    1376 

                    1377 ;498:     uint32_t frsmID, BufferView* outBuf)


                    1378 ;499: {


                    1379 

                    1380 ;500:     /*


                    1381 ;501:         A1


                    1382 ;502:             02 InvokeID


                    1383 ;503:             9F 4A пустой


                    1384 ;504:     */


                    1385 ;505:     FileRespHeadInfo respHeadInfo;


                    1386 ;506: 


                    1387 ;507:     if (!fs_fileClose(frsmID))


                    1388 

00000780 e3500000   1389 	cmp	r0,0

                    1390 ;508:     {


                    1391 

                    1392 ;509:         return FALSE;



                                                                      Page 24
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g6k1.s
                    1393 

00000784 0a00000e   1394 	beq	.L1711

                    1395 ;510:     }


                    1396 ;511:     mmsConn->isFileOpen = FALSE;


                    1397 

00000788 e3a01000   1398 	mov	r1,0

0000078c e58d1010   1399 	str	r1,[sp,16]

                    1400 ;514:     respHeadInfo.responseLen = determineResponseLen(invokeID,


                    1401 

00000790 e3a00c75   1402 	mov	r0,117<<8

00000794 e2800093   1403 	add	r0,r0,147

00000798 e7c51000   1404 	strb	r1,[r5,r0]

                    1405 ;512: 


                    1406 ;513:     respHeadInfo.dataLen = 0;


                    1407 

0000079c e1a00004   1408 	mov	r0,r4

000007a0 ebfffe24*  1409 	bl	determineResponseLen

000007a4 e58d4004   1410 	str	r4,[sp,4]

                    1411 ;517:     respHeadInfo.extTag = 0x9F4A;


                    1412 

000007a8 e28d1004   1413 	add	r1,sp,4

000007ac e58d000c   1414 	str	r0,[sp,12]

                    1415 ;515:         respHeadInfo.dataLen);


                    1416 ;516:     respHeadInfo.invokeID = invokeID;


                    1417 

000007b0 e3a00c9f   1418 	mov	r0,159<<8

000007b4 e280004a   1419 	add	r0,r0,74

000007b8 e1cd00b8   1420 	strh	r0,[sp,8]

                    1421 ;518:     return encodeFileResponseHead(outBuf, &respHeadInfo);


                    1422 

000007bc e1a00006   1423 	mov	r0,r6

000007c0 ebfffe5a*  1424 	bl	encodeFileResponseHead

                    1425 .L1711:

000007c4 e28dd014   1426 	add	sp,sp,20

000007c8 e8bd8070   1427 	ldmfd	[sp]!,{r4-r6,pc}

                    1428 	.endf	mms_handleFileCloseRequest

                    1429 	.align	4

                    1430 ;frsmID	[sp]	local

                    1431 ;result	r0	local

                    1432 ;respHeadInfo	[sp,4]	local

                    1433 

                    1434 ;mmsConn	r5	param

                    1435 ;inBuf	r12	param

                    1436 ;invokeID	r4	param

                    1437 ;outBuf	r6	param

                    1438 

                    1439 	.section ".bss","awb"

                    1440 .L1794:

                    1441 	.data

                    1442 	.text

                    1443 

                    1444 ;531: }


                    1445 	.align	4

                    1446 ;__UNNAMED_1_static_in_makeGeneralizedTime	.L507	static

                    1447 

                    1448 	.data

                    1449 	.ghsnote version,6

                    1450 	.ghsnote tools,3

                    1451 	.ghsnote options,0

                    1452 	.text

                    1453 	.align	4


                                                                      Page 25
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_g6k1.s
                    1454 	.section ".rodata","a"

                    1455 	.align	4

                    1456 	.text

