#include <debug.h>


#ifdef SYSLOG

#include <syslog.h>
#include "Clib.h"
#include <string.h>
#include <stdarg.h>

int g_syslogHandler;
volatile STIMER g_pauseTimer;

static void pause(void)
{
    memset((STIMER*)&g_pauseTimer, 0, sizeof(g_pauseTimer));
    CreateTimer((STIMER*)&g_pauseTimer);
    g_pauseTimer.AlarmTime = 5000;
    g_pauseTimer.Precision = 1;
    g_pauseTimer.Started = 1;
    while ( !g_pauseTimer.Alarm) {
        Idle();
    }
}

void printf_wrapper(char* fmtMsg, ...)
{
    va_list args;
    va_start(args, fmtMsg);

    vsyslog(g_syslogHandler,LOG_NOTICE, fmtMsg, args);

    va_end(args);
}

void debugStart()
{    
    pause();
    syslog_init();
    g_syslogHandler = openlog ("M<PERSON>",0,LOG_LOCAL1);
}

#else

void debugStart()
{
}

#endif


void debugSendUshort(char* text, unsigned short value)
{
}

void debugSendStrL(char* text, unsigned char* str, int strLen)
{
}

void debugSendDump(char* text, unsigned char* data, int byteCount)
{
}

void debugSendText(char* text)
{
}
