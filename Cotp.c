#include "Cotp.h"

#include <debug.h>
#include "platform_socket.h"
#include <stddef.h>
#include <string.h>

#define TPDU_TYPE_CONNECT_REQUEST 0xe0
#define TPDU_TYPE_CONNECT_RESPONSE 0xd0
#define TPDU_TYPE_DATA 0xf0

#define CONNECTION_RESPONSE_HEADER_LENGTH 7
#define DATA_HEADER_LENGTH 3

#define OPT_TPDU_SIZE 0xC0

// Читает заголовок TPKT, LI, и DT в буфер заголовка
// Возвращает полный размер TPDU или -1 при ошибке
static int readHeader(COTPConnection * conn)
{
    TPKTHeader* pTPKTHeader = &conn->resvTPKTHeader.tpktHeader;
    int recvResult;
	int cotpLength;

    //Получаем заголовок TPKT, LI, DT
    recvResult = readSocket(conn->socket, &conn->resvTPKTHeader, sizeof(PacketHeader));
    if(!recvResult)
    {
        //Ошибка чтения
        ERROR_REPORT("Read TPKT header Error");
        return -1;
    }

    //проверяем заголовок TPKT
    if(pTPKTHeader->version != 3 || pTPKTHeader->reserved != 0 )
    {
        //Неверный заголовок TPKT
		ERROR_REPORT("Invalid TPKT header");
        return -1;
    }

	cotpLength = pTPKTHeader->packetLenH * 256 + pTPKTHeader->packetLenL;		
    return cotpLength;
}

static int parseCOTPoptions(COTPConnection* cotpConn, unsigned char* options,
                            int allOptionsLen)
{
    int optionPos = 0;    
    while(optionPos < allOptionsLen)
    {                
        int optionType = options[optionPos++];
        int optionLen = options[optionPos++];        
        if(optionPos + optionLen > allOptionsLen)
        {
            //option length error
            debugSendText("option length error");
            return -1;
        }
        switch(optionType)
        {
        case OPT_TPDU_SIZE:
            if (optionLen == 1) {
                //TODO Use requestedTpduSize
                //int requestedTpduSize = (1 << options[optionPos]);
                //debugSendUshort("\tmax TPDU size:", requestedTpduSize);
            }
            else
            {
               debugSendUshort("\tInvalid option size", optionType);
            }
            break;        
        //default:
            //debugSendUshort("\tUnknown option:", optionType);
        }
        optionPos += optionLen;
    }
    return 1;
}

static int getCOTPoptionsLength()
{
    //TPDU size option;
    return 3;
}

void writeTPKTHeader(int messageLen, unsigned char* buf)
{
    TPKTHeader* header = (TPKTHeader*)buf;
    header->version = 3;
    header->reserved = 0;
    header->packetLenH = messageLen >> 8;
    header->packetLenL = messageLen & 0xFF;
}

static void writeCOTPConnectResponseHeader(COTPConnection* cotpConn,int optionsLength,
                                           unsigned char* buf)
{

    int headerLengthValue =
            CONNECTION_RESPONSE_HEADER_LENGTH -1;//Длина не входит
    buf[0] =  headerLengthValue  + optionsLength;
    buf[1] = TPDU_TYPE_CONNECT_RESPONSE;
    buf[2] = cotpConn->remoteRef >> 8;
    buf[3] = cotpConn->remoteRef & 0xff;
    buf[4] = cotpConn->localRef >> 8;
    buf[5] = cotpConn->localRef & 0xff;
    buf[6] = cotpConn->protocolClass;
}

static void writeCOTPoptions(unsigned char* buf)
{
        buf[0] = OPT_TPDU_SIZE;
        buf[1] = 1;// Размер опции
        buf[2] = 10;//Максимальный размер TPDU: степень 2
}

static int sendCOTPConnectionResponse(COTPConnection* cotpConn, unsigned char* buf)
{
    int bufPos = 0;
    int optionsLength = getCOTPoptionsLength();    
    //4 + 7 + 3
    int messageLen = sizeof(TPKTHeader) + CONNECTION_RESPONSE_HEADER_LENGTH
            + optionsLength;

    writeTPKTHeader(messageLen, buf);
    bufPos += sizeof( TPKTHeader);
    writeCOTPConnectResponseHeader(cotpConn, optionsLength, buf + bufPos );
    bufPos += CONNECTION_RESPONSE_HEADER_LENGTH;

    writeCOTPoptions(buf + bufPos );

    return writeSocket(cotpConn->socket, buf, messageLen);
}

static int processCOTPconnectRequest(COTPConnection* cotpConn, int tpduSize)
{
    //RFC905 13.3.1
   ConnectRequest* pConnectRequest = &cotpConn->recvConnectRequest;

    //Размер пакета без заголовка TPKT, LI и DT
    int remainPacketSize = tpduSize - sizeof (PacketHeader);

    unsigned char li = cotpConn->resvTPKTHeader.li;

    if(remainPacketSize > sizeof(ConnectRequest))
    {
        //Пакет не лезет в буфер
        debugSendText("TPDU does not fit in the buffer");
        return -1;
    }

    if(li < 6)
    {
        return -1;
    }

    //Читаем оставшуюся часть пакета
    if(readSocket(cotpConn->socket, pConnectRequest, remainPacketSize ) == -1)
    {
        //Ошибка чтения
        return -1;
    }
    cotpConn->remoteRef = (pConnectRequest->srcRefH << 8) + pConnectRequest->srcRefL;
    //debugSendUshort("\tremoteRef:", cotpConn->remoteRef);
    cotpConn->protocolClass = pConnectRequest->protocolClass;
    //debugSendUshort("\tprotocolClass:", cotpConn->protocolClass);

    //  Внимание! LI не входит в длину
    if(parseCOTPoptions(cotpConn, pConnectRequest->variablePart, li - 6) < 0)
    {
        return -1;
    }

    if(sendCOTPConnectionResponse(cotpConn, cotpConn->outBuf) == -1)
    {
        debugSendText("sendCOTPConnectionResponse error");
        return -1;
    }

    debugSendText("Connection response is sent");
    return 1;
}

// Получает остаток пакета DATA TPDU и складывает в текущую позицию буфера
// Возвращает
// 0 если не последний пакет,
// 1 если последний
// -1 при ошибке
static int processCOTPdataTPDU(COTPConnection* cotpConn,
                               unsigned char* inBuffer, int inBufferSize, int tpduSize)
{
    unsigned char flowControl;

    int dataSize = tpduSize - sizeof(PacketHeader) - 1/*flow control*/;

    if(cotpConn->inBufPos +  dataSize > inBufferSize)
    {
        //Не лезет в буфер
        return -1;
    }

    //RFC905 13.7.1
    if (cotpConn->resvTPKTHeader.li != 2)// DT + TPDU-NR
    {
         return -1;
    }

    //Читаем оставшуюся часть заголовка
    if(readSocket(cotpConn->socket, &flowControl, 1) == -1)
    {
        return -1;
    }

    //Читаем сами данные
    if(readSocket(cotpConn->socket, inBuffer + cotpConn->inBufPos, dataSize) == -1)
    {
            return -1;
    }
    cotpConn->inBufPos += dataSize;

     if (flowControl & 0x80)
     {
         //Последний пакет
         return 1;
     }
     else
     {
         //Не последний пакет
         return 0;
     }
}

//Формирует TPKT пакет с данными и посылает.
//Возвращает, сколько фактически послано данных включая заголовок
//или -1
static int cotpSendDataTPDU(COTPConnection* cotpConn, void* sendData, int sendByteCount,
                            int eot)
{
    //RFC905 13.7.1    
    unsigned char* buf = cotpConn->outBuf;
    int bufPos = 0;
    int messageLen = sizeof(TPKTHeader) + DATA_HEADER_LENGTH + sendByteCount;
	
    writeTPKTHeader(messageLen, buf);
    bufPos += sizeof(TPKTHeader);
    //Write COTP data header
    //li
    buf[bufPos++] = 2;// DT + EOT
    //dt
    buf[bufPos++] =COTP_DATA_TRANSFER;
    //EOT
    buf[bufPos++] = eot ? 0x80: 0;
    //Write user data
    memcpy(buf + bufPos, sendData, sendByteCount);    
    return writeSocket(cotpConn->socket, buf, messageLen);
}


//Посылает данные по COTP, разбивая на фрагменты по необходимости.
//Возвращает byteCount
//или -1 при ошибке
int cotpSendData(COTPConnection* cotpConn, void* data, int byteCount)
{
    unsigned char* buf = data;
    int bytesRemain = byteCount;
    int eot;
    do
    {
        int bytesToSend = MAX_TPKT_TPDU_SIZE - sizeof(TPKTHeader)
                - DATA_HEADER_LENGTH;
        eot = 0;
        if(bytesToSend >= bytesRemain)
        {
            bytesToSend = bytesRemain;
            eot = 1;
        }
        if( cotpSendDataTPDU(cotpConn, buf, bytesToSend, eot) == -1 )
        {
            debugSendText("Error sending TPDU");
            return -1;
        }        
        bytesRemain -= bytesToSend;
        buf += bytesToSend;
    }
    while(!eot);
    return byteCount;
}

int cotpReceiveData(COTPConnection* cotpConn, void* recvBuf, int maxByteCount )
{
    int lastDataPacket;
    cotpConn->inBufPos = 0;
    while(1)
    {
        //Читаем заголовок пакета (TPKT + кусочек COTP)
        int tpduSize = readHeader(cotpConn);
        if(tpduSize == -1)
        {
            return -1;
        }

        switch (cotpConn->resvTPKTHeader.dt) {
        case TPDU_TYPE_CONNECT_REQUEST:
            debugSendText("Received COTP connect request");
            if(processCOTPconnectRequest(cotpConn, tpduSize) < 0)
            {
                return -1;
            }
            continue;
        case TPDU_TYPE_DATA:			            
            lastDataPacket = processCOTPdataTPDU(cotpConn, recvBuf, maxByteCount,
                                                 tpduSize);
            if(lastDataPacket < 0)
            {
                return -1;
            }
            if(lastDataPacket)
            {
                return cotpConn->inBufPos;
            }
            continue;
        default:
            debugSendUshort("Unknown TPDU type", cotpConn->resvTPKTHeader.dt);
            continue;
        }
    }

}

/*
//Возвращает количество полученных данных
int cotp(COTPConnection* cotpConn, void* sendData, int sendByteCount, void* recvBuf,
         int maxByteCount)
{    
    int lastDataPacket;
    cotpConn->inBufPos = 0;
    if(sendData != NULL && sendByteCount)
    {
        if(cotpSendData(cotpConn, sendData, sendByteCount) == -1)
        {
            return -1;
        }
    }
    while(1)
    {
        //Читаем заголовок пакета (TPKT + кусочек COTP)
        int tpduSize = readHeader(cotpConn);
        if(tpduSize == -1)
        {
            return -1;
        }

        switch (cotpConn->resvTPKTHeader.dt) {
        case TPDU_TYPE_CONNECT_REQUEST:            
            debugSendText("Received COTP connect request");
            if(processCOTPconnectRequest(cotpConn, tpduSize) < 0)
            {
                return -1;
            }
            continue;
        case TPDU_TYPE_DATA:
            //debugSendText("Received COTP data");
            lastDataPacket = processCOTPdataTPDU(cotpConn, recvBuf, maxByteCount,
                                                 tpduSize);
            if(lastDataPacket < 0)
            {
                return -1;
            }
            if(lastDataPacket)
            {
                return cotpConn->inBufPos;
            }
            continue;
        default:
            debugSendUshort("Unknown TPDU type", cotpConn->resvTPKTHeader.dt);
            continue;
        }
    }    
}
*/

void initCOTPConnection(COTPConnection* cotpConn, SERVER_SOCKET socket)
{
    cotpConn->socket = socket;
    cotpConn->protocolClass = 0xFF;
    cotpConn->remoteRef = 0xFFFF;
    cotpConn->localRef = 1;
}
