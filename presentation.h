#ifndef PRESENTATION_H_		
#define PRESENTATION_H_

#include "session.h"
#include "MmsConst.h"

typedef struct {
    unsigned int callingPresentationSelector;
    unsigned int calledPresentationSelector;
    unsigned char presentationContextId;
    unsigned char nextContextId;
    unsigned char acseContextId;
    unsigned char mmsContextId;
    unsigned char outBuf[DEFAULT_BUFFER_SIZE];
    int inDataSize;
} IsoPresentation;


void initPresentation(IsoPresentation* presentation);

//! Создаёт сообщение "Connnect presentation accept"
//! в указанном буфере
//! Возвращает размер сообщения
int isoPresentation_createCpaMessage(IsoPresentation* presentation,
                                 unsigned char* buf, unsigned char* userData, int userDataLen);


int isoPresentation_parseUserData(IsoPresentation* presentation,
                                  unsigned char* inbuf, int inLen,
                                  unsigned char** pOutUserData);

int IsoPresentation_createUserData(IsoPresentation* presentation,
	unsigned char* buf, unsigned char* userData, int userDataLen);

typedef struct 
{

	unsigned int	ModeSelector;	//выбор режима - нормальный или X.410-1984
	//unsigned int callingPresentationSelector;
	//unsigned int calledPresentationSelector;
	unsigned char	NextContextId;	//presentation-context-identifier
	unsigned char	AcseContextId;	//ACSE идентификатор контекста
	unsigned char	MmsContextId;	//MMS идентификатор контекста
	int				UserDataPos;	//позиция данных пользователя в общем пакете Presentation
	int				UserDataSize;	//размер данных пользователя	
}Presentation_Connect_Packet;

//Кодирование данных пользователя
//pBuffer					- буфер для кодирования
//iBufPos					- позиция в буфере с которой нужно кодировать
//iUserDataPacketLength		- длина данных пользователя
//ucEncode					- (>0) - кодировать
//ucContextId				- идентификатор контекста
//
//Возвращает				- длину закодированных данных или текущую позицию в буфере
int EncodeUserData( unsigned char* pBuffer, int iBufPos, int iUserDataPacketLength, 
				    unsigned char ucEncode, unsigned char ucContextId );

#endif //PRESENTATION_H_
