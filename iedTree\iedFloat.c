#include "iedFloat.h"

#include "debug.h"
#include "iedFinalDA.h"
#include "iedEntity.h"
#include "iedTree.h"
#include "../DataSlice.h"
#include "../AsnEncoding.h"
#include "../BERCoder.h"

#include "fnan.h"

#define FLOAT_ENCODED_SIZE 7

static bool calcReadLen(IEDEntity entity, size_t* pLen )
{
    *pLen = FLOAT_ENCODED_SIZE;
    return true;
}

//Общая инициализация для FLOAT и REAL
static void commonInit(IEDEntity entity)
{
    TerminalItem* extInfo = entity->extInfo;
    FloatAccsessInfo* accessInfo = extInfo->accessInfo;

    extInfo->f.multiplier = accessInfo->multiplier;

    //Если будет ошибка, то запишется -1;
    entity->dataSliceOffset = DataSlice_getAnalogOffset(accessInfo->valueOffset);

    entity->calcReadLen = calcReadLen;

    IEDTree_addToCmpList(entity);
}

//===================REAL======================

static void realUpdateFromDataSlice(IEDEntity entity)
{
    int offset  = entity->dataSliceOffset;
    float value;

    if(offset == -1)
    {
        return;
    }

    value = DataSlice_getRealFastCurrDS(offset);

    if(entity->realValue == value)
    {
        entity->changed = TRGOP_NONE;
    }
    else
    {
		//Кэш сбрасываем для RealAsInt64
        entity->cached = false;

        entity->changed = entity->trgOps;
        entity->realValue = value;
        IEDEntity_setTimeStamp(entity, dataSliceGetTimeStamp());
    }
}

static bool realEncodeRead(IEDEntity entity, BufferView* outBuf)
{
    uint8_t* encodeBuf;
    TerminalItem* extInfo = entity->extInfo;
    float value = entity->realValue;

    if(!isfnan(value))
    {
        value *= extInfo->f.multiplier;
    }

    if(!BufferView_alloc(outBuf,FLOAT_ENCODED_SIZE, &encodeBuf))
    {
        ERROR_REPORT("Unable to allocate buffer");
        return false;
    }

    // Возвращаемое значение не нужно,
    // потому что функция не возвращает ошибки, а размер известен заранее
    BerEncoder_EncodeFloatWithTL(0x87, value, 32, 8, encodeBuf, 0);
    outBuf->pos += FLOAT_ENCODED_SIZE;
    return true;
}

void IEDReal_init(IEDEntity entity)
{
    commonInit(entity);
    entity->encodeRead = realEncodeRead;
    entity->updateFromDataSlice = realUpdateFromDataSlice;
}

//===================REAL AS INT64======================
static void realAsInt64FillCache(IEDEntity entity)
{
    TerminalItem* extInfo = entity->extInfo;
    int64_t intValue;
    if(isfnan(entity->realValue))
    {
        intValue = -1;
    }
    else
    {
        float realValue = entity->realValue * extInfo->f.multiplier;
        intValue = (uint64_t)realValue;
    }
    entity->cache.int64Value = intValue;
    entity->cachedBERLen =
        BERCoder_calcIntEncodedLen(&intValue, sizeof(intValue));
    entity->cached = true;
}

static bool realAsInt64CalcReadLen(IEDEntity entity, size_t* pLen )
{
    if(!entity->cached)
    {
        realAsInt64FillCache(entity);
    }
    *pLen = entity->cachedBERLen + 2;
    return true;
}

static bool realAsInt64EncodeRead(IEDEntity entity, BufferView* outBuf)
{
    if(!entity->cached)
    {
        realAsInt64FillCache(entity);
    }

    if(!BufferView_encodeTL(outBuf, IEC61850_BER_INTEGER, entity->cachedBERLen))
    {
        ERROR_REPORT("Uable to write TL");
        return false;
    }

    if(!BufferView_reverseWrite(outBuf, &entity->cache.int64Value,
        entity->cachedBERLen))
    {
        ERROR_REPORT("Uable to write int64 value");
        return false;
    }

    return true;
}

void IEDRealAsInt64_init(IEDEntity entity)
{
    commonInit(entity);
    entity->calcReadLen = realAsInt64CalcReadLen;
    entity->encodeRead = realAsInt64EncodeRead;
    entity->updateFromDataSlice = realUpdateFromDataSlice;
}

//===================FLOAT======================
static void floatUpdateFromDataSlice(IEDEntity entity)
{
    int offset  = entity->dataSliceOffset;
    int value;

    if(offset == -1)
    {
        return;
    }

    value = DataSlice_getFixedFastCurrDS(offset);

    if(entity->fixedValue == value)
    {
        entity->changed = TRGOP_NONE;
    }
    else
    {
        entity->changed = entity->trgOps;
        entity->fixedValue = value;
        IEDEntity_setTimeStamp(entity, dataSliceGetTimeStamp());
    }
}

static bool floatEncodeRead(IEDEntity entity, BufferView* outBuf)
{
    uint8_t* encodeBuf;
    TerminalItem* extInfo = entity->extInfo;
    int fixedValue = entity->fixedValue;
    float floatValue;

    if(fixedValue != 0x7FFFFFFF)
    {
		double doubleValue = fixedValue;        
        doubleValue *= extInfo->f.multiplier;
		floatValue = (float)doubleValue;
    }
    else
    {
        floatValue = fnan(0);
    }

    if(!BufferView_alloc(outBuf,FLOAT_ENCODED_SIZE, &encodeBuf))
    {
        ERROR_REPORT("Unable to allocate buffer");
        return false;
    }

    // Возвращаемое значение не нужно,
    // потому что функция не возвращает ошибки, а размер известен заранее
    BerEncoder_EncodeFloatWithTL(0x87, floatValue, 32, 8, encodeBuf, 0);
    outBuf->pos += FLOAT_ENCODED_SIZE;
    return true;
}

void IEDFloat_init(IEDEntity entity)
{
    commonInit(entity);
    entity->encodeRead = floatEncodeRead;
    entity->updateFromDataSlice = floatUpdateFromDataSlice;
}

