                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2h41.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=mms.c -o gh_2h41.o -list=mms.lst C:\Users\<USER>\AppData\Local\Temp\gh_2h41.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_2h41.s
Source File: mms.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile mms.c -o mms.o

                      10 ;Source File:   mms.c

                      11 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      12 ;Compile Date:  Tue Oct 07 09:39:34 2025

                      13 ;Host OS:       Win32

                      14 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      15 ;Release:       MULTI v4.2.3

                      16 ;Revision Date: Wed Mar 29 05:25:47 2006

                      17 ;Release Date:  Fri Mar 31 10:02:14 2006

                      18 

                      19 ;1: 


                      20 ;2: #include "stddef.h"


                      21 ;3: #include "server.h"


                      22 ;4: #include "platform_thread.h"


                      23 ;5: #include "Cotp.h"


                      24 ;6: #include "acse.h"


                      25 ;7: #include "session.h"


                      26 ;8: #include "mms.h"


                      27 ;9: #include "send_thread.h"


                      28 ;10: #include "AsnEncoding.h"  


                      29 ;11: #include "mms_get_name_list.h"


                      30 ;12: #include "mms_get_variable_access_attributes.h"


                      31 ;13: #include "mmsservices.h"


                      32 ;14: #include "mms_read.h"


                      33 ;15: #include "file_system.h"


                      34 ;16: #include "out_queue.h"


                      35 ;17: #include "out_buffers.h"


                      36 ;18: #include "reports.h"


                      37 ;19: #include "connections.h"


                      38 ;20: #include "control.h"


                      39 ;21: #include <debug.h>


                      40 ;22: #include <Clib.h>


                      41 ;23: 


                      42 ;24: //Типы тегов Ассоциации MMS


                      43 ;25: #define	MMS_LOCAL_DETAIL_CALLING	 0x80


                      44 ;26: #define MMS_MAX_SERV_OUTSTANDING_CALLING 0x81


                      45 ;27: #define MMS_MAX_SERV_OUTSTANDING_CALLED 0x82


                      46 ;28: #define MMS_DATA_STRUCTURE_NESTING_LEVEL 0x83


                      47 ;29: #define	MMS_INIT_REQUEST_DETAIL 0xa4


                      48 ;30: #define	MMS_INIT_RESPONSE_DETAIL 0xa4


                      49 ;31: #define MMS_VERSION_NUMBER 0x80


                      50 ;32: #define MMS_PARAMETER_CBB 0x81



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2h41.s
                      51 ;33: #define MMS_SERVICES_SUPPORTED 0x82


                      52 ;34: 


                      53 ;35: /**********************************************************************************************


                      54 ;36:  * MMS Server Capabilities


                      55 ;37:  *********************************************************************************************/


                      56 ;38: 


                      57 ;39: #define MMS_SERVICE_STATUS 0x80


                      58 ;40: #define MMS_SERVICE_GET_NAME_LIST 0x40


                      59 ;41: #define MMS_SERVICE_IDENTIFY 0x20


                      60 ;42: #define MMS_SERVICE_RENAME 0x10


                      61 ;43: #define MMS_SERVICE_READ 0x08


                      62 ;44: #define MMS_SERVICE_WRITE 0x04


                      63 ;45: #define MMS_SERVICE_GET_VARIABLE_ACCESS_ATTRIBUTES 0x02


                      64 ;46: #define MMS_SERVICE_DEFINE_NAMED_VARIABLE 0x01


                      65 ;47: 


                      66 ;48: #define MMS_SERVICE_DEFINE_SCATTERED_ACCESS 0x80


                      67 ;49: #define MMS_SERVICE_GET_SCATTERED_ACCESS_ATTRIBUTES 0x40


                      68 ;50: #define MMS_SERVICE_DELETE_VARIABLE_ACCESS 0x20


                      69 ;51: #define MMS_SERVICE_DEFINE_NAMED_VARIABLE_LIST 0x10


                      70 ;52: #define MMS_SERVICE_GET_NAMED_VARIABLE_LIST_ATTRIBUTES 0x08


                      71 ;53: #define MMS_SERVICE_DELETE_NAMED_VARIABLE_LIST 0x04


                      72 ;54: #define MMS_SERVICE_DEFINE_NAMED_TYPE 0x02


                      73 ;55: #define MMS_SERVICE_GET_NAMED_TYPE_ATTRIBUTES 0x01


                      74 ;56: 


                      75 ;57: #define MMS_SERVICE_OBTAIN_FILE 0x02


                      76 ;58: 


                      77 ;59: #define MMS_SERVICE_READ_JOURNAL 0x40


                      78 ;60: 


                      79 ;61: #define MMS_SERVICE_FILE_OPEN 0x80


                      80 ;62: #define MMS_SERVICE_FILE_READ 0x40


                      81 ;63: #define MMS_SERVICE_FILE_CLOSE 0x20


                      82 ;64: #define MMS_SERVICE_FILE_RENAME 0x01


                      83 ;65: #define MMS_SERVICE_FILE_DELETE 0x08


                      84 ;66: #define MMS_SERVICE_FILE_DIRECTORY 0x04


                      85 ;67: #define MMS_SERVICE_UNSOLICITED_STATUS 0x02


                      86 ;68: #define MMS_SERVICE_INFORMATION_REPORT 0x01


                      87 ;69: 


                      88 ;70: #define MMS_SERVICE_CONCLUDE 0x10


                      89 ;71: #define MMS_SERVICE_CANCEL 0x08


                      90 ;72: 


                      91 ;73: 


                      92 ;74: // servicesSupported MMS bitstring


                      93 ;75: static unsigned char servicesSupported[] =


                      94 ;76: {


                      95 ;77:         0x00


                      96 ;78:         | MMS_SERVICE_STATUS


                      97 ;79:         | MMS_SERVICE_GET_NAME_LIST    


                      98 ;80:         | MMS_SERVICE_IDENTIFY


                      99 ;81:         | MMS_SERVICE_READ


                     100 ;82:         | MMS_SERVICE_WRITE


                     101 ;83:         | MMS_SERVICE_GET_VARIABLE_ACCESS_ATTRIBUTES


                     102 ;84:         ,


                     103 ;85:         0x00


                     104 ;86:         //| MMS_SERVICE_DEFINE_NAMED_VARIABLE_LIST


                     105 ;87:         //| MMS_SERVICE_DELETE_NAMED_VARIABLE_LIST


                     106 ;88:         | MMS_SERVICE_GET_NAMED_VARIABLE_LIST_ATTRIBUTES


                     107 ;89:         ,


                     108 ;90:         0x00,


                     109 ;91:         0x00,


                     110 ;92:         0x00,


                     111 ;93:         0x00



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2h41.s
                     112 ;94:         //| MMS_SERVICE_OBTAIN_FILE


                     113 ;95:         ,


                     114 ;96:         0x00,


                     115 ;97:         0x00,


                     116 ;98:         0x00


                     117 ;99:         //| MMS_SERVICE_READ_JOURNAL


                     118 ;100:         ,


                     119 ;101:         0x00


                     120 ;102:         


                     121 ;103:         | MMS_SERVICE_FILE_OPEN


                     122 ;104:         | MMS_SERVICE_FILE_READ


                     123 ;105:         | MMS_SERVICE_FILE_CLOSE


                     124 ;106:         //| MMS_SERVICE_FILE_RENAME


                     125 ;107:         //| MMS_SERVICE_FILE_DELETE


                     126 ;108:         | MMS_SERVICE_FILE_DIRECTORY


                     127 ;109:         


                     128 ;110:         | MMS_SERVICE_INFORMATION_REPORT


                     129 ;111:         ,


                     130 ;112:         0x00


                     131 ;113:         | MMS_SERVICE_CONCLUDE


                     132 ;114:         | MMS_SERVICE_CANCEL


                     133 ;115: };


                     134 ;116: 


                     135 ;117: /* negotiated parameter CBB */


                     136 ;118: static unsigned char parameterCBB[] =


                     137 ;119: {


                     138 ;120:         0xf1,


                     139 ;121:         0x00


                     140 ;122: };


                     141 ;123: 


                     142 ;124: static int encodeInitResponseDetail(unsigned char* buf, int bufPos, int encode)


                     143 ;125: {


                     144 ;126:     int initResponseDetailSize = 14 + 5 + 3;


                     145 ;127:     if (!encode)


                     146 ;128:         return initResponseDetailSize + 2;


                     147 ;129:     bufPos = BerEncoder_encodeTL(MMS_INIT_RESPONSE_DETAIL, initResponseDetailSize,


                     148 ;130:                                  buf, bufPos);


                     149 ;131:     bufPos = BerEncoder_encodeUInt32WithTL(MMS_VERSION_NUMBER, 1, buf, bufPos);


                     150 ;132:     bufPos = BerEncoder_encodeBitString(MMS_PARAMETER_CBB, 11, parameterCBB,


                     151 ;133:                                         buf, bufPos);


                     152 ;134:     bufPos = BerEncoder_encodeBitString(MMS_SERVICES_SUPPORTED, 85,


                     153 ;135:                                         servicesSupported, buf, bufPos);


                     154 ;136: 


                     155 ;137:     return bufPos;


                     156 ;138: }


                     157 ;139: 


                     158 ;140: static void mms_initConnection(MmsConnection* mmsConn)


                     159 

                     160 ;143: }


                     161 

                     162 ;144: 


                     163 ;145: static void mms_closeConnection(MmsConnection* mmsConn)


                     164 

                     165 ;150: 	}


                     166 ;151: }


                     167 

                     168 ;152: 


                     169 ;153: 


                     170 ;154: static void initIsoConnection(IsoConnection* isoConn)


                     171 

                     172 ;166: }



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2h41.s
                     173 

                     174 ;167: 


                     175 ;168: static int createInitiateResponse(IsoConnection* isoConn, unsigned char* buf)


                     176 

                     177 ;205: }


                     178 

                     179 	.text

                     180 	.align	4

                     181 encodeInitResponseDetail:

00000000 e92d4030    182 	stmfd	[sp]!,{r4-r5,lr}

00000004 e24dd004    183 	sub	sp,sp,4

00000008 e59f5278*   184 	ldr	r5,.L118

0000000c e3520000    185 	cmp	r2,0

00000010 03a00018    186 	moveq	r0,24

00000014 0a000016    187 	beq	.L69

00000018 e1a04000    188 	mov	r4,r0

0000001c e1a02004    189 	mov	r2,r4

00000020 e1a03001    190 	mov	r3,r1

00000024 e3a01016    191 	mov	r1,22

00000028 e3a000a4    192 	mov	r0,164

0000002c eb000000*   193 	bl	BerEncoder_encodeTL

00000030 e1a02004    194 	mov	r2,r4

00000034 e3a01001    195 	mov	r1,1

00000038 e1a03000    196 	mov	r3,r0

0000003c e3a00080    197 	mov	r0,128

00000040 eb000000*   198 	bl	BerEncoder_encodeUInt32WithTL

00000044 e1a03004    199 	mov	r3,r4

00000048 e1a02005    200 	mov	r2,r5

0000004c e3a0100b    201 	mov	r1,11

00000050 e58d0000    202 	str	r0,[sp]

00000054 e3a00081    203 	mov	r0,129

00000058 eb000000*   204 	bl	BerEncoder_encodeBitString

0000005c e1a03004    205 	mov	r3,r4

00000060 e2852004    206 	add	r2,r5,4

00000064 e3a01055    207 	mov	r1,85

00000068 e58d0000    208 	str	r0,[sp]

0000006c e3a00082    209 	mov	r0,130

00000070 eb000000*   210 	bl	BerEncoder_encodeBitString

                     211 .L69:

00000074 e28dd004    212 	add	sp,sp,4

00000078 e8bd4030    213 	ldmfd	[sp]!,{r4-r5,lr}

0000007c e12fff1e*   214 	ret	

                     215 	.endf	encodeInitResponseDetail

                     216 	.align	4

                     217 

                     218 ;buf	r4	param

                     219 ;bufPos	r1	param

                     220 ;encode	r2	param

                     221 

                     222 	.data

                     223 .L102:

                     224 .L103:

00000000 f1         225 parameterCBB:	.data.b	241

00000001 00         226 	.space	1

                     227 	.type	parameterCBB,$object

                     228 	.size	parameterCBB,2

00000002 0000       229 	.space	2

                     230 .L104:

00000004 08ee       231 servicesSupported:	.data.b	238,8

00000006 00         232 	.space	1

00000007 00         233 	.space	1


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2h41.s
00000008 00         234 	.space	1

00000009 00         235 	.space	1

0000000a 00         236 	.space	1

0000000b 00         237 	.space	1

0000000c 00         238 	.space	1

0000000d 18e5       239 	.data.b	229,24

0000000f 00         240 	.space	1

                     241 	.type	servicesSupported,$object

                     242 	.size	servicesSupported,12

                     243 	.text

                     244 

                     245 

                     246 ;206: 


                     247 ;207: int processSessionConnect(IsoConnection* isoConn)


                     248 	.align	4

                     249 	.align	4

                     250 processSessionConnect::

00000080 e92d4070    251 	stmfd	[sp]!,{r4-r6,lr}

00000084 e1a04000    252 	mov	r4,r0

                     253 ;208: {


                     254 

                     255 ;209:     int acseDataLen;


                     256 ;210:     int presentationDataLen;


                     257 ;211:     int mmsDataLen;


                     258 ;212: 


                     259 ;213: 


                     260 ;214: 


                     261 ;215:     debugSendText("Send Accept SPDU");


                     262 

00000088 e28f0000*   263 	adr	r0,.L165

0000008c e24dd004    264 	sub	sp,sp,4

00000090 eb000000*   265 	bl	debugSendText

                     266 ;216:     mmsDataLen = createInitiateResponse( isoConn, isoConn->isoOutBuf);


                     267 

                     268 ;169: {


                     269 

                     270 ;170:     int bufPos = 0;


                     271 

                     272 ;171:     int initiateResponseLength = 0;


                     273 

                     274 ;172: 


                     275 ;173:     initiateResponseLength += 2 + BerEncoder_UInt32determineEncodedSize(


                     276 

00000094 e594000c    277 	ldr	r0,[r4,12]

00000098 eb000000*   278 	bl	BerEncoder_UInt32determineEncodedSize

0000009c e2805002    279 	add	r5,r0,2

                     280 ;174:                 isoConn->maxPduSize);


                     281 ;175:     initiateResponseLength += 2 + BerEncoder_UInt32determineEncodedSize(


                     282 

000000a0 e5940000    283 	ldr	r0,[r4]

000000a4 eb000000*   284 	bl	BerEncoder_UInt32determineEncodedSize

000000a8 e0800005    285 	add	r0,r0,r5

000000ac e2805002    286 	add	r5,r0,2

                     287 ;176:                 isoConn->maxServOutstandingCalling);


                     288 ;177:     initiateResponseLength += 2 + BerEncoder_UInt32determineEncodedSize(


                     289 

000000b0 e5940004    290 	ldr	r0,[r4,4]

000000b4 eb000000*   291 	bl	BerEncoder_UInt32determineEncodedSize

000000b8 e0800005    292 	add	r0,r0,r5

000000bc e2805002    293 	add	r5,r0,2

                     294 ;178:                 isoConn->maxServOutstandingCalled);



                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2h41.s
                     295 ;179:     initiateResponseLength += 2 + BerEncoder_UInt32determineEncodedSize(


                     296 

000000c0 e5940008    297 	ldr	r0,[r4,8]

000000c4 eb000000*   298 	bl	BerEncoder_UInt32determineEncodedSize

000000c8 e3a02000    299 	mov	r2,0

000000cc e1a01002    300 	mov	r1,r2

000000d0 e0800005    301 	add	r0,r0,r5

000000d4 e2805002    302 	add	r5,r0,2

                     303 ;180:                 isoConn->dataStructureNestingLevel);


                     304 ;181: 


                     305 ;182:     initiateResponseLength += encodeInitResponseDetail(NULL, 0, 0);


                     306 

000000d8 e1a00002    307 	mov	r0,r2

000000dc ebffffc7*   308 	bl	encodeInitResponseDetail

000000e0 e0851000    309 	add	r1,r5,r0

                     310 ;183: 


                     311 ;184:     /* Initiate response pdu */


                     312 ;185:     bufPos = BerEncoder_encodeTL(MMS_INITIATE_RESPONSE_PDU,


                     313 

000000e4 e3a05fd7    314 	mov	r5,0x035c

000000e8 e2855a50    315 	add	r5,r5,5<<16

000000ec e0842005    316 	add	r2,r4,r5

000000f0 e3a03000    317 	mov	r3,0

000000f4 e3a000a9    318 	mov	r0,169

000000f8 eb000000*   319 	bl	BerEncoder_encodeTL

                     320 ;186:                                  initiateResponseLength, buf, bufPos);


                     321 ;187: 


                     322 ;188:     bufPos = BerEncoder_encodeUInt32WithTL(MMS_LOCAL_DETAIL_CALLING,


                     323 

000000fc e0842005    324 	add	r2,r4,r5

00000100 e1a03000    325 	mov	r3,r0

00000104 e594100c    326 	ldr	r1,[r4,12]

00000108 e3a00080    327 	mov	r0,128

0000010c eb000000*   328 	bl	BerEncoder_encodeUInt32WithTL

                     329 ;189:                                            isoConn->maxPduSize, buf, bufPos);


                     330 ;190: 


                     331 ;191:     bufPos = BerEncoder_encodeUInt32WithTL(MMS_MAX_SERV_OUTSTANDING_CALLING,


                     332 

00000110 e0842005    333 	add	r2,r4,r5

00000114 e1a03000    334 	mov	r3,r0

00000118 e5941000    335 	ldr	r1,[r4]

0000011c e3a00081    336 	mov	r0,129

00000120 eb000000*   337 	bl	BerEncoder_encodeUInt32WithTL

                     338 ;192:                                            isoConn->maxServOutstandingCalling,


                     339 ;193:                                            buf, bufPos);


                     340 ;194: 


                     341 ;195:     bufPos = BerEncoder_encodeUInt32WithTL(MMS_MAX_SERV_OUTSTANDING_CALLED,


                     342 

00000124 e0842005    343 	add	r2,r4,r5

00000128 e1a03000    344 	mov	r3,r0

0000012c e5941004    345 	ldr	r1,[r4,4]

00000130 e3a00082    346 	mov	r0,130

00000134 eb000000*   347 	bl	BerEncoder_encodeUInt32WithTL

                     348 ;196:                                            isoConn->maxServOutstandingCalled,


                     349 ;197:                                            buf, bufPos);


                     350 ;198: 


                     351 ;199:     bufPos = BerEncoder_encodeUInt32WithTL(MMS_DATA_STRUCTURE_NESTING_LEVEL,


                     352 

00000138 e0842005    353 	add	r2,r4,r5

0000013c e1a03000    354 	mov	r3,r0

00000140 e5941008    355 	ldr	r1,[r4,8]


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2h41.s
00000144 e3a00083    356 	mov	r0,131

00000148 eb000000*   357 	bl	BerEncoder_encodeUInt32WithTL

                     358 ;200:                                            isoConn->dataStructureNestingLevel,


                     359 ;201:                                            buf, bufPos);


                     360 ;202: 


                     361 ;203:     bufPos = encodeInitResponseDetail(buf, bufPos, 1);


                     362 

0000014c e1a01000    363 	mov	r1,r0

00000150 e0840005    364 	add	r0,r4,r5

00000154 e3a02001    365 	mov	r2,1

00000158 ebffffa8*   366 	bl	encodeInitResponseDetail

                     367 ;204:     return bufPos;


                     368 

                     369 ;217:     acseDataLen = AcseConnection_createAssociateResponseMessage(


                     370 

0000015c e0842005    371 	add	r2,r4,r5

00000160 e2841db7    372 	add	r1,r4,0x2dc0

00000164 e2816a40    373 	add	r6,r1,1<<18

00000168 e3a01000    374 	mov	r1,0

0000016c e58d1000    375 	str	r1,[sp]

00000170 e2861004    376 	add	r1,r6,4

00000174 e1a03000    377 	mov	r3,r0

00000178 e1a00006    378 	mov	r0,r6

0000017c eb000000*   379 	bl	AcseConnection_createAssociateResponseMessage

                     380 ;218:                 &isoConn->acse,  isoConn->acse.outBuf, isoConn->isoOutBuf,


                     381 ;219:                 mmsDataLen, ACSE_RESULT_ACCEPT);


                     382 ;220: 


                     383 ;221:     presentationDataLen = isoPresentation_createCpaMessage(&isoConn->presentation,


                     384 

00000180 e2862004    385 	add	r2,r6,4

00000184 e2841edb    386 	add	r1,r4,0x0db0

00000188 e2815bf0    387 	add	r5,r1,15<<14

0000018c e285100c    388 	add	r1,r5,12

00000190 e1a03000    389 	mov	r3,r0

00000194 e1a00005    390 	mov	r0,r5

00000198 eb000000*   391 	bl	isoPresentation_createCpaMessage

0000019c e1a06000    392 	mov	r6,r0

                     393 ;222:                                  isoConn->presentation.outBuf,isoConn->acse.outBuf,


                     394 ;223:                                                        acseDataLen);    


                     395 ;224: 


                     396 ;225:     isoConn->pCurrCotpOutBuf = allocSessionOutBuffer(&isoConn->outBuffers,


                     397 

000001a0 e2840e80    398 	add	r0,r4,1<<11

000001a4 e2800018    399 	add	r0,r0,24

000001a8 e3a01c60    400 	mov	r1,3<<13

000001ac eb000000*   401 	bl	allocSessionOutBuffer

000001b0 e3500000    402 	cmp	r0,0

                     403 ;228:     {


                     404 

                     405 ;229:         ERROR_REPORT("No free buffer to send");


                     406 ;230:         return -1;


                     407 

000001b4 11a02006    408 	movne	r2,r6

000001b8 1285100c    409 	addne	r1,r5,12

000001bc e5840814    410 	str	r0,[r4,2068]

                     411 ;226: 		SESSION_OUT_BUF_SIZE);


                     412 ;227:     if(isoConn->pCurrCotpOutBuf == NULL)


                     413 

000001c0 03e00000    414 	mvneq	r0,0

                     415 ;231:     }


                     416 ;232:     return createAcceptSPDU( isoConn->pCurrCotpOutBuf->cotpOutBuf,



                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2h41.s
                     417 

000001c4 12800008    418 	addne	r0,r0,8

000001c8 1b000000*   419 	blne	createAcceptSPDU

000001cc e28dd004    420 	add	sp,sp,4

000001d0 e8bd8070    421 	ldmfd	[sp]!,{r4-r6,pc}

                     422 	.endf	processSessionConnect

                     423 	.align	4

                     424 ;presentationDataLen	r6	local

                     425 ;.L151	.L154	static

                     426 ;initiateResponseLength	r5	local

                     427 

                     428 ;isoConn	r4	param

                     429 

                     430 	.section ".bss","awb"

                     431 .L150:

                     432 	.data

                     433 	.text

                     434 

                     435 ;233:                                           isoConn->presentation.outBuf,presentationDataLen);


                     436 ;234: }


                     437 

                     438 ;235: 


                     439 ;236: //outBuf - куда складывать результат


                     440 ;237: //pOutLen - куда складывать длину результата


                     441 ;238: MmsIndication mmsProcessMessage(IsoConnection* isoConn,


                     442 	.align	4

                     443 	.align	4

                     444 mmsProcessMessage::

000001d4 e92d4cf0    445 	stmfd	[sp]!,{r4-r7,r10-fp,lr}

                     446 ;239: 								unsigned char* inBuf, int* pRequestPDULen, int inLen,


                     447 ;240:                                 unsigned char* outBuf, int* pOutLen)


                     448 ;241: {


                     449 

000001d8 e1a0b000    450 	mov	fp,r0

000001dc e1a04001    451 	mov	r4,r1

000001e0 e24dd00c    452 	sub	sp,sp,12

000001e4 e59d7028    453 	ldr	r7,[sp,40]

000001e8 e59da02c    454 	ldr	r10,[sp,44]

000001ec e1a06002    455 	mov	r6,r2

000001f0 e3a02000    456 	mov	r2,0

                     457 ;242:     //смотри ParseMmsPacket


                     458 ;243:     //       и MmsServerConnection_parseMessage


                     459 ;244:     MmsIndication retVal;


                     460 ;245:     int bufPos = 0;


                     461 

                     462 ;246:     unsigned char pduType;


                     463 ;247:     int pduLength;


                     464 ;248: 


                     465 ;249:     if (inLen < 2)


                     466 

000001f4 e3530002    467 	cmp	r3,2

000001f8 ba000006    468 	blt	.L172

                     469 ;250:     {


                     470 

                     471 ;251:         return MMS_ERROR;


                     472 

                     473 ;252:     }


                     474 ;253: 


                     475 ;254:     pduType = inBuf[bufPos++];


                     476 

000001fc e28d1008    477 	add	r1,sp,8


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2h41.s
00000200 e1a00004    478 	mov	r0,r4

00000204 e7d45002    479 	ldrb	r5,[r4,r2]

                     480 ;255:     bufPos = BerDecoder_decodeLength(inBuf, &pduLength, bufPos, inLen);


                     481 

00000208 e3a02001    482 	mov	r2,1

0000020c eb000000*   483 	bl	BerDecoder_decodeLength

00000210 e1b02000    484 	movs	r2,r0

                     485 ;256: 


                     486 ;257:     if (bufPos < 0)


                     487 

00000214 5a000001    488 	bpl	.L171

                     489 .L172:

                     490 ;258:     {


                     491 

                     492 ;259:         return MMS_ERROR;


                     493 

00000218 e3a00000    494 	mov	r0,0

0000021c ea000017    495 	b	.L166

                     496 .L171:

                     497 ;260:     }


                     498 ;261: 


                     499 ;262: 	//Полная длина всего PDU вместе с тэгом и длиной


                     500 ;263: 	*pRequestPDULen = bufPos + pduLength;


                     501 

00000220 e59d1008    502 	ldr	r1,[sp,8]

00000224 e0810002    503 	add	r0,r1,r2

00000228 e5860000    504 	str	r0,[r6]

                     505 ;264: 


                     506 ;265:     switch (pduType) {


                     507 

0000022c e25500a0    508 	subs	r0,r5,160

00000230 0a000005    509 	beq	.L177

00000234 e3500008    510 	cmp	r0,8

00000238 1a00000c    511 	bne	.L178

                     512 ;266:     case MMS_INITIATE_REQUEST_PDU:


                     513 ;267:         debugSendText("MMS_INITIATE_REQUEST_PDU");        


                     514 

0000023c e28f0000*   515 	adr	r0,.L283

00000240 eb000000*   516 	bl	debugSendText

                     517 ;268:         retVal = MMS_INITIATE;


                     518 

00000244 e3a00001    519 	mov	r0,1

                     520 ;280:         break;


                     521 ;281:     }


                     522 ;282:     return retVal;


                     523 

00000248 ea00000c    524 	b	.L166

                     525 .L177:

                     526 ;269:         break;


                     527 ;270:     case MMS_CONFIRMED_REQUEST_PDU:


                     528 ;271:         //debugSendText("MMS_CONFIRMED_REQUEST_PDU");		


                     529 ;272:         *pOutLen = handleConfirmedRequestPdu(isoConn, inBuf, bufPos, bufPos + pduLength,


                     530 

0000024c e3a0cc60    531 	mov	r12,3<<13

00000250 e88d1080    532 	stmea	[sp],{r7,r12}

00000254 e0813002    533 	add	r3,r1,r2

00000258 e1a01004    534 	mov	r1,r4

0000025c e1a0000b    535 	mov	r0,fp

00000260 eb000000*   536 	bl	handleConfirmedRequestPdu

00000264 e58a0000    537 	str	r0,[r10]

                     538 ;273:                                             outBuf, DEFAULT_BUFFER_SIZE);



                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2h41.s
                     539 ;274:         retVal = MMS_CONFIRMED_REQUEST;


                     540 

00000268 e3a00002    541 	mov	r0,2

                     542 ;280:         break;


                     543 ;281:     }


                     544 ;282:     return retVal;


                     545 

0000026c ea000003    546 	b	.L166

                     547 .L178:

                     548 ;275:         break;


                     549 ;276:     default:


                     550 ;277:         //mmsMsg_createMmsRejectPdu(NULL, MMS_ERROR_REJECT_UNKNOWN_PDU_TYPE, response);


                     551 ;278:         debugSendUshort("Unknown MMS PDU type ", pduType);


                     552 

00000270 e28f0000*   553 	adr	r0,.L284

00000274 e1a01005    554 	mov	r1,r5

00000278 eb000000*   555 	bl	debugSendUshort

                     556 ;279:         retVal = MMS_ERROR;


                     557 

0000027c e3a00000    558 	mov	r0,0

                     559 ;280:         break;


                     560 ;281:     }


                     561 ;282:     return retVal;


                     562 

                     563 .L166:

00000280 e28dd00c    564 	add	sp,sp,12

00000284 e8bd8cf0    565 	ldmfd	[sp]!,{r4-r7,r10-fp,pc}

                     566 	.endf	mmsProcessMessage

                     567 	.align	4

                     568 .L118:

00000288 00000000*   569 	.data.w	.L102

                     570 	.type	.L118,$object

                     571 	.size	.L118,4

                     572 

                     573 .L165:

                     574 ;	"Send Accept SPDU\000"

0000028c 646e6553    575 	.data.b	83,101,110,100

00000290 63634120    576 	.data.b	32,65,99,99

00000294 20747065    577 	.data.b	101,112,116,32

00000298 55445053    578 	.data.b	83,80,68,85

0000029c 00         579 	.data.b	0

0000029d 000000     580 	.align 4

                     581 

                     582 	.type	.L165,$object

                     583 	.size	.L165,4

                     584 

                     585 .L283:

                     586 ;	"MMS_INITIATE_REQUEST_PDU\000"

000002a0 5f534d4d    587 	.data.b	77,77,83,95

000002a4 54494e49    588 	.data.b	73,78,73,84

000002a8 45544149    589 	.data.b	73,65,84,69

000002ac 5145525f    590 	.data.b	95,82,69,81

000002b0 54534555    591 	.data.b	85,69,83,84

000002b4 5544505f    592 	.data.b	95,80,68,85

000002b8 00         593 	.data.b	0

000002b9 000000     594 	.align 4

                     595 

                     596 	.type	.L283,$object

                     597 	.size	.L283,4

                     598 

                     599 .L284:


                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2h41.s
                     600 ;	"Unknown MMS PDU type \000"

000002bc 6e6b6e55    601 	.data.b	85,110,107,110

000002c0 206e776f    602 	.data.b	111,119,110,32

000002c4 20534d4d    603 	.data.b	77,77,83,32

000002c8 20554450    604 	.data.b	80,68,85,32

000002cc 65707974    605 	.data.b	116,121,112,101

000002d0 0020       606 	.data.b	32,0

000002d2 0000       607 	.align 4

                     608 

                     609 	.type	.L284,$object

                     610 	.size	.L284,4

                     611 

                     612 	.align	4

                     613 ;bufPos	r2	local

                     614 ;pduType	r5	local

                     615 ;pduLength	[sp,8]	local

                     616 ;.L261	.L266	static

                     617 ;.L262	.L265	static

                     618 

                     619 ;isoConn	fp	param

                     620 ;inBuf	r4	param

                     621 ;pRequestPDULen	r6	param

                     622 ;inLen	r3	param

                     623 ;outBuf	r7	param

                     624 ;pOutLen	r10	param

                     625 

                     626 	.section ".bss","awb"

                     627 .L260:

                     628 	.data

                     629 	.text

                     630 

                     631 ;283: }


                     632 

                     633 ;284: 


                     634 ;285: int processSessionData(IsoConnection* isoConn,


                     635 	.align	4

                     636 	.align	4

                     637 processSessionData::

000002d4 e92d4df0    638 	stmfd	[sp]!,{r4-r8,r10-fp,lr}

                     639 ;286:                        unsigned char* inBuf, int inLen)


                     640 ;287: 


                     641 ;288: {	


                     642 

000002d8 e3a04000    643 	mov	r4,0

000002dc e1a07004    644 	mov	r7,r4

000002e0 e1a0b004    645 	mov	fp,r4

                     646 ;289: 	int mmsInPacketLen;


                     647 ;290: 	int mmsInPacketPos = 0;


                     648 

                     649 ;291:     int presentationDataLen;


                     650 ;292: 	int mmsOutDataLen = 0;


                     651 

                     652 ;293:     unsigned char* userData;


                     653 ;294: 	unsigned char* outBuf = isoConn->isoOutBuf;


                     654 

000002e4 e1a0a000    655 	mov	r10,r0

000002e8 e28a0fd7    656 	add	r0,r10,0x035c

000002ec e2805a50    657 	add	r5,r0,5<<16

                     658 ;295: 


                     659 ;296:     MmsIndication mmsInd;


                     660 ;297: 	bool confirmedRequest = false;



                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2h41.s
                     661 

                     662 ;298: 


                     663 ;299:     //debugSendText("processSessionData");


                     664 ;300: 	mmsInPacketLen = isoPresentation_parseUserData(&isoConn->presentation,


                     665 

000002f0 e28a0edb    666 	add	r0,r10,0x0db0

000002f4 e2800bf0    667 	add	r0,r0,15<<14

000002f8 e1a08000    668 	mov	r8,r0

000002fc e24dd018    669 	sub	sp,sp,24

00000300 e28d3008    670 	add	r3,sp,8

00000304 eb000000*   671 	bl	isoPresentation_parseUserData

00000308 e1a06000    672 	mov	r6,r0

                     673 ;301:                                                           inBuf, inLen, &userData);


                     674 ;302: 	if(mmsInPacketLen == -1)


                     675 

0000030c e3760001    676 	cmn	r6,1

00000310 0a000031    677 	beq	.L295

                     678 ;303:     {


                     679 

                     680 ;304:        return -1;


                     681 

                     682 ;305:     }


                     683 ;306: 


                     684 ;307: 	debugSendUshort("mmsPacketLen ", mmsInPacketLen);


                     685 

00000314 e1a01806    686 	mov	r1,r6 lsl 16

00000318 e28f0000*   687 	adr	r0,.L426

0000031c e1a01821    688 	mov	r1,r1 lsr 16

00000320 eb000000*   689 	bl	debugSendUshort

                     690 ;308: 


                     691 ;309: 	//mmsInPacketLen это длина всего сообщения, которая может


                     692 ;310: 	//включать в себя несколько PDU с разными invokeID	


                     693 ;311: 


                     694 ;312: 	while(mmsInPacketPos < mmsInPacketLen)


                     695 

00000324 e1540006    696 	cmp	r4,r6

00000328 aa000011    697 	bge	.L291

                     698 .L292:

                     699 ;313: 	{


                     700 

                     701 ;314: 		int responsePDULen;


                     702 ;315: 		int requestPDULen;


                     703 ;316: 		mmsInd = mmsProcessMessage(isoConn,


                     704 

0000032c e28dc00c    705 	add	r12,sp,12

00000330 e88d1020    706 	stmea	[sp],{r5,r12}

00000334 e1a03006    707 	mov	r3,r6

00000338 e28d2010    708 	add	r2,sp,16

0000033c e59d1008    709 	ldr	r1,[sp,8]

00000340 e1a0000a    710 	mov	r0,r10

00000344 ebffffa2*   711 	bl	mmsProcessMessage

                     712 ;317: 								   userData, &requestPDULen, mmsInPacketLen,


                     713 ;318: 								   outBuf,&responsePDULen);


                     714 ;319: 		if(mmsInd == MMS_CONFIRMED_REQUEST)


                     715 

00000348 e3500002    716 	cmp	r0,2

                     717 ;320: 		{


                     718 

                     719 ;321: 			confirmedRequest = true;


                     720 

0000034c 03a0b001    721 	moveq	fp,1


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2h41.s
                     722 ;322: 		}


                     723 ;323: 


                     724 ;324: 


                     725 ;325: 		userData += requestPDULen;


                     726 

00000350 e28d1008    727 	add	r1,sp,8

00000354 e891000d    728 	ldmfd	[r1],{r0,r2-r3}

00000358 e0877002    729 	add	r7,r7,r2

                     730 ;328: 		outBuf += responsePDULen;


                     731 

0000035c e0800003    732 	add	r0,r0,r3

00000360 e58d0008    733 	str	r0,[sp,8]

                     734 ;326: 		mmsInPacketPos += requestPDULen;


                     735 

00000364 e0855002    736 	add	r5,r5,r2

00000368 e0844003    737 	add	r4,r4,r3

                     738 ;327: 		mmsOutDataLen += responsePDULen;


                     739 

0000036c e1540006    740 	cmp	r4,r6

00000370 baffffed    741 	blt	.L292

                     742 .L291:

                     743 ;329: 	}


                     744 ;330: 


                     745 ;331: 


                     746 ;332: 	if(confirmedRequest)


                     747 

00000374 e35b0000    748 	cmp	fp,0

00000378 0a000017    749 	beq	.L295

                     750 ;333:     {


                     751 

                     752 ;334:         //ACSE_createMessage() ;


                     753 ;335: 		debugSendUshort("MmsResponseLen:", mmsOutDataLen);


                     754 

0000037c e1a01807    755 	mov	r1,r7 lsl 16

00000380 e28f0000*   756 	adr	r0,.L427

00000384 e1a01821    757 	mov	r1,r1 lsr 16

00000388 eb000000*   758 	bl	debugSendUshort

                     759 ;336:         presentationDataLen =  IsoPresentation_createUserData(&isoConn->presentation,


                     760 

0000038c e1a03007    761 	mov	r3,r7

00000390 e28a0fd7    762 	add	r0,r10,0x035c

00000394 e2802a50    763 	add	r2,r0,5<<16

00000398 e1a00008    764 	mov	r0,r8

0000039c e280100c    765 	add	r1,r0,12

000003a0 eb000000*   766 	bl	IsoPresentation_createUserData

000003a4 e1a04000    767 	mov	r4,r0

                     768 ;337: 															  isoConn->presentation.outBuf,isoConn->isoOutBuf, mmsOutDataLen);


                     769 ;338: 


                     770 ;339:         isoConn->pCurrCotpOutBuf = allocSessionOutBuffer(&isoConn->outBuffers,


                     771 

000003a8 e28a0e80    772 	add	r0,r10,1<<11

000003ac e2800018    773 	add	r0,r0,24

000003b0 e3a01c60    774 	mov	r1,3<<13

000003b4 eb000000*   775 	bl	allocSessionOutBuffer

000003b8 e58a0814    776 	str	r0,[r10,2068]

                     777 ;340: 			SESSION_OUT_BUF_SIZE);


                     778 ;341:         if(isoConn->pCurrCotpOutBuf == NULL)


                     779 

000003bc e3500000    780 	cmp	r0,0

000003c0 0a000005    781 	beq	.L295

                     782 ;342:         {



                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2h41.s
                     783 

                     784 ;343:             ERROR_REPORT("No free buffer to send");


                     785 ;344:             return -1;


                     786 

                     787 ;345:         }


                     788 ;346:         return isoSession_createDataSpdu(isoConn->pCurrCotpOutBuf->cotpOutBuf, 


                     789 

000003c4 e1a03004    790 	mov	r3,r4

000003c8 e288200c    791 	add	r2,r8,12

000003cc e2800008    792 	add	r0,r0,8

000003d0 e3a01c60    793 	mov	r1,3<<13

000003d4 eb000000*   794 	bl	isoSession_createDataSpdu

000003d8 ea000000    795 	b	.L285

                     796 .L295:

                     797 ;347:             SESSION_OUT_BUF_SIZE, isoConn->presentation.outBuf,presentationDataLen);


                     798 ;348: 


                     799 ;349:     }


                     800 ;350:     return -1;


                     801 

000003dc e3e00000    802 	mvn	r0,0

                     803 .L285:

000003e0 e28dd018    804 	add	sp,sp,24

000003e4 e8bd8df0    805 	ldmfd	[sp]!,{r4-r8,r10-fp,pc}

                     806 	.endf	processSessionData

                     807 	.align	4

                     808 ;mmsInPacketLen	r6	local

                     809 ;mmsInPacketPos	r4	local

                     810 ;presentationDataLen	r4	local

                     811 ;mmsOutDataLen	r7	local

                     812 ;userData	[sp,8]	local

                     813 ;outBuf	r5	local

                     814 ;confirmedRequest	fp	local

                     815 ;.L391	.L396	static

                     816 ;responsePDULen	[sp,12]	local

                     817 ;requestPDULen	[sp,16]	local

                     818 ;.L392	.L395	static

                     819 

                     820 ;isoConn	r10	param

                     821 ;inBuf	none	param

                     822 ;inLen	none	param

                     823 

                     824 	.section ".bss","awb"

                     825 .L390:

                     826 	.data

                     827 	.text

                     828 

                     829 ;351: }


                     830 

                     831 ;352: 


                     832 ;353: void closeIsoConnection(IsoConnection* isoConn)


                     833 	.align	4

                     834 	.align	4

                     835 closeIsoConnection::

000003e8 e92d4030    836 	stmfd	[sp]!,{r4-r5,lr}

000003ec e1a04000    837 	mov	r4,r0

                     838 ;354: {


                     839 

                     840 ;355: 	COTPConnection* pCotpConn = &isoConn->cotpConn;


                     841 

000003f0 e2840bf2    842 	add	r0,r4,242<<10

000003f4 e280509c    843 	add	r5,r0,156


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2h41.s
                     844 ;356: 	mms_closeConnection(&isoConn->mmsConn);


                     845 

                     846 ;146: {


                     847 

                     848 ;147: 	if (mmsConn->isFileOpen)


                     849 

000003f8 e3a00f8f    850 	mov	r0,0x023c

000003fc e2600a49    851 	rsb	r0,r0,73<<12

00000400 e0840000    852 	add	r0,r4,r0

00000404 e2800c75    853 	add	r0,r0,117<<8

00000408 e5d01093    854 	ldrb	r1,[r0,147]

0000040c e2800090    855 	add	r0,r0,144

00000410 e3510000    856 	cmp	r1,0

                     857 ;148: 	{


                     858 

                     859 ;149: 		fs_fileClose(mmsConn->frsmID);


                     860 

00000414 15900004    861 	ldrne	r0,[r0,4]

00000418 1b000000*   862 	blne	fs_fileClose

                     863 ;357: 	isoConn->connected = FALSE;


                     864 

0000041c e3a00000    865 	mov	r0,0

00000420 e3a01bf2    866 	mov	r1,242<<10

00000424 e2811098    867 	add	r1,r1,152

00000428 e7c10004    868 	strb	r0,[r1,r4]

                     869 ;358: 	disableDisconnectedReports();


                     870 

0000042c eb000000*   871 	bl	disableDisconnectedReports

                     872 ;359: 	Control_disableWaitingObjects();


                     873 

00000430 eb000000*   874 	bl	Control_disableWaitingObjects

                     875 ;360:     while(isoConn->sendThreadIsRunning)


                     876 

00000434 e3a01bf2    877 	mov	r1,242<<10

00000438 e2811099    878 	add	r1,r1,153

0000043c e7d10004    879 	ldrb	r0,[r1,r4]

00000440 e3500000    880 	cmp	r0,0

00000444 0a00000e    881 	beq	.L436

                     882 .L437:

                     883 ;361:     {


                     884 

                     885 ;362:         Idle();


                     886 

00000448 ea000007    887 	b	.L512

                     888 	.align	4

                     889 .L426:

                     890 ;	"mmsPacketLen \000"

0000044c 50736d6d    891 	.data.b	109,109,115,80

00000450 656b6361    892 	.data.b	97,99,107,101

00000454 6e654c74    893 	.data.b	116,76,101,110

00000458 0020       894 	.data.b	32,0

0000045a 0000       895 	.align 4

                     896 

                     897 	.type	.L426,$object

                     898 	.size	.L426,4

                     899 

                     900 .L427:

                     901 ;	"MmsResponseLen:\000"

0000045c 52736d4d    902 	.data.b	77,109,115,82

00000460 6f707365    903 	.data.b	101,115,112,111

00000464 4c65736e    904 	.data.b	110,115,101,76


                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2h41.s
00000468 003a6e65    905 	.data.b	101,110,58,0

                     906 	.align 4

                     907 

                     908 	.type	.L427,$object

                     909 	.size	.L427,4

                     910 

                     911 .L512:

                     912 

0000046c e6000010    913 	.word	0xE6000010

                     914 

00000470 e3a01bf2    915 	mov	r1,242<<10

00000474 e2811099    916 	add	r1,r1,153

00000478 e7d10004    917 	ldrb	r0,[r1,r4]

0000047c e3500000    918 	cmp	r0,0

00000480 1afffff0    919 	bne	.L437

                     920 .L436:

                     921 ;363:     }


                     922 ;364: 	closeServerSocket(pCotpConn->socket);


                     923 

00000484 e5950000    924 	ldr	r0,[r5]

00000488 eb000000*   925 	bl	closeServerSocket

                     926 ;365: 	ERROR_REPORT("Connection closed");


                     927 ;366: 	


                     928 ;367: 	OutQueue_done(&isoConn->outQueue);


                     929 

0000048c e2840bf2    930 	add	r0,r4,242<<10

00000490 e280006c    931 	add	r0,r0,108

00000494 eb000000*   932 	bl	OutQueue_done

                     933 ;368:     SessionBuffers_done(&isoConn->outBuffers);


                     934 

00000498 e2840e80    935 	add	r0,r4,1<<11

0000049c e2800018    936 	add	r0,r0,24

000004a0 eb000000*   937 	bl	SessionBuffers_done

                     938 ;369:     freeConnection(isoConn);


                     939 

000004a4 e1a00004    940 	mov	r0,r4

000004a8 e8bd4030    941 	ldmfd	[sp]!,{r4-r5,lr}

000004ac ea000000*   942 	b	freeConnection

                     943 	.endf	closeIsoConnection

                     944 	.align	4

                     945 ;pCotpConn	r5	local

                     946 

                     947 ;isoConn	r4	param

                     948 

                     949 	.section ".bss","awb"

                     950 .L492:

                     951 	.data

                     952 	.text

                     953 

                     954 ;370: }


                     955 

                     956 ;371: 


                     957 ;372: void mmsThread(IsoConnection* mmsConn)


                     958 	.align	4

                     959 	.align	4

                     960 mmsThread::

000004b0 e92d4070    961 	stmfd	[sp]!,{r4-r6,lr}

                     962 ;373: {        


                     963 

000004b4 e24dd008    964 	sub	sp,sp,8

000004b8 e3a06000    965 	mov	r6,0


                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2h41.s
                     966 ;374:     int cotpSendDataCount = 0;       


                     967 

                     968 ;375:     int byteCount;        


                     969 ;376: 


                     970 ;377:     mmsConn->pCurrCotpOutBuf = NULL;


                     971 

000004bc e1a04000    972 	mov	r4,r0

000004c0 e28f0000*   973 	adr	r0,.L711

000004c4 e5846814    974 	str	r6,[r4,2068]

                     975 ;378:     debugSendText("MMS thread started");	


                     976 

000004c8 eb000000*   977 	bl	debugSendText

                     978 ;379:     while (1) {


                     979 

000004cc e28f5000*   980 	adr	r5,.L712

                     981 .L517:

                     982 ;380:         debugSendUshort("COTP data to send:", cotpSendDataCount);


                     983 

000004d0 e1a01806    984 	mov	r1,r6 lsl 16

000004d4 e1a01821    985 	mov	r1,r1 lsr 16

000004d8 e1a00005    986 	mov	r0,r5

000004dc eb000000*   987 	bl	debugSendUshort

                     988 ;381: 


                     989 ;382:         if(cotpSendDataCount > 0)


                     990 

000004e0 e3560000    991 	cmp	r6,0

000004e4 c5941814    992 	ldrgt	r1,[r4,2068]

000004e8 c2840bf2    993 	addgt	r0,r4,242<<10

000004ec c5816004    994 	strgt	r6,[r1,4]

000004f0 c280006c    995 	addgt	r0,r0,108

000004f4 cb000000*   996 	blgt	OutQueue_insert

                     997 ;383:         {


                     998 

                     999 ;384: 			mmsConn->pCurrCotpOutBuf->byteCount = cotpSendDataCount;


                    1000 

                    1001 ;385:             if(!OutQueue_insert(&mmsConn->outQueue,


                    1002 

                    1003 ;386:                                 mmsConn->pCurrCotpOutBuf))


                    1004 ;387:             {


                    1005 

                    1006 ;388:                 ERROR_REPORT("Out queue overflow");


                    1007 ;389:             }


                    1008 ;390:             cotpSendDataCount = 0;


                    1009 

                    1010 ;391:         }


                    1011 ;392: 


                    1012 ;393:         //Получить данные COTP


                    1013 ;394:         byteCount =  cotpReceiveData(&mmsConn->cotpConn,


                    1014 

000004f8 e2841014   1015 	add	r1,r4,20

000004fc e2840bf2   1016 	add	r0,r4,242<<10

00000500 e280009c   1017 	add	r0,r0,156

00000504 e3a02e80   1018 	mov	r2,1<<11

00000508 eb000000*  1019 	bl	cotpReceiveData

0000050c e1a01000   1020 	mov	r1,r0

                    1021 ;395:                                      mmsConn->cotpInBuf, COTP_IN_BUF_SIZE);


                    1022 ;396: 


                    1023 ;397:         if( byteCount  == -1 )


                    1024 

00000510 e3710001   1025 	cmn	r1,1

00000514 0a00001c   1026 	beq	.L533


                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2h41.s
                    1027 ;398:         {


                    1028 

                    1029 ;399: 			closeIsoConnection(mmsConn);


                    1030 

                    1031 ;400:             ERROR_REPORT("COTP error");			


                    1032 ;401:             return;


                    1033 

                    1034 ;402:         }


                    1035 ;403:         else


                    1036 ;404:         {


                    1037 

                    1038 ;405:             unsigned char* userData;


                    1039 ;406:             int userDataLen;


                    1040 ;407: 


                    1041 ;408:             IsoSessionIndication sessionIndication = parseSessionMessage(mmsConn->cotpInBuf,


                    1042 

00000518 e28d3004   1043 	add	r3,sp,4

0000051c e1a0200d   1044 	mov	r2,sp

00000520 e2840014   1045 	add	r0,r4,20

00000524 eb000000*  1046 	bl	parseSessionMessage

                    1047 ;409:                                                                          byteCount, &userData, &userDataLen);


                    1048 ;410: 


                    1049 ;411:             switch(sessionIndication)


                    1050 

00000528 e2500001   1051 	subs	r0,r0,1

0000052c 0a000011   1052 	beq	.L530

00000530 e2500001   1053 	subs	r0,r0,1

00000534 0a000002   1054 	beq	.L528

00000538 e3500002   1055 	cmp	r0,2

                    1056 ;422:                 break;


                    1057 ;423:             default:


                    1058 ;424:                 cotpSendDataCount = -1;


                    1059 

0000053c 1a000012   1060 	bne	.L533

00000540 ea000005   1061 	b	.L529

                    1062 .L528:

                    1063 ;412:             {


                    1064 ;413:             case SESSION_CONNECT:


                    1065 ;414:                 cotpSendDataCount = processSessionConnect(mmsConn);


                    1066 

00000544 e1a00004   1067 	mov	r0,r4

00000548 ebfffecc*  1068 	bl	processSessionConnect

0000054c e1a06000   1069 	mov	r6,r0

                    1070 ;425:                 break;


                    1071 ;426:             }


                    1072 ;427:             if(cotpSendDataCount == -1)


                    1073 

00000550 e3760001   1074 	cmn	r6,1

00000554 1affffdd   1075 	bne	.L517

00000558 ea00000b   1076 	b	.L533

                    1077 .L529:

                    1078 ;415:                 break;


                    1079 ;416:             case SESSION_DATA:


                    1080 ;417:                 cotpSendDataCount = processSessionData(mmsConn,userData, userDataLen);


                    1081 

0000055c e89d0006   1082 	ldmfd	[sp],{r1-r2}

00000560 e1a00004   1083 	mov	r0,r4

00000564 ebffff5a*  1084 	bl	processSessionData

00000568 e1a06000   1085 	mov	r6,r0

                    1086 ;425:                 break;


                    1087 ;426:             }



                                                                      Page 19
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2h41.s
                    1088 ;427:             if(cotpSendDataCount == -1)


                    1089 

0000056c e3760001   1090 	cmn	r6,1

00000570 1affffd6   1091 	bne	.L517

00000574 ea000004   1092 	b	.L533

                    1093 .L530:

                    1094 ;418:                 break;


                    1095 ;419:             case SESSION_ERROR:


                    1096 ;420:                 debugSendText("Session error");


                    1097 

00000578 e28f0000*  1098 	adr	r0,.L713

0000057c eb000000*  1099 	bl	debugSendText

                    1100 ;421:                 cotpSendDataCount = -1;


                    1101 

                    1102 ;428:             {


                    1103 

                    1104 ;429: 				closeIsoConnection(mmsConn);                


                    1105 

00000580 e1a00004   1106 	mov	r0,r4

00000584 ebffff97*  1107 	bl	closeIsoConnection

                    1108 ;430:                 return;


                    1109 

00000588 ea000001   1110 	b	.L513

                    1111 .L533:

                    1112 ;428:             {


                    1113 

                    1114 ;429: 				closeIsoConnection(mmsConn);                


                    1115 

0000058c e1a00004   1116 	mov	r0,r4

00000590 ebffff94*  1117 	bl	closeIsoConnection

                    1118 ;430:                 return;


                    1119 

                    1120 .L513:

00000594 e28dd008   1121 	add	sp,sp,8

00000598 e8bd8070   1122 	ldmfd	[sp]!,{r4-r6,pc}

                    1123 	.endf	mmsThread

                    1124 	.align	4

                    1125 ;cotpSendDataCount	r6	local

                    1126 ;byteCount	r1	local

                    1127 ;.L673	.L679	static

                    1128 ;.L674	.L680	static

                    1129 ;userData	[sp]	local

                    1130 ;userDataLen	[sp,4]	local

                    1131 ;.L675	.L678	static

                    1132 

                    1133 ;mmsConn	r4	param

                    1134 

                    1135 	.section ".bss","awb"

                    1136 .L672:

                    1137 	.data

                    1138 	.text

                    1139 

                    1140 ;431:             }


                    1141 ;432:         }


                    1142 ;433:     }


                    1143 ;434: }


                    1144 

                    1145 ;435: 


                    1146 ;436: 


                    1147 ;437: void handleMMSConnection(SERVER_SOCKET socket)


                    1148 	.align	4


                                                                      Page 20
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2h41.s
                    1149 	.align	4

                    1150 handleMMSConnection::

0000059c e92d4030   1151 	stmfd	[sp]!,{r4-r5,lr}

000005a0 e1a05000   1152 	mov	r5,r0

                    1153 ;438: {


                    1154 

                    1155 ;439: 	IsoConnection* isoConn = allocateConnection();


                    1156 

000005a4 eb000000*  1157 	bl	allocateConnection

000005a8 e1b04000   1158 	movs	r4,r0

                    1159 ;440: 	if (isoConn == NULL)


                    1160 

                    1161 ;441: 	{


                    1162 

                    1163 ;442: 		ERROR_REPORT("Unable to allocate connection");


                    1164 ;443: 		closeServerSocket(socket);


                    1165 

000005ac 01a00005   1166 	moveq	r0,r5

000005b0 08bd4030   1167 	ldmeqfd	[sp]!,{r4-r5,lr}

000005b4 0a000000*  1168 	beq	closeServerSocket

                    1169 ;445: 	}


                    1170 ;446: 


                    1171 ;447:     debugSendText("\r\n==============================\r\nTCP Connected");


                    1172 

000005b8 e28f0000*  1173 	adr	r0,.L776

000005bc eb000000*  1174 	bl	debugSendText

                    1175 ;448:     initIsoConnection(isoConn);


                    1176 

                    1177 ;155: {


                    1178 

                    1179 ;156:     isoConn->maxServOutstandingCalling


                    1180 

000005c0 e3a00005   1181 	mov	r0,5

000005c4 e1a02000   1182 	mov	r2,r0

                    1183 ;157:             = DEFAULT_MAX_SERV_OUTSTANDING_CALLING;


                    1184 ;158:     isoConn->maxServOutstandingCalled


                    1185 

                    1186 ;159:             = DEFAULT_MAX_SERV_OUTSTANDING_CALLED;


                    1187 ;160:     isoConn->dataStructureNestingLevel


                    1188 

000005c8 e3a0300a   1189 	mov	r3,10

                    1190 ;161:             = DEFAULT_DATA_STRUCTURE_NESTING_LEVEL;    


                    1191 ;162:     isoConn->maxPduSize = CONFIG_MMS_MAXIMUM_PDU_SIZE;


                    1192 

000005cc e3a0cb40   1193 	mov	r12,1<<16

000005d0 e884100d   1194 	stmea	[r4],{r0,r2-r3,r12}

                    1195 ;163: 	initSessionOutBuffers(&isoConn->outBuffers);


                    1196 

000005d4 e2840e80   1197 	add	r0,r4,1<<11

000005d8 e2800018   1198 	add	r0,r0,24

000005dc eb000000*  1199 	bl	initSessionOutBuffers

                    1200 ;164:     OutQueue_init(&isoConn->outQueue);


                    1201 

000005e0 e2840bf2   1202 	add	r0,r4,242<<10

000005e4 e280006c   1203 	add	r0,r0,108

000005e8 eb000000*  1204 	bl	OutQueue_init

                    1205 ;165: 	mms_initConnection(&isoConn->mmsConn);


                    1206 

                    1207 ;141: {


                    1208 

                    1209 ;142: 	mmsConn->isFileOpen = FALSE;



                                                                      Page 21
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2h41.s
                    1210 

000005ec e3a00f8f   1211 	mov	r0,0x023c

000005f0 e2600a49   1212 	rsb	r0,r0,73<<12

000005f4 e0840000   1213 	add	r0,r4,r0

000005f8 e2800c75   1214 	add	r0,r0,117<<8

000005fc e3a01000   1215 	mov	r1,0

00000600 e5c01093   1216 	strb	r1,[r0,147]

                    1217 ;449:     initPresentation(&isoConn->presentation);


                    1218 

00000604 e2840edb   1219 	add	r0,r4,0x0db0

00000608 e2800bf0   1220 	add	r0,r0,15<<14

0000060c eb000000*  1221 	bl	initPresentation

                    1222 ;450:     AcseConnection_init(&isoConn->acse);


                    1223 

00000610 e2840db7   1224 	add	r0,r4,0x2dc0

00000614 e2800a40   1225 	add	r0,r0,1<<18

00000618 eb000000*  1226 	bl	AcseConnection_init

                    1227 ;451:     initCOTPConnection(&isoConn->cotpConn, socket);


                    1228 

0000061c e1a01005   1229 	mov	r1,r5

00000620 e2840bf2   1230 	add	r0,r4,242<<10

00000624 e280009c   1231 	add	r0,r0,156

00000628 eb000000*  1232 	bl	initCOTPConnection

                    1233 ;452:     debugSendText("Starting MMS thread...");    


                    1234 

0000062c e28f0000*  1235 	adr	r0,.L777

00000630 eb000000*  1236 	bl	debugSendText

                    1237 ;453: 	isoConn->connected = TRUE;    


                    1238 

00000634 e3a00001   1239 	mov	r0,1

00000638 e3a01bf2   1240 	mov	r1,242<<10

0000063c e2811098   1241 	add	r1,r1,152

00000640 e7c10004   1242 	strb	r0,[r1,r4]

                    1243 ;454:     createThread(sendThread, isoConn);


                    1244 

00000644 e59f0094*  1245 	ldr	r0,.L778

00000648 e1a01004   1246 	mov	r1,r4

0000064c eb000000*  1247 	bl	createThread

                    1248 ;455:     createThread(mmsThread, isoConn);


                    1249 

00000650 e1a01004   1250 	mov	r1,r4

00000654 e59f0088*  1251 	ldr	r0,.L779

00000658 e8bd4030   1252 	ldmfd	[sp]!,{r4-r5,lr}

0000065c ea000000*  1253 	b	createThread

                    1254 	.endf	handleMMSConnection

                    1255 	.align	4

                    1256 .L711:

                    1257 ;	"MMS thread started\000"

00000660 20534d4d   1258 	.data.b	77,77,83,32

00000664 65726874   1259 	.data.b	116,104,114,101

00000668 73206461   1260 	.data.b	97,100,32,115

0000066c 74726174   1261 	.data.b	116,97,114,116

00000670 6465      1262 	.data.b	101,100

00000672 00        1263 	.data.b	0

00000673 00        1264 	.align 4

                    1265 

                    1266 	.type	.L711,$object

                    1267 	.size	.L711,4

                    1268 

                    1269 .L712:

                    1270 ;	"COTP data to send:\000"


                                                                      Page 22
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2h41.s
00000674 50544f43   1271 	.data.b	67,79,84,80

00000678 74616420   1272 	.data.b	32,100,97,116

0000067c 6f742061   1273 	.data.b	97,32,116,111

00000680 6e657320   1274 	.data.b	32,115,101,110

00000684 3a64      1275 	.data.b	100,58

00000686 00        1276 	.data.b	0

00000687 00        1277 	.align 4

                    1278 

                    1279 	.type	.L712,$object

                    1280 	.size	.L712,4

                    1281 

                    1282 .L713:

                    1283 ;	"Session error\000"

00000688 73736553   1284 	.data.b	83,101,115,115

0000068c 206e6f69   1285 	.data.b	105,111,110,32

00000690 6f727265   1286 	.data.b	101,114,114,111

00000694 0072      1287 	.data.b	114,0

00000696 0000      1288 	.align 4

                    1289 

                    1290 	.type	.L713,$object

                    1291 	.size	.L713,4

                    1292 

                    1293 .L776:

                    1294 ;	"\r\n==============================\r\nTCP Connected\000"

00000698 3d3d0a0d   1295 	.data.b	13,10,61,61

0000069c 3d3d3d3d   1296 	.data.b	61,61,61,61

000006a0 3d3d3d3d   1297 	.data.b	61,61,61,61

000006a4 3d3d3d3d   1298 	.data.b	61,61,61,61

000006a8 3d3d3d3d   1299 	.data.b	61,61,61,61

000006ac 3d3d3d3d   1300 	.data.b	61,61,61,61

000006b0 3d3d3d3d   1301 	.data.b	61,61,61,61

000006b4 3d3d3d3d   1302 	.data.b	61,61,61,61

000006b8 43540a0d   1303 	.data.b	13,10,84,67

000006bc 6f432050   1304 	.data.b	80,32,67,111

000006c0 63656e6e   1305 	.data.b	110,110,101,99

000006c4 00646574   1306 	.data.b	116,101,100,0

                    1307 	.align 4

                    1308 

                    1309 	.type	.L776,$object

                    1310 	.size	.L776,4

                    1311 

                    1312 .L777:

                    1313 ;	"Starting MMS thread...\000"

000006c8 72617453   1314 	.data.b	83,116,97,114

000006cc 676e6974   1315 	.data.b	116,105,110,103

000006d0 534d4d20   1316 	.data.b	32,77,77,83

000006d4 72687420   1317 	.data.b	32,116,104,114

000006d8 2e646165   1318 	.data.b	101,97,100,46

000006dc 2e2e      1319 	.data.b	46,46

000006de 00        1320 	.data.b	0

000006df 00        1321 	.align 4

                    1322 

                    1323 	.type	.L777,$object

                    1324 	.size	.L777,4

                    1325 

                    1326 .L778:

000006e0 00000000*  1327 	.data.w	sendThread

                    1328 	.type	.L778,$object

                    1329 	.size	.L778,4

                    1330 

                    1331 .L779:


                                                                      Page 23
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2h41.s
000006e4 00000000*  1332 	.data.w	mmsThread

                    1333 	.type	.L779,$object

                    1334 	.size	.L779,4

                    1335 

                    1336 	.align	4

                    1337 ;isoConn	r4	local

                    1338 ;.L759	.L763	static

                    1339 ;.L760	.L764	static

                    1340 

                    1341 ;socket	r5	param

                    1342 

                    1343 	.section ".bss","awb"

                    1344 .L758:

                    1345 	.data

                    1346 	.text

                    1347 

                    1348 ;456: 


                    1349 ;457: }


                    1350 	.align	4

                    1351 ;servicesSupported	.L104	static

                    1352 ;parameterCBB	.L103	static

                    1353 

                    1354 	.data

                    1355 	.ghsnote version,6

                    1356 	.ghsnote tools,1

                    1357 	.ghsnote options,0

                    1358 	.text

                    1359 	.align	4

                    1360 	.data

                    1361 	.align	4

                    1362 	.text

