#include "iedmodel.h"
#include "AsnEncoding.h"
#include "MmsConst.h"
#include "mms_data.h"
#include "mms_rcb.h"
#include "mms_gocb.h"
#include "bufViewBER.h"
#include "tools.h"
#include "IEDCompile/InnerAttributeTypes.h"
#include <debug.h>
#include <string.h>
#include <stdbool.h>

static int encodeStructAccessAttrs(uint8_t* outBuf, int bufPos, int objectPos,
    bool determineSize, bool topStruct);

// Нужно писать перед байтовыми массивами, потому что
// без этого иногда собирается падающая программа.
// Возможно, баг postlink
#pragma alignvar (4)

unsigned char defaultIed[] = {
        0xea, 0x3a, 0x1a, 0x03, 0x49, 0x45, 0x44, 0xe2,
        0x33, 0x1a, 0x07, 0x4c, 0x44, 0x65, 0x76, 0x69,
        0x63, 0x65, 0xe4, 0x28, 0x1a, 0x05, 0x4d, 0x4d,
        0x58, 0x55, 0x30, 0xe6, 0x1f, 0x1a, 0x04, 0x54,
        0x6f, 0x74, 0x57, 0xe8, 0x17, 0x1a, 0x03, 0x6d,
        0x61, 0x67, 0xe9, 0x10, 0x1a, 0x01, 0x66, 0x02,
        0x01, 0x02, 0x04, 0x08, 0xc0, 0x00, 0x00, 0x00,
        0xcf, 0x1a, 0x31, 0x35
};

unsigned char* iedModel = defaultIed;
int iedModelSize = sizeof(defaultIed);

void setIedModel(unsigned char* pModel, int modelSize)
{
    iedModel = pModel;
    iedModelSize = modelSize;
}

int readTL(int pos, uint8_t* pTag, int* pLen, int* pFullLen)
{
    BufferView bv;

    bv.len = iedModelSize;
    bv.p = iedModel;
    bv.pos = pos;

    if (BerDecoder_decodeTLFromBufferView(&bv, pTag, pLen, pFullLen))
    {
        return bv.pos;
    }

    return 0;
}

int skipObject(int pos)
{
    int len;
    //пропускаем тэг
    pos++;
    //определяем и пропускаем длину
    pos = BerDecoder_decodeLength(iedModel, &len, pos, iedModelSize);
    if( pos <= 0)
    {
        return 0;
    }
    //пропускаем содержимое
    pos+=len;
    return pos;
}

//Возвращает полную длину строки (вместе с тегом и длиной),
// закодированной BER(например, VisibleString)
//Работает только с тэгом в один байт
// При ошибке возвращает 0
int getBerStringLength(int berStringPos)
{
    int length;
    int newPos;
    berStringPos++;//Пропускаем тэг
    newPos = BerDecoder_decodeLength(iedModel, &length, berStringPos ,
                                     iedModelSize);
    if(newPos < 1)
    {
        return 0;
    }
    else
    {
        return 1 + (newPos - berStringPos) + length;
    }
}

// Возвращает имя объекта IED из описания информационной модели
// в виде BER VisibleString.
// Результат складывается в buf, размер результата возвращается.
// pObjPos принимает позицию объекта, и возвращает позицию,
// следующую за объектом.
// Если buf == NULL, то функция не пишет в buf (используется
// для определения полного размера имени).
// При ошибке возвращает 0
int getIEDObjectNameString(int *pObjPos, unsigned char* buf)
{
    int objLen;
    int nameLen;
    int namePos;
    int pos = *pObjPos;
    pos++;//Пропускаем тэг
    //Получаем длину объекта и позицию имени.
    namePos = BerDecoder_decodeLength(iedModel, &objLen, pos , iedModelSize);
    if(namePos < 1)
    {
        return 0;
    }

    nameLen = getBerStringLength(namePos);
    if( !nameLen )
    {
        return 0;
    }

    if(buf != NULL)
    {
        memcpy(buf, iedModel + namePos, nameLen);
    }
    *pObjPos = namePos + objLen;
    return nameLen;
}

bool getObjectName(int objPos, StringView* result)
{
    uint8_t nameTag;
    int nameLen;
    int namePos;
    int objectLen;

    //Пропускаем тэг и длину
    int pos = readTL(objPos, NULL, &objectLen, NULL);
    if(!pos)
    {
        return FALSE;
    }
    if (objectLen == 0)
    {
        ERROR_REPORT("Empty object");
        return FALSE;
    }

    namePos = readTL(pos, &nameTag, &nameLen, NULL);

    if(!namePos || nameTag != ASN_VISIBLE_STRING)
    {
        return FALSE;
    }


    StringView_init(result, (char*)iedModel + namePos, nameLen);

    return TRUE;
}

bool IEDModel_isServiceInfo(uint8_t tag)
{
    return tag == ASN_VISIBLE_STRING
            || tag ==  IED_GSE_LIST
            || tag == IED_CONTROL_INFO
            || tag == IED_OBJ_FLAGS;
}

int getSubObjectsPos(int rootObjPos, int* pEndPos)
{
    int endPos;
    int rootObjLen;
    int pos = rootObjPos;
    //Пропускаем тэг корневого объекта
    pos++;

    //Получаем длину объекта
    pos = BerDecoder_decodeLength(iedModel, &rootObjLen, pos, iedModelSize);
    if( pos <= 0)
    {
        return 0;
    }

    //Получаем позицию конца объекта
    endPos = pos + rootObjLen;
    if(endPos > iedModelSize)
    {
        //Объект неправдоподобно большой
        return 0;
    }
    *pEndPos = endPos;

    if (pos >= endPos)
    {
        //Пустой объект
        return pos;
    }

    //Пропускаем служебную информацию
    while (pos < endPos)
    {
        if(IEDModel_isServiceInfo(iedModel[pos]))
        {
            pos = skipObject(pos);
            if (pos == 0)
            {
                return 0;
            }
        }
        else
        {
            return pos;
        }
    }

    //Пустой объект
    return pos;
}

int findObjectBySimpleName(int rootObjPos, unsigned char* name, int argNameLen)
{
    int objPos;
    int endPos;

    objPos = getSubObjectsPos(rootObjPos, &endPos);
    if( objPos == 0)
    {
        return 0;
    }

    while (objPos < endPos)
    {
        int nameCompareResult;
        int nameLen;
        int objLen;
        int namePos;
        int nextObjPos;
        int pos = objPos;
        pos++;//Пропускаем тэг
        //Получаем длину объекта и позицию имени.
        namePos = BerDecoder_decodeLength(iedModel, &objLen, pos, iedModelSize);
        if (namePos < 1)
        {
            return 0;
        }
        if (iedModel[namePos] != ASN_VISIBLE_STRING)
        {
            //Не найдено имя объекта
            return 0;
        }
        nextObjPos = namePos + objLen;

        // Пропускаем тэг
        pos = namePos + 1;

        //Получаем длину имени
        //namePos в результате должен указывать на саму строку
        namePos = BerDecoder_decodeLength(iedModel, &nameLen, pos, iedModelSize);
        if (namePos < 1)
        {
            return 0;
        }
        //Сравниваем имена
        nameCompareResult = memcmp(name, iedModel + namePos, nameLen);
        if (nameLen == argNameLen &&  nameCompareResult == 0)
        {
            return objPos;
        }
        objPos = nextObjPos;
    }

    //debugSendText("\t!!!!!!!!!!Object is not found");
    return 0;
}

int findObjectByTag(int rootObjPos, uint8_t tagToFind)
{
    int objPos;
    int endPos;

    objPos = getSubObjectsPos(rootObjPos, &endPos);
    if( objPos == 0)
    {
        return 0;
    }

    while (objPos < endPos)
    {
        int objLen;
        int pos = objPos;
        uint8_t tag = iedModel[pos];
        if(tag == tagToFind)
        {
            return pos;
        }
        pos++;
        //Получаем длину объекта и позицию имени.
        pos = BerDecoder_decodeLength(iedModel, &objLen, pos, iedModelSize);
        if (pos < 1)
        {
            ERROR_REPORT("Unable to decode length");
            return 0;
        }
        objPos = pos + objLen;
    }

    ERROR_REPORT("Object tag is not found");
    return 0;
}

int findDomainSection(int section, uint8_t* domainId, int domainIdLen)
{
    int vmdPos = findObjectBySimpleName(0, domainId, domainIdLen);
    if (vmdPos == 0)
    {
        return 0;
    }
    return findObjectByTag(vmdPos, section);
}

// Длина первого простого в сложном имени до разделителя или конца строки
int getSimpleNameLen(uint8_t* name, int fullNameLen, bool* delimiterFound)
{
    int len = 0;
    *delimiterFound = FALSE;
    while (len < fullNameLen)
    {
        if (name[len] == '$')
        {
            *delimiterFound = TRUE;
            return len;
        }
        len++;
    }
    return len;
}

int findObjectByPath(int rootObjPos, uint8_t* name, int fullNameLen)
{
    int objPos = rootObjPos;
    while (fullNameLen != 0)
    {
        bool delimiterfound;
        int simpleNameLen = getSimpleNameLen(name, fullNameLen,
            &delimiterfound);
        objPos = findObjectBySimpleName(objPos, name, simpleNameLen);
        if (objPos == 0)
        {
            return 0;
        }

        if (delimiterfound)
        {
            simpleNameLen++;
        }

        name += simpleNameLen;
        fullNameLen -= simpleNameLen;
    }
    return objPos;
}

int findObjectByFullName(int section, StringView* domainName, StringView* objectName)
{
    int sectionPos;
    int objectPos;
    sectionPos = findDomainSection(section, (uint8_t*)domainName->p, domainName->len);
    if(sectionPos == 0)
    {
        return 0;
    }
    objectPos = findObjectByPath(sectionPos, (uint8_t*)objectName->p, objectName->len);
    if(objectPos == 0)
    {
        return 0;
    }
    return objectPos;
}

int getDataSetByPath(StringView* pDatasetPath)
{
    StringView domainName;
    StringView objectName;
    int dataSetPos;

    if(! StringView_splitChar(pDatasetPath, '/', &domainName, &objectName))
    {
        ERROR_REPORT("Unable to split dataset name");
        return 0;
    }

    dataSetPos = findObjectByFullName(IED_VMD_DATA_SET_SECTION,
                                      &domainName, &objectName);
    if(dataSetPos == 0)
    {
        ERROR_REPORT("Unable to find dataset");
        return 0;
    }

    return dataSetPos;
}

void writeChildrenNames(int rootObjPos, DomainNameWriter* writer,
    bool recursive, int objectsTagToWrite)
{
    int objEndPos;
    int childObjectPos = getSubObjectsPos(rootObjPos, &objEndPos);
    if (childObjectPos == 0)
    {
        //Подобъектов нет или ошибка.
        //Это нормальное завешение функции для IED_DA_FINAL, например.
        return;
    }
    while (childObjectPos < objEndPos)
    {
        int subObjLen;
        int nameLen;
        int namePos;
        int objContentPos;
        int pos = childObjectPos;
        uint8_t tag = iedModel[pos++];
        //Позиция имени в формате ber и размер подобъекта
        namePos = BerDecoder_decodeLength(iedModel, &subObjLen, pos,
            iedModelSize);
        objContentPos = namePos;
        if (namePos < 1)
        {
            //Если ошибка, считаем что больше детей нет
            return;
        }
        if (iedModel[namePos++] != ASN_VISIBLE_STRING)
        {
            //Если ошибка, считаем что больше детей нет
            return;
        }
        //Начало непосредственно строки имени и длина этой строки
        namePos = BerDecoder_decodeLength(iedModel, &nameLen, namePos,
            iedModelSize);
        if (namePos < 1)
        {
            //Если ошибка, считаем что больше детей нет
            return;
        }
        DomainNameWriter_pushName(writer, iedModel + namePos, nameLen);
        if (objectsTagToWrite == IED_ANY_TAG || objectsTagToWrite == tag)
        {
            DomainNameWriter_encode(writer);
        }
        if(recursive)
        {
            writeChildrenNames(childObjectPos, writer, TRUE, objectsTagToWrite);
        }
        DomainNameWriter_discardName(writer);
        childObjectPos = objContentPos + subObjLen;
    }
}

int encodeReadConst(uint8_t* outBuf, int bufPos, int constObjPos, bool determineSize)
{
    //Размер длины и тэга вместе
    int sizeOfTL;

    int fullSize;
    int objectSize;
    uint8_t constTypeTag;
    uint8_t mmsTag;
    //Пропускаем тэг
    int pos = constObjPos +1;
    pos = BerDecoder_decodeLength(iedModel, &objectSize, pos, iedModelSize);
    sizeOfTL = pos - constObjPos;
    fullSize = sizeOfTL + objectSize;
    if(determineSize)
    {
        return fullSize;
    }

    memcpy(outBuf + bufPos, iedModel + constObjPos, fullSize);

    constTypeTag = outBuf[bufPos];

    if ((constTypeTag & BER_TAG_CLASS_MASK) != BER_CONTEXT_SPECIFIC)
    {
        ERROR_REPORT("Invalid constant tag");
        return 0;
    }
    constTypeTag &= ~BER_TAG_CLASS_MASK;

    switch (constTypeTag)
    {
    case IEC61850_BOOLEAN:
        mmsTag = IEC61850_BER_BOOLEAN;
        break;
    case IEC61850_INT32:
    case IEC61850_INT64:
    case IEC61850_ENUMERATED:
        mmsTag = IEC61850_BER_INTEGER;
        break;
    case IEC61850_INT8U:
    case IEC61850_INT16U:
    case IEC61850_INT32U:
        mmsTag = IEC61850_BER_UNSIGNED_INTEGER;
        break;
    case IEC61850_FLOAT32:
        mmsTag = IEC61850_BER_FLOAT;
        break;
    case IEC61850_VISIBLE_STRING_32:
    case IEC61850_VISIBLE_STRING_64:
    case IEC61850_VISIBLE_STRING_65:
    case IEC61850_VISIBLE_STRING_129:
    case IEC61850_VISIBLE_STRING_255:
        mmsTag = IEC61850_BER_VISIBLE_STRING;
        break;
    case IEC61850_UNICODE_STRING_255:
        mmsTag = IEC61850_BER_MMS_STRING;
        break;
    case IEC61850_OCTET_STRING_6:
    case IEC61850_OCTET_STRING_64:
        mmsTag = IEC61850_BER_OCTET_STRING;
        break;
    case IEC61850_GENERIC_BITSTRING:
    case IEC61850_QUALITY:
        mmsTag = IEC61850_BER_BIT_STRING;
        break;
    case IEC61850_ENTRY_TIME:
        mmsTag = IEC61850_BER_BINARY_TIME;
        break;
    default:
        //Если неизвестно что, то пусть будет строка
        ERROR_REPORT("Unknown constant tag %02X", constTypeTag);
        mmsTag = IEC61850_BER_VISIBLE_STRING;
    }
    outBuf[bufPos] = mmsTag;
    return bufPos + fullSize;
}

uint8_t* getAlignedDescrStruct(int pos)
{
    //Какое смещение использовано для выравнивания структуры описания
    uint8_t* pDescrStructAlignOffset;
    uint8_t descrTag;
    int descrLen;
    //Получаем указатель на структуру описания
    descrTag = iedModel[pos++];
    if (descrTag != ASN_OCTET_STRING)
    {
        return NULL;
    };
    pos = BerDecoder_decodeLength(iedModel, &descrLen, pos, iedModelSize);
    if (pos == -1)
    {
        return NULL;
    }

    //Получаем структуру описания с учётом выравнивания
    pDescrStructAlignOffset = iedModel + pos;
    RET_IF_NOT(*pDescrStructAlignOffset < 4, "Invalid alignment");
    return pDescrStructAlignOffset + *pDescrStructAlignOffset + 1;
}

int encodeTypeAccessAttrs(uint8_t* outBuf, int bufPos, enum InnerAttributeType type,
                    //Позиция константы или структуры для доступа к значению
                          int attrDataPos,
                          bool determineSize)
{
    switch(type)
    {
    case INNER_TYPE_CONST:
        return encodeAccessAttrConst(outBuf, bufPos, attrDataPos, determineSize);
    case INNER_TYPE_REAL_VALUE:
    case INNER_TYPE_REAL_SETT:
    case INNER_TYPE_FLOAT_VALUE:
    case INNER_TYPE_FLOAT_SETT:
        return encodeAccessAttrFloat(outBuf, bufPos, determineSize);
    case INNER_TYPE_QUALITY:
        return encodeAccessAttrQuality(outBuf, bufPos, determineSize);
    case INNER_TYPE_TIME_STAMP:
        return encodeAccessAttrTimeStamp(outBuf, bufPos, determineSize);
    case INNER_TYPE_INT32_SETTS:
    case INNER_TYPE_INT32:
        return encodeAccessAttrInt(outBuf, bufPos, 32, determineSize);
    case INNER_TYPE_ENUMERATED:
    case INNER_TYPE_ENUMERATED_SETTS:
        return encodeAccessAttrInt(outBuf, bufPos, 8, determineSize);
    case INNER_TYPE_INT32U_SETTS:
    case INNER_TYPE_INT32U:
        return encodeAccessAttrUInt(outBuf, bufPos, 32, determineSize);
    case INNER_TYPE_INT8U:
        return encodeAccessAttrUInt(outBuf, bufPos, 8, determineSize);
    case INNER_TYPE_REAL_AS_INT64:
            return encodeAccessAttrInt(outBuf, bufPos, 64, determineSize);
    case INNER_TYPE_BOOLEAN:
        return encodeAccessAttrBoolean(outBuf, bufPos, determineSize);
    case INNER_TYPE_RCB:
        return encodeAccessAttrRCB(outBuf, bufPos, attrDataPos, determineSize);
    case INNER_TYPE_GOCB:
        return encodeAccessAttrGoCB(outBuf, bufPos, attrDataPos, determineSize);
    case INNER_TYPE_CODEDENUM:
        return encodeAccessAttrCodedEnum(outBuf, bufPos, attrDataPos,
            determineSize);
    default:
        ERROR_REPORT("Unsupported inner type %d", type);
        return 0;
    }
}

//Для GetValibaleAccessAttributes
//Пишет имя в буфер с тэгом 0х80
//или определяет размер
static int encodeNameAttr(uint8_t* outBuf, int bufPos, int namePos ,
                          bool determineSize)
{
    int totalLen;
    int len;
    int pos  = namePos;
    //пропускаем тэг
    pos++;
    //определяем и пропускаем длину
    pos = BerDecoder_decodeLength(iedModel, &len, pos, iedModelSize);
    if( pos <= 0)
    {
        return 0;
    }

    pos+=len;
    totalLen = pos - namePos;
    if(determineSize)
    {
        return totalLen;
    }
    memcpy(outBuf + bufPos, iedModel + namePos, totalLen);
    outBuf[bufPos] = 0x80;

    return bufPos + totalLen;
}

int encodeSimpleDataAccessAttrs(uint8_t* outBuf, int bufPos, int objectPos, bool determineSize,
                                bool topStruct)
{
    uint8_t typeIdTag;
    int typeIdLen;
    enum InnerAttributeType typeId;
    int typeDescrSize;
    int pos = objectPos;
    int objectSize;
    int sequenceSize;
    int totalSize;
    int objectNamePos;
    int nameLen;
    int daDataPos;
    //!!!
    int result;

    //======================Получаем иденитификатор типа============

    //Пропускаем тэг
    pos++;
    //Опеределяем длину
    pos = BerDecoder_decodeLength(iedModel, &objectSize, pos, iedModelSize);
    if(pos == -1)
    {
        return 0;
    }

    //Запоминаем положение имени и пропускаем
    objectNamePos = pos;
    pos = skipObject(pos);
    if (pos == 0)
    {
        return 0;
    }

    //Пропускаем прочую служебную информацию
    while(IEDModel_isServiceInfo(iedModel[pos]))
    {
        pos = skipObject(pos);
    }

    //Получаем идентификатор типа
    typeIdTag = iedModel[pos++];
    if(typeIdTag != ASN_INTEGER)
    {
        return 0;
    };
    typeIdLen = iedModel[pos++];
    //Получаем идентификатор типа
    typeId = (enum InnerAttributeType)BerDecoder_decodeUint32(iedModel, typeIdLen, pos);
    pos+=typeIdLen;
    daDataPos = pos;

    //===================== Определяем длину =========================
    typeDescrSize = encodeTypeAccessAttrs(outBuf, bufPos, typeId, daDataPos , TRUE);
    if(topStruct)
    {
        sequenceSize = typeDescrSize;
        //+Deletable size
        sequenceSize += 3;
    }
    else
    {
        sequenceSize = 1
                + BerEncoder_determineLengthSize(typeDescrSize)
                + typeDescrSize;
        //+Name size
        nameLen = encodeNameAttr(NULL, 0, objectNamePos, TRUE);
        if(nameLen < 1)
        {
            return 0;
        }
        sequenceSize += nameLen;
    }


    if(determineSize)
    {
        totalSize = 1
                + BerEncoder_determineLengthSize(sequenceSize)
                + sequenceSize;
        return totalSize;
    }

    //=======================Пишем================================
    if(!topStruct)
    {
        //Если не topStruct пишем sequence
        bufPos = BerEncoder_encodeTL(ASN_SEQUENCE, sequenceSize,
                                     outBuf, bufPos);
        //Имя объекта
        bufPos = encodeNameAttr(outBuf, bufPos, objectNamePos, FALSE);
        //Тэг и размер описания типа
        bufPos = BerEncoder_encodeTL(0xA1, typeDescrSize, outBuf, bufPos);
    }
    else
    {
        //Deletable
        bufPos = BerEncoder_encodeBoolean(VAR_DELETABLE, FALSE, outBuf, bufPos);
        //Тэг и размер описания типа
        bufPos = BerEncoder_encodeTL(0xA2, typeDescrSize, outBuf, bufPos);
    }
    
    //Вызываем соответствующую типу функцию
    result = encodeTypeAccessAttrs(outBuf, bufPos, typeId, daDataPos, FALSE);
    return result;
}

int encodeObjectAccessAttrs(uint8_t* outBuf, int bufPos, int objectPos, bool determineSize,
                            bool topStruct)
{
    uint8_t tag = iedModel[objectPos];
    if(tag == IED_DA_FINAL)
    {
        return encodeSimpleDataAccessAttrs( outBuf, bufPos, objectPos, determineSize, topStruct);
    }
    else
    {
        return encodeStructAccessAttrs(outBuf, bufPos, objectPos, determineSize, topStruct);
    }
}

int encodeChildrenAccessAttrs(uint8_t* outBuf, int bufPos, int rootObjPos, bool determineSize)
{
    int totalSize = 0;
    int objEndPos;
    int childObjectPos = getSubObjectsPos(rootObjPos, &objEndPos);
    if (childObjectPos == 0)
    {
        //Если ошибка, считаем что детей нет
        return 0;
    }
    while (childObjectPos < objEndPos)
    {
        int subObjLen;
        int objContentPos;
        int pos = childObjectPos;
        //Пропускаем тэг
        pos++;
        //Получаем размер подобъекта
        objContentPos = BerDecoder_decodeLength(iedModel, &subObjLen, pos,
            iedModelSize);
        if (objContentPos < 1)
        {
            //Если ошибка, считаем что больше детей нет
            return 0;
        }
        bufPos = encodeObjectAccessAttrs(outBuf, bufPos, childObjectPos, determineSize,
                                         FALSE);
        if (determineSize)
        {
            totalSize += bufPos;
        }

        childObjectPos = objContentPos + subObjLen;
    }

    if (determineSize)
    {
        return totalSize;
    }
    else
    {
        return bufPos;
    }
}

static int encodeStructAccessAttrs(uint8_t* outBuf, int bufPos, int objectPos, bool determineSize,
                          bool topStruct)
{
    int totalSize;
    int sequenceSize;
    int descrTypeSize;
    int structureSize;
    int fieldListSize;
    int nameLen;
    int objectNamePos;
    int pos = objectPos;
    int objectSize;


    //Пропускаем тэг
    pos++;

    //Пропускаем длину
    pos = BerDecoder_decodeLength(iedModel, &objectSize, pos, iedModelSize);
    if(pos == -1)
    {
        return 0;
    }

    objectNamePos = pos;


    //================= Определяем размеры ===================
    fieldListSize = encodeChildrenAccessAttrs(outBuf, bufPos,  objectPos, TRUE);
    structureSize = 1
            + BerEncoder_determineLengthSize(fieldListSize)
            + fieldListSize;

    descrTypeSize = 1
            + BerEncoder_determineLengthSize(structureSize)
            + structureSize;

    sequenceSize = 1
        + BerEncoder_determineLengthSize(descrTypeSize)
        + descrTypeSize;

    if(topStruct)
    {
        //+Deletable size
        totalSize = sequenceSize + 3;
    }
    else
    {

        //+Name size
        nameLen = encodeNameAttr(NULL, 0, objectNamePos, TRUE);
        if(nameLen < 1)
        {
            return 0;
        }
        sequenceSize += nameLen;

        totalSize = 1
            + BerEncoder_determineLengthSize(sequenceSize)
            + sequenceSize;
    }

    if(determineSize)
    {
        return totalSize;
    }

    //=================== Пишем ==============================
    if(!topStruct)
    {
        //Если не topStruct пишем sequence
        bufPos = BerEncoder_encodeTL(ASN_SEQUENCE, sequenceSize,
                                     outBuf, bufPos);
        //Имя объекта
        bufPos = encodeNameAttr(outBuf, bufPos, objectNamePos, FALSE);
    }
    else
    {
        //Deletable
        bufPos = BerEncoder_encodeBoolean(VAR_DELETABLE, FALSE, outBuf, bufPos);
    }

    //Описание типа
    bufPos = BerEncoder_encodeTL(

                //Дурацкий хак
                topStruct? ASN_TYPEDESCRIPTION_STRUCTURE:
                           ASN_TYPEDESCRIPTION_COMPONENT_TYPE,

                                 descrTypeSize, outBuf, bufPos);

    //Structure
    bufPos = BerEncoder_encodeTL(ASN_TYPEDESCRIPTION_STRUCTURE,
                                 structureSize, outBuf, bufPos);
    //Тэг списка полей tag=0xa1
    bufPos = BerEncoder_encodeTL(0xA1, fieldListSize, outBuf, bufPos);

    //Сами поля
    bufPos = encodeChildrenAccessAttrs(outBuf, bufPos,  objectPos, FALSE);
    return bufPos;
}

void* IEDModel_ptrFromPos(size_t pos)
{
    if (pos >= (size_t)iedModelSize)
    {
        return NULL;
    }
    return iedModel + pos;
}

void processSubobjects(int parentPos, void(*func)(int))
{
    int objPos;
    int endPos;

    objPos = getSubObjectsPos(parentPos, &endPos);
    if (objPos == 0)
    {
        ERROR_REPORT("Error reading objects at pos = %d", parentPos);
        return;
    }
    while (objPos < endPos)
    {
        int objLen;
        int pos = objPos;
        //Skip tag
        pos++;
        pos = BerDecoder_decodeLength(iedModel, &objLen, pos, iedModelSize);
        if (pos < 1)
        {
            ERROR_REPORT("Error reading object length");
            return;
        }
        func(objPos);
        objPos = pos + objLen;
    }
}

int getDAValuePos(int rootObjPos, uint8_t* name, int nameLen,
    enum InnerAttributeType* attrType)
{
    int pos;
    uint8_t tag;
    uint8_t typeIdTag;
    int typeIdLen;
    int daPos = findObjectBySimpleName(rootObjPos, name, nameLen);
    RET_IF_NOT(daPos, "Unable to find DA by name");
    pos = readTL(daPos, &tag, NULL, NULL);
    RET_IF_NOT(pos, "Error reading DA at %d", daPos);
    //Skip name
    pos = skipObject(pos);
    RET_IF_NOT(pos, "Error reading DA at %d", daPos);

    //Получаем идентификатор типа
    typeIdTag = iedModel[pos++];
    RET_IF_NOT(typeIdTag == ASN_INTEGER, "Error reading DA at %d", daPos);
    typeIdLen = iedModel[pos++];
    //Получаем идентификатор типа
    *attrType = (enum InnerAttributeType)
        BerDecoder_decodeUint32(iedModel, typeIdLen, pos);
    pos += typeIdLen;
    //pos указывает на константу или OCTET_STRING со структурой
    return pos;
}

//Возвращает значение константного DA в виде StringView
bool getConstDAString(size_t parentPos, const char* attrName,
    StringView* result)
{
    uint8_t tag;
    int len;
    enum InnerAttributeType attrType;
    int daPos = getDAValuePos(parentPos, (uint8_t*) attrName, strlen(attrName),
        &attrType);
    RET_IF_NOT(daPos, "Error reading %s", attrName);
    //pos указывает на константу
    daPos = readTL(daPos, &tag, &len, NULL);
    RET_IF_NOT(daPos, "Error reading value at %d", daPos);
    StringView_init(result, (char*)iedModel + daPos, len);
    return TRUE;
}

//Возвращает значение константного DA в виде uint32_t
bool getConstDAULong(size_t parentPos, const char* attrName,
    uint32_t* result)
{
    uint8_t tag;
    int len;
    enum InnerAttributeType attrType;
    int daPos = getDAValuePos(parentPos, (uint8_t*) attrName, 7, &attrType);
    RET_IF_NOT(daPos, "Error reading %s", attrName);
    daPos = readTL(daPos, &tag, &len, NULL);
    RET_IF_NOT(daPos, "Error reading %s", attrName);
    *result = BerDecoder_decodeUint32(iedModel, len, daPos);
    return TRUE;
}

bool IEDModel_skipServiceInfo(BufferView* subObjects)
{
    while(!BufferView_endOfBuf(subObjects))
    {
        uint8_t objInfoTag;
        if(!BufferView_peekTag(subObjects, &objInfoTag))
        {
            return false;
        }

        if(!IEDModel_isServiceInfo(objInfoTag))
        {
            break;
        }
        BufferView_skipObject(subObjects, objInfoTag, false);
    }
    return true;
}


bool IEDModel_getChildren(const BufferView* berObject,  BufferView* children)
{
    size_t len;
    *children = *berObject;
    if(!BufferView_decodeTL(children, NULL, &len, NULL))
    {
        return false;
    }

    BufferView_init(children, children->p + children->pos, len, 0);

    if(!IEDModel_skipServiceInfo(children))
    {
        return false;
    }
    return true;
}

bool IEDModel_getTermItemDescrStruct(BufferView* descrObject,
                                    void** pDescrStruct)
{
    //Какое смещение использовано для выравнивания структуры описания
    uint8_t* pDescrStructAlignOffset;
    uint8_t descrTag;
    size_t descrLen;
    //Получаем указатель на структуру описания
    if(BufferView_endOfBuf(descrObject))
    {
        return false;
    }
    if(!BufferView_decodeTL(descrObject, &descrTag, &descrLen, NULL))
    {
        return false;
    }
    if (descrTag != ASN_OCTET_STRING)
    {
        ERROR_REPORT("Invalid tag");
        return false;
    };
    //Получаем структуру описания с учётом выравнивания
    pDescrStructAlignOffset = descrObject->p + descrObject->pos;
    if(*pDescrStructAlignOffset >= 4)
    {
        ERROR_REPORT("Invalid alignment");
        return false;
    }

    if(!BufferView_advance(descrObject, descrLen))
    {
        ERROR_REPORT("Invalid object length");
        return false;
    }

    *pDescrStruct = pDescrStructAlignOffset + *pDescrStructAlignOffset + 1;
    if(( ((uint32_t)(*pDescrStruct)) & 3) != 0)
    {
        ERROR_REPORT("Invalid alignment");
        return false;
    }
    return true;
}

bool IEDModel_getBufferView(size_t pos, BufferView* bv)
{
    if(pos > (size_t)iedModelSize)
    {
        return false;
    }
    BufferView_init(bv, iedModel, iedModelSize, pos);
    return true;
}


