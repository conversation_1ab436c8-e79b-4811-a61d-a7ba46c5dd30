                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_54k1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=platform_critical_section.c -o gh_54k1.o -list=platform_critical_section.lst C:\Users\<USER>\AppData\Local\Temp\gh_54k1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_54k1.s
Source File: platform_critical_section.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      10 ;		platform_critical_section.c

                      11 ;Source File:   platform_critical_section.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:56:02 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include <platform_critical_section.h>


                      21 ;2: 


                      22 ;3: #include <process.h>


                      23 ;4: 


                      24 ;5: void CriticalSection_Init(CriticalSection *cs)


                      25 ;6: {


                      26 ;7:     CSInit(cs);


                      27 ;8: }


                      28 ;9: 


                      29 ;10: void CriticalSection_Done(CriticalSection *cs)


                      30 

                      31 ;12: }


                      32 

                      33 	.text

                      34 	.align	4

                      35 CriticalSection_Init::

00000000 ea000000*    36 	b	CSInit

                      37 	.endf	CriticalSection_Init

                      38 	.align	4

                      39 

                      40 ;cs	none	param

                      41 

                      42 	.section ".bss","awb"

                      43 .L46:

                      44 	.data

                      45 	.text

                      46 

                      47 

                      48 ;13: 


                      49 ;14: void CriticalSection_Lock(CriticalSection *cs)


                      50 	.align	4


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_54k1.s
                      51 	.align	4

                      52 CriticalSection_Lock::

                      53 ;15: {


                      54 

                      55 ;16:     CSLock(cs);


                      56 

00000004 ea000000*    57 	b	CSLock

                      58 	.endf	CriticalSection_Lock

                      59 	.align	4

                      60 

                      61 ;cs	none	param

                      62 

                      63 	.section ".bss","awb"

                      64 .L78:

                      65 	.data

                      66 	.text

                      67 

                      68 ;17: }


                      69 

                      70 ;18: 


                      71 ;19: void CriticalSection_Unlock(CriticalSection *cs)


                      72 	.align	4

                      73 	.align	4

                      74 CriticalSection_Unlock::

                      75 ;20: {


                      76 

                      77 ;21:     CSUnlock(cs);


                      78 

00000008 ea000000*    79 	b	CSUnlock

                      80 	.endf	CriticalSection_Unlock

                      81 	.align	4

                      82 

                      83 ;cs	none	param

                      84 

                      85 	.section ".bss","awb"

                      86 .L110:

                      87 	.data

                      88 	.text

                      89 

                      90 ;22: }


                      91 	.align	4

                      92 	.align	4

                      93 CriticalSection_Done::

                      94 ;11: {


                      95 

0000000c e12fff1e*    96 	ret	

                      97 	.endf	CriticalSection_Done

                      98 	.align	4

                      99 

                     100 ;cs	none	param

                     101 

                     102 	.section ".bss","awb"

                     103 .L142:

                     104 	.data

                     105 	.text

                     106 	.align	4

                     107 

                     108 	.data

                     109 	.ghsnote version,6

                     110 	.ghsnote tools,3

                     111 	.ghsnote options,0


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_54k1.s
                     112 	.text

                     113 	.align	4

