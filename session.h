#ifndef SESSION_H_		
#define SESSION_H_
#include "Cotp.h"

typedef enum
{
    SESSION_OK,
    SESSION_ERROR,
    SESSION_CONNECT,
    SESSION_GIVE_TOKEN,
    SESSION_DATA,
    SESSION_ABORT,
    SESSION_FINISH,
    SESSION_DISCONNECT,
    SESSION_NOT_FINISHED
}IsoSessionIndication;

IsoSessionIndication parseSessionMessage(unsigned char* message, int inLen,
                                         unsigned char** pUserData, int* pUserDataSize);
int createAcceptSPDU( unsigned char* buf, unsigned char* userData, int userDataLen);
int isoSession_createDataSpdu(unsigned char* buf, int bufSize,
	unsigned char* userData, int userDataLen);


#define SESION_DATA_PACKET_HEADER_SIZE	4	//Размер заголовка пакета Передача данных



#define	CONNECTION_ACCEPT_PARAMETER		0x05	//Соединить/принять

#define	SESSION_REQUIREMENTS_PARAMETER	0x14	//Требования пользователя к сеансу
#define	CALLING_SSELECTOR_PARAMETER		0x33	//Селектор вызывающего на сеансовом уровне
#define	CALLED_SSELECTOR_PARAMETER		0x34	//Селектор вызываюемого на сеансовом уровне

//SSelector		ИД ПДУСн (S-selector) - идентификатор пункта доступа к услугам сеансового уровня стр.86
typedef struct
{
    unsigned char	Size;		//размер -  0 .. 16 - 0 значит S-selector не присутствует
    unsigned char	Value[16];	//значение S-selector
}SSelector;

//Session_Options		параметры пакета Session Соединение ПБДСн Соединение стр.82
typedef struct														
{


	//Соединить/Принять Код группового параметра 0x05
	unsigned char	ProtocolOptions;	//факультативные возможности протокола - код параметра - 0x13
	unsigned char	VersionNumber;		//Номер версии (должен быть 2) - код параметра - 0x16
	//Соединить/Принять Код группового параметра 0x05

	
	unsigned short	SessionRequirements;	//Требования пользователя к сеансу

	SSelector		SSelSrc;			//идентификатор вызывающего ПДУСн - код параметра - 0x33
	SSelector		SSelDst;			//идентификатор вызываемого ПДУТУ - код параметра - 0x34
	

}Session_Options;

//Session_Connect_Packet		пакет Session Соединение ПБДСн Соединение стр.82
typedef struct														
{																	
																	
	unsigned char		PacketLength;	 //УД-указатель длины - длина пакета в октетах исключая УД и код запроса
    Session_Options		SessionOptions;  //параметры пакета
    int					UserDataPos;	 //позиция данных пользователя в общем пакете Session
    unsigned char		UserDataSize;	 //размер данных пользователя

	
}Session_Connect_Packet;

#endif //SESSION_H_
