GetVariableAccessAttributes
Описание переменной (на примере float)

<Sequence len="13" tag="0x30">
  <Deletable len="1" tag="0x80"/>
  <Constructed len="8" tag="0xa1">
	<FloatDescription len="6" tag="0xa7">
	  <Int len="1" tag="0x2"/>
	  <Int len="1" tag="0x2"/>
	</FloatDescription>
  </Constructed>
</Sequence>


Описание структуры
В структуре верхнего уровня тэга Sequence нет


<Sequence len="13" tag="0x30"> <!-- кроме верхнего уровня -->
  <Deletable len="1" tag="0x80"/>
  <Constructed len="8" tag="0xa1">
	<Structure len="49" tag="0xa2">
	  <Constructed len="47" tag="0xa1">
		Описания переменных
	  </Constructed>
	</Structure>
  </Constructed>
</Sequence> <!-- кроме верхнего уровня -->