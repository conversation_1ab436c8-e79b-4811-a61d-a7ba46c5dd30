                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_64k1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=stringView.c -o gh_64k1.o -list=stringView.lst C:\Users\<USER>\AppData\Local\Temp\gh_64k1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_64k1.s
Source File: stringView.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile stringView.c

                      10 ;		-o stringView.o

                      11 ;Source File:   stringView.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Oct 07 09:39:28 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "stringView.h"


                      21 ;2: #include <stddef.h>


                      22 ;3: #include <string.h>


                      23 ;4: 


                      24 ;5: void StringView_init(StringView* strView, const char * str, size_t len)


                      25 	.text

                      26 	.align	4

                      27 StringView_init::

                      28 ;6: {


                      29 

                      30 ;7: 	strView->p = str;


                      31 

00000000 e1a03001     32 	mov	r3,r1

00000004 e880000c     33 	stmea	[r0],{r2-r3}

                      34 ;8: 	strView->len = len;


                      35 

00000008 e12fff1e*    36 	ret	

                      37 	.endf	StringView_init

                      38 	.align	4

                      39 

                      40 ;strView	r0	param

                      41 ;str	r1	param

                      42 ;len	r2	param

                      43 

                      44 	.section ".bss","awb"

                      45 .L30:

                      46 	.data

                      47 	.text

                      48 

                      49 ;9: }


                      50 


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_64k1.s
                      51 ;10: 


                      52 ;11: void StringView_fromStringView(StringView* strView, StringView* src)


                      53 	.align	4

                      54 	.align	4

                      55 StringView_fromStringView::

                      56 ;12: {


                      57 

                      58 ;13: 	StringView_init(strView, src->p, src->len);


                      59 

0000000c e891000c     60 	ldmfd	[r1],{r2-r3}

00000010 e1a01003     61 	mov	r1,r3

00000014 eafffff9*    62 	b	StringView_init

                      63 	.endf	StringView_fromStringView

                      64 	.align	4

                      65 

                      66 ;strView	none	param

                      67 ;src	r1	param

                      68 

                      69 	.section ".bss","awb"

                      70 .L62:

                      71 	.data

                      72 	.text

                      73 

                      74 ;14: }


                      75 

                      76 ;15: 


                      77 ;16: void StringView_fromCStr(StringView* strView, char * str)


                      78 	.align	4

                      79 	.align	4

                      80 StringView_fromCStr::

00000018 e92d4030     81 	stmfd	[sp]!,{r4-r5,lr}

0000001c e1a05000     82 	mov	r5,r0

00000020 e1a04001     83 	mov	r4,r1

                      84 ;17: {


                      85 

                      86 ;18:     StringView_init(strView, str, strlen(str));


                      87 

00000024 e1a00004     88 	mov	r0,r4

00000028 eb000000*    89 	bl	strlen

0000002c e1a01004     90 	mov	r1,r4

00000030 e1a02000     91 	mov	r2,r0

00000034 e1a00005     92 	mov	r0,r5

00000038 e8bd4030     93 	ldmfd	[sp]!,{r4-r5,lr}

0000003c eaffffef*    94 	b	StringView_init

                      95 	.endf	StringView_fromCStr

                      96 	.align	4

                      97 

                      98 ;strView	r5	param

                      99 ;str	r4	param

                     100 

                     101 	.section ".bss","awb"

                     102 .L94:

                     103 	.data

                     104 	.text

                     105 

                     106 ;19: }


                     107 

                     108 ;20: 


                     109 ;21: int StringView_findChar(StringView * strView, unsigned char ch)


                     110 	.align	4

                     111 	.align	4


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_64k1.s
                     112 StringView_findChar::

00000040 e92d0030    113 	stmfd	[sp]!,{r4-r5}

                     114 ;22: {


                     115 

                     116 ;23: 	size_t pos;


                     117 ;24: 


                     118 ;25: 	if (strView->p == NULL)


                     119 

00000044 e590c004    120 	ldr	r12,[r0,4]

00000048 e35c0000    121 	cmp	r12,0

0000004c 0a000035    122 	beq	.L106

                     123 ;26: 	{


                     124 

                     125 ;27: 		return -1;


                     126 

                     127 ;28: 	}


                     128 ;29: 


                     129 ;30: 	for (pos = 0; pos < strView->len; ++pos)


                     130 

00000050 e5900000    131 	ldr	r0,[r0]

00000054 e3a02000    132 	mov	r2,0

00000058 e3500000    133 	cmp	r0,0

0000005c a1a04000    134 	movge	r4,r0

00000060 b3a04000    135 	movlt	r4,0

00000064 e1b031a4    136 	movs	r3,r4 lsr 3

00000068 0a000023    137 	beq	.L159

0000006c e1a0000c    138 	mov	r0,r12

                     139 .L160:

00000070 e5d05000    140 	ldrb	r5,[r0]

00000074 e1550001    141 	cmp	r5,r1

00000078 0a000024    142 	beq	.L195

0000007c e5d05001    143 	ldrb	r5,[r0,1]

00000080 e1550001    144 	cmp	r5,r1

00000084 02820001    145 	addeq	r0,r2,1

00000088 0a000027    146 	beq	.L101

0000008c e5d05002    147 	ldrb	r5,[r0,2]

00000090 e1550001    148 	cmp	r5,r1

00000094 02820002    149 	addeq	r0,r2,2

00000098 0a000023    150 	beq	.L101

0000009c e5d05003    151 	ldrb	r5,[r0,3]

000000a0 e1550001    152 	cmp	r5,r1

000000a4 02820003    153 	addeq	r0,r2,3

000000a8 0a00001f    154 	beq	.L101

000000ac e5d05004    155 	ldrb	r5,[r0,4]

000000b0 e1550001    156 	cmp	r5,r1

000000b4 02820004    157 	addeq	r0,r2,4

000000b8 0a00001b    158 	beq	.L101

000000bc e5d05005    159 	ldrb	r5,[r0,5]

000000c0 e1550001    160 	cmp	r5,r1

000000c4 02820005    161 	addeq	r0,r2,5

000000c8 0a000017    162 	beq	.L101

000000cc e5d05006    163 	ldrb	r5,[r0,6]

000000d0 e1550001    164 	cmp	r5,r1

000000d4 02820006    165 	addeq	r0,r2,6

000000d8 0a000013    166 	beq	.L101

000000dc e5d05007    167 	ldrb	r5,[r0,7]

000000e0 e1550001    168 	cmp	r5,r1

000000e4 02820007    169 	addeq	r0,r2,7

000000e8 0a00000f    170 	beq	.L101

000000ec e2800008    171 	add	r0,r0,8

000000f0 e2822008    172 	add	r2,r2,8


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_64k1.s
000000f4 e2533001    173 	subs	r3,r3,1

000000f8 1affffdc    174 	bne	.L160

                     175 .L159:

000000fc e2143007    176 	ands	r3,r4,7

00000100 0a000008    177 	beq	.L106

00000104 e082000c    178 	add	r0,r2,r12

                     179 .L194:

00000108 e5d0c000    180 	ldrb	r12,[r0]

0000010c e15c0001    181 	cmp	r12,r1

                     182 .L195:

00000110 01a00002    183 	moveq	r0,r2

00000114 0a000004    184 	beq	.L101

                     185 .L197:

00000118 e2800001    186 	add	r0,r0,1

0000011c e2822001    187 	add	r2,r2,1

00000120 e2533001    188 	subs	r3,r3,1

00000124 1afffff7    189 	bne	.L194

                     190 .L106:

                     191 ;35: 		}


                     192 ;36: 	}


                     193 ;37: 	return -1;


                     194 

00000128 e3e00000    195 	mvn	r0,0

                     196 .L101:

0000012c e8bd0030    197 	ldmfd	[sp]!,{r4-r5}

00000130 e12fff1e*   198 	ret	

                     199 	.endf	StringView_findChar

                     200 	.align	4

                     201 ;pos	r2	local

                     202 

                     203 ;strView	r0	param

                     204 ;ch	r1	param

                     205 

                     206 	.section ".bss","awb"

                     207 .L452:

                     208 	.data

                     209 	.text

                     210 

                     211 ;38: }


                     212 

                     213 ;39: 


                     214 ;40: int StringView_findCharBack(StringView * strView, unsigned char ch)


                     215 	.align	4

                     216 	.align	4

                     217 StringView_findCharBack::

00000134 e92d0030    218 	stmfd	[sp]!,{r4-r5}

                     219 ;41: {


                     220 

                     221 ;42:     int pos;


                     222 ;43: 


                     223 ;44:     if (strView->p == NULL)


                     224 

00000138 e590c004    225 	ldr	r12,[r0,4]

0000013c e35c0000    226 	cmp	r12,0

00000140 0a000035    227 	beq	.L529

                     228 ;45:     {


                     229 

                     230 ;46:         return -1;


                     231 

                     232 ;47:     }


                     233 ;48: 



                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_64k1.s
                     234 ;49:     for (pos = strView->len - 1; pos >= 0; --pos)


                     235 

00000144 e5902000    236 	ldr	r2,[r0]

00000148 e3520000    237 	cmp	r2,0

0000014c e2420001    238 	sub	r0,r2,1

00000150 a1a04002    239 	movge	r4,r2

00000154 b3a04000    240 	movlt	r4,0

00000158 e1b031a4    241 	movs	r3,r4 lsr 3

0000015c 0a000024    242 	beq	.L575

00000160 e08c2000    243 	add	r2,r12,r0

00000164 e2422007    244 	sub	r2,r2,7

                     245 .L576:

00000168 e5d25007    246 	ldrb	r5,[r2,7]

0000016c e1550001    247 	cmp	r5,r1

00000170 0a00002a    248 	beq	.L524

00000174 e5d25006    249 	ldrb	r5,[r2,6]

00000178 e1550001    250 	cmp	r5,r1

0000017c 02400001    251 	subeq	r0,r0,1

00000180 0a000026    252 	beq	.L524

00000184 e5d25005    253 	ldrb	r5,[r2,5]

00000188 e1550001    254 	cmp	r5,r1

0000018c 02400002    255 	subeq	r0,r0,2

00000190 0a000022    256 	beq	.L524

00000194 e5d25004    257 	ldrb	r5,[r2,4]

00000198 e1550001    258 	cmp	r5,r1

0000019c 02400003    259 	subeq	r0,r0,3

000001a0 0a00001e    260 	beq	.L524

000001a4 e5d25003    261 	ldrb	r5,[r2,3]

000001a8 e1550001    262 	cmp	r5,r1

000001ac 02400004    263 	subeq	r0,r0,4

000001b0 0a00001a    264 	beq	.L524

000001b4 e5d25002    265 	ldrb	r5,[r2,2]

000001b8 e1550001    266 	cmp	r5,r1

000001bc 02400005    267 	subeq	r0,r0,5

000001c0 0a000016    268 	beq	.L524

000001c4 e5d25001    269 	ldrb	r5,[r2,1]

000001c8 e1550001    270 	cmp	r5,r1

000001cc 02400006    271 	subeq	r0,r0,6

000001d0 0a000012    272 	beq	.L524

000001d4 e5d25000    273 	ldrb	r5,[r2]

000001d8 e1550001    274 	cmp	r5,r1

000001dc 02400007    275 	subeq	r0,r0,7

000001e0 0a00000e    276 	beq	.L524

000001e4 e2422008    277 	sub	r2,r2,8

000001e8 e2400008    278 	sub	r0,r0,8

000001ec e2533001    279 	subs	r3,r3,1

000001f0 1affffdc    280 	bne	.L576

                     281 .L575:

000001f4 e2143007    282 	ands	r3,r4,7

000001f8 0a000007    283 	beq	.L529

000001fc e080200c    284 	add	r2,r0,r12

                     285 .L610:

00000200 e5d2c000    286 	ldrb	r12,[r2]

00000204 e15c0001    287 	cmp	r12,r1

00000208 0a000004    288 	beq	.L524

                     289 .L613:

0000020c e2422001    290 	sub	r2,r2,1

00000210 e2400001    291 	sub	r0,r0,1

00000214 e2533001    292 	subs	r3,r3,1

00000218 1afffff8    293 	bne	.L610

                     294 .L529:


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_64k1.s
                     295 ;54:         }


                     296 ;55:     }


                     297 ;56:     return -1;


                     298 

0000021c e3e00000    299 	mvn	r0,0

                     300 .L524:

00000220 e8bd0030    301 	ldmfd	[sp]!,{r4-r5}

00000224 e12fff1e*   302 	ret	

                     303 	.endf	StringView_findCharBack

                     304 	.align	4

                     305 ;pos	r0	local

                     306 

                     307 ;strView	r0	param

                     308 ;ch	r1	param

                     309 

                     310 	.section ".bss","awb"

                     311 .L868:

                     312 	.data

                     313 	.text

                     314 

                     315 ;57: }


                     316 

                     317 ;58: 


                     318 ;59: int StringView_splitChar(StringView* src, unsigned char splitChar,


                     319 	.align	4

                     320 	.align	4

                     321 StringView_splitChar::

00000228 e92d4070    322 	stmfd	[sp]!,{r4-r6,lr}

                     323 ;60: 	StringView* dst1, StringView* dst2)


                     324 ;61: {


                     325 

                     326 ;62: 	int splitPos = StringView_findChar(src, splitChar);


                     327 

0000022c e1a06002    328 	mov	r6,r2

00000230 e1a05003    329 	mov	r5,r3

00000234 e1a04000    330 	mov	r4,r0

00000238 ebffff80*   331 	bl	StringView_findChar

                     332 ;63: 	if (splitPos == -1)


                     333 

0000023c e3700001    334 	cmn	r0,1

                     335 ;64: 	{


                     336 

                     337 ;65: 		return 0;


                     338 

00000240 03a00000    339 	moveq	r0,0

00000244 0a00000b    340 	beq	.L940

                     341 ;66: 	}


                     342 ;67: 	dst1->len = splitPos;


                     343 

00000248 e5941004    344 	ldr	r1,[r4,4]

0000024c e5860000    345 	str	r0,[r6]

                     346 ;68: 	dst1->p = src->p;


                     347 

00000250 e5861004    348 	str	r1,[r6,4]

                     349 ;69: 	dst2->len = src->len - splitPos - 1;


                     350 

00000254 e5941000    351 	ldr	r1,[r4]

00000258 e0411000    352 	sub	r1,r1,r0

0000025c e2411001    353 	sub	r1,r1,1

00000260 e5851000    354 	str	r1,[r5]

                     355 ;70: 	dst2->p = src->p + splitPos + 1;



                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_64k1.s
                     356 

00000264 e5941004    357 	ldr	r1,[r4,4]

00000268 e0800001    358 	add	r0,r0,r1

0000026c e2800001    359 	add	r0,r0,1

00000270 e5850004    360 	str	r0,[r5,4]

                     361 ;71: 	return 1;


                     362 

00000274 e3a00001    363 	mov	r0,1

                     364 .L940:

00000278 e8bd8070    365 	ldmfd	[sp]!,{r4-r6,pc}

                     366 	.endf	StringView_splitChar

                     367 	.align	4

                     368 ;splitPos	r0	local

                     369 

                     370 ;src	r4	param

                     371 ;splitChar	none	param

                     372 ;dst1	r6	param

                     373 ;dst2	r5	param

                     374 

                     375 	.section ".bss","awb"

                     376 .L982:

                     377 	.data

                     378 	.text

                     379 

                     380 ;72: }


                     381 

                     382 ;73: 


                     383 ;74: int StringView_cmp(StringView * strView1, StringView * strView2)


                     384 	.align	4

                     385 	.align	4

                     386 StringView_cmp::

0000027c e1a03000    387 	mov	r3,r0

                     388 ;75: {


                     389 

                     390 ;76: 	int result;


                     391 ;77:     int lenDiff;


                     392 ;78:     lenDiff = strView1->len - strView2->len;


                     393 

00000280 e5910000    394 	ldr	r0,[r1]

00000284 e5932000    395 	ldr	r2,[r3]

00000288 e0520000    396 	subs	r0,r2,r0

                     397 ;79: 	if (lenDiff != 0)


                     398 

                     399 ;82: 	}


                     400 ;83: 	result = memcmp(strView1->p, strView2->p, strView1->len);


                     401 

0000028c 05930004    402 	ldreq	r0,[r3,4]

00000290 05911004    403 	ldreq	r1,[r1,4]

00000294 0a000000*   404 	beq	memcmp

                     405 ;80: 	{


                     406 

                     407 ;81: 		return lenDiff;


                     408 

00000298 e12fff1e*   409 	ret	

                     410 	.endf	StringView_cmp

                     411 	.align	4

                     412 ;lenDiff	r0	local

                     413 

                     414 ;strView1	r3	param

                     415 ;strView2	r1	param

                     416 


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_64k1.s
                     417 	.section ".bss","awb"

                     418 .L1030:

                     419 	.data

                     420 	.text

                     421 

                     422 ;85: }


                     423 

                     424 ;86: 


                     425 ;87: int StringView_cmpCStr(StringView * strView, const char* cstr)


                     426 	.align	4

                     427 	.align	4

                     428 StringView_cmpCStr::

0000029c e92d4030    429 	stmfd	[sp]!,{r4-r5,lr}

                     430 ;88: {


                     431 

                     432 ;89: 	StringView cstrView;


                     433 ;90: 


                     434 ;91:     StringView_init(&cstrView, cstr, strlen(cstr));


                     435 

000002a0 e24dd008    436 	sub	sp,sp,8

000002a4 e1a05000    437 	mov	r5,r0

000002a8 e1a04001    438 	mov	r4,r1

000002ac e1a00004    439 	mov	r0,r4

000002b0 eb000000*   440 	bl	strlen

000002b4 e1a01004    441 	mov	r1,r4

000002b8 e1a02000    442 	mov	r2,r0

000002bc e1a0000d    443 	mov	r0,sp

000002c0 ebffff4e*   444 	bl	StringView_init

                     445 ;92: 	return StringView_cmp(strView, &cstrView);


                     446 

000002c4 e1a0100d    447 	mov	r1,sp

000002c8 e1a00005    448 	mov	r0,r5

000002cc ebffffea*   449 	bl	StringView_cmp

000002d0 e28dd008    450 	add	sp,sp,8

000002d4 e8bd8030    451 	ldmfd	[sp]!,{r4-r5,pc}

                     452 	.endf	StringView_cmpCStr

                     453 	.align	4

                     454 ;cstrView	[sp]	local

                     455 

                     456 ;strView	r5	param

                     457 ;cstr	r4	param

                     458 

                     459 	.section ".bss","awb"

                     460 .L1070:

                     461 	.data

                     462 	.text

                     463 

                     464 ;93: }


                     465 	.align	4

                     466 

                     467 	.data

                     468 	.ghsnote version,6

                     469 	.ghsnote tools,3

                     470 	.ghsnote options,0

                     471 	.text

                     472 	.align	4

