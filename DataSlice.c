#include "DataSlice.h"
#include <debug.h>
#include "../../../../DataSlice/DataSliceClient/datasliceif.h"
#include "fast_memcpy.h"
#include <stdint.h>

#define MAX_DATA_SLICE 16384

//! TODO: переименовать, добавив префикс lists (напр.listsDataSlice, listsCurrentDataSlice и тд)
//Интерфейс DataSlice
DataSliceIf* dataSliceIf;

DataSliceWnd* currentDataSlice;

#pragma alignvar (4)
uint8_t  dataSliceCopy[MAX_DATA_SLICE];
size_t dataSliceSize;

static OnUpdateDataSliceFunc g_oldCallBack;
static OnUpdateDataSliceFunc g_newCallBack;

//! dataslice для уставок
DataSliceSetts* settsDataSlice;
size_t settsDataSliceSize;

//! возвращает интерфейс для dataslice расчетных значений или уставок
static void *getDataSliceInterface(const char *moduleId)
{
	 GetDataSliceIf getDataSlice;
	 _HANDLE hModule = _GetModuleHandle((char*)moduleId);
	if (!hModule) return NULL;
	
	getDataSlice = (GetDataSliceIf)_GetProcAddress(hModule,GET_DATASLICEIF_PREFIX);
	if (!getDataSlice) return NULL;
	
	return getDataSlice();
	
}

bool dataSliceInit(void)
{
   
	// расчетные значения
    dataSliceIf = getDataSliceInterface(DATA_SLICE_LISTS_MODULE_ID);
	if (!dataSliceIf) 
		return false;
	// getDataSliceWndSize не возвращает 0
	dataSliceSize = dataSliceIf->getDataSliceWndSize();
	
	// уставки
	settsDataSlice = getDataSliceInterface(DATA_SLICE_SETTS_MODULE_ID);
	if (!settsDataSlice)
		return false;
	
	settsDataSliceSize = settsDataSlice->getDataSliceWndSize();
	
	
    return true;
}

void dataSliceCapture(void)
{
	//currentDataSlice = dataSliceIf->getDataSliceWnd();

	fast_memcpy(dataSliceCopy, dataSliceIf->getDataSliceWnd(), dataSliceSize);

	currentDataSlice = (DataSliceWnd*)dataSliceCopy;
}

void dataSliceRelease(void)
{

}

unsigned long long getCurrentDataSliceTime(void)
{
    return dataSliceIf->getDataSliceWnd()->time;
}

unsigned long long dataSliceGetTimeStamp(void)
{
    return currentDataSlice->time;
}

long dataSliceGetFloatValue(uint16_t offset)
{
    long value;    
    if(!dataSliceIf->getAnalogValue(offset, &value))
    {
        value = 0;
        ERROR_REPORT("DataSlice analog value error");
        TRACE("Offset = %d", offset);
    }
    return value;
}

float dataSliceGetRealValue(uint16_t offset)
{
    float value;
    if(!dataSliceIf->getAnalogValue(offset, (void*)&value))
    {
        value = 0;
        ERROR_REPORT("DataSlice analog value error");
        TRACE("Offset = %d", offset);
    }

    return value;
}

bool dataSliceGetBoolValue(uint16_t offset)
{
    bool value;    

    if(!dataSliceIf->getBoolValue(offset, &value))
    {
        value = 0;
        ERROR_REPORT("DataSlice bool value error");
        TRACE("Offset = %d", offset);
    }

    return value != 0;
}

int dataSliceGetIntValue(uint16_t offset)
{
    long value;    
    if(!dataSliceIf->getIntValue(offset, &value))
    {
        value = 0;
        ERROR_REPORT("DataSlice int value error");
        TRACE("Offset = %d", offset);
    }
    return value;
}

bool DataSlice_getBoolFast(void* dataSliceWnd, uint16_t offset)
{
    uint8_t* pWnd = dataSliceWnd;
    return pWnd[offset];
}

bool DataSlice_getBoolFastCurrDS(uint16_t offset)
{
	uint8_t* pWnd = (uint8_t*)currentDataSlice;
	return pWnd[offset];
}

int DataSlice_getInt32FastCurrDS(uint16_t offset)
{
	uint8_t* pWnd = (uint8_t*)currentDataSlice;
	return *(int*)(pWnd+offset);
}

uint32_t DataSlice_getUInt32FastCurrDS(uint16_t offset)
{
    uint8_t* pWnd = (uint8_t*)currentDataSlice;
    return *(uint32_t*)(pWnd+offset);
}

float DataSlice_getRealFastCurrDS(uint16_t offset)
{
	uint8_t* pWnd = (uint8_t*)currentDataSlice;
	return *(float*)(pWnd+offset);
}

int DataSlice_getFixedFastCurrDS(uint16_t offset)
{
	uint8_t* pWnd = (uint8_t*)currentDataSlice;
	return *(int*)(pWnd+offset);
}


void* DataSlice_getDataSliceWnd(void)
{
    return dataSliceIf->getDataSliceWnd();
}

int DataSlice_getBoolOffset(int offset)
{
    uint16_t dsOffset;

    if(offset == -1)
    {
        return -1;
    }
    dsOffset = dataSliceIf->getBoolOffset(offset);
    if(dsOffset == 0xFFFF)
    {
		TRACE("DataSlice bool offset  %x is not found", offset);
        ERROR_REPORT("DataSlice bool offset  %d is not found", offset);
        return -1;
    }
    return dsOffset;
}

int DataSlice_getIntOffset(int offset)
{
	uint16_t dsOffset;

	dsOffset = dataSliceIf->getIntOffset(offset);
	if(dsOffset == 0xFFFF)
	{
		ERROR_REPORT("DataSlice int offset  %d is not found", offset);
		return -1;
	}
	return dsOffset;
}

int DataSlice_getAnalogOffset(int offset)
{
	uint16_t dsOffset;
	dsOffset = dataSliceIf->getAnalogOffset(offset);
	if(dsOffset == 0xFFFF)
	{
		ERROR_REPORT("DataSlice analog offset  %d is not found", offset);
		return -1;
	}
	return dsOffset;
}

static void DataSlice_callback()
{	
    g_newCallBack();
    if(g_oldCallBack)
    {
        g_oldCallBack();
    }
}

void DataSlice_setCallBack(void (*func)(void))
{
    g_newCallBack = func;
    g_oldCallBack = dataSliceIf->setOnUpdateCallback(DataSlice_callback);
}
