#pragma once

#include "reporter.h"
#include "bufView.h"


// Режим обработки набора данных в отчёте:
// инициализация начальных значений
// General Interrorgation
// Режим сравнения на data change или quality change
typedef enum {RPT_GI, RPT_INTG, RPT_INIT, RPT_CMP} RptProcessMode;


//Reason for Inclusion
#define REASON_DCHG DCHG
#define REASON_QCHG QCHG
#define REASON_GI 1
#define REASON_INTEGRITY (1 << 1)

// Структура для параметров обработки отчёта (проверки на изменения, GI, 
// инициализации начальных значений)
typedef struct {
	RptProcessMode mode;		
	//Индекс элемета DataSet
	size_t itemIdx;
	BufferView* outBuf;
    uint8_t *inclusionReasons;
	Reporter* reporter;
} RptProcContext;
