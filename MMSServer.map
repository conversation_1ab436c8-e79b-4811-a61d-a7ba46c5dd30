Link Date:	Tu<PERSON> Sep 30 09:56:09 2025
Host OS:	GHS_WIN32
Version:	ELXR 4.0 (c) 1998-2003 Green Hills Software    Build: Mar 24 2006


Load Map Tue Sep 30 09:56:09 2025
Image Summary

  Section              Base      Size(hex)    Size(dec)  SecOffs
  .text                70528644  0001a114       106772   0000120
  .interfunc           70542758  00000004            4   001a234
  .rodata              7054275c  000005f8         1528   001a238
  .fixaddr             70542d54  00000000            0   001a830
  .fixtype             70542d54  00000000            0   001a830
  .data                70542d60  0000051c         1308   001a83c
  .bss                 7054327c  0039a088      3776648   001ad58
  .ghsinfo             708dd304  000000d4          212   001ad58
  .stack               708dd3d8  00001000         4096   001ae2c
  .rela.text           00000000  0000c108        49416   001ae2c
  .rela.rodata         00000000  00000000            0   0026f34
  .rela.data           00000000  00000318          792   0026f34
  .rela.bss            00000000  00000000            0   002724c
  .rela.ghsinfo        00000000  000000d8          216   002724c
  .rela.fixaddr        00000000  00000000            0   0027324
  .rela.fixtype        00000000  00000000            0   0027324
  .rel.text            00000000  00000030           48   0027324
  .rela.interfunc      00000000  0000000c           12   002736c

Load Map Tue Sep 30 09:56:09 2025
Module Summary

  Origin+Size    Section          Module
70528644+000074  .text            c0_bss.obj
7054327c+000008  .bss             c0_bss.obj
00000000+0000c0  .rela.text       c0_bss.obj
705286b8+000034  .text            indarchk.o
000000c0+000030  .rela.text       indarchk.o
705286ec+00002c  .text            indarchj.o
000000f0+000030  .rela.text       indarchj.o
70528718+000710  .text            ccvsprnt.o
00000120+00051c  .rela.text       ccvsprnt.o
70542d60+00005c  .data            ccvsprnt.o
00000000+000024  .rela.ghsinfo    ccvsprnt.o
70528e28+00021c  .text            ccllout.o
0000063c+00012c  .rela.text       ccllout.o
70529044+000404  .text            ccefgout.o
00000768+000204  .rela.text       ccefgout.o
70542dbc+000011  .data            ccefgout.o
70529448+0001b0  .text            iedTree.o
70542dd0+00000c  .data            iedTree.o
70543284+000001  .bss             iedTree.o
0000096c+000174  .rela.text       iedTree.o
00000000+00000c  .rela.data       iedTree.o
705295f8+0008f8  .text            iedEntity.o
00000ae0+0003f0  .rela.text       iedEntity.o
00000024+000024  .rela.ghsinfo    iedEntity.o
70529ef0+0004b8  .text            iedObjects.o
00000ed0+0001a4  .rela.text       iedObjects.o
7052a3a8+000174  .text            iedFC.o
70542ddc+000048  .data            iedFC.o
7054275c+000036  .rodata          iedFC.o
00001074+000090  .rela.text       iedFC.o
0000000c+0000d8  .rela.data       iedFC.o
7052a51c+0006d4  .text            iedControlModel.o
00001104+0002c4  .rela.text       iedControlModel.o
7052abf0+00065c  .text            iedFinalDA.o
70542e24+000004  .data            iedFinalDA.o
000013c8+000288  .rela.text       iedFinalDA.o
7052b24c+00021c  .text            iedQuality.o
00001650+000168  .rela.text       iedQuality.o
7052b468+0000ec  .text            iedBool.o
000017b8+0000b4  .rela.text       iedBool.o
7052b554+0003e8  .text            iedFloat.o
0000186c+00027c  .rela.text       iedFloat.o
7052b93c+0002b0  .text            iedCodedEnum.o
00001ae8+000180  .rela.text       iedCodedEnum.o
7052bbec+000124  .text            iedInt.o
00001c68+0000cc  .rela.text       iedInt.o
7052bd10+000124  .text            iedUInt.o
00001d34+0000cc  .rela.text       iedUInt.o
7052be34+000188  .text            iedEnum.o
00001e00+0000fc  .rela.text       iedEnum.o
7052bfbc+000144  .text            iedTimeStamp.o
00001efc+000048  .rela.text       iedTimeStamp.o
7052c100+0000ac  .text            iedConstDA.o
00001f44+000084  .rela.text       iedConstDA.o
7052c1ac+00008c  .text            iedNoEntity.o
70543288+00006c  .bss             iedNoEntity.o
70542794+000004  .rodata          iedNoEntity.o
00001fc8+0000cc  .rela.text       iedNoEntity.o
7052c238+0002c8  .text            DataSet.o
00002094+0000a8  .rela.text       DataSet.o
7052c500+000140  .text            RptItem.o
70542e28+000020  .data            RptItem.o
0000213c+0000f0  .rela.text       RptItem.o
000000e4+000060  .rela.data       RptItem.o
7052c640+0001f8  .text            RptDataSet.o
0000222c+000054  .rela.text       RptDataSet.o
7052c838+000054  .text            RptBool.o
70542e48+000010  .data            RptBool.o
00002280+000054  .rela.text       RptBool.o
00000144+000030  .rela.data       RptBool.o
7052c88c+000254  .text            ObjectNameParser.o
000022d4+0000f0  .rela.text       ObjectNameParser.o
7052cae0+000664  .text            infoReport.o
70542e58+00001c  .data            infoReport.o
705432f4+000084  .bss             infoReport.o
70542798+000010  .rodata          infoReport.o
000023c4+0003b4  .rela.text       infoReport.o
00000174+00000c  .rela.data       infoReport.o
7052d144+000330  .text            control.o
70542e74+000004  .data            control.o
70543378+006000  .bss             control.o
00002778+00021c  .rela.text       control.o
7052d474+000438  .text            fast_memcpy.o
00002994+00000c  .rela.text       fast_memcpy.o
7052d8ac+00024c  .text            BERCoder.o
000029a0+000018  .rela.text       BERCoder.o
7052daf8+0001d0  .text            lwip_socket.o
70542e78+000010  .data            lwip_socket.o
000029b8+0000d8  .rela.text       lwip_socket.o
7052dcc8+000008  .text            platformTools.o
00002a90+000018  .rela.text       platformTools.o
7052dcd0+0001fc  .text            timers_iednexus.o
70542e88+000014  .data            timers_iednexus.o
70549378+000014  .bss             timers_iednexus.o
00002aa8+0001ec  .rela.text       timers_iednexus.o
7052decc+0000cc  .text            main.o
00002c94+000120  .rela.text       main.o
7052df98+000080  .text            frsm.o
00002db4+000060  .rela.text       frsm.o
7052e018+001010  .text            goose.o
70542e9c+000004  .data            goose.o
7054938c+002ba0  .bss             goose.o
705427a8+000028  .rodata          goose.o
00002e14+0007bc  .rela.text       goose.o
7052f028+000290  .text            netTools.o
70542ea0+000004  .data            netTools.o
7054bf2c+000018  .bss             netTools.o
000035d0+000288  .rela.text       netTools.o
7052f2b8+000074  .text            BusError.o
70542ea4+000004  .data            BusError.o
00003858+00006c  .rela.text       BusError.o
7052f32c+00020c  .text            ConfigFiles.o
70542ea8+000040  .data            ConfigFiles.o
000038c4+0000b4  .rela.text       ConfigFiles.o
00000180+000018  .rela.data       ConfigFiles.o
7052f538+000620  .text            OscFiles.o
00003978+000420  .rela.text       OscFiles.o
7052fb58+00008c  .text            MemoryManager.o
70542ee8+000008  .data            MemoryManager.o
00003d98+000084  .rela.text       MemoryManager.o
7052fbe4+0000fc  .text            mms_gocb.o
00003e1c+000060  .rela.text       mms_gocb.o
7052fce0+0004c0  .text            FinalDA.o
00003e7c+0001d4  .rela.text       FinalDA.o
705301a0+0002d8  .text            stringView.o
00004050+000090  .rela.text       stringView.o
70530478+0001a4  .text            bufView.o
000040e0+00006c  .rela.text       bufView.o
7053061c+0007bc  .text            bufViewBER.o
0000414c+0001b0  .rela.text       bufViewBER.o
70530dd8+00003c  .text            bufViewMMS.o
000042fc+00000c  .rela.text       bufViewMMS.o
70530e14+0004ac  .text            file_system.o
705427d0+00000c  .rodata          file_system.o
00004308+0002ac  .rela.text       file_system.o
705312c0+000010  .text            platform_thread.o
000045b4+00000c  .rela.text       platform_thread.o
705312d0+0015f8  .text            OscConverter.o
705427dc+000064  .rodata          OscConverter.o
000045c0+000c84  .rela.text       OscConverter.o
00000198+000018  .rela.data       OscConverter.o
705328c8+0001b8  .text            OscDescr.o
70542ef0+000004  .data            OscDescr.o
00005244+0000f0  .rela.text       OscDescr.o
70532a80+000b28  .text            OscInfo.o
70542ef4+000068  .data            OscInfo.o
7054bf44+00002c  .bss             OscInfo.o
00005334+000750  .rela.text       OscInfo.o
000001b0+000138  .rela.data       OscInfo.o
705335a8+0004ac  .text            OscReadFileContext.o
00005a84+000294  .rela.text       OscReadFileContext.o
70533a54+000234  .text            OscWriteBuffer.o
00005d18+0000fc  .rela.text       OscWriteBuffer.o
70533c88+000ddc  .text            tlsf.o
70542840+000008  .rodata          tlsf.o
00005e14+00081c  .rela.text       tlsf.o
70534a64+000038  .text            timetools.o
00006630+000018  .rela.text       timetools.o
70534a9c+000118  .text            crc32.o
70542848+000400  .rodata          crc32.o
00006648+000030  .rela.text       crc32.o
70534bb4+000fb8  .text            fdzipstream.o
70542f5c+000004  .data            fdzipstream.o
00006678+00057c  .rela.text       fdzipstream.o
70535b6c+0017c0  .text            AsnEncoding.o
00006bf4+000258  .rela.text       AsnEncoding.o
7053732c+0003d4  .text            Cotp.o
70542c48+00008c  .rodata          Cotp.o
00006e4c+000180  .rela.text       Cotp.o
70537700+00019c  .text            session.o
70542f60+000004  .data            session.o
70542cd4+000004  .rodata          session.o
00006fcc+00006c  .rela.text       session.o
7053789c+0003cc  .text            presentation.o
70542f64+000014  .data            presentation.o
00007038+0001d4  .rela.text       presentation.o
70537c68+0001ac  .text            acse.o
70542f78+000008  .data            acse.o
0000720c+000108  .rela.text       acse.o
70537e14+0006e8  .text            mms.o
70542f80+000010  .data            mms.o
00007314+000390  .rela.text       mms.o
705384fc+0001fc  .text            mms_get_variable_access_attributes.o
70542cd8+000014  .rodata          mms_get_variable_access_attributes.o
000076a4+000114  .rela.text       mms_get_variable_access_attributes.o
705386f8+00037c  .text            mms_get_data_set_access_attr.o
000077b8+0001b0  .rela.text       mms_get_data_set_access_attr.o
70538a74+0003a4  .text            mms_get_name_list.o
00007968+000144  .rela.text       mms_get_name_list.o
70538e18+000464  .text            mms_rcb.o
00007aac+000198  .rela.text       mms_rcb.o
00000048+000024  .rela.ghsinfo    mms_rcb.o
7053927c+000014  .text            debug.o
00007c44+00003c  .rela.text       debug.o
70539290+000010  .text            platform_critical_section.o
00007c80+000030  .rela.text       platform_critical_section.o
705392a0+000404  .text            mms_read.o
00007cb0+0001e0  .rela.text       mms_read.o
705396a4+000768  .text            mms_write.o
00007e90+000198  .rela.text       mms_write.o
70539e0c+000cd8  .text            mms_data.o
70542cec+00000c  .rodata          mms_data.o
00008028+0004ec  .rela.text       mms_data.o
0000006c+000024  .rela.ghsinfo    mms_data.o
7053aae4+0007cc  .text            mms_fs.o
70542cf8+000020  .rodata          mms_fs.o
00008514+000360  .rela.text       mms_fs.o
7053b2b0+00124c  .text            iedmodel.o
70542f90+000044  .data            iedmodel.o
00008874+0007e0  .rela.text       iedmodel.o
000002e8+00000c  .rela.data       iedmodel.o
00000090+000048  .rela.ghsinfo    iedmodel.o
7053c4fc+000498  .text            mmsServices.o
70542fd4+00000c  .data            mmsServices.o
70542d18+000014  .rodata          mmsServices.o
00009054+0001d4  .rela.text       mmsServices.o
000002f4+000024  .rela.data       mmsServices.o
7053c994+000210  .text            mms_error.o
70542d2c+000008  .rodata          mms_error.o
00009228+00009c  .rela.text       mms_error.o
7053cba4+0001a0  .text            DomainNameWriter.o
000092c4+000054  .rela.text       DomainNameWriter.o
7053cd44+0004fc  .text            pwin_access.o
7054bf70+000084  .bss             pwin_access.o
00009318+000264  .rela.text       pwin_access.o
7053d240+0003a4  .text            DataSlice.o
7054bff4+000008  .bss             DataSlice.o
0000957c+000348  .rela.text       DataSlice.o
7053d5e4+000120  .text            out_queue.o
000098c4+000078  .rela.text       out_queue.o
7053d704+00009c  .text            send_thread.o
0000993c+000024  .rela.text       send_thread.o
7053d7a0+00023c  .text            out_buffers.o
00009960+0000a8  .rela.text       out_buffers.o
7053d9dc+000484  .text            rcb.o
70542d34+000010  .rodata          rcb.o
00009a08+000378  .rela.text       rcb.o
7053de60+0014c8  .text            reports.o
70542fe0+000010  .data            reports.o
7054bffc+008140  .bss             reports.o
00009d80+000708  .rela.text       reports.o
7053f328+0003fc  .text            ReportQueue.o
0000a488+0000f0  .rela.text       ReportQueue.o
7053f724+000030  .text            connections.o
0000a578+000024  .rela.text       connections.o
7053f754+0000d8  .text            server.o
70542ff0+000004  .data            server.o
7055413c+000001  .bss             server.o
70542d44+000010  .rodata          server.o
0000a59c+0000d8  .rela.text       server.o
7053f82c+000004  .text            MMSServer.o
0000a674+00000c  .rela.text       MMSServer.o
7053f830+000004  .text            gmalloc.lib(malloc.o)
0000a680+00000c  .rela.text       gmalloc.lib(malloc.o)
7053f834+000004  .text            gmalloc.lib(free.o)
0000a68c+00000c  .rela.text       gmalloc.lib(free.o)
7053f838+00002c  .text            ARMLIBV4.lib(idivmod.o)
00000000+000010  .rel.text        ARMLIBV4.lib(idivmod.o)
7053f864+000148  .text            ARMLIBV4.lib(idiv.o)
00000010+000008  .rel.text        ARMLIBV4.lib(idiv.o)
7053f9ac+000004  .text            ARMLIBV4.lib(div0.o)
7053f9b0+000074  .text            CLib.lib(atoi.o)
0000a698+000024  .rela.text       CLib.lib(atoi.o)
7053fa24+000024  .text            CLib.lib(strlen.o)
0000a6bc+00000c  .rela.text       CLib.lib(strlen.o)
7053fa48+000014  .text            CLib.lib(itoa.o)
0000a6c8+00000c  .rela.text       CLib.lib(itoa.o)
7053fa5c+000044  .text            CLib.lib(lmt.o)
0000a6d4+000018  .rela.text       CLib.lib(lmt.o)
7053faa0+0001c0  .text            CLib.lib(gmt.o)
0000a6ec+000048  .rela.text       CLib.lib(gmt.o)
7053fc60+00003c  .text            CLib.lib(strncpy.o)
0000a734+00000c  .rela.text       CLib.lib(strncpy.o)
7053fc9c+000064  .text            CLib.lib(lockinterrupt.o)
0000a740+00000c  .rela.text       CLib.lib(lockinterrupt.o)
70554140+00000c  .bss             CLib.lib(osdata.obj)
7053fd00+000018  .text            CLib.lib(setvect.obj)
7053fd18+000020  .text            CLib.lib(getvect.obj)
7053fd38+000018  .text            CLib.lib(memset.obj)
0000a74c+00000c  .rela.text       CLib.lib(memset.obj)
7053fd50+000044  .text            CLib.lib(memcpy.obj)
0000a758+000024  .rela.text       CLib.lib(memcpy.obj)
7053fd94+000014  .text            CLib.lib(CrTimer.obj)
7053fda8+000028  .text            CLib.lib(exit.obj)
0000a77c+00003c  .rela.text       CLib.lib(exit.obj)
7053fdd0+000018  .text            CLib.lib(GetPH.obj)
7053fde8+00001c  .text            CLib.lib(GetPA.obj)
7053fe04+000018  .text            CLib.lib(gmema.obj)
7053fe1c+000014  .text            CLib.lib(gmemf.obj)
7053fe30+000048  .text            CLib.lib(CS.obj)
0000a7b8+00000c  .rela.text       CLib.lib(CS.obj)
7053fe78+00000c  .text            CLib.lib(listlist.obj)
7053fe84+000040  .text            CLib.lib(days.obj)
7053fec4+000030  .text            CLib.lib(memcmp.obj)
0000a7c4+000018  .rela.text       CLib.lib(memcmp.obj)
7053fef4+000044  .text            CLib.lib(thread.obj)
70542ff4+000104  .data            CLib.lib(ctype.o)
7053ff38+000074  .text            CLib.lib(__ltoa.o)
0000a7dc+000018  .rela.text       CLib.lib(__ltoa.o)
7053ffac+000630  .text            lwiplib.lib(lwiplib.obj)
70543100+000104  .data            lwiplib.lib(lwiplib.obj)
0000a7f4+000b10  .rela.text       lwiplib.lib(lwiplib.obj)
705405dc+000038  .text            ethbus.lib(ethbuslib.o)
70543204+000008  .data            ethbus.lib(ethbuslib.o)
0000b304+000030  .rela.text       ethbus.lib(ethbuslib.o)
70540614+00047c  .text            libansi.a(ccdocvt.o)
7054320c+000070  .data            libansi.a(ccdocvt.o)
0000b334+00030c  .rela.text       libansi.a(ccdocvt.o)
70540a90+0000a8  .text            libansi.a(ccsnprtf.o)
0000b640+000060  .rela.text       libansi.a(ccsnprtf.o)
70542758+000004  .interfunc       libarch.a(indarch4.o)
00000000+00000c  .rela.interfunc  libarch.a(indarch4.o)
70540b38+000058  .text            libarch.a(indbsch2.o)
0000b6a0+000030  .rela.text       libarch.a(indbsch2.o)
70540b90+000044  .text            libarch.a(indclz32.o)
0000b6d0+00000c  .rela.text       libarch.a(indclz32.o)
70540bd4+000030  .text            libind.a(indfcmp.o)
0000b6dc+00000c  .rela.text       libind.a(indfcmp.o)
70540c04+00003c  .text            libind.a(inddcmp.o)
0000b6e8+000018  .rela.text       libind.a(inddcmp.o)
70540c40+000254  .text            libind.a(indfto64.o)
0000b700+0000cc  .rela.text       libind.a(indfto64.o)
70540e94+000368  .text            libind.a(inddto64.o)
0000b7cc+0000d8  .rela.text       libind.a(inddto64.o)
705411fc+00009c  .text            libind.a(indfmul.o)
0000b8a4+00003c  .rela.text       libind.a(indfmul.o)
70541298+0000e4  .text            libind.a(indfdiv.o)
0000b8e0+000060  .rela.text       libind.a(indfdiv.o)
7054137c+00041c  .text            libind.a(inddadd.o)
0000b940+000120  .rela.text       libind.a(inddadd.o)
70541798+000104  .text            libind.a(inddmul.o)
0000ba60+00003c  .rela.text       libind.a(inddmul.o)
7054189c+0003d0  .text            libind.a(indddiv.o)
0000ba9c+000060  .rela.text       libind.a(indddiv.o)
70541c6c+0000b4  .text            libind.a(indfto32.o)
0000bafc+0000b4  .rela.text       libind.a(indfto32.o)
70541d20+000140  .text            libind.a(inddto32.o)
0000bbb0+000054  .rela.text       libind.a(inddto32.o)
70541e60+000070  .text            libind.a(ind32tof.o)
0000bc04+00003c  .rela.text       libind.a(ind32tof.o)
70541ed0+00006c  .text            libind.a(ind32tod.o)
0000bc40+00003c  .rela.text       libind.a(ind32tod.o)
70541f3c+000024  .text            libind.a(indftod.o)
0000bc7c+000018  .rela.text       libind.a(indftod.o)
70541f60+000054  .text            libind.a(inddtof.o)
0000bc94+000030  .rela.text       libind.a(inddtof.o)
70541fb4+00004c  .text            libind.a(indisinf.o)
0000bcc4+000024  .rela.text       libind.a(indisinf.o)
70542000+000060  .text            libind.a(indisnan.o)
0000bce8+00003c  .rela.text       libind.a(indisnan.o)
70542060+0000f8  .text            libind.a(ind64_div64.o)
0000bd24+0000a8  .rela.text       libind.a(ind64_div64.o)
70542158+0000dc  .text            libind.a(ind64_rem64.o)
0000bdcc+000054  .rela.text       libind.a(ind64_rem64.o)
70542234+0000c0  .text            libind.a(ind64_lsh64.o)
0000be20+0000c0  .rela.text       libind.a(ind64_lsh64.o)
705422f4+0000d4  .text            libind.a(ind64_rsh64.o)
0000bee0+0000c0  .rela.text       libind.a(ind64_rsh64.o)
705423c8+0001c0  .text            libind.a(ind64_udiv64.o)
0000bfa0+00012c  .rela.text       libind.a(ind64_udiv64.o)
70542588+000090  .text            libind.a(ind64_urem64.o)
0000c0cc+00003c  .rela.text       libind.a(ind64_urem64.o)
70542618+00002c  .text            ARMLIBV4.lib(udivmod.o)
00000018+000010  .rel.text        ARMLIBV4.lib(udivmod.o)
70542644+000114  .text            ARMLIBV4.lib(udiv.o)
00000028+000008  .rel.text        ARMLIBV4.lib(udiv.o)

Load Map Tue Sep 30 09:56:09 2025
Global Symbols (sorted alphabetically)

 .text            70537c74 AcseConnection_createAssociateResponseMessage
 .text            70537c68 AcseConnection_init
 .data            70542f6c Asn_Id_Acse
 .data            70542f70 Asn_Id_Mms
 .text            7052d8ac BERCoder_calcIntEncodedLen
 .text            7052da7c BERCoder_reverseCopy
 .text            70536074 BerDecoder_DecodeBitStringTLToInt
 .text            70536148 BerDecoder_DecodeLengthOld
 .text            70536378 BerDecoder_DecodeObjectName
 .text            70536414 BerDecoder_DecodeObjectNameToStringView
 .text            705362e0 BerDecoder_DecodeUint32Old
 .text            705372ec BerDecoder_decodeFloat
 .text            70535dd4 BerDecoder_decodeInt32
 .text            70535bac BerDecoder_decodeLength
 .text            70535f28 BerDecoder_decodeString
 .text            70535d58 BerDecoder_decodeTLFromBufferView
 .text            70535e78 BerDecoder_decodeUint32
 .text            7053651c BerEncoder_CompressInteger
 .text            70536994 BerEncoder_EncodeFloatWithTL
 .text            7053689c BerEncoder_EncodeInt32
 .text            70536c28 BerEncoder_EncodeInt32WithTL
 .text            705364d4 BerEncoder_Int32DetermineEncodedSize
 .text            70537118 BerEncoder_RevertByteOrder
 .text            70536474 BerEncoder_UInt32determineEncodedSize
 .text            70536fdc BerEncoder_determineEncodedStringSize
 .text            7053645c BerEncoder_determineFullObjectSize
 .text            70536440 BerEncoder_determineLengthSize
 .text            70536d74 BerEncoder_encodeBitString
 .text            70536f0c BerEncoder_encodeBitStringUshortBuf
 .text            705370f0 BerEncoder_encodeBoolean
 .text            70535b6c BerEncoder_encodeLength
 .text            70536d30 BerEncoder_encodeOctetString
 .text            70537004 BerEncoder_encodeStringWithTL
 .text            70535f10 BerEncoder_encodeTL
 .text            7053679c BerEncoder_encodeUInt32
 .text            70536b18 BerEncoder_encodeUInt32WithTL
 .text            70536f9c BerEncoder_encodeUcharBitString
 .text            70536f48 BerEncoder_encodeUshortBitString
 .text            705364c4 BerEncoder_uint32determineEncodedSizeTL
 .text            70530dd8 BufView_decodeObjectName
 .text            70530600 BufferView_advance
 .text            70530484 BufferView_alloc
 .text            70530794 BufferView_decodeExtTL
 .text            7053084c BufferView_decodeInt32
 .text            705308d4 BufferView_decodeInt32TL
 .text            70530984 BufferView_decodeStringViewTL
 .text            70530704 BufferView_decodeTL
 .text            70530890 BufferView_decodeUInt32
 .text            7053092c BufferView_decodeUInt32TL
 .text            70530c20 BufferView_encodeBoolean
 .text            70530cbc BufferView_encodeBufferView
 .text            70530b28 BufferView_encodeExtTL
 .text            70530b88 BufferView_encodeInt32
 .text            70530c60 BufferView_encodeOctetString
 .text            70530aa8 BufferView_encodeStr
 .text            70530a24 BufferView_encodeStringView
 .text            70530adc BufferView_encodeTL
 .text            70530bd4 BufferView_encodeUInt32
 .text            70530478 BufferView_init
 .text            7053061c BufferView_peekTag
 .text            705305b4 BufferView_readStringView
 .text            70530cc4 BufferView_reverseWrite
 .text            705306bc BufferView_skipAnyObject
 .text            7053063c BufferView_skipObject
 .text            70530574 BufferView_writeData
 .text            70530500 BufferView_writeStr
 .text            705304a8 BufferView_writeStringView
 .text            705309f4 BufferView_writeTag
 .text            70530528 BufferView_writeUshortBE
 .text            7052f304 BusError_check
 .text            7052f2fc BusError_init
 .text            7052f52c CFGFS_closeFile
 .text            7052f528 CFGFS_findClose
 .text            7052f3f4 CFGFS_findFirst
 .text            7052f430 CFGFS_findNext
 .text            7052f394 CFGFS_init
 .text            7052f44c CFGFS_openFile
 .text            7052f4c8 CFGFS_readFile
 .text            7053fe68 CSInit
 .text            7053fe4c CSLock
 .text            7053fe30 CSTrylock
 .text            7053fe68 CSUnlock
 .text            7052d1a4 Control_disableWaitingObjects
 .text            7052d16c Control_processCtrlObjects
 .text            7052d144 Control_registerCtrlObj
 .text            7052d398 Control_sendNegativeCmdTermReport
 .text            7052d2dc Control_sendPositiveCmdTermReport
 .text            7052d1dc Control_sendServiceErrorReport
 .text            7053ca68 CreateMmsConfirmedErrorPdu
 .text            7053fd94 CreateTimer
 .text            7053929c CriticalSection_Done
 .text            70539290 CriticalSection_Init
 .text            70539294 CriticalSection_Lock
 .text            70539298 CriticalSection_Unlock
 .text            7052c424 DataSet_calcReadLen
 .text            7052c4a4 DataSet_encodeRead
 .text            7052c3f8 DataSet_getDataSetObj
 .text            7052c40c DataSet_getFirstItem
 .text            7052c238 DataSet_init
 .text            7052c3ac DataSet_postCreate
 .text            7053d548 DataSlice_getAnalogOffset
 .text            7053d464 DataSlice_getBoolFast
 .text            7053d46c DataSlice_getBoolFastCurrDS
 .text            7053d4d8 DataSlice_getBoolOffset
 .text            7053d4bc DataSlice_getDataSliceWnd
 .text            7053d4ac DataSlice_getFixedFastCurrDS
 .text            7053d47c DataSlice_getInt32FastCurrDS
 .text            7053d514 DataSlice_getIntOffset
 .text            7053d49c DataSlice_getRealFastCurrDS
 .text            7053d48c DataSlice_getUInt32FastCurrDS
 .text            7053d5a8 DataSlice_setCallBack
 .text            70536014 DecodeBIT_STRINGPrimitive
 .text            70535f90 DecodeBOOLEAN
 .text            7053cc60 DomainNameWriter_discardName
 .text            7053cc90 DomainNameWriter_encode
 .text            7053cba4 DomainNameWriter_init
 .text            7053cbf8 DomainNameWriter_pushName
 .text            7053cbc8 DomainNameWriter_setStartName
 .text            70535fbc EncodeBIT_STRINGPrimitive
 .text            70535f68 EncodeBOOLEAN
 .text            7052fce0 FDAQuality_create
 .text            7052fe70 FDAQuality_encodeFixedData
 .text            7052fdd8 FDAQuality_encodeGOOSETemplate
 .text            7052fdc8 FDAQuality_getFixedEncodedSize
 .text            7052fe3c FDAQuality_readAndCompare
 .text            7052fe98 FDA_create
 .text            70530140 FDA_encodeFixedData
 .text            7052ff94 FDA_encodeGOOSETemplate
 .text            7052ff44 FDA_getFixedEncodedSize
 .text            70530050 FDA_readAndCompare
 .text            7052ede8 GOOSE_getGoEna
 .text            7052ee1c GOOSE_getNdsCom
 .text            7052eec0 GOOSE_init
 .text            7052eb00 GOOSE_send
 .text            7052eb40 GOOSE_sendChanges
 .text            7052ee50 GOOSE_setGoEna
 .text            7052e41c GSE_init
 .text            7053feb4 GetDaysPtr
 .text            7053fd18 GetIntCallBack
 .text            7053fe78 GetListOfList
 .text            7052e6e8 GoCB_init
 .text            7052b508 IEDBool_init
 .text            7052bad4 IEDCodedEnum_init
 .text            7052a2d0 IEDComplexObj_calcReadLen
 .text            7052a310 IEDComplexObj_encodeRead
 .text            70529f60 IEDComplexObj_init
 .text            7052a170 IEDComplexObj_write
 .text            7052a0e0 IEDConstDA_calcReadLen
 .text            7052a120 IEDConstDA_encodeRead
 .text            7052c100 IEDConstDA_init
 .text            7052a5dc IEDControlDA_checkTerminate
 .text            7052a664 IEDControlDA_disconnect
 .text            7052a6e8 IEDControlDA_getOrCat
 .text            7052a6a8 IEDControlDA_getOrIdent
 .text            7052a91c IEDControlDA_init
 .text            7052a51c IEDControlDA_isControlDA
 .text            7052a544 IEDControlDA_isReady
 .text            7052a56c IEDControlDA_waitReady
 .text            7052ab4c IEDControlDA_write
 .text            7052ab20 IEDControlDO_init
 .text            7052a810 IEDCtlNum_init
 .text            7052a038 IEDDA_init
 .text            70529ff8 IEDDO_getTimeStampDA
 .text            70529fa4 IEDDO_init
 .text            70529788 IEDEntity_addChild
 .text            705295f8 IEDEntity_alloc
 .text            70529d58 IEDEntity_attachTimeStamp
 .text            705297ec IEDEntity_create
 .text            7052986c IEDEntity_createFromBER
 .text            70529e44 IEDEntity_findChanges
 .text            70529b3c IEDEntity_getChildByCStrName
 .text            70529b74 IEDEntity_getChildByFullName
 .text            70529b04 IEDEntity_getChildByName
 .text            70529c00 IEDEntity_getChildByTag
 .text            70529d2c IEDEntity_getDomainId
 .text            70529cc0 IEDEntity_getFullItemId
 .text            70529c48 IEDEntity_getFullName
 .text            705297b0 IEDEntity_init
 .text            70529ec4 IEDEntity_isFinalDA
 .text            70529820 IEDEntity_postCreate
 .text            70529dcc IEDEntity_printFullName
 .text            70529e90 IEDEntity_setReadOnlyRecursive
 .text            70529e2c IEDEntity_setTimeStamp
 .text            70529c28 IEDEntity_write
 .text            70529d94 IEDEntity_writeFullName
 .text            7052bf70 IEDEnum_init
 .data            70542ddc IEDFCTypeNames
 .text            7052a3a8 IEDFC_init
 .text            7052ac04 IEDFinalDA_init
 .text            7052b900 IEDFloat_init
 .text            7052bcc4 IEDInt32_init
 .text            70529f40 IEDLD_getDataSection
 .text            70529f50 IEDLD_getDataSetSection
 .text            70529ef0 IEDLD_init
 .text            7053c4bc IEDModel_getBufferView
 .text            7053c3c4 IEDModel_getChildren
 .text            7053c428 IEDModel_getTermItemDescrStruct
 .text            7053b484 IEDModel_isServiceInfo
 .text            7053c170 IEDModel_ptrFromPos
 .text            7053c35c IEDModel_skipServiceInfo
 .text            7052c21c IEDNoEntity_get
 .text            7052c1dc IEDNoEntity_init
 .text            7052a71c IEDOrCat_calcReadLen
 .text            7052a838 IEDOrCat_encodeRead
 .text            7052a7dc IEDOrCat_init
 .text            7052a750 IEDOrCat_write
 .text            7052a85c IEDOrIdent_calcReadLen
 .text            7052a880 IEDOrIdent_encodeRead
 .text            7052a7a8 IEDOrIdent_init
 .text            7052a8a4 IEDOrIdent_write
 .text            7052b36c IEDQuality_init
 .text            7052b7bc IEDRealAsInt64_init
 .text            7052b6a0 IEDReal_init
 .text            7052ae6c IEDTermItemDA_calcReadLen
 .text            7052afb4 IEDTermItemDA_encodeRead
 .text            7052ae54 IEDTermItemDA_getTerminalItemDescr
 .text            7052b0f4 IEDTermItemDA_write
 .text            7052bfbc IEDTimeStampDA_calcReadLen
 .text            7052bfcc IEDTimeStampDA_encodeRead
 .text            7052c034 IEDTimeStampDA_write
 .text            705295d0 IEDTree_addToCmpList
 .text            705294c8 IEDTree_findDataByFullName
 .text            70529504 IEDTree_findDataSetByFullName
 .text            70529540 IEDTree_findDataSetBySingleName
 .text            70529448 IEDTree_init
 .text            705294b8 IEDTree_lock
 .text            705294c0 IEDTree_unlock
 .text            7052959c IEDTree_updateFromDataSlice
 .text            70529570 IEDTree_write
 .text            7052bde8 IEDUInt32_init
 .text            7052add0 IEDVarDA_calcReadLen
 .text            7052ae00 IEDVarDA_encodeRead
 .text            7052ae30 IEDVarDA_write
 .text            7052cc60 InfoReport_createLastApplErrorReport
 .text            7052ce28 InfoReport_createNegativeCmdTermReport
 .text            7052ccf8 InfoReport_createPositiveCmdTermReport
 .text            7052d080 InfoReport_send
 .text            70537200 IsIncluded
 .text            70537bb8 IsoPresentation_createUserData
 .text            7053a17c MMSData_encodeTimeStamp
 .text            7053cb3c MMSError_createConfirmedErrorPdu
 .text            7052fb84 MM_alloc
 .text            7052fbd0 MM_getAllocated
 .text            7052fbc0 MM_getRemaining
 .text            7052fb58 MM_init
 .text            7052f248 NetTools_busOK
 .text            7052f200 NetTools_getIf
 .text            7052f218 NetTools_getMac
 .text            7052f170 NetTools_init
 .text            7052f230 NetTools_send
 .text            70532a44 OSCDescr_analogName
 .text            70532a50 OSCDescr_analogUnits
 .text            70532a5c OSCDescr_boolName
 .text            705328d8 OSCDescr_findDescrAnalogItem
 .text            70532948 OSCDescr_findDescrBoolItem
 .text            70532a68 OSCDescr_getFreq
 .text            70532a24 OSCDescr_getTerminalName
 .text            70532a34 OSCDescr_getTerminalVersion
 .text            705329bc OSCDescr_init
 .text            7052f964 OSCFS_closeFile
 .text            7052f790 OSCFS_findClose
 .text            7052f6ec OSCFS_findFirst
 .text            7052f758 OSCFS_findNext
 .text            7052f538 OSCFS_init
 .text            7052f844 OSCFS_openFile
 .text            7052f974 OSCFS_readFile
 .text            70532f28 OSCFrame_getADCPeriod
 .text            70532f58 OSCFrame_getAnalogValue
 .text            70532fd8 OSCFrame_getBoolValue
 .text            705330b0 OSCFrame_getFreq
 .text            70533024 OSCFrame_getTick
 .text            70532bb4 OSCInfo_create
 .text            70532d50 OSCInfo_destroy
 .text            70532df0 OSCInfo_getADCClkFreq
 .text            70532efc OSCInfo_getADCFractionSize
 .text            70532ee4 OSCInfo_getADCSampleSize
 .text            70533528 OSCInfo_getAnalog
 .text            70533540 OSCInfo_getAnalogCft
 .text            70532e50 OSCInfo_getAnalogCount
 .text            70533550 OSCInfo_getAnalogMax
 .text            70533560 OSCInfo_getAnalogMin
 .text            70533534 OSCInfo_getBool
 .text            70532e68 OSCInfo_getBoolCount
 .text            70532f18 OSCInfo_getBufferContent
 .text            70532dc0 OSCInfo_getDateMS
 .text            70532f20 OSCInfo_getFrameBuffer
 .text            70532e98 OSCInfo_getFrameCount
 .text            70532ea0 OSCInfo_getFrameOffset
 .text            70532e80 OSCInfo_getHeaderSize
 .text            70532f14 OSCInfo_getOscContentOffset
 .text            70532dd8 OSCInfo_getOscVersion
 .text            70532e38 OSCInfo_getPointPerFrameCount
 .text            70532e20 OSCInfo_getPrehistFirstFrameNum
 .text            70532e08 OSCInfo_getPrehistFrameCount
 .text            70532da8 OSCInfo_getUTCDate
 .text            70532b10 OSCInfo_init
 .text            7053312c OSCInfo_initContent
 .text            70532d8c OSCInfo_lockHeaderBuf
 .text            70532da0 OSCInfo_unlockHeaderBuf
 .text            7052c8c8 ObjectNameParser_parse
 .text            705328a8 OscConverter_init
 .text            70531a24 OscConverter_processCfg
 .text            70532824 OscConverter_processHdr
 .text            705312d0 OscConverter_processPeriod
 .text            7052f608 OscFiles_free
 .text            7052f598 OscFiles_malloc
 .text            7052f5cc OscFiles_realloc
 .text            70533818 OscReadFileContext_closeCfg
 .text            70533878 OscReadFileContext_closeDat
 .text            705338d8 OscReadFileContext_closeHdr
 .text            70533690 OscReadFileContext_create
 .text            70533774 OscReadFileContext_destroy
 .text            705338e0 OscReadFileContext_flushAndClose
 .text            70533a14 OscReadFileContext_getFreqCfg
 .text            70533a08 OscReadFileContext_getFreqCount
 .text            70533908 OscReadFileContext_getPhistotyTime
 .text            70533938 OscReadFileContext_getPhistotyTimeMS
 .text            705337c4 OscReadFileContext_writeCfgToStream
 .text            70533820 OscReadFileContext_writeDatToStream
 .text            70533988 OscReadFileContext_writeFreq
 .text            70533880 OscReadFileContext_writeHdrToStream
 .text            70533aa0 OscWriteBuffer_attach
 .text            70533a54 OscWriteBuffer_create
 .text            70533be0 OscWriteBuffer_data
 .text            70533bf0 OscWriteBuffer_dataLen
 .text            70533c08 OscWriteBuffer_destroy
 .text            70533bcc OscWriteBuffer_empty
 .text            70533abc OscWriteBuffer_reset
 .text            70533ba4 OscWriteBuffer_resize
 .text            70533bf8 OscWriteBuffer_size
 .text            70533c18 OscWriteBuffer_toBufferView
 .text            70533b60 OscWriteBuffer_toWriteBuffer
 .text            70533acc OscWriteBuffer_write
 .text            7053d5fc OutQueue_done
 .text            7053d6a0 OutQueue_get
 .text            7053d5e4 OutQueue_init
 .text            7053d614 OutQueue_insert
 .text            7053d600 OutQueue_isEmpty
 .text            7052dcc8 PTools_lockInterrupt
 .text            7052dccc PTools_unlockInterrupt
 .text            7053f358 ReportQueue_init
 .text            7053f6e8 ReportQueue_isEmpty
 .text            7053f70c ReportQueue_isOverflow
 .text            7053f6a8 ReportQueue_purge
 .text            7053f5c8 ReportQueue_read
 .text            7053f400 ReportQueue_write
 .text            7053f1fc Reporter_isOwnerConnection
 .text            7053f2d0 Reporter_setDataSetName
 .text            7053f210 Reporter_setEnable
 .text            7053f304 Reporter_setGI
 .text            7053f2e4 Reporter_setIntgPd
 .text            7053f270 Reporter_setResv
 .text            7052c87c RptBool_init
 .text            7052c640 RptDataSetItem_addFinalDARptItem
 .text            7052c65c RptDataSet_create
 .text            7052c788 RptDataSet_flushAndUpdate
 .text            7052c6f0 RptDataSet_updateChanges
 .text            7052c500 RptItem_alloc
 .text            7052c558 RptItem_create
 .text            7053d800 SessionBuffers_done
 .text            7053fd00 SetIntCallBack
 .text            7053041c StringView_cmp
 .text            7053043c StringView_cmpCStr
 .text            705301e0 StringView_findChar
 .text            705302d4 StringView_findCharBack
 .text            705301b8 StringView_fromCStr
 .text            705301ac StringView_fromStringView
 .text            705301a0 StringView_init
 .text            705303c8 StringView_splitChar
 .text            70534a64 TimeTools_gmtime32
 .text            70534a80 TimeTools_localtime32
 .text            7052de98 Timers_getTickCount
 .text            7052dd84 Timers_init
 .text            7052de60 Timers_isTimeout
 .text            7052dd14 Timers_setGoose1msCallBack
 .text            7052dd20 Timers_setIntegrity1msCallBack
 .text            7052dd2c Timers_setNetBusChek1msCallback
 .text            7053fef4 _CreateThread
 .text            7053ff2c _GetExitCodeThread
 .text            7053fdd0 _GetModuleHandle
 .text            7053fde8 _GetProcAddress
 .text            7053ff14 _TerminateThread
 .text            70540b90 __CLZ32
 .bss             7054327c __CS__
 .text            7053f864 __aeabi_idiv
 .text            7053f9ac __aeabi_idiv0
 .text            7053f98c __aeabi_idivmod
 .text            7053f9ac __aeabi_ldiv0
 .text            70542644 __aeabi_uidiv
 .text            70542738 __aeabi_uidivmod
 .text            70541380 __dadd
 .text            70540c04 __dcmp
 .text            7054189c __ddiv
 .text            7053f864 __divsi3
 .text            70541798 __dmul
 .text            70540614 __docvt
 .text            7054137c __dsub
 .text            70541f60 __dtof
 .text            70541d78 __dtoi
 .text            70540f6c __dtoi64
 .text            70541074 __dtoi64r
 .text            70541dd4 __dtoir
 .text            70541d20 __dtou
 .text            70540e94 __dtou64
 .text            7053fda8 __exit
 .text            70540bd4 __fcmp
 .text            70541298 __fdiv
 .text            705411fc __fmul
 .text            70541c8c __fto32
 .text            70541f3c __ftod
 .text            70541c7c __ftoi
 .text            70540cd4 __ftoi64
 .text            70540da0 __ftoi64r
 .text            70541c84 __ftoir
 .text            70541c6c __ftou
 .text            70540c40 __ftou64
 .text            70541c74 __ftour
 .text            70542060 __gh_div64
 .text            70529300 __gh_float_printf
 .text            70528810 __gh_hexout
 .text            70528f30 __gh_long_long_printf
 .text            70542234 __gh_lsh64
 .text            705287c0 __gh_octhex_finish
 .text            70542158 __gh_rem64
 .text            705422f4 __gh_rsh64
 .text            7052884c __gh_strout
 .text            705423c8 __gh_udiv64
 .text            70528f74 __gh_uns_long_long_printf
 .text            70542588 __gh_urem64
 .text            70528988 __gh_vsprintf
 .text            70528718 __gh_xxdecout
 .interfunc       70542758 __ghsArmBLR
 .text            70540b38 __ghs_bsearch_16
 .bss             7054327c __ghsbegin_bss
 .text            70528644 __ghsbegin_text
 .bss             708dd304 __ghsend_bss
 .text            70542758 __ghsend_text
 .text            7053faa0 __gmtime
 .text            70541ed8 __itod
 .text            70541e68 __itof
 .text            7053fa5c __localtime
 .text            7053ff38 __ltoa
 .text            705286b8 __sdiv10
 .text            7053f838 __sdiv_32_32
 .text            705286cc __smod10
 .text            7053f848 __smod_32_32
 .text            705286ec __udiv10
 .text            70542618 __udiv_32_32
 .text            70542644 __udivsi3
 .text            705286fc __umod10
 .text            70542628 __umod_32_32
 .text            70541ed0 __utod
 .text            70541e60 __utof
 .data            70542ff4 _ctypes_
 .text            7053fe04 _globalAlloc
 .text            7053fe1c _globalFree
 .bss             70554144 _handle
 .text            70528644 _init00
 .text            7053fa48 _itoa
 .bss             70554148 _osdata
 .bss             70554140 _tsr
 .text            7052db10 acceptConnection
 .text            7053d804 allocSessionOutBuffer
 .text            7053f724 allocateConnection
 .data            70542f78 appContextNameMms
 .data            70542e58 applErrorVarSpec
 .text            7053f9b0 atoi
 .data            70542f64 berId
 .data            70542f68 calledPresentationSelector
 .data            70542ee0 cfgFiles
 .data            70542ec4 cidFile
 .text            705381fc closeIsoConnection
 .text            7053f7e8 closeServerSocket
 .bss             705541ec cmdTermObjNameBuf
 .text            705373f8 cotpReceiveData
 .text            7053734c cotpSendData
 .text            70534a9c crc32
 .text            70537788 createAcceptSPDU
 .text            705312c0 createThread
 .bss             705542ac csHeaderBuf
 .bss             7055414c ctrlObjects
 .bss             705582c0 currentDataSlice
 .text            7053d2e0 dataSliceCapture
 .bss             705542c0 dataSliceCopy
 .text            7053d3bc dataSliceGetBoolValue
 .text            7053d354 dataSliceGetFloatValue
 .text            7053d430 dataSliceGetIntValue
 .text            7053d388 dataSliceGetRealValue
 .text            7053d344 dataSliceGetTimeStamp
 .bss             705542b4 dataSliceIf
 .text            7053d278 dataSliceInit
 .text            7053d5d4 dataSliceRelease
 .bss             705542b8 dataSliceSize
 .data            70542dd4 dataSliceUpdateList
 .data            70542dd8 dataSliceUpdateListTail
 .text            70539288 debugSendDump
 .text            70539284 debugSendStrL
 .text            7053928c debugSendText
 .text            70539280 debugSendUshort
 .text            7053927c debugStart
 .data            70542e28 defaultBehavior
 .data            70542e38 defaultFinalDABehavior
 .data            70542f90 defaultIed
 .text            7053ef54 disableDisconnectedReports
 .text            70529044 efout..trg.2Farm4.2Fobjs.2Flibs.2Flibansi.2Fccefgout.
 .text            7053a6b4 encodeAccessAttrBitString
 .text            7053a6d4 encodeAccessAttrBitStringConst
 .text            7053a890 encodeAccessAttrBoolean
 .text            7053a8b0 encodeAccessAttrCodedEnum
 .text            7053a918 encodeAccessAttrConst
 .text            7053a8f8 encodeAccessAttrEntryTime
 .text            7053a744 encodeAccessAttrFloat
 .text            7052fbe4 encodeAccessAttrGoCB
 .text            7053a7d8 encodeAccessAttrInt
 .text            7053a848 encodeAccessAttrInt128
 .text            7053a694 encodeAccessAttrQuality
 .text            70538e18 encodeAccessAttrRCB
 .text            7053a79c encodeAccessAttrString
 .text            7053a71c encodeAccessAttrTimeStamp
 .text            7053a810 encodeAccessAttrUInt
 .text            7053a45c encodeBoolValue
 .text            7053c0bc encodeChildrenAccessAttrs
 .text            70538908 encodeDataSetAttrs
 .text            705386f8 encodeDataSetRefAttr
 .text            7053bf00 encodeObjectAccessAttrs
 .text            7053a538 encodeOctetString8Value
 .text            7052fc2c encodeReadAttrGoCB
 .text            70538ed0 encodeReadAttrRCB
 .text            7053a664 encodeReadBoolean
 .text            7053a4ec encodeReadCodedEnum
 .text            7053b940 encodeReadConst
 .text            7053a050 encodeReadFloat
 .text            7053a0d8 encodeReadFloatSett
 .text            7053a56c encodeReadInt32
 .text            7053a5e4 encodeReadInt32Sett
 .text            7053a5b4 encodeReadInt32U
 .text            7053a62c encodeReadInt32USett
 .text            7053a094 encodeReadReal
 .text            7053a12c encodeReadRealSett
 .text            7053a1f8 encodeReadTimeStamp
 .text            7053bd0c encodeSimpleDataAccessAttrs
 .text            7053bb14 encodeTypeAccessAttrs
 .text            7053a420 encodeUInt32Value
 .text            705405dc ethBusGetInterface
 .text            7054005c etharp_output
 .text            7054004c ethernet_input
 .text            7052d474 fast_memcpy
 .text            7053ef3c finalizeReportRegistration
 .text            7053b680 findDomainSection
 .text            7053b7d0 findObjectByFullName
 .text            7053b764 findObjectByPath
 .text            7053b53c findObjectBySimpleName
 .text            7053b604 findObjectByTag
 .text            7053f834 free
 .text            7053f750 freeConnection
 .text            70534bdc freeMemory
 .text            7053d9d0 freeSessionOutBuffer
 .text            7052dfac frsm_alloc
 .text            7052dfd0 frsm_free
 .text            7052dffc frsm_getById
 .text            7052df98 frsm_init
 .text            705311f8 fs_fileClose
 .text            705310b4 fs_fileOpen
 .text            70531260 fs_fileRead
 .text            7053109c fs_findClose
 .text            70530eb8 fs_findFirst
 .text            70530fbc fs_findNext
 .text            70530e48 fs_init
 .data            70542f60 g_DataSpdu
 .bss             70554274 g_FRSM
 .data            70542fec g_reportCount
 .bss             705582c4 g_reports
 .text            7053baac getAlignedDescrStruct
 .text            7053b33c getBerStringLength
 .text            7053c288 getConstDAString
 .text            7053c300 getConstDAULong
 .text            7053d31c getCurrentDataSliceTime
 .text            7053c208 getDAValuePos
 .text            705389d4 getDataSetAccessAttrs
 .text            7053b804 getDataSetByPath
 .text            7053a304 getEnumDataValue
 .text            7053a238 getEnumValue
 .text            7053cf70 getFloatSett
 .text            7053cf14 getFloatValue
 .text            7053ef08 getFreeReport
 .text            7053b384 getIEDObjectNameString
 .text            7053cfd0 getIntSett
 .text            7053b408 getObjectName
 .text            7053de60 getRCB
 .text            7053cfa0 getRealSett
 .text            7053de9c getReporterByIndex
 .text            7053b6a4 getSimpleNameLen
 .text            7053b4a4 getSubObjectsPos
 .text            7053c610 handleConfirmedRequestPdu
 .text            7053f754 handleIncomingConnections
 .text            705383b0 handleMMSConnection
 .data            70542ea8 icdFile
 .data            70542fcc iedModel
 .data            70542fd0 iedModelSize
 .text            7052abf0 incSettCounter
 .text            705376dc initCOTPConnection
 .text            7052c524 initFinalDARptItem
 .text            7053cd44 initPWin
 .text            705379b8 initPresentation
 .text            7053def8 initReportCompareDataset
 .text            7053eedc initReports
 .text            7053d7a0 initSessionOutBuffers
 .text            70540044 ipaddr_addr
 .text            7053fc9c isInterruptEnabled
 .text            7053ded0 isRCBConnected
 .text            70540194 is_init
 .text            70541fb4 isinf
 .text            70542000 isnan
 .text            705379cc isoPresentation_createCpaMessage
 .text            70537af8 isoPresentation_parseUserData
 .text            70537810 isoSession_createDataSpdu
 .data            70542e70 lastApplErrorName
 .bss             70554270 listenSocket
 .text            7053cec0 loadIedModel
 .text            7053ce28 loadRomModule
 .text            7053fcb4 lockInterrupt
 .text            70540084 lwip_accept
 .text            70540074 lwip_bind
 .text            7054008c lwip_close
 .text            705400bc lwip_connect
 .text            705400ec lwip_fcntl
 .text            705400d4 lwip_getpeername
 .text            705400cc lwip_getsockname
 .text            7054003c lwip_htonl
 .text            70540034 lwip_htons
 .text            705400dc lwip_inet_ntop
 .text            705400e4 lwip_inet_pton
 .text            705400b4 lwip_ioctl
 .text            7054007c lwip_listen
 .text            70540094 lwip_recv
 .text            705400ac lwip_recvfrom
 .text            705400c4 lwip_select
 .text            7054009c lwip_send
 .text            705400a4 lwip_sendto
 .text            7054006c lwip_setsockopt
 .text            70540064 lwip_socket
 .text            7054019c lwiplib_init
 .text            7054018c lwipport_init
 .text            7053f82c main
 .text            7053f830 malloc
 .text            7053fec4 memcmp
 .text            7053fd50 memcpy
 .text            7053fd50 memmove
 .text            7053fd38 memset
 .text            70537fe8 mmsProcessMessage
 .text            7053c4fc mmsServer_handleIdentifyRequest
 .text            705382c4 mmsThread
 .text            7053c994 mms_createMmsRejectPdu
 .text            70538a74 mms_createNameListResponse
 .text            7053b230 mms_handleFileCloseRequest
 .text            7053ac70 mms_handleFileDirRequest
 .text            7053afd8 mms_handleFileOpenRequest
 .text            7053b154 mms_handleFileReadRequest
 .text            70538a08 mms_handleGetDataSetAccessAttr
 .text            70538bbc mms_handleGetNameListRequest
 .text            705384fc mms_handleGetVariableAccessAttr
 .text            705392a0 mms_handleReadRequest
 .text            70539990 mms_handleWriteRequest
 .text            7053ffd4 netif_add
 .text            7053fff4 netif_remove
 .text            7053ffec netif_set_default
 .text            7053ffdc netif_set_link_up
 .text            7053ffe4 netif_set_up
 .text            7053fffc netifapi_netif_common
 .text            7054016c nwAddNetif
 .text            7054012c nwEthSend
 .text            70540114 nwGetDefaultNetif
 .text            70540124 nwGetEthRecvHook
 .text            70540184 nwGetFeature
 .text            7054014c nwGetMac
 .text            7054010c nwGetNetif
 .text            70540144 nwMtiAddFilter
 .text            7054013c nwMtiClear
 .text            70540134 nwMtiEnable
 .text            70540154 nwRegisterReload
 .text            70540164 nwReload
 .text            70540174 nwRemoveNetif
 .text            7054015c nwRemoveReload
 .text            7054017c nwSetDefaultNetif
 .text            7054011c nwSetEthRecvHook
 .data            70542e98 oldHWTmrIsrHandlerPtr
 .bss             70554298 oscHeaderBuf
 .data            70542ef4 oscInfoV3
 .data            70542f28 oscInfoV4
 .bss             70554290 osc_tlsf
 .text            70537700 parseSessionMessage
 .text            7054000c pbuf_alloc
 .text            7054002c pbuf_alloc_reference
 .text            70540004 pbuf_copy_partial
 .text            70540014 pbuf_free
 .text            70540024 pbuf_header
 .text            7054001c pbuf_take
 .text            7052e038 prepareGOOSEbuf
 .text            70537e94 processSessionConnect
 .text            705380e8 processSessionData
 .text            7053c194 processSubobjects
 .text            7053d1e4 pwaOscFindClose
 .text            7053d19c pwaOscFindFirst
 .text            7053d1c0 pwaOscFindNext
 .text            7053d200 pwaOscOscRead
 .text            7053d008 pwaWriteFloatSett
 .text            7053d124 pwaWriteIntSett
 .text            7053d080 pwaWriteRealSett
 .text            70539e0c qualityFromBitsFast
 .text            7053a65c readBoolValue
 .text            7053a484 readCodedEnum
 .text            70539ff4 readFloatValue
 .text            7053a3d0 readIntSettValue
 .text            7053a3f8 readIntValue
 .text            7053a014 readRealValue
 .text            7052dbd8 readSocket
 .text            7053b2c4 readTL
 .text            7053de18 registerAllLogicalDeviceRCB
 .text            7053de10 registerAllLogicalNodeRCB
 .text            7053de38 registerAllRCB
 .text            7053dd88 registerBufferedReport
 .text            7053dab4 registerConfRev
 .text            7052ea64 registerGoCBsGivenFC
 .text            7053db14 registerOptFlds
 .text            7053dd98 registerRCBsGivenFC
 .text            7053db68 registerReport
 .text            7053d9dc registerRptID
 .text            7053da64 registerTrgOps
 .text            7053dd90 registerUnbufferedReport
 .data            70542fe4 reportVarNameSequence
 .text            7053d704 sendThread
 .text            7052decc serverMain
 .text            7053b2b0 setIedModel
 .data            70542e24 settCounter
 .bss             705542b0 settsDataSlice
 .bss             705542bc settsDataSliceSize
 .text            7053b300 skipObject
 .text            70540a90 snprintf
 .text            7052daf8 socketInit
 .text            70540aac sputc_limit...2Fexport.2Frelmgr.2Farmmipsv800_release.2Fcvs.2Ftrg.2Farm4.2Fobjs.2Fccsnprtf.
 .text            7052dc38 startListening
 .text            7053fa24 strlen
 .text            7053fc60 strncpy
 .text            705400f4 sys_get_def_netif
 .text            705400fc sys_tcpip_lock
 .text            70540104 sys_tcpip_unlock
 .text            70540054 tcpip_input
 .bss             70554294 tlsfCS
 .text            70534548 tlsf_add_pool
 .text            70534a38 tlsf_align_size
 .text            70534a50 tlsf_alloc_overhead
 .text            705344ec tlsf_block_size
 .text            70534a48 tlsf_block_size_max
 .text            70534a40 tlsf_block_size_min
 .text            70534230 tlsf_check
 .text            70534510 tlsf_check_pool
 .text            70534640 tlsf_create
 .text            705346f0 tlsf_create_with_pool
 .text            70534720 tlsf_destroy
 .text            7053486c tlsf_free
 .text            70534724 tlsf_get_pool
 .text            70534734 tlsf_malloc
 .text            7053476c tlsf_memalign
 .text            70534540 tlsf_pool_overhead
 .text            705348dc tlsf_realloc
 .text            705345f0 tlsf_remove_pool
 .text            70534a2c tlsf_size
 .text            70534474 tlsf_walk_pool
 .text            7053fce4 unlockInterrupt
 .text            70528768 unsout..trg.2Farm4.2Fobjs.2Flibs.2Flibansi.2Fccvsprnt.
 .text            70540ad4 vsnprintf
 .text            7052fcac writeAttrGoCB
 .text            705391ac writeAttrRCB
 .text            705397fc writeBoolean
 .text            7053b844 writeChildrenNames
 .text            70539838 writeCodedEnum
 .text            705396a4 writeFloatSett
 .text            705390d8 writeGI
 .text            70539770 writeIntSett
 .text            7053913c writeIntgPd
 .text            7053970c writeRealSett
 .text            7052dc20 writeSocket
 .text            7053732c writeTPKTHeader
 .text            7053cf4c writeTele
 .text            70541ee0 x32tod...2E.2Fobjs.2Find32tod.
 .text            70541e70 x32tof...2E.2Fobjs.2Find32tof.
 .text            70528e28 xdecout..trg.2Farm4.2Fobjs.2Flibs.2Flibansi.2Fccllout.
 .text            70534ed4 zs_entrybegin
 .text            705351c8 zs_entrydata
 .text            7053536c zs_entryend
 .text            7053546c zs_finish
 .text            70534da8 zs_free
 .text            70534ce0 zs_init
 .text            70534c6c zs_registermethod
 .text            705335a8 zs_user_writeToStream
 .text            70534e04 zs_writeentry
