#include "pwin_access.h"

#include "../../../../AT91CORE/modules/pw/pwinlib/config.h"
#include "../../../../AT91CORE/modules/pw/pwinlib/pwinlib.h"
#include "../../../../AT91CORE/modules/pw/pwinlib/lasterr.h"

#include <debug.h>
#include <types.h>
#include <process.h>

#define PWIN_DEFAULT_TIMEOUT 1000

static _HANDLE pwlHandle;
static _PWL_GetAnalogValue pwlGetAnalogValue;
static _LoadRomFromAddr pwlLoadRomFromAddr;
static _PWL_initLibIf pwlInitLibIf;

static PWinLibIf pwinLibIf;

bool initPWin(void)
{
    _PWL_Open pwlOpen;
    _HANDLE hModule = _GetModuleHandle("PWINLIB");
    if(! hModule)
    {
        return FALSE;
    }

    pwlOpen = (_PWL_Open)_GetProcAddress(hModule,(char*)PWL_OPEN);
    if(! pwlOpen)
    {
        return FALSE;
    }

    pwlGetAnalogValue = (_PWL_GetAnalogValue)
            _GetProcAddress(hModule,(char*)PWL_GET_ANALOG_VALUE);
    if(! pwlGetAnalogValue)
    {
        return FALSE;
    }

    pwlLoadRomFromAddr = (_LoadRomFromAddr)
            _GetProcAddress(hModule,(char*)PWL_LOAD_ROMM_FROM_ADDR);
    if(! pwlLoadRomFromAddr)
    {
        return FALSE;
    }

    pwlInitLibIf = (_PWL_initLibIf)
            _GetProcAddress(hModule,(char*)PWL_INIT_IF);
    if(! pwlInitLibIf)
    {
        return FALSE;
    }
    pwlInitLibIf(&pwinLibIf,sizeof(PWinLibIf));


    do
    {
        Idle();
        pwlHandle = pwlOpen();
    }
    while(!pwlHandle);

	pwinLibIf.configure(pwlHandle,PWL_CONFIGURE_WAIT_READY_BEFORE_OPERATION,0);
	pwinLibIf.configure(pwlHandle,PWL_CONFIGURE_TIMEOUT,PWIN_DEFAULT_TIMEOUT);

    return TRUE;
}

bool loadRomModule(uint32_t signature, void** pRomModule, uint8_t** pRomModuleData,
    size_t* pRomModuleDataSize, __time32_t *time)
{
    RMODULE* module;
	
	while (true)
	{
		module = pwlLoadRomFromAddr(pwlHandle, 0, signature, TRUE);
		// нашли модель
		if (module)
		{
			break;
		}
		
		// ошибок нет, не найден роммодуль
		if (pwinLibIf.getLastError(pwlHandle) == PWL_ERROR_ROMMODULE_NOT_FOUND)
			return FALSE;
		
		// была ошибка по pwin - повторяем поиск
	}
	
	if (pRomModule)
	{		
		*pRomModule = module;
	}
	
	if (pRomModuleData)
	{
	   *pRomModuleData = (uint8_t*)(module + 1);
	}
	// размер
	if (pRomModuleDataSize)
	{
        *pRomModuleDataSize = module->MSize - sizeof(RMODULE);
	}
	// время
	if (time)
	{
		*time = module->DateTime;
	}
    return TRUE;
}

uint8_t* loadIedModel(int* pSize )
{
	
	uint8_t *modelPtr = NULL;
	
	// Если не удалось загрузить загрузить модель, изготовленную из CID-файла, 
	// загружается модель по-умолчанию
	loadRomModule('IED1',NULL,&modelPtr,(size_t*)pSize,NULL) || 
		loadRomModule('IED0',NULL,&modelPtr,(size_t*)pSize,NULL);
	
    return modelPtr;
}

int getFloatValue(short offset)
{
    long value;
    bool result = pwlGetAnalogValue(pwlHandle, offset, &value);
    if( !result  )
    {
        return 0x7FFFFFFF;
    }
    else
    {
        return value;
    }
}

void writeTele(uint32_t offset)
{
    pwinLibIf.sendTele(pwlHandle, offset);
}

//***************************************************************
// Этим функциям здесь не место, их нужно перенести в dataslice.c
// Оставлены только для проверки.
#include "../../../../DataSlice/DataSliceClient/datasliceif.h"

int getFloatSett(unsigned short offset)
{
	extern DataSliceSetts* settsDataSlice;
	int32_t result;
	if (!settsDataSlice->getWordValue(offset, &result))
	{
		return 0;
	}
	return result;
}

float getRealSett(unsigned short offset)
{
	
	extern DataSliceSetts* settsDataSlice;
	float result;
	if (!settsDataSlice->getWordValue(offset, (int32_t*)(void*)&result))
	{
		return 0.f;
	}
	return result;
}


int getIntSett(int offset)
{
	extern DataSliceSetts* settsDataSlice;
	int32_t result;
	if (!settsDataSlice->getWordValue(offset, &result))
	{
		return 0;
	}
	return result;
}

//****************************************************************

bool pwaWriteFloatSett(unsigned short offset, int value)
{
    return pwinLibIf.prepareSetts(pwlHandle, WRITE_TYPE_SETT) 
        && pwinLibIf.writeAnalogSett(pwlHandle, 0, offset, value) 
        && pwinLibIf.finalizeSetts(pwlHandle);    
}

bool pwaWriteRealSett(unsigned short offset, float value)
{
    int* pValue = (void*)&value;
    int intValue = *pValue;

    return pwinLibIf.prepareSetts(pwlHandle, WRITE_TYPE_SETT) 
        && pwinLibIf.writeAnalogSett(pwlHandle, 0, offset, intValue) 
        && pwinLibIf.finalizeSetts(pwlHandle);    
}


bool pwaWriteIntSett(unsigned short offset, int value)
{        
    return pwinLibIf.prepareSetts(pwlHandle, WRITE_TYPE_SETT) 
        && pwinLibIf.writeIntSett(pwlHandle, 0, offset, value) 
        && pwinLibIf.finalizeSetts(pwlHandle);   
}

int pwaOscFindFirst(unsigned int oscNum, PWFileInfo *fileInfo)
{
   return pwinLibIf.oscFindFirst(pwlHandle,oscNum,fileInfo);
}

int pwaOscFindNext(int findResult, PWFileInfo *fileInfo)
{
    return pwinLibIf.oscFindNext(pwlHandle,findResult,fileInfo);
}
void pwaOscFindClose(void)
{
	pwinLibIf.oscFindClose(pwlHandle);
}

int pwaOscOscRead(PWFileInfo *fileInfo, unsigned int offset, unsigned char *data, int maxDataSize)
{
	return pwinLibIf.oscRead(pwlHandle,fileInfo,offset,data,maxDataSize);
}

