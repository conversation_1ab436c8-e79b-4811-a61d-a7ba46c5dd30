                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fvg1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=rcb.c -o gh_fvg1.o -list=rcb.lst C:\Users\<USER>\AppData\Local\Temp\gh_fvg1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_fvg1.s
Source File: rcb.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile rcb.c -o rcb.o

                      10 ;Source File:   rcb.c

                      11 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      12 ;Compile Date:  Tue Oct 07 09:39:39 2025

                      13 ;Host OS:       Win32

                      14 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      15 ;Release:       MULTI v4.2.3

                      16 ;Revision Date: Wed Mar 29 05:25:47 2006

                      17 ;Release Date:  Fri Mar 31 10:02:14 2006

                      18 

                      19 ;1: #include "rcb.h"


                      20 ;2: #include "AsnEncoding.h"


                      21 ;3: #include "IEDCompile/InnerAttributeTypes.h"


                      22 ;4: #include "iedmodel.h"


                      23 ;5: #include "iedTree/iedTree.h"


                      24 ;6: #include "reportItems/RptDataSet.h"


                      25 ;7: #include "reporter.h"


                      26 ;8: #include "reports.h"


                      27 ;9: #include <debug.h>


                      28 ;10: #include <string.h>


                      29 ;11: 


                      30 ;12: //Получение имени DataSet из атрибута RCB DatSet.


                      31 ;13: static bool readDatSetAttr(int datSetDAPos, StringView* dataSetFullName)


                      32 

                      33 ;55: }


                      34 

                      35 ;56: 


                      36 ;57: 


                      37 ;58: static bool registerRCBDataset(int rcbPos, PReporter report)


                      38 

                      39 ;109: }


                      40 

                      41 ;110: 


                      42 ;111: bool registerRptID(int rcbPos, RCB* report)


                      43 	.text

                      44 	.align	4

                      45 registerRptID::

00000000 e92d4010     46 	stmfd	[sp]!,{r4,lr}

                      47 ;112: {


                      48 

                      49 ;113:     int rptIDLen;


                      50 ;114:     int rptIDvalPos;



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fvg1.s
                      51 ;115:     int pos;


                      52 ;116:     uint8_t tag;


                      53 ;117:     int rptIDDAPos = findObjectBySimpleName(rcbPos, "RptID", 5);


                      54 

00000004 e24dd008     55 	sub	sp,sp,8

00000008 e1a04001     56 	mov	r4,r1

0000000c e28f1000*    57 	adr	r1,.L220

00000010 e3a02005     58 	mov	r2,5

00000014 eb000000*    59 	bl	findObjectBySimpleName

                      60 ;118:     RET_IF_NOT(rptIDDAPos, "RptIDPos attr is not found in RCB at pos %04X", rcbPos);


                      61 

00000018 e3500000     62 	cmp	r0,0

0000001c 0a00000a     63 	beq	.L122

                      64 ;119:     pos = readTL(rptIDDAPos, &tag, NULL, NULL);


                      65 

00000020 e28d1003     66 	add	r1,sp,3

00000024 e3a03000     67 	mov	r3,0

00000028 e1a02003     68 	mov	r2,r3

0000002c eb000000*    69 	bl	readTL

                      70 ;120:     RET_IF_NOT(pos, "Error reading RptDI at %d", rptIDDAPos);


                      71 

00000030 e3500000     72 	cmp	r0,0

00000034 0a000004     73 	beq	.L122

                      74 ;121:     //Skip name


                      75 ;122:     pos = skipObject(pos);


                      76 

00000038 eb000000*    77 	bl	skipObject

                      78 ;123:     RET_IF_NOT(pos, "Error reading RptDI at %d", rptIDDAPos);


                      79 

0000003c e3500000     80 	cmp	r0,0

00000040 0a000001     81 	beq	.L122

                      82 ;124:     //Skip Inner Attribute Type


                      83 ;125:     pos = skipObject(pos);


                      84 

00000044 eb000000*    85 	bl	skipObject

                      86 ;126:     RET_IF_NOT(pos, "Error reading RptDI at %d", rptIDDAPos);


                      87 

00000048 e3500000     88 	cmp	r0,0

                      89 .L122:

                      90 ;127:     //pos указывает на константу RptID


                      91 ;128:     rptIDvalPos = readTL(pos, &tag, &rptIDLen, NULL);


                      92 

                      93 ;129:     RET_IF_NOT(pos, "Error reading value RptDI at %d", pos);


                      94 

0000004c 03a00000     95 	moveq	r0,0

00000050 0a00000a     96 	beq	.L107

                      97 .L121:

00000054 e28d2004     98 	add	r2,sp,4

00000058 e28d1003     99 	add	r1,sp,3

0000005c e3a03000    100 	mov	r3,0

00000060 eb000000*   101 	bl	readTL

                     102 ;130: 


                     103 ;131: 


                     104 ;132:     StringView_init(&report->rptID,


                     105 

00000064 e59f331c*   106 	ldr	r3,.L221

00000068 e5931000    107 	ldr	r1,[r3]

0000006c e59d2004    108 	ldr	r2,[sp,4]

00000070 e0801001    109 	add	r1,r0,r1

00000074 e2840004    110 	add	r0,r4,4

00000078 eb000000*   111 	bl	StringView_init


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fvg1.s
                     112 ;133:         (const char*)iedModel + rptIDvalPos, rptIDLen);    


                     113 ;134:     return TRUE;


                     114 

0000007c e3a00001    115 	mov	r0,1

                     116 .L107:

00000080 e28dd008    117 	add	sp,sp,8

00000084 e8bd8010    118 	ldmfd	[sp]!,{r4,pc}

                     119 	.endf	registerRptID

                     120 	.align	4

                     121 ;rptIDLen	[sp,4]	local

                     122 ;pos	r2	local

                     123 ;tag	[sp,3]	local

                     124 ;rptIDDAPos	r2	local

                     125 ;.L201	.L205	static

                     126 

                     127 ;rcbPos	none	param

                     128 ;report	r4	param

                     129 

                     130 	.section ".bss","awb"

                     131 .L200:

                     132 	.section ".rodata","a"

                     133 .L202:

                     134 __UNNAMED_1_static_in_registerRCBDataset:;	"DatSet\000"

00000000 53746144    135 	.data.b	68,97,116,83

00000004 7465       136 	.data.b	101,116

00000006 00         137 	.data.b	0

00000007 00         138 	.space	1

                     139 	.type	__UNNAMED_1_static_in_registerRCBDataset,$object

                     140 	.size	__UNNAMED_1_static_in_registerRCBDataset,8

                     141 	.data

                     142 	.text

                     143 

                     144 ;135: }


                     145 

                     146 ;136: 


                     147 ;137: bool registerTrgOps(int rcbPos, RCB* rcb)


                     148 	.align	4

                     149 	.align	4

                     150 registerTrgOps::

00000088 e92d4030    151 	stmfd	[sp]!,{r4-r5,lr}

                     152 ;138: {


                     153 

                     154 ;139:     int trgOps;


                     155 ;140:     enum InnerAttributeType attrType;


                     156 ;141:     //По 7.2 этот атрибут должен называться TrgOp, но по факту везде TrgOps


                     157 ;142:     int trgOpsPos = getDAValuePos(rcbPos, "TrgOps", 6, &attrType);


                     158 

0000008c e24dd004    159 	sub	sp,sp,4

00000090 e1a0300d    160 	mov	r3,sp

00000094 e1a05001    161 	mov	r5,r1

00000098 e28f1000*   162 	adr	r1,.L303

0000009c e3a02006    163 	mov	r2,6

000000a0 eb000000*   164 	bl	getDAValuePos

000000a4 e1b04000    165 	movs	r4,r0

                     166 ;143:     RET_IF_NOT(trgOpsPos, "Error reading TrgOps");


                     167 

000000a8 0a000007    168 	beq	.L228

                     169 ;144:     trgOps = BerDecoder_DecodeBitStringTLToInt(iedModel, trgOpsPos);


                     170 

000000ac e59f22d4*   171 	ldr	r2,.L221

000000b0 e5920000    172 	ldr	r0,[r2]


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fvg1.s
000000b4 e1a01004    173 	mov	r1,r4

000000b8 eb000000*   174 	bl	BerDecoder_DecodeBitStringTLToInt

                     175 ;145:     RET_IF_NOT(trgOpsPos >= 0, "Error decoding TrgOps");


                     176 

000000bc e3540000    177 	cmp	r4,0

                     178 ;146:     rcb->trgOps = trgOps;


                     179 

000000c0 a5c5001a    180 	strgeb	r0,[r5,26]

                     181 ;147:     return TRUE;


                     182 

000000c4 a3a00001    183 	movge	r0,1

000000c8 aa000000    184 	bge	.L222

                     185 .L228:

000000cc e3a00000    186 	mov	r0,0

                     187 .L222:

000000d0 e28dd004    188 	add	sp,sp,4

000000d4 e8bd8030    189 	ldmfd	[sp]!,{r4-r5,pc}

                     190 	.endf	registerTrgOps

                     191 	.align	4

                     192 ;trgOps	r0	local

                     193 ;attrType	[sp]	local

                     194 ;trgOpsPos	r4	local

                     195 ;.L285	.L288	static

                     196 

                     197 ;rcbPos	none	param

                     198 ;rcb	r5	param

                     199 

                     200 	.section ".bss","awb"

                     201 .L284:

                     202 	.data

                     203 	.text

                     204 

                     205 ;148: }


                     206 

                     207 ;149: 


                     208 ;150: bool registerConfRev(int rcbPos, RCB* pRCB)


                     209 	.align	4

                     210 	.align	4

                     211 registerConfRev::

000000d8 e92d4010    212 	stmfd	[sp]!,{r4,lr}

                     213 ;151: {


                     214 

                     215 ;152:     uint8_t tag;


                     216 ;153:     int len;


                     217 ;154:     int confRev;


                     218 ;155:     enum InnerAttributeType attrType;


                     219 ;156:     int confRevPos = getDAValuePos(rcbPos, "ConfRev", 7, &attrType);


                     220 

000000dc e24dd00c    221 	sub	sp,sp,12

000000e0 e28d3008    222 	add	r3,sp,8

000000e4 e1a04001    223 	mov	r4,r1

000000e8 e28f1000*   224 	adr	r1,.L379

000000ec e3a02007    225 	mov	r2,7

000000f0 eb000000*   226 	bl	getDAValuePos

                     227 ;157:     RET_IF_NOT(confRevPos, "Error reading ConfRev");


                     228 

000000f4 e3500000    229 	cmp	r0,0

000000f8 0a000004    230 	beq	.L310

                     231 ;158:     confRevPos = readTL(confRevPos, &tag, &len, NULL);


                     232 

000000fc e28d2004    233 	add	r2,sp,4


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fvg1.s
00000100 e28d1003    234 	add	r1,sp,3

00000104 e3a03000    235 	mov	r3,0

00000108 eb000000*   236 	bl	readTL

0000010c e1b02000    237 	movs	r2,r0

                     238 ;159:     RET_IF_NOT(confRevPos, "Error reading ConfRev");


                     239 

                     240 .L310:

00000110 03a00000    241 	moveq	r0,0

00000114 0a000005    242 	beq	.L304

                     243 .L309:

                     244 ;160:     confRev = BerDecoder_decodeUint32(iedModel, len, confRevPos);


                     245 

00000118 e59f3268*   246 	ldr	r3,.L221

0000011c e59d1004    247 	ldr	r1,[sp,4]

00000120 e5930000    248 	ldr	r0,[r3]

00000124 eb000000*   249 	bl	BerDecoder_decodeUint32

                     250 ;161:     pRCB->confRev = confRev;


                     251 

00000128 e5840014    252 	str	r0,[r4,20]

                     253 ;162:     return TRUE;


                     254 

0000012c e3a00001    255 	mov	r0,1

                     256 .L304:

00000130 e28dd00c    257 	add	sp,sp,12

00000134 e8bd8010    258 	ldmfd	[sp]!,{r4,pc}

                     259 	.endf	registerConfRev

                     260 	.align	4

                     261 ;tag	[sp,3]	local

                     262 ;len	[sp,4]	local

                     263 ;attrType	[sp,8]	local

                     264 ;confRevPos	r2	local

                     265 ;.L365	.L368	static

                     266 

                     267 ;rcbPos	none	param

                     268 ;pRCB	r4	param

                     269 

                     270 	.section ".bss","awb"

                     271 .L364:

                     272 	.data

                     273 	.text

                     274 

                     275 ;163: }


                     276 

                     277 ;164: 


                     278 ;165: 


                     279 ;166: bool registerOptFlds(int rcbPos, RCB* report)


                     280 	.align	4

                     281 	.align	4

                     282 registerOptFlds::

00000138 e92d4030    283 	stmfd	[sp]!,{r4-r5,lr}

                     284 ;167: {


                     285 

                     286 ;168:     int optFlds;


                     287 ;169:     enum InnerAttributeType attrType;


                     288 ;170:     int optFldsPos = getDAValuePos(rcbPos, "OptFlds", 7, &attrType);


                     289 

0000013c e24dd004    290 	sub	sp,sp,4

00000140 e1a0300d    291 	mov	r3,sp

00000144 e1a05001    292 	mov	r5,r1

00000148 e28f1000*   293 	adr	r1,.L463

0000014c e3a02007    294 	mov	r2,7


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fvg1.s
00000150 eb000000*   295 	bl	getDAValuePos

00000154 e1b04000    296 	movs	r4,r0

                     297 ;171:     RET_IF_NOT(optFldsPos, "Error reading OptFlds");


                     298 

00000158 0a000008    299 	beq	.L386

                     300 ;172:     optFlds = BerDecoder_DecodeBitStringTLToInt(iedModel, optFldsPos);


                     301 

0000015c e59f2224*   302 	ldr	r2,.L221

00000160 e5920000    303 	ldr	r0,[r2]

00000164 e1a01004    304 	mov	r1,r4

00000168 eb000000*   305 	bl	BerDecoder_DecodeBitStringTLToInt

                     306 ;173:     RET_IF_NOT(optFldsPos >= 0, "Error decoding OptFlds");


                     307 

0000016c e3540000    308 	cmp	r4,0

                     309 ;174:     report->optFlds = optFlds & SUPPORTED_OPTFLDS;


                     310 

00000170 a20000ee    311 	andge	r0,r0,238

00000174 a1c502b0    312 	strgeh	r0,[r5,32]

                     313 ;175:     return TRUE;


                     314 

00000178 a3a00001    315 	movge	r0,1

0000017c aa000000    316 	bge	.L380

                     317 .L386:

00000180 e3a00000    318 	mov	r0,0

                     319 .L380:

00000184 e28dd004    320 	add	sp,sp,4

00000188 e8bd8030    321 	ldmfd	[sp]!,{r4-r5,pc}

                     322 	.endf	registerOptFlds

                     323 	.align	4

                     324 ;optFlds	r0	local

                     325 ;attrType	[sp]	local

                     326 ;optFldsPos	r4	local

                     327 ;.L445	.L448	static

                     328 

                     329 ;rcbPos	none	param

                     330 ;report	r5	param

                     331 

                     332 	.section ".bss","awb"

                     333 .L444:

                     334 	.data

                     335 	.text

                     336 

                     337 ;176: }


                     338 

                     339 ;177: 


                     340 ;178: void registerReport(int rcbPos, bool buffered)


                     341 	.align	4

                     342 	.align	4

                     343 registerReport::

0000018c e92d48f0    344 	stmfd	[sp]!,{r4-r7,fp,lr}

00000190 e24dd014    345 	sub	sp,sp,20

00000194 e1a07000    346 	mov	r7,r0

00000198 e1a0b001    347 	mov	fp,r1

                     348 ;179: {


                     349 

                     350 ;180:     PReporter pReporter = getFreeReport();


                     351 

0000019c eb000000*   352 	bl	getFreeReport

000001a0 e1b04000    353 	movs	r4,r0

                     354 ;181:     RCB* pRCB = &pReporter->rcb;


                     355 


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fvg1.s
000001a4 e1a05004    356 	mov	r5,r4

                     357 ;182:     if (pReporter == NULL)


                     358 

000001a8 0a000072    359 	beq	.L464

                     360 ;183:     {


                     361 

                     362 ;184:         ERROR_REPORT("Unable to register report: too many reports");


                     363 ;185:         return;


                     364 

                     365 ;186:     }


                     366 ;187: 


                     367 ;188:     pReporter->rcb.buffered = buffered;


                     368 

000001ac e5c4b000    369 	strb	fp,[r4]

                     370 ;189:     pReporter->rcb.rptEna = FALSE;


                     371 

000001b0 e3a01000    372 	mov	r1,0

000001b4 e5c41001    373 	strb	r1,[r4,1]

                     374 ;190:     pReporter->rcb.resv = false;


                     375 

000001b8 e5c41002    376 	strb	r1,[r4,2]

                     377 ;191:     pReporter->sessionOutBuffer.busy = FALSE;


                     378 

000001bc e3a00b42    379 	mov	r0,66<<10

000001c0 e2800060    380 	add	r0,r0,96

000001c4 e7c01004    381 	strb	r1,[r0,r4]

                     382 ;192:     pReporter->connection = NULL;


                     383 

000001c8 e584102c    384 	str	r1,[r4,44]

                     385 ;194:     pReporter->intgTimerAlam = false;


                     386 

000001cc e5c41030    387 	strb	r1,[r4,48]

                     388 ;195:     pRCB->confRev = 0;


                     389 

000001d0 e5851014    390 	str	r1,[r5,20]

                     391 ;196:     pRCB->sqNum = 1;


                     392 

000001d4 e5841040    393 	str	r1,[r4,64]

                     394 ;193:     pReporter->intgPdCounter = 0;


                     395 

000001d8 e5851024    396 	str	r1,[r5,36]

000001dc e5851028    397 	str	r1,[r5,40]

                     398 ;198:     pRCB->intgPd = 0;


                     399 

000001e0 e585101c    400 	str	r1,[r5,28]

                     401 ;199: 


                     402 ;200:     if (!registerRptID(rcbPos, pRCB))


                     403 

000001e4 e1a01005    404 	mov	r1,r5

000001e8 e3a00001    405 	mov	r0,1

000001ec e1c501b8    406 	strh	r0,[r5,24]

                     407 ;197:     pRCB->entryID = 0;


                     408 

000001f0 e1a00007    409 	mov	r0,r7

000001f4 ebffff81*   410 	bl	registerRptID

000001f8 e3500000    411 	cmp	r0,0

000001fc 0a00005d    412 	beq	.L464

                     413 ;201:     {


                     414 

                     415 ;202:         return;


                     416 


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fvg1.s
                     417 ;203:     }


                     418 ;204: 


                     419 ;205:     if (!registerRCBDataset(rcbPos, pReporter))


                     420 

                     421 ;59: {


                     422 

                     423 ;60:     IEDEntity dataSetEntity;


                     424 ;61: 


                     425 ;62:     StringView dataSetFullName;


                     426 ;63:     int dataSetPos;


                     427 ;64:     int datSetDAPos = findObjectBySimpleName(rcbPos, "DatSet", 6);


                     428 

00000200 e59f119c*   429 	ldr	r1,.L855

00000204 e1a00007    430 	mov	r0,r7

00000208 e3a02006    431 	mov	r2,6

0000020c eb000000*   432 	bl	findObjectBySimpleName

                     433 ;65:     if (!datSetDAPos)


                     434 

00000210 e3500000    435 	cmp	r0,0

00000214 0a000057    436 	beq	.L464

                     437 ;66:     {


                     438 

                     439 ;67:         ERROR_REPORT("DatSet attr is not found in RCB at pos %04X", rcbPos);


                     440 ;68:         return FALSE;


                     441 

                     442 ;69:     }


                     443 ;70: 


                     444 ;71:     if (!readDatSetAttr(datSetDAPos, &dataSetFullName))


                     445 

                     446 ;14: {


                     447 

                     448 ;15:     int innerTypeLen;


                     449 ;16:     enum InnerAttributeType innerAttrType;


                     450 ;17:     int namePos;


                     451 ;18:     uint8_t nameTag;


                     452 ;19:     int nameLen;


                     453 ;20:     //Пропускем TL


                     454 ;21:     int pos = readTL(datSetDAPos, NULL, NULL, NULL);


                     455 

00000218 e3a03000    456 	mov	r3,0

0000021c e1a02003    457 	mov	r2,r3

00000220 e1a01003    458 	mov	r1,r3

00000224 eb000000*   459 	bl	readTL

                     460 ;22:     //Пропускаем имя


                     461 ;23:     pos = skipObject(pos);


                     462 

00000228 eb000000*   463 	bl	skipObject

                     464 ;24: 


                     465 ;25:     //Inner TYPE


                     466 ;26:     if (iedModel[pos++] != ASN_INTEGER)


                     467 

0000022c e59f1154*   468 	ldr	r1,.L221

00000230 e591c000    469 	ldr	r12,[r1]

00000234 e2806001    470 	add	r6,r0,1

00000238 e7dc0000    471 	ldrb	r0,[r12,r0]

0000023c e3500002    472 	cmp	r0,2

00000240 1a00004c    473 	bne	.L464

                     474 ;27:     {


                     475 

                     476 ;28:         return FALSE;


                     477 


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fvg1.s
                     478 ;29:     }


                     479 ;30:     pos = BerDecoder_decodeLength(iedModel, &innerTypeLen, pos, iedModelSize);


                     480 

00000244 e1a02006    481 	mov	r2,r6

00000248 e59f0158*   482 	ldr	r0,.L856

0000024c e28d1004    483 	add	r1,sp,4

00000250 e5903000    484 	ldr	r3,[r0]

00000254 e1a0000c    485 	mov	r0,r12

00000258 eb000000*   486 	bl	BerDecoder_decodeLength

0000025c e2506000    487 	subs	r6,r0,0

                     488 ;31:     if (pos < 1)


                     489 

00000260 da000044    490 	ble	.L464

                     491 ;32:     {


                     492 

                     493 ;33:         return FALSE;


                     494 

                     495 ;34:     }


                     496 ;35:     innerAttrType = (enum InnerAttributeType)


                     497 

00000264 e59f311c*   498 	ldr	r3,.L221

00000268 e59d1004    499 	ldr	r1,[sp,4]

0000026c e5930000    500 	ldr	r0,[r3]

00000270 e1a02006    501 	mov	r2,r6

00000274 eb000000*   502 	bl	BerDecoder_decodeUint32

                     503 ;36:             BerDecoder_decodeUint32(iedModel, innerTypeLen, pos);


                     504 ;37:     pos += innerTypeLen;


                     505 

00000278 e59d1004    506 	ldr	r1,[sp,4]

0000027c e350001f    507 	cmp	r0,31

00000280 e0866001    508 	add	r6,r6,r1

                     509 ;38:     if (innerAttrType != INNER_TYPE_CONST)


                     510 

00000284 1a00003b    511 	bne	.L464

                     512 ;39:     {


                     513 

                     514 ;40:         return FALSE;


                     515 

                     516 ;41:     }


                     517 ;42: 


                     518 ;43:     //Читаем имя DataSet


                     519 ;44:     //Неплохо бы сделать это через готовую функцию


                     520 ;45:     namePos = readTL(pos, &nameTag, &nameLen, NULL);


                     521 

00000288 e28d2008    522 	add	r2,sp,8

0000028c e28d1003    523 	add	r1,sp,3

00000290 e1a00006    524 	mov	r0,r6

00000294 e3a03000    525 	mov	r3,0

00000298 eb000000*   526 	bl	readTL

                     527 ;46: 


                     528 ;47:     if (!namePos || nameTag !=(BER_CONTEXT_SPECIFIC | IEC61850_VISIBLE_STRING_64))


                     529 

0000029c e3500000    530 	cmp	r0,0

000002a0 0a000034    531 	beq	.L464

000002a4 e5dd1003    532 	ldrb	r1,[sp,3]

000002a8 e3510091    533 	cmp	r1,145

000002ac 1a000031    534 	bne	.L464

000002b0 e59f30d0*   535 	ldr	r3,.L221

000002b4 e5931000    536 	ldr	r1,[r3]

000002b8 e59d2008    537 	ldr	r2,[sp,8]

000002bc e0801001    538 	add	r1,r0,r1


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fvg1.s
000002c0 e28d000c    539 	add	r0,sp,12

000002c4 eb000000*   540 	bl	StringView_init

                     541 ;48:     {


                     542 

                     543 ;49:         return FALSE;


                     544 

                     545 ;50:     }


                     546 ;51: 


                     547 ;52:     StringView_init(dataSetFullName,(const char*)iedModel + namePos, nameLen);


                     548 

                     549 ;53: 


                     550 ;54:     return TRUE;


                     551 

                     552 ;72:     {


                     553 

                     554 ;73:         ERROR_REPORT("Unable to read DatSet attr at pos %04X", datSetDAPos);


                     555 ;74:         return FALSE;


                     556 

                     557 ;75:     }


                     558 ;76: 


                     559 ;77:     dataSetPos = getDataSetByPath(&dataSetFullName);


                     560 

000002c8 e28d000c    561 	add	r0,sp,12

000002cc eb000000*   562 	bl	getDataSetByPath

                     563 ;78:     if (!dataSetPos)


                     564 

000002d0 e3500000    565 	cmp	r0,0

000002d4 0a000027    566 	beq	.L464

                     567 ;79:     {


                     568 

                     569 ;80:         ERROR_REPORT("DataSet is not found");


                     570 ;81:         return FALSE;


                     571 

                     572 ;82:     }


                     573 ;83: 


                     574 ;84:     Reporter_setDataSetName(report, &dataSetFullName);


                     575 

000002d8 e28d100c    576 	add	r1,sp,12

000002dc e1a00004    577 	mov	r0,r4

000002e0 eb000000*   578 	bl	Reporter_setDataSetName

                     579 ;85: 


                     580 ;86:     //Получаем DataSet в IEDTree


                     581 ;87:     dataSetEntity = IEDTree_findDataSetBySingleName(&dataSetFullName);


                     582 

000002e4 e28d000c    583 	add	r0,sp,12

000002e8 eb000000*   584 	bl	IEDTree_findDataSetBySingleName

000002ec e1b01000    585 	movs	r1,r0

                     586 ;88:     if(dataSetEntity == NULL)


                     587 

000002f0 0a000020    588 	beq	.L464

                     589 ;89:     {


                     590 

                     591 ;90:         return false;


                     592 

                     593 ;91:     }


                     594 ;92:     report->dataSetEntity = dataSetEntity;


                     595 

000002f4 e5841034    596 	str	r1,[r4,52]

                     597 ;93:     report->dataSet = DataSet_getDataSetObj(dataSetEntity);


                     598 

000002f8 eb000000*   599 	bl	DataSet_getDataSetObj


                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fvg1.s
000002fc e5840038    600 	str	r0,[r4,56]

                     601 ;94: 


                     602 ;95:     if(report->dataSet == NULL)


                     603 

00000300 e3500000    604 	cmp	r0,0

00000304 0a00001b    605 	beq	.L464

                     606 ;96:     {


                     607 

                     608 ;97:         ERROR_REPORT("Invalid DataSet");


                     609 ;98:         return false;


                     610 

                     611 ;99:     }


                     612 ;100: 


                     613 ;101:     report->rptDataSet = RptDataSet_create(report);


                     614 

00000308 e1a00004    615 	mov	r0,r4

0000030c eb000000*   616 	bl	RptDataSet_create

00000310 e584003c    617 	str	r0,[r4,60]

                     618 ;102:     if(report->rptDataSet == NULL)


                     619 

00000314 e3500000    620 	cmp	r0,0

00000318 0a000016    621 	beq	.L464

                     622 ;103:     {


                     623 

                     624 ;104:         ERROR_REPORT("Unable to create RptDataSet");


                     625 ;105:         return false;


                     626 

                     627 ;106:     }


                     628 ;107: 


                     629 ;108:     return true;


                     630 

                     631 ;206:     {


                     632 

                     633 ;207:         return;


                     634 

                     635 ;208:     }


                     636 ;209: 


                     637 ;210:     if (!registerConfRev(rcbPos, pRCB))


                     638 

0000031c e1a01005    639 	mov	r1,r5

00000320 e1a00007    640 	mov	r0,r7

00000324 ebffff6b*   641 	bl	registerConfRev

00000328 e3500000    642 	cmp	r0,0

0000032c 0a000011    643 	beq	.L464

                     644 ;211:     {


                     645 

                     646 ;212:         return;


                     647 

                     648 ;213:     }


                     649 ;214: 


                     650 ;215:     if (!registerTrgOps(rcbPos, pRCB))


                     651 

00000330 e1a01005    652 	mov	r1,r5

00000334 e1a00007    653 	mov	r0,r7

00000338 ebffff52*   654 	bl	registerTrgOps

0000033c e3500000    655 	cmp	r0,0

00000340 0a00000c    656 	beq	.L464

                     657 ;216:     {


                     658 

                     659 ;217:         return;


                     660 


                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fvg1.s
                     661 ;218:     }


                     662 ;219: 


                     663 ;220:     if (!registerOptFlds(rcbPos, pRCB))


                     664 

00000344 e1a01005    665 	mov	r1,r5

00000348 e1a00007    666 	mov	r0,r7

0000034c ebffff79*   667 	bl	registerOptFlds

00000350 e3500000    668 	cmp	r0,0

00000354 0a000007    669 	beq	.L464

                     670 ;221:     {


                     671 

                     672 ;222:         return;


                     673 

                     674 ;223:     }


                     675 ;224: 


                     676 ;225:     if(!initReportCompareDataset(pReporter))


                     677 

00000358 e1a00004    678 	mov	r0,r4

0000035c eb000000*   679 	bl	initReportCompareDataset

00000360 e3500000    680 	cmp	r0,0

00000364 0a000003    681 	beq	.L464

                     682 ;226:     {


                     683 

                     684 ;227:         return;


                     685 

                     686 ;228:     }


                     687 ;229: 	    


                     688 ;230:     finalizeReportRegistration();


                     689 

00000368 eb000000*   690 	bl	finalizeReportRegistration

                     691 ;231:     if (buffered)


                     692 

0000036c e35b0000    693 	cmp	fp,0

                     694 ;232:     {


                     695 

                     696 ;233:         ReportQueue_init(&(pReporter->buffer));


                     697 

00000370 12840044    698 	addne	r0,r4,68

00000374 1b000000*   699 	blne	ReportQueue_init

                     700 .L464:

00000378 e28dd014    701 	add	sp,sp,20

0000037c e8bd88f0    702 	ldmfd	[sp]!,{r4-r7,fp,pc}

                     703 	.endf	registerReport

                     704 	.align	4

                     705 .L220:

                     706 ;	"RptID\000"

00000380 49747052    707 	.data.b	82,112,116,73

00000384 0044       708 	.data.b	68,0

00000386 0000       709 	.align 4

                     710 

                     711 	.type	.L220,$object

                     712 	.size	.L220,4

                     713 

                     714 .L221:

00000388 00000000*   715 	.data.w	iedModel

                     716 	.type	.L221,$object

                     717 	.size	.L221,4

                     718 

                     719 .L303:

                     720 ;	"TrgOps\000"

0000038c 4f677254    721 	.data.b	84,114,103,79


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fvg1.s
00000390 7370       722 	.data.b	112,115

00000392 00         723 	.data.b	0

00000393 00         724 	.align 4

                     725 

                     726 	.type	.L303,$object

                     727 	.size	.L303,4

                     728 

                     729 .L379:

                     730 ;	"ConfRev\000"

00000394 666e6f43    731 	.data.b	67,111,110,102

00000398 00766552    732 	.data.b	82,101,118,0

                     733 	.align 4

                     734 

                     735 	.type	.L379,$object

                     736 	.size	.L379,4

                     737 

                     738 .L463:

                     739 ;	"OptFlds\000"

0000039c 4674704f    740 	.data.b	79,112,116,70

000003a0 0073646c    741 	.data.b	108,100,115,0

                     742 	.align 4

                     743 

                     744 	.type	.L463,$object

                     745 	.size	.L463,4

                     746 

                     747 .L855:

000003a4 00000000*   748 	.data.w	.L202

                     749 	.type	.L855,$object

                     750 	.size	.L855,4

                     751 

                     752 .L856:

000003a8 00000000*   753 	.data.w	iedModelSize

                     754 	.type	.L856,$object

                     755 	.size	.L856,4

                     756 

                     757 	.align	4

                     758 ;pReporter	r4	local

                     759 ;pRCB	r5	local

                     760 ;dataSetEntity	r1	local

                     761 ;dataSetFullName	[sp,12]	local

                     762 ;datSetDAPos	r1	local

                     763 ;innerTypeLen	[sp,4]	local

                     764 ;namePos	r0	local

                     765 ;nameTag	[sp,3]	local

                     766 ;nameLen	[sp,8]	local

                     767 ;pos	r6	local

                     768 

                     769 ;rcbPos	r7	param

                     770 ;buffered	fp	param

                     771 

                     772 	.section ".bss","awb"

                     773 .L796:

                     774 	.data

                     775 	.text

                     776 

                     777 ;234:     }


                     778 ;235: }


                     779 

                     780 ;236: 


                     781 ;237: void registerBufferedReport(int rcbPos)


                     782 	.align	4


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fvg1.s
                     783 	.align	4

                     784 registerBufferedReport::

                     785 ;238: {


                     786 

                     787 ;239:     TRACE("Buffered report. Pos: %04X", rcbPos);


                     788 ;240:     registerReport(rcbPos, TRUE);


                     789 

000003ac e3a01001    790 	mov	r1,1

000003b0 eaffff75*   791 	b	registerReport

                     792 	.endf	registerBufferedReport

                     793 	.align	4

                     794 

                     795 ;rcbPos	none	param

                     796 

                     797 	.section ".bss","awb"

                     798 .L878:

                     799 	.data

                     800 	.text

                     801 

                     802 ;241: }


                     803 

                     804 ;242: 


                     805 ;243: void registerUnbufferedReport(int rcbPos)


                     806 	.align	4

                     807 	.align	4

                     808 registerUnbufferedReport::

                     809 ;244: {


                     810 

                     811 ;245:     TRACE("Unbuffered report. Pos: %04X", rcbPos);


                     812 ;246:     registerReport(rcbPos, FALSE);


                     813 

000003b4 e3a01000    814 	mov	r1,0

000003b8 eaffff73*   815 	b	registerReport

                     816 	.endf	registerUnbufferedReport

                     817 	.align	4

                     818 

                     819 ;rcbPos	none	param

                     820 

                     821 	.section ".bss","awb"

                     822 .L910:

                     823 	.data

                     824 	.text

                     825 

                     826 ;247: }


                     827 

                     828 ;248: 


                     829 ;249: // Регистрирует все RCB, которые найдёт в указанном объекте FC


                     830 ;250: // В зависимости от имени FC регистрирует buffered, unbuffered,


                     831 ;251: // или вообще никакие RCB (если имя FC не "BR" и не "RP")


                     832 ;252: void registerRCBsGivenFC(int fcPos)


                     833 	.align	4

                     834 	.align	4

                     835 registerRCBsGivenFC::

000003bc e92d4010    836 	stmfd	[sp]!,{r4,lr}

                     837 ;253: {


                     838 

                     839 ;254:     StringView fcName;


                     840 ;255: 


                     841 ;256:     if(!getObjectName(fcPos, &fcName))


                     842 

000003c0 e24dd008    843 	sub	sp,sp,8


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fvg1.s
000003c4 e1a0100d    844 	mov	r1,sp

000003c8 e1a04000    845 	mov	r4,r0

000003cc eb000000*   846 	bl	getObjectName

000003d0 e3500000    847 	cmp	r0,0

000003d4 0a000014    848 	beq	.L917

                     849 ;257:     {


                     850 

                     851 ;258:         ERROR_REPORT("Unable to read FC name");


                     852 ;259:         return;


                     853 

                     854 ;260:     }


                     855 ;261:     if(fcName.len != 2)


                     856 

000003d8 e59d0000    857 	ldr	r0,[sp]

000003dc e3500002    858 	cmp	r0,2

000003e0 1a000011    859 	bne	.L917

                     860 ;262:     {


                     861 

                     862 ;263:         ERROR_REPORT("Invalid FC name");


                     863 ;264:         return;


                     864 

                     865 ;265:     }


                     866 ;266:     if ( memcmp("RP", fcName.p, 2) == 0)


                     867 

000003e4 e59d1004    868 	ldr	r1,[sp,4]

000003e8 e59f0078*   869 	ldr	r0,.L1032

000003ec e3a02002    870 	mov	r2,2

000003f0 eb000000*   871 	bl	memcmp

000003f4 e3500000    872 	cmp	r0,0

000003f8 1a000003    873 	bne	.L925

000003fc e59f1068*   874 	ldr	r1,.L1033

                     875 ;267:     {


                     876 

                     877 ;268:         //Unbuffered reports


                     878 ;269:         processSubobjects(fcPos, registerUnbufferedReport);


                     879 

00000400 e1a00004    880 	mov	r0,r4

00000404 eb000000*   881 	bl	processSubobjects

00000408 ea000007    882 	b	.L917

                     883 .L925:

                     884 ;270:     }


                     885 ;271:     else if( memcmp("BR", fcName.p, 2) == 0)


                     886 

0000040c e59d1004    887 	ldr	r1,[sp,4]

00000410 e59f0058*   888 	ldr	r0,.L1034

00000414 e3a02002    889 	mov	r2,2

00000418 eb000000*   890 	bl	memcmp

0000041c e3500000    891 	cmp	r0,0

                     892 ;272:     {


                     893 

                     894 ;273:         //Buffered reports


                     895 ;274:         processSubobjects(fcPos, registerBufferedReport);


                     896 

00000420 059f104c*   897 	ldreq	r1,.L1035

00000424 01a00004    898 	moveq	r0,r4

00000428 0b000000*   899 	bleq	processSubobjects

                     900 .L917:

0000042c e28dd008    901 	add	sp,sp,8

00000430 e8bd8010    902 	ldmfd	[sp]!,{r4,pc}

                     903 	.endf	registerRCBsGivenFC

                     904 	.align	4


                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fvg1.s
                     905 ;fcName	[sp]	local

                     906 ;.L1006	.L1010	static

                     907 ;.L1007	.L1011	static

                     908 

                     909 ;fcPos	r4	param

                     910 

                     911 	.section ".bss","awb"

                     912 .L1005:

                     913 	.section ".rodata","a"

                     914 .L1010:;	"RP\000"

00000008 5052       915 	.data.b	82,80

0000000a 00         916 	.data.b	0

                     917 	.type	.L1010,$object

                     918 	.size	.L1010,3

                     919 .L1011:;	"BR\000"

0000000b 5242       920 	.data.b	66,82

0000000d 00         921 	.data.b	0

                     922 	.type	.L1011,$object

                     923 	.size	.L1011,3

                     924 	.data

                     925 	.text

                     926 

                     927 ;275:     }


                     928 ;276: }


                     929 

                     930 ;277: 


                     931 ;278: void registerAllLogicalNodeRCB(int lnPos)


                     932 	.align	4

                     933 	.align	4

                     934 registerAllLogicalNodeRCB::

00000434 e59f103c*   935 	ldr	r1,.L1061

                     936 ;279: {


                     937 

                     938 ;280:     processSubobjects(lnPos, registerRCBsGivenFC);


                     939 

00000438 ea000000*   940 	b	processSubobjects

                     941 	.endf	registerAllLogicalNodeRCB

                     942 	.align	4

                     943 

                     944 ;lnPos	none	param

                     945 

                     946 	.section ".bss","awb"

                     947 .L1054:

                     948 	.data

                     949 	.text

                     950 

                     951 ;281: }


                     952 

                     953 ;282: 


                     954 ;283: void registerAllLogicalDeviceRCB(int ldPos)


                     955 	.align	4

                     956 	.align	4

                     957 registerAllLogicalDeviceRCB::

0000043c e92d4000    958 	stmfd	[sp]!,{lr}

                     959 ;284: {


                     960 

                     961 ;285:     int dataSectionPos;


                     962 ;286: 


                     963 ;287:     dataSectionPos = findObjectByTag(ldPos, IED_VMD_DATA_SECTION);


                     964 

00000440 e3a010ec    965 	mov	r1,236


                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fvg1.s
00000444 eb000000*   966 	bl	findObjectByTag

                     967 ;288:     if(!dataSectionPos)


                     968 

00000448 e3500000    969 	cmp	r0,0

0000044c 159f1028*   970 	ldrne	r1,.L1107

                     971 ;289:     {


                     972 

                     973 ;290:         ERROR_REPORT("Data section is not found");


                     974 ;291:         return;


                     975 

                     976 ;292:     }


                     977 ;293: 


                     978 ;294:     processSubobjects(dataSectionPos, registerAllLogicalNodeRCB);


                     979 

00000450 18bd4000    980 	ldmnefd	[sp]!,{lr}

00000454 1a000000*   981 	bne	processSubobjects

00000458 e8bd8000    982 	ldmfd	[sp]!,{pc}

                     983 	.endf	registerAllLogicalDeviceRCB

                     984 	.align	4

                     985 ;dataSectionPos	r1	local

                     986 

                     987 ;ldPos	none	param

                     988 

                     989 	.section ".bss","awb"

                     990 .L1096:

                     991 	.data

                     992 	.text

                     993 

                     994 ;295: }


                     995 

                     996 ;296: 


                     997 ;297: void registerAllRCB(void)


                     998 	.align	4

                     999 	.align	4

                    1000 registerAllRCB::

0000045c e59f101c*  1001 	ldr	r1,.L1141

                    1002 ;298: {


                    1003 

                    1004 ;299:     processSubobjects(0, registerAllLogicalDeviceRCB);


                    1005 

00000460 e3a00000   1006 	mov	r0,0

00000464 ea000000*  1007 	b	processSubobjects

                    1008 	.endf	registerAllRCB

                    1009 	.align	4

                    1010 

                    1011 	.section ".bss","awb"

                    1012 .L1134:

                    1013 	.data

                    1014 	.text

                    1015 

                    1016 ;300: }


                    1017 	.align	4

                    1018 .L1032:

00000468 00000000*  1019 	.data.w	.L1010

                    1020 	.type	.L1032,$object

                    1021 	.size	.L1032,4

                    1022 

                    1023 .L1033:

0000046c 00000000*  1024 	.data.w	registerUnbufferedReport

                    1025 	.type	.L1033,$object

                    1026 	.size	.L1033,4


                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fvg1.s
                    1027 

                    1028 .L1034:

00000470 00000000*  1029 	.data.w	.L1011

                    1030 	.type	.L1034,$object

                    1031 	.size	.L1034,4

                    1032 

                    1033 .L1035:

00000474 00000000*  1034 	.data.w	registerBufferedReport

                    1035 	.type	.L1035,$object

                    1036 	.size	.L1035,4

                    1037 

                    1038 .L1061:

00000478 00000000*  1039 	.data.w	registerRCBsGivenFC

                    1040 	.type	.L1061,$object

                    1041 	.size	.L1061,4

                    1042 

                    1043 .L1107:

0000047c 00000000*  1044 	.data.w	registerAllLogicalNodeRCB

                    1045 	.type	.L1107,$object

                    1046 	.size	.L1107,4

                    1047 

                    1048 .L1141:

00000480 00000000*  1049 	.data.w	registerAllLogicalDeviceRCB

                    1050 	.type	.L1141,$object

                    1051 	.size	.L1141,4

                    1052 

                    1053 	.align	4

                    1054 ;iedModel	iedModel	import

                    1055 ;iedModelSize	iedModelSize	import

                    1056 ;__UNNAMED_1_static_in_registerRCBDataset	.L202	static

                    1057 

                    1058 	.data

                    1059 	.ghsnote version,6

                    1060 	.ghsnote tools,3

                    1061 	.ghsnote options,0

                    1062 	.text

                    1063 	.align	4

                    1064 	.section ".rodata","a"

0000000e 0000      1065 	.align	4

                    1066 	.text

