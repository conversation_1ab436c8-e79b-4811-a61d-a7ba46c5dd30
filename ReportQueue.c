#include "ReportQueue.h"
#include <debug.h>
#include <string.h>



static void lockQueue(ReportQueue *queue)
{
	CriticalSection_Lock(&(queue->criticalSection));
}
static void unlockQueue(ReportQueue *queue)
{
	CriticalSection_Unlock(&(queue->criticalSection));
}
	

void ReportQueue_init(ReportQueue *queue)
{
	queue->chunkCount = REPORT_MEM_SIZE / REPORT_CHUNK_SIZE;
	queue->criticalErrorCount = 0;
	queue->lastOverflowCount = queue->overflowCount = 0;
	queue->head = 0;
	queue->tail = 0;
	CriticalSection_Init(&queue->criticalSection);
}

static int getFreeChunkCount(ReportQueue *queue)
{
	int freeChunkCount;
	// очередь переполнена
	if (queue->head == -1)
	{
		return 0;
	}
	// очередь пустая
	if (queue->head == queue->tail)
	{
		return queue->chunkCount;
	}
	// смотрим сколько осталось
	if (queue->head > queue->tail)
	{
		
		freeChunkCount = (queue->chunkCount - queue->head) + queue->tail;
	}
	else
	{	
		freeChunkCount = queue->tail - queue->head;
	}

	return freeChunkCount;
}

// пустое чтение, для осбождения памяти
static void dummyRead(ReportQueue *queue)
{
	ReportChunk *chunk;
	int tail;
	
	if (queue->head == -1)
	{
		queue->head = queue->tail;
	}

	tail = queue->tail;
	while (1)
	{
		chunk = &queue->chunk[tail];
		tail++;
		if (tail >= queue->chunkCount)
		{
			tail = 0;
		}

		if (chunk->lastFlag)
		{
			chunk->lastFlag = 0;
			break;
		}

		// очередь полностью освободилась
		if (tail == queue->head)
		{
			break;
		}
	}
	
	queue->tail = tail;
}

static void freeChunk(ReportQueue *queue, int chunkCount)
{	
	do
	{
		dummyRead(queue);
	} while (getFreeChunkCount(queue) < chunkCount);
}

int	 ReportQueue_write(ReportQueue *queue, unsigned char *data, int size)
{
	ReportChunk *chunk = NULL;
	int takeChunkCount;
	int freeChunkCount;
	int head;
	int copyCount;
	int sizeToWrite = size;
			
	// 
	if (size <= 0)
	{
		return 0;
	}

	// количество занимаемых кусков
	takeChunkCount = (size / REPORT_CHUNK_SIZE) + (size % REPORT_CHUNK_SIZE != 0);
	// пытаемся положить данных больше, чем общая память
	if (takeChunkCount > queue->chunkCount)
	{
		queue->criticalErrorCount++;
		queue->overflowCount++;
		return 0;
	}

	// блокировка, т.к. может вызваться purge
	lockQueue(queue);
	freeChunkCount = getFreeChunkCount(queue);
	if (freeChunkCount < takeChunkCount)
	{
		queue->overflowCount++;
		// освобождается нужное количество
		freeChunk(queue, takeChunkCount);
	}

	head = queue->head;

	while (sizeToWrite > 0)
	{
		copyCount = sizeToWrite > REPORT_CHUNK_SIZE ? REPORT_CHUNK_SIZE : sizeToWrite;
		chunk = &queue->chunk[head];

		if (copyCount == REPORT_CHUNK_SIZE)
		{
			//Закомментировано потому что выравнивание на 4 не гарантировано
			//copyChunk(chunk->payload, data);
			memcpy(chunk->payload, data, copyCount);
		}
		else
		{
			memcpy(chunk->payload, data, copyCount);
		}

		sizeToWrite -= copyCount;
		chunk->len = copyCount;
		chunk->lastFlag = 0;
		data += copyCount;
		head++;
		if (head >= queue->chunkCount)
		{
			head = 0;
		}
	}
	
	// тут chunk уже не может быть NULL
	chunk->lastFlag = 1;

	// последний элемент
	if (head == queue->tail) // переполнение
	{
		head = -1;
	}

	queue->head = head;
	unlockQueue(queue);
	return size;
}

int ReportQueue_read(ReportQueue *queue, unsigned char *data, int maxDataSize)
{
	int copyCount = 0;
	int tail;
	ReportChunk *chunk;

	if (ReportQueue_isEmpty(queue))
	{
		return 0;
	}
	
	lockQueue(queue);

	if (queue->head == -1)
	{
		queue->head = queue->tail;
	}

	tail = queue->tail;
	while (1)
	{
		chunk = &queue->chunk[tail];
		if (maxDataSize > chunk->len)
		{
			if (chunk->len == REPORT_CHUNK_SIZE)
			{
				//Закомментировано потому что выравнивание на 4 не гарантировано
				//copyChunk(data, chunk->payload);
				memcpy(data, chunk->payload, chunk->len);
			}
			else
			{
				memcpy(data, chunk->payload, chunk->len);
			}
			copyCount += chunk->len;
			data += chunk->len;
		}

		maxDataSize -= chunk->len;
		tail++;
		if (tail >= queue->chunkCount)
		{
			tail = 0;
		}
		if (chunk->lastFlag)
		{
			chunk->lastFlag = 0;
			break;
		}
		
	}
	queue->lastOverflowCount = queue->overflowCount;
	queue->tail = tail;
	unlockQueue(queue);
	return copyCount;
}

void ReportQueue_purge(ReportQueue *queue)
{
	lockQueue(queue);
	queue->head = queue->tail = 0;
	queue->lastOverflowCount = queue->overflowCount = 0;
	unlockQueue(queue);
}

int ReportQueue_isEmpty(ReportQueue *queue)
{
	return queue->head == queue->tail;
}

int ReportQueue_isOverflow(ReportQueue *queue)
{
	return queue->lastOverflowCount != queue->overflowCount;
}

