#include "ObjectNameParser.h"


#include "iedTree/iedTree.h"
#include "iedTree/iedNoEntity.h"

#include "BaseAsnTypes.h"

#include <debug.h>

// Получает имя из BER и находит подобъект объекта entity.
// Смещение bv должно указывать на BER со строкой имени.
// Если объект не найден, возвращает NoEntity.
// При прочих ошибках возвращает NULL
static IEDEntity parseName(BufferView* bv, uint8_t tag, IEDEntity entity)
{
    StringView name;
    if(!BufferView_decodeStringViewTL(bv, tag, &name))
    {
        ERROR_REPORT("Error parsing alternate object name");
        return NULL;
    }
    entity = IEDEntity_getChildByName(entity, &name);
    if(entity == NULL)
    {
        // Элемент не найден в дереве. Возвращаем NoEntity
        return IEDNoEntity_get();
    }
    return entity;
}
    

// Смещение bv должно указывать на первый байт (0xA0).
// Буфер должен заканчиваться с последний байтом имени.
// При ошибке разбора возвращает NULL
// Если объект с указанным именем не найден, возвращает NoEntity
static IEDEntity parseAlternateSpec(BufferView* bv, IEDEntity entity)
{
    uint8_t tag;
    size_t len;
    
    while (true)
    {
        if(!BufferView_peekTag(bv, &tag))
        {
            ERROR_REPORT("Error 7 parsing alternate object name");
            return NULL;
        }
        
        if(tag == 0x81)
        {
            // Alternate access из одного имени
            return parseName(bv, tag, entity);            
        }


        if(!BufferView_decodeTL(bv, &tag, &len, NULL))
        {
            ERROR_REPORT("Error 2 parsing alternate object name");
            return NULL;
        };
        if(tag != 0xA0)
        {
            ERROR_REPORT("Error 3 parsing alternate object name");
            return NULL;
        }
        if(!BufferView_peekTag(bv, &tag) || (tag != 0x80 && tag != 0x81))
        {
            ERROR_REPORT("Error 4 parsing alternate object name");
            return NULL;
        }
        entity = parseName(bv, tag, entity);
        if(entity == NULL)
        {
            return NULL;
        }
        if(BufferView_endOfBuf(bv))
        {
            return entity;
        }
        
        if(!BufferView_decodeTL(bv, &tag, &len, NULL) || tag != ASN_SEQUENCE)
        {
            ERROR_REPORT("Error 6 parsing alternate object name");
            return NULL;
        }        
    }
    
}

// Парсит элемент списка спецификации имён объектов.
// bv должен указывать на элемент списка, т.е. на ASN_SEQUENCE (0x30).
IEDEntity ObjectNameParser_parse(BufferView* bv)
{
    uint8_t tag;
    size_t len;
    BufferView nameSpecification;
    StringView ldName;
    StringView objectName;
    IEDEntity entity;

    if(!BufferView_decodeTL(bv, &tag, &len, NULL) || tag != ASN_SEQUENCE)
    {    
        ERROR_REPORT("Error parsing object name");
        return NULL;
    }

    BufferView_init(&nameSpecification, BufferView_currentPtr(bv), len, 0);

    // Продвигаемся вперёд на всю длину элемента списка чтобы следующий
    // вызов ObjectNameParser_parse попал на следующий элемент списка.
    // Дальше будем работать через nameSpecification
    if(!BufferView_advance(bv, len))
    {
        ERROR_REPORT("Error 1 parsing object name");
        return NULL;
    }

    if(!BufferView_decodeTL(&nameSpecification, &tag, &len, NULL) 
        || tag != 0xA0)
    {
        ERROR_REPORT("Error 2 parsing object name");
        return NULL;
    }
    if(!BufferView_decodeTL(&nameSpecification, &tag, &len, NULL) 
        || tag != 0xA1)
    {
        ERROR_REPORT("Error 3 parsing object name");
        return NULL;
    }

    // Здесь должно быть две строки - LD и имя объекта
    if(!BufferView_decodeStringViewTL(&nameSpecification, ASN_VISIBLE_STRING,
        &ldName))
    {
        ERROR_REPORT("Error parsing object LD name string");
        return NULL;
    }
    if(!BufferView_decodeStringViewTL(&nameSpecification, ASN_VISIBLE_STRING,
        &objectName))
    {
        ERROR_REPORT("Error parsing object name string");
        return NULL;
    }
        
    // Ищем элемент дерева полному имени
    entity = IEDTree_findDataByFullName(&ldName, &objectName);
    if(entity == NULL)
    {
        // Элемент не найден в дереве. Возвращаем NoEntity
        return IEDNoEntity_get();
    }

    if(BufferView_endOfBuf(&nameSpecification))
    {
        // Это был последний элемент в спецификации имени
        return entity;
    }

    // Alternate access
    if(!BufferView_decodeTL(&nameSpecification, &tag, &len, NULL) 
        || tag != 0xA5)
    {
        ERROR_REPORT("Error parsing alternate access");
        return NULL;
    }

    return parseAlternateSpec(&nameSpecification, entity);
}
