                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ioo1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=iedUInt.c -o iedTree\gh_ioo1.o -list=iedTree/iedUInt.lst C:\Users\<USER>\AppData\Local\Temp\gh_ioo1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_ioo1.s
Source File: iedUInt.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      10 ;		iedTree/iedUInt.c -o iedTree/iedUInt.o

                      11 ;Source File:   iedTree/iedUInt.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Oct 07 09:39:21 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "iedUInt.h"


                      21 ;2: 


                      22 ;3: #include "iedTree.h"


                      23 ;4: #include "iedFinalDA.h"


                      24 ;5: 


                      25 ;6: #include "../DataSlice.h"


                      26 ;7: #include "../AsnEncoding.h"


                      27 ;8: 


                      28 ;9: #include "debug.h"


                      29 ;10: 


                      30 ;11: #include "IEDCompile/AccessInfo.h"


                      31 ;12: 


                      32 ;13: #define MAX_UINT32_ENCODED_SIZE 7


                      33 ;14: 


                      34 ;15: static void updateFromDataSlice(IEDEntity entity)


                      35 	.text

                      36 	.align	4

                      37 updateFromDataSlice:

00000000 e92d4010     38 	stmfd	[sp]!,{r4,lr}

00000004 e1a04000     39 	mov	r4,r0

                      40 ;16: {


                      41 

                      42 ;17: 	int offset  = entity->dataSliceOffset;


                      43 

00000008 e594002c     44 	ldr	r0,[r4,44]

                      45 ;18:     uint32_t value;


                      46 ;19: 


                      47 ;20: 	if(offset == -1)


                      48 

0000000c e3700001     49 	cmn	r0,1

00000010 0a00000f     50 	beq	.L2


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ioo1.s
                      51 ;21: 	{


                      52 

                      53 ;22: 		return;


                      54 

                      55 ;23: 	}


                      56 ;24: 


                      57 ;25:     value = DataSlice_getUInt32FastCurrDS(offset);


                      58 

00000014 e1a00800     59 	mov	r0,r0 lsl 16

00000018 e1a00820     60 	mov	r0,r0 lsr 16

0000001c eb000000*    61 	bl	DataSlice_getUInt32FastCurrDS

                      62 ;26: 


                      63 ;27:     if(entity->uintValue == value)


                      64 

00000020 e5941030     65 	ldr	r1,[r4,48]

00000024 e1510000     66 	cmp	r1,r0

                      67 ;28: 	{


                      68 

                      69 ;29: 		entity->changed = TRGOP_NONE;


                      70 

00000028 03a00000     71 	moveq	r0,0

0000002c 05840028     72 	streq	r0,[r4,40]

00000030 0a000007     73 	beq	.L2

                      74 ;30: 	}


                      75 ;31: 	else


                      76 ;32: 	{


                      77 

                      78 ;33: 		entity->changed = entity->trgOps;


                      79 

00000034 e5941024     80 	ldr	r1,[r4,36]

00000038 e5840030     81 	str	r0,[r4,48]

                      82 ;35: 		IEDEntity_setTimeStamp(entity, dataSliceGetTimeStamp());


                      83 

0000003c e5841028     84 	str	r1,[r4,40]

                      85 ;34:         entity->uintValue = value;


                      86 

00000040 eb000000*    87 	bl	dataSliceGetTimeStamp

00000044 e1a02001     88 	mov	r2,r1

00000048 e1a01000     89 	mov	r1,r0

0000004c e1a00004     90 	mov	r0,r4

00000050 eb000000*    91 	bl	IEDEntity_setTimeStamp

                      92 .L2:

00000054 e8bd4010     93 	ldmfd	[sp]!,{r4,lr}

00000058 e12fff1e*    94 	ret	

                      95 	.endf	updateFromDataSlice

                      96 	.align	4

                      97 ;offset	r0	local

                      98 ;value	r0	local

                      99 

                     100 ;entity	r4	param

                     101 

                     102 	.section ".bss","awb"

                     103 .L56:

                     104 	.data

                     105 	.text

                     106 

                     107 ;36: 	}


                     108 ;37: }


                     109 

                     110 ;38: 


                     111 ;39: static bool calcReadLen(IEDEntity entity, size_t* pLen )



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ioo1.s
                     112 	.align	4

                     113 	.align	4

                     114 calcReadLen:

0000005c e92d4010    115 	stmfd	[sp]!,{r4,lr}

                     116 ;40: {


                     117 

                     118 ;41: 	// +2 for tag and length


                     119 ;42:     *pLen = BerEncoder_UInt32determineEncodedSize(entity->uintValue) + 2;


                     120 

00000060 e5900030    121 	ldr	r0,[r0,48]

00000064 e1a04001    122 	mov	r4,r1

00000068 eb000000*   123 	bl	BerEncoder_UInt32determineEncodedSize

0000006c e2800002    124 	add	r0,r0,2

00000070 e5840000    125 	str	r0,[r4]

                     126 ;43: 


                     127 ;44: 	return true;


                     128 

00000074 e3a00001    129 	mov	r0,1

00000078 e8bd4010    130 	ldmfd	[sp]!,{r4,lr}

0000007c e12fff1e*   131 	ret	

                     132 	.endf	calcReadLen

                     133 	.align	4

                     134 

                     135 ;entity	r0	param

                     136 ;pLen	r4	param

                     137 

                     138 	.section ".bss","awb"

                     139 .L97:

                     140 	.data

                     141 	.text

                     142 

                     143 ;45: }


                     144 

                     145 ;46: 


                     146 ;47: static bool encodeRead(IEDEntity entity, BufferView* outBuf)


                     147 	.align	4

                     148 	.align	4

                     149 encodeRead:

00000080 e92d4030    150 	stmfd	[sp]!,{r4-r5,lr}

00000084 e24dd004    151 	sub	sp,sp,4

00000088 e1a0200d    152 	mov	r2,sp

0000008c e1a05000    153 	mov	r5,r0

00000090 e1a04001    154 	mov	r4,r1

00000094 e1a00004    155 	mov	r0,r4

00000098 e3a01007    156 	mov	r1,7

0000009c eb000000*   157 	bl	BufferView_alloc

                     158 ;48: {


                     159 

                     160 ;49: 	uint8_t* encodeBuf;


                     161 ;50: 	int fullEncodedLen;


                     162 ;51: 


                     163 ;52: 	//Запрашиваем в буфере максимум места чтобы не вычислять.


                     164 ;53: 	//Это фактически только проверка, поэтому небольшая жадность не повредит.


                     165 ;54:     if(!BufferView_alloc(outBuf,MAX_UINT32_ENCODED_SIZE, &encodeBuf))


                     166 

000000a0 e3500000    167 	cmp	r0,0

                     168 ;55: 	{


                     169 

                     170 ;56: 		ERROR_REPORT("Unable to allocate buffer");


                     171 ;57: 		return false;


                     172 


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ioo1.s
000000a4 0a000008    173 	beq	.L104

                     174 ;58: 	}


                     175 ;59: 


                     176 ;60: 	//Функция возвращает новое смещение в буфере, но поскольку начальное


                     177 ;61: 	//смещение 0, можно считать это размером.


                     178 ;62:     fullEncodedLen = BerEncoder_encodeUInt32WithTL(


                     179 

000000a8 e59d2000    180 	ldr	r2,[sp]

000000ac e5951030    181 	ldr	r1,[r5,48]

000000b0 e3a03000    182 	mov	r3,0

000000b4 e3a00086    183 	mov	r0,134

000000b8 eb000000*   184 	bl	BerEncoder_encodeUInt32WithTL

                     185 ;63:                 IEC61850_BER_UNSIGNED_INTEGER, entity->uintValue, encodeBuf, 0);


                     186 ;64: 


                     187 ;65: 	outBuf->pos += fullEncodedLen;


                     188 

000000bc e5941004    189 	ldr	r1,[r4,4]

000000c0 e0811000    190 	add	r1,r1,r0

000000c4 e5841004    191 	str	r1,[r4,4]

                     192 ;66: 	return true;


                     193 

000000c8 e3a00001    194 	mov	r0,1

                     195 .L104:

000000cc e28dd004    196 	add	sp,sp,4

000000d0 e8bd4030    197 	ldmfd	[sp]!,{r4-r5,lr}

000000d4 e12fff1e*   198 	ret	

                     199 	.endf	encodeRead

                     200 	.align	4

                     201 ;encodeBuf	[sp]	local

                     202 

                     203 ;entity	r5	param

                     204 ;outBuf	r4	param

                     205 

                     206 	.section ".bss","awb"

                     207 .L154:

                     208 	.data

                     209 	.text

                     210 

                     211 ;67: }


                     212 

                     213 ;68: 


                     214 ;69: 


                     215 ;70: void IEDUInt32_init(IEDEntity entity)


                     216 	.align	4

                     217 	.align	4

                     218 IEDUInt32_init::

000000d8 e92d4070    219 	stmfd	[sp]!,{r4-r6,lr}

000000dc e280405c    220 	add	r4,r0,92

000000e0 e5140004    221 	ldr	r0,[r4,-4]

                     222 ;73: 	IntBoolAccessInfo* accessInfo = extInfo->accessInfo;


                     223 

000000e4 e5900000    224 	ldr	r0,[r0]

                     225 ;74: 


                     226 ;75: 	//Если будет ошибка, то запишется -1;


                     227 ;76: 	entity->dataSliceOffset = DataSlice_getIntOffset(accessInfo->valueOffset);


                     228 

000000e8 e59f5028*   229 	ldr	r5,.L197

000000ec e5900004    230 	ldr	r0,[r0,4]

000000f0 e59f6024*   231 	ldr	r6,.L198

                     232 ;71: {


                     233 


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ioo1.s
                     234 ;72: 	TerminalItem* extInfo = entity->extInfo;


                     235 

000000f4 eb000000*   236 	bl	DataSlice_getIntOffset

000000f8 e5040030    237 	str	r0,[r4,-48]

                     238 ;77: 


                     239 ;78: 	entity->calcReadLen = calcReadLen;


                     240 

                     241 ;79: 	entity->encodeRead = encodeRead;


                     242 

000000fc e1a00006    243 	mov	r0,r6

00000100 e8840021    244 	stmea	[r4],{r0,r5}

                     245 ;80: 	entity->updateFromDataSlice = updateFromDataSlice;


                     246 

00000104 e59f0014*   247 	ldr	r0,.L199

00000108 e584000c    248 	str	r0,[r4,12]

                     249 ;81: 


                     250 ;82: 	IEDTree_addToCmpList(entity);


                     251 

0000010c e244005c    252 	sub	r0,r4,92

00000110 e8bd4070    253 	ldmfd	[sp]!,{r4-r6,lr}

00000114 ea000000*   254 	b	IEDTree_addToCmpList

                     255 	.endf	IEDUInt32_init

                     256 	.align	4

                     257 ;extInfo	r0	local

                     258 ;accessInfo	r0	local

                     259 

                     260 ;entity	r4	param

                     261 

                     262 	.section ".bss","awb"

                     263 .L190:

                     264 	.data

                     265 	.text

                     266 

                     267 ;83: }


                     268 	.align	4

                     269 .L197:

00000118 00000000*   270 	.data.w	calcReadLen

                     271 	.type	.L197,$object

                     272 	.size	.L197,4

                     273 

                     274 .L198:

0000011c 00000000*   275 	.data.w	encodeRead

                     276 	.type	.L198,$object

                     277 	.size	.L198,4

                     278 

                     279 .L199:

00000120 00000000*   280 	.data.w	updateFromDataSlice

                     281 	.type	.L199,$object

                     282 	.size	.L199,4

                     283 

                     284 	.align	4

                     285 

                     286 	.data

                     287 	.ghsnote version,6

                     288 	.ghsnote tools,3

                     289 	.ghsnote options,0

                     290 	.text

                     291 	.align	4

