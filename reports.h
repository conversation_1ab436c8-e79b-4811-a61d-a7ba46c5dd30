#pragma once

//Включен из за SessionOutBuffer
#include "rcb.h"

#include "stringView.h"
#include "IsoConnectionForward.h"
#include <types.h>
#include <stdint.h>
#include <stdbool.h>

#define MAX_REPORT_COUNT 40

typedef struct ReporterStruct * PReporter;

extern size_t g_reportCount;

PReporter getReporterByIndex(size_t index);

bool isRCBConnected(PReporter pReport);


//! Функция ничего не делает, только проверяет размер Data set
//! указанного очёта.
//! Остаток из прошлого. Проверку надо перенсти и функцию удалить.
bool initReportCompareDataset(PReporter reporter);
void initReports(void);
PReporter getFreeReport(void);
void finalizeReportRegistration(void);
void disableDisconnectedReports(void);

bool Reporter_isOwnerConnection(PReporter pReport, IsoConnection* conn);
bool Reporter_setEnable(PReporter rpt, IsoConnection* isoConection,
	bool enable);
bool Reporter_setResv(PReporter rpt, IsoConnection* isoConn, bool value);
void Reporter_setDataSetName(PReporter rpt, StringView* name);
void Reporter_setIntgPd(PReporter rpt, uint32_t intgPd);
void Reporter_setGI(PReporter rpt, bool gi);
