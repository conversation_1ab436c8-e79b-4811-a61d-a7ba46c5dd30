#include "mmsservices.h"

#include "AsnEncoding.h"
#include "mms_error.h"
#include "mms_read.h"
#include "mms_write.h"
#include "mms_get_name_list.h"
#include "mms_get_variable_access_attributes.h"
#include "mms_get_data_set_access_attr.h"
#include "mms_fs.h"
#include "mms.h"
#include "bufViewBER.h"
#include <debug.h>
#include <types.h>
#include <stddef.h>
#include <string.h>

static char* g_vendor = "MTRA";
static char* g_model = "device";
static char* g_revision = "1";

int mmsServer_handleIdentifyRequest(uint32_t invokeId, uint8_t* outBuf)
{
    int bufPos = 0;

    uint32_t invokeIdLength = BerEncoder_UInt32determineEncodedSize(invokeId);
    uint32_t vendorNameLength = strlen(g_vendor);
    uint32_t modelNameLength = strlen(g_model);
    uint32_t revisionLength = strlen(g_revision);

    uint32_t identityLength = 3 +  BerEncoder_determineLengthSize(vendorNameLength)
            + BerEncoder_determineLengthSize(modelNameLength) + BerEncoder_determineLengthSize(revisionLength)
            + vendorNameLength + modelNameLength + revisionLength;

    uint32_t identifyResponseLength = invokeIdLength + 2 + 1 + BerEncoder_determineLengthSize(identityLength)
            + identityLength;

    /* Identify response pdu */
    bufPos = BerEncoder_encodeTL(0xa1, identifyResponseLength, outBuf, bufPos);

    /* invokeId */
    bufPos = BerEncoder_encodeTL(0x02, invokeIdLength, outBuf, bufPos);
    bufPos = BerEncoder_encodeUInt32(invokeId, outBuf, bufPos);

    bufPos = BerEncoder_encodeTL(0xa2, identityLength, outBuf, bufPos);
    bufPos = BerEncoder_encodeStringWithTL(0x80, g_vendor, outBuf, bufPos);
    bufPos = BerEncoder_encodeStringWithTL(0x81, g_model, outBuf, bufPos);
    bufPos = BerEncoder_encodeStringWithTL(0x82, g_revision, outBuf, bufPos);

    return bufPos;
}




int handleConfirmedRequestPdu(IsoConnection* isoConn,
                               unsigned char* inBuf, int inBufPos, int inBufLen,
                               unsigned char* outBuf, int maxBufSize)

{	
	BufferView inBufView;
	BufferView outBufView;
	MmsConnection* mmsConn = &isoConn->mmsConn;

    int fullResponseSize = 0;
    // Размер ответа на каждый запрос
    int requestResponseSize;
    unsigned int invokeId = 0;
    

    while(inBufPos < inBufLen)
    {
		uint32_t tag;
		BufferView pktBufview;
		//Размер данных тэга
		size_t length;
		
		//Временная переменная для некоторых сервисов
		bool serviceOK;

		//Эта переменная проверяется только если requestResponseSize == 0.
		//Тэги, которые просто содержат информацию, но не вызывают
		//формирования ответа, должны устанавливать её в true.
		//В противном случае requestResponseSize == 0 будет считаться
		//необработанной ошибкой.
		//Эту муть можно будет переделать когда сервисы будут явно возвращать
		//наличие/отсутствие ошибки
		bool tagOK = false;

		BufferView_init(&pktBufview, inBuf, inBufLen, inBufPos);
		if (!BufferView_decodeExtTL(&pktBufview, &tag, &length, NULL))
		{
			//Не понятно, что делать с такой ошибкой
			ERROR_REPORT("Unable to parse request");
			break;
		}
		inBufPos = pktBufview.pos;

		// Пока некоторые операции используют непосредственно буфера,
		// а некоторые - BufferView, готовим BufferView для каждой операции.
		// В будущем надо сделать инициализацию BufferView в начале функции,
		// Для входящих данных и для исходящих.
		BufferView_init(&inBufView, inBuf + inBufPos, length, 0);
		BufferView_init(&outBufView, outBuf, maxBufSize - fullResponseSize, 0);
		
		switch (tag)
		{			
		case ASN_INTEGER: //Invoke ID
			invokeId = BerDecoder_decodeUint32(inBuf, length, inBufPos);							
			requestResponseSize = 0;
			tagOK = true;
			break;

		case MMS_SERVICE_READ_CODE: 			
            requestResponseSize = mms_handleReadRequest(mmsConn,
                inBuf, inBufPos, inBufLen, invokeId, outBuf, maxBufSize - fullResponseSize);

			break;

		case MMS_SERVICE_WRITE_CODE:			
			TRACE("MMS write request received");
			requestResponseSize = mms_handleWriteRequest(isoConn, inBuf, inBufPos,
				inBufLen, invokeId, outBuf);
			break;

		case MMS_SERVICE_GET_NAME_LIST_CODE:			
			//TRACE("MMS_SERVICE_GET_NAME_LIST_CODE");
			requestResponseSize = mms_handleGetNameListRequest(mmsConn, inBuf, inBufPos,
				inBufLen, invokeId, outBuf);
			debugSendUshort("requestResponseSize", requestResponseSize);
			break;

		case MMS_SERVICE_GET_VARIABLE_ACCESS_ATRIBUTES_CODE:
			//TRACE("MMS_SERVICE_GET_VARIABLE_ACCESS_ATRIBUTES_CODE");

			requestResponseSize = mms_handleGetVariableAccessAttr(mmsConn, inBuf, inBufPos,
				inBufLen, invokeId, outBuf);
			break;

		case MMS_SERVICE_GET_DATA_SET_ATTRIBUTES_CODE:
			//TRACE("MMS_SERVICE_GET_DATA_SET_ATTRIBUTES_CODE");
			requestResponseSize = mms_handleGetDataSetAccessAttr(mmsConn, inBuf, inBufPos,
				inBufLen, invokeId, outBuf);
			break;

		case MMS_SERVICE_IDENTIFY_CODE:
			TRACE("Identify service");
			requestResponseSize = mmsServer_handleIdentifyRequest(invokeId, outBuf);
			debugSendUshort("Identify service returned:", requestResponseSize);
			break;

		case MMS_SERVICE_FILE_DIRECTORY_REQUEST:			
			serviceOK = mms_handleFileDirRequest(mmsConn,
				&inBufView, invokeId, &outBufView);
			if (serviceOK)
			{
				requestResponseSize = outBufView.pos;
			}
			else
			{
				ERROR_REPORT("handleFileDirRequest error");
				requestResponseSize = 0;
			}

			break;

		case MMS_SERVICE_FILE_OPEN_REQUEST:			
			serviceOK = mms_handleFileOpenRequest(mmsConn,
				&inBufView, invokeId, &outBufView);
			if (serviceOK)
			{
				requestResponseSize = outBufView.pos;
			}
			else
			{
				ERROR_REPORT("handleFileOpenRequest error");
				requestResponseSize = 0;
			}
			break;

		case MMS_SERVICE_FILE_READ_REQUEST:							
			serviceOK = mms_handleFileReadRequest(mmsConn,
				&inBufView, invokeId, &outBufView);
			if (serviceOK)
			{
				requestResponseSize = outBufView.pos;
			}
			else
			{
				ERROR_REPORT("handleFileReadRequest error");
				requestResponseSize = 0;
			}
			break;
		case MMS_SERVICE_FILE_CLOSE_REQUEST:							
			serviceOK = mms_handleFileCloseRequest(mmsConn,
				&inBufView, invokeId, &outBufView);
			if (serviceOK)
			{
				requestResponseSize = outBufView.pos;
			}
			else
			{
				ERROR_REPORT("handleFileReadRequest error");
				requestResponseSize = 0;
			}
			break;

		default:
			ERROR_REPORT("Unsupperted MMS tag %04X", tag);
			requestResponseSize = mms_createMmsRejectPdu(&invokeId,
				MMS_ERROR_REJECT_UNRECOGNIZED_SERVICE, outBuf);				
			break;
		}
		if (requestResponseSize == 0 && !tagOK)
		{
			ERROR_REPORT("Unknown MMS service error");
			requestResponseSize = CreateMmsConfirmedErrorPdu(invokeId,
				outBuf, MMS_ERROR_ACCESS_OTHER);
		}
        
        inBufPos += length;
        outBuf += requestResponseSize;        
        fullResponseSize += requestResponseSize;		
    }

	//TRACE("fullResponseSize = %d", fullResponseSize);
	VERIFY(fullResponseSize <= maxBufSize)
	
    return fullResponseSize;
}
