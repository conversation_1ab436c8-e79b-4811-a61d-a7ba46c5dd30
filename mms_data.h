#pragma once

#include <types.h>
#include "IEDCompile/AccessInfo.h"
#include <stdint.h>
#include <stddef.h>
#include <stdbool.h>

uint16_t qualityFromBits(QualityAccsessInfo* accessInfo);
uint16_t qualityFromBitsFast(void* dataSliceWnd, QualityAccsessInfo* accessInfo);
float readFloatValue(FloatAccsessInfo* accessInfo);
float readRealValue(FloatAccsessInfo* accessInfo);
//! По значению типа enumerator возвращает соответствующее ему значение данных
int getEnumDataValue(int enumValue, EnumTableRecord* table, size_t tableSize);
//! По значению dataValue возвращает
//! соответствующее ему значение типа enumerator
int getEnumValue(int dataValue, EnumTableRecord* table, size_t tableSize);
int readIntSettValue(IntBoolAccessInfo* accessInfo);
int readIntValue(IntBoolAccessInfo* accessInfo);
int readBoolValue(IntBoolAccessInfo* accessInfo);

int encodeUInt32Value(uint8_t* outBuf, int bufPos, uint32_t value,
    bool determineSize);
int encodeBoolValue(uint8_t* outBuf, int bufPos, bool value,
    bool determineSize);
int readCodedEnum(CodedEnumAccessInfo* accessInfo);
int encodeReadCodedEnum(uint8_t* outBuf, int bufPos, void* descrStruct,
    bool determineSize);
int encodeOctetString8Value(uint8_t* outBuf, int bufPos, void* pValue,
    bool determineSize);

int encodeReadFloat(uint8_t* outBuf, int bufPos, void* descrStruct, bool determineSize);
int encodeReadReal(uint8_t* outBuf, int bufPos, void* descrStruct,
                   bool determineSize);
int encodeReadFloatSett(uint8_t* outBuf, int bufPos, void* descrStruct, bool determineSize);
int encodeReadRealSett(uint8_t* outBuf, int bufPos, void* descrStruct, bool determineSize);
int MMSData_encodeTimeStamp(uint8_t tag, uint64_t timeStamp, uint8_t* outBuf,
    int bufPos);
int encodeReadTimeStamp(uint8_t* outBuf, int bufPos, bool determineSize);
int encodeReadInt32(uint8_t* outBuf, int bufPos, void* descrStruct, bool determineSize);
int encodeReadInt32Sett(uint8_t* outBuf, int bufPos, void* descrStruct, bool determineSize);
int encodeReadInt32U(uint8_t* outBuf, int bufPos, void* descrStruct, bool determineSize);
int encodeReadInt32USett(uint8_t* outBuf, int bufPos, void* descrStruct, bool determineSize);
int encodeReadBoolean(uint8_t* outBuf, int bufPos, void* descrStruct,
    bool determineSize);

int encodeAccessAttrConst(uint8_t* outBuf, int bufPos, int constPos, bool determineSize);
int encodeAccessAttrFloat(uint8_t* outBuf, int bufPos, bool determineSize);
int encodeAccessAttrQuality(uint8_t* outBuf, int bufPos, bool determineSize);
int encodeAccessAttrBitString(int bitCount, uint8_t* outBuf, int bufPos,
    bool determineSize);
int encodeAccessAttrTimeStamp(uint8_t* outBuf, int bufPos, bool determineSize);
int encodeAccessAttrString(uint8_t* outBuf, int bufPos, uint8_t tag, int size,
    bool determineSize);
int encodeAccessAttrInt(uint8_t* outBuf, int bufPos, uint8_t bitCount,
    bool determineSize);
int encodeAccessAttrInt128(uint8_t* outBuf, int bufPos, bool determineSize);
int encodeAccessAttrUInt(uint8_t* outBuf, int bufPos, uint8_t bitCount,
    bool determineSize);
int encodeAccessAttrBoolean(uint8_t* outBuf, int bufPos, bool determineSize);
int encodeAccessAttrCodedEnum(uint8_t* outBuf, int bufPos, int accessDataPos,
    bool determineSize);





