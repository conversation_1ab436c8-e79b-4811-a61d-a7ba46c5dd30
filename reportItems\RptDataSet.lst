                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gu81.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=RptDataSet.c -o reportItems\gh_gu81.o -list=reportItems/RptDataSet.lst C:\Users\<USER>\AppData\Local\Temp\gh_gu81.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_gu81.s
Source File: RptDataSet.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      10 ;		reportItems/RptDataSet.c -o reportItems/RptDataSet.o

                      11 ;Source File:   reportItems/RptDataSet.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:55:49 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: 


                      21 ;2: #include "RptDataSet.h"


                      22 ;3: 


                      23 ;4: #include "RptItem.h"


                      24 ;5: #include "../iedTree/DataSet.h"


                      25 ;6: #include "../reporter.h"


                      26 ;7: #include "../reports.h"


                      27 ;8: 


                      28 ;9: 


                      29 ;10: 


                      30 ;11: 


                      31 ;12: static RptDataSetItem createRptDataSetItem(


                      32 

                      33 ;32: }    


                      34 

                      35 ;33: 


                      36 ;34: static void addRptDataSetItem(RptDataSet rptDataSet, RptDataSetItem rptDsItem)


                      37 

                      38 ;45: }


                      39 

                      40 ;46: 


                      41 ;47: void RptDataSetItem_addFinalDARptItem(RptDataSetItem rptDSItem, RptItem rptItem)


                      42 ;48: {


                      43 ;49:     if(rptDSItem->firstFinalDAItem == NULL)


                      44 ;50:     {


                      45 ;51:         rptDSItem->firstFinalDAItem = rptItem;


                      46 ;52:     }


                      47 ;53:     else


                      48 ;54:     {


                      49 ;55:         rptDSItem->lastFinalDAItem->nextFinalDARptItem = rptItem;


                      50 ;56:     }



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gu81.s
                      51 ;57:     rptDSItem->lastFinalDAItem = rptItem;


                      52 ;58: }


                      53 ;59: 


                      54 ;60: 


                      55 ;61: 


                      56 ;62: // Обновляет новоесостояние RptDataSetItem на основе данных из IEDTree


                      57 ;63: static void RptDataSetItem_updateChanges(RptDataSetItem rptDsItem)


                      58 

                      59 ;87:     }


                      60 ;88: }


                      61 

                      62 ;89: 


                      63 ;90: // Переписывает новые значения поверх старых


                      64 ;91: static void RptDataSetItem_overwriteOld(RptDataSetItem rptDsItem)


                      65 

                      66 ;98:     }


                      67 ;99: }


                      68 

                      69 	.text

                      70 	.align	4

                      71 RptDataSetItem_addFinalDARptItem::

00000000 e5902008     72 	ldr	r2,[r0,8]

00000004 e3520000     73 	cmp	r2,0

00000008 1590200c     74 	ldrne	r2,[r0,12]

0000000c 05801008     75 	streq	r1,[r0,8]

00000010 15821010     76 	strne	r1,[r2,16]

00000014 e580100c     77 	str	r1,[r0,12]

00000018 e12fff1e*    78 	ret	

                      79 	.endf	RptDataSetItem_addFinalDARptItem

                      80 	.align	4

                      81 

                      82 ;rptDSItem	r0	param

                      83 ;rptItem	r1	param

                      84 

                      85 	.section ".bss","awb"

                      86 .L138:

                      87 	.data

                      88 	.text

                      89 

                      90 

                      91 ;100: 


                      92 ;101: RptDataSet RptDataSet_create(PReporter reporter)


                      93 	.align	4

                      94 	.align	4

                      95 RptDataSet_create::

0000001c e92d4070     96 	stmfd	[sp]!,{r4-r6,lr}

                      97 ;102: {   


                      98 

                      99 ;103:     DataSetItem* dsItem; 


                     100 ;104:     DataSet* srcDataSet = reporter->dataSet;


                     101 

00000020 e5904038    102 	ldr	r4,[r0,56]

                     103 ;105:     RptDataSet rptDataSet = RptItem_alloc(sizeof(struct RptDataSetStruct));


                     104 

00000024 e3a00014    105 	mov	r0,20

00000028 eb000000*   106 	bl	RptItem_alloc

0000002c e1b05000    107 	movs	r5,r0

                     108 ;106:     if(rptDataSet == NULL)


                     109 

00000030 1a000001    110 	bne	.L153

                     111 .L154:


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gu81.s
                     112 ;107:     {


                     113 

                     114 ;108:         return NULL;


                     115 

00000034 e3a00000    116 	mov	r0,0

00000038 ea00001b    117 	b	.L151

                     118 .L153:

                     119 ;109:     }


                     120 ;110: 


                     121 ;111:     dsItem = srcDataSet->firstItem;


                     122 

0000003c e5946000    123 	ldr	r6,[r4]

                     124 ;112:     while(dsItem != NULL)


                     125 

00000040 e3560000    126 	cmp	r6,0

00000044 0a000017    127 	beq	.L157

                     128 .L161:

                     129 ;113:     {


                     130 

                     131 ;114:         RptDataSetItem rptDsItem = createRptDataSetItem(rptDataSet,dsItem);


                     132 

                     133 ;13:     RptDataSet rptDataSet, DataSetItem* srcDSItem)


                     134 ;14: {


                     135 

                     136 ;15:     RptItem rptitem;


                     137 ;16:     RptDataSetItem dsItem = RptItem_alloc(sizeof(struct RptDataSetItemStruct));


                     138 

00000048 e3a00020    139 	mov	r0,32

0000004c eb000000*   140 	bl	RptItem_alloc

00000050 e1b04000    141 	movs	r4,r0

                     142 ;17:     if(dsItem == NULL)


                     143 

00000054 0a000004    144 	beq	.L164

                     145 ;18:     {


                     146 

                     147 ;19:         return NULL;


                     148 

                     149 ;20:     }


                     150 ;21:     dsItem->parentRptDataSet = rptDataSet;


                     151 

00000058 e5845000    152 	str	r5,[r4]

                     153 ;22: 


                     154 ;23:     rptitem = RptItem_create(srcDSItem->obj, dsItem);


                     155 

0000005c e5960014    156 	ldr	r0,[r6,20]

00000060 e1a01004    157 	mov	r1,r4

00000064 eb000000*   158 	bl	RptItem_create

                     159 ;24:     if(rptitem == NULL)


                     160 

00000068 e3500000    161 	cmp	r0,0

                     162 .L164:

                     163 ;25:     {


                     164 

                     165 ;26:         return NULL;


                     166 

0000006c 03a04000    167 	moveq	r4,0

                     168 ;115:         if(rptDsItem == NULL)


                     169 

00000070 0affffef    170 	beq	.L154

                     171 .L165:

                     172 ;27:     }



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gu81.s
                     173 ;28:     


                     174 ;29:     dsItem->rptItem = rptitem;


                     175 

00000074 e5840004    176 	str	r0,[r4,4]

                     177 ;30: 


                     178 ;31:     return dsItem;


                     179 

                     180 ;115:         if(rptDsItem == NULL)


                     181 

00000078 e3540000    182 	cmp	r4,0

0000007c 0affffec    183 	beq	.L154

                     184 ;116:         {


                     185 

                     186 ;117:             return NULL;


                     187 

                     188 ;118:         }


                     189 ;119:         addRptDataSetItem(rptDataSet, rptDsItem);


                     190 

                     191 ;35: {


                     192 

                     193 ;36:     if(rptDataSet->firstItem == NULL)


                     194 

00000080 e5950004    195 	ldr	r0,[r5,4]

00000084 e3500000    196 	cmp	r0,0

                     197 ;39:     }


                     198 ;40:     else


                     199 ;41:     {


                     200 

                     201 ;42:         rptDataSet->lastItem->next = rptDsItem;


                     202 

00000088 15950008    203 	ldrne	r0,[r5,8]

0000008c 1580401c    204 	strne	r4,[r0,28]

                     205 ;43:     }


                     206 ;44:     rptDataSet->lastItem = rptDsItem;


                     207 

00000090 15854008    208 	strne	r4,[r5,8]

                     209 ;120:         dsItem = dsItem->next;


                     210 

                     211 ;37:     {


                     212 

                     213 ;38:         rptDataSet->firstItem = rptDsItem;


                     214 

00000094 01a00004    215 	moveq	r0,r4

00000098 09850011    216 	stmeqfa	[r5],{r0,r4}

                     217 ;43:     }


                     218 ;44:     rptDataSet->lastItem = rptDsItem;


                     219 

                     220 ;120:         dsItem = dsItem->next;


                     221 

0000009c e5966000    222 	ldr	r6,[r6]

000000a0 e3560000    223 	cmp	r6,0

000000a4 1affffe7    224 	bne	.L161

                     225 .L157:

                     226 ;121:     }


                     227 ;122: 


                     228 ;123:     return rptDataSet;    


                     229 

000000a8 e1a00005    230 	mov	r0,r5

                     231 .L151:

000000ac e8bd8070    232 	ldmfd	[sp]!,{r4-r6,pc}

                     233 	.endf	RptDataSet_create


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gu81.s
                     234 	.align	4

                     235 ;dsItem	r6	local

                     236 ;srcDataSet	r4	local

                     237 ;rptDataSet	r5	local

                     238 ;rptDsItem	r4	local

                     239 ;rptitem	r0	local

                     240 ;dsItem	r4	local

                     241 

                     242 ;reporter	r0	param

                     243 

                     244 	.section ".bss","awb"

                     245 .L302:

                     246 	.data

                     247 	.text

                     248 

                     249 ;124: }


                     250 

                     251 ;125: 


                     252 ;126: void RptDataSet_updateChanges(RptDataSet rptDataSet)


                     253 	.align	4

                     254 	.align	4

                     255 RptDataSet_updateChanges::

000000b0 e92d4cf0    256 	stmfd	[sp]!,{r4-r7,r10-fp,lr}

                     257 ;127: {    


                     258 

                     259 ;128:     RptDataSetItem rptDsItem = rptDataSet->firstItem;


                     260 

000000b4 e3a0c000    261 	mov	r12,0

000000b8 e5c0c011    262 	strb	r12,[r0,17]

                     263 ;130:     rptDataSet->hasChanges = false;


                     264 

000000bc e5904004    265 	ldr	r4,[r0,4]

                     266 ;129:     rptDataSet->needsSend = false;


                     267 

000000c0 e5c0c010    268 	strb	r12,[r0,16]

                     269 ;131: 


                     270 ;132:     while(rptDsItem != NULL)


                     271 

000000c4 e3540000    272 	cmp	r4,0

000000c8 0a00001d    273 	beq	.L340

                     274 .L347:

                     275 ;133:     {


                     276 

                     277 ;134:         RptDataSetItem_updateChanges(rptDsItem);


                     278 

                     279 ;64: {


                     280 

                     281 ;65:     RptDataSet rptDataSet = rptDsItem->parentRptDataSet;


                     282 

000000cc e594a000    283 	ldr	r10,[r4]

                     284 ;66:     TrgOps trgOps = (TrgOps)rptDataSet->reporter->rcb.trgOps;


                     285 

000000d0 e5945008    286 	ldr	r5,[r4,8]

                     287 ;68:     while(rptItem != NULL)


                     288 

000000d4 e59a0000    289 	ldr	r0,[r10]

000000d8 e3a07001    290 	mov	r7,1

000000dc e5d0601a    291 	ldrb	r6,[r0,26]

                     292 ;67:     RptItem rptItem = rptDsItem->firstFinalDAItem;


                     293 

000000e0 e3550000    294 	cmp	r5,0


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gu81.s
000000e4 0a000013    295 	beq	.L345

                     296 .L348:

                     297 ;69:     {


                     298 

                     299 ;70:         TrgOps changed = rptItem->iedObj->changed;


                     300 

000000e8 e5950008    301 	ldr	r0,[r5,8]

000000ec e5900028    302 	ldr	r0,[r0,40]

                     303 ;71:         if(changed != TRGOP_NONE)


                     304 

000000f0 e3500000    305 	cmp	r0,0

000000f4 0a00000c    306 	beq	.L353

                     307 ;72:         {            


                     308 

                     309 ;73:             RptDataSetItem rptDsItem = rptItem->rptDsItem;


                     310 

000000f8 e595c00c    311 	ldr	r12,[r5,12]

                     312 ;74:             rptDsItem->changedNew = changed;


                     313 

000000fc e59cb010    314 	ldr	fp,[r12,16]

00000100 e58c0014    315 	str	r0,[r12,20]

                     316 ;75: 


                     317 ;76:             //Если есть конфликтующие изменения, то надо посылать отчёт


                     318 ;77:             if((rptDsItem->changedOld & trgOps)  != TRGOP_NONE


                     319 

00000104 e116000b    320 	tst	r6,fp

00000108 11160000    321 	tstne	r6,r0

                     322 ;78:                 && (changed & trgOps) != TRGOP_NONE)             


                     323 ;79:             {


                     324 

                     325 ;80:                 rptDsItem->needsSend = true;


                     326 

0000010c 15cc7018    327 	strneb	r7,[r12,24]

                     328 ;81:                 rptDataSet->needsSend = true;


                     329 

00000110 15ca7011    330 	strneb	r7,[r10,17]

                     331 ;82:             }


                     332 ;83:             rptItem->changedNew = changed;            


                     333 

00000114 e5850014    334 	str	r0,[r5,20]

                     335 ;84:             rptItem->behaviour->updateChanges(rptItem);


                     336 

00000118 e5950004    337 	ldr	r0,[r5,4]

0000011c e590c000    338 	ldr	r12,[r0]

00000120 e1a00005    339 	mov	r0,r5

00000124 e1a0e00f    340 	mov	lr,pc

00000128 e12fff1c*   341 	bx	r12

                     342 .L353:

                     343 ;85:         }        


                     344 ;86:         rptItem = rptItem->nextFinalDARptItem;


                     345 

0000012c e5955010    346 	ldr	r5,[r5,16]

00000130 e3550000    347 	cmp	r5,0

00000134 1affffeb    348 	bne	.L348

                     349 .L345:

                     350 ;135:         rptDsItem = rptDsItem->next;


                     351 

00000138 e594401c    352 	ldr	r4,[r4,28]

0000013c e3540000    353 	cmp	r4,0

00000140 1affffe1    354 	bne	.L347

                     355 .L340:


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gu81.s
00000144 e8bd8cf0    356 	ldmfd	[sp]!,{r4-r7,r10-fp,pc}

                     357 	.endf	RptDataSet_updateChanges

                     358 	.align	4

                     359 ;rptDsItem	r4	local

                     360 ;rptDataSet	r10	local

                     361 ;trgOps	r6	local

                     362 ;rptItem	r5	local

                     363 ;changed	r0	local

                     364 ;rptDsItem	r12	local

                     365 

                     366 ;rptDataSet	r0	param

                     367 

                     368 	.section ".bss","awb"

                     369 .L441:

                     370 	.data

                     371 	.text

                     372 

                     373 ;136:     }


                     374 ;137: }


                     375 

                     376 ;138: 


                     377 ;139: // Функция копирует новые изменения поверх старых. 


                     378 ;140: // Если если есть конфиликты, то старые данные помещаются в отчёт.


                     379 ;141: bool RptDataSet_flushAndUpdate(RptDataSet rptDataSet, BufferView* outBuf)


                     380 	.align	4

                     381 	.align	4

                     382 RptDataSet_flushAndUpdate::

00000148 e92d44f0    383 	stmfd	[sp]!,{r4-r7,r10,lr}

                     384 ;142: {


                     385 

                     386 ;143:     RptDataSetItem rptDsItem = rptDataSet->firstItem;


                     387 

0000014c e1a06001    388 	mov	r6,r1

00000150 e1a07000    389 	mov	r7,r0

00000154 e5974004    390 	ldr	r4,[r7,4]

                     391 ;144:     while(rptDsItem != NULL)


                     392 

00000158 e3a05000    393 	mov	r5,0

0000015c e3540000    394 	cmp	r4,0

00000160 0a000020    395 	beq	.L476

                     396 .L477:

                     397 ;145:     {        


                     398 

                     399 ;146:         if(rptDsItem->changedNew != TRGOP_NONE)


                     400 

00000164 e5940014    401 	ldr	r0,[r4,20]

00000168 e3500000    402 	cmp	r0,0

0000016c 0a00001a    403 	beq	.L478

                     404 ;147:         {                 


                     405 

                     406 ;148:             RptItem rptItem = rptDsItem->rptItem;            


                     407 

                     408 ;149:             if(rptDsItem->needsSend)


                     409 

00000170 e5d4c018    410 	ldrb	r12,[r4,24]

00000174 e35c0000    411 	cmp	r12,0

00000178 0a000009    412 	beq	.L488

0000017c e5940004    413 	ldr	r0,[r4,4]

                     414 ;150:             {                


                     415 

                     416 ;151:                 rptDsItem->needsSend = false;



                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gu81.s
                     417 

00000180 e5c45018    418 	strb	r5,[r4,24]

00000184 e5901004    419 	ldr	r1,[r0,4]

00000188 e591c004    420 	ldr	r12,[r1,4]

0000018c e1a01006    421 	mov	r1,r6

00000190 e1a0e00f    422 	mov	lr,pc

00000194 e12fff1c*   423 	bx	r12

                     424 ;152:                 if(!rptItem->behaviour->encodeRead(rptItem, outBuf))


                     425 

00000198 e3500000    426 	cmp	r0,0

                     427 ;153:                 {


                     428 

                     429 ;154:                     return false;


                     430 

0000019c 0a000014    431 	beq	.L473

000001a0 ea00000a    432 	b	.L485

                     433 .L488:

                     434 ;155:                 }            


                     435 ;156:             }            


                     436 ;157:             else


                     437 ;158:             {


                     438 

                     439 ;159:                 RptDataSetItem_overwriteOld(rptDsItem);


                     440 

                     441 ;92: {


                     442 

                     443 ;93:     RptItem rptItem = rptDsItem->firstFinalDAItem;


                     444 

000001a4 e594a008    445 	ldr	r10,[r4,8]

                     446 ;94:     while(rptItem != NULL)


                     447 

000001a8 e35a0000    448 	cmp	r10,0

000001ac 0a000007    449 	beq	.L485

                     450 .L489:

                     451 ;95:     {


                     452 

                     453 ;96:         rptItem->behaviour->overwriteOld(rptItem);


                     454 

000001b0 e59a0004    455 	ldr	r0,[r10,4]

000001b4 e590c00c    456 	ldr	r12,[r0,12]

000001b8 e1a0000a    457 	mov	r0,r10

000001bc e1a0e00f    458 	mov	lr,pc

000001c0 e12fff1c*   459 	bx	r12

                     460 ;97:         rptItem = rptItem->nextFinalDARptItem;


                     461 

000001c4 e59aa010    462 	ldr	r10,[r10,16]

000001c8 e35a0000    463 	cmp	r10,0

000001cc 1afffff7    464 	bne	.L489

                     465 .L485:

                     466 ;160: 


                     467 ;161:             }


                     468 ;162:             rptDsItem->changedOld = rptDsItem->changedNew;


                     469 

000001d0 e5940014    470 	ldr	r0,[r4,20]

000001d4 e5845014    471 	str	r5,[r4,20]

000001d8 e5840010    472 	str	r0,[r4,16]

                     473 ;163:             rptDsItem->changedNew = TRGOP_NONE;


                     474 

                     475 .L478:

                     476 ;164:         }        


                     477 ;165:         rptDsItem = rptDsItem->next;



                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gu81.s
                     478 

000001dc e594401c    479 	ldr	r4,[r4,28]

000001e0 e3540000    480 	cmp	r4,0

000001e4 1affffde    481 	bne	.L477

                     482 .L476:

                     483 ;166:     }


                     484 ;167:     rptDataSet->hasChanges = false;


                     485 

000001e8 e5c74010    486 	strb	r4,[r7,16]

                     487 ;168:     rptDataSet->needsSend = false;


                     488 

000001ec e5c75011    489 	strb	r5,[r7,17]

                     490 ;169:     return true;


                     491 

000001f0 e3a00001    492 	mov	r0,1

                     493 .L473:

000001f4 e8bd84f0    494 	ldmfd	[sp]!,{r4-r7,r10,pc}

                     495 	.endf	RptDataSet_flushAndUpdate

                     496 	.align	4

                     497 ;rptDsItem	r4	local

                     498 ;rptItem	r0	local

                     499 ;rptItem	r10	local

                     500 

                     501 ;rptDataSet	r7	param

                     502 ;outBuf	r6	param

                     503 

                     504 	.section ".bss","awb"

                     505 .L616:

                     506 	.data

                     507 	.text

                     508 

                     509 ;170: }


                     510 	.align	4

                     511 

                     512 	.data

                     513 	.ghsnote version,6

                     514 	.ghsnote tools,3

                     515 	.ghsnote options,0

                     516 	.text

                     517 	.align	4

