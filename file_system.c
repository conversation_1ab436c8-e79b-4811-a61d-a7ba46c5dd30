#include "file_system.h"

#include "BaseAsnTypes.h"
#include "local_types.h"
#include "types.h"
#include "bufView.h"
#include "frsm.h"
#include "FS/ConfigFiles.h"
#include "FS/OscFiles.h"
#include <stddef.h>

#define CFG_DIR_NAME "config"
#define OSC_DIR_NAME "osc"

inline bool isDelimiter(char c)
{
    return c == '\\' || c == '/';
}

// Ищет первый разделитель от конца строки к началу
// Если не найден, возвращает FALSE;
static bool findDelimiter(const StringView* fullName, size_t* delimiterPos )
{
    size_t pos = fullName->len;
    while (pos > 0)
    {
        --pos;
        if (isDelimiter(fullName->p[pos]))
        {
            *delimiterPos = pos;
            return TRUE;
        }
    }
    return FALSE;
}

// Удаляет разделитель директориев слева, если есть.
static void stripLeftDelim(StringView* name)
{
    //Срезаем лидирующий разделитель, если есть
    if (name->len > 0 && isDelimiter(name->p[0]))
    {
        StringView_init(name, name->p + 1, name->len - 1);
    }
}

//Разделяет полное имя файла на собственно имя файла и имя директория
//Имена всегда возвращаются без лидирущих разделителей.
static void splitFullFileName(const StringView* fullName,
    StringView* dirName, StringView* fileName)
{
    size_t delimiterPos;
    if (findDelimiter(fullName, &delimiterPos))
    {
        //Разделитель найден
        //Имя файла
        size_t fileNameStart = delimiterPos + 1;
        StringView_init(fileName, fullName->p + fileNameStart,
            fullName->len - fileNameStart);
        //Директорий
        StringView_init(dirName, fullName->p, delimiterPos);
        //Срезаем лидирующий разделитель, если есть
        stripLeftDelim(dirName);
    }
    else
    {
        //Разделитель не найден.
        //Только имя файла. Директорий пустой.
        StringView_init(dirName, fullName->p, 0);
        StringView_init(fileName, fullName->p, fullName->len);
    }
}


bool fs_init(void)
{
    return frsm_init() && CFGFS_init() && OSCFS_init();
}


static FNameErrCode oscFindFirst(StringView* startFileName,
    FSFindData* fileInfo, BufferView* bufForName)
{
    fileInfo->subsystem = FS_SUB_OSC;
    BufferView_writeStr(bufForName, "/" OSC_DIR_NAME "/");
    return OSCFS_findFirst(startFileName, fileInfo, bufForName);
}

FNameErrCode fs_findFirst(StringView* dirName, StringView* startFileName,
    FSFindData* fileInfo, BufferView* bufForName)
{
    FNameErrCode result;

    //dirName без лидирующего разделителя
    StringView strippedDirName;
    StringView_fromStringView(&strippedDirName, dirName);
    stripLeftDelim(&strippedDirName);

    //Если пустое имя директория, значит нужен список корневого директория
    fileInfo->rootDir = (strippedDirName.len == 0);
    if (fileInfo->rootDir
        || StringView_cmpCStr(&strippedDirName, CFG_DIR_NAME) == 0)
    {
        //Поиск в конфигурационных файлах
        //Сохраняем позицию буфера имени для восстановления, если
        //конфигурационного файла не найдено
        size_t oldNamePos = bufForName->pos;
        fileInfo->subsystem = FS_SUB_CFG;
        BufferView_writeStr(bufForName, "/" CFG_DIR_NAME "/");
        result = CFGFS_findFirst(startFileName, fileInfo, bufForName);
        //Если обходим корневоей директорий и ничего не нашли в конфигах,
        //переходим к поиску осциллограмм
        if (result == FNAME_NOT_FOUND && fileInfo->rootDir)
        {
            //восстанавливаем позицию буфера имени
            bufForName->pos = oldNamePos;
            result = oscFindFirst(startFileName, fileInfo, bufForName);
        }
    }
    else if(StringView_cmpCStr(&strippedDirName, OSC_DIR_NAME) == 0)
    {
        //Поиск в осциллограммах
        result = oscFindFirst(startFileName, fileInfo, bufForName);
    }
    else
    {
        result = FNAME_NOT_FOUND;
    }
    if (result == FNAME_OK)
    {
        StringView_init(&fileInfo->fileName, (char*)bufForName->p, bufForName->pos);
    }
    return result;
}

FNameErrCode fs_findNext(FSFindData* fileInfo, BufferView* bufForName)
{
    FNameErrCode result;

    if (fileInfo->subsystem == FS_SUB_CFG)
    {
        //Поиск в конфигурационных файлах
        //Сохраняем позицию буфера имени для восстановления, если
        //конфигурационного файла не найдено
        size_t oldNamePos = bufForName->pos;
        BufferView_writeStr(bufForName, "/" CFG_DIR_NAME "/");
        result = CFGFS_findNext(fileInfo, bufForName);
        //Если обходим корневоей директорий и ничего не нашли в конфигах,
        //переходим к поиску осциллограмм
        if (result == FNAME_NOT_FOUND && fileInfo->rootDir)
        {
            // закрытие поиска, может здесь должен быть вызов CFGFS_findClose(fileInfo)?
            fs_findClose(fileInfo);
            //восстанавливаем позицию буфера имени
            bufForName->pos = oldNamePos;
            result = oscFindFirst(NULL, fileInfo, bufForName);
        }
    }
    else if (fileInfo->subsystem == FS_SUB_OSC)
    {
        BufferView_writeStr(bufForName, "/" OSC_DIR_NAME "/");
        result = OSCFS_findNext(fileInfo, bufForName);
    }
    else
    {
        result = FNAME_NOT_FOUND;
    }
    if (result == FNAME_OK)
    {
        StringView_init(&fileInfo->fileName, (char*)bufForName->p, bufForName->pos);
    }
    return result;
}
void fs_findClose(FSFindData* fileInfo)
{
    if (fileInfo->subsystem == FS_SUB_CFG)
    {
        CFGFS_findClose(fileInfo);
    }
    else if (fileInfo->subsystem == FS_SUB_OSC)
    {
        OSCFS_findClose(fileInfo);
    }
}
static bool fileOpen(StringView* dirName, StringView* fileName, FRSM* frsm,
                       FSFileAttr* attr)
{
        if(StringView_cmpCStr(dirName, CFG_DIR_NAME) == 0 )
        {
            frsm->subsystem = FS_SUB_CFG;
            return CFGFS_openFile(fileName, frsm, attr);
        }
        else if(StringView_cmpCStr(dirName, OSC_DIR_NAME) == 0 )
        {
            frsm->subsystem = FS_SUB_OSC;
            return OSCFS_openFile(fileName, frsm, attr);
        }
        return FALSE;
}

bool fs_fileOpen(StringView* fullFileName, size_t startPos, uint32_t* frsmID,
    FSFileAttr* attr)
{
    StringView dirName;
    StringView fileName;
    FRSM* frsm;

    splitFullFileName(fullFileName, &dirName, &fileName);
    if( ! frsm_alloc(frsmID))
    {
        return FALSE;
    }
    frsm_getById(*frsmID, &frsm);
    if(!fileOpen(&dirName, &fileName, frsm, attr))
    {
        frsm_free(*frsmID);
        return FALSE;
    }
    return TRUE;
}

bool fs_fileClose(uint32_t frsmID)
{
    FRSM* frsm;
    bool result;
    if (!frsm_getById(frsmID, &frsm))
    {
        return FALSE;
    }
    switch (frsm->subsystem)
    {
    case FS_SUB_CFG:
        result = CFGFS_closeFile(frsm);
        break;
    case FS_SUB_OSC:
        result = OSCFS_closeFile(frsm);
        break;
    default:
        result = FALSE;
    }
    if (result)
    {
        frsm_free(frsmID);
    }
    return result;
}

bool fs_fileRead(uint32_t frsmID, BufferView* fileReadBuf, bool *moreFollows)
{
    FRSM* frsm;
    bool result;
    if (!frsm_getById(frsmID, &frsm))
    {
        return FALSE;
    }
    switch (frsm->subsystem)
    {
    case FS_SUB_CFG:
        result = CFGFS_readFile(frsm, fileReadBuf, moreFollows);
        break;
    case FS_SUB_OSC:
        result = OSCFS_readFile(frsm, fileReadBuf, moreFollows);
        break;
    default:
        result = FALSE;
    }
    return result;
}
