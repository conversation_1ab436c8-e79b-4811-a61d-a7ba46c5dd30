#pragma once

#include "mmsconnection.h"

#include <stddef.h>

#define MMS_READ_SPECIFICATION_WITH_RESULT      0x80
#define MMS_READ_VARIABLE_ACCESS_SPECIFICATION  0xa1

#define MMS_READ_LIST_OF_VARIABLE   0xa0
#define MMS_READ_VARIABLE_LIST_NAME 0xa1

#define MMS_READ_ALTERNATE_ACCESS   0xa5

#define ASN_VARIABLE_ACCESS_SPECIFICATION_LIST_OF_VARIABLE  0xa0
#define ASN_VARIABLE_ACCESS_SPECIFICATION_VARIABLE_LIST_NAME  0xa1

#define ASN_VARIABLE_SPECIFICATION_ADDRESS 0xa1
#define ASN_VARIABLE_SPECIFICATION_VARIABLE_DESCRIPTION 0xa2

#define MMS_READ_RESPONSE_VARIABLES 0xa0
#define MMS_READ_RESPONSE_DATA 0xa1

int mms_handleReadRequest(MmsConnection* mmsConn,
        unsigned char* inBuf, int bufPos, int maxBufPos, unsigned int invokeId,
        unsigned char* response, size_t maxRespSize);
