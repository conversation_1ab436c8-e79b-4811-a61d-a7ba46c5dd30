#include "BERCoder.h"


size_t BERCoder_calcIntEncodedLen(const void* pValue, size_t intSize)
{
    // Функция работает в big endian. То есть начинаем с последнего
    // байта, он же - старший
    const uint8_t* pValueBytes = ((const uint8_t*)pValue) + (intSize - 1);
    size_t i;
    uint8_t byte = *pValueBytes;

    // Пропускаем лидирующие знаковые байты. Цикл по старшим 7 байтам
    for (i = 0; i < (intSize - 1); i++)
    {
        uint8_t nextByte = pValueBytes[-1];
        uint8_t nextBit = nextByte & 0x80;

        // Check if current byte is not purely a sign extension
        // (0xFF for negative numbers or 0x00 for positive numbers)
        if (!(byte == 0xFF && nextBit || byte == 0x00 && !nextBit))
        {
            break;
        }
        byte = nextByte;
        pValueBytes--;
    }

    // Return the minimum number of bytes needed to represent the value
    return intSize - i;
}

void BERCoder_reverseCopy(const void* src, uint8_t* dst, size_t len)
{    
    size_t i;
    const uint8_t* pSrc = src;
    pSrc += len - 1;
    
    for (i = 0; i < len; i++)
    {
        *dst++ = *pSrc--;
    }
}
