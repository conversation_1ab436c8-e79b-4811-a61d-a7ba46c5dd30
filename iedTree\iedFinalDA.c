#include "iedFinalDA.h"

#include "iedControlModel.h"


#include "iedQuality.h"
#include "iedFloat.h"
#include "iedInt.h"
#include "iedUInt.h"
#include "iedEnum.h"
#include "iedConstDA.h"
#include "../mms_data.h"
#include "../mms_rcb.h"
#include "../mms_gocb.h"
#include "../mms_write.h"
#include "../DataSlice.h"

#include "IEDCompile/AccessInfo.h"
#include "IEDCompile/InnerAttributeTypes.h"

#include "../iedmodel.h"

#include "../BaseAsnTypes.h"

//!!! Для отладки
int settCounter = 0;
void incSettCounter()
{
    settCounter++;
}


//==================Инициализация TerminalItemDA==============


// Функция инициализирует специфические поля для Terminal Item DA.
// ber указывает на AccessInfo до выравнивания.
// По завершению функции состояние ber не сохраняется.
static bool initTermItemDA(IEDEntity entity, BufferView* ber)
{
    TerminalItem* extInfo = IEDEntity_alloc(sizeof(TerminalItem));
    if(extInfo == NULL)
    {
        return false;
    }
    entity->extInfo = extInfo;

    if(!IEDModel_getTermItemDescrStruct(ber, &extInfo->accessInfo))
    {
        return false;
    }

    switch(entity->subType)
    {
        case INNER_TYPE_QUALITY:
            IEDQuality_init(entity);
            break;
        case INNER_TYPE_FLOAT_VALUE:
            IEDFloat_init(entity);
            break;
        case INNER_TYPE_REAL_VALUE:
            IEDReal_init(entity);
            break;
        case INNER_TYPE_INT32:
            IEDInt32_init(entity);
            break;
        case INNER_TYPE_INT32U:
            IEDUInt32_init(entity);
            break;
        case INNER_TYPE_INT8U:
            ERROR_REPORT("INNER_TYPE_INT8U values not implemented");
            break;
        case INNER_TYPE_ENUMERATED:
            IEDEnum_init(entity);
            break;
        case INNER_TYPE_BOOLEAN:
            IEDBool_init(entity);
            break;
        case INNER_TYPE_CODEDENUM:
            IEDCodedEnum_init(entity);
            break;
        case INNER_TYPE_REAL_AS_INT64:
            IEDRealAsInt64_init(entity);
            break;

        //Если неизвестный тип, то останется функция-заглушка

        /*!!!
        Это код для подсчёта уставок. Нужен для разработки DataSlice для уставок.
        Для нормальной работы не нужен.

        case INNER_TYPE_REAL_SETT:
        case INNER_TYPE_INT32_SETTS:
        case INNER_TYPE_ENUMERATED_SETTS:
        case INNER_TYPE_INT32U_SETTS:
        case INNER_TYPE_FLOAT_SETT:
            incSettCounter();


            break;
        */

    }

    return true;
}

static bool initTimeStampDA(IEDEntity entity)
{
    TimeStamp* extInfo;
    entity->type = IED_ENTITY_DA_TIMESTAMP;
    extInfo = IEDEntity_alloc(sizeof(TimeStamp));
    if(extInfo == NULL)
    {
        return false;
    }
    entity->extInfo = extInfo;
    extInfo->timeStamp = dataSliceGetTimeStamp();
    return true;
}

bool IEDFinalDA_init(IEDEntity entity)
{
    enum InnerAttributeType attrType;
    BufferView ber = entity->ber;


    //Пропустить тэг и длину
    BufferView_decodeTL(&ber, NULL, NULL, NULL);

    if(!IEDModel_skipServiceInfo(&ber))
    {
        return false;
    }
    if(!BufferView_decodeUInt32TL(&ber, ASN_INTEGER, (uint32_t*)&attrType))
    {
        ERROR_REPORT("Error decoding attribute type");
        return false;
    }

    if(attrType == INNER_TYPE_TIME_STAMP)
    {
        // ----- t
        return initTimeStampDA(entity);
    }
    else if(attrType == INNER_TYPE_CONST)
    {
        // ----- const
        return IEDConstDA_init(entity, &ber);        
    }
    else
    {
        // ----- Всё остальное

        entity->type = IED_ENTITY_DA_TERMINAL_ITEM;
        entity->subType = attrType;
        return initTermItemDA(entity, &ber);
    }
}

bool IEDVarDA_calcReadLen(IEDEntity entity, size_t *pLen)
{
    if(entity->type != IED_ENTITY_DA_VAR)
    {
        return false;
    }
    switch(entity->subType)
    {
    case DA_SUBTYPE_ORIDENT:
        return IEDOrIdent_calcReadLen(entity, pLen);
    case DA_SUBTYPE_ORCAT:
        return IEDOrCat_calcReadLen(entity, pLen);
    default:
        return false;
    }
}

bool IEDVarDA_encodeRead(IEDEntity entity, BufferView *outBuf)
{
    if(entity->type != IED_ENTITY_DA_VAR)
    {
        return false;
    }
    switch(entity->subType)
    {
    case DA_SUBTYPE_ORIDENT:
        return IEDOrIdent_encodeRead(entity, outBuf);
    case DA_SUBTYPE_ORCAT:
        return IEDOrCat_encodeRead(entity, outBuf);
    default:
        return false;
    }
}

MmsDataAccessError IEDVarDA_write(IEDEntity entity,
                                           IsoConnection* isoConn, BufferView* value)
{
    switch (entity->subType)
    {
    case DA_SUBTYPE_ORIDENT:
        return IEDOrIdent_write(entity, isoConn, value);
    case DA_SUBTYPE_ORCAT:
        return IEDOrCat_write(entity, isoConn, value);
    default:
        ERROR_REPORT("Invalid object to write");
        return DATA_ACCESS_ERROR_OBJECT_ACCESS_UNSUPPORTED;
    }
}

void* IEDTermItemDA_getTerminalItemDescr(IEDEntity entity)
{
    TerminalItem* extInfo;
    if(entity->type != IED_ENTITY_DA_TERMINAL_ITEM)
    {
        return NULL;
    }
    extInfo = entity->extInfo;
    return extInfo->accessInfo;
}

bool IEDTermItemDA_calcReadLen(IEDEntity entity, size_t *pLen)
{
    void* pDescrStruct;
    int dataLen;
    TerminalItem* termItem;

    if(entity->type != IED_ENTITY_DA_TERMINAL_ITEM
            || entity->extInfo == NULL)
    {
        ERROR_REPORT("Invalid terminal item DA");
        return false;
    }

    termItem = entity->extInfo;
    pDescrStruct = termItem->accessInfo;
    switch (entity->subType)
    {
    case INNER_TYPE_INT8U:
        dataLen = encodeReadInt32U(NULL, 0, pDescrStruct, true);
        break;
    case INNER_TYPE_RCB:
        dataLen = encodeReadAttrRCB(NULL, 0, pDescrStruct, true);
        break;
    case INNER_TYPE_FLOAT_SETT:
        dataLen = encodeReadFloatSett(NULL, 0, pDescrStruct, true);
        break;
    case INNER_TYPE_REAL_SETT:
        dataLen = encodeReadRealSett(NULL, 0, pDescrStruct, true);
        break;
    case INNER_TYPE_INT32_SETTS:
    case INNER_TYPE_ENUMERATED_SETTS:
        dataLen = encodeReadInt32Sett(NULL, 0, pDescrStruct, true);
        break;
    case INNER_TYPE_INT32U_SETTS:
        dataLen = encodeReadInt32USett(NULL, 0, pDescrStruct, true);
        break;
    case INNER_TYPE_GOCB:
        dataLen = encodeReadAttrGoCB(NULL, 0, pDescrStruct, true);
        break;
    default:
        ERROR_REPORT("Unsupported data type");
        return false;
    }
    if(dataLen <=0)
    {
        ERROR_REPORT("Invalid read length");
        return false;
    }
    *pLen = dataLen;
    return true;
}

bool IEDTermItemDA_encodeRead(IEDEntity entity, BufferView *outBufView)
{
    void* pDescrStruct;
    uint8_t *writeBuf = outBufView->p + outBufView->pos;
    int dataLen;
    TerminalItem* termItem;

    if(entity->type != IED_ENTITY_DA_TERMINAL_ITEM
            || entity->extInfo == NULL)
    {
        ERROR_REPORT("Invalid terminal item DA");
        return false;
    }

    termItem = entity->extInfo;

    pDescrStruct = termItem->accessInfo;
    switch (entity->subType)
    {
    case INNER_TYPE_INT8U:
        dataLen = encodeReadInt32U(writeBuf, 0, pDescrStruct, false);
        break;
    case INNER_TYPE_RCB:
        dataLen = encodeReadAttrRCB(writeBuf, 0, pDescrStruct, false);
        break;
    case INNER_TYPE_FLOAT_SETT:
        dataLen = encodeReadFloatSett(writeBuf, 0, pDescrStruct, false);
        break;
    case INNER_TYPE_REAL_SETT:
        dataLen = encodeReadRealSett(writeBuf, 0, pDescrStruct, false);
        break;
    case INNER_TYPE_INT32_SETTS:
    case INNER_TYPE_ENUMERATED_SETTS:
        dataLen = encodeReadInt32Sett(writeBuf, 0, pDescrStruct, false);
        break;
    case INNER_TYPE_INT32U_SETTS:
        dataLen = encodeReadInt32USett(writeBuf, 0, pDescrStruct, false);
        break;
    case INNER_TYPE_GOCB:
        dataLen = encodeReadAttrGoCB(writeBuf, 0, pDescrStruct, false);
        break;
    default:
        ERROR_REPORT("Unsupported data type");
        return false;
    }
    if(dataLen <=0)
    {
        ERROR_REPORT("Invalid read length");
        return false;
    }

    if(!BufferView_advance(outBufView, dataLen))
    {
        ERROR_REPORT("Buffer overflow");
        return false;
    }
    return true;
}

MmsDataAccessError IEDTermItemDA_write(IEDEntity entity,
                                           IsoConnection* isoConn, BufferView* value)
{
    TerminalItem* termItem = entity->extInfo;
    void *pDescrStruct = termItem->accessInfo;
    size_t len;
    uint8_t *pValue;

    if(entity->type != IED_ENTITY_DA_TERMINAL_ITEM)
    {
        return DATA_ACCESS_ERROR_TYPE_INCONSISTENT;
    }
    if(BufferView_endOfBuf(value))
    {
        ERROR_REPORT("Empty write value");
        return DATA_ACCESS_ERROR_OBJECT_VALUE_INVALID;
    }
    pValue = value->p + value->pos;
    if(!BufferView_decodeTL(value, NULL, &len, NULL))
    {
        ERROR_REPORT("Invalid write value");
        return DATA_ACCESS_ERROR_OBJECT_VALUE_INVALID;
    }
    if(!BufferView_advance(value, len))
    {
        ERROR_REPORT("Invalid write value");
        return DATA_ACCESS_ERROR_OBJECT_VALUE_INVALID;
    }
    switch (entity->subType)
    {
    case INNER_TYPE_RCB:
        writeAttrRCB(isoConn, pDescrStruct, pValue);
        break;
    case INNER_TYPE_GOCB:
        writeAttrGoCB(pDescrStruct, pValue);
        break;
    case INNER_TYPE_BOOLEAN:
        writeBoolean(pDescrStruct, pValue);
        break;
    case INNER_TYPE_CODEDENUM:
        writeCodedEnum(pDescrStruct, pValue);
        break;
    case INNER_TYPE_FLOAT_SETT:
        return writeFloatSett(pDescrStruct, pValue);
    case INNER_TYPE_REAL_SETT:
        return writeRealSett(pDescrStruct, pValue);        
    case INNER_TYPE_INT32_SETTS:
    case INNER_TYPE_INT32U_SETTS:
    case INNER_TYPE_ENUMERATED_SETTS:
        return writeIntSett(pDescrStruct, pValue);        
    default:
        ERROR_REPORT("Write to unsupported type %d ignored", entity->subType);
        //Для неподдерживаемых типов имитируем успешную запись
    }
    return DATA_ACCESS_ERROR_SUCCESS;
}
