                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=tlsf.c -o tlsf\gh_gho1.o -list=tlsf/tlsf.lst C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
Source File: tlsf.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile tlsf/tlsf.c -o

                      10 ;		tlsf/tlsf.o

                      11 ;Source File:   tlsf/tlsf.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:55:57 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include <assert.h>


                      21 ;2: #include <limits.h>


                      22 ;3: #include <stddef.h>


                      23 ;4: #include <stdio.h>


                      24 ;5: #include <stdlib.h>


                      25 ;6: #include <string.h>


                      26 ;7: 


                      27 ;8: #include "tlsf.h"


                      28 ;9: 


                      29 ;10: #if defined(__cplusplus)


                      30 ;11: #define tlsf_decl inline


                      31 ;12: #else


                      32 ;13: #define tlsf_decl static


                      33 ;14: #endif


                      34 ;15: 


                      35 ;16: /*


                      36 ;17: ** Architecture-specific bit manipulation routines.


                      37 ;18: **


                      38 ;19: ** TLSF achieves O(1) cost for malloc and free operations by limiting


                      39 ;20: ** the search for a free block to a free list of guaranteed size


                      40 ;21: ** adequate to fulfill the request, combined with efficient free list


                      41 ;22: ** queries using bitmasks and architecture-specific bit-manipulation


                      42 ;23: ** routines.


                      43 ;24: **


                      44 ;25: ** Most modern processors provide instructions to count leading zeroes


                      45 ;26: ** in a word, find the lowest and highest set bit, etc. These


                      46 ;27: ** specific implementations will be used when available, falling back


                      47 ;28: ** to a reasonably efficient generic implementation.


                      48 ;29: **


                      49 ;30: ** NOTE: TLSF spec relies on ffs/fls returning value 0..31.


                      50 ;31: ** ffs/fls return 1-32 by default, returning 0 for error.



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
                      51 ;32: */


                      52 ;33: 


                      53 ;34: /*


                      54 ;35: ** Detect whether or not we are building for a 32- or 64-bit (LP/LLP)


                      55 ;36: ** architecture. There is no reliable portable method at compile-time.


                      56 ;37: */


                      57 ;38: #if defined (__alpha__) || defined (__ia64__) || defined (__x86_64__) \


                      58 ;39: 	|| defined (_WIN64) || defined (__LP64__) || defined (__LLP64__)


                      59 ;40: #define TLSF_64BIT


                      60 ;41: #endif


                      61 ;42: 


                      62 ;43: /*


                      63 ;44: ** gcc 3.4 and above have builtin support, specialized for architecture.


                      64 ;45: ** Some compilers masquerade as gcc; patchlevel test filters them out.


                      65 ;46: */


                      66 ;47: #if defined (__GNUC__) && (__GNUC__ > 3 || (__GNUC__ == 3 && __GNUC_MINOR__ >= 4)) \


                      67 ;48: 	&& defined (__GNUC_PATCHLEVEL__)


                      68 ;49: 


                      69 ;50: #if defined (__SNC__)


                      70 ;51: /* SNC for Playstation 3. */


                      71 ;52: 


                      72 ;53: tlsf_decl int tlsf_ffs(unsigned int word)


                      73 ;54: {


                      74 ;55: 	const unsigned int reverse = word & (~word + 1);


                      75 ;56: 	const int bit = 32 - __builtin_clz(reverse);


                      76 ;57: 	return bit - 1;


                      77 ;58: }


                      78 ;59: 


                      79 ;60: #else


                      80 ;61: 


                      81 ;62: tlsf_decl int tlsf_ffs(unsigned int word)


                      82 ;63: {


                      83 ;64: 	return __builtin_ffs(word) - 1;


                      84 ;65: }


                      85 ;66: 


                      86 ;67: #endif


                      87 ;68: 


                      88 ;69: tlsf_decl int tlsf_fls(unsigned int word)


                      89 ;70: {


                      90 ;71: 	const int bit = word ? 32 - __builtin_clz(word) : 0;


                      91 ;72: 	return bit - 1;


                      92 ;73: }


                      93 ;74: 


                      94 ;75: #elif defined (_MSC_VER) && (_MSC_VER >= 1400) && (defined (_M_IX86) || defined (_M_X64))


                      95 ;76: /* Microsoft Visual C++ support on x86/X64 architectures. */


                      96 ;77: 


                      97 ;78: #include <intrin.h>


                      98 ;79: 


                      99 ;80: #pragma intrinsic(_BitScanReverse)


                     100 ;81: #pragma intrinsic(_BitScanForward)


                     101 ;82: 


                     102 ;83: tlsf_decl int tlsf_fls(unsigned int word)


                     103 ;84: {


                     104 ;85: 	unsigned long index;


                     105 ;86: 	return _BitScanReverse(&index, word) ? index : -1;


                     106 ;87: }


                     107 ;88: 


                     108 ;89: tlsf_decl int tlsf_ffs(unsigned int word)


                     109 ;90: {


                     110 ;91: 	unsigned long index;


                     111 ;92: 	return _BitScanForward(&index, word) ? index : -1;



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
                     112 ;93: }


                     113 ;94: 


                     114 ;95: #elif defined (_MSC_VER) && defined (_M_PPC)


                     115 ;96: /* Microsoft Visual C++ support on PowerPC architectures. */


                     116 ;97: 


                     117 ;98: #include <ppcintrinsics.h>


                     118 ;99: 


                     119 ;100: tlsf_decl int tlsf_fls(unsigned int word)


                     120 ;101: {


                     121 ;102: 	const int bit = 32 - _CountLeadingZeros(word);


                     122 ;103: 	return bit - 1;


                     123 ;104: }


                     124 ;105: 


                     125 ;106: tlsf_decl int tlsf_ffs(unsigned int word)


                     126 ;107: {


                     127 ;108: 	const unsigned int reverse = word & (~word + 1);


                     128 ;109: 	const int bit = 32 - _CountLeadingZeros(reverse);


                     129 ;110: 	return bit - 1;


                     130 ;111: }


                     131 ;112: 


                     132 ;113: #elif defined (__ARMCC_VERSION)


                     133 ;114: /* RealView Compilation Tools for ARM */


                     134 ;115: 


                     135 ;116: tlsf_decl int tlsf_ffs(unsigned int word)


                     136 ;117: {


                     137 ;118: 	const unsigned int reverse = word & (~word + 1);


                     138 ;119: 	const int bit = 32 - __clz(reverse);


                     139 ;120: 	return bit - 1;


                     140 ;121: }


                     141 ;122: 


                     142 ;123: tlsf_decl int tlsf_fls(unsigned int word)


                     143 ;124: {


                     144 ;125: 	const int bit = word ? 32 - __clz(word) : 0;


                     145 ;126: 	return bit - 1;


                     146 ;127: }


                     147 ;128: 


                     148 ;129: #elif defined (__ghs__)


                     149 ;130: /* Green Hills support for PowerPC */


                     150 ;131: 


                     151 ;132: #define tlsf_assert(p)


                     152 ;133: #define printf(p,...)


                     153 ;134: unsigned int __CLZ32(unsigned int Rm); // arm_ghs.h


                     154 ;135: //#include <ppc_ghs.h>


                     155 ;136: 


                     156 ;137: tlsf_decl int tlsf_ffs(unsigned int word)


                     157 ;138: {


                     158 ;139: 	const unsigned int reverse = word & (~word + 1);


                     159 ;140: 	const int bit = 32 - __CLZ32(reverse);


                     160 ;141: 	return bit - 1;


                     161 ;142: }


                     162 ;143: 


                     163 ;144: tlsf_decl int tlsf_fls(unsigned int word)


                     164 ;145: {


                     165 ;146: 	const int bit = word ? 32 - __CLZ32(word) : 0;


                     166 ;147: 	return bit - 1;


                     167 ;148: }


                     168 ;149: 


                     169 ;150: #else


                     170 ;151: /* Fall back to generic implementation. */


                     171 ;152: 


                     172 ;153: tlsf_decl int tlsf_fls_generic(unsigned int word)



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
                     173 ;154: {


                     174 ;155: 	int bit = 32;


                     175 ;156: 


                     176 ;157: 	if (!word) bit -= 1;


                     177 ;158: 	if (!(word & 0xffff0000)) { word <<= 16; bit -= 16; }


                     178 ;159: 	if (!(word & 0xff000000)) { word <<= 8; bit -= 8; }


                     179 ;160: 	if (!(word & 0xf0000000)) { word <<= 4; bit -= 4; }


                     180 ;161: 	if (!(word & 0xc0000000)) { word <<= 2; bit -= 2; }


                     181 ;162: 	if (!(word & 0x80000000)) { word <<= 1; bit -= 1; }


                     182 ;163: 


                     183 ;164: 	return bit;


                     184 ;165: }


                     185 ;166: 


                     186 ;167: /* Implement ffs in terms of fls. */


                     187 ;168: tlsf_decl int tlsf_ffs(unsigned int word)


                     188 ;169: {


                     189 ;170: 	return tlsf_fls_generic(word & (~word + 1)) - 1;


                     190 ;171: }


                     191 ;172: 


                     192 ;173: tlsf_decl int tlsf_fls(unsigned int word)


                     193 ;174: {


                     194 ;175: 	return tlsf_fls_generic(word) - 1;


                     195 ;176: }


                     196 ;177: 


                     197 ;178: #endif


                     198 ;179: 


                     199 ;180: /* Possibly 64-bit version of tlsf_fls. */


                     200 ;181: #if defined (TLSF_64BIT)


                     201 ;182: tlsf_decl int tlsf_fls_sizet(size_t size)


                     202 ;183: {


                     203 ;184: 	int high = (int)(size >> 32);


                     204 ;185: 	int bits = 0;


                     205 ;186: 	if (high)


                     206 ;187: 	{


                     207 ;188: 		bits = 32 + tlsf_fls(high);


                     208 ;189: 	}


                     209 ;190: 	else


                     210 ;191: 	{


                     211 ;192: 		bits = tlsf_fls((int)size & 0xffffffff);


                     212 ;193: 


                     213 ;194: 	}


                     214 ;195: 	return bits;


                     215 ;196: }


                     216 ;197: #else


                     217 ;198: #define tlsf_fls_sizet tlsf_fls


                     218 ;199: #endif


                     219 ;200: 


                     220 ;201: #undef tlsf_decl


                     221 ;202: 


                     222 ;203: /*


                     223 ;204: ** Constants.


                     224 ;205: */


                     225 ;206: 


                     226 ;207: /* Public constants: may be modified. */


                     227 ;208: enum tlsf_public


                     228 ;209: {


                     229 ;210: 	/* log2 of number of linear subdivisions of block sizes. Larger


                     230 ;211: 	** values require more memory in the control structure. Values of


                     231 ;212: 	** 4 or 5 are typical.


                     232 ;213: 	*/


                     233 ;214: 	SL_INDEX_COUNT_LOG2 = 5,



                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
                     234 ;215: };


                     235 ;216: 


                     236 ;217: /* Private constants: do not modify. */


                     237 ;218: enum tlsf_private


                     238 ;219: {


                     239 ;220: #if defined (TLSF_64BIT)


                     240 ;221: 	/* All allocation sizes and addresses are aligned to 8 bytes. */


                     241 ;222: 	ALIGN_SIZE_LOG2 = 3,


                     242 ;223: #else


                     243 ;224: 	/* All allocation sizes and addresses are aligned to 4 bytes. */


                     244 ;225: 	ALIGN_SIZE_LOG2 = 2,


                     245 ;226: #endif


                     246 ;227: 	ALIGN_SIZE = (1 << ALIGN_SIZE_LOG2),


                     247 ;228: 


                     248 ;229: 	/*


                     249 ;230: 	** We support allocations of sizes up to (1 << FL_INDEX_MAX) bits.


                     250 ;231: 	** However, because we linearly subdivide the second-level lists, and


                     251 ;232: 	** our minimum size granularity is 4 bytes, it doesn't make sense to


                     252 ;233: 	** create first-level lists for sizes smaller than SL_INDEX_COUNT * 4,


                     253 ;234: 	** or (1 << (SL_INDEX_COUNT_LOG2 + 2)) bytes, as there we will be


                     254 ;235: 	** trying to split size ranges into more slots than we have available.


                     255 ;236: 	** Instead, we calculate the minimum threshold size, and place all


                     256 ;237: 	** blocks below that size into the 0th first-level list.


                     257 ;238: 	*/


                     258 ;239: 


                     259 ;240: #if defined (TLSF_64BIT)


                     260 ;241: 	/*


                     261 ;242: 	** TODO: We can increase this to support larger sizes, at the expense


                     262 ;243: 	** of more overhead in the TLSF structure.


                     263 ;244: 	*/


                     264 ;245: 	FL_INDEX_MAX = 32,


                     265 ;246: #else


                     266 ;247: 	FL_INDEX_MAX = 30,


                     267 ;248: #endif


                     268 ;249: 	SL_INDEX_COUNT = (1 << SL_INDEX_COUNT_LOG2),


                     269 ;250: 	FL_INDEX_SHIFT = (SL_INDEX_COUNT_LOG2 + ALIGN_SIZE_LOG2),


                     270 ;251: 	FL_INDEX_COUNT = (FL_INDEX_MAX - FL_INDEX_SHIFT + 1),


                     271 ;252: 


                     272 ;253: 	SMALL_BLOCK_SIZE = (1 << FL_INDEX_SHIFT),


                     273 ;254: };


                     274 ;255: 


                     275 ;256: /*


                     276 ;257: ** Cast and min/max macros.


                     277 ;258: */


                     278 ;259: 


                     279 ;260: #define tlsf_cast(t, exp)	((t) (exp))


                     280 ;261: #define tlsf_min(a, b)		((a) < (b) ? (a) : (b))


                     281 ;262: #define tlsf_max(a, b)		((a) > (b) ? (a) : (b))


                     282 ;263: 


                     283 ;264: /*


                     284 ;265: ** Set assert macro, if it has not been provided by the user.


                     285 ;266: */


                     286 ;267: #if !defined (tlsf_assert)


                     287 ;268: #define tlsf_assert assert


                     288 ;269: #endif


                     289 ;270: 


                     290 ;271: /*


                     291 ;272: ** Static assertion mechanism.


                     292 ;273: */


                     293 ;274: 


                     294 ;275: #define _tlsf_glue2(x, y) x ## y



                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
                     295 ;276: #define _tlsf_glue(x, y) _tlsf_glue2(x, y)


                     296 ;277: #define tlsf_static_assert(exp) \


                     297 ;278: 	typedef char _tlsf_glue(static_assert, __LINE__) [(exp) ? 1 : -1]


                     298 ;279: 


                     299 ;280: /* This code has been tested on 32- and 64-bit (LP/LLP) architectures. */


                     300 ;281: tlsf_static_assert(sizeof(int) * CHAR_BIT == 32);


                     301 ;282: tlsf_static_assert(sizeof(size_t) * CHAR_BIT >= 32);


                     302 ;283: tlsf_static_assert(sizeof(size_t) * CHAR_BIT <= 64);


                     303 ;284: 


                     304 ;285: /* SL_INDEX_COUNT must be <= number of bits in sl_bitmap's storage type. */


                     305 ;286: tlsf_static_assert(sizeof(unsigned int) * CHAR_BIT >= SL_INDEX_COUNT);


                     306 ;287: 


                     307 ;288: /* Ensure we've properly tuned our sizes. */


                     308 ;289: tlsf_static_assert(ALIGN_SIZE == SMALL_BLOCK_SIZE / SL_INDEX_COUNT);


                     309 ;290: 


                     310 ;291: /*


                     311 ;292: ** Data structures and associated constants.


                     312 ;293: */


                     313 ;294: 


                     314 ;295: /*


                     315 ;296: ** Block header structure.


                     316 ;297: **


                     317 ;298: ** There are several implementation subtleties involved:


                     318 ;299: ** - The prev_phys_block field is only valid if the previous block is free.


                     319 ;300: ** - The prev_phys_block field is actually stored at the end of the


                     320 ;301: **   previous block. It appears at the beginning of this structure only to


                     321 ;302: **   simplify the implementation.


                     322 ;303: ** - The next_free / prev_free fields are only valid if the block is free.


                     323 ;304: */


                     324 ;305: typedef struct block_header_t


                     325 ;306: {


                     326 ;307: 	/* Points to the previous physical block. */


                     327 ;308: 	struct block_header_t* prev_phys_block;


                     328 ;309: 


                     329 ;310: 	/* The size of this block, excluding the block header. */


                     330 ;311: 	size_t size;


                     331 ;312: 


                     332 ;313: 	/* Next and previous free blocks. */


                     333 ;314: 	struct block_header_t* next_free;


                     334 ;315: 	struct block_header_t* prev_free;


                     335 ;316: } block_header_t;


                     336 ;317: 


                     337 ;318: /*


                     338 ;319: ** Since block sizes are always at least a multiple of 4, the two least


                     339 ;320: ** significant bits of the size field are used to store the block status:


                     340 ;321: ** - bit 0: whether block is busy or free


                     341 ;322: ** - bit 1: whether previous block is busy or free


                     342 ;323: */


                     343 ;324: static const size_t block_header_free_bit = 1 << 0;


                     344 ;325: static const size_t block_header_prev_free_bit = 1 << 1;


                     345 ;326: 


                     346 ;327: /*


                     347 ;328: ** The size of the block header exposed to used blocks is the size field.


                     348 ;329: ** The prev_phys_block field is stored *inside* the previous free block.


                     349 ;330: */


                     350 ;331: static const size_t block_header_overhead = sizeof(size_t);


                     351 ;332: 


                     352 ;333: /* User data starts directly after the size field in a used block. */


                     353 ;334: static const size_t block_start_offset =


                     354 ;335: 	offsetof(block_header_t, size) + sizeof(size_t);


                     355 ;336: 



                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
                     356 ;337: /*


                     357 ;338: ** A free block must be large enough to store its header minus the size of


                     358 ;339: ** the prev_phys_block field, and no larger than the number of addressable


                     359 ;340: ** bits for FL_INDEX.


                     360 ;341: */


                     361 ;342: static const size_t block_size_min = 


                     362 ;343: 	sizeof(block_header_t) - sizeof(block_header_t*);


                     363 ;344: static const size_t block_size_max = tlsf_cast(size_t, 1) << FL_INDEX_MAX;


                     364 ;345: 


                     365 ;346: 


                     366 ;347: /* The TLSF control structure. */


                     367 ;348: typedef struct control_t


                     368 ;349: {


                     369 ;350: 	/* Empty lists point at this block to indicate they are free. */


                     370 ;351: 	block_header_t block_null;


                     371 ;352: 


                     372 ;353: 	/* Bitmaps for free lists. */


                     373 ;354: 	unsigned int fl_bitmap;


                     374 ;355: 	unsigned int sl_bitmap[FL_INDEX_COUNT];


                     375 ;356: 


                     376 ;357: 	/* Head of free lists. */


                     377 ;358: 	block_header_t* blocks[FL_INDEX_COUNT][SL_INDEX_COUNT];


                     378 ;359: } control_t;


                     379 ;360: 


                     380 ;361: /* A type used for casting when doing pointer arithmetic. */


                     381 ;362: typedef ptrdiff_t tlsfptr_t;


                     382 ;363: 


                     383 ;364: /*


                     384 ;365: ** block_header_t member functions.


                     385 ;366: */


                     386 ;367: 


                     387 ;368: static size_t block_size(const block_header_t* block)


                     388 ;369: {


                     389 ;370: 	return block->size & ~(block_header_free_bit | block_header_prev_free_bit);


                     390 ;371: }


                     391 ;372: 


                     392 ;373: static void block_set_size(block_header_t* block, size_t size)


                     393 ;374: {


                     394 ;375: 	const size_t oldsize = block->size;


                     395 ;376: 	block->size = size | (oldsize & (block_header_free_bit | block_header_prev_free_bit));


                     396 ;377: }


                     397 ;378: 


                     398 ;379: static int block_is_last(const block_header_t* block)


                     399 

                     400 ;382: }


                     401 

                     402 ;383: 


                     403 ;384: static int block_is_free(const block_header_t* block)


                     404 ;385: {


                     405 ;386: 	return tlsf_cast(int, block->size & block_header_free_bit);


                     406 ;387: }


                     407 ;388: 


                     408 ;389: static void block_set_free(block_header_t* block)


                     409 ;390: {


                     410 ;391: 	block->size |= block_header_free_bit;


                     411 ;392: }


                     412 ;393: 


                     413 ;394: static void block_set_used(block_header_t* block)


                     414 ;395: {


                     415 ;396: 	block->size &= ~block_header_free_bit;


                     416 ;397: }



                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
                     417 ;398: 


                     418 ;399: static int block_is_prev_free(const block_header_t* block)


                     419 ;400: {


                     420 ;401: 	return tlsf_cast(int, block->size & block_header_prev_free_bit);


                     421 ;402: }


                     422 ;403: 


                     423 ;404: static void block_set_prev_free(block_header_t* block)


                     424 ;405: {


                     425 ;406: 	block->size |= block_header_prev_free_bit;


                     426 ;407: }


                     427 ;408: 


                     428 ;409: static void block_set_prev_used(block_header_t* block)


                     429 ;410: {


                     430 ;411: 	block->size &= ~block_header_prev_free_bit;


                     431 ;412: }


                     432 ;413: 


                     433 ;414: static block_header_t* block_from_ptr(const void* ptr)


                     434 ;415: {


                     435 ;416: 	return tlsf_cast(block_header_t*,


                     436 ;417: 		tlsf_cast(unsigned char*, ptr) - block_start_offset);


                     437 ;418: }


                     438 ;419: 


                     439 ;420: static void* block_to_ptr(const block_header_t* block)


                     440 ;421: {


                     441 ;422: 	return tlsf_cast(void*,


                     442 ;423: 		tlsf_cast(unsigned char*, block) + block_start_offset);


                     443 ;424: }


                     444 ;425: 


                     445 ;426: /* Return location of next block after block of given size. */


                     446 ;427: static block_header_t* offset_to_block(const void* ptr, size_t size)


                     447 ;428: {


                     448 ;429: 	return tlsf_cast(block_header_t*, tlsf_cast(tlsfptr_t, ptr) + size);


                     449 ;430: }


                     450 ;431: 


                     451 ;432: /* Return location of previous block. */


                     452 ;433: static block_header_t* block_prev(const block_header_t* block)


                     453 

                     454 ;437: }


                     455 

                     456 ;438: 


                     457 ;439: /* Return location of next existing block. */


                     458 ;440: static block_header_t* block_next(const block_header_t* block)


                     459 ;441: {


                     460 ;442: 	block_header_t* next = offset_to_block(block_to_ptr(block),


                     461 ;443: 		block_size(block) - block_header_overhead);


                     462 ;444: 	tlsf_assert(!block_is_last(block));


                     463 ;445: 	return next;


                     464 ;446: }


                     465 ;447: 


                     466 ;448: /* Link a new block with its physical neighbor, return the neighbor. */


                     467 ;449: static block_header_t* block_link_next(block_header_t* block)


                     468 ;450: {


                     469 ;451: 	block_header_t* next = block_next(block);


                     470 ;452: 	next->prev_phys_block = block;


                     471 ;453: 	return next;


                     472 ;454: }


                     473 ;455: 


                     474 ;456: static void block_mark_as_free(block_header_t* block)


                     475 ;457: {


                     476 ;458: 	/* Link the block to the next block, first. */


                     477 ;459: 	block_header_t* next = block_link_next(block);



                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
                     478 ;460: 	block_set_prev_free(next);


                     479 ;461: 	block_set_free(block);


                     480 ;462: }


                     481 ;463: 


                     482 ;464: static void block_mark_as_used(block_header_t* block)


                     483 ;465: {


                     484 ;466: 	block_header_t* next = block_next(block);


                     485 ;467: 	block_set_prev_used(next);


                     486 ;468: 	block_set_used(block);


                     487 ;469: }


                     488 ;470: 


                     489 ;471: static size_t align_up(size_t x, size_t align)


                     490 

                     491 ;475: }


                     492 

                     493 ;476: 


                     494 ;477: static size_t align_down(size_t x, size_t align)


                     495 

                     496 ;481: }


                     497 

                     498 ;482: 


                     499 ;483: static void* align_ptr(const void* ptr, size_t align)


                     500 ;484: {


                     501 ;485: 	const tlsfptr_t aligned =


                     502 ;486: 		(tlsf_cast(tlsfptr_t, ptr) + (align - 1)) & ~(align - 1);


                     503 ;487: 	tlsf_assert(0 == (align & (align - 1)) && "must align to a power of two");


                     504 ;488: 	return tlsf_cast(void*, aligned);


                     505 ;489: }


                     506 ;490: 


                     507 ;491: /*


                     508 ;492: ** Adjust an allocation size to be aligned to word size, and no smaller


                     509 ;493: ** than internal minimum.


                     510 ;494: */


                     511 ;495: static size_t adjust_request_size(size_t size, size_t align)


                     512 ;496: {


                     513 ;497: 	size_t adjust = 0;


                     514 ;498: 	if (size)


                     515 ;499: 	{


                     516 ;500: 		const size_t aligned = align_up(size, align);


                     517 ;501: 


                     518 ;502: 		/* aligned sized must not exceed block_size_max or we'll go out of bounds on sl_bitmap */


                     519 ;503: 		if (aligned < block_size_max) 


                     520 ;504: 		{


                     521 ;505: 			adjust = tlsf_max(aligned, block_size_min);


                     522 ;506: 		}


                     523 ;507: 	}


                     524 ;508: 	return adjust;


                     525 ;509: }


                     526 ;510: 


                     527 ;511: /*


                     528 ;512: ** TLSF utility functions. In most cases, these are direct translations of


                     529 ;513: ** the documentation found in the white paper.


                     530 ;514: */


                     531 ;515: 


                     532 ;516: static void mapping_insert(size_t size, int* fli, int* sli)


                     533 ;517: {


                     534 ;518: 	int fl, sl;


                     535 ;519: 	if (size < SMALL_BLOCK_SIZE)


                     536 ;520: 	{


                     537 ;521: 		/* Store small blocks in first list. */


                     538 ;522: 		fl = 0;



                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
                     539 ;523: 		sl = tlsf_cast(int, size) / (SMALL_BLOCK_SIZE / SL_INDEX_COUNT);


                     540 ;524: 	}


                     541 ;525: 	else


                     542 ;526: 	{


                     543 ;527: 		fl = tlsf_fls_sizet(size);


                     544 ;528: 		sl = tlsf_cast(int, size >> (fl - SL_INDEX_COUNT_LOG2)) ^ (1 << SL_INDEX_COUNT_LOG2);


                     545 ;529: 		fl -= (FL_INDEX_SHIFT - 1);


                     546 ;530: 	}


                     547 ;531: 	*fli = fl;


                     548 ;532: 	*sli = sl;


                     549 ;533: }


                     550 ;534: 


                     551 ;535: /* This version rounds up to the next block size (for allocations) */


                     552 ;536: static void mapping_search(size_t size, int* fli, int* sli)


                     553 

                     554 ;544: }


                     555 

                     556 ;545: 


                     557 ;546: static block_header_t* search_suitable_block(control_t* control, int* fli, int* sli)


                     558 

                     559 ;576: }


                     560 

                     561 ;577: 


                     562 ;578: /* Remove a free block from the free list.*/


                     563 ;579: static void remove_free_block(control_t* control, block_header_t* block, int fl, int sl)


                     564 ;580: {


                     565 ;581: 	block_header_t* prev = block->prev_free;


                     566 ;582: 	block_header_t* next = block->next_free;


                     567 ;583: 	tlsf_assert(prev && "prev_free field can not be null");


                     568 ;584: 	tlsf_assert(next && "next_free field can not be null");


                     569 ;585: 	next->prev_free = prev;


                     570 ;586: 	prev->next_free = next;


                     571 ;587: 


                     572 ;588: 	/* If this block is the head of the free list, set new head. */


                     573 ;589: 	if (control->blocks[fl][sl] == block)


                     574 ;590: 	{


                     575 ;591: 		control->blocks[fl][sl] = next;


                     576 ;592: 


                     577 ;593: 		/* If the new head is null, clear the bitmap. */


                     578 ;594: 		if (next == &control->block_null)


                     579 ;595: 		{


                     580 ;596: 			control->sl_bitmap[fl] &= ~(1 << sl);


                     581 ;597: 


                     582 ;598: 			/* If the second bitmap is now empty, clear the fl bitmap. */


                     583 ;599: 			if (!control->sl_bitmap[fl])


                     584 ;600: 			{


                     585 ;601: 				control->fl_bitmap &= ~(1 << fl);


                     586 ;602: 			}


                     587 ;603: 		}


                     588 ;604: 	}


                     589 ;605: }


                     590 ;606: 


                     591 ;607: /* Insert a free block into the free block list. */


                     592 ;608: static void insert_free_block(control_t* control, block_header_t* block, int fl, int sl)


                     593 

                     594 ;626: }


                     595 

                     596 ;627: 


                     597 ;628: /* Remove a given block from the free list. */


                     598 ;629: static void block_remove(control_t* control, block_header_t* block)


                     599 ;630: {



                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
                     600 ;631: 	int fl, sl;


                     601 ;632: 	mapping_insert(block_size(block), &fl, &sl);


                     602 ;633: 	remove_free_block(control, block, fl, sl);


                     603 ;634: }


                     604 ;635: 


                     605 ;636: /* Insert a given block into the free list. */


                     606 ;637: static void block_insert(control_t* control, block_header_t* block)


                     607 ;638: {


                     608 ;639: 	int fl, sl;


                     609 ;640: 	mapping_insert(block_size(block), &fl, &sl);


                     610 ;641: 	insert_free_block(control, block, fl, sl);


                     611 ;642: }


                     612 ;643: 


                     613 ;644: static int block_can_split(block_header_t* block, size_t size)


                     614 ;645: {


                     615 ;646: 	return block_size(block) >= sizeof(block_header_t) + size;


                     616 ;647: }


                     617 ;648: 


                     618 ;649: /* Split a block into two, the second of which is free. */


                     619 ;650: static block_header_t* block_split(block_header_t* block, size_t size)


                     620 ;651: {


                     621 ;652: 	/* Calculate the amount of space left in the remaining block. */


                     622 ;653: 	block_header_t* remaining =


                     623 ;654: 		offset_to_block(block_to_ptr(block), size - block_header_overhead);


                     624 ;655: 


                     625 ;656: 	const size_t remain_size = block_size(block) - (size + block_header_overhead);


                     626 ;657: 


                     627 ;658: 	tlsf_assert(block_to_ptr(remaining) == align_ptr(block_to_ptr(remaining), ALIGN_SIZE)


                     628 ;659: 		&& "remaining block not aligned properly");


                     629 ;660: 


                     630 ;661: 	tlsf_assert(block_size(block) == remain_size + size + block_header_overhead);


                     631 ;662: 	block_set_size(remaining, remain_size);


                     632 ;663: 	tlsf_assert(block_size(remaining) >= block_size_min && "block split with invalid size");


                     633 ;664: 


                     634 ;665: 	block_set_size(block, size);


                     635 ;666: 	block_mark_as_free(remaining);


                     636 ;667: 


                     637 ;668: 	return remaining;


                     638 ;669: }


                     639 ;670: 


                     640 ;671: /* Absorb a free block's storage into an adjacent previous free block. */


                     641 ;672: static block_header_t* block_absorb(block_header_t* prev, block_header_t* block)


                     642 ;673: {


                     643 ;674: 	tlsf_assert(!block_is_last(prev) && "previous block can't be last");


                     644 ;675: 	/* Note: Leaves flags untouched. */


                     645 ;676: 	prev->size += block_size(block) + block_header_overhead;


                     646 ;677: 	block_link_next(prev);


                     647 ;678: 	return prev;


                     648 ;679: }


                     649 ;680: 


                     650 ;681: /* Merge a just-freed block with an adjacent previous free block. */


                     651 ;682: static block_header_t* block_merge_prev(control_t* control, block_header_t* block)


                     652 

                     653 ;694: }


                     654 

                     655 ;695: 


                     656 ;696: /* Merge a just-freed block with an adjacent free block. */


                     657 ;697: static block_header_t* block_merge_next(control_t* control, block_header_t* block)


                     658 ;698: {


                     659 ;699: 	block_header_t* next = block_next(block);


                     660 ;700: 	tlsf_assert(next && "next physical block can't be null");



                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
                     661 ;701: 


                     662 ;702: 	if (block_is_free(next))


                     663 ;703: 	{


                     664 ;704: 		tlsf_assert(!block_is_last(block) && "previous block can't be last");


                     665 ;705: 		block_remove(control, next);


                     666 ;706: 		block = block_absorb(block, next);


                     667 ;707: 	}


                     668 ;708: 


                     669 ;709: 	return block;


                     670 ;710: }


                     671 ;711: 


                     672 ;712: /* Trim any trailing block space off the end of a block, return to pool. */


                     673 ;713: static void block_trim_free(control_t* control, block_header_t* block, size_t size)


                     674 

                     675 ;722: 	}


                     676 ;723: }


                     677 

                     678 ;724: 


                     679 ;725: /* Trim any trailing block space off the end of a used block, return to pool. */


                     680 ;726: static void block_trim_used(control_t* control, block_header_t* block, size_t size)


                     681 

                     682 ;737: 	}


                     683 ;738: }


                     684 

                     685 ;739: 


                     686 ;740: static block_header_t* block_trim_free_leading(control_t* control, block_header_t* block, size_t size)


                     687 

                     688 ;754: }


                     689 

                     690 ;755: 


                     691 ;756: static block_header_t* block_locate_free(control_t* control, size_t size)


                     692 ;757: {


                     693 ;758: 	int fl = 0, sl = 0;


                     694 ;759: 	block_header_t* block = 0;


                     695 ;760: 


                     696 ;761: 	if (size)


                     697 ;762: 	{


                     698 ;763: 		mapping_search(size, &fl, &sl);


                     699 ;764: 		


                     700 ;765: 		/*


                     701 ;766: 		** mapping_search can futz with the size, so for excessively large sizes it can sometimes wind up 


                     702 ;767: 		** with indices that are off the end of the block array.


                     703 ;768: 		** So, we protect against that here, since this is the only callsite of mapping_search.


                     704 ;769: 		** Note that we don't need to check sl, since it comes from a modulo operation that guarantees it's always in range.


                     705 ;770: 		*/


                     706 ;771: 		if (fl < FL_INDEX_COUNT)


                     707 ;772: 		{


                     708 ;773: 			block = search_suitable_block(control, &fl, &sl);


                     709 ;774: 		}


                     710 ;775: 	}


                     711 ;776: 


                     712 ;777: 	if (block)


                     713 ;778: 	{


                     714 ;779: 		tlsf_assert(block_size(block) >= size);


                     715 ;780: 		remove_free_block(control, block, fl, sl);


                     716 ;781: 	}


                     717 ;782: 


                     718 ;783: 	return block;


                     719 ;784: }


                     720 ;785: 


                     721 ;786: static void* block_prepare_used(control_t* control, block_header_t* block, size_t size)



                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
                     722 ;787: {


                     723 ;788: 	void* p = 0;


                     724 ;789: 	if (block)


                     725 ;790: 	{


                     726 ;791: 		tlsf_assert(size && "size must be non-zero");


                     727 ;792: 		block_trim_free(control, block, size);


                     728 ;793: 		block_mark_as_used(block);


                     729 ;794: 		p = block_to_ptr(block);


                     730 ;795: 	}


                     731 ;796: 	return p;


                     732 ;797: }


                     733 ;798: 


                     734 ;799: /* Clear structure and point all empty lists at the null block. */


                     735 ;800: static void control_construct(control_t* control)


                     736 

                     737 ;814: 		}


                     738 ;815: 	}


                     739 ;816: }


                     740 

                     741 ;817: 


                     742 ;818: /*


                     743 ;819: ** Debugging utilities.


                     744 ;820: */


                     745 ;821: 


                     746 ;822: typedef struct integrity_t


                     747 ;823: {


                     748 ;824: 	int prev_status;


                     749 ;825: 	int status;


                     750 ;826: } integrity_t;


                     751 ;827: 


                     752 ;828: #define tlsf_insist(x) { tlsf_assert(x); if (!(x)) { status--; } }


                     753 ;829: 


                     754 ;830: static void integrity_walker(void* ptr, size_t size, int used, void* user)


                     755 ;831: {


                     756 ;832: 	block_header_t* block = block_from_ptr(ptr);


                     757 ;833: 	integrity_t* integ = tlsf_cast(integrity_t*, user);


                     758 ;834: 	const int this_prev_status = block_is_prev_free(block) ? 1 : 0;


                     759 ;835: 	const int this_status = block_is_free(block) ? 1 : 0;


                     760 ;836: 	const size_t this_block_size = block_size(block);


                     761 ;837: 


                     762 ;838: 	int status = 0;


                     763 ;839: 	(void)used;


                     764 ;840: 	tlsf_insist(integ->prev_status == this_prev_status && "prev status incorrect");


                     765 ;841: 	tlsf_insist(size == this_block_size && "block size incorrect");


                     766 ;842: 


                     767 ;843: 	integ->prev_status = this_status;


                     768 ;844: 	integ->status += status;


                     769 ;845: }


                     770 ;846: 


                     771 ;847: int tlsf_check(tlsf_t tlsf)


                     772 ;848: {


                     773 ;849: 	int i, j;


                     774 ;850: 


                     775 ;851: 	control_t* control = tlsf_cast(control_t*, tlsf);


                     776 ;852: 	int status = 0;


                     777 ;853: 


                     778 ;854: 	/* Check that the free lists and bitmaps are accurate. */


                     779 ;855: 	for (i = 0; i < FL_INDEX_COUNT; ++i)


                     780 ;856: 	{


                     781 ;857: 		for (j = 0; j < SL_INDEX_COUNT; ++j)


                     782 ;858: 		{



                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
                     783 ;859: 			const int fl_map = control->fl_bitmap & (1 << i);


                     784 ;860: 			const int sl_list = control->sl_bitmap[i];


                     785 ;861: 			const int sl_map = sl_list & (1 << j);


                     786 ;862: 			const block_header_t* block = control->blocks[i][j];


                     787 ;863: 


                     788 ;864: 			/* Check that first- and second-level lists agree. */


                     789 ;865: 			if (!fl_map)


                     790 ;866: 			{


                     791 ;867: 				tlsf_insist(!sl_map && "second-level map must be null");


                     792 ;868: 			}


                     793 ;869: 


                     794 ;870: 			if (!sl_map)


                     795 ;871: 			{


                     796 ;872: 				tlsf_insist(block == &control->block_null && "block list must be null");


                     797 ;873: 				continue;


                     798 ;874: 			}


                     799 ;875: 


                     800 ;876: 			/* Check that there is at least one free block. */


                     801 ;877: 			tlsf_insist(sl_list && "no free blocks in second-level map");


                     802 ;878: 			tlsf_insist(block != &control->block_null && "block should not be null");


                     803 ;879: 


                     804 ;880: 			while (block != &control->block_null)


                     805 ;881: 			{


                     806 ;882: 				int fli, sli;


                     807 ;883: 				tlsf_insist(block_is_free(block) && "block should be free");


                     808 ;884: 				tlsf_insist(!block_is_prev_free(block) && "blocks should have coalesced");


                     809 ;885: 				tlsf_insist(!block_is_free(block_next(block)) && "blocks should have coalesced");


                     810 ;886: 				tlsf_insist(block_is_prev_free(block_next(block)) && "block should be free");


                     811 ;887: 				tlsf_insist(block_size(block) >= block_size_min && "block not minimum size");


                     812 ;888: 


                     813 ;889: 				mapping_insert(block_size(block), &fli, &sli);


                     814 ;890: 				tlsf_insist(fli == i && sli == j && "block size indexed in wrong list");


                     815 ;891: 				block = block->next_free;


                     816 ;892: 			}


                     817 ;893: 		}


                     818 ;894: 	}


                     819 ;895: 


                     820 ;896: 	return status;


                     821 ;897: }


                     822 ;898: 


                     823 ;899: #undef tlsf_insist


                     824 ;900: 


                     825 ;901: static void default_walker(void* ptr, size_t size, int used, void* user)


                     826 ;902: {


                     827 ;903: 	(void)user;


                     828 ;904: 	printf("\t%p %s size: %x (%p)\n", ptr, used ? "used" : "free", (unsigned int)size, block_from_ptr(ptr));


                     829 ;905: }


                     830 ;906: 


                     831 ;907: void tlsf_walk_pool(pool_t pool, tlsf_walker walker, void* user)


                     832 ;908: {


                     833 ;909: 	tlsf_walker pool_walker = walker ? walker : default_walker;


                     834 ;910: 	block_header_t* block =


                     835 ;911: 		offset_to_block(pool, -(int)block_header_overhead);


                     836 ;912: 


                     837 ;913: 	while (block && !block_is_last(block))


                     838 ;914: 	{


                     839 ;915: 		pool_walker(


                     840 ;916: 			block_to_ptr(block),


                     841 ;917: 			block_size(block),


                     842 ;918: 			!block_is_free(block),


                     843 ;919: 			user);



                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
                     844 ;920: 		block = block_next(block);


                     845 ;921: 	}


                     846 ;922: }


                     847 ;923: 


                     848 ;924: size_t tlsf_block_size(void* ptr)


                     849 ;925: {


                     850 ;926: 	size_t size = 0;


                     851 ;927: 	if (ptr)


                     852 ;928: 	{


                     853 ;929: 		const block_header_t* block = block_from_ptr(ptr);


                     854 ;930: 		size = block_size(block);


                     855 ;931: 	}


                     856 ;932: 	return size;


                     857 ;933: }


                     858 ;934: 


                     859 ;935: int tlsf_check_pool(pool_t pool)


                     860 ;936: {


                     861 ;937: 	/* Check that the blocks are physically correct. */


                     862 ;938: 	integrity_t integ = { 0, 0 };


                     863 ;939: 	tlsf_walk_pool(pool, integrity_walker, &integ);


                     864 ;940: 


                     865 ;941: 	return integ.status;


                     866 ;942: }


                     867 ;943: 


                     868 ;944: /*


                     869 ;945: ** Size of the TLSF structures in a given memory block passed to


                     870 ;946: ** tlsf_create, equal to the size of a control_t


                     871 ;947: */


                     872 ;948: size_t tlsf_size(void)


                     873 

                     874 ;951: }


                     875 

                     876 ;952: 


                     877 ;953: size_t tlsf_align_size(void)


                     878 

                     879 ;956: }


                     880 

                     881 ;957: 


                     882 ;958: size_t tlsf_block_size_min(void)


                     883 

                     884 ;961: }


                     885 

                     886 ;962: 


                     887 ;963: size_t tlsf_block_size_max(void)


                     888 

                     889 ;966: }


                     890 

                     891 ;967: 


                     892 ;968: /*


                     893 ;969: ** Overhead of the TLSF structures in a given memory block passed to


                     894 ;970: ** tlsf_add_pool, equal to the overhead of a free block and the


                     895 ;971: ** sentinel block.


                     896 ;972: */


                     897 ;973: size_t tlsf_pool_overhead(void)


                     898 ;974: {


                     899 ;975: 	return 2 * block_header_overhead;


                     900 ;976: }


                     901 ;977: 


                     902 ;978: size_t tlsf_alloc_overhead(void)


                     903 

                     904 ;981: }



                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
                     905 

                     906 	.text

                     907 	.align	4

                     908 tlsf_ffs:

00000000 e92d4000    909 	stmfd	[sp]!,{lr}

00000004 e2601000    910 	rsb	r1,r0,0

00000008 e0010000    911 	and	r0,r1,r0

0000000c eb000000*   912 	bl	__CLZ32

00000010 e2600020    913 	rsb	r0,r0,32

00000014 e2400001    914 	sub	r0,r0,1

00000018 e8bd4000    915 	ldmfd	[sp]!,{lr}

0000001c e12fff1e*   916 	ret	

                     917 	.endf	tlsf_ffs

                     918 	.align	4

                     919 ;bit	r0	local

                     920 

                     921 ;word	r0	param

                     922 

                     923 	.section ".bss","awb"

                     924 .L302:

                     925 	.data

                     926 	.text

                     927 

                     928 

                     929 	.align	4

                     930 	.align	4

                     931 tlsf_fls:

00000020 e92d4000    932 	stmfd	[sp]!,{lr}

00000024 e3a01000    933 	mov	r1,0

00000028 e3500000    934 	cmp	r0,0

0000002c 0a000001    935 	beq	.L309

00000030 eb000000*   936 	bl	__CLZ32

00000034 e2601020    937 	rsb	r1,r0,32

                     938 .L309:

00000038 e2410001    939 	sub	r0,r1,1

0000003c e8bd4000    940 	ldmfd	[sp]!,{lr}

00000040 e12fff1e*   941 	ret	

                     942 	.endf	tlsf_fls

                     943 	.align	4

                     944 

                     945 ;word	r0	param

                     946 

                     947 	.section ".bss","awb"

                     948 .L349:

                     949 	.data

                     950 	.text

                     951 

                     952 

                     953 	.align	4

                     954 	.align	4

                     955 block_size:

00000044 e5900004    956 	ldr	r0,[r0,4]

00000048 e3c00003    957 	bic	r0,r0,3

0000004c e12fff1e*   958 	ret	

                     959 	.endf	block_size

                     960 	.align	4

                     961 

                     962 ;block	r0	param

                     963 

                     964 	.section ".bss","awb"

                     965 .L382:


                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
                     966 	.data

                     967 	.text

                     968 

                     969 

                     970 	.align	4

                     971 	.align	4

                     972 block_set_size:

00000050 e5902004    973 	ldr	r2,[r0,4]

00000054 e2022003    974 	and	r2,r2,3

00000058 e1821001    975 	orr	r1,r2,r1

0000005c e5801004    976 	str	r1,[r0,4]

00000060 e12fff1e*   977 	ret	

                     978 	.endf	block_set_size

                     979 	.align	4

                     980 ;oldsize	r2	local

                     981 

                     982 ;block	r0	param

                     983 ;size	r1	param

                     984 

                     985 	.section ".bss","awb"

                     986 .L414:

                     987 	.data

                     988 	.text

                     989 

                     990 

                     991 	.align	4

                     992 	.align	4

                     993 block_is_free:

00000064 e5900004    994 	ldr	r0,[r0,4]

00000068 e2000001    995 	and	r0,r0,1

0000006c e12fff1e*   996 	ret	

                     997 	.endf	block_is_free

                     998 	.align	4

                     999 

                    1000 ;block	r0	param

                    1001 

                    1002 	.section ".bss","awb"

                    1003 .L446:

                    1004 	.data

                    1005 	.text

                    1006 

                    1007 

                    1008 	.align	4

                    1009 	.align	4

                    1010 block_set_free:

00000070 e5901004   1011 	ldr	r1,[r0,4]

00000074 e3811001   1012 	orr	r1,r1,1

00000078 e5801004   1013 	str	r1,[r0,4]

0000007c e12fff1e*  1014 	ret	

                    1015 	.endf	block_set_free

                    1016 	.align	4

                    1017 

                    1018 ;block	r0	param

                    1019 

                    1020 	.section ".bss","awb"

                    1021 .L478:

                    1022 	.data

                    1023 	.text

                    1024 

                    1025 

                    1026 	.align	4


                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
                    1027 	.align	4

                    1028 block_set_used:

00000080 e5901004   1029 	ldr	r1,[r0,4]

00000084 e3c11001   1030 	bic	r1,r1,1

00000088 e5801004   1031 	str	r1,[r0,4]

0000008c e12fff1e*  1032 	ret	

                    1033 	.endf	block_set_used

                    1034 	.align	4

                    1035 

                    1036 ;block	r0	param

                    1037 

                    1038 	.section ".bss","awb"

                    1039 .L510:

                    1040 	.data

                    1041 	.text

                    1042 

                    1043 

                    1044 	.align	4

                    1045 	.align	4

                    1046 block_is_prev_free:

00000090 e5900004   1047 	ldr	r0,[r0,4]

00000094 e2000002   1048 	and	r0,r0,2

00000098 e12fff1e*  1049 	ret	

                    1050 	.endf	block_is_prev_free

                    1051 	.align	4

                    1052 

                    1053 ;block	r0	param

                    1054 

                    1055 	.section ".bss","awb"

                    1056 .L542:

                    1057 	.data

                    1058 	.text

                    1059 

                    1060 

                    1061 	.align	4

                    1062 	.align	4

                    1063 block_set_prev_free:

0000009c e5901004   1064 	ldr	r1,[r0,4]

000000a0 e3811002   1065 	orr	r1,r1,2

000000a4 e5801004   1066 	str	r1,[r0,4]

000000a8 e12fff1e*  1067 	ret	

                    1068 	.endf	block_set_prev_free

                    1069 	.align	4

                    1070 

                    1071 ;block	r0	param

                    1072 

                    1073 	.section ".bss","awb"

                    1074 .L574:

                    1075 	.data

                    1076 	.text

                    1077 

                    1078 

                    1079 	.align	4

                    1080 	.align	4

                    1081 block_set_prev_used:

000000ac e5901004   1082 	ldr	r1,[r0,4]

000000b0 e3c11002   1083 	bic	r1,r1,2

000000b4 e5801004   1084 	str	r1,[r0,4]

000000b8 e12fff1e*  1085 	ret	

                    1086 	.endf	block_set_prev_used

                    1087 	.align	4


                                                                      Page 19
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
                    1088 

                    1089 ;block	r0	param

                    1090 

                    1091 	.section ".bss","awb"

                    1092 .L606:

                    1093 	.data

                    1094 	.text

                    1095 

                    1096 

                    1097 	.align	4

                    1098 	.align	4

                    1099 block_from_ptr:

000000bc e2400008   1100 	sub	r0,r0,8

000000c0 e12fff1e*  1101 	ret	

                    1102 	.endf	block_from_ptr

                    1103 	.align	4

                    1104 

                    1105 ;ptr	r0	param

                    1106 

                    1107 	.section ".bss","awb"

                    1108 .L638:

                    1109 	.data

                    1110 	.text

                    1111 

                    1112 

                    1113 	.align	4

                    1114 	.align	4

                    1115 block_to_ptr:

000000c4 e2800008   1116 	add	r0,r0,8

000000c8 e12fff1e*  1117 	ret	

                    1118 	.endf	block_to_ptr

                    1119 	.align	4

                    1120 

                    1121 ;block	r0	param

                    1122 

                    1123 	.section ".bss","awb"

                    1124 .L670:

                    1125 	.data

                    1126 	.text

                    1127 

                    1128 

                    1129 	.align	4

                    1130 	.align	4

                    1131 offset_to_block:

000000cc e0810000   1132 	add	r0,r1,r0

000000d0 e12fff1e*  1133 	ret	

                    1134 	.endf	offset_to_block

                    1135 	.align	4

                    1136 

                    1137 ;ptr	r0	param

                    1138 ;size	r1	param

                    1139 

                    1140 	.section ".bss","awb"

                    1141 .L702:

                    1142 	.data

                    1143 	.text

                    1144 

                    1145 

                    1146 	.align	4

                    1147 	.align	4

                    1148 block_next:


                                                                      Page 20
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
000000d4 e92d4030   1149 	stmfd	[sp]!,{r4-r5,lr}

000000d8 e1a04000   1150 	mov	r4,r0

000000dc ebffffd8*  1151 	bl	block_size

000000e0 e2405004   1152 	sub	r5,r0,4

000000e4 e1a00004   1153 	mov	r0,r4

000000e8 ebfffff5*  1154 	bl	block_to_ptr

000000ec e1a01005   1155 	mov	r1,r5

000000f0 ebfffff5*  1156 	bl	offset_to_block

000000f4 e8bd4030   1157 	ldmfd	[sp]!,{r4-r5,lr}

000000f8 e12fff1e*  1158 	ret	

                    1159 	.endf	block_next

                    1160 	.align	4

                    1161 

                    1162 ;block	r4	param

                    1163 

                    1164 	.section ".bss","awb"

                    1165 .L734:

                    1166 	.data

                    1167 	.text

                    1168 

                    1169 

                    1170 	.align	4

                    1171 	.align	4

                    1172 block_link_next:

000000fc e92d4010   1173 	stmfd	[sp]!,{r4,lr}

00000100 e1a04000   1174 	mov	r4,r0

00000104 ebfffff2*  1175 	bl	block_next

00000108 e5804000   1176 	str	r4,[r0]

0000010c e8bd4010   1177 	ldmfd	[sp]!,{r4,lr}

00000110 e12fff1e*  1178 	ret	

                    1179 	.endf	block_link_next

                    1180 	.align	4

                    1181 

                    1182 ;block	r4	param

                    1183 

                    1184 	.section ".bss","awb"

                    1185 .L766:

                    1186 	.data

                    1187 	.text

                    1188 

                    1189 

                    1190 	.align	4

                    1191 	.align	4

                    1192 block_mark_as_free:

00000114 e92d4010   1193 	stmfd	[sp]!,{r4,lr}

00000118 e1a04000   1194 	mov	r4,r0

0000011c ebfffff6*  1195 	bl	block_link_next

00000120 ebffffdd*  1196 	bl	block_set_prev_free

00000124 e1a00004   1197 	mov	r0,r4

00000128 ebffffd0*  1198 	bl	block_set_free

0000012c e8bd4010   1199 	ldmfd	[sp]!,{r4,lr}

00000130 e12fff1e*  1200 	ret	

                    1201 	.endf	block_mark_as_free

                    1202 	.align	4

                    1203 

                    1204 ;block	r4	param

                    1205 

                    1206 	.section ".bss","awb"

                    1207 .L798:

                    1208 	.data

                    1209 	.text


                                                                      Page 21
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
                    1210 

                    1211 

                    1212 	.align	4

                    1213 	.align	4

                    1214 block_mark_as_used:

00000134 e92d4010   1215 	stmfd	[sp]!,{r4,lr}

00000138 e1a04000   1216 	mov	r4,r0

0000013c ebffffe4*  1217 	bl	block_next

00000140 ebffffd9*  1218 	bl	block_set_prev_used

00000144 e1a00004   1219 	mov	r0,r4

00000148 ebffffcc*  1220 	bl	block_set_used

0000014c e8bd4010   1221 	ldmfd	[sp]!,{r4,lr}

00000150 e12fff1e*  1222 	ret	

                    1223 	.endf	block_mark_as_used

                    1224 	.align	4

                    1225 

                    1226 ;block	r4	param

                    1227 

                    1228 	.section ".bss","awb"

                    1229 .L830:

                    1230 	.data

                    1231 	.text

                    1232 

                    1233 

                    1234 	.align	4

                    1235 	.align	4

                    1236 align_ptr:

00000154 e2411001   1237 	sub	r1,r1,1

00000158 e0810000   1238 	add	r0,r1,r0

0000015c e1c00001   1239 	bic	r0,r0,r1

00000160 e12fff1e*  1240 	ret	

                    1241 	.endf	align_ptr

                    1242 	.align	4

                    1243 

                    1244 ;ptr	r0	param

                    1245 ;align	r1	param

                    1246 

                    1247 	.section ".bss","awb"

                    1248 .L862:

                    1249 	.data

                    1250 	.text

                    1251 

                    1252 

                    1253 	.align	4

                    1254 	.align	4

                    1255 adjust_request_size:

00000164 e1b02000   1256 	movs	r2,r0

00000168 e3a00000   1257 	mov	r0,0

0000016c 0a000007   1258 	beq	.L869

                    1259 ;472: {


                    1260 

                    1261 ;473: 	tlsf_assert(0 == (align & (align - 1)) && "must align to a power of two");


                    1262 ;474: 	return (x + (align - 1)) & ~(align - 1);


                    1263 

00000170 e2411001   1264 	sub	r1,r1,1

00000174 e0812002   1265 	add	r2,r1,r2

00000178 e1c21001   1266 	bic	r1,r2,r1

0000017c e3510440   1267 	cmp	r1,1<<30

00000180 2a000002   1268 	bhs	.L869

00000184 e351000c   1269 	cmp	r1,12

00000188 21a00001   1270 	movhs	r0,r1


                                                                      Page 22
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
0000018c 33a0000c   1271 	movlo	r0,12

                    1272 .L869:

00000190 e12fff1e*  1273 	ret	

                    1274 	.endf	adjust_request_size

                    1275 	.align	4

                    1276 ;adjust	r0	local

                    1277 ;aligned	r1	local

                    1278 

                    1279 ;size	r2	param

                    1280 ;align	r1	param

                    1281 

                    1282 	.section ".bss","awb"

                    1283 .L916:

                    1284 	.data

                    1285 	.text

                    1286 

                    1287 

                    1288 	.align	4

                    1289 	.align	4

                    1290 mapping_insert:

00000194 e92d4070   1291 	stmfd	[sp]!,{r4-r6,lr}

00000198 e1a06002   1292 	mov	r6,r2

0000019c e1a04000   1293 	mov	r4,r0

000001a0 e3540080   1294 	cmp	r4,128

000001a4 33a00000   1295 	movlo	r0,0

000001a8 e1a05001   1296 	mov	r5,r1

000001ac 31a01fc4   1297 	movlo	r1,r4 asr 31

000001b0 30841f21   1298 	addlo	r1,r4,r1 lsr 30

000001b4 31a01141   1299 	movlo	r1,r1 asr 2

000001b8 3a000004   1300 	blo	.L929

000001bc ebffff97*  1301 	bl	tlsf_fls

000001c0 e2401005   1302 	sub	r1,r0,5

000001c4 e1a01134   1303 	mov	r1,r4 lsr r1

000001c8 e2211020   1304 	eor	r1,r1,32

000001cc e2400006   1305 	sub	r0,r0,6

                    1306 .L929:

000001d0 e5850000   1307 	str	r0,[r5]

000001d4 e5861000   1308 	str	r1,[r6]

000001d8 e8bd4070   1309 	ldmfd	[sp]!,{r4-r6,lr}

000001dc e12fff1e*  1310 	ret	

                    1311 	.endf	mapping_insert

                    1312 	.align	4

                    1313 ;fl	r0	local

                    1314 ;sl	r1	local

                    1315 

                    1316 ;size	r4	param

                    1317 ;fli	r5	param

                    1318 ;sli	r6	param

                    1319 

                    1320 	.section ".bss","awb"

                    1321 .L970:

                    1322 	.data

                    1323 	.text

                    1324 

                    1325 

                    1326 	.align	4

                    1327 	.align	4

                    1328 remove_free_block:

000001e0 e92d0030   1329 	stmfd	[sp]!,{r4-r5}

000001e4 e591c008   1330 	ldr	r12,[r1,8]

000001e8 e591400c   1331 	ldr	r4,[r1,12]


                                                                      Page 23
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
000001ec e58c400c   1332 	str	r4,[r12,12]

000001f0 e584c008   1333 	str	r12,[r4,8]

000001f4 e0834282   1334 	add	r4,r3,r2 lsl 5

000001f8 e0805104   1335 	add	r5,r0,r4 lsl 2

000001fc e5955074   1336 	ldr	r5,[r5,116]

00000200 e1550001   1337 	cmp	r5,r1

00000204 00801104   1338 	addeq	r1,r0,r4 lsl 2

00000208 0581c074   1339 	streq	r12,[r1,116]

0000020c 015c0000   1340 	cmpeq	r12,r0

00000210 1a00000a   1341 	bne	.L984

00000214 e3a01001   1342 	mov	r1,1

00000218 e1a0c311   1343 	mov	r12,r1 lsl r3

0000021c e0803102   1344 	add	r3,r0,r2 lsl 2

00000220 e5931014   1345 	ldr	r1,[r3,20]

00000224 e1d1100c   1346 	bics	r1,r1,r12

00000228 e5831014   1347 	str	r1,[r3,20]

0000022c 03a01001   1348 	moveq	r1,1

00000230 01a02211   1349 	moveq	r2,r1 lsl r2

00000234 05901010   1350 	ldreq	r1,[r0,16]

00000238 01c11002   1351 	biceq	r1,r1,r2

0000023c 05801010   1352 	streq	r1,[r0,16]

                    1353 .L984:

00000240 e8bd0030   1354 	ldmfd	[sp]!,{r4-r5}

00000244 e12fff1e*  1355 	ret	

                    1356 	.endf	remove_free_block

                    1357 	.align	4

                    1358 ;prev	r4	local

                    1359 ;next	r12	local

                    1360 

                    1361 ;control	r0	param

                    1362 ;block	r1	param

                    1363 ;fl	r2	param

                    1364 ;sl	r3	param

                    1365 

                    1366 	.section ".bss","awb"

                    1367 .L1036:

                    1368 	.data

                    1369 	.text

                    1370 

                    1371 

                    1372 	.align	4

                    1373 	.align	4

                    1374 block_remove:

00000248 e92d4030   1375 	stmfd	[sp]!,{r4-r5,lr}

0000024c e24dd008   1376 	sub	sp,sp,8

00000250 e1a05000   1377 	mov	r5,r0

00000254 e1a04001   1378 	mov	r4,r1

00000258 e1a00004   1379 	mov	r0,r4

0000025c ebffff78*  1380 	bl	block_size

00000260 e28d2004   1381 	add	r2,sp,4

00000264 e1a0100d   1382 	mov	r1,sp

00000268 ebffffc9*  1383 	bl	mapping_insert

0000026c e89d000c   1384 	ldmfd	[sp],{r2-r3}

00000270 e1a01004   1385 	mov	r1,r4

00000274 e1a00005   1386 	mov	r0,r5

00000278 ebffffd8*  1387 	bl	remove_free_block

0000027c e28dd008   1388 	add	sp,sp,8

00000280 e8bd4030   1389 	ldmfd	[sp]!,{r4-r5,lr}

00000284 e12fff1e*  1390 	ret	

                    1391 	.endf	block_remove

                    1392 	.align	4


                                                                      Page 24
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
                    1393 ;fl	[sp]	local

                    1394 ;sl	[sp,4]	local

                    1395 

                    1396 ;control	r5	param

                    1397 ;block	r4	param

                    1398 

                    1399 	.section ".bss","awb"

                    1400 .L1070:

                    1401 	.data

                    1402 	.text

                    1403 

                    1404 

                    1405 	.align	4

                    1406 	.align	4

                    1407 block_insert:

00000288 e92d4030   1408 	stmfd	[sp]!,{r4-r5,lr}

0000028c e24dd008   1409 	sub	sp,sp,8

00000290 e1a04000   1410 	mov	r4,r0

00000294 e1a05001   1411 	mov	r5,r1

00000298 e1a00005   1412 	mov	r0,r5

0000029c ebffff68*  1413 	bl	block_size

000002a0 e28d2004   1414 	add	r2,sp,4

000002a4 e1a0100d   1415 	mov	r1,sp

000002a8 ebffffb9*  1416 	bl	mapping_insert

                    1417 ;609: {


                    1418 

                    1419 ;610: 	block_header_t* current = control->blocks[fl][sl];


                    1420 

000002ac e89d0005   1421 	ldmfd	[sp],{r0,r2}

000002b0 e0821280   1422 	add	r1,r2,r0 lsl 5

000002b4 e0843101   1423 	add	r3,r4,r1 lsl 2

000002b8 e5931074   1424 	ldr	r1,[r3,116]

                    1425 ;611: 	tlsf_assert(current && "free list cannot have a null entry");


                    1426 ;612: 	tlsf_assert(block && "cannot insert a null entry into the free list");


                    1427 ;613: 	block->next_free = current;


                    1428 

000002bc e585400c   1429 	str	r4,[r5,12]

                    1430 ;615: 	current->prev_free = block;


                    1431 

000002c0 e5851008   1432 	str	r1,[r5,8]

                    1433 ;614: 	block->prev_free = &control->block_null;


                    1434 

000002c4 e581500c   1435 	str	r5,[r1,12]

                    1436 ;616: 


                    1437 ;617: 	tlsf_assert(block_to_ptr(block) == align_ptr(block_to_ptr(block), ALIGN_SIZE)


                    1438 ;618: 		&& "block not aligned properly");


                    1439 ;619: 	/*


                    1440 ;620: 	** Insert the new block at the head of the list, and mark the first-


                    1441 ;621: 	** and second-level bitmaps appropriately.


                    1442 ;622: 	*/


                    1443 ;623: 	control->blocks[fl][sl] = block;


                    1444 

000002c8 e5835074   1445 	str	r5,[r3,116]

                    1446 ;624: 	control->fl_bitmap |= (1 << fl);


                    1447 

000002cc e5941010   1448 	ldr	r1,[r4,16]

000002d0 e3a03001   1449 	mov	r3,1

000002d4 e1811013   1450 	orr	r1,r1,r3 lsl r0

000002d8 e5841010   1451 	str	r1,[r4,16]

                    1452 ;625: 	control->sl_bitmap[fl] |= (1 << sl);


                    1453 


                                                                      Page 25
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
000002dc e0841100   1454 	add	r1,r4,r0 lsl 2

000002e0 e5910014   1455 	ldr	r0,[r1,20]

000002e4 e1800213   1456 	orr	r0,r0,r3 lsl r2

000002e8 e5810014   1457 	str	r0,[r1,20]

000002ec e28dd008   1458 	add	sp,sp,8

000002f0 e8bd4030   1459 	ldmfd	[sp]!,{r4-r5,lr}

000002f4 e12fff1e*  1460 	ret	

                    1461 	.endf	block_insert

                    1462 	.align	4

                    1463 ;fl	[sp]	local

                    1464 ;sl	[sp,4]	local

                    1465 ;current	r1	local

                    1466 

                    1467 ;control	r4	param

                    1468 ;block	r5	param

                    1469 

                    1470 	.section ".bss","awb"

                    1471 .L1105:

                    1472 	.data

                    1473 	.text

                    1474 

                    1475 

                    1476 	.align	4

                    1477 	.align	4

                    1478 block_can_split:

000002f8 e92d4010   1479 	stmfd	[sp]!,{r4,lr}

000002fc e2814010   1480 	add	r4,r1,16

00000300 ebffff4f*  1481 	bl	block_size

00000304 e1500004   1482 	cmp	r0,r4

00000308 23a00001   1483 	movhs	r0,1

0000030c 33a00000   1484 	movlo	r0,0

00000310 e8bd4010   1485 	ldmfd	[sp]!,{r4,lr}

00000314 e12fff1e*  1486 	ret	

                    1487 	.endf	block_can_split

                    1488 	.align	4

                    1489 

                    1490 ;block	none	param

                    1491 ;size	r4	param

                    1492 

                    1493 	.section ".bss","awb"

                    1494 .L1134:

                    1495 	.data

                    1496 	.text

                    1497 

                    1498 

                    1499 	.align	4

                    1500 	.align	4

                    1501 block_split:

00000318 e92d4070   1502 	stmfd	[sp]!,{r4-r6,lr}

0000031c e1a05001   1503 	mov	r5,r1

00000320 e1a06000   1504 	mov	r6,r0

00000324 ebffff66*  1505 	bl	block_to_ptr

00000328 e2451004   1506 	sub	r1,r5,4

0000032c ebffff66*  1507 	bl	offset_to_block

00000330 e1a04000   1508 	mov	r4,r0

00000334 e1a00006   1509 	mov	r0,r6

00000338 ebffff41*  1510 	bl	block_size

0000033c e2851004   1511 	add	r1,r5,4

00000340 e0401001   1512 	sub	r1,r0,r1

00000344 e1a00004   1513 	mov	r0,r4

00000348 ebffff40*  1514 	bl	block_set_size


                                                                      Page 26
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
0000034c e1a01005   1515 	mov	r1,r5

00000350 e1a00006   1516 	mov	r0,r6

00000354 ebffff3d*  1517 	bl	block_set_size

00000358 e1a00004   1518 	mov	r0,r4

0000035c ebffff6c*  1519 	bl	block_mark_as_free

00000360 e1a00004   1520 	mov	r0,r4

00000364 e8bd4070   1521 	ldmfd	[sp]!,{r4-r6,lr}

00000368 e12fff1e*  1522 	ret	

                    1523 	.endf	block_split

                    1524 	.align	4

                    1525 

                    1526 ;block	r6	param

                    1527 ;size	r5	param

                    1528 

                    1529 	.section ".bss","awb"

                    1530 .L1166:

                    1531 	.data

                    1532 	.text

                    1533 

                    1534 

                    1535 	.align	4

                    1536 	.align	4

                    1537 block_absorb:

0000036c e92d4010   1538 	stmfd	[sp]!,{r4,lr}

00000370 e1a04000   1539 	mov	r4,r0

00000374 e1a00001   1540 	mov	r0,r1

00000378 ebffff31*  1541 	bl	block_size

0000037c e5941004   1542 	ldr	r1,[r4,4]

00000380 e0810000   1543 	add	r0,r1,r0

00000384 e2800004   1544 	add	r0,r0,4

00000388 e5840004   1545 	str	r0,[r4,4]

0000038c e1a00004   1546 	mov	r0,r4

00000390 ebffff59*  1547 	bl	block_link_next

00000394 e1a00004   1548 	mov	r0,r4

00000398 e8bd4010   1549 	ldmfd	[sp]!,{r4,lr}

0000039c e12fff1e*  1550 	ret	

                    1551 	.endf	block_absorb

                    1552 	.align	4

                    1553 

                    1554 ;prev	r4	param

                    1555 ;block	r1	param

                    1556 

                    1557 	.section ".bss","awb"

                    1558 .L1198:

                    1559 	.data

                    1560 	.text

                    1561 

                    1562 

                    1563 	.align	4

                    1564 	.align	4

                    1565 block_merge_next:

000003a0 e92d4070   1566 	stmfd	[sp]!,{r4-r6,lr}

000003a4 e1a06000   1567 	mov	r6,r0

000003a8 e1a04001   1568 	mov	r4,r1

000003ac e1a00004   1569 	mov	r0,r4

000003b0 ebffff47*  1570 	bl	block_next

000003b4 e1a05000   1571 	mov	r5,r0

000003b8 ebffff29*  1572 	bl	block_is_free

000003bc e3500000   1573 	cmp	r0,0

000003c0 0a000006   1574 	beq	.L1207

000003c4 e1a01005   1575 	mov	r1,r5


                                                                      Page 27
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
000003c8 e1a00006   1576 	mov	r0,r6

000003cc ebffff9d*  1577 	bl	block_remove

000003d0 e1a01005   1578 	mov	r1,r5

000003d4 e1a00004   1579 	mov	r0,r4

000003d8 ebffffe3*  1580 	bl	block_absorb

000003dc e1a04000   1581 	mov	r4,r0

                    1582 .L1207:

000003e0 e1a00004   1583 	mov	r0,r4

000003e4 e8bd4070   1584 	ldmfd	[sp]!,{r4-r6,lr}

000003e8 e12fff1e*  1585 	ret	

                    1586 	.endf	block_merge_next

                    1587 	.align	4

                    1588 ;next	r5	local

                    1589 

                    1590 ;control	r6	param

                    1591 ;block	r4	param

                    1592 

                    1593 	.section ".bss","awb"

                    1594 .L1245:

                    1595 	.data

                    1596 	.text

                    1597 

                    1598 

                    1599 	.align	4

                    1600 	.align	4

                    1601 block_locate_free:

000003ec e92d4070   1602 	stmfd	[sp]!,{r4-r6,lr}

000003f0 e24dd008   1603 	sub	sp,sp,8

000003f4 e1a05000   1604 	mov	r5,r0

000003f8 e3a06000   1605 	mov	r6,0

000003fc e1a00006   1606 	mov	r0,r6

00000400 e88d0041   1607 	stmea	[sp],{r0,r6}

00000404 e3510000   1608 	cmp	r1,0

00000408 0a00002e   1609 	beq	.L1276

0000040c e1a04001   1610 	mov	r4,r1

                    1611 ;537: {


                    1612 

                    1613 ;538: 	if (size >= SMALL_BLOCK_SIZE)


                    1614 

00000410 e3540080   1615 	cmp	r4,128

00000414 3a000005   1616 	blo	.L1264

                    1617 ;539: 	{


                    1618 

                    1619 ;540: 		const size_t round = (1 << (tlsf_fls_sizet(size) - SL_INDEX_COUNT_LOG2)) - 1;


                    1620 

00000418 e1a00004   1621 	mov	r0,r4

0000041c ebfffeff*  1622 	bl	tlsf_fls

00000420 e2400005   1623 	sub	r0,r0,5

00000424 e3a01001   1624 	mov	r1,1

00000428 e0840011   1625 	add	r0,r4,r1 lsl r0

                    1626 ;541: 		size += round;


                    1627 

0000042c e2404001   1628 	sub	r4,r0,1

                    1629 .L1264:

                    1630 ;542: 	}


                    1631 ;543: 	mapping_insert(size, fli, sli);


                    1632 

00000430 e28d2004   1633 	add	r2,sp,4

00000434 e1a0100d   1634 	mov	r1,sp

00000438 e1a00004   1635 	mov	r0,r4

0000043c ebffff54*  1636 	bl	mapping_insert


                                                                      Page 28
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
00000440 e59d4000   1637 	ldr	r4,[sp]

00000444 e3540018   1638 	cmp	r4,24

00000448 aa00001e   1639 	bge	.L1276

                    1640 ;547: {


                    1641 

                    1642 ;548: 	int fl = *fli;


                    1643 

                    1644 ;549: 	int sl = *sli;


                    1645 

0000044c e59d3004   1646 	ldr	r3,[sp,4]

                    1647 ;550: 


                    1648 ;551: 	/*


                    1649 ;552: 	** First, search for a block in the list associated with the given


                    1650 ;553: 	** fl/sl index.


                    1651 ;554: 	*/


                    1652 ;555: 	unsigned int sl_map = control->sl_bitmap[fl] & (~0U << sl);


                    1653 

00000450 e0850104   1654 	add	r0,r5,r4 lsl 2

00000454 e5900014   1655 	ldr	r0,[r0,20]

00000458 e3e01000   1656 	mvn	r1,0

0000045c e0100311   1657 	ands	r0,r0,r1 lsl r3

                    1658 ;556: 	if (!sl_map)


                    1659 

00000460 1a00000c   1660 	bne	.L1274

                    1661 ;557: 	{


                    1662 

                    1663 ;558: 		/* No block exists. Search in the next largest first-level list. */


                    1664 ;559: 		const unsigned int fl_map = control->fl_bitmap & (~0U << (fl + 1));


                    1665 

00000464 e2840001   1666 	add	r0,r4,1

00000468 e1a00011   1667 	mov	r0,r1 lsl r0

0000046c e5951010   1668 	ldr	r1,[r5,16]

00000470 e0100001   1669 	ands	r0,r0,r1

                    1670 ;560: 		if (!fl_map)


                    1671 

00000474 1a000002   1672 	bne	.L1273

                    1673 ;561: 		{


                    1674 

                    1675 ;562: 			/* No free blocks available, memory has been exhausted. */


                    1676 ;563: 			return 0;


                    1677 

00000478 e1b06000   1678 	movs	r6,r0

0000047c 0a000011   1679 	beq	.L1276

00000480 ea00000c   1680 	b	.L1277

                    1681 .L1273:

                    1682 ;564: 		}


                    1683 ;565: 


                    1684 ;566: 		fl = tlsf_ffs(fl_map);


                    1685 

00000484 ebfffedd*  1686 	bl	tlsf_ffs

00000488 e1a04000   1687 	mov	r4,r0

                    1688 ;567: 		*fli = fl;


                    1689 

0000048c e58d4000   1690 	str	r4,[sp]

                    1691 ;568: 		sl_map = control->sl_bitmap[fl];


                    1692 

00000490 e0850104   1693 	add	r0,r5,r4 lsl 2

00000494 e5900014   1694 	ldr	r0,[r0,20]

                    1695 .L1274:

                    1696 ;569: 	}


                    1697 ;570: 	tlsf_assert(sl_map && "internal error - second level bitmap is null");



                                                                      Page 29
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
                    1698 ;571: 	sl = tlsf_ffs(sl_map);


                    1699 

00000498 ebfffed8*  1700 	bl	tlsf_ffs

                    1701 ;572: 	*sli = sl;


                    1702 

0000049c e1a03000   1703 	mov	r3,r0

000004a0 e0800284   1704 	add	r0,r0,r4 lsl 5

000004a4 e0850100   1705 	add	r0,r5,r0 lsl 2

000004a8 e5906074   1706 	ldr	r6,[r0,116]

000004ac e58d3004   1707 	str	r3,[sp,4]

                    1708 ;573: 


                    1709 ;574: 	/* Return the first block in the free list. */


                    1710 ;575: 	return control->blocks[fl][sl];


                    1711 

000004b0 e3560000   1712 	cmp	r6,0

000004b4 0a000003   1713 	beq	.L1276

                    1714 .L1277:

000004b8 e59d2000   1715 	ldr	r2,[sp]

000004bc e1a01006   1716 	mov	r1,r6

000004c0 e1a00005   1717 	mov	r0,r5

000004c4 ebffff45*  1718 	bl	remove_free_block

                    1719 .L1276:

000004c8 e1a00006   1720 	mov	r0,r6

000004cc e28dd008   1721 	add	sp,sp,8

000004d0 e8bd4070   1722 	ldmfd	[sp]!,{r4-r6,lr}

000004d4 e12fff1e*  1723 	ret	

                    1724 	.endf	block_locate_free

                    1725 	.align	4

                    1726 ;fl	[sp]	local

                    1727 ;sl	[sp,4]	local

                    1728 ;block	r6	local

                    1729 ;size	r4	local

                    1730 ;round	r0	local

                    1731 ;fl	r4	local

                    1732 ;sl_map	r0	local

                    1733 ;fl_map	r0	local

                    1734 

                    1735 ;control	r5	param

                    1736 ;size	r1	param

                    1737 

                    1738 	.section ".bss","awb"

                    1739 .L1442:

                    1740 	.data

                    1741 	.text

                    1742 

                    1743 

                    1744 	.align	4

                    1745 	.align	4

                    1746 block_prepare_used:

000004d8 e92d4070   1747 	stmfd	[sp]!,{r4-r6,lr}

000004dc e1b04001   1748 	movs	r4,r1

000004e0 e1a05002   1749 	mov	r5,r2

000004e4 e1a06000   1750 	mov	r6,r0

000004e8 e3a00000   1751 	mov	r0,0

000004ec 0a000013   1752 	beq	.L1469

                    1753 ;714: {


                    1754 

                    1755 ;715: 	tlsf_assert(block_is_free(block) && "block must be free");


                    1756 ;716: 	if (block_can_split(block, size))


                    1757 

000004f0 e1a01005   1758 	mov	r1,r5


                                                                      Page 30
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
000004f4 e1a00004   1759 	mov	r0,r4

000004f8 ebffff7e*  1760 	bl	block_can_split

000004fc e3500000   1761 	cmp	r0,0

00000500 0a00000a   1762 	beq	.L1473

                    1763 ;717: 	{


                    1764 

                    1765 ;718: 		block_header_t* remaining_block = block_split(block, size);


                    1766 

00000504 e1a01005   1767 	mov	r1,r5

00000508 e1a00004   1768 	mov	r0,r4

0000050c ebffff81*  1769 	bl	block_split

00000510 e1a05000   1770 	mov	r5,r0

                    1771 ;719: 		block_link_next(block);


                    1772 

00000514 e1a00004   1773 	mov	r0,r4

00000518 ebfffef7*  1774 	bl	block_link_next

                    1775 ;720: 		block_set_prev_free(remaining_block);


                    1776 

0000051c e1a00005   1777 	mov	r0,r5

00000520 ebfffedd*  1778 	bl	block_set_prev_free

                    1779 ;721: 		block_insert(control, remaining_block);


                    1780 

00000524 e1a01005   1781 	mov	r1,r5

00000528 e1a00006   1782 	mov	r0,r6

0000052c ebffff55*  1783 	bl	block_insert

                    1784 .L1473:

00000530 e1a00004   1785 	mov	r0,r4

00000534 ebfffefe*  1786 	bl	block_mark_as_used

00000538 e1a00004   1787 	mov	r0,r4

0000053c ebfffee0*  1788 	bl	block_to_ptr

                    1789 .L1469:

00000540 e8bd4070   1790 	ldmfd	[sp]!,{r4-r6,lr}

00000544 e12fff1e*  1791 	ret	

                    1792 	.endf	block_prepare_used

                    1793 	.align	4

                    1794 ;p	r0	local

                    1795 ;remaining_block	r5	local

                    1796 

                    1797 ;control	r6	param

                    1798 ;block	r4	param

                    1799 ;size	r5	param

                    1800 

                    1801 	.section ".bss","awb"

                    1802 .L1529:

                    1803 	.data

                    1804 	.text

                    1805 

                    1806 

                    1807 	.align	4

                    1808 	.align	4

                    1809 integrity_walker:

00000548 e92d4cf0   1810 	stmfd	[sp]!,{r4-r7,r10-fp,lr}

0000054c e3a05000   1811 	mov	r5,0

00000550 e1a0a001   1812 	mov	r10,r1

00000554 e1a04003   1813 	mov	r4,r3

00000558 ebfffed7*  1814 	bl	block_from_ptr

0000055c e1a06000   1815 	mov	r6,r0

00000560 ebfffeca*  1816 	bl	block_is_prev_free

00000564 e1b0b000   1817 	movs	fp,r0

00000568 13a0b001   1818 	movne	fp,1

0000056c e1a00006   1819 	mov	r0,r6


                                                                      Page 31
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
00000570 ebfffebb*  1820 	bl	block_is_free

00000574 e1b07000   1821 	movs	r7,r0

00000578 13a07001   1822 	movne	r7,1

0000057c e1a00006   1823 	mov	r0,r6

00000580 ebfffeaf*  1824 	bl	block_size

00000584 e8940006   1825 	ldmfd	[r4],{r1-r2}

00000588 e151000b   1826 	cmp	r1,fp

0000058c 13e05000   1827 	mvnne	r5,0

00000590 e15a0000   1828 	cmp	r10,r0

00000594 12455001   1829 	subne	r5,r5,1

00000598 e082a005   1830 	add	r10,r2,r5

0000059c e8840480   1831 	stmea	[r4],{r7,r10}

000005a0 e8bd4cf0   1832 	ldmfd	[sp]!,{r4-r7,r10-fp,lr}

000005a4 e12fff1e*  1833 	ret	

                    1834 	.endf	integrity_walker

                    1835 	.align	4

                    1836 ;block	r6	local

                    1837 ;integ	r4	local

                    1838 ;this_prev_status	fp	local

                    1839 ;this_status	r7	local

                    1840 ;this_block_size	r0	local

                    1841 ;status	r5	local

                    1842 

                    1843 ;ptr	none	param

                    1844 ;size	r10	param

                    1845 ;used	none	param

                    1846 ;user	r4	param

                    1847 

                    1848 	.section ".bss","awb"

                    1849 .L1593:

                    1850 	.data

                    1851 	.text

                    1852 

                    1853 

                    1854 	.align	4

                    1855 	.align	4

                    1856 tlsf_check::

000005a8 e92d4ff0   1857 	stmfd	[sp]!,{r4-fp,lr}

000005ac e24dd010   1858 	sub	sp,sp,16

000005b0 e1a05000   1859 	mov	r5,r0

000005b4 e3a04000   1860 	mov	r4,0

000005b8 e1a07004   1861 	mov	r7,r4

                    1862 .L1614:

000005bc e3a0a000   1863 	mov	r10,0

000005c0 e3a08010   1864 	mov	r8,16

000005c4 e2850074   1865 	add	r0,r5,116

000005c8 e080b387   1866 	add	fp,r0,r7 lsl 7

                    1867 .L1793:

000005cc e5950010   1868 	ldr	r0,[r5,16]

000005d0 e3a0c001   1869 	mov	r12,1

000005d4 e000371c   1870 	and	r3,r0,r12 lsl r7

000005d8 e0850107   1871 	add	r0,r5,r7 lsl 2

000005dc e5902014   1872 	ldr	r2,[r0,20]

000005e0 e59b6000   1873 	ldr	r6,[fp]

000005e4 e1a00002   1874 	mov	r0,r2

000005e8 e1100a1c   1875 	tst	r0,r12 lsl r10

000005ec 0a000008   1876 	beq	.L1796

000005f0 e3530000   1877 	cmp	r3,0

000005f4 02444001   1878 	subeq	r4,r4,1

000005f8 e3500000   1879 	cmp	r0,0

000005fc 02444001   1880 	subeq	r4,r4,1


                                                                      Page 32
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
00000600 e1560005   1881 	cmp	r6,r5

00000604 02444001   1882 	subeq	r4,r4,1

00000608 01560005   1883 	cmpeq	r6,r5

0000060c 1a00000c   1884 	bne	.L1802

00000610 ea00002c   1885 	b	.L1979

                    1886 .L1796:

00000614 e1560005   1887 	cmp	r6,r5

00000618 0a00002f   1888 	beq	.L1818

0000061c e2444001   1889 	sub	r4,r4,1

00000620 e1a00002   1890 	mov	r0,r2

00000624 e28a2001   1891 	add	r2,r10,1

00000628 e1a01003   1892 	mov	r1,r3

0000062c e3a03001   1893 	mov	r3,1

00000630 e1100213   1894 	tst	r0,r3 lsl r2

00000634 e59b6004   1895 	ldr	r6,[fp,4]

00000638 e28bb008   1896 	add	fp,fp,8

0000063c 0a000037   1897 	beq	.L1821

00000640 ea00002d   1898 	b	.L1819

                    1899 .L1802:

00000644 e1a00006   1900 	mov	r0,r6

00000648 ebfffe85*  1901 	bl	block_is_free

0000064c e3500000   1902 	cmp	r0,0

00000650 02444001   1903 	subeq	r4,r4,1

00000654 e1a00006   1904 	mov	r0,r6

00000658 ebfffe8c*  1905 	bl	block_is_prev_free

0000065c e3500000   1906 	cmp	r0,0

00000660 12444001   1907 	subne	r4,r4,1

00000664 e1a00006   1908 	mov	r0,r6

00000668 ebfffe99*  1909 	bl	block_next

0000066c e1a09000   1910 	mov	r9,r0

00000670 ebfffe7b*  1911 	bl	block_is_free

00000674 e3500000   1912 	cmp	r0,0

00000678 12444001   1913 	subne	r4,r4,1

0000067c e1a00009   1914 	mov	r0,r9

00000680 ebfffe82*  1915 	bl	block_is_prev_free

00000684 e3500000   1916 	cmp	r0,0

00000688 02444001   1917 	subeq	r4,r4,1

0000068c e1a00006   1918 	mov	r0,r6

00000690 ebfffe6b*  1919 	bl	block_size

00000694 e350000c   1920 	cmp	r0,12

00000698 32444001   1921 	sublo	r4,r4,1

0000069c e28d2004   1922 	add	r2,sp,4

000006a0 e1a0100d   1923 	mov	r1,sp

000006a4 ebfffeba*  1924 	bl	mapping_insert

000006a8 e59d0000   1925 	ldr	r0,[sp]

000006ac e1500007   1926 	cmp	r0,r7

000006b0 059d0004   1927 	ldreq	r0,[sp,4]

000006b4 e5966008   1928 	ldr	r6,[r6,8]

000006b8 0150000a   1929 	cmpeq	r0,r10

000006bc 12444001   1930 	subne	r4,r4,1

000006c0 e1560005   1931 	cmp	r6,r5

000006c4 1affffde   1932 	bne	.L1802

                    1933 .L1979:

000006c8 e5950010   1934 	ldr	r0,[r5,16]

000006cc e3a01001   1935 	mov	r1,1

000006d0 e0003711   1936 	and	r3,r0,r1 lsl r7

000006d4 e0850107   1937 	add	r0,r5,r7 lsl 2

000006d8 e5902014   1938 	ldr	r2,[r0,20]

                    1939 .L1818:

000006dc e1a00002   1940 	mov	r0,r2

000006e0 e28a2001   1941 	add	r2,r10,1


                                                                      Page 33
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
000006e4 e1a01003   1942 	mov	r1,r3

000006e8 e3a03001   1943 	mov	r3,1

000006ec e1100213   1944 	tst	r0,r3 lsl r2

000006f0 e59b6004   1945 	ldr	r6,[fp,4]

000006f4 e28bb008   1946 	add	fp,fp,8

000006f8 0a000008   1947 	beq	.L1821

                    1948 .L1819:

000006fc e3510000   1949 	cmp	r1,0

00000700 02444001   1950 	subeq	r4,r4,1

00000704 e3500000   1951 	cmp	r0,0

00000708 02444001   1952 	subeq	r4,r4,1

0000070c e1560005   1953 	cmp	r6,r5

00000710 02444001   1954 	subeq	r4,r4,1

00000714 01560005   1955 	cmpeq	r6,r5

00000718 1a000007   1956 	bne	.L1827

0000071c ea000028   1957 	b	.L1842

                    1958 .L1821:

00000720 e1560005   1959 	cmp	r6,r5

00000724 0a000026   1960 	beq	.L1842

00000728 e2444001   1961 	sub	r4,r4,1

0000072c e28aa002   1962 	add	r10,r10,2

00000730 e2588001   1963 	subs	r8,r8,1

00000734 1affffa4   1964 	bne	.L1793

00000738 ea000024   1965 	b	.L1615

                    1966 .L1827:

0000073c e1a00006   1967 	mov	r0,r6

00000740 ebfffe47*  1968 	bl	block_is_free

00000744 e3500000   1969 	cmp	r0,0

00000748 02444001   1970 	subeq	r4,r4,1

0000074c e1a00006   1971 	mov	r0,r6

00000750 ebfffe4e*  1972 	bl	block_is_prev_free

00000754 e3500000   1973 	cmp	r0,0

00000758 12444001   1974 	subne	r4,r4,1

0000075c e1a00006   1975 	mov	r0,r6

00000760 ebfffe5b*  1976 	bl	block_next

00000764 e1a09000   1977 	mov	r9,r0

00000768 ebfffe3d*  1978 	bl	block_is_free

0000076c e3500000   1979 	cmp	r0,0

00000770 12444001   1980 	subne	r4,r4,1

00000774 e1a00009   1981 	mov	r0,r9

00000778 ebfffe44*  1982 	bl	block_is_prev_free

0000077c e3500000   1983 	cmp	r0,0

00000780 02444001   1984 	subeq	r4,r4,1

00000784 e1a00006   1985 	mov	r0,r6

00000788 ebfffe2d*  1986 	bl	block_size

0000078c e350000c   1987 	cmp	r0,12

00000790 32444001   1988 	sublo	r4,r4,1

00000794 e28d2004   1989 	add	r2,sp,4

00000798 e1a0100d   1990 	mov	r1,sp

0000079c ebfffe7c*  1991 	bl	mapping_insert

000007a0 e59d0000   1992 	ldr	r0,[sp]

000007a4 e5966008   1993 	ldr	r6,[r6,8]

000007a8 e1500007   1994 	cmp	r0,r7

000007ac 059d1004   1995 	ldreq	r1,[sp,4]

000007b0 028a0001   1996 	addeq	r0,r10,1

000007b4 01510000   1997 	cmpeq	r1,r0

000007b8 12444001   1998 	subne	r4,r4,1

000007bc e1560005   1999 	cmp	r6,r5

000007c0 1affffdd   2000 	bne	.L1827

                    2001 .L1842:

000007c4 e28aa002   2002 	add	r10,r10,2


                                                                      Page 34
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
000007c8 e2588001   2003 	subs	r8,r8,1

000007cc 1affff7e   2004 	bne	.L1793

                    2005 .L1615:

000007d0 e2877001   2006 	add	r7,r7,1

000007d4 e3570018   2007 	cmp	r7,24

000007d8 baffff77   2008 	blt	.L1614

000007dc e1a00004   2009 	mov	r0,r4

000007e0 e28dd010   2010 	add	sp,sp,16

000007e4 e8bd8ff0   2011 	ldmfd	[sp]!,{r4-fp,pc}

                    2012 	.endf	tlsf_check

                    2013 	.align	4

                    2014 ;i	r7	local

                    2015 ;j	r10	local

                    2016 ;control	r5	local

                    2017 ;status	r4	local

                    2018 ;fl_map	r1	local

                    2019 ;sl_list	r0	local

                    2020 ;block	r6	local

                    2021 ;fli	[sp]	local

                    2022 ;sli	[sp,4]	local

                    2023 

                    2024 ;tlsf	r0	param

                    2025 

                    2026 	.section ".bss","awb"

                    2027 .L2240:

                    2028 	.data

                    2029 	.text

                    2030 

                    2031 

                    2032 	.align	4

                    2033 	.align	4

                    2034 default_walker:

000007e8 e12fff1e*  2035 	ret	

                    2036 	.endf	default_walker

                    2037 	.align	4

                    2038 

                    2039 ;ptr	none	param

                    2040 ;size	none	param

                    2041 ;used	none	param

                    2042 ;user	none	param

                    2043 

                    2044 	.section ".bss","awb"

                    2045 .L2382:

                    2046 	.data

                    2047 	.text

                    2048 

                    2049 

                    2050 	.align	4

                    2051 	.align	4

                    2052 tlsf_walk_pool::

000007ec e92d44f0   2053 	stmfd	[sp]!,{r4-r7,r10,lr}

000007f0 e1a05002   2054 	mov	r5,r2

000007f4 e3510000   2055 	cmp	r1,0

000007f8 11a04001   2056 	movne	r4,r1

000007fc 059f45cc*  2057 	ldreq	r4,.L2487

00000800 e3e01003   2058 	mvn	r1,3

00000804 ebfffe30*  2059 	bl	offset_to_block

00000808 e1b06000   2060 	movs	r6,r0

0000080c 0a000013   2061 	beq	.L2389

00000810 ea00000f   2062 	b	.L2400

                    2063 .L2396:


                                                                      Page 35
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
00000814 e1a00006   2064 	mov	r0,r6

00000818 ebfffe11*  2065 	bl	block_is_free

0000081c e3500000   2066 	cmp	r0,0

00000820 03a0a001   2067 	moveq	r10,1

00000824 13a0a000   2068 	movne	r10,0

00000828 e1a00006   2069 	mov	r0,r6

0000082c ebfffe24*  2070 	bl	block_to_ptr

00000830 e1a03005   2071 	mov	r3,r5

00000834 e1a0200a   2072 	mov	r2,r10

00000838 e1a01007   2073 	mov	r1,r7

0000083c e1a0e00f   2074 	mov	lr,pc

00000840 e12fff14*  2075 	bx	r4

00000844 e1a00006   2076 	mov	r0,r6

00000848 ebfffe21*  2077 	bl	block_next

0000084c e1b06000   2078 	movs	r6,r0

00000850 0a000002   2079 	beq	.L2389

                    2080 .L2400:

                    2081 ;380: {


                    2082 

                    2083 ;381: 	return block_size(block) == 0;


                    2084 

00000854 ebfffdfa*  2085 	bl	block_size

00000858 e1b07000   2086 	movs	r7,r0

0000085c 1affffec   2087 	bne	.L2396

                    2088 .L2389:

00000860 e8bd84f0   2089 	ldmfd	[sp]!,{r4-r7,r10,pc}

                    2090 	.endf	tlsf_walk_pool

                    2091 	.align	4

                    2092 ;pool_walker	r4	local

                    2093 ;block	r6	local

                    2094 

                    2095 ;pool	r0	param

                    2096 ;walker	r1	param

                    2097 ;user	r5	param

                    2098 

                    2099 	.section ".bss","awb"

                    2100 .L2467:

                    2101 	.data

                    2102 	.text

                    2103 

                    2104 

                    2105 	.align	4

                    2106 	.align	4

                    2107 tlsf_block_size::

00000864 e92d4000   2108 	stmfd	[sp]!,{lr}

00000868 e3a01000   2109 	mov	r1,0

0000086c e3500000   2110 	cmp	r0,0

00000870 0a000002   2111 	beq	.L2490

00000874 ebfffe10*  2112 	bl	block_from_ptr

00000878 ebfffdf1*  2113 	bl	block_size

0000087c e1a01000   2114 	mov	r1,r0

                    2115 .L2490:

00000880 e1a00001   2116 	mov	r0,r1

00000884 e8bd8000   2117 	ldmfd	[sp]!,{pc}

                    2118 	.endf	tlsf_block_size

                    2119 	.align	4

                    2120 ;size	r1	local

                    2121 

                    2122 ;ptr	r0	param

                    2123 

                    2124 	.section ".bss","awb"


                                                                      Page 36
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
                    2125 .L2525:

                    2126 	.data

                    2127 	.text

                    2128 

                    2129 

                    2130 	.align	4

                    2131 	.align	4

                    2132 tlsf_check_pool::

00000888 e92d4000   2133 	stmfd	[sp]!,{lr}

0000088c e59f2540*  2134 	ldr	r2,.L2567

00000890 e592c000   2135 	ldr	r12,[r2]

00000894 e52dc008   2136 	str	r12,[sp,-8]!

00000898 e5922004   2137 	ldr	r2,[r2,4]

0000089c e59f1534*  2138 	ldr	r1,.L2568

000008a0 e58d2004   2139 	str	r2,[sp,4]

000008a4 e1a0200d   2140 	mov	r2,sp

000008a8 ebffffcf*  2141 	bl	tlsf_walk_pool

000008ac e59d0004   2142 	ldr	r0,[sp,4]

000008b0 e28dd008   2143 	add	sp,sp,8

000008b4 e8bd8000   2144 	ldmfd	[sp]!,{pc}

                    2145 	.endf	tlsf_check_pool

                    2146 	.align	4

                    2147 ;integ	[sp]	local

                    2148 ;.L2559	.L2562	static

                    2149 

                    2150 ;pool	none	param

                    2151 

                    2152 	.section ".bss","awb"

                    2153 .L2558:

                    2154 	.section ".rodata","a"

00000000 00000000   2155 .L2562:	.space	4

00000004 00000000   2156 	.space	4

                    2157 	.type	.L2562,$object

                    2158 	.size	.L2562,8

                    2159 	.data

                    2160 	.text

                    2161 

                    2162 

                    2163 	.align	4

                    2164 	.align	4

                    2165 tlsf_pool_overhead::

000008b8 e3a00008   2166 	mov	r0,8

000008bc e12fff1e*  2167 	ret	

                    2168 	.endf	tlsf_pool_overhead

                    2169 	.align	4

                    2170 

                    2171 	.section ".bss","awb"

                    2172 .L2590:

                    2173 	.data

                    2174 	.text

                    2175 

                    2176 

                    2177 ;982: 


                    2178 ;983: pool_t tlsf_add_pool(tlsf_t tlsf, void* mem, size_t bytes)


                    2179 	.align	4

                    2180 	.align	4

                    2181 tlsf_add_pool::

000008c0 e92d4070   2182 	stmfd	[sp]!,{r4-r6,lr}

000008c4 e1a06000   2183 	mov	r6,r0

000008c8 e1a04001   2184 	mov	r4,r1

000008cc e1a05002   2185 	mov	r5,r2


                                                                      Page 37
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
                    2186 ;984: {


                    2187 

                    2188 ;985: 	block_header_t* block;


                    2189 ;986: 	block_header_t* next;


                    2190 ;987: 


                    2191 ;988: 	const size_t pool_overhead = tlsf_pool_overhead();


                    2192 

000008d0 ebfffff8*  2193 	bl	tlsf_pool_overhead

                    2194 ;989: 	const size_t pool_bytes = align_down(bytes - pool_overhead, ALIGN_SIZE);


                    2195 

000008d4 e0450000   2196 	sub	r0,r5,r0

                    2197 ;478: {


                    2198 

                    2199 ;479: 	tlsf_assert(0 == (align & (align - 1)) && "must align to a power of two");


                    2200 ;480: 	return x - (x & (align - 1));


                    2201 

000008d8 e3c05003   2202 	bic	r5,r0,3

                    2203 ;990: 


                    2204 ;991: 	if (((ptrdiff_t)mem % ALIGN_SIZE) != 0)


                    2205 

000008dc e1a00fc4   2206 	mov	r0,r4 asr 31

000008e0 e2000003   2207 	and	r0,r0,3

000008e4 e0840000   2208 	add	r0,r4,r0

000008e8 e3c00003   2209 	bic	r0,r0,3

000008ec e1540000   2210 	cmp	r4,r0

000008f0 1a000002   2211 	bne	.L2607

                    2212 ;992: 	{


                    2213 

                    2214 ;993: 		printf("tlsf_add_pool: Memory must be aligned by %u bytes.\n",


                    2215 ;994: 			(unsigned int)ALIGN_SIZE);


                    2216 ;995: 		return 0;


                    2217 

                    2218 ;996: 	}


                    2219 ;997: 


                    2220 ;998: 	if (pool_bytes < block_size_min || pool_bytes > block_size_max)


                    2221 

000008f4 e355000c   2222 	cmp	r5,12

000008f8 22751440   2223 	rsbhss	r1,r5,1<<30

000008fc 2a000001   2224 	bhs	.L2606

                    2225 .L2607:

                    2226 ;999: 	{


                    2227 

                    2228 ;1000: #if defined (TLSF_64BIT)


                    2229 ;1001: 		printf("tlsf_add_pool: Memory size must be between 0x%x and 0x%x00 bytes.\n", 


                    2230 ;1002: 			(unsigned int)(pool_overhead + block_size_min),


                    2231 ;1003: 			(unsigned int)((pool_overhead + block_size_max) / 256));


                    2232 ;1004: #else


                    2233 ;1005: 		printf("tlsf_add_pool: Memory size must be between %u and %u bytes.\n", 


                    2234 ;1006: 			(unsigned int)(pool_overhead + block_size_min),


                    2235 ;1007: 			(unsigned int)(pool_overhead + block_size_max));


                    2236 ;1008: #endif


                    2237 ;1009: 		return 0;


                    2238 

00000900 e3a00000   2239 	mov	r0,0

00000904 ea000016   2240 	b	.L2597

                    2241 .L2606:

                    2242 ;1010: 	}


                    2243 ;1011: 


                    2244 ;1012: 	/*


                    2245 ;1013: 	** Create the main free block. Offset the start of the block slightly


                    2246 ;1014: 	** so that the prev_phys_block field falls outside of the pool -



                                                                      Page 38
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
                    2247 ;1015: 	** it will never be used.


                    2248 ;1016: 	*/


                    2249 ;1017: 	block = offset_to_block(mem, -(tlsfptr_t)block_header_overhead);


                    2250 

00000908 e3e01003   2251 	mvn	r1,3

0000090c e1a00004   2252 	mov	r0,r4

00000910 ebfffded*  2253 	bl	offset_to_block

                    2254 ;1018: 	block_set_size(block, pool_bytes);


                    2255 

00000914 e1a01005   2256 	mov	r1,r5

00000918 e1a05000   2257 	mov	r5,r0

0000091c ebfffdcb*  2258 	bl	block_set_size

                    2259 ;1019: 	block_set_free(block);


                    2260 

00000920 e1a00005   2261 	mov	r0,r5

00000924 ebfffdd1*  2262 	bl	block_set_free

                    2263 ;1020: 	block_set_prev_used(block);


                    2264 

00000928 e1a00005   2265 	mov	r0,r5

0000092c ebfffdde*  2266 	bl	block_set_prev_used

                    2267 ;1021: 	block_insert(tlsf_cast(control_t*, tlsf), block);


                    2268 

00000930 e1a01005   2269 	mov	r1,r5

00000934 e1a00006   2270 	mov	r0,r6

00000938 ebfffe52*  2271 	bl	block_insert

                    2272 ;1022: 


                    2273 ;1023: 	/* Split the block to create a zero-size sentinel block. */


                    2274 ;1024: 	next = block_link_next(block);


                    2275 

0000093c e1a00005   2276 	mov	r0,r5

00000940 ebfffded*  2277 	bl	block_link_next

                    2278 ;1025: 	block_set_size(next, 0);


                    2279 

00000944 e1a05000   2280 	mov	r5,r0

00000948 e3a01000   2281 	mov	r1,0

0000094c ebfffdbf*  2282 	bl	block_set_size

                    2283 ;1026: 	block_set_used(next);


                    2284 

00000950 e1a00005   2285 	mov	r0,r5

00000954 ebfffdc9*  2286 	bl	block_set_used

                    2287 ;1027: 	block_set_prev_free(next);


                    2288 

00000958 e1a00005   2289 	mov	r0,r5

0000095c ebfffdce*  2290 	bl	block_set_prev_free

                    2291 ;1028: 


                    2292 ;1029: 	return mem;


                    2293 

00000960 e1a00004   2294 	mov	r0,r4

                    2295 .L2597:

00000964 e8bd8070   2296 	ldmfd	[sp]!,{r4-r6,pc}

                    2297 	.endf	tlsf_add_pool

                    2298 	.align	4

                    2299 ;block	r5	local

                    2300 ;next	r5	local

                    2301 ;pool_bytes	r5	local

                    2302 ;x	r0	local

                    2303 

                    2304 ;tlsf	r6	param

                    2305 ;mem	r4	param

                    2306 ;bytes	r5	param

                    2307 


                                                                      Page 39
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
                    2308 	.section ".bss","awb"

                    2309 .L2676:

                    2310 	.data

                    2311 	.text

                    2312 

                    2313 ;1030: }


                    2314 

                    2315 ;1031: 


                    2316 ;1032: void tlsf_remove_pool(tlsf_t tlsf, pool_t pool)


                    2317 	.align	4

                    2318 	.align	4

                    2319 tlsf_remove_pool::

00000968 e92d4030   2320 	stmfd	[sp]!,{r4-r5,lr}

                    2321 ;1033: {


                    2322 

0000096c e24dd008   2323 	sub	sp,sp,8

00000970 e1a05000   2324 	mov	r5,r0

00000974 e3a02000   2325 	mov	r2,0

00000978 e1a00002   2326 	mov	r0,r2

0000097c e88d0005   2327 	stmea	[sp],{r0,r2}

                    2328 ;1034: 	control_t* control = tlsf_cast(control_t*, tlsf);


                    2329 

                    2330 ;1035: 	block_header_t* block = offset_to_block(pool, -(int)block_header_overhead);


                    2331 

00000980 e1a00001   2332 	mov	r0,r1

00000984 e3e01003   2333 	mvn	r1,3

00000988 ebfffdcf*  2334 	bl	offset_to_block

                    2335 ;1036: 


                    2336 ;1037: 	int fl = 0, sl = 0;


                    2337 

                    2338 ;1038: 


                    2339 ;1039: 	tlsf_assert(block_is_free(block) && "block should be free");


                    2340 ;1040: 	tlsf_assert(!block_is_free(block_next(block)) && "next block should not be free");


                    2341 ;1041: 	tlsf_assert(block_size(block_next(block)) == 0 && "next block size should be zero");


                    2342 ;1042: 


                    2343 ;1043: 	mapping_insert(block_size(block), &fl, &sl);


                    2344 

0000098c e1a04000   2345 	mov	r4,r0

00000990 ebfffdab*  2346 	bl	block_size

00000994 e28d2004   2347 	add	r2,sp,4

00000998 e1a0100d   2348 	mov	r1,sp

0000099c ebfffdfc*  2349 	bl	mapping_insert

                    2350 ;1044: 	remove_free_block(control, block, fl, sl);


                    2351 

000009a0 e89d000c   2352 	ldmfd	[sp],{r2-r3}

000009a4 e1a01004   2353 	mov	r1,r4

000009a8 e1a00005   2354 	mov	r0,r5

000009ac ebfffe0b*  2355 	bl	remove_free_block

000009b0 e28dd008   2356 	add	sp,sp,8

000009b4 e8bd8030   2357 	ldmfd	[sp]!,{r4-r5,pc}

                    2358 	.endf	tlsf_remove_pool

                    2359 	.align	4

                    2360 ;block	r4	local

                    2361 ;fl	[sp]	local

                    2362 ;sl	[sp,4]	local

                    2363 

                    2364 ;tlsf	r5	param

                    2365 ;pool	r1	param

                    2366 

                    2367 	.section ".bss","awb"

                    2368 .L2718:


                                                                      Page 40
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
                    2369 	.data

                    2370 	.text

                    2371 

                    2372 ;1045: }


                    2373 

                    2374 ;1046: 


                    2375 ;1047: /*


                    2376 ;1048: ** TLSF main interface.


                    2377 ;1049: */


                    2378 ;1050: 


                    2379 ;1051: #if _DEBUG


                    2380 ;1052: int test_ffs_fls()


                    2381 ;1053: {


                    2382 ;1054: 	/* Verify ffs/fls work properly. */


                    2383 ;1055: 	int rv = 0;


                    2384 ;1056: 	rv += (tlsf_ffs(0) == -1) ? 0 : 0x1;


                    2385 ;1057: 	rv += (tlsf_fls(0) == -1) ? 0 : 0x2;


                    2386 ;1058: 	rv += (tlsf_ffs(1) == 0) ? 0 : 0x4;


                    2387 ;1059: 	rv += (tlsf_fls(1) == 0) ? 0 : 0x8;


                    2388 ;1060: 	rv += (tlsf_ffs(0x80000000) == 31) ? 0 : 0x10;


                    2389 ;1061: 	rv += (tlsf_ffs(0x80008000) == 15) ? 0 : 0x20;


                    2390 ;1062: 	rv += (tlsf_fls(0x80000008) == 31) ? 0 : 0x40;


                    2391 ;1063: 	rv += (tlsf_fls(0x7FFFFFFF) == 30) ? 0 : 0x80;


                    2392 ;1064: 


                    2393 ;1065: #if defined (TLSF_64BIT)


                    2394 ;1066: 	rv += (tlsf_fls_sizet(0x80000000) == 31) ? 0 : 0x100;


                    2395 ;1067: 	rv += (tlsf_fls_sizet(0x100000000) == 32) ? 0 : 0x200;


                    2396 ;1068: 	rv += (tlsf_fls_sizet(0xffffffffffffffff) == 63) ? 0 : 0x400;


                    2397 ;1069: #endif


                    2398 ;1070: 


                    2399 ;1071: 	if (rv)


                    2400 ;1072: 	{


                    2401 ;1073: 		printf("test_ffs_fls: %x ffs/fls tests failed.\n", rv);


                    2402 ;1074: 	}


                    2403 ;1075: 	return rv;


                    2404 ;1076: }


                    2405 ;1077: #endif


                    2406 ;1078: 


                    2407 ;1079: tlsf_t tlsf_create(void* mem)


                    2408 	.align	4

                    2409 	.align	4

                    2410 tlsf_create::

000009b8 e92d0030   2411 	stmfd	[sp]!,{r4-r5}

                    2412 ;1080: {


                    2413 

                    2414 ;1081: #if _DEBUG


                    2415 ;1082: 	if (test_ffs_fls())


                    2416 ;1083: 	{


                    2417 ;1084: 		return 0;


                    2418 ;1085: 	}


                    2419 ;1086: #endif


                    2420 ;1087: 


                    2421 ;1088: 	if (((tlsfptr_t)mem % ALIGN_SIZE) != 0)


                    2422 

000009bc e1a01fc0   2423 	mov	r1,r0 asr 31

000009c0 e2011003   2424 	and	r1,r1,3

000009c4 e0801001   2425 	add	r1,r0,r1

000009c8 e3c11003   2426 	bic	r1,r1,3

000009cc e1500001   2427 	cmp	r0,r1

                    2428 ;1089: 	{


                    2429 


                                                                      Page 41
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
                    2430 ;1090: 		printf("tlsf_create: Memory must be aligned to %u bytes.\n",


                    2431 ;1091: 			(unsigned int)ALIGN_SIZE);


                    2432 ;1092: 		return 0;


                    2433 

000009d0 13a00000   2434 	movne	r0,0

000009d4 1a000021   2435 	bne	.L2725

000009d8 e5800008   2436 	str	r0,[r0,8]

                    2437 ;805: 	control->block_null.prev_free = &control->block_null;


                    2438 

000009dc e580000c   2439 	str	r0,[r0,12]

                    2440 ;806: 


                    2441 ;807: 	control->fl_bitmap = 0;


                    2442 

000009e0 e3a03000   2443 	mov	r3,0

                    2444 ;1093: 	}


                    2445 ;1094: 


                    2446 ;1095: 	control_construct(tlsf_cast(control_t*, mem));


                    2447 

                    2448 ;801: {


                    2449 

                    2450 ;802: 	int i, j;


                    2451 ;803: 


                    2452 ;804: 	control->block_null.next_free = &control->block_null;


                    2453 

000009e4 e5803010   2454 	str	r3,[r0,16]

                    2455 ;808: 	for (i = 0; i < FL_INDEX_COUNT; ++i)


                    2456 

000009e8 e1a01003   2457 	mov	r1,r3

000009ec e3a0200c   2458 	mov	r2,12

                    2459 .L2796:

000009f0 e3a04004   2460 	mov	r4,4

000009f4 e080c101   2461 	add	r12,r0,r1 lsl 2

000009f8 e58c3014   2462 	str	r3,[r12,20]

000009fc e280c074   2463 	add	r12,r0,116

00000a00 e08cc381   2464 	add	r12,r12,r1 lsl 7

                    2465 .L2804:

00000a04 e1a05000   2466 	mov	r5,r0

00000a08 e8ac0021   2467 	stmea	[r12]!,{r0,r5}

00000a0c e8ac0021   2468 	stmea	[r12]!,{r0,r5}

00000a10 e8ac0021   2469 	stmea	[r12]!,{r0,r5}

00000a14 e8ac0021   2470 	stmea	[r12]!,{r0,r5}

00000a18 e2544001   2471 	subs	r4,r4,1

00000a1c 1afffff8   2472 	bne	.L2804

00000a20 e281c001   2473 	add	r12,r1,1

00000a24 e080410c   2474 	add	r4,r0,r12 lsl 2

00000a28 e5843014   2475 	str	r3,[r4,20]

00000a2c e3a04004   2476 	mov	r4,4

00000a30 e2805074   2477 	add	r5,r0,116

00000a34 e085c38c   2478 	add	r12,r5,r12 lsl 7

                    2479 .L2814:

00000a38 e1a05000   2480 	mov	r5,r0

00000a3c e8ac0021   2481 	stmea	[r12]!,{r0,r5}

00000a40 e8ac0021   2482 	stmea	[r12]!,{r0,r5}

00000a44 e8ac0021   2483 	stmea	[r12]!,{r0,r5}

00000a48 e8ac0021   2484 	stmea	[r12]!,{r0,r5}

00000a4c e2544001   2485 	subs	r4,r4,1

00000a50 1afffff8   2486 	bne	.L2814

00000a54 e2811002   2487 	add	r1,r1,2

00000a58 e2522001   2488 	subs	r2,r2,1

                    2489 ;1096: 


                    2490 ;1097: 	return tlsf_cast(tlsf_t, mem);



                                                                      Page 42
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
                    2491 

00000a5c 1affffe3   2492 	bne	.L2796

                    2493 .L2725:

00000a60 e8bd0030   2494 	ldmfd	[sp]!,{r4-r5}

00000a64 e12fff1e*  2495 	ret	

                    2496 	.endf	tlsf_create

                    2497 	.align	4

                    2498 ;i	r1	local

                    2499 

                    2500 ;mem	r0	param

                    2501 

                    2502 	.section ".bss","awb"

                    2503 .L2952:

                    2504 	.data

                    2505 	.text

                    2506 

                    2507 ;1098: }


                    2508 

                    2509 ;1099: 


                    2510 ;1100: tlsf_t tlsf_create_with_pool(void* mem, size_t bytes)


                    2511 	.align	4

                    2512 	.align	4

                    2513 tlsf_create_with_pool::

00000a68 e92d4030   2514 	stmfd	[sp]!,{r4-r5,lr}

                    2515 ;1101: {


                    2516 

                    2517 ;1102: 	tlsf_t tlsf = tlsf_create(mem);


                    2518 

00000a6c e1a05001   2519 	mov	r5,r1

00000a70 e1a04000   2520 	mov	r4,r0

00000a74 ebffffcf*  2521 	bl	tlsf_create

                    2522 ;1103: 	tlsf_add_pool(tlsf, (char*)mem + tlsf_size(), bytes - tlsf_size());


                    2523 

                    2524 ;949: {


                    2525 

                    2526 ;950: 	return sizeof(control_t);


                    2527 

                    2528 ;949: {


                    2529 

                    2530 ;950: 	return sizeof(control_t);


                    2531 

00000a78 e3a03ec0   2532 	mov	r3,3<<10

00000a7c e2833074   2533 	add	r3,r3,116

00000a80 e0452003   2534 	sub	r2,r5,r3

00000a84 e0841003   2535 	add	r1,r4,r3

00000a88 e1a04000   2536 	mov	r4,r0

00000a8c ebffff8b*  2537 	bl	tlsf_add_pool

                    2538 ;1104: 	return tlsf;


                    2539 

00000a90 e1a00004   2540 	mov	r0,r4

00000a94 e8bd8030   2541 	ldmfd	[sp]!,{r4-r5,pc}

                    2542 	.endf	tlsf_create_with_pool

                    2543 	.align	4

                    2544 

                    2545 ;mem	r4	param

                    2546 ;bytes	r5	param

                    2547 

                    2548 	.section ".bss","awb"

                    2549 .L3022:

                    2550 	.data

                    2551 	.text


                                                                      Page 43
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
                    2552 

                    2553 ;1105: }


                    2554 

                    2555 ;1106: 


                    2556 ;1107: void tlsf_destroy(tlsf_t tlsf)


                    2557 	.align	4

                    2558 	.align	4

                    2559 tlsf_destroy::

                    2560 ;1108: {


                    2561 

                    2562 ;1109: 	/* Nothing to do. */


                    2563 ;1110: 	(void)tlsf;


                    2564 

00000a98 e12fff1e*  2565 	ret	

                    2566 	.endf	tlsf_destroy

                    2567 	.align	4

                    2568 

                    2569 ;tlsf	none	param

                    2570 

                    2571 	.section ".bss","awb"

                    2572 .L3054:

                    2573 	.data

                    2574 	.text

                    2575 

                    2576 ;1111: }


                    2577 

                    2578 ;1112: 


                    2579 ;1113: pool_t tlsf_get_pool(tlsf_t tlsf)


                    2580 	.align	4

                    2581 	.align	4

                    2582 tlsf_get_pool::

                    2583 ;1114: {


                    2584 

                    2585 ;1115: 	return tlsf_cast(pool_t, (char*)tlsf + tlsf_size());


                    2586 

                    2587 ;949: {


                    2588 

                    2589 ;950: 	return sizeof(control_t);


                    2590 

00000a9c e3a01ec0   2591 	mov	r1,3<<10

00000aa0 e2811074   2592 	add	r1,r1,116

00000aa4 e0800001   2593 	add	r0,r0,r1

00000aa8 e12fff1e*  2594 	ret	

                    2595 	.endf	tlsf_get_pool

                    2596 	.align	4

                    2597 

                    2598 ;tlsf	r0	param

                    2599 

                    2600 	.section ".bss","awb"

                    2601 .L3086:

                    2602 	.data

                    2603 	.text

                    2604 

                    2605 ;1116: }


                    2606 

                    2607 ;1117: 


                    2608 ;1118: void* tlsf_malloc(tlsf_t tlsf, size_t size)


                    2609 	.align	4

                    2610 	.align	4

                    2611 tlsf_malloc::

00000aac e92d4030   2612 	stmfd	[sp]!,{r4-r5,lr}


                                                                      Page 44
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
00000ab0 e1a05000   2613 	mov	r5,r0

                    2614 ;1119: {


                    2615 

                    2616 ;1120: 	control_t* control = tlsf_cast(control_t*, tlsf);


                    2617 

                    2618 ;1121: 	const size_t adjust = adjust_request_size(size, ALIGN_SIZE);


                    2619 

00000ab4 e1a00001   2620 	mov	r0,r1

00000ab8 e3a01004   2621 	mov	r1,4

00000abc ebfffda8*  2622 	bl	adjust_request_size

00000ac0 e1a04000   2623 	mov	r4,r0

                    2624 ;1122: 	block_header_t* block = block_locate_free(control, adjust);


                    2625 

00000ac4 e1a01004   2626 	mov	r1,r4

00000ac8 e1a00005   2627 	mov	r0,r5

00000acc ebfffe46*  2628 	bl	block_locate_free

                    2629 ;1123: 	return block_prepare_used(control, block, adjust);


                    2630 

00000ad0 e1a02004   2631 	mov	r2,r4

00000ad4 e1a01000   2632 	mov	r1,r0

00000ad8 e1a00005   2633 	mov	r0,r5

00000adc e8bd4030   2634 	ldmfd	[sp]!,{r4-r5,lr}

00000ae0 eafffe7c*  2635 	b	block_prepare_used

                    2636 	.endf	tlsf_malloc

                    2637 	.align	4

                    2638 ;adjust	r4	local

                    2639 

                    2640 ;tlsf	r5	param

                    2641 ;size	r1	param

                    2642 

                    2643 	.section ".bss","awb"

                    2644 .L3118:

                    2645 	.data

                    2646 	.text

                    2647 

                    2648 ;1124: }


                    2649 

                    2650 ;1125: 


                    2651 ;1126: void* tlsf_memalign(tlsf_t tlsf, size_t align, size_t size)


                    2652 	.align	4

                    2653 	.align	4

                    2654 tlsf_memalign::

00000ae4 e92d4cf0   2655 	stmfd	[sp]!,{r4-r7,r10-fp,lr}

                    2656 ;1127: {


                    2657 

                    2658 ;1128: 	control_t* control = tlsf_cast(control_t*, tlsf);


                    2659 

00000ae8 e1a05000   2660 	mov	r5,r0

                    2661 ;1129: 	const size_t adjust = adjust_request_size(size, ALIGN_SIZE);


                    2662 

00000aec e1a00002   2663 	mov	r0,r2

00000af0 e1a0a001   2664 	mov	r10,r1

00000af4 e3a01004   2665 	mov	r1,4

00000af8 ebfffd99*  2666 	bl	adjust_request_size

00000afc e1a0100a   2667 	mov	r1,r10

00000b00 e1a04000   2668 	mov	r4,r0

                    2669 ;1130: 


                    2670 ;1131: 	/*


                    2671 ;1132: 	** We must allocate an additional minimum block size bytes so that if


                    2672 ;1133: 	** our free block will leave an alignment gap which is smaller, we can


                    2673 ;1134: 	** trim a leading free block and release it back to the pool. We must



                                                                      Page 45
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
                    2674 ;1135: 	** do this because the previous physical block is in use, therefore


                    2675 ;1136: 	** the prev_phys_block field is not valid, and we can't simply adjust


                    2676 ;1137: 	** the size of that block.


                    2677 ;1138: 	*/


                    2678 ;1139: 	const size_t gap_minimum = sizeof(block_header_t);


                    2679 

                    2680 ;1140: 	const size_t size_with_gap = adjust_request_size(adjust + align + gap_minimum, align);


                    2681 

00000b04 e084000a   2682 	add	r0,r4,r10

00000b08 e2800010   2683 	add	r0,r0,16

00000b0c ebfffd94*  2684 	bl	adjust_request_size

                    2685 ;1141: 


                    2686 ;1142: 	/*


                    2687 ;1143: 	** If alignment is less than or equals base alignment, we're done.


                    2688 ;1144: 	** If we requested 0 bytes, return null, as tlsf_malloc(0) does.


                    2689 ;1145: 	*/


                    2690 ;1146: 	const size_t aligned_size = (adjust && align > ALIGN_SIZE) ? size_with_gap : adjust;


                    2691 

00000b10 e3540000   2692 	cmp	r4,0

00000b14 0a000007   2693 	beq	.L3127

00000b18 e35a0004   2694 	cmp	r10,4

00000b1c 91a01004   2695 	movls	r1,r4

00000b20 81a01000   2696 	movhi	r1,r0

                    2697 ;1147: 


                    2698 ;1148: 	block_header_t* block = block_locate_free(control, aligned_size);


                    2699 

00000b24 e1a00005   2700 	mov	r0,r5

00000b28 ebfffe2f*  2701 	bl	block_locate_free

00000b2c e1b06000   2702 	movs	r6,r0

                    2703 ;1149: 


                    2704 ;1150: 	/* This can't be a static assert. */


                    2705 ;1151: 	tlsf_assert(sizeof(block_header_t) == block_size_min + block_header_overhead);


                    2706 ;1152: 


                    2707 ;1153: 	if (block)


                    2708 

00000b30 0a000026   2709 	beq	.L3131

00000b34 ea000004   2710 	b	.L3132

                    2711 .L3127:

00000b38 e1a01004   2712 	mov	r1,r4

                    2713 ;1147: 


                    2714 ;1148: 	block_header_t* block = block_locate_free(control, aligned_size);


                    2715 

00000b3c e1a00005   2716 	mov	r0,r5

00000b40 ebfffe29*  2717 	bl	block_locate_free

00000b44 e1b06000   2718 	movs	r6,r0

                    2719 ;1149: 


                    2720 ;1150: 	/* This can't be a static assert. */


                    2721 ;1151: 	tlsf_assert(sizeof(block_header_t) == block_size_min + block_header_overhead);


                    2722 ;1152: 


                    2723 ;1153: 	if (block)


                    2724 

00000b48 0a000020   2725 	beq	.L3131

                    2726 .L3132:

                    2727 ;1154: 	{


                    2728 

                    2729 ;1155: 		void* ptr = block_to_ptr(block);


                    2730 

00000b4c ebfffd5c*  2731 	bl	block_to_ptr

                    2732 ;1156: 		void* aligned = align_ptr(ptr, align);


                    2733 

00000b50 e1a0100a   2734 	mov	r1,r10


                                                                      Page 46
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
00000b54 e1a0b000   2735 	mov	fp,r0

00000b58 ebfffd7d*  2736 	bl	align_ptr

                    2737 ;1157: 		size_t gap = tlsf_cast(size_t,


                    2738 

00000b5c e050700b   2739 	subs	r7,r0,fp

                    2740 ;1158: 			tlsf_cast(tlsfptr_t, aligned) - tlsf_cast(tlsfptr_t, ptr));


                    2741 ;1159: 


                    2742 ;1160: 		/* If gap size is too small, offset to next aligned boundary. */


                    2743 ;1161: 		if (gap && gap < gap_minimum)


                    2744 

00000b60 0a00001a   2745 	beq	.L3131

00000b64 e3570010   2746 	cmp	r7,16

00000b68 2a000007   2747 	bhs	.L3140

00000b6c e2671010   2748 	rsb	r1,r7,16

00000b70 e15a0001   2749 	cmp	r10,r1

00000b74 21a0100a   2750 	movhs	r1,r10

00000b78 e0810000   2751 	add	r0,r1,r0

00000b7c e1a0100a   2752 	mov	r1,r10

00000b80 ebfffd73*  2753 	bl	align_ptr

00000b84 e050700b   2754 	subs	r7,r0,fp

                    2755 ;1162: 		{


                    2756 

                    2757 ;1163: 			const size_t gap_remain = gap_minimum - gap;


                    2758 

                    2759 ;1164: 			const size_t offset = tlsf_max(gap_remain, align);


                    2760 

                    2761 ;1165: 			const void* next_aligned = tlsf_cast(void*,


                    2762 

                    2763 ;1166: 				tlsf_cast(tlsfptr_t, aligned) + offset);


                    2764 ;1167: 


                    2765 ;1168: 			aligned = align_ptr(next_aligned, align);


                    2766 

                    2767 ;1169: 			gap = tlsf_cast(size_t,


                    2768 

                    2769 ;1170: 				tlsf_cast(tlsfptr_t, aligned) - tlsf_cast(tlsfptr_t, ptr));


                    2770 ;1171: 		}


                    2771 ;1172: 


                    2772 ;1173: 		if (gap)


                    2773 

00000b88 0a000010   2774 	beq	.L3131

                    2775 .L3140:

                    2776 ;1174: 		{


                    2777 

                    2778 ;1175: 			tlsf_assert(gap >= gap_minimum && "gap size too small");


                    2779 ;1176: 			block = block_trim_free_leading(control, block, gap);


                    2780 

                    2781 ;741: {


                    2782 

                    2783 ;742: 	block_header_t* remaining_block = block;


                    2784 

00000b8c e1a0a006   2785 	mov	r10,r6

                    2786 ;743: 	if (block_can_split(block, size))


                    2787 

00000b90 e1a01007   2788 	mov	r1,r7

00000b94 e1a00006   2789 	mov	r0,r6

00000b98 ebfffdd6*  2790 	bl	block_can_split

00000b9c e3500000   2791 	cmp	r0,0

00000ba0 0a000009   2792 	beq	.L3138

                    2793 ;744: 	{


                    2794 

                    2795 ;745: 		/* We want the 2nd block. */



                                                                      Page 47
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
                    2796 ;746: 		remaining_block = block_split(block, size - block_header_overhead);


                    2797 

00000ba4 e2471004   2798 	sub	r1,r7,4

00000ba8 e1a00006   2799 	mov	r0,r6

00000bac ebfffdd9*  2800 	bl	block_split

                    2801 ;747: 		block_set_prev_free(remaining_block);


                    2802 

00000bb0 e1a0a000   2803 	mov	r10,r0

00000bb4 ebfffd38*  2804 	bl	block_set_prev_free

                    2805 ;748: 


                    2806 ;749: 		block_link_next(block);


                    2807 

00000bb8 e1a00006   2808 	mov	r0,r6

00000bbc ebfffd4e*  2809 	bl	block_link_next

                    2810 ;750: 		block_insert(control, block);


                    2811 

00000bc0 e1a01006   2812 	mov	r1,r6

00000bc4 e1a00005   2813 	mov	r0,r5

00000bc8 ebfffdae*  2814 	bl	block_insert

                    2815 .L3138:

                    2816 ;751: 	}


                    2817 ;752: 


                    2818 ;753: 	return remaining_block;


                    2819 

00000bcc e1a0600a   2820 	mov	r6,r10

                    2821 .L3131:

                    2822 ;1177: 		}


                    2823 ;1178: 	}


                    2824 ;1179: 


                    2825 ;1180: 	return block_prepare_used(control, block, adjust);


                    2826 

00000bd0 e1a02004   2827 	mov	r2,r4

00000bd4 e1a01006   2828 	mov	r1,r6

00000bd8 e1a00005   2829 	mov	r0,r5

00000bdc e8bd4cf0   2830 	ldmfd	[sp]!,{r4-r7,r10-fp,lr}

00000be0 eafffe3c*  2831 	b	block_prepare_used

                    2832 	.endf	tlsf_memalign

                    2833 	.align	4

                    2834 ;control	r5	local

                    2835 ;adjust	r4	local

                    2836 ;size_with_gap	r0	local

                    2837 ;block	r6	local

                    2838 ;ptr	fp	local

                    2839 ;aligned	r0	local

                    2840 ;gap	r7	local

                    2841 ;gap_remain	r1	local

                    2842 ;offset	r1	local

                    2843 ;remaining_block	r10	local

                    2844 

                    2845 ;tlsf	r0	param

                    2846 ;align	r10	param

                    2847 ;size	r2	param

                    2848 

                    2849 	.section ".bss","awb"

                    2850 .L3269:

                    2851 	.data

                    2852 	.text

                    2853 

                    2854 ;1181: }


                    2855 

                    2856 ;1182: 



                                                                      Page 48
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
                    2857 ;1183: void tlsf_free(tlsf_t tlsf, void* ptr)


                    2858 	.align	4

                    2859 	.align	4

                    2860 tlsf_free::

00000be4 e92d4070   2861 	stmfd	[sp]!,{r4-r6,lr}

                    2862 ;1184: {


                    2863 

                    2864 ;1185: 	/* Don't attempt to free a NULL pointer. */


                    2865 ;1186: 	if (ptr)


                    2866 

00000be8 e3510000   2867 	cmp	r1,0

00000bec 0a000017   2868 	beq	.L3294

                    2869 ;1187: 	{


                    2870 

                    2871 ;1188: 		control_t* control = tlsf_cast(control_t*, tlsf);


                    2872 

00000bf0 e1a04000   2873 	mov	r4,r0

                    2874 ;1189: 		block_header_t* block = block_from_ptr(ptr);


                    2875 

00000bf4 e1a00001   2876 	mov	r0,r1

00000bf8 ebfffd2f*  2877 	bl	block_from_ptr

                    2878 ;1190: 		tlsf_assert(!block_is_free(block) && "block already marked as free");


                    2879 ;1191: 		block_mark_as_free(block);


                    2880 

00000bfc e1a05000   2881 	mov	r5,r0

00000c00 ebfffd43*  2882 	bl	block_mark_as_free

                    2883 ;1192: 		block = block_merge_prev(control, block);


                    2884 

                    2885 ;683: {


                    2886 

                    2887 ;684: 	if (block_is_prev_free(block))


                    2888 

00000c04 e1a00005   2889 	mov	r0,r5

00000c08 ebfffd20*  2890 	bl	block_is_prev_free

00000c0c e3500000   2891 	cmp	r0,0

00000c10 0a000007   2892 	beq	.L3298

                    2893 ;685: 	{


                    2894 

                    2895 ;686: 		block_header_t* prev = block_prev(block);


                    2896 

                    2897 ;434: {


                    2898 

                    2899 ;435: 	tlsf_assert(block_is_prev_free(block) && "previous block must be free");


                    2900 ;436: 	return block->prev_phys_block;


                    2901 

00000c14 e5956000   2902 	ldr	r6,[r5]

                    2903 ;687: 		tlsf_assert(prev && "prev physical block can't be null");


                    2904 ;688: 		tlsf_assert(block_is_free(prev) && "prev block is not free though marked as such");


                    2905 ;689: 		block_remove(control, prev);


                    2906 

00000c18 e1a00004   2907 	mov	r0,r4

00000c1c e1a01006   2908 	mov	r1,r6

00000c20 ebfffd88*  2909 	bl	block_remove

                    2910 ;690: 		block = block_absorb(prev, block);


                    2911 

00000c24 e1a01005   2912 	mov	r1,r5

00000c28 e1a00006   2913 	mov	r0,r6

00000c2c ebfffdce*  2914 	bl	block_absorb

00000c30 e1a05000   2915 	mov	r5,r0

                    2916 .L3298:

                    2917 ;691: 	}



                                                                      Page 49
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
                    2918 ;692: 


                    2919 ;693: 	return block;


                    2920 

                    2921 ;1193: 		block = block_merge_next(control, block);


                    2922 

00000c34 e1a01005   2923 	mov	r1,r5

00000c38 e1a00004   2924 	mov	r0,r4

00000c3c ebfffdd7*  2925 	bl	block_merge_next

                    2926 ;1194: 		block_insert(control, block);


                    2927 

00000c40 e1a01000   2928 	mov	r1,r0

00000c44 e1a00004   2929 	mov	r0,r4

00000c48 e8bd4070   2930 	ldmfd	[sp]!,{r4-r6,lr}

00000c4c eafffd8d*  2931 	b	block_insert

                    2932 .L3294:

00000c50 e8bd8070   2933 	ldmfd	[sp]!,{r4-r6,pc}

                    2934 	.endf	tlsf_free

                    2935 	.align	4

                    2936 ;control	r4	local

                    2937 ;block	r5	local

                    2938 

                    2939 ;tlsf	r0	param

                    2940 ;ptr	r1	param

                    2941 

                    2942 	.section ".bss","awb"

                    2943 .L3348:

                    2944 	.data

                    2945 	.text

                    2946 

                    2947 ;1195: 	}


                    2948 ;1196: }


                    2949 

                    2950 ;1197: 


                    2951 ;1198: /*


                    2952 ;1199: ** The TLSF block information provides us with enough information to


                    2953 ;1200: ** provide a reasonably intelligent implementation of realloc, growing or


                    2954 ;1201: ** shrinking the currently allocated block as required.


                    2955 ;1202: **


                    2956 ;1203: ** This routine handles the somewhat esoteric edge cases of realloc:


                    2957 ;1204: ** - a non-zero size with a null pointer will behave like malloc


                    2958 ;1205: ** - a zero size with a non-null pointer will behave like free


                    2959 ;1206: ** - a request that cannot be satisfied will leave the original buffer


                    2960 ;1207: **   untouched


                    2961 ;1208: ** - an extended buffer size will leave the newly-allocated area with


                    2962 ;1209: **   contents undefined


                    2963 ;1210: */


                    2964 ;1211: void* tlsf_realloc(tlsf_t tlsf, void* ptr, size_t size)


                    2965 	.align	4

                    2966 	.align	4

                    2967 tlsf_realloc::

00000c54 e92d4ff0   2968 	stmfd	[sp]!,{r4-fp,lr}

00000c58 e24dd00c   2969 	sub	sp,sp,12

00000c5c e1b04001   2970 	movs	r4,r1

00000c60 e1a05002   2971 	mov	r5,r2

                    2972 ;1212: {


                    2973 

                    2974 ;1213: 	control_t* control = tlsf_cast(control_t*, tlsf);


                    2975 

                    2976 ;1214: 	void* p = 0;


                    2977 

                    2978 ;1215: 



                                                                      Page 50
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
                    2979 ;1216: 	/* Zero-size requests are treated as free. */


                    2980 ;1217: 	if (ptr && size == 0)


                    2981 

00000c64 0a000008   2982 	beq	.L3368

00000c68 e3a06000   2983 	mov	r6,0

00000c6c e1a07000   2984 	mov	r7,r0

00000c70 e1a08007   2985 	mov	r8,r7

00000c74 e3550000   2986 	cmp	r5,0

00000c78 1a000006   2987 	bne	.L3367

                    2988 ;1218: 	{


                    2989 

                    2990 ;1219: 		tlsf_free(tlsf, ptr);


                    2991 

00000c7c e1a01004   2992 	mov	r1,r4

00000c80 ebffffd7*  2993 	bl	tlsf_free

                    2994 ;1263: 		}


                    2995 ;1264: 	}


                    2996 ;1265: 


                    2997 ;1266: 	return p;


                    2998 

00000c84 e1a00006   2999 	mov	r0,r6

00000c88 ea000043   3000 	b	.L3361

                    3001 .L3368:

                    3002 ;1220: 	}


                    3003 ;1221: 	/* Requests with NULL pointers are treated as malloc. */


                    3004 ;1222: 	else if (!ptr)


                    3005 

                    3006 ;1223: 	{


                    3007 

                    3008 ;1224: 		p = tlsf_malloc(tlsf, size);


                    3009 

00000c8c e1a01005   3010 	mov	r1,r5

00000c90 ebffff85*  3011 	bl	tlsf_malloc

                    3012 ;1263: 		}


                    3013 ;1264: 	}


                    3014 ;1265: 


                    3015 ;1266: 	return p;


                    3016 

00000c94 ea000040   3017 	b	.L3361

                    3018 .L3367:

                    3019 ;1225: 	}


                    3020 ;1226: 	else


                    3021 ;1227: 	{


                    3022 

                    3023 ;1228: 		block_header_t* block = block_from_ptr(ptr);


                    3024 

00000c98 e1a00004   3025 	mov	r0,r4

00000c9c ebfffd06*  3026 	bl	block_from_ptr

                    3027 ;1229: 		block_header_t* next = block_next(block);


                    3028 

00000ca0 e1a06000   3029 	mov	r6,r0

00000ca4 ebfffd0a*  3030 	bl	block_next

00000ca8 e1a09000   3031 	mov	r9,r0

00000cac e1a0a000   3032 	mov	r10,r0

                    3033 ;1230: 


                    3034 ;1231: 		const size_t cursize = block_size(block);


                    3035 

00000cb0 e1a00006   3036 	mov	r0,r6

00000cb4 ebfffce2*  3037 	bl	block_size

00000cb8 e1a0b000   3038 	mov	fp,r0

                    3039 ;1232: 		const size_t combined = cursize + block_size(next) + block_header_overhead;



                                                                      Page 51
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
                    3040 

00000cbc e1a0000a   3041 	mov	r0,r10

00000cc0 ebfffcdf*  3042 	bl	block_size

00000cc4 e08b0000   3043 	add	r0,fp,r0

00000cc8 e280a004   3044 	add	r10,r0,4

00000ccc e58da008   3045 	str	r10,[sp,8]

                    3046 ;1233: 		const size_t adjust = adjust_request_size(size, ALIGN_SIZE);


                    3047 

00000cd0 e1a00005   3048 	mov	r0,r5

00000cd4 e3a01004   3049 	mov	r1,4

00000cd8 ebfffd21*  3050 	bl	adjust_request_size

00000cdc e1a0a000   3051 	mov	r10,r0

                    3052 ;1234: 


                    3053 ;1235: 		tlsf_assert(!block_is_free(block) && "block already marked as free");


                    3054 ;1236: 


                    3055 ;1237: 		/*


                    3056 ;1238: 		** If the next block is used, or when combined with the current


                    3057 ;1239: 		** block, does not offer enough space, we must reallocate and copy.


                    3058 ;1240: 		*/


                    3059 ;1241: 		if (adjust > cursize && (!block_is_free(next) || adjust > combined))


                    3060 

00000ce0 e15a000b   3061 	cmp	r10,fp

00000ce4 9a00001a   3062 	bls	.L3381

00000ce8 e1a00009   3063 	mov	r0,r9

00000cec ebfffcdc*  3064 	bl	block_is_free

00000cf0 e3500000   3065 	cmp	r0,0

00000cf4 0a000002   3066 	beq	.L3371

00000cf8 e59d0008   3067 	ldr	r0,[sp,8]

00000cfc e15a0000   3068 	cmp	r10,r0

00000d00 9a00000e   3069 	bls	.L3378

                    3070 .L3371:

                    3071 ;1242: 		{


                    3072 

                    3073 ;1243: 			p = tlsf_malloc(tlsf, size);


                    3074 

00000d04 e1a01005   3075 	mov	r1,r5

00000d08 e1a00007   3076 	mov	r0,r7

00000d0c ebffff66*  3077 	bl	tlsf_malloc

00000d10 e1b06000   3078 	movs	r6,r0

                    3079 ;1244: 			if (p)


                    3080 

00000d14 0a00001f   3081 	beq	.L3366

                    3082 ;1245: 			{


                    3083 

                    3084 ;1246: 				const size_t minsize = tlsf_min(cursize, size);


                    3085 

00000d18 e155000b   3086 	cmp	r5,fp

00000d1c 91a02005   3087 	movls	r2,r5

00000d20 81a0200b   3088 	movhi	r2,fp

                    3089 ;1247: 				memcpy(p, ptr, minsize);


                    3090 

00000d24 e1a01004   3091 	mov	r1,r4

00000d28 eb000000*  3092 	bl	memcpy

                    3093 ;1248: 				tlsf_free(tlsf, ptr);


                    3094 

00000d2c e1a01004   3095 	mov	r1,r4

00000d30 e1a00007   3096 	mov	r0,r7

00000d34 ebffffaa*  3097 	bl	tlsf_free

                    3098 ;1263: 		}


                    3099 ;1264: 	}


                    3100 ;1265: 



                                                                      Page 52
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
                    3101 ;1266: 	return p;


                    3102 

00000d38 e1a00006   3103 	mov	r0,r6

00000d3c ea000016   3104 	b	.L3361

                    3105 .L3378:

                    3106 ;1249: 			}


                    3107 ;1250: 		}


                    3108 ;1251: 		else


                    3109 ;1252: 		{


                    3110 

                    3111 ;1253: 			/* Do we need to expand to the next block? */


                    3112 ;1254: 			if (adjust > cursize)


                    3113 

                    3114 ;1255: 			{


                    3115 

                    3116 ;1256: 				block_merge_next(control, block);


                    3117 

00000d40 e1a01006   3118 	mov	r1,r6

00000d44 e1a00008   3119 	mov	r0,r8

00000d48 ebfffd94*  3120 	bl	block_merge_next

                    3121 ;1257: 				block_mark_as_used(block);


                    3122 

00000d4c e1a00006   3123 	mov	r0,r6

00000d50 ebfffcf7*  3124 	bl	block_mark_as_used

                    3125 .L3381:

                    3126 ;1258: 			}


                    3127 ;1259: 


                    3128 ;1260: 			/* Trim the resulting block and return the original pointer. */


                    3129 ;1261: 			block_trim_used(control, block, adjust);


                    3130 

                    3131 ;727: {


                    3132 

                    3133 ;728: 	tlsf_assert(!block_is_free(block) && "block must be used");


                    3134 ;729: 	if (block_can_split(block, size))


                    3135 

00000d54 e1a0100a   3136 	mov	r1,r10

00000d58 e1a00006   3137 	mov	r0,r6

00000d5c ebfffd65*  3138 	bl	block_can_split

00000d60 e3500000   3139 	cmp	r0,0

00000d64 0a00000a   3140 	beq	.L3379

                    3141 ;730: 	{


                    3142 

                    3143 ;731: 		/* If the next block is free, we must coalesce. */


                    3144 ;732: 		block_header_t* remaining_block = block_split(block, size);


                    3145 

00000d68 e1a0100a   3146 	mov	r1,r10

00000d6c e1a00006   3147 	mov	r0,r6

00000d70 ebfffd68*  3148 	bl	block_split

                    3149 ;733: 		block_set_prev_used(remaining_block);


                    3150 

00000d74 e1a05000   3151 	mov	r5,r0

00000d78 ebfffccb*  3152 	bl	block_set_prev_used

                    3153 ;734: 


                    3154 ;735: 		remaining_block = block_merge_next(control, remaining_block);


                    3155 

00000d7c e1a01005   3156 	mov	r1,r5

00000d80 e1a00008   3157 	mov	r0,r8

00000d84 ebfffd85*  3158 	bl	block_merge_next

                    3159 ;736: 		block_insert(control, remaining_block);


                    3160 

00000d88 e1a01000   3161 	mov	r1,r0


                                                                      Page 53
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
00000d8c e1a00008   3162 	mov	r0,r8

00000d90 ebfffd3c*  3163 	bl	block_insert

                    3164 .L3379:

                    3165 ;1262: 			p = ptr;


                    3166 

00000d94 e1a06004   3167 	mov	r6,r4

                    3168 .L3366:

                    3169 ;1263: 		}


                    3170 ;1264: 	}


                    3171 ;1265: 


                    3172 ;1266: 	return p;


                    3173 

00000d98 e1a00006   3174 	mov	r0,r6

                    3175 .L3361:

00000d9c e28dd00c   3176 	add	sp,sp,12

00000da0 e8bd8ff0   3177 	ldmfd	[sp]!,{r4-fp,pc}

                    3178 	.endf	tlsf_realloc

                    3179 	.align	4

                    3180 ;control	r8	local

                    3181 ;p	r6	local

                    3182 ;block	r6	local

                    3183 ;next	r9	local

                    3184 ;cursize	fp	local

                    3185 ;combined	[sp,8]	local

                    3186 ;adjust	r10	local

                    3187 ;remaining_block	r5	local

                    3188 

                    3189 ;tlsf	r7	param

                    3190 ;ptr	r4	param

                    3191 ;size	r5	param

                    3192 

                    3193 	.section ".bss","awb"

                    3194 .L3556:

                    3195 	.data

                    3196 	.text

                    3197 

                    3198 ;1267: }


                    3199 	.align	4

                    3200 	.align	4

                    3201 tlsf_size::

                    3202 ;949: {


                    3203 

                    3204 ;950: 	return sizeof(control_t);


                    3205 

00000da4 e3a00ec0   3206 	mov	r0,3<<10

00000da8 e2800074   3207 	add	r0,r0,116

00000dac e12fff1e*  3208 	ret	

                    3209 	.endf	tlsf_size

                    3210 	.align	4

                    3211 

                    3212 	.section ".bss","awb"

                    3213 .L3614:

                    3214 	.data

                    3215 	.text

                    3216 	.align	4

                    3217 	.align	4

                    3218 tlsf_align_size::

                    3219 ;954: {


                    3220 

                    3221 ;955: 	return ALIGN_SIZE;


                    3222 


                                                                      Page 54
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
00000db0 e3a00004   3223 	mov	r0,4

00000db4 e12fff1e*  3224 	ret	

                    3225 	.endf	tlsf_align_size

                    3226 	.align	4

                    3227 

                    3228 	.section ".bss","awb"

                    3229 .L3646:

                    3230 	.data

                    3231 	.text

                    3232 	.align	4

                    3233 	.align	4

                    3234 tlsf_block_size_min::

                    3235 ;959: {


                    3236 

                    3237 ;960: 	return block_size_min;


                    3238 

00000db8 e3a0000c   3239 	mov	r0,12

00000dbc e12fff1e*  3240 	ret	

                    3241 	.endf	tlsf_block_size_min

                    3242 	.align	4

                    3243 

                    3244 	.section ".bss","awb"

                    3245 .L3678:

                    3246 	.data

                    3247 	.text

                    3248 	.align	4

                    3249 	.align	4

                    3250 tlsf_block_size_max::

                    3251 ;964: {


                    3252 

                    3253 ;965: 	return block_size_max;


                    3254 

00000dc0 e3a00440   3255 	mov	r0,1<<30

00000dc4 e12fff1e*  3256 	ret	

                    3257 	.endf	tlsf_block_size_max

                    3258 	.align	4

                    3259 

                    3260 	.section ".bss","awb"

                    3261 .L3710:

                    3262 	.data

                    3263 	.text

                    3264 	.align	4

                    3265 	.align	4

                    3266 tlsf_alloc_overhead::

                    3267 ;979: {


                    3268 

                    3269 ;980: 	return block_header_overhead;


                    3270 

00000dc8 e3a00004   3271 	mov	r0,4

00000dcc e12fff1e*  3272 	ret	

                    3273 	.endf	tlsf_alloc_overhead

                    3274 	.align	4

                    3275 

                    3276 	.section ".bss","awb"

                    3277 .L3742:

                    3278 	.data

                    3279 	.text

                    3280 	.align	4

                    3281 .L2487:

00000dd0 00000000*  3282 	.data.w	default_walker

                    3283 	.type	.L2487,$object


                                                                      Page 55
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gho1.s
                    3284 	.size	.L2487,4

                    3285 

                    3286 .L2567:

00000dd4 00000000*  3287 	.data.w	.L2562

                    3288 	.type	.L2567,$object

                    3289 	.size	.L2567,4

                    3290 

                    3291 .L2568:

00000dd8 00000000*  3292 	.data.w	integrity_walker

                    3293 	.type	.L2568,$object

                    3294 	.size	.L2568,4

                    3295 

                    3296 	.align	4

                    3297 

                    3298 	.data

                    3299 	.ghsnote version,6

                    3300 	.ghsnote tools,3

                    3301 	.ghsnote options,0

                    3302 	.text

                    3303 	.align	4

                    3304 	.section ".rodata","a"

                    3305 	.align	4

                    3306 	.text

