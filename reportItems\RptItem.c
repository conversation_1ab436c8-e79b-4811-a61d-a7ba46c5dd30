#include "RptItem.h"


#include "RptDataSet.h"

#include "RptBool.h"

#include "../MemoryManager.h"


#include "IEDCompile/InnerAttributeTypes.h"
#include <debug.h>

#include <string.h>

//==================== Загрушки функций=====================

static void defaultInitValue(RptItem item)
{
    ERROR_REPORT("defaultInitValue called"); 
}

static void dafaultUpdateChanges(RptItem item)
{
    ERROR_REPORT("dafaultUpdateChanges called");    
}

static bool defaultEncodeRead(RptItem item, BufferView* outBuf)
{
    ERROR_REPORT("defaultEncodeRead called"); 
    return false;
}

static bool defaultCalcReadLen(RptItem item, size_t* pLen )
{
    ERROR_REPORT("defaultCalcReadLen called"); 
    return false;
}

static void defaultOverwriteOld(RptItem item)
{
    ERROR_REPORT("defaultOverwriteOld called"); 
}

struct RptItemBehavior defaultBehavior = {
    defaultInitValue,
    dafaultUpdateChanges,
    defaultEncodeRead,
    defaultCalcReadLen,
    defaultOverwriteOld
};

//================= Загрушки функций для FinalDA =================

static void finalDAInitValue(RptItem item)
{
    ERROR_REPORT("finalDAInitValue called"); 
}

static void finalDADefaultUpdateChanges(RptItem item)
{
    ERROR_REPORT("finalDADefaultUpdateChanges called"); 
}

static bool finalDADefaultEncodeRead(RptItem item, BufferView* outBuf)
{
    ERROR_REPORT("finalDADefaultEncodeRead called"); 
    return false;
}

static bool finalDADefaultCalcReadLen(RptItem item, size_t* pLen )
{
    ERROR_REPORT("finalDADefaultCalcReadLen called"); 
    return false;
}

static void finalDADefaultOverwriteOld(RptItem item)
{
    ERROR_REPORT("finalDADefaultOverwriteOld called"); 
}

struct RptItemBehavior defaultFinalDABehavior = {
    finalDADefaultUpdateChanges,
    finalDADefaultEncodeRead,
    finalDADefaultCalcReadLen,
    finalDADefaultOverwriteOld
};


void *RptItem_alloc(size_t size)
{
    void* p = MM_alloc(size);
    if(p != NULL)
    {
        memset(p, 0, size);
    }
    return p;
}


bool initFinalDARptItem(RptItem item)
{
    IEDEntity iedObj = item->iedObj;    
    if(iedObj->type == IED_ENTITY_DA_TERMINAL_ITEM)
    {
        if(iedObj->trgOps == TRGOP_NONE)
        {
            TRACE("FinalDA without trgOps in report");            
        }

        switch(iedObj->subType)
        {
        case INNER_TYPE_BOOLEAN:
            RptBool_init(item);
            break;
        default:
            ERROR_REPORT("Unsupported FinalDA type");
            return false;
        }
    }
    
    return true;
}

RptItem RptItem_create(IEDEntity iedObj, RptDataSetItem rptDsItem)
{
    RptItem item = RptItem_alloc(sizeof(struct RptItemStruct));
    if(item == NULL)
    {
        return NULL;
    }

    item->behaviour = &defaultBehavior;

    if(IEDEntity_isFinalDA(iedObj))
    {        
        item->behaviour = &defaultFinalDABehavior;
        item->iedObj = iedObj;
        item->rptDsItem = rptDsItem;
        if(!initFinalDARptItem(item))
        {
            return NULL;
        }
        RptDataSetItem_addFinalDARptItem(rptDsItem, item);
    }
    else
    {
        // Это составной элемент
        // Рекурсивно создаём детей
        IEDEntity child = iedObj->firstChild;
        while(child != NULL)
        {
            RptItem childItem = RptItem_create(child, rptDsItem);
            if(childItem == NULL)
            {
                return NULL;
            }
            if(item->firstChild == NULL)
            {
                item->firstChild = childItem;
            }
            else
            {
                item->lastChild->nextSibling = childItem;
            }
            item->lastChild = childItem;
            child = child->next;
        }
    }

    return item;    
}

