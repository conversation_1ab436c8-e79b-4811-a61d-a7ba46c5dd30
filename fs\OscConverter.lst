                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=OscConverter.c -o fs\gh_c9c1.o -list=fs/OscConverter.lst C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s
Source File: OscConverter.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      10 ;		fs/OscConverter.c -o fs/OscConverter.o

                      11 ;Source File:   fs/OscConverter.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:55:56 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "OscConverter.h"


                      21 ;2: #include "platform_critical_section.h"


                      22 ;3: #include <time.h>


                      23 ;4: #include <string.h>


                      24 ;5: #include <stdlib.h>


                      25 ;6: #include <stdio.h>


                      26 ;7: #include "../timetools.h"


                      27 ;8: 


                      28 ;9: bool  OscConverter_init(void)


                      29 

                      30 ;12: }


                      31 

                      32 ;13: 


                      33 ;14: 


                      34 ;15: bool  OscConverter_processPeriod(OscReadFileContext * readFileContext)


                      35 ;16: {


                      36 ;17: 	char *formatBuffer = readFileContext->formatBuffer;


                      37 ;18: 	OscWriteBuffer *datBuffer = &readFileContext->datBuffer;


                      38 ;19: 	OSCInfoStruct *oscInfo;


                      39 ;20: 	int pointPerFrame;


                      40 ;21: 	int analogCount;


                      41 ;22: 	int boolCount;


                      42 ;23: 	int pointNum;


                      43 ;24: 	int sampleNum;


                      44 ;25: 	int size;


                      45 ;26: 	double tick;


                      46 ;27: 	int analogNum;


                      47 ;28: 	int boolNum;


                      48 ;29: 	float freq;


                      49 ;30: 


                      50 ;31: 	if (!readFileContext)



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s
                      51 ;32: 	{


                      52 ;33: 		return false;


                      53 ;34: 	}


                      54 ;35: 	oscInfo = readFileContext->oscInfo;


                      55 ;36: 	if (!oscInfo)


                      56 ;37: 	{


                      57 ;38: 		return false;


                      58 ;39: 	}


                      59 ;40: 


                      60 ;41: 	pointPerFrame = OSCInfo_getPointPerFrameCount(oscInfo);


                      61 ;42: 	analogCount = OSCInfo_getAnalogCount(oscInfo);


                      62 ;43: 	boolCount = OSCInfo_getBoolCount(oscInfo);


                      63 ;44: 	sampleNum = (readFileContext->curFrame) * pointPerFrame + 1;


                      64 ;45: 


                      65 ;46: 


                      66 ;47: 


                      67 ;48: 	OscWriteBuffer_reset(datBuffer);


                      68 ;49: 


                      69 ;50: 	for (pointNum = 0; pointNum < pointPerFrame; ++pointNum)


                      70 ;51: 	{


                      71 ;52: 		size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, "%d", sampleNum);


                      72 ;53: 		if (!OscWriteBuffer_write(datBuffer, formatBuffer, size)) return false;


                      73 ;54: 


                      74 ;55: 		size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, ",%lld", 


                      75 ;56: 			(uint64_t)( readFileContext->tick + 0.5));


                      76 ;57: 		if (!OscWriteBuffer_write(datBuffer, formatBuffer, size)) return false;


                      77 ;58: 		


                      78 ;59: 		if (!OSCFrame_getTick(oscInfo, &tick))


                      79 ;60: 		{


                      80 ;61: 			return false;


                      81 ;62: 		}


                      82 ;63: 		readFileContext->tick += tick;


                      83 ;64: 


                      84 ;65: 		// если еще не вышли за предысторию, фиксируем тики


                      85 ;66: 		if (readFileContext->curFrame < OSCInfo_getPrehistFrameCount(oscInfo))


                      86 ;67: 		{


                      87 ;68: 			readFileContext->phistoryTick = readFileContext->tick;


                      88 ;69: 		}


                      89 ;70: 


                      90 ;71: 		


                      91 ;72: 


                      92 ;73: 		for (analogNum = 0; analogNum < analogCount; ++analogNum)


                      93 ;74: 		{


                      94 ;75: 			int analogValue = OSCFrame_getAnalogValue(oscInfo, pointNum, analogNum);


                      95 ;76: 			size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, ",%d", analogValue);


                      96 ;77: 			if (!OscWriteBuffer_write(datBuffer, formatBuffer, size)) return false;


                      97 ;78: 		}


                      98 ;79: 


                      99 ;80: 		for (boolNum = 0; boolNum < boolCount; ++boolNum)


                     100 ;81: 		{


                     101 ;82: 			int boolValue = OSCFrame_getBoolValue(oscInfo, pointNum, boolNum);


                     102 ;83: 			size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, ",%d", boolValue);


                     103 ;84: 			if (!OscWriteBuffer_write(datBuffer, formatBuffer, size)) return false;


                     104 ;85: 		}


                     105 ;86: 


                     106 ;87: 


                     107 ;88: 		size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, "\r\n");


                     108 ;89: 		if (!OscWriteBuffer_write(datBuffer, formatBuffer, size)) return false;


                     109 ;90: 


                     110 ;91: 		// частоту для cfg файла


                     111 ;92: 		if (!OSCFrame_getFreq(oscInfo, &freq))



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s
                     112 ;93: 		{


                     113 ;94: 			return false;


                     114 ;95: 		}


                     115 ;96: 		if (!OscReadFileContext_writeFreq(readFileContext, freq, sampleNum))


                     116 ;97: 		{


                     117 ;98: 			return false;


                     118 ;99: 		}


                     119 ;100: 


                     120 ;101: 		sampleNum++;


                     121 ;102: 	}


                     122 ;103: 


                     123 ;104: 	return true;


                     124 ;105: }


                     125 ;106: 


                     126 ;107: //! вычисление времени начала предыстории


                     127 ;108: static void getPhistTime(OscReadFileContext * readFileContext,


                     128 

                     129 ;126: 	


                     130 ;127: }


                     131 

                     132 ;128: //! время в COMTRADE 


                     133 ;129: static bool writeFormatTimeToCfgBuffer(OscReadFileContext * readFileContext,


                     134 ;130: 	__time32_t t, int ms)


                     135 ;131: {


                     136 ;132: 	char *formatBuffer = readFileContext->formatBuffer;


                     137 ;133: 	OscWriteBuffer *cfgBuffer = &readFileContext->cfgBuffer;


                     138 ;134: 	int size;


                     139 ;135: 	struct tm tmTime;


                     140 ;136: 


                     141 ;137: 	// пишется UTC, поэтому осцилограмма будет отличатся от мониторной, 


                     142 ;138: 	// чтобы было все одинаково нужно вытащить часовой пояс из ЦП


                     143 ;139: 	if (TimeTools_gmtime32(&tmTime, &t) == true)


                     144 ;140: 	{


                     145 ;141: 		// по идее тут нужно прибавить 1900, но comtrade


                     146 ;142: 		// требует год в формате %02d, поэтому вычитаем 100


                     147 ;143: 


                     148 ;144: 		// Update: для нового формата используется год в виде 4-х символов


                     149 ;145: 		// поэтому прибавляем 1900 и меняем формат на %04d


                     150 ;146: 		tmTime.tm_year += 1900;


                     151 ;147: 		tmTime.tm_mon += 1;


                     152 ;148: 


                     153 ;149: 		size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE,


                     154 ;150: 			"%02d/%02d/%04d,%02d:%02d:%02d.%03d\r\n",


                     155 ;151: 			tmTime.tm_mday,


                     156 ;152: 			tmTime.tm_mon,


                     157 ;153: 			tmTime.tm_year,


                     158 ;154: 			tmTime.tm_hour,


                     159 ;155: 			tmTime.tm_min,


                     160 ;156: 			tmTime.tm_sec,


                     161 ;157: 			ms);


                     162 ;158: 	}


                     163 ;159: 	else


                     164 ;160: 	{


                     165 ;161: 		size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE,


                     166 ;162: 			"01/01/1980 00:00:00.%03d\r\n",ms);


                     167 ;163: 	}


                     168 ;164: 


                     169 ;165: 	return OscWriteBuffer_write(cfgBuffer, formatBuffer, size);


                     170 ;166: }


                     171 ;167: 


                     172 ;168: //! записывает время предыстории и осцилограммы



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s
                     173 ;169: static bool writeOscTimeToCfg(OscReadFileContext * readFileContext)


                     174 

                     175 ;190: }


                     176 

                     177 ;191: //! описание аналоговых каналов в cfg


                     178 ;192: static bool writeAnalogToCfg(OscReadFileContext * readFileContext)


                     179 

                     180 ;228: }


                     181 

                     182 ;229: 


                     183 ;230: //! описание дискретных каналов в cfg


                     184 ;231: static bool writeBoolToCfg(OscReadFileContext * readFileContext)


                     185 

                     186 ;252: }


                     187 

                     188 ;253: 


                     189 ;254: //! частота и список изменений частоты в cfg


                     190 ;255: static bool writeFrequencyToCfg(OscReadFileContext * readFileContext)


                     191 

                     192 ;286: }


                     193 

                     194 	.text

                     195 	.align	4

                     196 OscConverter_processPeriod::

00000000 e92d4ff0    197 	stmfd	[sp]!,{r4-fp,lr}

00000004 e1a0a000    198 	mov	r10,r0

00000008 e2804050    199 	add	r4,r0,80

0000000c e3a01e40    200 	mov	r1,1<<10

00000010 e2811064    201 	add	r1,r1,100

00000014 e24dd030    202 	sub	sp,sp,48

00000018 e3500000    203 	cmp	r0,0

0000001c 1590502c    204 	ldrne	r5,[r0,44]

00000020 e0807001    205 	add	r7,r0,r1

00000024 13550000    206 	cmpne	r5,0

                     207 .L182:

00000028 03a00000    208 	moveq	r0,0

0000002c 0a00019a    209 	beq	.L176

                     210 .L181:

00000030 e1a00005    211 	mov	r0,r5

00000034 eb000000*   212 	bl	OSCInfo_getPointPerFrameCount

00000038 e58d0020    213 	str	r0,[sp,32]

0000003c e1a06000    214 	mov	r6,r0

00000040 e1a0b000    215 	mov	fp,r0

00000044 e1a00005    216 	mov	r0,r5

00000048 eb000000*   217 	bl	OSCInfo_getAnalogCount

0000004c e58d001c    218 	str	r0,[sp,28]

00000050 e1a00005    219 	mov	r0,r5

00000054 eb000000*   220 	bl	OSCInfo_getBoolCount

00000058 e59a1030    221 	ldr	r1,[r10,48]

0000005c e001019b    222 	mul	r1,fp,r1

00000060 e3a0b000    223 	mov	fp,0

00000064 e58d0018    224 	str	r0,[sp,24]

00000068 e2810001    225 	add	r0,r1,1

0000006c e58d0010    226 	str	r0,[sp,16]

00000070 e1a00007    227 	mov	r0,r7

00000074 eb000000*   228 	bl	OscWriteBuffer_reset

00000078 e15b0006    229 	cmp	fp,r6

0000007c aa000185    230 	bge	.L184

                     231 .L186:

00000080 e59d3010    232 	ldr	r3,[sp,16]

00000084 e28f2000*   233 	adr	r2,.L1026


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s
00000088 e1a00004    234 	mov	r0,r4

0000008c e3a01e40    235 	mov	r1,1<<10

00000090 eb000000*   236 	bl	snprintf

00000094 e1a01004    237 	mov	r1,r4

00000098 e1a02000    238 	mov	r2,r0

0000009c e1a00007    239 	mov	r0,r7

000000a0 eb000000*   240 	bl	OscWriteBuffer_write

000000a4 e3500000    241 	cmp	r0,0

000000a8 0affffde    242 	beq	.L182

000000ac e59a0034    243 	ldr	r0,[r10,52]

000000b0 e59a1038    244 	ldr	r1,[r10,56]

000000b4 e3a02000    245 	mov	r2,0

000000b8 e3a036fe    246 	mov	r3,254<<20

000000bc e28335c0    247 	add	r3,r3,3<<28

000000c0 eb000000*   248 	bl	__dadd

000000c4 eb000000*   249 	bl	__dtou64

000000c8 e52d0004    250 	str	r0,[sp,-4]!

000000cc e58d1004    251 	str	r1,[sp,4]

000000d0 e8bd0008    252 	ldmfd	[sp]!,{r3}

000000d4 e28f2000*   253 	adr	r2,.L1027

000000d8 e1a00004    254 	mov	r0,r4

000000dc e3a01e40    255 	mov	r1,1<<10

000000e0 eb000000*   256 	bl	snprintf

000000e4 e1a01004    257 	mov	r1,r4

000000e8 e1a02000    258 	mov	r2,r0

000000ec e1a00007    259 	mov	r0,r7

000000f0 eb000000*   260 	bl	OscWriteBuffer_write

000000f4 e3500000    261 	cmp	r0,0

000000f8 0affffca    262 	beq	.L182

000000fc e28d1028    263 	add	r1,sp,40

00000100 e1a00005    264 	mov	r0,r5

00000104 eb000000*   265 	bl	OSCFrame_getTick

00000108 e3500000    266 	cmp	r0,0

0000010c 0affffc5    267 	beq	.L182

00000110 e59d302c    268 	ldr	r3,[sp,44]

00000114 e59d2028    269 	ldr	r2,[sp,40]

00000118 e59a1038    270 	ldr	r1,[r10,56]

0000011c e59a0034    271 	ldr	r0,[r10,52]

00000120 eb000000*   272 	bl	__dadd

00000124 e58a1038    273 	str	r1,[r10,56]

00000128 e58a0034    274 	str	r0,[r10,52]

0000012c e1a00005    275 	mov	r0,r5

00000130 eb000000*   276 	bl	OSCInfo_getPrehistFrameCount

00000134 e1a0600a    277 	mov	r6,r10

00000138 e5961030    278 	ldr	r1,[r6,48]

0000013c e1510000    279 	cmp	r1,r0

00000140 35961034    280 	ldrlo	r1,[r6,52]

00000144 35b62038    281 	ldrlo	r2,[r6,56]!

00000148 39860006    282 	stmlofa	[r6],{r1-r2}

0000014c e59d001c    283 	ldr	r0,[sp,28]

00000150 e3a08000    284 	mov	r8,0

00000154 e3500000    285 	cmp	r0,0

00000158 b3a00000    286 	movlt	r0,0

0000015c e58d0014    287 	str	r0,[sp,20]

00000160 e1b091a0    288 	movs	r9,r0 lsr 3

00000164 0a00007b    289 	beq	.L319

00000168 e59f6304*   290 	ldr	r6,.L1028

                     291 .L320:

0000016c e1a02008    292 	mov	r2,r8

00000170 e1a0100b    293 	mov	r1,fp

00000174 e1a00005    294 	mov	r0,r5


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s
00000178 eb000000*   295 	bl	OSCFrame_getAnalogValue

0000017c e1a02006    296 	mov	r2,r6

00000180 e1a03000    297 	mov	r3,r0

00000184 e1a00004    298 	mov	r0,r4

00000188 e3a01e40    299 	mov	r1,1<<10

0000018c eb000000*   300 	bl	snprintf

00000190 e1a01004    301 	mov	r1,r4

00000194 e1a02000    302 	mov	r2,r0

00000198 e1a00007    303 	mov	r0,r7

0000019c eb000000*   304 	bl	OscWriteBuffer_write

000001a0 e3500000    305 	cmp	r0,0

000001a4 0affff9f    306 	beq	.L182

000001a8 e2882001    307 	add	r2,r8,1

000001ac e1a0100b    308 	mov	r1,fp

000001b0 e1a00005    309 	mov	r0,r5

000001b4 eb000000*   310 	bl	OSCFrame_getAnalogValue

000001b8 e1a02006    311 	mov	r2,r6

000001bc e1a03000    312 	mov	r3,r0

000001c0 e1a00004    313 	mov	r0,r4

000001c4 e3a01e40    314 	mov	r1,1<<10

000001c8 eb000000*   315 	bl	snprintf

000001cc e1a01004    316 	mov	r1,r4

000001d0 e1a02000    317 	mov	r2,r0

000001d4 e1a00007    318 	mov	r0,r7

000001d8 eb000000*   319 	bl	OscWriteBuffer_write

000001dc e3500000    320 	cmp	r0,0

000001e0 0affff90    321 	beq	.L182

000001e4 e2882002    322 	add	r2,r8,2

000001e8 e1a0100b    323 	mov	r1,fp

000001ec e1a00005    324 	mov	r0,r5

000001f0 eb000000*   325 	bl	OSCFrame_getAnalogValue

000001f4 e1a02006    326 	mov	r2,r6

000001f8 e1a03000    327 	mov	r3,r0

000001fc e1a00004    328 	mov	r0,r4

00000200 e3a01e40    329 	mov	r1,1<<10

00000204 eb000000*   330 	bl	snprintf

00000208 e1a01004    331 	mov	r1,r4

0000020c e1a02000    332 	mov	r2,r0

00000210 e1a00007    333 	mov	r0,r7

00000214 eb000000*   334 	bl	OscWriteBuffer_write

00000218 e3500000    335 	cmp	r0,0

0000021c 0affff81    336 	beq	.L182

00000220 e2882003    337 	add	r2,r8,3

00000224 e1a0100b    338 	mov	r1,fp

00000228 e1a00005    339 	mov	r0,r5

0000022c eb000000*   340 	bl	OSCFrame_getAnalogValue

00000230 e1a02006    341 	mov	r2,r6

00000234 e1a03000    342 	mov	r3,r0

00000238 e1a00004    343 	mov	r0,r4

0000023c e3a01e40    344 	mov	r1,1<<10

00000240 eb000000*   345 	bl	snprintf

00000244 e1a01004    346 	mov	r1,r4

00000248 e1a02000    347 	mov	r2,r0

0000024c e1a00007    348 	mov	r0,r7

00000250 eb000000*   349 	bl	OscWriteBuffer_write

00000254 e3500000    350 	cmp	r0,0

00000258 0affff72    351 	beq	.L182

0000025c e2882004    352 	add	r2,r8,4

00000260 e1a0100b    353 	mov	r1,fp

00000264 e1a00005    354 	mov	r0,r5

00000268 eb000000*   355 	bl	OSCFrame_getAnalogValue


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s
0000026c e1a02006    356 	mov	r2,r6

00000270 e1a03000    357 	mov	r3,r0

00000274 e1a00004    358 	mov	r0,r4

00000278 e3a01e40    359 	mov	r1,1<<10

0000027c eb000000*   360 	bl	snprintf

00000280 e1a01004    361 	mov	r1,r4

00000284 e1a02000    362 	mov	r2,r0

00000288 e1a00007    363 	mov	r0,r7

0000028c eb000000*   364 	bl	OscWriteBuffer_write

00000290 e3500000    365 	cmp	r0,0

00000294 0affff63    366 	beq	.L182

00000298 e2882005    367 	add	r2,r8,5

0000029c e1a0100b    368 	mov	r1,fp

000002a0 e1a00005    369 	mov	r0,r5

000002a4 eb000000*   370 	bl	OSCFrame_getAnalogValue

000002a8 e1a02006    371 	mov	r2,r6

000002ac e1a03000    372 	mov	r3,r0

000002b0 e1a00004    373 	mov	r0,r4

000002b4 e3a01e40    374 	mov	r1,1<<10

000002b8 eb000000*   375 	bl	snprintf

000002bc e1a01004    376 	mov	r1,r4

000002c0 e1a02000    377 	mov	r2,r0

000002c4 e1a00007    378 	mov	r0,r7

000002c8 eb000000*   379 	bl	OscWriteBuffer_write

000002cc e3500000    380 	cmp	r0,0

000002d0 0affff54    381 	beq	.L182

000002d4 e2882006    382 	add	r2,r8,6

000002d8 e1a0100b    383 	mov	r1,fp

000002dc e1a00005    384 	mov	r0,r5

000002e0 eb000000*   385 	bl	OSCFrame_getAnalogValue

000002e4 e1a02006    386 	mov	r2,r6

000002e8 e1a03000    387 	mov	r3,r0

000002ec e1a00004    388 	mov	r0,r4

000002f0 e3a01e40    389 	mov	r1,1<<10

000002f4 eb000000*   390 	bl	snprintf

000002f8 e1a01004    391 	mov	r1,r4

000002fc e1a02000    392 	mov	r2,r0

00000300 e1a00007    393 	mov	r0,r7

00000304 eb000000*   394 	bl	OscWriteBuffer_write

00000308 e3500000    395 	cmp	r0,0

0000030c 0affff45    396 	beq	.L182

00000310 e2882007    397 	add	r2,r8,7

00000314 e1a0100b    398 	mov	r1,fp

00000318 e1a00005    399 	mov	r0,r5

0000031c eb000000*   400 	bl	OSCFrame_getAnalogValue

00000320 e1a02006    401 	mov	r2,r6

00000324 e1a03000    402 	mov	r3,r0

00000328 e1a00004    403 	mov	r0,r4

0000032c e3a01e40    404 	mov	r1,1<<10

00000330 eb000000*   405 	bl	snprintf

00000334 e1a01004    406 	mov	r1,r4

00000338 e1a02000    407 	mov	r2,r0

0000033c e1a00007    408 	mov	r0,r7

00000340 eb000000*   409 	bl	OscWriteBuffer_write

00000344 e3500000    410 	cmp	r0,0

00000348 0affff36    411 	beq	.L182

0000034c e2888008    412 	add	r8,r8,8

00000350 e2599001    413 	subs	r9,r9,1

00000354 1affff84    414 	bne	.L320

                     415 .L319:

00000358 e59d0014    416 	ldr	r0,[sp,20]


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s
0000035c e2109007    417 	ands	r9,r0,7

00000360 0a000012    418 	beq	.L199

00000364 e59f6108*   419 	ldr	r6,.L1028

                     420 .L346:

00000368 e1a02008    421 	mov	r2,r8

0000036c e1a0100b    422 	mov	r1,fp

00000370 e1a00005    423 	mov	r0,r5

00000374 eb000000*   424 	bl	OSCFrame_getAnalogValue

00000378 e1a02006    425 	mov	r2,r6

0000037c e1a03000    426 	mov	r3,r0

00000380 e1a00004    427 	mov	r0,r4

00000384 e3a01e40    428 	mov	r1,1<<10

00000388 eb000000*   429 	bl	snprintf

0000038c e1a01004    430 	mov	r1,r4

00000390 e1a02000    431 	mov	r2,r0

00000394 e1a00007    432 	mov	r0,r7

00000398 eb000000*   433 	bl	OscWriteBuffer_write

0000039c e3500000    434 	cmp	r0,0

000003a0 0affff20    435 	beq	.L182

000003a4 e2888001    436 	add	r8,r8,1

000003a8 e2599001    437 	subs	r9,r9,1

000003ac 1affffed    438 	bne	.L346

                     439 .L199:

000003b0 e59d0018    440 	ldr	r0,[sp,24]

000003b4 e3a08000    441 	mov	r8,0

000003b8 e3500000    442 	cmp	r0,0

000003bc b3a00000    443 	movlt	r0,0

000003c0 e58d0014    444 	str	r0,[sp,20]

000003c4 e1b091a0    445 	movs	r9,r0 lsr 3

000003c8 0a000080    446 	beq	.L351

000003cc e59f60a0*   447 	ldr	r6,.L1028

                     448 .L352:

000003d0 e1a02008    449 	mov	r2,r8

000003d4 e1a0100b    450 	mov	r1,fp

000003d8 e1a00005    451 	mov	r0,r5

000003dc eb000000*   452 	bl	OSCFrame_getBoolValue

000003e0 e1a02006    453 	mov	r2,r6

000003e4 e1a03000    454 	mov	r3,r0

000003e8 e1a00004    455 	mov	r0,r4

000003ec e3a01e40    456 	mov	r1,1<<10

000003f0 eb000000*   457 	bl	snprintf

000003f4 e1a01004    458 	mov	r1,r4

000003f8 e1a02000    459 	mov	r2,r0

000003fc e1a00007    460 	mov	r0,r7

00000400 eb000000*   461 	bl	OscWriteBuffer_write

00000404 e3500000    462 	cmp	r0,0

00000408 0affff06    463 	beq	.L182

0000040c e2882001    464 	add	r2,r8,1

00000410 e1a0100b    465 	mov	r1,fp

00000414 e1a00005    466 	mov	r0,r5

00000418 eb000000*   467 	bl	OSCFrame_getBoolValue

0000041c e1a02006    468 	mov	r2,r6

00000420 e1a03000    469 	mov	r3,r0

00000424 e1a00004    470 	mov	r0,r4

00000428 e3a01e40    471 	mov	r1,1<<10

0000042c eb000000*   472 	bl	snprintf

00000430 e1a01004    473 	mov	r1,r4

00000434 e1a02000    474 	mov	r2,r0

00000438 e1a00007    475 	mov	r0,r7

0000043c eb000000*   476 	bl	OscWriteBuffer_write

00000440 e3500000    477 	cmp	r0,0


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s
00000444 0afffef7    478 	beq	.L182

00000448 e2882002    479 	add	r2,r8,2

0000044c e1a0100b    480 	mov	r1,fp

00000450 e1a00005    481 	mov	r0,r5

00000454 eb000000*   482 	bl	OSCFrame_getBoolValue

00000458 e1a02006    483 	mov	r2,r6

0000045c e1a03000    484 	mov	r3,r0

00000460 e1a00004    485 	mov	r0,r4

00000464 ea000003    486 	b	.L1029

                     487 	.align	4

                     488 .L1026:

                     489 ;	"%d\000"

00000468 6425       490 	.data.b	37,100

0000046a 00         491 	.data.b	0

0000046b 00         492 	.align 4

                     493 

                     494 	.type	.L1026,$object

                     495 	.size	.L1026,4

                     496 

                     497 .L1027:

                     498 ;	",%lld\000"

0000046c 6c6c252c    499 	.data.b	44,37,108,108

00000470 0064       500 	.data.b	100,0

00000472 0000       501 	.align 4

                     502 

                     503 	.type	.L1027,$object

                     504 	.size	.L1027,4

                     505 

                     506 .L1028:

00000474 00000000*   507 	.data.w	.L894

                     508 	.type	.L1028,$object

                     509 	.size	.L1028,4

                     510 

                     511 .L1029:

                     512 

00000478 e3a01e40    513 	mov	r1,1<<10

0000047c eb000000*   514 	bl	snprintf

00000480 e1a01004    515 	mov	r1,r4

00000484 e1a02000    516 	mov	r2,r0

00000488 e1a00007    517 	mov	r0,r7

0000048c eb000000*   518 	bl	OscWriteBuffer_write

00000490 e3500000    519 	cmp	r0,0

00000494 0afffee3    520 	beq	.L182

00000498 e2882003    521 	add	r2,r8,3

0000049c e1a0100b    522 	mov	r1,fp

000004a0 e1a00005    523 	mov	r0,r5

000004a4 eb000000*   524 	bl	OSCFrame_getBoolValue

000004a8 e1a02006    525 	mov	r2,r6

000004ac e1a03000    526 	mov	r3,r0

000004b0 e1a00004    527 	mov	r0,r4

000004b4 e3a01e40    528 	mov	r1,1<<10

000004b8 eb000000*   529 	bl	snprintf

000004bc e1a01004    530 	mov	r1,r4

000004c0 e1a02000    531 	mov	r2,r0

000004c4 e1a00007    532 	mov	r0,r7

000004c8 eb000000*   533 	bl	OscWriteBuffer_write

000004cc e3500000    534 	cmp	r0,0

000004d0 0afffed4    535 	beq	.L182

000004d4 e2882004    536 	add	r2,r8,4

000004d8 e1a0100b    537 	mov	r1,fp

000004dc e1a00005    538 	mov	r0,r5


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s
000004e0 eb000000*   539 	bl	OSCFrame_getBoolValue

000004e4 e1a02006    540 	mov	r2,r6

000004e8 e1a03000    541 	mov	r3,r0

000004ec e1a00004    542 	mov	r0,r4

000004f0 e3a01e40    543 	mov	r1,1<<10

000004f4 eb000000*   544 	bl	snprintf

000004f8 e1a01004    545 	mov	r1,r4

000004fc e1a02000    546 	mov	r2,r0

00000500 e1a00007    547 	mov	r0,r7

00000504 eb000000*   548 	bl	OscWriteBuffer_write

00000508 e3500000    549 	cmp	r0,0

0000050c 0afffec5    550 	beq	.L182

00000510 e2882005    551 	add	r2,r8,5

00000514 e1a0100b    552 	mov	r1,fp

00000518 e1a00005    553 	mov	r0,r5

0000051c eb000000*   554 	bl	OSCFrame_getBoolValue

00000520 e1a02006    555 	mov	r2,r6

00000524 e1a03000    556 	mov	r3,r0

00000528 e1a00004    557 	mov	r0,r4

0000052c e3a01e40    558 	mov	r1,1<<10

00000530 eb000000*   559 	bl	snprintf

00000534 e1a01004    560 	mov	r1,r4

00000538 e1a02000    561 	mov	r2,r0

0000053c e1a00007    562 	mov	r0,r7

00000540 eb000000*   563 	bl	OscWriteBuffer_write

00000544 e3500000    564 	cmp	r0,0

00000548 0afffeb6    565 	beq	.L182

0000054c e2882006    566 	add	r2,r8,6

00000550 e1a0100b    567 	mov	r1,fp

00000554 e1a00005    568 	mov	r0,r5

00000558 eb000000*   569 	bl	OSCFrame_getBoolValue

0000055c e1a02006    570 	mov	r2,r6

00000560 e1a03000    571 	mov	r3,r0

00000564 e1a00004    572 	mov	r0,r4

00000568 e3a01e40    573 	mov	r1,1<<10

0000056c eb000000*   574 	bl	snprintf

00000570 e1a01004    575 	mov	r1,r4

00000574 e1a02000    576 	mov	r2,r0

00000578 e1a00007    577 	mov	r0,r7

0000057c eb000000*   578 	bl	OscWriteBuffer_write

00000580 e3500000    579 	cmp	r0,0

00000584 0afffea7    580 	beq	.L182

00000588 e2882007    581 	add	r2,r8,7

0000058c e1a0100b    582 	mov	r1,fp

00000590 e1a00005    583 	mov	r0,r5

00000594 eb000000*   584 	bl	OSCFrame_getBoolValue

00000598 e1a02006    585 	mov	r2,r6

0000059c e1a03000    586 	mov	r3,r0

000005a0 e1a00004    587 	mov	r0,r4

000005a4 e3a01e40    588 	mov	r1,1<<10

000005a8 eb000000*   589 	bl	snprintf

000005ac e1a01004    590 	mov	r1,r4

000005b0 e1a02000    591 	mov	r2,r0

000005b4 e1a00007    592 	mov	r0,r7

000005b8 eb000000*   593 	bl	OscWriteBuffer_write

000005bc e3500000    594 	cmp	r0,0

000005c0 0afffe98    595 	beq	.L182

000005c4 e2888008    596 	add	r8,r8,8

000005c8 e2599001    597 	subs	r9,r9,1

000005cc 1affff7f    598 	bne	.L352

                     599 .L351:


                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s
000005d0 e59d0014    600 	ldr	r0,[sp,20]

000005d4 e2109007    601 	ands	r9,r0,7

000005d8 0a000012    602 	beq	.L206

000005dc e51f6170*   603 	ldr	r6,.L1028

                     604 .L378:

000005e0 e1a02008    605 	mov	r2,r8

000005e4 e1a0100b    606 	mov	r1,fp

000005e8 e1a00005    607 	mov	r0,r5

000005ec eb000000*   608 	bl	OSCFrame_getBoolValue

000005f0 e1a02006    609 	mov	r2,r6

000005f4 e1a03000    610 	mov	r3,r0

000005f8 e1a00004    611 	mov	r0,r4

000005fc e3a01e40    612 	mov	r1,1<<10

00000600 eb000000*   613 	bl	snprintf

00000604 e1a01004    614 	mov	r1,r4

00000608 e1a02000    615 	mov	r2,r0

0000060c e1a00007    616 	mov	r0,r7

00000610 eb000000*   617 	bl	OscWriteBuffer_write

00000614 e3500000    618 	cmp	r0,0

00000618 0afffe82    619 	beq	.L182

0000061c e2888001    620 	add	r8,r8,1

00000620 e2599001    621 	subs	r9,r9,1

00000624 1affffed    622 	bne	.L378

                     623 .L206:

00000628 e28f2000*   624 	adr	r2,.L1030

0000062c e1a00004    625 	mov	r0,r4

00000630 e3a01e40    626 	mov	r1,1<<10

00000634 eb000000*   627 	bl	snprintf

00000638 e1a01004    628 	mov	r1,r4

0000063c e1a02000    629 	mov	r2,r0

00000640 e1a00007    630 	mov	r0,r7

00000644 eb000000*   631 	bl	OscWriteBuffer_write

00000648 e3500000    632 	cmp	r0,0

0000064c 0afffe75    633 	beq	.L182

00000650 e28d1004    634 	add	r1,sp,4

00000654 e1a00005    635 	mov	r0,r5

00000658 eb000000*   636 	bl	OSCFrame_getFreq

0000065c e3500000    637 	cmp	r0,0

00000660 0afffe70    638 	beq	.L182

00000664 e59d2010    639 	ldr	r2,[sp,16]

00000668 e59d1004    640 	ldr	r1,[sp,4]

0000066c e1a0000a    641 	mov	r0,r10

00000670 eb000000*   642 	bl	OscReadFileContext_writeFreq

00000674 e3500000    643 	cmp	r0,0

00000678 0afffe6a    644 	beq	.L182

0000067c e59d0010    645 	ldr	r0,[sp,16]

00000680 e2800001    646 	add	r0,r0,1

00000684 e58d0010    647 	str	r0,[sp,16]

00000688 e59d0020    648 	ldr	r0,[sp,32]

0000068c e28bb001    649 	add	fp,fp,1

00000690 e15b0000    650 	cmp	fp,r0

00000694 bafffe79    651 	blt	.L186

                     652 .L184:

00000698 e3a00001    653 	mov	r0,1

                     654 .L176:

0000069c e28dd030    655 	add	sp,sp,48

000006a0 e8bd8ff0    656 	ldmfd	[sp]!,{r4-fp,pc}

                     657 	.endf	OscConverter_processPeriod

                     658 	.align	4

                     659 ;formatBuffer	r4	local

                     660 ;datBuffer	r7	local


                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s
                     661 ;oscInfo	r5	local

                     662 ;pointPerFrame	[sp,32]	local

                     663 ;analogCount	[sp,28]	local

                     664 ;boolCount	[sp,24]	local

                     665 ;pointNum	fp	local

                     666 ;sampleNum	[sp,16]	local

                     667 ;tick	[sp,40]	local

                     668 ;analogNum	r8	local

                     669 ;boolNum	r8	local

                     670 ;freq	[sp,4]	local

                     671 ;.L888	.L896	static

                     672 ;.L889	.L895	static

                     673 ;.L890	.L894	static

                     674 ;.L891	.L897	static

                     675 

                     676 ;readFileContext	r10	param

                     677 

                     678 	.section ".bss","awb"

                     679 .L887:

                     680 	.section ".rodata","a"

                     681 .L894:;	",%d\000"

00000000 0064252c    682 	.data.b	44,37,100,0

                     683 	.type	.L894,$object

                     684 	.size	.L894,4

                     685 	.data

                     686 	.text

                     687 

                     688 

                     689 	.align	4

                     690 	.align	4

                     691 writeFormatTimeToCfgBuffer:

000006a4 e92d4072    692 	stmfd	[sp]!,{r1,r4-r6,lr}

000006a8 e1a05002    693 	mov	r5,r2

000006ac e24dd03c    694 	sub	sp,sp,60

000006b0 e58d103c    695 	str	r1,[sp,60]

000006b4 e28d103c    696 	add	r1,sp,60

000006b8 e2804050    697 	add	r4,r0,80

000006bc e2806e45    698 	add	r6,r0,0x0450

000006c0 e28d0018    699 	add	r0,sp,24

000006c4 eb000000*   700 	bl	TimeTools_gmtime32

000006c8 e3a01e40    701 	mov	r1,1<<10

000006cc e3500001    702 	cmp	r0,1

000006d0 1a000014    703 	bne	.L1033

000006d4 e59d202c    704 	ldr	r2,[sp,44]

000006d8 e59d3020    705 	ldr	r3,[sp,32]

000006dc e2820e70    706 	add	r0,r2,7<<8

000006e0 e280206c    707 	add	r2,r0,108

000006e4 e59d0028    708 	ldr	r0,[sp,40]

000006e8 e58d202c    709 	str	r2,[sp,44]

000006ec e2800001    710 	add	r0,r0,1

000006f0 e59dc018    711 	ldr	r12,[sp,24]

000006f4 e1a0e005    712 	mov	lr,r5

000006f8 e59d501c    713 	ldr	r5,[sp,28]

000006fc e58d0028    714 	str	r0,[sp,40]

00000700 e88d502d    715 	stmea	[sp],{r0,r2-r3,r5,r12,lr}

00000704 e59d3024    716 	ldr	r3,[sp,36]

00000708 e28f2000*   717 	adr	r2,.L1087

0000070c e1a00004    718 	mov	r0,r4

00000710 eb000000*   719 	bl	snprintf

00000714 e1a01004    720 	mov	r1,r4

00000718 e1a02000    721 	mov	r2,r0


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s
0000071c e1a00006    722 	mov	r0,r6

00000720 eb000000*   723 	bl	OscWriteBuffer_write

00000724 ea000007    724 	b	.L1031

                     725 .L1033:

00000728 e1a03005    726 	mov	r3,r5

0000072c e28f2000*   727 	adr	r2,.L1088

00000730 e1a00004    728 	mov	r0,r4

00000734 eb000000*   729 	bl	snprintf

00000738 e1a01004    730 	mov	r1,r4

0000073c e1a02000    731 	mov	r2,r0

00000740 e1a00006    732 	mov	r0,r6

00000744 eb000000*   733 	bl	OscWriteBuffer_write

                     734 .L1031:

00000748 e28dd03c    735 	add	sp,sp,60

0000074c e8bd4072    736 	ldmfd	[sp]!,{r1,r4-r6,lr}

00000750 e12fff1e*   737 	ret	

                     738 	.endf	writeFormatTimeToCfgBuffer

                     739 	.align	4

                     740 ;formatBuffer	r4	local

                     741 ;cfgBuffer	r6	local

                     742 ;size	r2	local

                     743 ;tmTime	[sp,24]	local

                     744 ;.L1073	.L1077	static

                     745 ;.L1074	.L1078	static

                     746 

                     747 ;readFileContext	r0	param

                     748 ;t	[sp,60]	param

                     749 ;ms	r5	param

                     750 

                     751 	.section ".bss","awb"

                     752 .L1072:

                     753 	.data

                     754 	.text

                     755 

                     756 

                     757 ;287: 


                     758 ;288: 


                     759 ;289: bool  OscConverter_processCfg(OscReadFileContext * readFileContext)


                     760 	.align	4

                     761 	.align	4

                     762 	.align	4

                     763 OscConverter_processCfg::

00000754 e92d4ff0    764 	stmfd	[sp]!,{r4-fp,lr}

                     765 ;290: {


                     766 

                     767 ;291: 	char *formatBuffer = readFileContext->formatBuffer;


                     768 

00000758 e1a09000    769 	mov	r9,r0

0000075c e280a050    770 	add	r10,r0,80

00000760 e24dd078    771 	sub	sp,sp,120

00000764 e58da058    772 	str	r10,[sp,88]

                     773 ;292: 	OscWriteBuffer *cfgBuffer = &readFileContext->cfgBuffer;


                     774 

00000768 e280be45    775 	add	fp,r0,0x0450

0000076c e590502c    776 	ldr	r5,[r0,44]

                     777 ;299: 	if (!oscInfo)


                     778 

00000770 e58db05c    779 	str	fp,[sp,92]

                     780 ;293: 	OSCInfoStruct *oscInfo;


                     781 ;294: 	int size;


                     782 ;295: 	int analogCount;



                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s
                     783 ;296: 	int boolCount;


                     784 ;297: 


                     785 ;298: 	oscInfo = readFileContext->oscInfo;


                     786 

00000774 e3550000    787 	cmp	r5,0

00000778 0a000372    788 	beq	.L1163

                     789 ;300: 	{


                     790 

                     791 ;301: 		return false;


                     792 

                     793 ;302: 	}


                     794 ;303: 


                     795 ;304: 	analogCount = OSCInfo_getAnalogCount(oscInfo);


                     796 

0000077c e1a00005    797 	mov	r0,r5

00000780 eb000000*   798 	bl	OSCInfo_getAnalogCount

00000784 e1a04000    799 	mov	r4,r0

                     800 ;305: 	boolCount = OSCInfo_getBoolCount(oscInfo);


                     801 

00000788 e1a00005    802 	mov	r0,r5

0000078c eb000000*   803 	bl	OSCInfo_getBoolCount

00000790 e1a05000    804 	mov	r5,r0

                     805 ;306: 


                     806 ;307: 	// имя блока и время


                     807 ;308: 	size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, "MTRA,%s,2013\r\n", OSCDescr_getTerminalName());


                     808 

00000794 eb000000*   809 	bl	OSCDescr_getTerminalName

00000798 e1a03000    810 	mov	r3,r0

0000079c e28f2000*   811 	adr	r2,.L2450

000007a0 e1a0000a    812 	mov	r0,r10

000007a4 e3a01e40    813 	mov	r1,1<<10

000007a8 eb000000*   814 	bl	snprintf

                     815 ;309: 	if (!OscWriteBuffer_write(cfgBuffer, formatBuffer, size)) return false;


                     816 

000007ac e1a0100a    817 	mov	r1,r10

000007b0 e1a02000    818 	mov	r2,r0

000007b4 e1a0000b    819 	mov	r0,fp

000007b8 eb000000*   820 	bl	OscWriteBuffer_write

000007bc e3500000    821 	cmp	r0,0

000007c0 0a000360    822 	beq	.L1163

                     823 ;310: 


                     824 ;311: 	// количество аналоговых и дискретных


                     825 ;312: 	size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, "%d,%dA,%dD\r\n", analogCount + boolCount, 


                     826 

000007c4 e88d0030    827 	stmea	[sp],{r4-r5}

000007c8 e0853004    828 	add	r3,r5,r4

000007cc e28f2000*   829 	adr	r2,.L2451

000007d0 e59d0058    830 	ldr	r0,[sp,88]

000007d4 e3a01e40    831 	mov	r1,1<<10

000007d8 eb000000*   832 	bl	snprintf

                     833 ;313: 		analogCount,


                     834 ;314: 		boolCount);


                     835 ;315: 	if (!OscWriteBuffer_write(cfgBuffer, formatBuffer, size)) return false;


                     836 

000007dc e1a02000    837 	mov	r2,r0

000007e0 e59d005c    838 	ldr	r0,[sp,92]

000007e4 e59d1058    839 	ldr	r1,[sp,88]

000007e8 eb000000*   840 	bl	OscWriteBuffer_write

000007ec e3500000    841 	cmp	r0,0

000007f0 0a000354    842 	beq	.L1163

                     843 ;316: 



                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s
                     844 ;317: 


                     845 ;318: 	// запись каналов


                     846 ;319: 


                     847 ;320: 	// аналоговые


                     848 ;321: 	if (!writeAnalogToCfg(readFileContext))


                     849 

                     850 ;193: {


                     851 

                     852 ;194: 	OSCInfoStruct *oscInfo = readFileContext->oscInfo;


                     853 

000007f4 e599502c    854 	ldr	r5,[r9,44]

                     855 ;195: 	int analogCount = OSCInfo_getAnalogCount(oscInfo);


                     856 

000007f8 e3a04000    857 	mov	r4,0

000007fc e1a00005    858 	mov	r0,r5

00000800 eb000000*   859 	bl	OSCInfo_getAnalogCount

                     860 ;196: 	OscWriteBuffer *cfgBuffer = &readFileContext->cfgBuffer;


                     861 

00000804 e289b050    862 	add	fp,r9,80

                     863 ;198: 	int analogNum;


                     864 ;199: 	int size;


                     865 ;200: 	// аналоговые


                     866 ;201: 	for (analogNum = 0; analogNum < analogCount; ++analogNum)


                     867 

00000808 e2891e45    868 	add	r1,r9,0x0450

0000080c e58d1050    869 	str	r1,[sp,80]

                     870 ;197: 	char *formatBuffer = readFileContext->formatBuffer;


                     871 

00000810 e3500000    872 	cmp	r0,0

00000814 a1a01000    873 	movge	r1,r0

00000818 b3a01000    874 	movlt	r1,0

0000081c e58d1060    875 	str	r1,[sp,96]

00000820 e1b00121    876 	movs	r0,r1 lsr 2

00000824 e58d0048    877 	str	r0,[sp,72]

00000828 0a000101    878 	beq	.L1472

0000082c e3a00000    879 	mov	r0,0

00000830 e58d0068    880 	str	r0,[sp,104]

00000834 e3a006ff    881 	mov	r0,255<<20

00000838 e28005c0    882 	add	r0,r0,3<<28

0000083c e58d006c    883 	str	r0,[sp,108]

00000840 e59f01b4*   884 	ldr	r0,.L2452

00000844 e59f71b4*   885 	ldr	r7,.L2453

00000848 e58d004c    886 	str	r0,[sp,76]

                     887 .L1473:

0000084c e1a01004    888 	mov	r1,r4

00000850 e1a00005    889 	mov	r0,r5

00000854 eb000000*   890 	bl	OSCInfo_getAnalog

00000858 e1a01004    891 	mov	r1,r4

0000085c e1a06000    892 	mov	r6,r0

00000860 e1a00005    893 	mov	r0,r5

00000864 eb000000*   894 	bl	OSCInfo_getAnalogCft

00000868 eb000000*   895 	bl	__ftod

0000086c e58d1074    896 	str	r1,[sp,116]

00000870 e1a01004    897 	mov	r1,r4

00000874 e58d0070    898 	str	r0,[sp,112]

00000878 e1a00005    899 	mov	r0,r5

0000087c eb000000*   900 	bl	OSCInfo_getAnalogMax

00000880 e1a01004    901 	mov	r1,r4

00000884 e1a0a000    902 	mov	r10,r0

00000888 e1a00005    903 	mov	r0,r5

0000088c eb000000*   904 	bl	OSCInfo_getAnalogMin


                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s
00000890 e58d0044    905 	str	r0,[sp,68]

00000894 e1a00006    906 	mov	r0,r6

00000898 eb000000*   907 	bl	OSCDescr_analogName

0000089c e1a08000    908 	mov	r8,r0

000008a0 e1a00006    909 	mov	r0,r6

000008a4 eb000000*   910 	bl	OSCDescr_analogUnits

000008a8 e3a0c053    911 	mov	r12,83

000008ac e59d2068    912 	ldr	r2,[sp,104]

000008b0 e59d306c    913 	ldr	r3,[sp,108]

000008b4 e58d202c    914 	str	r2,[sp,44]

000008b8 e59d106c    915 	ldr	r1,[sp,108]

000008bc e28de030    916 	add	lr,sp,48

000008c0 e88e100e    917 	stmea	[lr],{r1-r3,r12}

000008c4 e59d3044    918 	ldr	r3,[sp,68]

000008c8 e3a01000    919 	mov	r1,0

000008cc e1a02001    920 	mov	r2,r1

000008d0 e1a0e001    921 	mov	lr,r1

000008d4 e28dc01c    922 	add	r12,sp,28

000008d8 e88c040e    923 	stmea	[r12],{r1-r3,r10}

000008dc e59d3070    924 	ldr	r3,[sp,112]

000008e0 e59dc074    925 	ldr	r12,[sp,116]

000008e4 e1a01007    926 	mov	r1,r7

000008e8 e1a02000    927 	mov	r2,r0

000008ec e1a00007    928 	mov	r0,r7

000008f0 e98d500f    929 	stmfa	[sp],{r0-r3,r12,lr}

000008f4 e58d8000    930 	str	r8,[sp]

000008f8 e2843001    931 	add	r3,r4,1

000008fc e59d204c    932 	ldr	r2,[sp,76]

00000900 e1a0000b    933 	mov	r0,fp

00000904 e3a01e40    934 	mov	r1,1<<10

00000908 eb000000*   935 	bl	snprintf

0000090c e1a02000    936 	mov	r2,r0

00000910 e59d0050    937 	ldr	r0,[sp,80]

00000914 e1a0100b    938 	mov	r1,fp

00000918 eb000000*   939 	bl	OscWriteBuffer_write

0000091c e3500000    940 	cmp	r0,0

00000920 0a000308    941 	beq	.L1163

00000924 e2841001    942 	add	r1,r4,1

00000928 e1a00005    943 	mov	r0,r5

0000092c eb000000*   944 	bl	OSCInfo_getAnalog

00000930 e2841001    945 	add	r1,r4,1

00000934 e1a06000    946 	mov	r6,r0

00000938 e1a00005    947 	mov	r0,r5

0000093c eb000000*   948 	bl	OSCInfo_getAnalogCft

00000940 eb000000*   949 	bl	__ftod

00000944 e58d1074    950 	str	r1,[sp,116]

00000948 e2841001    951 	add	r1,r4,1

0000094c e58d0070    952 	str	r0,[sp,112]

00000950 e1a00005    953 	mov	r0,r5

00000954 eb000000*   954 	bl	OSCInfo_getAnalogMax

00000958 e2841001    955 	add	r1,r4,1

0000095c e1a0a000    956 	mov	r10,r0

00000960 e1a00005    957 	mov	r0,r5

00000964 eb000000*   958 	bl	OSCInfo_getAnalogMin

00000968 e58d0044    959 	str	r0,[sp,68]

0000096c e1a00006    960 	mov	r0,r6

00000970 eb000000*   961 	bl	OSCDescr_analogName

00000974 e1a08000    962 	mov	r8,r0

00000978 e1a00006    963 	mov	r0,r6

0000097c eb000000*   964 	bl	OSCDescr_analogUnits

00000980 e3a0c053    965 	mov	r12,83


                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s
00000984 e59d2068    966 	ldr	r2,[sp,104]

00000988 e59d306c    967 	ldr	r3,[sp,108]

0000098c e58d202c    968 	str	r2,[sp,44]

00000990 ea00001b    969 	b	.L2454

                     970 	.align	4

                     971 .L1030:

                     972 ;	"\r\n\000"

00000994 0a0d       973 	.data.b	13,10

00000996 00         974 	.data.b	0

00000997 00         975 	.align 4

                     976 

                     977 	.type	.L1030,$object

                     978 	.size	.L1030,4

                     979 

                     980 .L1087:

                     981 ;	"%02d/%02d/%04d,%02d:%02d:%02d.%03d\r\n\000"

00000998 64323025    982 	.data.b	37,48,50,100

0000099c 3230252f    983 	.data.b	47,37,48,50

000009a0 30252f64    984 	.data.b	100,47,37,48

000009a4 252c6434    985 	.data.b	52,100,44,37

000009a8 3a643230    986 	.data.b	48,50,100,58

000009ac 64323025    987 	.data.b	37,48,50,100

000009b0 3230253a    988 	.data.b	58,37,48,50

000009b4 30252e64    989 	.data.b	100,46,37,48

000009b8 0a0d6433    990 	.data.b	51,100,13,10

000009bc 00         991 	.data.b	0

000009bd 000000     992 	.align 4

                     993 

                     994 	.type	.L1087,$object

                     995 	.size	.L1087,4

                     996 

                     997 .L1088:

                     998 ;	"01/01/1980 00:00:00.%03d\r\n\000"

000009c0 302f3130    999 	.data.b	48,49,47,48

000009c4 39312f31   1000 	.data.b	49,47,49,57

000009c8 30203038   1001 	.data.b	56,48,32,48

000009cc 30303a30   1002 	.data.b	48,58,48,48

000009d0 2e30303a   1003 	.data.b	58,48,48,46

000009d4 64333025   1004 	.data.b	37,48,51,100

000009d8 0a0d      1005 	.data.b	13,10

000009da 00        1006 	.data.b	0

000009db 00        1007 	.align 4

                    1008 

                    1009 	.type	.L1088,$object

                    1010 	.size	.L1088,4

                    1011 

                    1012 .L2450:

                    1013 ;	"MTRA,%s,2013\r\n\000"

000009dc 4152544d   1014 	.data.b	77,84,82,65

000009e0 2c73252c   1015 	.data.b	44,37,115,44

000009e4 33313032   1016 	.data.b	50,48,49,51

000009e8 0a0d      1017 	.data.b	13,10

000009ea 00        1018 	.data.b	0

000009eb 00        1019 	.align 4

                    1020 

                    1021 	.type	.L2450,$object

                    1022 	.size	.L2450,4

                    1023 

                    1024 .L2451:

                    1025 ;	"%d,%dA,%dD\r\n\000"

000009ec 252c6425   1026 	.data.b	37,100,44,37


                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s
000009f0 252c4164   1027 	.data.b	100,65,44,37

000009f4 0a0d4464   1028 	.data.b	100,68,13,10

000009f8 00        1029 	.data.b	0

000009f9 000000    1030 	.align 4

                    1031 

                    1032 	.type	.L2451,$object

                    1033 	.size	.L2451,4

                    1034 

                    1035 .L2452:

000009fc 00000000*  1036 	.data.w	.L2254

                    1037 	.type	.L2452,$object

                    1038 	.size	.L2452,4

                    1039 

                    1040 .L2453:

00000a00 00000000*  1041 	.data.w	.L2252

                    1042 	.type	.L2453,$object

                    1043 	.size	.L2453,4

                    1044 

                    1045 .L2454:

                    1046 

00000a04 e59d106c   1047 	ldr	r1,[sp,108]

00000a08 e28de030   1048 	add	lr,sp,48

00000a0c e88e100e   1049 	stmea	[lr],{r1-r3,r12}

00000a10 e59d3044   1050 	ldr	r3,[sp,68]

00000a14 e3a01000   1051 	mov	r1,0

00000a18 e1a02001   1052 	mov	r2,r1

00000a1c e1a0e001   1053 	mov	lr,r1

00000a20 e28dc01c   1054 	add	r12,sp,28

00000a24 e88c040e   1055 	stmea	[r12],{r1-r3,r10}

00000a28 e59d3070   1056 	ldr	r3,[sp,112]

00000a2c e59dc074   1057 	ldr	r12,[sp,116]

00000a30 e1a01007   1058 	mov	r1,r7

00000a34 e1a02000   1059 	mov	r2,r0

00000a38 e1a00007   1060 	mov	r0,r7

00000a3c e98d500f   1061 	stmfa	[sp],{r0-r3,r12,lr}

00000a40 e58d8000   1062 	str	r8,[sp]

00000a44 e2843002   1063 	add	r3,r4,2

00000a48 e59d204c   1064 	ldr	r2,[sp,76]

00000a4c e1a0000b   1065 	mov	r0,fp

00000a50 e3a01e40   1066 	mov	r1,1<<10

00000a54 eb000000*  1067 	bl	snprintf

00000a58 e1a02000   1068 	mov	r2,r0

00000a5c e59d0050   1069 	ldr	r0,[sp,80]

00000a60 e1a0100b   1070 	mov	r1,fp

00000a64 eb000000*  1071 	bl	OscWriteBuffer_write

00000a68 e3500000   1072 	cmp	r0,0

00000a6c 0a0002b5   1073 	beq	.L1163

00000a70 e2841002   1074 	add	r1,r4,2

00000a74 e1a00005   1075 	mov	r0,r5

00000a78 eb000000*  1076 	bl	OSCInfo_getAnalog

00000a7c e2841002   1077 	add	r1,r4,2

00000a80 e1a06000   1078 	mov	r6,r0

00000a84 e1a00005   1079 	mov	r0,r5

00000a88 eb000000*  1080 	bl	OSCInfo_getAnalogCft

00000a8c eb000000*  1081 	bl	__ftod

00000a90 e58d1074   1082 	str	r1,[sp,116]

00000a94 e2841002   1083 	add	r1,r4,2

00000a98 e58d0070   1084 	str	r0,[sp,112]

00000a9c e1a00005   1085 	mov	r0,r5

00000aa0 eb000000*  1086 	bl	OSCInfo_getAnalogMax

00000aa4 e2841002   1087 	add	r1,r4,2


                                                                      Page 19
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s
00000aa8 e1a0a000   1088 	mov	r10,r0

00000aac e1a00005   1089 	mov	r0,r5

00000ab0 eb000000*  1090 	bl	OSCInfo_getAnalogMin

00000ab4 e58d0044   1091 	str	r0,[sp,68]

00000ab8 e1a00006   1092 	mov	r0,r6

00000abc eb000000*  1093 	bl	OSCDescr_analogName

00000ac0 e1a08000   1094 	mov	r8,r0

00000ac4 e1a00006   1095 	mov	r0,r6

00000ac8 eb000000*  1096 	bl	OSCDescr_analogUnits

00000acc e3a0c053   1097 	mov	r12,83

00000ad0 e59d2068   1098 	ldr	r2,[sp,104]

00000ad4 e59d306c   1099 	ldr	r3,[sp,108]

00000ad8 e58d202c   1100 	str	r2,[sp,44]

00000adc e59d106c   1101 	ldr	r1,[sp,108]

00000ae0 e28de030   1102 	add	lr,sp,48

00000ae4 e88e100e   1103 	stmea	[lr],{r1-r3,r12}

00000ae8 e59d3044   1104 	ldr	r3,[sp,68]

00000aec e3a01000   1105 	mov	r1,0

00000af0 e1a02001   1106 	mov	r2,r1

00000af4 e1a0e001   1107 	mov	lr,r1

00000af8 e28dc01c   1108 	add	r12,sp,28

00000afc e88c040e   1109 	stmea	[r12],{r1-r3,r10}

00000b00 e59d3070   1110 	ldr	r3,[sp,112]

00000b04 e59dc074   1111 	ldr	r12,[sp,116]

00000b08 e1a01007   1112 	mov	r1,r7

00000b0c e1a02000   1113 	mov	r2,r0

00000b10 e1a00007   1114 	mov	r0,r7

00000b14 e98d500f   1115 	stmfa	[sp],{r0-r3,r12,lr}

00000b18 e58d8000   1116 	str	r8,[sp]

00000b1c e2843003   1117 	add	r3,r4,3

00000b20 e59d204c   1118 	ldr	r2,[sp,76]

00000b24 e1a0000b   1119 	mov	r0,fp

00000b28 e3a01e40   1120 	mov	r1,1<<10

00000b2c eb000000*  1121 	bl	snprintf

00000b30 e1a02000   1122 	mov	r2,r0

00000b34 e59d0050   1123 	ldr	r0,[sp,80]

00000b38 e1a0100b   1124 	mov	r1,fp

00000b3c eb000000*  1125 	bl	OscWriteBuffer_write

00000b40 e3500000   1126 	cmp	r0,0

00000b44 0a00027f   1127 	beq	.L1163

00000b48 e2841003   1128 	add	r1,r4,3

00000b4c e1a00005   1129 	mov	r0,r5

00000b50 eb000000*  1130 	bl	OSCInfo_getAnalog

00000b54 e2841003   1131 	add	r1,r4,3

00000b58 e1a06000   1132 	mov	r6,r0

00000b5c e1a00005   1133 	mov	r0,r5

00000b60 eb000000*  1134 	bl	OSCInfo_getAnalogCft

00000b64 eb000000*  1135 	bl	__ftod

00000b68 e58d1074   1136 	str	r1,[sp,116]

00000b6c e2841003   1137 	add	r1,r4,3

00000b70 e58d0070   1138 	str	r0,[sp,112]

00000b74 e1a00005   1139 	mov	r0,r5

00000b78 eb000000*  1140 	bl	OSCInfo_getAnalogMax

00000b7c e2841003   1141 	add	r1,r4,3

00000b80 e1a0a000   1142 	mov	r10,r0

00000b84 e1a00005   1143 	mov	r0,r5

00000b88 eb000000*  1144 	bl	OSCInfo_getAnalogMin

00000b8c e58d0044   1145 	str	r0,[sp,68]

00000b90 e1a00006   1146 	mov	r0,r6

00000b94 eb000000*  1147 	bl	OSCDescr_analogName

00000b98 e1a08000   1148 	mov	r8,r0


                                                                      Page 20
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s
00000b9c e1a00006   1149 	mov	r0,r6

00000ba0 eb000000*  1150 	bl	OSCDescr_analogUnits

00000ba4 e3a0c053   1151 	mov	r12,83

00000ba8 e59d2068   1152 	ldr	r2,[sp,104]

00000bac e59d306c   1153 	ldr	r3,[sp,108]

00000bb0 e58d202c   1154 	str	r2,[sp,44]

00000bb4 e59d106c   1155 	ldr	r1,[sp,108]

00000bb8 e28de030   1156 	add	lr,sp,48

00000bbc e88e100e   1157 	stmea	[lr],{r1-r3,r12}

00000bc0 e59dc044   1158 	ldr	r12,[sp,68]

00000bc4 e1a0e00a   1159 	mov	lr,r10

00000bc8 e3a0a000   1160 	mov	r10,0

00000bcc e1a0200a   1161 	mov	r2,r10

00000bd0 e1a0300a   1162 	mov	r3,r10

00000bd4 e28d1018   1163 	add	r1,sp,24

00000bd8 e881540c   1164 	stmea	[r1],{r2-r3,r10,r12,lr}

00000bdc e59dc070   1165 	ldr	r12,[sp,112]

00000be0 e59de074   1166 	ldr	lr,[sp,116]

00000be4 e1a01007   1167 	mov	r1,r7

00000be8 e1a0a000   1168 	mov	r10,r0

00000bec e1a00008   1169 	mov	r0,r8

00000bf0 e88d5483   1170 	stmea	[sp],{r0-r1,r7,r10,r12,lr}

00000bf4 e2843004   1171 	add	r3,r4,4

00000bf8 e59d204c   1172 	ldr	r2,[sp,76]

00000bfc e1a0000b   1173 	mov	r0,fp

00000c00 e3a01e40   1174 	mov	r1,1<<10

00000c04 eb000000*  1175 	bl	snprintf

00000c08 e1a02000   1176 	mov	r2,r0

00000c0c e59d0050   1177 	ldr	r0,[sp,80]

00000c10 e1a0100b   1178 	mov	r1,fp

00000c14 eb000000*  1179 	bl	OscWriteBuffer_write

00000c18 e3500000   1180 	cmp	r0,0

00000c1c 0a000249   1181 	beq	.L1163

00000c20 e59d0048   1182 	ldr	r0,[sp,72]

00000c24 e2844004   1183 	add	r4,r4,4

00000c28 e2500001   1184 	subs	r0,r0,1

00000c2c e58d0048   1185 	str	r0,[sp,72]

00000c30 1affff05   1186 	bne	.L1473

                    1187 .L1472:

00000c34 e59d0060   1188 	ldr	r0,[sp,96]

00000c38 e2100003   1189 	ands	r0,r0,3

00000c3c e58d0048   1190 	str	r0,[sp,72]

00000c40 0a000043   1191 	beq	.L1116

00000c44 e3a00000   1192 	mov	r0,0

00000c48 e58d0068   1193 	str	r0,[sp,104]

00000c4c e3a006ff   1194 	mov	r0,255<<20

00000c50 e28005c0   1195 	add	r0,r0,3<<28

00000c54 e58d006c   1196 	str	r0,[sp,108]

00000c58 e51f0264*  1197 	ldr	r0,.L2452

00000c5c e51f7264*  1198 	ldr	r7,.L2453

00000c60 e58d004c   1199 	str	r0,[sp,76]

                    1200 .L1491:

00000c64 e1a01004   1201 	mov	r1,r4

00000c68 e1a00005   1202 	mov	r0,r5

00000c6c eb000000*  1203 	bl	OSCInfo_getAnalog

00000c70 e1a01004   1204 	mov	r1,r4

00000c74 e1a06000   1205 	mov	r6,r0

00000c78 e1a00005   1206 	mov	r0,r5

00000c7c eb000000*  1207 	bl	OSCInfo_getAnalogCft

00000c80 eb000000*  1208 	bl	__ftod

00000c84 e58d1074   1209 	str	r1,[sp,116]


                                                                      Page 21
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s
00000c88 e1a01004   1210 	mov	r1,r4

00000c8c e58d0070   1211 	str	r0,[sp,112]

00000c90 e1a00005   1212 	mov	r0,r5

00000c94 eb000000*  1213 	bl	OSCInfo_getAnalogMax

00000c98 e1a01004   1214 	mov	r1,r4

00000c9c e1a0a000   1215 	mov	r10,r0

00000ca0 e1a00005   1216 	mov	r0,r5

00000ca4 eb000000*  1217 	bl	OSCInfo_getAnalogMin

00000ca8 e1a08000   1218 	mov	r8,r0

00000cac e1a00006   1219 	mov	r0,r6

00000cb0 eb000000*  1220 	bl	OSCDescr_analogName

00000cb4 e58d0060   1221 	str	r0,[sp,96]

00000cb8 e1a00006   1222 	mov	r0,r6

00000cbc eb000000*  1223 	bl	OSCDescr_analogUnits

00000cc0 e3a0c053   1224 	mov	r12,83

00000cc4 e59d2068   1225 	ldr	r2,[sp,104]

00000cc8 e59d306c   1226 	ldr	r3,[sp,108]

00000ccc e58d202c   1227 	str	r2,[sp,44]

00000cd0 e59d106c   1228 	ldr	r1,[sp,108]

00000cd4 e28de030   1229 	add	lr,sp,48

00000cd8 e88e100e   1230 	stmea	[lr],{r1-r3,r12}

00000cdc e1a0c00a   1231 	mov	r12,r10

00000ce0 e1a0a008   1232 	mov	r10,r8

00000ce4 e3a08000   1233 	mov	r8,0

00000ce8 e1a02008   1234 	mov	r2,r8

00000cec e59d1070   1235 	ldr	r1,[sp,112]

00000cf0 e1a03008   1236 	mov	r3,r8

00000cf4 e58d1010   1237 	str	r1,[sp,16]

00000cf8 e59d1074   1238 	ldr	r1,[sp,116]

00000cfc e28de014   1239 	add	lr,sp,20

00000d00 e88e150e   1240 	stmea	[lr],{r1-r3,r8,r10,r12}

00000d04 e1a08000   1241 	mov	r8,r0

00000d08 e59d0060   1242 	ldr	r0,[sp,96]

00000d0c e1a01007   1243 	mov	r1,r7

00000d10 e88d0183   1244 	stmea	[sp],{r0-r1,r7-r8}

00000d14 e2843001   1245 	add	r3,r4,1

00000d18 e59d204c   1246 	ldr	r2,[sp,76]

00000d1c e1a0000b   1247 	mov	r0,fp

00000d20 e3a01e40   1248 	mov	r1,1<<10

00000d24 eb000000*  1249 	bl	snprintf

00000d28 e1a02000   1250 	mov	r2,r0

00000d2c e59d0050   1251 	ldr	r0,[sp,80]

00000d30 e1a0100b   1252 	mov	r1,fp

00000d34 eb000000*  1253 	bl	OscWriteBuffer_write

00000d38 e3500000   1254 	cmp	r0,0

00000d3c 0a000201   1255 	beq	.L1163

00000d40 e59d0048   1256 	ldr	r0,[sp,72]

00000d44 e2844001   1257 	add	r4,r4,1

00000d48 e2500001   1258 	subs	r0,r0,1

00000d4c e58d0048   1259 	str	r0,[sp,72]

00000d50 1affffc3   1260 	bne	.L1491

                    1261 .L1116:

                    1262 ;224: 


                    1263 ;225: 	}


                    1264 ;226: 


                    1265 ;227: 	return true;


                    1266 

                    1267 ;322: 	{


                    1268 

                    1269 ;323: 		return false;


                    1270 


                                                                      Page 22
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s
                    1271 ;324: 	}


                    1272 ;325: 


                    1273 ;326: 	// дискретные


                    1274 ;327: 	if (!writeBoolToCfg(readFileContext))


                    1275 

                    1276 ;232: {


                    1277 

                    1278 ;233: 	OSCInfoStruct *oscInfo = readFileContext->oscInfo;


                    1279 

00000d54 e599602c   1280 	ldr	r6,[r9,44]

                    1281 ;234: 	int boolCount = OSCInfo_getBoolCount(oscInfo);


                    1282 

00000d58 e3a04000   1283 	mov	r4,0

00000d5c e1a00006   1284 	mov	r0,r6

00000d60 eb000000*  1285 	bl	OSCInfo_getBoolCount

                    1286 ;235: 	OscWriteBuffer *cfgBuffer = &readFileContext->cfgBuffer;


                    1287 

00000d64 e289be45   1288 	add	fp,r9,0x0450

                    1289 ;236: 	char *formatBuffer = readFileContext->formatBuffer;


                    1290 

00000d68 e2895050   1291 	add	r5,r9,80

                    1292 ;237: 	int boolNum;


                    1293 ;238: 	int size;


                    1294 ;239: 


                    1295 ;240: 	for (boolNum = 0; boolNum < boolCount; ++boolNum)


                    1296 

00000d6c e3500000   1297 	cmp	r0,0

00000d70 a1a01000   1298 	movge	r1,r0

00000d74 b3a01000   1299 	movlt	r1,0

00000d78 e58d1060   1300 	str	r1,[sp,96]

00000d7c e1b081a1   1301 	movs	r8,r1 lsr 3

00000d80 0a00008b   1302 	beq	.L1431

00000d84 e59f770c*  1303 	ldr	r7,.L2455

                    1304 .L1432:

00000d88 e1a01004   1305 	mov	r1,r4

00000d8c e1a00006   1306 	mov	r0,r6

00000d90 eb000000*  1307 	bl	OSCInfo_getBool

00000d94 eb000000*  1308 	bl	OSCDescr_boolName

00000d98 e3a01001   1309 	mov	r1,1

00000d9c e88d0003   1310 	stmea	[sp],{r0-r1}

00000da0 e2843001   1311 	add	r3,r4,1

00000da4 e1a02007   1312 	mov	r2,r7

00000da8 e1a00005   1313 	mov	r0,r5

00000dac e3a01e40   1314 	mov	r1,1<<10

00000db0 eb000000*  1315 	bl	snprintf

00000db4 e1a01005   1316 	mov	r1,r5

00000db8 e1a02000   1317 	mov	r2,r0

00000dbc e1a0000b   1318 	mov	r0,fp

00000dc0 eb000000*  1319 	bl	OscWriteBuffer_write

00000dc4 e3500000   1320 	cmp	r0,0

00000dc8 0a0001de   1321 	beq	.L1163

00000dcc e2841001   1322 	add	r1,r4,1

00000dd0 e1a00006   1323 	mov	r0,r6

00000dd4 eb000000*  1324 	bl	OSCInfo_getBool

00000dd8 eb000000*  1325 	bl	OSCDescr_boolName

00000ddc e3a01001   1326 	mov	r1,1

00000de0 e88d0003   1327 	stmea	[sp],{r0-r1}

00000de4 e2843002   1328 	add	r3,r4,2

00000de8 e1a02007   1329 	mov	r2,r7

00000dec e1a00005   1330 	mov	r0,r5

00000df0 e3a01e40   1331 	mov	r1,1<<10


                                                                      Page 23
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s
00000df4 eb000000*  1332 	bl	snprintf

00000df8 e1a01005   1333 	mov	r1,r5

00000dfc e1a02000   1334 	mov	r2,r0

00000e00 e1a0000b   1335 	mov	r0,fp

00000e04 eb000000*  1336 	bl	OscWriteBuffer_write

00000e08 e3500000   1337 	cmp	r0,0

00000e0c 0a0001cd   1338 	beq	.L1163

00000e10 e2841002   1339 	add	r1,r4,2

00000e14 e1a00006   1340 	mov	r0,r6

00000e18 eb000000*  1341 	bl	OSCInfo_getBool

00000e1c eb000000*  1342 	bl	OSCDescr_boolName

00000e20 e3a01001   1343 	mov	r1,1

00000e24 e88d0003   1344 	stmea	[sp],{r0-r1}

00000e28 e2843003   1345 	add	r3,r4,3

00000e2c e1a02007   1346 	mov	r2,r7

00000e30 e1a00005   1347 	mov	r0,r5

00000e34 e3a01e40   1348 	mov	r1,1<<10

00000e38 eb000000*  1349 	bl	snprintf

00000e3c e1a01005   1350 	mov	r1,r5

00000e40 e1a02000   1351 	mov	r2,r0

00000e44 e1a0000b   1352 	mov	r0,fp

00000e48 eb000000*  1353 	bl	OscWriteBuffer_write

00000e4c e3500000   1354 	cmp	r0,0

00000e50 0a0001bc   1355 	beq	.L1163

00000e54 e2841003   1356 	add	r1,r4,3

00000e58 e1a00006   1357 	mov	r0,r6

00000e5c eb000000*  1358 	bl	OSCInfo_getBool

00000e60 eb000000*  1359 	bl	OSCDescr_boolName

00000e64 e3a01001   1360 	mov	r1,1

00000e68 e88d0003   1361 	stmea	[sp],{r0-r1}

00000e6c e2843004   1362 	add	r3,r4,4

00000e70 e1a02007   1363 	mov	r2,r7

00000e74 e1a00005   1364 	mov	r0,r5

00000e78 e3a01e40   1365 	mov	r1,1<<10

00000e7c eb000000*  1366 	bl	snprintf

00000e80 e1a01005   1367 	mov	r1,r5

00000e84 e1a02000   1368 	mov	r2,r0

00000e88 e1a0000b   1369 	mov	r0,fp

00000e8c eb000000*  1370 	bl	OscWriteBuffer_write

00000e90 e3500000   1371 	cmp	r0,0

00000e94 0a0001ab   1372 	beq	.L1163

00000e98 e2841004   1373 	add	r1,r4,4

00000e9c e1a00006   1374 	mov	r0,r6

00000ea0 eb000000*  1375 	bl	OSCInfo_getBool

00000ea4 eb000000*  1376 	bl	OSCDescr_boolName

00000ea8 e3a01001   1377 	mov	r1,1

00000eac e88d0003   1378 	stmea	[sp],{r0-r1}

00000eb0 e2843005   1379 	add	r3,r4,5

00000eb4 e1a02007   1380 	mov	r2,r7

00000eb8 e1a00005   1381 	mov	r0,r5

00000ebc e3a01e40   1382 	mov	r1,1<<10

00000ec0 eb000000*  1383 	bl	snprintf

00000ec4 e1a01005   1384 	mov	r1,r5

00000ec8 e1a02000   1385 	mov	r2,r0

00000ecc e1a0000b   1386 	mov	r0,fp

00000ed0 eb000000*  1387 	bl	OscWriteBuffer_write

00000ed4 e3500000   1388 	cmp	r0,0

00000ed8 0a00019a   1389 	beq	.L1163

00000edc e2841005   1390 	add	r1,r4,5

00000ee0 e1a00006   1391 	mov	r0,r6

00000ee4 eb000000*  1392 	bl	OSCInfo_getBool


                                                                      Page 24
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s
00000ee8 eb000000*  1393 	bl	OSCDescr_boolName

00000eec e3a01001   1394 	mov	r1,1

00000ef0 e88d0003   1395 	stmea	[sp],{r0-r1}

00000ef4 e2843006   1396 	add	r3,r4,6

00000ef8 e1a02007   1397 	mov	r2,r7

00000efc e1a00005   1398 	mov	r0,r5

00000f00 e3a01e40   1399 	mov	r1,1<<10

00000f04 eb000000*  1400 	bl	snprintf

00000f08 e1a01005   1401 	mov	r1,r5

00000f0c e1a02000   1402 	mov	r2,r0

00000f10 e1a0000b   1403 	mov	r0,fp

00000f14 eb000000*  1404 	bl	OscWriteBuffer_write

00000f18 e3500000   1405 	cmp	r0,0

00000f1c 0a000189   1406 	beq	.L1163

00000f20 e2841006   1407 	add	r1,r4,6

00000f24 e1a00006   1408 	mov	r0,r6

00000f28 eb000000*  1409 	bl	OSCInfo_getBool

00000f2c eb000000*  1410 	bl	OSCDescr_boolName

00000f30 e3a01001   1411 	mov	r1,1

00000f34 e88d0003   1412 	stmea	[sp],{r0-r1}

00000f38 e2843007   1413 	add	r3,r4,7

00000f3c e1a02007   1414 	mov	r2,r7

00000f40 e1a00005   1415 	mov	r0,r5

00000f44 e3a01e40   1416 	mov	r1,1<<10

00000f48 eb000000*  1417 	bl	snprintf

00000f4c e1a01005   1418 	mov	r1,r5

00000f50 e1a02000   1419 	mov	r2,r0

00000f54 e1a0000b   1420 	mov	r0,fp

00000f58 eb000000*  1421 	bl	OscWriteBuffer_write

00000f5c e3500000   1422 	cmp	r0,0

00000f60 0a000178   1423 	beq	.L1163

00000f64 e2841007   1424 	add	r1,r4,7

00000f68 e1a00006   1425 	mov	r0,r6

00000f6c eb000000*  1426 	bl	OSCInfo_getBool

00000f70 eb000000*  1427 	bl	OSCDescr_boolName

00000f74 e3a01001   1428 	mov	r1,1

00000f78 e88d0003   1429 	stmea	[sp],{r0-r1}

00000f7c e2843008   1430 	add	r3,r4,8

00000f80 e1a02007   1431 	mov	r2,r7

00000f84 e1a00005   1432 	mov	r0,r5

00000f88 e3a01e40   1433 	mov	r1,1<<10

00000f8c eb000000*  1434 	bl	snprintf

00000f90 e1a01005   1435 	mov	r1,r5

00000f94 e1a02000   1436 	mov	r2,r0

00000f98 e1a0000b   1437 	mov	r0,fp

00000f9c eb000000*  1438 	bl	OscWriteBuffer_write

00000fa0 e3500000   1439 	cmp	r0,0

00000fa4 0a000167   1440 	beq	.L1163

00000fa8 e2844008   1441 	add	r4,r4,8

00000fac e2588001   1442 	subs	r8,r8,1

00000fb0 1affff74   1443 	bne	.L1432

                    1444 .L1431:

00000fb4 e59d0060   1445 	ldr	r0,[sp,96]

00000fb8 e2108007   1446 	ands	r8,r0,7

00000fbc 0a000014   1447 	beq	.L1128

00000fc0 e59f74d0*  1448 	ldr	r7,.L2455

                    1449 .L1466:

00000fc4 e1a01004   1450 	mov	r1,r4

00000fc8 e1a00006   1451 	mov	r0,r6

00000fcc eb000000*  1452 	bl	OSCInfo_getBool

00000fd0 eb000000*  1453 	bl	OSCDescr_boolName


                                                                      Page 25
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s
00000fd4 e3a01001   1454 	mov	r1,1

00000fd8 e88d0003   1455 	stmea	[sp],{r0-r1}

00000fdc e2843001   1456 	add	r3,r4,1

00000fe0 e1a02007   1457 	mov	r2,r7

00000fe4 e1a00005   1458 	mov	r0,r5

00000fe8 e3a01e40   1459 	mov	r1,1<<10

00000fec eb000000*  1460 	bl	snprintf

00000ff0 e1a01005   1461 	mov	r1,r5

00000ff4 e1a02000   1462 	mov	r2,r0

00000ff8 e1a0000b   1463 	mov	r0,fp

00000ffc eb000000*  1464 	bl	OscWriteBuffer_write

00001000 e3500000   1465 	cmp	r0,0

00001004 0a00014f   1466 	beq	.L1163

00001008 e2844001   1467 	add	r4,r4,1

0000100c e2588001   1468 	subs	r8,r8,1

00001010 1affffeb   1469 	bne	.L1466

                    1470 .L1128:

                    1471 ;249: 	}


                    1472 ;250: 


                    1473 ;251: 	return true;


                    1474 

                    1475 ;328: 	{


                    1476 

                    1477 ;329: 		return false;


                    1478 

                    1479 ;330: 	}


                    1480 ;331: 	// изменение частоты


                    1481 ;332: 	if (!writeFrequencyToCfg(readFileContext))


                    1482 

                    1483 ;256: {


                    1484 

                    1485 ;257: 	OscWriteBuffer *cfgBuffer = &readFileContext->cfgBuffer;


                    1486 

00001014 e2897e45   1487 	add	r7,r9,0x0450

                    1488 ;258: 	char *formatBuffer = readFileContext->formatBuffer;


                    1489 

00001018 e2895050   1490 	add	r5,r9,80

                    1491 ;259: 	double freq;


                    1492 ;260: 	int freqCount;


                    1493 ;261: 	int freqNum;


                    1494 ;262: 	int size;


                    1495 ;263: 	// частота


                    1496 ;264: 	freq = OSCDescr_getFreq();


                    1497 

0000101c eb000000*  1498 	bl	OSCDescr_getFreq

00001020 eb000000*  1499 	bl	__ftod

                    1500 ;265: 	size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, "%d\r\n", (int)freq);


                    1501 

00001024 e59f6470*  1502 	ldr	r6,.L2456

00001028 eb000000*  1503 	bl	__dtoi

0000102c e1a02006   1504 	mov	r2,r6

00001030 e1a03000   1505 	mov	r3,r0

00001034 e1a00005   1506 	mov	r0,r5

00001038 e3a01e40   1507 	mov	r1,1<<10

0000103c eb000000*  1508 	bl	snprintf

                    1509 ;266: 	if (!OscWriteBuffer_write(cfgBuffer, formatBuffer, size)) return false;


                    1510 

00001040 e1a01005   1511 	mov	r1,r5

00001044 e1a02000   1512 	mov	r2,r0

00001048 e1a00007   1513 	mov	r0,r7

0000104c eb000000*  1514 	bl	OscWriteBuffer_write


                                                                      Page 26
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s
00001050 e3500000   1515 	cmp	r0,0

00001054 0a00013b   1516 	beq	.L1163

                    1517 ;267: 


                    1518 ;268: 	// количество изменений частоты


                    1519 ;269: 	freqCount = OscReadFileContext_getFreqCount(readFileContext);


                    1520 

00001058 e1a00009   1521 	mov	r0,r9

0000105c eb000000*  1522 	bl	OscReadFileContext_getFreqCount

00001060 e1a04000   1523 	mov	r4,r0

                    1524 ;270: 	size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, "%d\r\n", freqCount);


                    1525 

00001064 e1a03004   1526 	mov	r3,r4

00001068 e1a02006   1527 	mov	r2,r6

0000106c e1a00005   1528 	mov	r0,r5

00001070 e3a01e40   1529 	mov	r1,1<<10

00001074 eb000000*  1530 	bl	snprintf

                    1531 ;271: 	if (!OscWriteBuffer_write(cfgBuffer, formatBuffer, size)) return false;


                    1532 

00001078 e1a01005   1533 	mov	r1,r5

0000107c e1a02000   1534 	mov	r2,r0

00001080 e1a00007   1535 	mov	r0,r7

00001084 eb000000*  1536 	bl	OscWriteBuffer_write

00001088 e3500000   1537 	cmp	r0,0

0000108c 0a00012d   1538 	beq	.L1163

                    1539 ;272: 


                    1540 ;273: 	// изменения частоты


                    1541 ;274: 	for (freqNum = 0; freqNum < freqCount; ++freqNum)


                    1542 

00001090 e3a06000   1543 	mov	r6,0

00001094 e3540000   1544 	cmp	r4,0

00001098 a1a00004   1545 	movge	r0,r4

0000109c b3a00000   1546 	movlt	r0,0

000010a0 e58d0060   1547 	str	r0,[sp,96]

000010a4 e1b081a0   1548 	movs	r8,r0 lsr 3

000010a8 0a0000ab   1549 	beq	.L1390

                    1550 .L1391:

000010ac e1a01006   1551 	mov	r1,r6

000010b0 e1a00009   1552 	mov	r0,r9

000010b4 eb000000*  1553 	bl	OscReadFileContext_getFreqCfg

000010b8 e1b04000   1554 	movs	r4,r0

000010bc 0a000121   1555 	beq	.L1163

000010c0 e5940000   1556 	ldr	r0,[r4]

000010c4 e59fb3d4*  1557 	ldr	fp,.L2457

000010c8 eb000000*  1558 	bl	__ftod

000010cc e5942004   1559 	ldr	r2,[r4,4]

000010d0 e24dd004   1560 	sub	sp,sp,4

000010d4 e88d0007   1561 	stmea	[sp],{r0-r2}

000010d8 e8bd0008   1562 	ldmfd	[sp]!,{r3}

000010dc e1a0200b   1563 	mov	r2,fp

000010e0 e1a00005   1564 	mov	r0,r5

000010e4 e3a01e40   1565 	mov	r1,1<<10

000010e8 eb000000*  1566 	bl	snprintf

000010ec e1a01005   1567 	mov	r1,r5

000010f0 e1a02000   1568 	mov	r2,r0

000010f4 e1a00007   1569 	mov	r0,r7

000010f8 eb000000*  1570 	bl	OscWriteBuffer_write

000010fc e3500000   1571 	cmp	r0,0

00001100 0a000110   1572 	beq	.L1163

00001104 e2861001   1573 	add	r1,r6,1

00001108 e1a00009   1574 	mov	r0,r9

0000110c eb000000*  1575 	bl	OscReadFileContext_getFreqCfg


                                                                      Page 27
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s
00001110 e1b04000   1576 	movs	r4,r0

00001114 0a00010b   1577 	beq	.L1163

00001118 e5940000   1578 	ldr	r0,[r4]

0000111c eb000000*  1579 	bl	__ftod

00001120 e5942004   1580 	ldr	r2,[r4,4]

00001124 e24dd004   1581 	sub	sp,sp,4

00001128 e88d0007   1582 	stmea	[sp],{r0-r2}

0000112c e8bd0008   1583 	ldmfd	[sp]!,{r3}

00001130 e1a0200b   1584 	mov	r2,fp

00001134 e1a00005   1585 	mov	r0,r5

00001138 e3a01e40   1586 	mov	r1,1<<10

0000113c eb000000*  1587 	bl	snprintf

00001140 e1a01005   1588 	mov	r1,r5

00001144 e1a02000   1589 	mov	r2,r0

00001148 e1a00007   1590 	mov	r0,r7

0000114c eb000000*  1591 	bl	OscWriteBuffer_write

00001150 e3500000   1592 	cmp	r0,0

00001154 0a0000fb   1593 	beq	.L1163

00001158 e2861002   1594 	add	r1,r6,2

0000115c e1a00009   1595 	mov	r0,r9

00001160 eb000000*  1596 	bl	OscReadFileContext_getFreqCfg

00001164 e1b04000   1597 	movs	r4,r0

00001168 0a0000f6   1598 	beq	.L1163

0000116c e5940000   1599 	ldr	r0,[r4]

00001170 eb000000*  1600 	bl	__ftod

00001174 e5942004   1601 	ldr	r2,[r4,4]

00001178 e24dd004   1602 	sub	sp,sp,4

0000117c e88d0007   1603 	stmea	[sp],{r0-r2}

00001180 e8bd0008   1604 	ldmfd	[sp]!,{r3}

00001184 e1a0200b   1605 	mov	r2,fp

00001188 e1a00005   1606 	mov	r0,r5

0000118c e3a01e40   1607 	mov	r1,1<<10

00001190 eb000000*  1608 	bl	snprintf

00001194 e1a01005   1609 	mov	r1,r5

00001198 e1a02000   1610 	mov	r2,r0

0000119c e1a00007   1611 	mov	r0,r7

000011a0 eb000000*  1612 	bl	OscWriteBuffer_write

000011a4 e3500000   1613 	cmp	r0,0

000011a8 0a0000e6   1614 	beq	.L1163

000011ac e2861003   1615 	add	r1,r6,3

000011b0 e1a00009   1616 	mov	r0,r9

000011b4 eb000000*  1617 	bl	OscReadFileContext_getFreqCfg

000011b8 e1b04000   1618 	movs	r4,r0

000011bc 0a0000e1   1619 	beq	.L1163

000011c0 e5940000   1620 	ldr	r0,[r4]

000011c4 eb000000*  1621 	bl	__ftod

000011c8 e5942004   1622 	ldr	r2,[r4,4]

000011cc e24dd004   1623 	sub	sp,sp,4

000011d0 e88d0007   1624 	stmea	[sp],{r0-r2}

000011d4 e8bd0008   1625 	ldmfd	[sp]!,{r3}

000011d8 e1a0200b   1626 	mov	r2,fp

000011dc e1a00005   1627 	mov	r0,r5

000011e0 e3a01e40   1628 	mov	r1,1<<10

000011e4 eb000000*  1629 	bl	snprintf

000011e8 e1a01005   1630 	mov	r1,r5

000011ec e1a02000   1631 	mov	r2,r0

000011f0 e1a00007   1632 	mov	r0,r7

000011f4 eb000000*  1633 	bl	OscWriteBuffer_write

000011f8 e3500000   1634 	cmp	r0,0

000011fc 0a0000d1   1635 	beq	.L1163

00001200 e2861004   1636 	add	r1,r6,4


                                                                      Page 28
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s
00001204 e1a00009   1637 	mov	r0,r9

00001208 eb000000*  1638 	bl	OscReadFileContext_getFreqCfg

0000120c e1b04000   1639 	movs	r4,r0

00001210 0a0000cc   1640 	beq	.L1163

00001214 e5940000   1641 	ldr	r0,[r4]

00001218 eb000000*  1642 	bl	__ftod

0000121c e5942004   1643 	ldr	r2,[r4,4]

00001220 e24dd004   1644 	sub	sp,sp,4

00001224 e88d0007   1645 	stmea	[sp],{r0-r2}

00001228 e8bd0008   1646 	ldmfd	[sp]!,{r3}

0000122c e1a0200b   1647 	mov	r2,fp

00001230 e1a00005   1648 	mov	r0,r5

00001234 e3a01e40   1649 	mov	r1,1<<10

00001238 eb000000*  1650 	bl	snprintf

0000123c e1a01005   1651 	mov	r1,r5

00001240 e1a02000   1652 	mov	r2,r0

00001244 e1a00007   1653 	mov	r0,r7

00001248 eb000000*  1654 	bl	OscWriteBuffer_write

0000124c e3500000   1655 	cmp	r0,0

00001250 0a0000bc   1656 	beq	.L1163

00001254 e2861005   1657 	add	r1,r6,5

00001258 e1a00009   1658 	mov	r0,r9

0000125c eb000000*  1659 	bl	OscReadFileContext_getFreqCfg

00001260 e1b04000   1660 	movs	r4,r0

00001264 0a0000b7   1661 	beq	.L1163

00001268 e5940000   1662 	ldr	r0,[r4]

0000126c eb000000*  1663 	bl	__ftod

00001270 e5942004   1664 	ldr	r2,[r4,4]

00001274 e24dd004   1665 	sub	sp,sp,4

00001278 e88d0007   1666 	stmea	[sp],{r0-r2}

0000127c e8bd0008   1667 	ldmfd	[sp]!,{r3}

00001280 e1a0200b   1668 	mov	r2,fp

00001284 e1a00005   1669 	mov	r0,r5

00001288 e3a01e40   1670 	mov	r1,1<<10

0000128c eb000000*  1671 	bl	snprintf

00001290 e1a01005   1672 	mov	r1,r5

00001294 e1a02000   1673 	mov	r2,r0

00001298 e1a00007   1674 	mov	r0,r7

0000129c eb000000*  1675 	bl	OscWriteBuffer_write

000012a0 e3500000   1676 	cmp	r0,0

000012a4 0a0000a7   1677 	beq	.L1163

000012a8 e2861006   1678 	add	r1,r6,6

000012ac e1a00009   1679 	mov	r0,r9

000012b0 eb000000*  1680 	bl	OscReadFileContext_getFreqCfg

000012b4 e1b04000   1681 	movs	r4,r0

000012b8 0a0000a2   1682 	beq	.L1163

000012bc e5940000   1683 	ldr	r0,[r4]

000012c0 eb000000*  1684 	bl	__ftod

000012c4 e5942004   1685 	ldr	r2,[r4,4]

000012c8 e24dd004   1686 	sub	sp,sp,4

000012cc e88d0007   1687 	stmea	[sp],{r0-r2}

000012d0 e8bd0008   1688 	ldmfd	[sp]!,{r3}

000012d4 e1a0200b   1689 	mov	r2,fp

000012d8 e1a00005   1690 	mov	r0,r5

000012dc e3a01e40   1691 	mov	r1,1<<10

000012e0 eb000000*  1692 	bl	snprintf

000012e4 e1a01005   1693 	mov	r1,r5

000012e8 e1a02000   1694 	mov	r2,r0

000012ec e1a00007   1695 	mov	r0,r7

000012f0 eb000000*  1696 	bl	OscWriteBuffer_write

000012f4 e3500000   1697 	cmp	r0,0


                                                                      Page 29
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s
000012f8 0a000092   1698 	beq	.L1163

000012fc e2861007   1699 	add	r1,r6,7

00001300 e1a00009   1700 	mov	r0,r9

00001304 eb000000*  1701 	bl	OscReadFileContext_getFreqCfg

00001308 e1b04000   1702 	movs	r4,r0

0000130c 0a00008d   1703 	beq	.L1163

00001310 e5940000   1704 	ldr	r0,[r4]

00001314 eb000000*  1705 	bl	__ftod

00001318 e5942004   1706 	ldr	r2,[r4,4]

0000131c e24dd004   1707 	sub	sp,sp,4

00001320 e88d0007   1708 	stmea	[sp],{r0-r2}

00001324 e8bd0008   1709 	ldmfd	[sp]!,{r3}

00001328 e1a0200b   1710 	mov	r2,fp

0000132c e1a00005   1711 	mov	r0,r5

00001330 e3a01e40   1712 	mov	r1,1<<10

00001334 eb000000*  1713 	bl	snprintf

00001338 e1a01005   1714 	mov	r1,r5

0000133c e1a02000   1715 	mov	r2,r0

00001340 e1a00007   1716 	mov	r0,r7

00001344 eb000000*  1717 	bl	OscWriteBuffer_write

00001348 e3500000   1718 	cmp	r0,0

0000134c 0a00007d   1719 	beq	.L1163

00001350 e2866008   1720 	add	r6,r6,8

00001354 e2588001   1721 	subs	r8,r8,1

00001358 1affff53   1722 	bne	.L1391

                    1723 .L1390:

0000135c e59d0060   1724 	ldr	r0,[sp,96]

00001360 e2108007   1725 	ands	r8,r0,7

00001364 0a000017   1726 	beq	.L1146

                    1727 .L1425:

00001368 e1a01006   1728 	mov	r1,r6

0000136c e1a00009   1729 	mov	r0,r9

00001370 eb000000*  1730 	bl	OscReadFileContext_getFreqCfg

00001374 e1b04000   1731 	movs	r4,r0

00001378 0a000072   1732 	beq	.L1163

0000137c e5940000   1733 	ldr	r0,[r4]

00001380 eb000000*  1734 	bl	__ftod

00001384 e5942004   1735 	ldr	r2,[r4,4]

00001388 e24dd004   1736 	sub	sp,sp,4

0000138c e88d0007   1737 	stmea	[sp],{r0-r2}

00001390 e8bd0008   1738 	ldmfd	[sp]!,{r3}

00001394 e59f2104*  1739 	ldr	r2,.L2457

00001398 e1a00005   1740 	mov	r0,r5

0000139c e3a01e40   1741 	mov	r1,1<<10

000013a0 eb000000*  1742 	bl	snprintf

000013a4 e1a01005   1743 	mov	r1,r5

000013a8 e1a02000   1744 	mov	r2,r0

000013ac e1a00007   1745 	mov	r0,r7

000013b0 eb000000*  1746 	bl	OscWriteBuffer_write

000013b4 e3500000   1747 	cmp	r0,0

000013b8 0a000062   1748 	beq	.L1163

000013bc e2866001   1749 	add	r6,r6,1

000013c0 e2588001   1750 	subs	r8,r8,1

000013c4 1affffe7   1751 	bne	.L1425

                    1752 .L1146:

                    1753 ;284: 	}


                    1754 ;285: 	return true;


                    1755 

                    1756 ;333: 	{


                    1757 

                    1758 ;334: 		return false;



                                                                      Page 30
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s
                    1759 

                    1760 ;335: 	}


                    1761 ;336: 	// время начала предыстории и время осцилограммы


                    1762 ;337: 	if (!writeOscTimeToCfg(readFileContext))


                    1763 

                    1764 ;170: {


                    1765 

                    1766 ;171: 	OSCInfoStruct *oscInfo = readFileContext->oscInfo;


                    1767 

000013c8 e599402c   1768 	ldr	r4,[r9,44]

                    1769 ;172: 	


                    1770 ;173: 	__time32_t oscTime = OSCInfo_getUTCDate(oscInfo);


                    1771 

000013cc e1a00004   1772 	mov	r0,r4

000013d0 eb000000*  1773 	bl	OSCInfo_getUTCDate

000013d4 e58d0060   1774 	str	r0,[sp,96]

000013d8 e1a08000   1775 	mov	r8,r0

                    1776 ;174: 	int oscTimeMS = OSCInfo_getDateMS(oscInfo);


                    1777 

000013dc e1a00004   1778 	mov	r0,r4

000013e0 eb000000*  1779 	bl	OSCInfo_getDateMS

000013e4 e599402c   1780 	ldr	r4,[r9,44]

                    1781 ;112: 	__time32_t oscTime = OSCInfo_getUTCDate(oscInfo);


                    1782 

000013e8 e1a0b000   1783 	mov	fp,r0

                    1784 ;175: 	int phistTimeMS;


                    1785 ;176: 	__time32_t phistTime;


                    1786 ;177: 


                    1787 ;178: 	getPhistTime(readFileContext, &phistTime, &phistTimeMS);


                    1788 

                    1789 ;109: 	__time32_t *t, int *ms)


                    1790 ;110: {


                    1791 

                    1792 ;111: 	OSCInfoStruct *oscInfo = readFileContext->oscInfo;


                    1793 

000013ec e1a00004   1794 	mov	r0,r4

000013f0 eb000000*  1795 	bl	OSCInfo_getUTCDate

000013f4 e1a05000   1796 	mov	r5,r0

                    1797 ;113: 	int oscTimeMS = OSCInfo_getDateMS(oscInfo);


                    1798 

000013f8 e1a00004   1799 	mov	r0,r4

000013fc eb000000*  1800 	bl	OSCInfo_getDateMS

                    1801 ;114: 	int64_t tmp;


                    1802 ;115: 


                    1803 ;116: 	// время осцилограммы в миллисекундах


                    1804 ;117: 	tmp = oscTime;


                    1805 

00001400 e3a0affa   1806 	mov	r10,0x03e8

00001404 e08c359a   1807 	umull	r3,r12,r10,r5

00001408 e1a02fc5   1808 	mov	r2,r5 asr 31

                    1809 ;118: 	tmp *= 1000;


                    1810 

0000140c e0424102   1811 	sub	r4,r2,r2 lsl 2

00001410 e0844382   1812 	add	r4,r4,r2 lsl 7

00001414 e08c3184   1813 	add	r3,r12,r4 lsl 3

00001418 e0451105   1814 	sub	r1,r5,r5 lsl 2

0000141c e0811385   1815 	add	r1,r1,r5 lsl 7

                    1816 ;119: 	tmp += oscTimeMS;


                    1817 

00001420 e0904181   1818 	adds	r4,r0,r1 lsl 3

00001424 e0a35fc0   1819 	adc	r5,r3,r0 asr 31


                                                                      Page 31
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s
                    1820 ;120: 


                    1821 ;121: 	// время предыстории в миллисекундах


                    1822 ;122: 	tmp -= OscReadFileContext_getPhistotyTimeMS(readFileContext);


                    1823 

00001428 e1a00009   1824 	mov	r0,r9

0000142c eb000000*  1825 	bl	OscReadFileContext_getPhistotyTimeMS

00001430 e0546000   1826 	subs	r6,r4,r0

00001434 e0c57fc0   1827 	sbc	r7,r5,r0 asr 31

                    1828 ;123: 	// в секундах


                    1829 ;124: 	*t = (__time32_t)(tmp / 1000);


                    1830 

00001438 e1a00006   1831 	mov	r0,r6

0000143c e1a01007   1832 	mov	r1,r7

00001440 e1a0200a   1833 	mov	r2,r10

00001444 e3a03000   1834 	mov	r3,0

00001448 eb000000*  1835 	bl	__gh_div64

0000144c e1a04000   1836 	mov	r4,r0

                    1837 ;125: 	*ms = tmp % 1000;


                    1838 

00001450 e1a00006   1839 	mov	r0,r6

00001454 e1a01007   1840 	mov	r1,r7

00001458 e1a0200a   1841 	mov	r2,r10

0000145c e3a03000   1842 	mov	r3,0

00001460 eb000000*  1843 	bl	__gh_rem64

                    1844 ;179: 	if (!writeFormatTimeToCfgBuffer(readFileContext, phistTime, phistTimeMS))


                    1845 

00001464 e1a01004   1846 	mov	r1,r4

00001468 e1a02000   1847 	mov	r2,r0

0000146c e1a00009   1848 	mov	r0,r9

00001470 ebfffc8b*  1849 	bl	writeFormatTimeToCfgBuffer

00001474 e3500000   1850 	cmp	r0,0

00001478 0a000032   1851 	beq	.L1163

                    1852 ;180: 	{


                    1853 

                    1854 ;181: 		return false;


                    1855 

                    1856 ;182: 	}


                    1857 ;183: 	if (!writeFormatTimeToCfgBuffer(readFileContext, oscTime, oscTimeMS))


                    1858 

0000147c e1a0200b   1859 	mov	r2,fp

00001480 e1a01008   1860 	mov	r1,r8

00001484 e1a00009   1861 	mov	r0,r9

00001488 ebfffc85*  1862 	bl	writeFormatTimeToCfgBuffer

0000148c e3500000   1863 	cmp	r0,0

00001490 0a00002c   1864 	beq	.L1163

                    1865 ;184: 	{


                    1866 

                    1867 ;185: 		return false;


                    1868 

                    1869 ;186: 	}


                    1870 ;187: 


                    1871 ;188: 


                    1872 ;189: 	return true;


                    1873 

                    1874 ;338: 	{


                    1875 

                    1876 ;339: 		return false;


                    1877 

                    1878 ;340: 	}


                    1879 ;341: 


                    1880 ;342: 	// ASCII согласно формату



                                                                      Page 32
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s
                    1881 ;343: 	size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, "ASCII\r\n");


                    1882 

00001494 ea000002   1883 	b	.L2458

                    1884 	.align	4

                    1885 .L2455:

00001498 00000000*  1886 	.data.w	.L2255

                    1887 	.type	.L2455,$object

                    1888 	.size	.L2455,4

                    1889 

                    1890 .L2456:

0000149c 00000000*  1891 	.data.w	.L2253

                    1892 	.type	.L2456,$object

                    1893 	.size	.L2456,4

                    1894 

                    1895 .L2457:

000014a0 00000000*  1896 	.data.w	.L2251

                    1897 	.type	.L2457,$object

                    1898 	.size	.L2457,4

                    1899 

                    1900 .L2458:

                    1901 

000014a4 e28f2000*  1902 	adr	r2,.L2459

000014a8 e59d0058   1903 	ldr	r0,[sp,88]

000014ac e3a01e40   1904 	mov	r1,1<<10

000014b0 eb000000*  1905 	bl	snprintf

                    1906 ;344: 	if (!OscWriteBuffer_write(cfgBuffer, formatBuffer, size)) return false;


                    1907 

000014b4 e1a02000   1908 	mov	r2,r0

000014b8 e59d005c   1909 	ldr	r0,[sp,92]

000014bc e59d1058   1910 	ldr	r1,[sp,88]

000014c0 eb000000*  1911 	bl	OscWriteBuffer_write

000014c4 e3500000   1912 	cmp	r0,0

000014c8 0a00001e   1913 	beq	.L1163

                    1914 ;345: 


                    1915 ;346: 	// Следующие значения вписываются напрямую


                    1916 ;347: 	// в дальнейшем следует переделать для большего соответствия формату


                    1917 ;348: 


                    1918 ;349: 	// коэффициент умножения временной метки


                    1919 ;350: 	size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, "1\r\n");


                    1920 

000014cc e28f2000*  1921 	adr	r2,.L2460

000014d0 e59d0058   1922 	ldr	r0,[sp,88]

000014d4 e3a01e40   1923 	mov	r1,1<<10

000014d8 eb000000*  1924 	bl	snprintf

                    1925 ;351: 	if (!OscWriteBuffer_write(cfgBuffer, formatBuffer, size)) return false;


                    1926 

000014dc e1a02000   1927 	mov	r2,r0

000014e0 e59d005c   1928 	ldr	r0,[sp,92]

000014e4 e59d1058   1929 	ldr	r1,[sp,88]

000014e8 eb000000*  1930 	bl	OscWriteBuffer_write

000014ec e3500000   1931 	cmp	r0,0

000014f0 0a000014   1932 	beq	.L1163

                    1933 ;352: 


                    1934 ;353: 	// Разница между локальным временем и UTC


                    1935 ;354: 	size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, "0,0\r\n");


                    1936 

000014f4 e59f40f0*  1937 	ldr	r4,.L2461

000014f8 e59d0058   1938 	ldr	r0,[sp,88]

000014fc e1a02004   1939 	mov	r2,r4

00001500 e3a01e40   1940 	mov	r1,1<<10

00001504 eb000000*  1941 	bl	snprintf


                                                                      Page 33
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s
                    1942 ;355: 	if (!OscWriteBuffer_write(cfgBuffer, formatBuffer, size)) return false;


                    1943 

00001508 e1a02000   1944 	mov	r2,r0

0000150c e59d1058   1945 	ldr	r1,[sp,88]

00001510 e59d005c   1946 	ldr	r0,[sp,92]

00001514 eb000000*  1947 	bl	OscWriteBuffer_write

00001518 e3500000   1948 	cmp	r0,0

0000151c 0a000009   1949 	beq	.L1163

                    1950 ;356: 	


                    1951 ;357: 	// Качество времени


                    1952 ;358: 	size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, "0,0\r\n");


                    1953 

00001520 e1a02004   1954 	mov	r2,r4

00001524 e59d0058   1955 	ldr	r0,[sp,88]

00001528 e3a01e40   1956 	mov	r1,1<<10

0000152c eb000000*  1957 	bl	snprintf

                    1958 ;359: 	if (!OscWriteBuffer_write(cfgBuffer, formatBuffer, size)) return false;


                    1959 

00001530 e1a02000   1960 	mov	r2,r0

00001534 e59d005c   1961 	ldr	r0,[sp,92]

00001538 e59d1058   1962 	ldr	r1,[sp,88]

0000153c eb000000*  1963 	bl	OscWriteBuffer_write

00001540 e3500000   1964 	cmp	r0,0

                    1965 ;360: 


                    1966 ;361: 	return true;


                    1967 

00001544 13a00001   1968 	movne	r0,1

                    1969 .L1163:

00001548 03a00000   1970 	moveq	r0,0

                    1971 .L1089:

0000154c e28dd078   1972 	add	sp,sp,120

00001550 e8bd8ff0   1973 	ldmfd	[sp]!,{r4-fp,pc}

                    1974 	.endf	OscConverter_processCfg

                    1975 	.align	4

                    1976 ;formatBuffer	[sp,88]	local

                    1977 ;cfgBuffer	[sp,92]	local

                    1978 ;oscInfo	r5	local

                    1979 ;analogCount	r4	local

                    1980 ;boolCount	r5	local

                    1981 ;.L2246	.L2261	static

                    1982 ;.L2247	.L2262	static

                    1983 ;oscInfo	r5	local

                    1984 ;cfgBuffer	[sp,80]	local

                    1985 ;formatBuffer	fp	local

                    1986 ;analogNum	r4	local

                    1987 ;pAnalog	r6	local

                    1988 ;max	[sp,64]	local

                    1989 ;min	[sp,68]	local

                    1990 ;oscInfo	r6	local

                    1991 ;cfgBuffer	fp	local

                    1992 ;formatBuffer	r5	local

                    1993 ;boolNum	r4	local

                    1994 ;cfgBuffer	r7	local

                    1995 ;formatBuffer	r5	local

                    1996 ;freqCount	r4	local

                    1997 ;freqNum	r6	local

                    1998 ;freqCfg	r4	local

                    1999 ;oscInfo	r4	local

                    2000 ;oscTime	[sp,96]	local

                    2001 ;oscTimeMS	fp	local

                    2002 ;phistTime	r4	local


                                                                      Page 34
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s
                    2003 ;oscInfo	r4	local

                    2004 ;oscTime	r5	local

                    2005 ;tmp	r1	local

                    2006 ;.L2248	.L2258	static

                    2007 ;.L2249	.L2259	static

                    2008 ;.L2250	.L2260	static

                    2009 ;cft	[sp,112]	local

                    2010 

                    2011 ;readFileContext	r9	param

                    2012 

                    2013 	.section ".bss","awb"

                    2014 .L2245:

                    2015 	.section ".rodata","a"

                    2016 .L2251:

                    2017 __UNNAMED_2_static_in_writeFrequencyToCfg:;	"%f,%d\r\n\000"

00000004 252c6625   2018 	.data.b	37,102,44,37

00000008 000a0d64   2019 	.data.b	100,13,10,0

                    2020 	.type	__UNNAMED_2_static_in_writeFrequencyToCfg,$object

                    2021 	.size	__UNNAMED_2_static_in_writeFrequencyToCfg,8

                    2022 .L2252:

                    2023 __UNNAMED_2_static_in_writeAnalogToCfg:;	"\000"

0000000c 00        2024 	.data.b	0

                    2025 	.type	__UNNAMED_2_static_in_writeAnalogToCfg,$object

                    2026 	.size	__UNNAMED_2_static_in_writeAnalogToCfg,1

0000000d 000000    2027 	.space	3

                    2028 .L2253:

                    2029 __UNNAMED_1_static_in_writeFrequencyToCfg:;	"%d\r\n\000"

00000010 0a0d6425   2030 	.data.b	37,100,13,10

00000014 00        2031 	.data.b	0

00000015 000000    2032 	.space	3

                    2033 	.type	__UNNAMED_1_static_in_writeFrequencyToCfg,$object

                    2034 	.size	__UNNAMED_1_static_in_writeFrequencyToCfg,8

                    2035 .L2254:

                    2036 __UNNAMED_1_static_in_writeAnalogToCfg:;	"%d,%s,%s,%s,%s,%e,%e,%d,%d,%d,%e,%e,%c\r\n\000"

00000018 252c6425   2037 	.data.b	37,100,44,37

0000001c 73252c73   2038 	.data.b	115,44,37,115

00000020 2c73252c   2039 	.data.b	44,37,115,44

00000024 252c7325   2040 	.data.b	37,115,44,37

00000028 65252c65   2041 	.data.b	101,44,37,101

0000002c 2c64252c   2042 	.data.b	44,37,100,44

00000030 252c6425   2043 	.data.b	37,100,44,37

00000034 65252c64   2044 	.data.b	100,44,37,101

00000038 2c65252c   2045 	.data.b	44,37,101,44

0000003c 0a0d6325   2046 	.data.b	37,99,13,10

00000040 00        2047 	.data.b	0

00000041 000000    2048 	.space	3

                    2049 	.type	__UNNAMED_1_static_in_writeAnalogToCfg,$object

                    2050 	.size	__UNNAMED_1_static_in_writeAnalogToCfg,44

                    2051 .L2255:

                    2052 __UNNAMED_1_static_in_writeBoolToCfg:;	"%d,%s,%d\r\n\000"

00000044 252c6425   2053 	.data.b	37,100,44,37

00000048 64252c73   2054 	.data.b	115,44,37,100

0000004c 0a0d      2055 	.data.b	13,10

0000004e 00        2056 	.data.b	0

0000004f 00        2057 	.space	1

                    2058 	.type	__UNNAMED_1_static_in_writeBoolToCfg,$object

                    2059 	.size	__UNNAMED_1_static_in_writeBoolToCfg,12

                    2060 .L2260:;	"0,0\r\n\000"

00000050 0d302c30   2061 	.data.b	48,44,48,13

00000054 000a      2062 	.data.b	10,0

00000056 0000      2063 	.space	2


                                                                      Page 35
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s
                    2064 	.type	.L2260,$object

                    2065 	.size	.L2260,8

                    2066 	.data

                    2067 	.text

                    2068 

                    2069 ;362: }


                    2070 

                    2071 ;363: 


                    2072 ;364: bool OscConverter_processHdr(OscReadFileContext * readFileContext)


                    2073 	.align	4

                    2074 	.align	4

                    2075 OscConverter_processHdr::

00001554 e92d40f0   2076 	stmfd	[sp]!,{r4-r7,lr}

                    2077 ;365: {


                    2078 

                    2079 ;366: 	// сюдя можно что-нибудь дописать


                    2080 ;367: 	char *comment = "";


                    2081 

                    2082 ;368: 	char *text = "";


                    2083 

                    2084 ;369: 


                    2085 ;370: 	char *formatBuffer = readFileContext->formatBuffer;


                    2086 

00001558 e2804050   2087 	add	r4,r0,80

                    2088 ;371: 	OscWriteBuffer *hdrBuffer = &readFileContext->hdrBuffer;


                    2089 

0000155c e2801e40   2090 	add	r1,r0,1<<10

00001560 e590002c   2091 	ldr	r0,[r0,44]

                    2092 ;375: 	if (!oscInfo)


                    2093 

00001564 e2815078   2094 	add	r5,r1,120

                    2095 ;372: 	OSCInfoStruct *oscInfo;


                    2096 ;373: 	int size;


                    2097 ;374: 	oscInfo = readFileContext->oscInfo;


                    2098 

00001568 e3500000   2099 	cmp	r0,0

0000156c 0a000017   2100 	beq	.L2471

                    2101 ;376: 	{


                    2102 

                    2103 ;377: 		return false;


                    2104 

                    2105 ;378: 	}


                    2106 ;379: 


                    2107 ;380: 	size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, "%s\r\n",text);


                    2108 

00001570 e59f6078*  2109 	ldr	r6,.L2558

00001574 e59f7078*  2110 	ldr	r7,.L2559

00001578 e1a03006   2111 	mov	r3,r6

0000157c e1a02007   2112 	mov	r2,r7

00001580 e1a00004   2113 	mov	r0,r4

00001584 e3a01e40   2114 	mov	r1,1<<10

00001588 eb000000*  2115 	bl	snprintf

                    2116 ;381: 	if (!OscWriteBuffer_write(hdrBuffer, formatBuffer, size)) return false;


                    2117 

0000158c e1a01004   2118 	mov	r1,r4

00001590 e1a02000   2119 	mov	r2,r0

00001594 e1a00005   2120 	mov	r0,r5

00001598 eb000000*  2121 	bl	OscWriteBuffer_write

0000159c e3500000   2122 	cmp	r0,0

000015a0 0a00000a   2123 	beq	.L2471

                    2124 ;382: 



                                                                      Page 36
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s
                    2125 ;383: 	size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, "%s\r\n",comment);


                    2126 

000015a4 e1a03006   2127 	mov	r3,r6

000015a8 e1a02007   2128 	mov	r2,r7

000015ac e1a00004   2129 	mov	r0,r4

000015b0 e3a01e40   2130 	mov	r1,1<<10

000015b4 eb000000*  2131 	bl	snprintf

                    2132 ;384: 	if (!OscWriteBuffer_write(hdrBuffer, formatBuffer, size)) return false;


                    2133 

000015b8 e1a01004   2134 	mov	r1,r4

000015bc e1a02000   2135 	mov	r2,r0

000015c0 e1a00005   2136 	mov	r0,r5

000015c4 eb000000*  2137 	bl	OscWriteBuffer_write

000015c8 e3500000   2138 	cmp	r0,0

                    2139 ;385: 


                    2140 ;386: 	return true;


                    2141 

000015cc 13a00001   2142 	movne	r0,1

                    2143 .L2471:

000015d0 03a00000   2144 	moveq	r0,0

                    2145 .L2462:

000015d4 e8bd80f0   2146 	ldmfd	[sp]!,{r4-r7,pc}

                    2147 	.endf	OscConverter_processHdr

                    2148 	.align	4

                    2149 ;.L2535	.L2539	static

                    2150 ;formatBuffer	r4	local

                    2151 ;hdrBuffer	r5	local

                    2152 ;oscInfo	r0	local

                    2153 ;.L2536	.L2540	static

                    2154 

                    2155 ;readFileContext	r0	param

                    2156 

                    2157 	.section ".bss","awb"

                    2158 .L2534:

                    2159 	.section ".rodata","a"

                    2160 .L2539:;	"\000"

00000058 00        2161 	.data.b	0

                    2162 	.type	.L2539,$object

                    2163 	.size	.L2539,1

00000059 000000    2164 	.space	3

                    2165 .L2540:;	"%s\r\n\000"

0000005c 0a0d7325   2166 	.data.b	37,115,13,10

00000060 00        2167 	.data.b	0

00000061 000000    2168 	.space	3

                    2169 	.type	.L2540,$object

                    2170 	.size	.L2540,8

                    2171 	.data

                    2172 	.text

                    2173 

                    2174 ;387: }


                    2175 	.align	4

                    2176 	.align	4

                    2177 OscConverter_init::

                    2178 ;10: {	


                    2179 

                    2180 ;11: 	return true;


                    2181 

000015d8 e3a00001   2182 	mov	r0,1

000015dc e12fff1e*  2183 	ret	

                    2184 	.endf	OscConverter_init

                    2185 	.align	4


                                                                      Page 37
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s
                    2186 

                    2187 	.section ".bss","awb"

                    2188 .L2574:

                    2189 	.data

                    2190 	.text

                    2191 	.align	4

                    2192 .L2459:

                    2193 ;	"ASCII\r\n\000"

000015e0 49435341   2194 	.data.b	65,83,67,73

000015e4 000a0d49   2195 	.data.b	73,13,10,0

                    2196 	.align 4

                    2197 

                    2198 	.type	.L2459,$object

                    2199 	.size	.L2459,4

                    2200 

                    2201 .L2460:

                    2202 ;	"1\r\n\000"

000015e8 000a0d31   2203 	.data.b	49,13,10,0

                    2204 	.align 4

                    2205 

                    2206 	.type	.L2460,$object

                    2207 	.size	.L2460,4

                    2208 

                    2209 .L2461:

000015ec 00000000*  2210 	.data.w	.L2260

                    2211 	.type	.L2461,$object

                    2212 	.size	.L2461,4

                    2213 

                    2214 .L2558:

000015f0 00000000*  2215 	.data.w	.L2539

                    2216 	.type	.L2558,$object

                    2217 	.size	.L2558,4

                    2218 

                    2219 .L2559:

000015f4 00000000*  2220 	.data.w	.L2540

                    2221 	.type	.L2559,$object

                    2222 	.size	.L2559,4

                    2223 

                    2224 	.align	4

                    2225 ;__UNNAMED_1_static_in_writeAnalogToCfg	.L2254	static

                    2226 ;__UNNAMED_2_static_in_writeAnalogToCfg	.L2252	static

                    2227 ;__UNNAMED_1_static_in_writeBoolToCfg	.L2255	static

                    2228 ;__UNNAMED_1_static_in_writeFrequencyToCfg	.L2253	static

                    2229 ;__UNNAMED_2_static_in_writeFrequencyToCfg	.L2251	static

                    2230 

                    2231 	.data

                    2232 	.need	__gh_long_long_printf

                    2233 	.need	__gh_float_printf

                    2234 	.ghsnote version,6

                    2235 	.ghsnote tools,3

                    2236 	.ghsnote options,0

                    2237 	.text

                    2238 	.align	4

                    2239 	.section ".rodata","a"

                    2240 	.align	4

                    2241 	.text

