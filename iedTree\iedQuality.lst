                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_37c1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=iedQuality.c -o iedTree\gh_37c1.o -list=iedTree/iedQuality.lst C:\Users\<USER>\AppData\Local\Temp\gh_37c1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_37c1.s
Source File: iedQuality.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      10 ;		iedTree/iedQuality.c -o iedTree/iedQuality.o

                      11 ;Source File:   iedTree/iedQuality.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Oct 07 09:39:20 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "iedQuality.h"


                      21 ;2: 


                      22 ;3: 


                      23 ;4: #include "debug.h"


                      24 ;5: #include "../DataSlice.h"


                      25 ;6: #include "iedFinalDA.h"


                      26 ;7: #include "iedTree.h"


                      27 ;8: #include "../AsnEncoding.h"


                      28 ;9: #include "../BusError.h"


                      29 ;10: 


                      30 ;11: #define QUALITY_ENCODED_SIZE 5


                      31 ;12: 


                      32 ;13: 


                      33 ;14: // Получает Quality из текущего "захваченного" DataSlice.


                      34 ;15: // Поддерживаются только два самых важных бита.


                      35 ;16: // Возвращаемое значение подготовлено к отправке в виде


                      36 ;17: // двухбайтного буфера в формате BER bit string: little endian и LSB->MSB.


                      37 ;18: // То есть bit 0 Quality это bit 7 результата функции.


                      38 ;19: // А bit 1 Quality это bit 6 результата


                      39 ;20: static uint16_t getQualityFastCurrDS(QualityAccsessInfo* accessInfo)


                      40 

                      41 ;46: }


                      42 

                      43 ;47: 


                      44 ;48: static void updateFromDataSlice(IEDEntity entity)


                      45 	.text

                      46 	.align	4

                      47 updateFromDataSlice:

00000000 e92d4070     48 	stmfd	[sp]!,{r4-r6,lr}

00000004 e1a05000     49 	mov	r5,r0

                      50 ;49: {



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_37c1.s
                      51 

                      52 ;50: 	TerminalItem* termItem = entity->extInfo;


                      53 

00000008 e5956058     54 	ldr	r6,[r5,88]

                      55 ;51: 	uint16_t value;


                      56 ;52: 


                      57 ;53: 	if(!BusError_check())


                      58 

0000000c eb000000*    59 	bl	BusError_check

00000010 e3500000     60 	cmp	r0,0

00000014 1a000004     61 	bne	.L34

                      62 ;54: 	{


                      63 

                      64 ;55: 		//Ставим bit 0


                      65 ;56: 		// Из за хитрого формата quality, который здесь используется


                      66 ;57: 		// (смотри функцию getQualityFastCurrDS), bit 0 это bit 7


                      67 ;58: 		value = 0x40;


                      68 

00000018 e1d503b0     69 	ldrh	r0,[r5,48]

0000001c e3a04040     70 	mov	r4,64

                      71 ;63: 	}


                      72 ;64: 


                      73 ;65: 	if(entity->qualityValue == value)


                      74 

00000020 e3500040     75 	cmp	r0,64

00000024 1a000017     76 	bne	.L42

00000028 ea000013     77 	b	.L43

                      78 .L34:

                      79 ;59: 	}


                      80 ;60: 	else


                      81 ;61: 	{


                      82 

                      83 ;62: 		 value = getQualityFastCurrDS(&termItem->q.accessInfo);


                      84 

                      85 ;21: {


                      86 

0000002c e5960008     87 	ldr	r0,[r6,8]

                      88 ;32: 	if(offset != -1 && DataSlice_getBoolFastCurrDS(offset))


                      89 

00000030 e3a04000     90 	mov	r4,0

                      91 ;22: 	int offset;


                      92 ;23: 	uint16_t quality = 0;


                      93 

                      94 ;24: 


                      95 ;25: 	// Функция поддерживает только 2 бита Q потому что


                      96 ;26: 	// остальные не нужны(пока?) и	 только добавят тормозов


                      97 ;27: 


                      98 ;28: 	


                      99 ;29: 	// Первый бит слева при отображении в IEDScout


                     100 ;30: 	// x000000000000	


                     101 ;31: 	offset = accessInfo->goodInvalidOffset;


                     102 

00000034 e3700001    103 	cmn	r0,1

00000038 0a000004    104 	beq	.L37

0000003c e1a00800    105 	mov	r0,r0 lsl 16

00000040 e1a00820    106 	mov	r0,r0 lsr 16

00000044 eb000000*   107 	bl	DataSlice_getBoolFastCurrDS

00000048 e3500000    108 	cmp	r0,0

                     109 ;33: 	{		


                     110 

                     111 ;34: 		quality |= (1 << 7);



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_37c1.s
                     112 

0000004c 13a04080    113 	movne	r4,128

                     114 .L37:

                     115 ;35: 	}


                     116 ;36: 


                     117 ;37: 	// Второй бит слева при отображении в IEDScout


                     118 ;38: 	// 0x00000000000


                     119 ;39: 	offset = accessInfo->reservedQuestionableOffset;


                     120 

00000050 e596000c    121 	ldr	r0,[r6,12]

                     122 ;40: 	if(offset != -1 && DataSlice_getBoolFastCurrDS(offset))


                     123 

00000054 e3700001    124 	cmn	r0,1

00000058 0a000004    125 	beq	.L32

0000005c e1a00800    126 	mov	r0,r0 lsl 16

00000060 e1a00820    127 	mov	r0,r0 lsr 16

00000064 eb000000*   128 	bl	DataSlice_getBoolFastCurrDS

00000068 e3500000    129 	cmp	r0,0

                     130 ;41: 	{


                     131 

                     132 ;42: 		quality |= (1 << 6);


                     133 

0000006c 13844040    134 	orrne	r4,r4,64

                     135 .L32:

                     136 ;43: 	}


                     137 ;44: 


                     138 ;45: 	return quality;


                     139 

                     140 ;63: 	}


                     141 ;64: 


                     142 ;65: 	if(entity->qualityValue == value)


                     143 

00000070 e1d503b0    144 	ldrh	r0,[r5,48]

00000074 e1500004    145 	cmp	r0,r4

00000078 1a000002    146 	bne	.L42

                     147 .L43:

                     148 ;66: 	{


                     149 

                     150 ;67: 		entity->changed = TRGOP_NONE;


                     151 

0000007c e3a00000    152 	mov	r0,0

00000080 e5850028    153 	str	r0,[r5,40]

00000084 ea000007    154 	b	.L27

                     155 .L42:

                     156 ;68: 	}


                     157 ;69: 	else


                     158 ;70: 	{


                     159 

                     160 ;71: 		entity->changed = entity->trgOps;


                     161 

00000088 e5950024    162 	ldr	r0,[r5,36]

0000008c e1c543b0    163 	strh	r4,[r5,48]

                     164 ;73: 		IEDEntity_setTimeStamp(entity, dataSliceGetTimeStamp());


                     165 

00000090 e5850028    166 	str	r0,[r5,40]

                     167 ;72: 		entity->qualityValue = value;


                     168 

00000094 eb000000*   169 	bl	dataSliceGetTimeStamp

00000098 e1a02001    170 	mov	r2,r1

0000009c e1a01000    171 	mov	r1,r0

000000a0 e1a00005    172 	mov	r0,r5


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_37c1.s
000000a4 eb000000*   173 	bl	IEDEntity_setTimeStamp

                     174 .L27:

000000a8 e8bd4070    175 	ldmfd	[sp]!,{r4-r6,lr}

000000ac e12fff1e*   176 	ret	

                     177 	.endf	updateFromDataSlice

                     178 	.align	4

                     179 ;termItem	r6	local

                     180 ;value	r4	local

                     181 ;offset	r0	local

                     182 ;quality	r4	local

                     183 

                     184 ;entity	r5	param

                     185 

                     186 	.section ".bss","awb"

                     187 .L142:

                     188 	.data

                     189 	.text

                     190 

                     191 ;74: 	}


                     192 ;75: }


                     193 

                     194 ;76: 


                     195 ;77: static bool calcReadLen(IEDEntity entity, size_t* pLen )


                     196 	.align	4

                     197 	.align	4

                     198 calcReadLen:

                     199 ;78: {


                     200 

                     201 ;79: 	*pLen = QUALITY_ENCODED_SIZE;


                     202 

000000b0 e3a00005    203 	mov	r0,5

000000b4 e5810000    204 	str	r0,[r1]

                     205 ;80: 	return true;


                     206 

000000b8 e3a00001    207 	mov	r0,1

000000bc e12fff1e*   208 	ret	

                     209 	.endf	calcReadLen

                     210 	.align	4

                     211 

                     212 ;entity	none	param

                     213 ;pLen	r1	param

                     214 

                     215 	.section ".bss","awb"

                     216 .L206:

                     217 	.data

                     218 	.text

                     219 

                     220 ;81: }


                     221 

                     222 ;82: 


                     223 ;83: static bool encodeRead(IEDEntity entity, BufferView* outBuf)


                     224 	.align	4

                     225 	.align	4

                     226 encodeRead:

000000c0 e92d4030    227 	stmfd	[sp]!,{r4-r5,lr}

000000c4 e24dd008    228 	sub	sp,sp,8

000000c8 e28d2004    229 	add	r2,sp,4

000000cc e1a05000    230 	mov	r5,r0

000000d0 e1a04001    231 	mov	r4,r1

000000d4 e1a00004    232 	mov	r0,r4

000000d8 e3a01005    233 	mov	r1,5


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_37c1.s
000000dc eb000000*   234 	bl	BufferView_alloc

                     235 ;84: {


                     236 

                     237 ;85: 	uint8_t* encodeBuf;


                     238 ;86: 


                     239 ;87: 	if(!BufferView_alloc(outBuf,QUALITY_ENCODED_SIZE, &encodeBuf))


                     240 

000000e0 e3500000    241 	cmp	r0,0

                     242 ;88: 	{


                     243 

                     244 ;89: 		ERROR_REPORT("Unable to allocat buffer");


                     245 ;90: 		return false;


                     246 

000000e4 0a00000a    247 	beq	.L213

                     248 ;91: 	}


                     249 ;92: 


                     250 ;93: 	// Возвращаемое значение не нужно,


                     251 ;94: 	// потому что функция не возвращает ошибки, а размер известен заранее


                     252 ;95: 	BerEncoder_encodeBitStringUshortBuf(ASN_TYPEDESCRIPTION_BIT_STRING,


                     253 

000000e8 e3a00000    254 	mov	r0,0

000000ec e58d0000    255 	str	r0,[sp]

000000f0 e59d3004    256 	ldr	r3,[sp,4]

000000f4 e1d523b0    257 	ldrh	r2,[r5,48]

000000f8 e3a0100d    258 	mov	r1,13

000000fc e3a00084    259 	mov	r0,132

00000100 eb000000*   260 	bl	BerEncoder_encodeBitStringUshortBuf

                     261 ;96: 		13, entity->qualityValue, encodeBuf, 0);


                     262 ;97: 	outBuf->pos += QUALITY_ENCODED_SIZE;


                     263 

00000104 e5941004    264 	ldr	r1,[r4,4]

00000108 e3a00001    265 	mov	r0,1

0000010c e2811005    266 	add	r1,r1,5

00000110 e5841004    267 	str	r1,[r4,4]

                     268 ;98: 	return true;


                     269 

                     270 .L213:

00000114 e28dd008    271 	add	sp,sp,8

00000118 e8bd4030    272 	ldmfd	[sp]!,{r4-r5,lr}

0000011c e12fff1e*   273 	ret	

                     274 	.endf	encodeRead

                     275 	.align	4

                     276 ;encodeBuf	[sp,4]	local

                     277 

                     278 ;entity	r5	param

                     279 ;outBuf	r4	param

                     280 

                     281 	.section ".bss","awb"

                     282 .L266:

                     283 	.data

                     284 	.text

                     285 

                     286 ;99: }


                     287 

                     288 ;100: 


                     289 ;101: void IEDQuality_init(IEDEntity entity)


                     290 	.align	4

                     291 	.align	4

                     292 IEDQuality_init::

00000120 e92d44f0    293 	stmfd	[sp]!,{r4-r7,r10,lr}

00000124 e59f60e4*   294 	ldr	r6,.L312


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_37c1.s
00000128 e280505c    295 	add	r5,r0,92

0000012c e5154004    296 	ldr	r4,[r5,-4]

                     297 ;112: 	accessInfo = extInfo->accessInfo;


                     298 

00000130 e59f70dc*   299 	ldr	r7,.L313

                     300 ;102: {


                     301 

                     302 ;103: 	TerminalItem* extInfo;


                     303 ;104: 	// accessInfo в элементе дерева, настраивается на смещения в DataSlice


                     304 ;105: 	QualityAccsessInfo* qAccessInfo;


                     305 ;106: 	//accessInfo из бинарника модели


                     306 ;107: 	QualityAccsessInfo* accessInfo;


                     307 ;108: 


                     308 ;109: 


                     309 ;110: 


                     310 ;111: 	extInfo = entity->extInfo;


                     311 

00000134 e594a000    312 	ldr	r10,[r4]

                     313 ;113: 	qAccessInfo = &extInfo->q.accessInfo;


                     314 

00000138 e284c004    315 	add	r12,r4,4

                     316 ;114: 	*qAccessInfo = *accessInfo;


                     317 

0000013c e8ba000f    318 	ldmfd	[r10]!,{r0-r3}

00000140 e8ac000f    319 	stmea	[r12]!,{r0-r3}

00000144 e8ba000f    320 	ldmfd	[r10]!,{r0-r3}

00000148 e8ac000f    321 	stmea	[r12]!,{r0-r3}

0000014c e8ba000f    322 	ldmfd	[r10]!,{r0-r3}

00000150 e8ac000f    323 	stmea	[r12]!,{r0-r3}

00000154 e89a0007    324 	ldmfd	[r10],{r0-r2}

00000158 e88c0007    325 	stmea	[r12],{r0-r2}

                     326 ;115: 


                     327 ;116: 


                     328 ;117: 


                     329 ;118: 	//Заменяем абсолютные смещения на смещения в DataSlice


                     330 ;119: 	qAccessInfo->goodInvalidOffset =


                     331 

0000015c e5940008    332 	ldr	r0,[r4,8]

00000160 eb000000*   333 	bl	DataSlice_getBoolOffset

00000164 e5840008    334 	str	r0,[r4,8]

                     335 ;120: 			DataSlice_getBoolOffset(qAccessInfo->goodInvalidOffset);


                     336 ;121: 	qAccessInfo->reservedQuestionableOffset =


                     337 

00000168 e594000c    338 	ldr	r0,[r4,12]

0000016c eb000000*   339 	bl	DataSlice_getBoolOffset

00000170 e584000c    340 	str	r0,[r4,12]

                     341 ;122: 			DataSlice_getBoolOffset(qAccessInfo->reservedQuestionableOffset);


                     342 ;123: 	qAccessInfo->overflowOffset =


                     343 

00000174 e5940010    344 	ldr	r0,[r4,16]

00000178 eb000000*   345 	bl	DataSlice_getBoolOffset

0000017c e5840010    346 	str	r0,[r4,16]

                     347 ;124: 			DataSlice_getBoolOffset(qAccessInfo->overflowOffset);


                     348 ;125: 	qAccessInfo->outOfRangeOffset =


                     349 

00000180 e5940014    350 	ldr	r0,[r4,20]

00000184 eb000000*   351 	bl	DataSlice_getBoolOffset

00000188 e5840014    352 	str	r0,[r4,20]

                     353 ;126: 			DataSlice_getBoolOffset(qAccessInfo->outOfRangeOffset);


                     354 ;127: 	qAccessInfo->badReferenceOffset =


                     355 


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_37c1.s
0000018c e5940018    356 	ldr	r0,[r4,24]

00000190 eb000000*   357 	bl	DataSlice_getBoolOffset

00000194 e5840018    358 	str	r0,[r4,24]

                     359 ;128: 			DataSlice_getBoolOffset(qAccessInfo->badReferenceOffset);


                     360 ;129: 	qAccessInfo->oscillatoryOffset =


                     361 

00000198 e594001c    362 	ldr	r0,[r4,28]

0000019c eb000000*   363 	bl	DataSlice_getBoolOffset

000001a0 e584001c    364 	str	r0,[r4,28]

                     365 ;130: 			DataSlice_getBoolOffset(qAccessInfo->oscillatoryOffset);


                     366 ;131: 	qAccessInfo->failureOffset =


                     367 

000001a4 e5940020    368 	ldr	r0,[r4,32]

000001a8 eb000000*   369 	bl	DataSlice_getBoolOffset

000001ac e5840020    370 	str	r0,[r4,32]

                     371 ;132: 			DataSlice_getBoolOffset(qAccessInfo->failureOffset);


                     372 ;133: 	qAccessInfo->oldDataOffset =


                     373 

000001b0 e5940024    374 	ldr	r0,[r4,36]

000001b4 eb000000*   375 	bl	DataSlice_getBoolOffset

000001b8 e5840024    376 	str	r0,[r4,36]

                     377 ;134: 			DataSlice_getBoolOffset(qAccessInfo->oldDataOffset);


                     378 ;135: 	qAccessInfo->inconsistentOffset =


                     379 

000001bc e5940028    380 	ldr	r0,[r4,40]

000001c0 eb000000*   381 	bl	DataSlice_getBoolOffset

000001c4 e5840028    382 	str	r0,[r4,40]

                     383 ;136: 			DataSlice_getBoolOffset(qAccessInfo->inconsistentOffset);


                     384 ;137: 	qAccessInfo->inaccurateOffset =


                     385 

000001c8 e594002c    386 	ldr	r0,[r4,44]

000001cc eb000000*   387 	bl	DataSlice_getBoolOffset

000001d0 e584002c    388 	str	r0,[r4,44]

                     389 ;138: 			DataSlice_getBoolOffset(qAccessInfo->inaccurateOffset);


                     390 ;139: 	qAccessInfo->processSubstitutedOffset =


                     391 

000001d4 e5940030    392 	ldr	r0,[r4,48]

000001d8 eb000000*   393 	bl	DataSlice_getBoolOffset

000001dc e5840030    394 	str	r0,[r4,48]

                     395 ;140: 			DataSlice_getBoolOffset(qAccessInfo->processSubstitutedOffset);


                     396 ;141: 	qAccessInfo->testOffset =


                     397 

000001e0 e5940034    398 	ldr	r0,[r4,52]

000001e4 eb000000*   399 	bl	DataSlice_getBoolOffset

000001e8 e5840034    400 	str	r0,[r4,52]

                     401 ;142: 			DataSlice_getBoolOffset(qAccessInfo->testOffset);


                     402 ;143: 	qAccessInfo->operatorBlockedOffset =


                     403 

000001ec e5940038    404 	ldr	r0,[r4,56]

000001f0 eb000000*   405 	bl	DataSlice_getBoolOffset

000001f4 e5840038    406 	str	r0,[r4,56]

                     407 ;144: 			DataSlice_getBoolOffset(qAccessInfo->operatorBlockedOffset);


                     408 ;145: 


                     409 ;146: 	entity->updateFromDataSlice = updateFromDataSlice;


                     410 

000001f8 e59f0018*   411 	ldr	r0,.L314

000001fc e585600c    412 	str	r6,[r5,12]

                     413 ;147: 	entity->calcReadLen = calcReadLen;


                     414 

                     415 ;148: 	entity->encodeRead = encodeRead;


                     416 


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_37c1.s
00000200 e8850081    417 	stmea	[r5],{r0,r7}

                     418 ;149: 


                     419 ;150: 	IEDTree_addToCmpList(entity);


                     420 

00000204 e245005c    421 	sub	r0,r5,92

00000208 e8bd44f0    422 	ldmfd	[sp]!,{r4-r7,r10,lr}

0000020c ea000000*   423 	b	IEDTree_addToCmpList

                     424 	.endf	IEDQuality_init

                     425 	.align	4

                     426 ;extInfo	r4	local

                     427 

                     428 ;entity	r5	param

                     429 

                     430 	.section ".bss","awb"

                     431 .L305:

                     432 	.data

                     433 	.text

                     434 

                     435 ;151: }


                     436 	.align	4

                     437 .L312:

00000210 00000000*   438 	.data.w	updateFromDataSlice

                     439 	.type	.L312,$object

                     440 	.size	.L312,4

                     441 

                     442 .L313:

00000214 00000000*   443 	.data.w	calcReadLen

                     444 	.type	.L313,$object

                     445 	.size	.L313,4

                     446 

                     447 .L314:

00000218 00000000*   448 	.data.w	encodeRead

                     449 	.type	.L314,$object

                     450 	.size	.L314,4

                     451 

                     452 	.align	4

                     453 

                     454 	.data

                     455 	.ghsnote version,6

                     456 	.ghsnote tools,3

                     457 	.ghsnote options,0

                     458 	.text

                     459 	.align	4

