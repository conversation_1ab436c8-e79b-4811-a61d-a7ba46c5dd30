#pragma once

#include <platform_critical_section.h>

#define REPORT_MEM_SIZE	    65536
#define REPORT_CHUNK_SIZE	128

// переполение (isOverflow)
// очистка (purge)
// есть ли что-нибудь (isEmpty)

typedef struct
{
	unsigned short len;
	unsigned char lastFlag;
	unsigned char reserved;
	unsigned char payload[REPORT_CHUNK_SIZE];
}ReportChunk;

typedef struct 
{
	ReportChunk chunk[REPORT_MEM_SIZE / REPORT_CHUNK_SIZE];
	int head;
	int tail;
	int chunkCount;
	int overflowCount;
	int lastOverflowCount;
	int criticalErrorCount;
	CriticalSection criticalSection;
}ReportQueue;


void ReportQueue_init(ReportQueue *queue);
int	ReportQueue_write(ReportQueue *queue, unsigned char *data, int size);
int ReportQueue_read(ReportQueue *queue, unsigned char *data,int maxDataSize);
void ReportQueue_purge(ReportQueue *queue);
int ReportQueue_isEmpty(ReportQueue *queue);
int ReportQueue_isOverflow(ReportQueue *queue);

