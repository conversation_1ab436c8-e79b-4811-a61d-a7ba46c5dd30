#pragma once

#include "bufView.h"
#include "stringView.h"
#include "IsoConnectionForward.h"
#include "iedTree/iedEntity.h"

#include <stdbool.h>
#include <stdint.h>

bool InfoReport_createLastApplErrorReport(BufferView* wrBuf,
                                      StringView* cntrlObj, uint8_t error, uint8_t orCat,
                                      StringView* orIdent, uint8_t ctlNum, uint8_t addCause);

bool InfoReport_createPositiveCmdTermReport( IEDEntity cntrlObj,
    BufferView* wrBuf, StringView* domainId, StringView* itemId);

bool InfoReport_createNegativeCmdTermReport(IEDEntity ctrlObj,
    BufferView* wrBuf, StringView* domainId, StringView* itemId, uint8_t addCause);

//Оформляет и отправляет отчёт
void InfoReport_send(IsoConnection* isoConn, uint8_t * infoReport,
                     size_t byteCount, uint8_t* reportMmsBuf,
                     uint8_t* reportPresentationBuf);
