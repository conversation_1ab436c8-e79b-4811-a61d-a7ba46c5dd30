#include "out_buffers.h"

#include <platform_critical_section.h>
#include <debug.h>
#include <stddef.h>

void initSessionOutBuffers(SessionBuffers* buffers)
{
    int i;
    CriticalSection_Init(&buffers->cs);
    for(i = 0; i < COTP_OUT_BUFFERS_COUNT; ++i)
    {
        buffers->buffers[i].busy = FALSE;
    }
    //CriticalSection_Unlock(&cs);
}

void SessionBuffers_done(SessionBuffers* buffers)
{
	CriticalSection_Done(&buffers->cs);
}

SessionOutBuffer* allocSessionOutBuffer(SessionBuffers* buffers, int size)
{
    int i;
    SessionOutBuffer* result = NULL;
	if (size > SESSION_OUT_BUF_SIZE)
	{
		ERROR_REPORT("Requested buffer is too large");
		return NULL;
	}

    CriticalSection_Lock(&buffers->cs);
    for(i = 0; i < COTP_OUT_BUFFERS_COUNT; ++i)
    {
        if(!buffers->buffers[i].busy)
        {
            result = buffers->buffers + i;
            result->busy = TRUE;
            break;
        }
    }
    CriticalSection_Unlock(&buffers->cs);
    return result;
}

void freeSessionOutBuffer(SessionOutBuffer* buf)
{
	buf->busy = FALSE;
}
