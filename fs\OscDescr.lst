                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bqo1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=OscDescr.c -o fs\gh_bqo1.o -list=fs/OscDescr.lst C:\Users\<USER>\AppData\Local\Temp\gh_bqo1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_bqo1.s
Source File: OscDescr.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile fs/OscDescr.c

                      10 ;		-o fs/OscDescr.o

                      11 ;Source File:   fs/OscDescr.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Oct 07 09:39:30 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include <stdlib.h>


                      21 ;2: #include "OscFiles.h"


                      22 ;3: #include "OscDescr.h"


                      23 ;4: #include "../pwin_access.h"


                      24 ;5: #include "platform_critical_section.h"


                      25 ;6: 


                      26 ;7: static OscDescrHeader *oscDescr = NULL;


                      27 ;8: 


                      28 ;9: 


                      29 ;10: static OscDescrAnalog *getAnalogPtr(void)


                      30 ;11: {


                      31 ;12: 	return (OscDescrAnalog*)(oscDescr + 1);


                      32 ;13: }


                      33 ;14: 


                      34 ;15: static OscDescrBool *getBoolPtr(void)


                      35 

                      36 ;22: }


                      37 

                      38 ;23: 


                      39 ;24: 


                      40 ;25: // ищет информацию об аналоговом канале по индексу


                      41 ;26: OscDescrAnalog *OSCDescr_findDescrAnalogItem(unsigned int itemIdex)


                      42 ;27: {


                      43 ;28: 	OscDescrAnalog *pAnalog = getAnalogPtr();


                      44 ;29: 	int firstIndex = 0;


                      45 ;30: 	int lastIndex = oscDescr->analogCount;


                      46 ;31: 	int averageIndex = 0;


                      47 ;32: 	unsigned int curIndex;


                      48 ;33: 	while (firstIndex < lastIndex)


                      49 ;34: 	{


                      50 ;35: 		averageIndex = (firstIndex + lastIndex) / 2;



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bqo1.s
                      51 ;36: 		curIndex = pAnalog[averageIndex].index;


                      52 ;37: 		if (itemIdex < curIndex)


                      53 ;38: 		{


                      54 ;39: 			lastIndex = averageIndex;


                      55 ;40: 		}


                      56 ;41: 		else


                      57 ;42: 			if (itemIdex > curIndex)


                      58 ;43: 			{


                      59 ;44: 				firstIndex = averageIndex + 1;


                      60 ;45: 			}


                      61 ;46: 			else


                      62 ;47: 			{


                      63 ;48: 


                      64 ;49: 				return &pAnalog[averageIndex];


                      65 ;50: 			}


                      66 ;51: 	}


                      67 ;52: 


                      68 ;53: 	return NULL;


                      69 ;54: }


                      70 ;55: 


                      71 ;56: // ищет информацию о дискретном канале по смещению


                      72 ;57: OscDescrBool *OSCDescr_findDescrBoolItem(unsigned int itemOffset)


                      73 ;58: {


                      74 ;59: 	// смещение дискретных


                      75 ;60: 	OscDescrBool *pBool = getBoolPtr();


                      76 ;61: 


                      77 ;62: 	int firstIndex = 0;


                      78 ;63: 	int lastIndex = oscDescr->boolCount;


                      79 ;64: 	int averageIndex = 0;


                      80 ;65: 	unsigned int curOffset;


                      81 ;66: 	while (firstIndex < lastIndex)


                      82 ;67: 	{


                      83 ;68: 		averageIndex = (firstIndex + lastIndex) / 2;


                      84 ;69: 		curOffset = pBool[averageIndex].offset;


                      85 ;70: 		if (itemOffset < curOffset)


                      86 ;71: 		{


                      87 ;72: 			lastIndex = averageIndex;


                      88 ;73: 		}


                      89 ;74: 		else


                      90 ;75: 			if (itemOffset > curOffset)


                      91 ;76: 			{


                      92 ;77: 				firstIndex = averageIndex + 1;


                      93 ;78: 			}


                      94 ;79: 			else


                      95 ;80: 			{


                      96 ;81: 


                      97 ;82: 				return &pBool[averageIndex];


                      98 ;83: 			}


                      99 ;84: 	}


                     100 ;85: 


                     101 ;86: 	return NULL;


                     102 ;87: }


                     103 ;88: 


                     104 ;89: 


                     105 ;90: // загрузка ром модуля с описанием


                     106 ;91: static bool initOscDescr(void)


                     107 

                     108 ;114: } 


                     109 

                     110 	.text

                     111 	.align	4


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bqo1.s
                     112 getAnalogPtr:

00000000 e59f11a8*   113 	ldr	r1,.L70

00000004 e5910000    114 	ldr	r0,[r1]

00000008 e2800094    115 	add	r0,r0,148

0000000c e12fff1e*   116 	ret	

                     117 	.endf	getAnalogPtr

                     118 	.align	4

                     119 

                     120 	.section ".bss","awb"

                     121 .L62:

                     122 	.data

                     123 .L63:

00000000 00000000    124 oscDescr:	.data.b	0,0,0,0

                     125 	.type	oscDescr,$object

                     126 	.size	oscDescr,4

                     127 	.text

                     128 

                     129 

                     130 	.align	4

                     131 	.align	4

                     132 OSCDescr_findDescrAnalogItem::

00000010 e92d4030    133 	stmfd	[sp]!,{r4-r5,lr}

00000014 e1a05000    134 	mov	r5,r0

00000018 ebfffff8*   135 	bl	getAnalogPtr

0000001c e59f218c*   136 	ldr	r2,.L70

00000020 e5921000    137 	ldr	r1,[r2]

00000024 e1d130bc    138 	ldrh	r3,[r1,12]

00000028 e3a04000    139 	mov	r4,0

0000002c e1540003    140 	cmp	r4,r3

00000030 aa000010    141 	bge	.L74

                     142 .L75:

00000034 e0831004    143 	add	r1,r3,r4

00000038 e0811fa1    144 	add	r1,r1,r1 lsr 31

0000003c e1a020c1    145 	mov	r2,r1 asr 1

00000040 e0801202    146 	add	r1,r0,r2 lsl 4

00000044 e5d1c005    147 	ldrb	r12,[r1,5]

00000048 e155000c    148 	cmp	r5,r12

0000004c 2a000003    149 	bhs	.L76

00000050 e1a03002    150 	mov	r3,r2

00000054 e1540003    151 	cmp	r4,r3

00000058 bafffff5    152 	blt	.L75

0000005c ea000005    153 	b	.L74

                     154 .L76:

00000060 e155000c    155 	cmp	r5,r12

00000064 91a00001    156 	movls	r0,r1

00000068 9a000003    157 	bls	.L71

0000006c e2824001    158 	add	r4,r2,1

00000070 e1540003    159 	cmp	r4,r3

00000074 baffffee    160 	blt	.L75

                     161 .L74:

00000078 e3a00000    162 	mov	r0,0

                     163 .L71:

0000007c e8bd8030    164 	ldmfd	[sp]!,{r4-r5,pc}

                     165 	.endf	OSCDescr_findDescrAnalogItem

                     166 	.align	4

                     167 ;pAnalog	r0	local

                     168 ;firstIndex	r4	local

                     169 ;lastIndex	r3	local

                     170 ;averageIndex	r2	local

                     171 ;curIndex	r12	local

                     172 


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bqo1.s
                     173 ;itemIdex	r5	param

                     174 

                     175 	.section ".bss","awb"

                     176 .L138:

                     177 	.data

                     178 	.text

                     179 

                     180 

                     181 	.align	4

                     182 	.align	4

                     183 OSCDescr_findDescrBoolItem::

00000080 e92d4030    184 	stmfd	[sp]!,{r4-r5,lr}

00000084 e1a05000    185 	mov	r5,r0

00000088 ebffffdc*   186 	bl	getAnalogPtr

                     187 ;19: 	pAnalog += oscDescr->analogCount;


                     188 

0000008c e59f211c*   189 	ldr	r2,.L70

00000090 e5921000    190 	ldr	r1,[r2]

00000094 e1d120bc    191 	ldrh	r2,[r1,12]

00000098 e080c202    192 	add	r12,r0,r2 lsl 4

                     193 ;20: 


                     194 ;21: 	return (OscDescrBool*)pAnalog;


                     195 

0000009c e1d120be    196 	ldrh	r2,[r1,14]

000000a0 e3a04000    197 	mov	r4,0

                     198 ;16: {


                     199 

                     200 ;17: 	// дискретные входы находятся после аналоговых


                     201 ;18: 	OscDescrAnalog *pAnalog = getAnalogPtr();


                     202 

000000a4 e1540002    203 	cmp	r4,r2

000000a8 aa00000f    204 	bge	.L172

                     205 .L173:

000000ac e0820004    206 	add	r0,r2,r4

000000b0 e0800fa0    207 	add	r0,r0,r0 lsr 31

000000b4 e1a010c0    208 	mov	r1,r0 asr 1

000000b8 e1a00181    209 	mov	r0,r1 lsl 3

000000bc e1b030bc    210 	ldrh	r3,[r0,r12]!

000000c0 e1550003    211 	cmp	r5,r3

000000c4 2a000003    212 	bhs	.L174

000000c8 e1a02001    213 	mov	r2,r1

000000cc e1540002    214 	cmp	r4,r2

000000d0 bafffff5    215 	blt	.L173

000000d4 ea000004    216 	b	.L172

                     217 .L174:

000000d8 e1550003    218 	cmp	r5,r3

000000dc 9a000003    219 	bls	.L165

000000e0 e2814001    220 	add	r4,r1,1

000000e4 e1540002    221 	cmp	r4,r2

000000e8 baffffef    222 	blt	.L173

                     223 .L172:

000000ec e3a00000    224 	mov	r0,0

                     225 .L165:

000000f0 e8bd8030    226 	ldmfd	[sp]!,{r4-r5,pc}

                     227 	.endf	OSCDescr_findDescrBoolItem

                     228 	.align	4

                     229 ;pBool	r12	local

                     230 ;firstIndex	r4	local

                     231 ;lastIndex	r2	local

                     232 ;averageIndex	r1	local

                     233 ;curOffset	r3	local


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bqo1.s
                     234 

                     235 ;itemOffset	r5	param

                     236 

                     237 	.section ".bss","awb"

                     238 .L244:

                     239 	.data

                     240 	.text

                     241 

                     242 

                     243 ;115: 


                     244 ;116: bool OSCDescr_init(void)


                     245 	.align	4

                     246 	.align	4

                     247 OSCDescr_init::

000000f4 e92d4000    248 	stmfd	[sp]!,{lr}

000000f8 e3a00000    249 	mov	r0,0

000000fc e52d0010    250 	str	r0,[sp,-16]!

00000100 e28d300c    251 	add	r3,sp,12

00000104 e28d2008    252 	add	r2,sp,8

00000108 e59f00a4*   253 	ldr	r0,.L365

0000010c e28d1004    254 	add	r1,sp,4

00000110 eb000000*   255 	bl	loadRomModule

                     256 ;117: {


                     257 

                     258 ;118: 	if (!initOscDescr())


                     259 

                     260 ;92: {


                     261 

                     262 ;93: 	void *pRommModule;


                     263 ;94: 	unsigned char * pRommModuleData;


                     264 ;95: 	size_t moduleDataSize;


                     265 ;96: 	OscDescrHeader *descr;


                     266 ;97: 


                     267 ;98: 	if (!loadRomModule('OSCD', &pRommModule, &pRommModuleData, &moduleDataSize,NULL))


                     268 

00000114 e3500000    269 	cmp	r0,0

00000118 1a000001    270 	bne	.L279

                     271 ;99: 	{


                     272 

                     273 ;100: 		return false;


                     274 

0000011c 13a00001    275 	movne	r0,1

00000120 ea00000b    276 	b	.L271

                     277 .L279:

                     278 ;101: 	}


                     279 ;102: 


                     280 ;103: 


                     281 ;104: 	descr = (OscDescrHeader *)pRommModuleData;


                     282 

00000124 e59d0008    283 	ldr	r0,[sp,8]

                     284 ;105: 	// неверная версия


                     285 ;106: 	if (descr->version != OSC_DESCR_VERSION)


                     286 

00000128 e5901000    287 	ldr	r1,[r0]

0000012c e3510000    288 	cmp	r1,0

00000130 0a000003    289 	beq	.L281

                     290 ;107: 	{


                     291 

                     292 ;108: 		free(descr);


                     293 

00000134 eb000000*   294 	bl	free


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bqo1.s
                     295 ;109: 		return false;


                     296 

00000138 e3b00000    297 	movs	r0,0

0000013c 13a00001    298 	movne	r0,1

00000140 ea000003    299 	b	.L271

                     300 .L281:

                     301 ;110: 	}


                     302 ;111: 	oscDescr = descr;


                     303 

00000144 e59f1064*   304 	ldr	r1,.L70

00000148 e5810000    305 	str	r0,[r1]

                     306 ;112: 	


                     307 ;113: 	return true;


                     308 

0000014c e3b00001    309 	movs	r0,1

00000150 13a00001    310 	movne	r0,1

                     311 .L271:

                     312 ;119: 	{


                     313 

                     314 ;120: 		return false;


                     315 

                     316 ;121: 	}


                     317 ;122: 


                     318 ;123: 


                     319 ;124: 	return true;


                     320 

00000154 e28dd010    321 	add	sp,sp,16

00000158 e8bd8000    322 	ldmfd	[sp]!,{pc}

                     323 	.endf	OSCDescr_init

                     324 	.align	4

                     325 ;pRommModule	[sp,4]	local

                     326 ;pRommModuleData	[sp,8]	local

                     327 ;moduleDataSize	[sp,12]	local

                     328 ;descr	r0	local

                     329 

                     330 	.section ".bss","awb"

                     331 .L350:

                     332 	.data

                     333 	.text

                     334 

                     335 ;125: }


                     336 

                     337 ;126: 


                     338 ;127: const char * OSCDescr_getTerminalName(void)


                     339 	.align	4

                     340 	.align	4

                     341 OSCDescr_getTerminalName::

                     342 ;128: {


                     343 

                     344 ;129: 	return oscDescr->terminalName;


                     345 

0000015c e59fc04c*   346 	ldr	r12,.L70

00000160 e59c0000    347 	ldr	r0,[r12]

00000164 e2800014    348 	add	r0,r0,20

00000168 e12fff1e*   349 	ret	

                     350 	.endf	OSCDescr_getTerminalName

                     351 	.align	4

                     352 

                     353 	.data

                     354 	.text

                     355 


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bqo1.s
                     356 ;130: }


                     357 

                     358 ;131: 


                     359 ;132: unsigned int OSCDescr_getTerminalVersion(void)


                     360 	.align	4

                     361 	.align	4

                     362 OSCDescr_getTerminalVersion::

                     363 ;133: {


                     364 

                     365 ;134: 	return oscDescr->terminalVersion;


                     366 

0000016c e59fc03c*   367 	ldr	r12,.L70

00000170 e59c0000    368 	ldr	r0,[r12]

00000174 e5900010    369 	ldr	r0,[r0,16]

00000178 e12fff1e*   370 	ret	

                     371 	.endf	OSCDescr_getTerminalVersion

                     372 	.align	4

                     373 

                     374 	.data

                     375 	.text

                     376 

                     377 ;135: }


                     378 

                     379 ;136: 


                     380 ;137: 


                     381 ;138: const char * OSCDescr_analogName(OscDescrAnalog *analog)


                     382 	.align	4

                     383 	.align	4

                     384 OSCDescr_analogName::

                     385 ;139: {


                     386 

                     387 ;140: 	return OSCDESCR_ANALOG_NAME(analog);


                     388 

0000017c e5901008    389 	ldr	r1,[r0,8]

00000180 e0810000    390 	add	r0,r1,r0

00000184 e12fff1e*   391 	ret	

                     392 	.endf	OSCDescr_analogName

                     393 	.align	4

                     394 

                     395 ;analog	r0	param

                     396 

                     397 	.section ".bss","awb"

                     398 .L446:

                     399 	.data

                     400 	.text

                     401 

                     402 ;141: }


                     403 

                     404 ;142: const char * OSCDescr_analogUnits(OscDescrAnalog *analog)


                     405 	.align	4

                     406 	.align	4

                     407 OSCDescr_analogUnits::

                     408 ;143: {


                     409 

                     410 ;144: 	return OSCDESCR_ANALOG_UNIT(analog);


                     411 

00000188 e590100c    412 	ldr	r1,[r0,12]

0000018c e0810000    413 	add	r0,r1,r0

00000190 e12fff1e*   414 	ret	

                     415 	.endf	OSCDescr_analogUnits

                     416 	.align	4


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bqo1.s
                     417 

                     418 ;analog	r0	param

                     419 

                     420 	.section ".bss","awb"

                     421 .L478:

                     422 	.data

                     423 	.text

                     424 

                     425 ;145: }


                     426 

                     427 ;146: 


                     428 ;147: const char * OSCDescr_boolName(OscDescrBool *pBool)


                     429 	.align	4

                     430 	.align	4

                     431 OSCDescr_boolName::

                     432 ;148: {


                     433 

                     434 ;149: 	return OSCDESCR_BOOL_NAME(pBool);


                     435 

00000194 e5901004    436 	ldr	r1,[r0,4]

00000198 e0810000    437 	add	r0,r1,r0

0000019c e12fff1e*   438 	ret	

                     439 	.endf	OSCDescr_boolName

                     440 	.align	4

                     441 

                     442 ;pBool	r0	param

                     443 

                     444 	.section ".bss","awb"

                     445 .L510:

                     446 	.data

                     447 	.text

                     448 

                     449 ;150: }


                     450 

                     451 ;151: 


                     452 ;152: float OSCDescr_getFreq(void)


                     453 	.align	4

                     454 	.align	4

                     455 OSCDescr_getFreq::

                     456 ;153: {


                     457 

                     458 ;154: 	return oscDescr->freq;


                     459 

000001a0 e59fc008*   460 	ldr	r12,.L70

000001a4 e59c0000    461 	ldr	r0,[r12]

000001a8 e5900004    462 	ldr	r0,[r0,4]

000001ac e12fff1e*   463 	ret	

                     464 	.endf	OSCDescr_getFreq

                     465 	.align	4

                     466 

                     467 	.data

                     468 	.text

                     469 

                     470 ;155: }


                     471 	.align	4

                     472 .L70:

000001b0 00000000*   473 	.data.w	.L63

                     474 	.type	.L70,$object

                     475 	.size	.L70,4

                     476 

                     477 .L365:


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bqo1.s
000001b4 4f534344    478 	.data.w	0x4f534344

                     479 	.type	.L365,$object

                     480 	.size	.L365,4

                     481 

                     482 	.align	4

                     483 ;oscDescr	.L63	static

                     484 

                     485 	.data

                     486 	.ghsnote version,6

                     487 	.ghsnote tools,3

                     488 	.ghsnote options,0

                     489 	.text

                     490 	.align	4

                     491 	.data

                     492 	.align	4

                     493 	.text

