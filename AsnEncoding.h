#ifndef  __ASNENCODING_H__
#define  __ASNENCODING_H__

#include "AssociationAsnTypes.h"
#include "stringView.h"
#include "bufView.h"
#include "local_types.h"

#include <types.h>
#include <stdint.h>
#include <stdbool.h>

//! Кодирует тэг и длину и складыват в буфер.
//! Возвращает позицию, следующую за значением в буфере
//! Используется при кодировании структур
int BerEncoder_encodeTL( unsigned char ucTag, unsigned int iLength,
                         unsigned char* pBuffer, int iBufPos );

int BerDecoder_decodeString(uint8_t* buf, int bufPos, int maxPos, uint8_t** str, int* strLen);

int BerDecoder_decodeLength(unsigned char* buffer, int* length, int bufPos, int maxBufPos);

//! Не использовать эту функцию
//! Вместо неё использовать BuffwerView_decodeTL
//! Функция оставлена чтобы не переделывать и не проверять имеющийся код.
//! В перспективе желательно от неё избавиться.
bool BerDecoder_decodeTLFromBufferView(BufferView* buf, uint8_t* pTag, int* pLen,
    int* pFullLen);

int32_t BerDecoder_decodeInt32(uint8_t* buffer, int intlen, int bufPos);
unsigned int BerDecoder_decodeUint32(unsigned char* buffer, int intLen, int bufPos);

//Кодирование типа BOOLEAN.
//pBuffer - буфер для кодирования
//bValue  - значение > 0 - TRUE
//
//Возвращает размер в байтах - всегда 3 (ASN_BOOLEANTYPE_SIZE)
int EncodeBOOLEAN( unsigned char* pBuffer, unsigned char bValue );

//Декодирование типа BOOLEAN.
//pBuffer - буфер для декодирования (размер всегда 3 - ASN_BOOLEANTYPE_SIZE)
//int iBufPos - позиция в буфере,
//pValueOut  - возвращаемое значение
//
//Возвращает  - позицию в буфере.
//      значение < 0 - ошибка
int DecodeBOOLEAN(unsigned char* pBuffer, int iBufPos, unsigned char* pValueOut);

//Кодирование типа INTERGER32 POSITIVE примитивным методом
//pBuffer -			буфер для кодирования
//iInteger -		число
//
//Возвращает размер в байтах   ( -1 - ошибка )
//int EncodePositiveINTEGER32( unsigned char* pBuffer, unsigned int iInteger );

//Кодирование типа INTERGER32 NEGATIVE примитивным методом
//pBuffer -			буфер для кодирования
//iInteger -		число
//
//Возвращает размер в байтах   ( -1 - ошибка )
//int EncodeNegativeINTEGER32( unsigned char* pBuffer, int iInteger );

//Кодирование типа INTERGER32 (NEGATIVE и POSITIVE) примитивным методом
//pBuffer -			буфер для кодирования
//iInteger -		число
//
//Возвращает размер в байтах   ( -1 - ошибка )
//int EncodeINTEGER32( unsigned char* pBuffer, int iInteger );

//Декодирование типа INTERGER32 (NEGATIVE и POSITIVE) примитивным методом
//pBuffer -			буфер для декодирования
//pInteger -		возвращаемое число
//
//Возвращает  - значение < 0 - ошибка
//int DecodeINTEGER32( unsigned char* pBuffer, int* pInteger );

//Кодирование типа BIT STRING примитивным методом
//pBuffer -			буфер для кодирования
//pBitString -		битовая строка
//iLengthInBit -	длина битовой строки в битах  (бит 0 - слева)
//
//Возвращает размер в байтах
int EncodeBIT_STRINGPrimitive( unsigned char* pBuffer, unsigned char* pBitString, int iLengthInBit );

//Декодирование типа BIT STRING примитивным методом
//pBuffer -				буфер для декодирования
//pBitStringOut -		возвращаемая битовая строка
//pBitStringSizeOut -	возвращаемая длина битовой строки
//pUnusedBitsQuantOut -	возвращаемое значение количества незначащих бит в конце
//
//Возвращает  - значение < 0 - ошибка
int DecodeBIT_STRINGPrimitive( unsigned char* pBuffer, unsigned char* pBitStringOut,
                               unsigned char* pBitStringSizeOut, unsigned char* pUnusedBitsQuantOut );

//! Декодирует BIT_STRING не более 31 бита с длиной и тегом в int;
//! Тэг не проверяется
//! При ошибке возвращает -1
int BerDecoder_DecodeBitStringTLToInt(uint8_t* inBuf, int inBufPos);

//Кодирование структуры ASN_Initiate_RequestPDU конструктивным методом SEQUENCE
//pBuffer -						буфер для кодирования
//pInnerStructBuffer -			буфер для кодирования внутренней структуры
//
//pASN_Initiate_RequestPDU-		сама структура
//
//Возвращает размер в байтах   ( -1 - ошибка )
//int EncodeASN_Initiate_RequestPDU( unsigned char* pBuffer, unsigned char* pInnerStructBuffer,
//													ASN_Initiate_RequestPDU* pASN_Initiate_RequestPDU );



//Кодирование структуры ASN_Initiate_ResponsePDU конструктивным методом SEQUENCE
//pBuffer -						буфер для кодирования
//pInnerStructBuffer -			буфер для кодирования внутренней структуры
//
//pASN_Initiate_ResponsePDU-	сама структура
//
//Возвращает размер в байтах   ( -1 - ошибка )
//int EncodeASN_Initiate_ResponsePDU( unsigned char* pBuffer, unsigned char* pInnerStructBuffer,
//													 ASN_Initiate_ResponsePDU* pASN_Initiate_ResponsePDU );

//Декодирование длины типа ASN
//pBuffer		-	буфер для декодирования
//iBufPos		-	позиция длины данных
//iMaxBufPos	-	максимальный размер буфера
//pLength		-	возвращаемая длина
//
//Возвращает очередную текущую позицию в буфере
int	BerDecoder_DecodeLengthOld(unsigned char* pBuffer, int iBufPos, int iMaxBufPos, int* pLength);

//Декодирование UINT32 типа ASN
//pBuffer		-	буфер для декодирования
//iBufPos		-	позиция данных
//iLength		-	длина данных
//
//Возвращает - значение UINT32
unsigned int BerDecoder_DecodeUint32Old( unsigned char* pBuffer, int iBufPos, int iLength );

//Возвращает очередную текущую позицию в буфере
int BerDecoder_DecodeObjectName(unsigned char* pBuffer, int iBufPos, int iTotalLength,
    unsigned char** pItemID, int* itemIdLen, unsigned char** pDomainId, int* domainLen);

//Возвращает очередную текущую позицию в буфере
int BerDecoder_DecodeObjectNameToStringView(unsigned char* pBuffer, int bufPos,
    int iTotalLength, StringView* domainId, StringView* itemId);

//Вычисляет размер поля длины типа ASN
//uLength		-	длина типа
//
//Возвращает - размер поля длины типа ASN
int BerEncoder_determineLengthSize( unsigned int uLength );

//Вычисляет полный размер объекта, включая длину и тэг, из из размера данных
int BerEncoder_determineFullObjectSize(unsigned int uLength);

//Кодирование значения длины параметра BER
//iLength	- Длина
//pBuffer	- буфер для кодирования
//iBufPos	- позиция в буфере для кодирования
//
//возвращает - очередную позицию iBufPos
int BerEncoder_encodeLength( unsigned int iLength, unsigned char* pBuffer, int iBufPos );

//Вычисляет размер UInt32 типа ASN
//iValue		-	значение
//
//Возвращает - размер UInt32 типа ASN
int BerEncoder_UInt32determineEncodedSize(unsigned int iValue);
size_t BerEncoder_uint32determineEncodedSizeTL(uint32_t value);

int BerEncoder_Int32DetermineEncodedSize(int iValue);

//Упаковывает Integer типа ASN
//pInteger		  -	указатель на значение
//iOriginalSize	  - исходный размер
//
//Возвращает - упакованный размер Integer типа ASN
int BerEncoder_CompressInteger( unsigned char* pInteger, int iOriginalSize );

//Кодирование UInt32 BER
//iValue	- значение
//pBuffer	- буфер для кодирования
//iBufPos	- позиция в буфере для кодирования
//
//возвращает - очередную позицию iBufPos
int BerEncoder_encodeUInt32(unsigned int iValue, unsigned char* pBuffer, int iBufPos);

int BerEncoder_EncodeInt32(int iValue, unsigned char* pBuffer, int iBufPos);


//Кодирование float с Тегом и Длиной BER
//ucTag		- Тег
//Value	- значение
//formatWidth - размер в битах 32 - float, 64 - double
//exponentWidth - размер экспоненты,
//pBuffer	- буфер для кодирования
//iBufPos	- позиция в буфере для кодирования
//
//возвращает - очередную позицию iBufPos
int BerEncoder_EncodeFloatWithTL(unsigned char ucTag, float Value,
    unsigned char formatWidth, unsigned char exponentWidth,
    unsigned char* pBuffer, int iBufPos);

//Кодирование UInt32 с Тегом и Длиной BER
//ucTag		- Тег
//iValue	- значение
//pBuffer	- буфер для кодирования
//iBufPos	- позиция в буфере для кодирования
//
//возвращает - очередную позицию iBufPos
int BerEncoder_encodeUInt32WithTL(unsigned char ucTag, unsigned int iValue,
    unsigned char* pBuffer, int iBufPos);

int BerEncoder_EncodeInt32WithTL(unsigned char ucTag, int iValue,
    unsigned char* pBuffer, int iBufPos);


int BerEncoder_encodeOctetString(uint8_t tag,const uint8_t* octetString, uint32_t octetStringSize,
                                 uint8_t* buffer, int bufPos);

//Кодирование BitString с Тегом и Длиной BER
//ucTag				- Тег
//iBitStringSize	- размер битовой строки в битах
//pBitString		- битовая строка
//pBuffer			- буфер для кодирования
//iBufPos			- позиция в буфере для кодирования
//
//возвращает - очередную позицию iBufPos
int BerEncoder_encodeBitString( unsigned char ucTag, int iBitStringSize, unsigned char* pBitString,
                                unsigned char* pBuffer, int iBufPos );


//! Кодирует bitstring, хранящуюся в uint16_t data.
//! data рассматривается как буфер в памяти, то есть первым
//! отправляется младший байт.
//! Битовая строка должна начинаться с бита 7 младшего байта
//! и заканчиваться младшими битами старшего байта.
//! То есть функция аналогична BerEncoder_encodeBitString,
//! но для фиксированной длины буфера 2 байта.
//! возвращает - очередную позицию iBufPos
int BerEncoder_encodeBitStringUshortBuf(uint8_t tag, int bitCount, uint16_t data,
    uint8_t* outBuf, int outBufPos);


//! Кодирует bitstring, хранящуюся в младших битах uint16_t
//! возвращает - очередную позицию iBufPos
int BerEncoder_encodeUshortBitString(uint8_t tag, int bitCount, uint16_t data,
    uint8_t* outBuf, int outBufPos);

//! Кодирует bitstring, хранящуюся в младших битах uint8_t
//! возвращает - очередную позицию iBufPos
int BerEncoder_encodeUcharBitString(uint8_t tag, int bitCount, uint8_t data,
    uint8_t* outBuf, int outBufPos);

//Вычисляет размер строки типа ASN
//String		-	строка
//
//Возвращает - размер строки
int BerEncoder_determineEncodedStringSize( const char* String );

//! Кодирование String с Тегом и Длиной BER
//! ucTag		- Тег
//! pString	- строка
//! pBuffer	- буфер для кодирования
//! iBufPos	- позиция в буфере для кодирования
// !возвращает - очередную позицию iBufPos
int BerEncoder_encodeStringWithTL( unsigned char ucTag, const char* pString,
                                    unsigned char* pBuffer, int iBufPos );

//Кодирование BOOLEAN с Тегом и Длиной BER
//ucTag		- Тег
//ucValue	- значение
//pBuffer	- буфер для кодирования
//iBufPos	- позиция в буфере для кодирования
//
//возвращает - очередную позицию iBufPos
int BerEncoder_encodeBoolean( unsigned char ucTag, unsigned char ucValue,
                              unsigned char* pBuffer, int iBufPos );

//Реверсирует байты в буфере
//pOctets -	указатель на буфер
//iSize	  - размер буфера
void BerEncoder_RevertByteOrder( unsigned char* pOctets, const int iSize );

//  Возвращает 1 в случае полного вхождения str1 в str2 или str2 в str1
//  иначе 0.
int IsIncluded(unsigned char* str1, unsigned char* str2);


float BerDecoder_decodeFloat(unsigned char* buffer, int bufPos);

#endif	 //__ASNENCODING_H__
