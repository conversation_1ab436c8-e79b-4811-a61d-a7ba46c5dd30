#include "mms_get_name_list.h"

#include "AsnEncoding.h"
#include "MmsConst.h"
#include "mms_error.h"
#include "mmsconnection.h"
#include "iedmodel.h"
#include <debug.h>

#include <types.h>
#include <stddef.h>


#define TAG_CONTINUE_AFTER 0x82


#define OBJECT_SCOPE_VMD					0
#define OBJECT_SCOPE_DOMAIN					1
#define OBJECT_SCOPE_ASSOCIATION			2

#define OBJECT_CLASS_NAMED_VARIABLE			0
#define OBJECT_CLASS_NAMED_VARIABLE_LIST	2
#define OBJECT_CLASS_JOURNAL				8
#define OBJECT_CLASS_DOMAIN					9

// 5 - максимуальная шапка списка (тэг + размер)
// 3 - "more follows"
// 6 - Invoke ID
// 5 - тэг и размер всего ответа
#define MAX_NAME_LIST_HEADER (5 + 3 + 6 + 5)

/*
int mms_createEmptyNameListResponse( unsigned int invokeId, unsigned char* response)
{
    int bufPos = 0;
    unsigned int identifierListSize;
    unsigned int  invokeIdSize;
    unsigned int listOfIdentifierSize;
    unsigned int getNameListSize;
    unsigned int confirmedServiceResponseSize;
    unsigned int confirmedResponsePDUSize;
    int moreFollows;


    //debugSendText("mms_createNameListResponse");

    //============== Определяем размеры =================
    //Размер списка идентификаторов
    //Настраиваем DomainNameWriter на определение размера

    identifierListSize = 0;

    moreFollows = FALSE;

    //Размер ответа на список имён
    listOfIdentifierSize = 1 + BerEncoder_determineLengthSize(identifierListSize)
            + identifierListSize;


    getNameListSize = listOfIdentifierSize;
    if (moreFollows == FALSE)
    {
        //3 байта
        getNameListSize += 3;
    }

    //Размер Invoke ID
    invokeIdSize = BerEncoder_UInt32determineEncodedSize(invokeId) + 2;

    //Размер всего ответа
    confirmedServiceResponseSize = 1 + BerEncoder_determineLengthSize(getNameListSize)
            + getNameListSize;
    confirmedResponsePDUSize = confirmedServiceResponseSize + invokeIdSize;

    //=================== Кодируем ответ ==================

    bufPos = BerEncoder_encodeTL(0xa1, confirmedResponsePDUSize, response, bufPos);

    bufPos = BerEncoder_encodeTL(ASN_INTEGER, invokeIdSize - 2, response, bufPos);
    bufPos = BerEncoder_encodeUInt32(invokeId, response, bufPos);

    bufPos = BerEncoder_encodeTL(0xa1, getNameListSize, response, bufPos);
    bufPos = BerEncoder_encodeTL(0xa0, identifierListSize, response, bufPos);


    identifierListSize =  0;
    bufPos += identifierListSize;

    if (!moreFollows)
    {        
        bufPos = BerEncoder_encodeBoolean(0x81, moreFollows, response, bufPos);
    }
    return bufPos;
}
*/

int mms_createNameListResponse(MmsConnection* mmsConn,
        unsigned int invokeId, unsigned char* response, int rootObjPos,
                               uint8_t* continueAfter, int continueAfterLen,
                               bool recursive, int objectsTagTowrite)
{
    int bufPos = 0;
    unsigned int identifierListSize;
    unsigned int  invokeIdSize;
    unsigned int listOfIdentifierSize;
    unsigned int getNameListSize;
    unsigned int confirmedServiceResponseSize;
    unsigned int confirmedResponsePDUSize;    
    int moreFollows;
    DomainNameWriter* nameWriter = &mmsConn->nameWriter;

    //debugSendText("mms_createNameListResponse");

    //============== Определяем размеры =================
    //Размер списка идентификаторов
    //Настраиваем DomainNameWriter на определение размера
    DomainNameWriter_init(nameWriter,NULL,
                          MAX_MMS_RESPONSE_SIZE - MAX_NAME_LIST_HEADER);
    if(continueAfter != NULL)
    {
        DomainNameWriter_setStartName(nameWriter,continueAfter, continueAfterLen);
    }

    writeChildrenNames(rootObjPos, nameWriter, recursive, objectsTagTowrite);
    identifierListSize = nameWriter->totalSize;

    moreFollows = nameWriter->bufferFull;    

    //Размер ответа на список имён
    listOfIdentifierSize = 1 + BerEncoder_determineLengthSize(identifierListSize)
            + identifierListSize;


    getNameListSize = listOfIdentifierSize;
    
    
    //размер moreFollows
    getNameListSize += 3;
    

    //Размер Invoke ID
    invokeIdSize = BerEncoder_UInt32determineEncodedSize(invokeId) + 2;

    //Размер всего ответа
    confirmedServiceResponseSize = 1 + BerEncoder_determineLengthSize(getNameListSize)
            + getNameListSize;
    confirmedResponsePDUSize = confirmedServiceResponseSize + invokeIdSize;

    //=================== Кодируем ответ ==================

    bufPos = BerEncoder_encodeTL(0xa1, confirmedResponsePDUSize, response, bufPos);

    bufPos = BerEncoder_encodeTL(ASN_INTEGER, invokeIdSize - 2, response, bufPos);
    bufPos = BerEncoder_encodeUInt32(invokeId, response, bufPos);

    bufPos = BerEncoder_encodeTL(0xa1, getNameListSize, response, bufPos);
    bufPos = BerEncoder_encodeTL(0xa0, identifierListSize, response, bufPos);

    //Настраиваем DomainNameWriter на фактическую запись
    DomainNameWriter_init(nameWriter, response + bufPos,
                          MAX_MMS_RESPONSE_SIZE - MAX_NAME_LIST_HEADER);
    if(continueAfter != NULL)
    {
        DomainNameWriter_setStartName(nameWriter,continueAfter, continueAfterLen);
    }

    writeChildrenNames(rootObjPos, nameWriter, recursive, objectsTagTowrite);

    identifierListSize =  nameWriter->totalSize;
    //debugSendUshort("nameWriter returned:", identifierListSize);

    bufPos += identifierListSize;
    bufPos = BerEncoder_encodeBoolean(0x81, moreFollows, response, bufPos);

    return bufPos;
}

int mms_handleGetNameListRequest(MmsConnection* mmsConn,
                                 unsigned char* inBuf, int bufPos, int maxBufPos,
                                  unsigned int invokeId, unsigned char* response)
{
    int responseSize = 0;
    int rootObj;
    int objectClass = -1;

    int objectScope = -1;

    uint8_t* domainId = NULL;
    int domainIdLength;

    uint8_t* continueAfter = NULL;
    int continueAfterLength = 0;

    //debugSendDump("\tdata:", inBuf + bufPos, maxBufPos - bufPos);

    while (bufPos < maxBufPos) {
        unsigned char tag = inBuf[bufPos++];
        int length;

        bufPos = BerDecoder_decodeLength(inBuf, &length, bufPos, maxBufPos);

        if (bufPos < 0)  {
            //mmsMsg_createMmsRejectPdu(&invokeId, MMS_ERROR_REJECT_INVALID_PDU, response);			
            return 0;
        }

        //debugSendUshort("Name List request tag:", tag);


        switch (tag) {
        case 0xa0: // objectClass
            bufPos++;
            length = inBuf[bufPos++];
            objectClass = BerDecoder_decodeUint32(inBuf, length, bufPos);            
            break;

        case 0xa1: // objectScope
            {

                unsigned char objectScopeTag = inBuf[bufPos++];
                bufPos = BerDecoder_decodeLength(inBuf, &length, bufPos, maxBufPos);

                //debugSendUshort("\tobjectScopeTag:", objectScopeTag);
                switch (objectScopeTag) {
                case 0x80: // vmd-specific
                    objectScope = OBJECT_SCOPE_VMD;
                    break;
                case 0x81: // domain-specific
                    domainIdLength = length;
                    domainId = inBuf + bufPos;
                    //debugSendUshort("\tdomainIdLength:", domainIdLength);
                    //debugSendStrL("\tdomainId:", domainId, domainIdLength);
                    objectScope = OBJECT_SCOPE_DOMAIN;
                    break;
                case 0x82: // association-specific
                    objectScope = OBJECT_SCOPE_ASSOCIATION;                    
                    return CreateMmsConfirmedErrorPdu( invokeId, response,
                                                   MMS_ERROR_ACCESS_OBJECT_ACCESS_UNSUPPORTED );
                default:
                    debugSendUshort("!!!!! Unsupported objectScope:",tag);
                    mms_createMmsRejectPdu(&invokeId, MMS_ERROR_REJECT_UNRECOGNIZED_MODIFIER, response);
                    return 0;
                }                
            }
            break;

        case TAG_CONTINUE_AFTER:
            continueAfter = inBuf + bufPos;
            continueAfterLength = length;
            break;
        default:        
            return 0;
            /*
            return CreateMmsConfirmedErrorPdu( invokeId, response,
                                                          MMS_ERROR_ACCESS_OBJECT_ACCESS_UNSUPPORTED );
                                                          */
        }

        bufPos += length;
    }           

    switch(objectScope)
    {
    case OBJECT_SCOPE_VMD:
        //debugSendText("Process OBJECT_SCOPE_VMD");
        responseSize =
                mms_createNameListResponse(mmsConn, invokeId,  response, 0,
					continueAfter, continueAfterLength, FALSE, IED_ANY_TAG);
        return responseSize;
    case OBJECT_SCOPE_DOMAIN:
		{
			int ldSection;
			int objectsTagToWrite;
			if (objectClass == OBJECT_CLASS_NAMED_VARIABLE_LIST)
			{
				//Для Data sets
				ldSection = IED_VMD_DATA_SET_SECTION;
				objectsTagToWrite = IED_DATA_SET;
			}
            else if (objectClass == OBJECT_CLASS_JOURNAL)
			{
				//Journal не поддерживается
                return CreateMmsConfirmedErrorPdu( invokeId, response,
					MMS_ERROR_ACCESS_OBJECT_NON_EXISTENT );				
			}
			else
			{
                //Для обычных данных
				ldSection = IED_VMD_DATA_SECTION;
				objectsTagToWrite = IED_ANY_TAG;
			}
			
			rootObj = findDomainSection(ldSection,
				domainId, domainIdLength);
			if (rootObj == 0)
			{
				ERROR_REPORT("Unable to find domain cection");
				return CreateMmsConfirmedErrorPdu(invokeId, response,
					MMS_ERROR_ACCESS_OBJECT_NON_EXISTENT);
			}
			return mms_createNameListResponse(mmsConn, invokeId, response,
				rootObj, continueAfter, continueAfterLength, TRUE,
				objectsTagToWrite);
		}                
    default:
        return 0;
    }

}



