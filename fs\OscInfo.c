#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <stdbool.h>

#include "OscInfo.h"
#include "../bufView.h"
#include "platform_critical_section.h"
#include "OscFiles.h"

// заголовоки осцилограмм разных версий
#ifndef STATIC_ASSERT
#define STATIC_ASSERT(e) typedef char __C_ASSERT__[(e)?1:-1]
#endif


OscWriteBuffer oscHeaderBuf;
#pragma alignvar (4)
CriticalSection csHeaderBuf;

#define OSC_HEADER_SIZE offsetof(OSCInfoStruct, iface)


// NOTE: копии этих структур храняться в мониторе и соответсвующих программах log
typedef struct OSCInfoStruct3 OSCInfoStruct3;
struct OSCInfoStruct3
{
	//! Размер осцилограммы в байтах. Размер:4	    
	unsigned long size;
	//! Абсолютный порядковый номер. Размер:4			    
	unsigned long absNumber;
	//! SizeCRC - контрольная сумма размера. Размер: 2
	unsigned short sizeCRC;
	//! количество кадров предыстории размер: 1		    
	unsigned char prehistCount;
	//! номер первого кадра предыстории размер: 1		    
	unsigned char prehistStart;
	//! Длительность осциллограммы в миллисекундах. Размер:4	    
	unsigned long duration;
	//! Тактовая частота АЦП в герцах. Размер:4		    
	unsigned long adcClkFreq;
	//! Дата (UTC) размер: 4					    
	unsigned long time;
	//! миллисекунды (Дополнение к дате, 0-999) размер: 2	    
	unsigned short time_ms;
	//! неиспользуемые байты 
	unsigned char oscVersionUnusedByte;
	//! Версия осциллографа 1 байта
	unsigned char oscVersion;

	//! колличество отсчетов на кадр
	unsigned short pointPerFrame;
	unsigned short reservedField;
	//! число аналоговых каналов в осциллограмме
	unsigned short ainCount;
	//! число дискретных каналов в осциллограмме
	unsigned short dinCount;

};

typedef struct OSCInfoStruct4 OSCInfoStruct4;
struct  OSCInfoStruct4
{
	//! Размер осцилограммы в байтах. Размер:4	    
	unsigned long size;
	//! Абсолютный порядковый номер. Размер:4			    
	unsigned long absNumber;
	//! SizeCRC - контрольная сумма размера. Размер: 2
	unsigned short sizeCRC;
	unsigned short reservedField;
	//! Длительность осциллограммы в миллисекундах. Размер:4	    
	unsigned long duration;
	//! Тактовая частота АЦП в герцах. Размер:4		    
	unsigned long adcClkFreq;
	//! Дата (UTC) размер: 4		
	unsigned long time;
	//! миллисекунды (Дополнение к дате, 0-999) размер: 2	    
	unsigned short time_ms;

	unsigned char oscVersionUnusedByte;
	//! Версия осциллографа 1 байта
	unsigned char oscVersion;

	//! количество кадров предыстории размер: 4		    
	unsigned long prehist_count;
	//! номер первого кадра предыстории размер: 4	
	unsigned long prehistStart;
	//! колличество отсчетов на кадр
	unsigned short pointPerFrame;
	//! Размер одной выборки АЦП в байтах
	unsigned char adcSampleSize;
	//! Количество бит после фиксированной точки в выборке АЦП,
	//! включая незначащие биты.
	//! (значение выборки AЦП надо поделить на 2 в этой степени)	
	unsigned char adcFractionSize;
	//! число аналоговых каналов в осциллограмме
	unsigned short ainCount;
	//! число дискретных каналов в осциллограмме
	unsigned short dinCount;
};

// интерфейс
typedef struct IOSCInfo IOSCInfo;
struct IOSCInfo
{
	// одинаковые для v3 и v4
	unsigned long(*getUTCDate)(OSCInfoStruct *oscInfo);
	unsigned long(*getDateMS)(OSCInfoStruct *oscInfo);
	unsigned long(*getOscVersion)(OSCInfoStruct *oscInfo);
	unsigned long(*getADCClkFreq)(OSCInfoStruct *oscInfo);
	unsigned long(*getPrehistFrameCount)(OSCInfoStruct *oscInfo);
	unsigned long(*getPrehistFirstFrameNum)(OSCInfoStruct *oscInfo);
	unsigned long(*getPointPerFrameCount)(OSCInfoStruct *oscInfo);
	unsigned long(*getAnalogInCount)(OSCInfoStruct *oscInfo);
	unsigned long(*getDigInCount)(OSCInfoStruct *oscInfo);
	unsigned int(*getOscSize)(OSCInfoStruct *oscInfo);
	// разные для v3 и v4
	unsigned long(*getHeaderSize)(OSCInfoStruct *oscInfo);
	int(*getADCSampleSize)(OSCInfoStruct *oscInfo);
	int(*getADCFractionSize)(OSCInfoStruct *oscInfo);
};

typedef struct OSCInfoAnalog OSCInfoAnalog;
struct OSCInfoAnalog
{
	OscDescrAnalog *pDescr;
	float cft;
	int maxValue;
	int minValue;
};

typedef struct OSCInfoBool OSCInfoBool;
struct OSCInfoBool
{
	OscDescrBool *pDescr;
};
//! класс
struct  OSCInfoStruct
{
	// должны идти первыми
	union 
	{
		OSCInfoStruct3 v3;
		OSCInfoStruct4 v4;
	};
	IOSCInfo *iface;
	OscWriteBuffer wbContent;
	OscWriteBuffer wbFrame;
	OSCInfoAnalog *pAnalog;
	OSCInfoBool *pBool;
	// количество фреймов в осцилограмме, включая предысторию
	unsigned int frameCount; 
	unsigned int frameSize;

	unsigned int firstFrameOffset;
};


static unsigned long getUTCDate(OSCInfoStruct *oscInfo)
{
	return oscInfo->v3.time;
}
static unsigned long getDateMS(OSCInfoStruct *oscInfo)
{
	return oscInfo->v3.time_ms;
}
static unsigned long getOscVersion(OSCInfoStruct *oscInfo)
{
	return oscInfo->v3.oscVersion;
}
static unsigned long getADCClkFreq(OSCInfoStruct *oscInfo)
{
	return oscInfo->v3.adcClkFreq;
}
static unsigned long getPrehistFrameCount3(OSCInfoStruct *oscInfo)
{
	return oscInfo->v3.prehistCount;
}

static unsigned long getPrehistFirstFrameNum3(OSCInfoStruct *oscInfo)
{
	return oscInfo->v3.prehistStart;
}

static unsigned long getPrehistFrameCount4(OSCInfoStruct *oscInfo)
{
	return oscInfo->v4.prehist_count;
}

static unsigned long getPrehistFirstFrameNum4(OSCInfoStruct *oscInfo)
{
	return oscInfo->v4.prehistStart;
}


static unsigned long getPointPerFrameCount3(OSCInfoStruct *oscInfo)
{
	return oscInfo->v3.pointPerFrame;
}

static unsigned long getPointPerFrameCount4(OSCInfoStruct *oscInfo)
{
	return oscInfo->v4.pointPerFrame;
}


static unsigned long getAnalogInCount3(OSCInfoStruct *oscInfo)
{
	return oscInfo->v3.ainCount;
}
static unsigned long getDigInCount3(OSCInfoStruct *oscInfo)
{
	return oscInfo->v3.dinCount;
}

static unsigned long getAnalogInCount4(OSCInfoStruct *oscInfo)
{
	return oscInfo->v4.ainCount;
}
static unsigned long getDigInCount4(OSCInfoStruct *oscInfo)
{
	return oscInfo->v4.dinCount;
}

static unsigned long getHeaderSize3(OSCInfoStruct *oscInfo)
{
	return sizeof(OSCInfoStruct3);
}
static unsigned long getHeaderSize4(OSCInfoStruct *oscInfo)
{
	return sizeof(OSCInfoStruct4);
}


static int getADCSampleSize3(OSCInfoStruct *oscInfo)
{
	return 2;
}
static int getADCFractionSize3(OSCInfoStruct *oscInfo)
{
	return 0;
}

static int getADCSampleSize4(OSCInfoStruct *oscInfo)
{
	int adcSampleSize = oscInfo->v4.adcSampleSize;
	if (adcSampleSize == 0)
	{
		//На случай кривого формата (коммент из монитора)
		adcSampleSize = 4;
	}
	return adcSampleSize;
}
static int getADCFractionSize4(OSCInfoStruct *oscInfo)
{
	return oscInfo->v4.adcFractionSize;
}

static int getAnalogContentIdSize(OSCInfoStruct *oscInfo)
{
	return 2;
}
static int getBoolContentIdSize(OSCInfoStruct *oscInfo)
{
	return 2;
}
static unsigned int getOscSize(OSCInfoStruct *oscInfo)
{
	return oscInfo->v3.size;
}
// инициализация интерфейсов
IOSCInfo oscInfoV3 = 
{
	getUTCDate,
	getDateMS,
	getOscVersion,
	getADCClkFreq,
	getPrehistFrameCount3,
	getPrehistFirstFrameNum3,
	getPointPerFrameCount3,
	getAnalogInCount3,
	getDigInCount3,
	getOscSize,
	getHeaderSize3,
	getADCSampleSize3,
	getADCFractionSize3,
};
IOSCInfo oscInfoV4 =
{
	getUTCDate,
	getDateMS,
	getOscVersion,
	getADCClkFreq,
	getPrehistFrameCount4,
	getPrehistFirstFrameNum4,
	getPointPerFrameCount4,
	getAnalogInCount4,
	getDigInCount4,
	getOscSize,
	getHeaderSize4,
	getADCSampleSize4,
	getADCFractionSize4
};

bool OSCInfo_init(void)
{	
	#pragma alignvar (4)
	static unsigned char oscHeader[OSC_HEADER_SIZE];
	if (!OscWriteBuffer_attach(&oscHeaderBuf, oscHeader, OSC_HEADER_SIZE))
	{
		return false;
	}

	CriticalSection_Init(&csHeaderBuf);
	return true;
}

static unsigned int getAdcPeriodSize(OSCInfoStruct *oscInfo)
{
	return 4;
}

static unsigned int getFrameSize(OSCInfoStruct *oscInfo)
{
	int pointsPerFrame = OSCInfo_getPointPerFrameCount(oscInfo);
	int analogCount = OSCInfo_getAnalogCount(oscInfo);
	int boolCount = OSCInfo_getBoolCount(oscInfo);
	int adcPeriodSize = getAdcPeriodSize(oscInfo);
	int adcSampleSize = OSCInfo_getADCSampleSize(oscInfo);
	if (adcSampleSize == 0)
	{
		return 0;
	}
	if (pointsPerFrame == 0)
	{
		return 0;
	}
	if (adcPeriodSize == 0)
	{
		return 0;
	}


	return adcPeriodSize +
		pointsPerFrame * adcSampleSize*analogCount
		+ 1 * boolCount;
}

static unsigned int getContentInfoSize(OSCInfoStruct *oscInfo)
{
	int analogCount;
	int boolCount;
	analogCount = OSCInfo_getAnalogCount(oscInfo);
	boolCount = OSCInfo_getBoolCount(oscInfo);
	
	return analogCount*getAnalogContentIdSize(oscInfo)
		+ boolCount * getBoolContentIdSize(oscInfo);

}
static unsigned int getFrameCount(OSCInfoStruct *oscInfo)
{
	int dataLen = oscInfo->iface->getOscSize(oscInfo);
	int frameSize = getFrameSize(oscInfo);
	if (frameSize == 0)
	{
		return 0;
	}
	dataLen -= OSCInfo_getHeaderSize(oscInfo);
	dataLen -= getContentInfoSize(oscInfo);
	if (dataLen < 0)
	{
		return 0;
	}

	return dataLen / frameSize;
}
OSCInfoStruct* OSCInfo_create(OscWriteBuffer *headBufferView)
{
	OSCInfoStruct *oscHeader = (OSCInfoStruct*)OscWriteBuffer_data(headBufferView);
	OSCInfoStruct *oscInfo;
	IOSCInfo *iface;
	int contentSize;
	int analogCount;
	int boolCount;
	
	if (oscHeader->v3.oscVersion == 3 )
	{
		iface = &oscInfoV3;
	}
	else if (oscHeader->v4.oscVersion == 4)
	{
		iface = &oscInfoV4;
	}
	else
	{
		return NULL;
	}

	// память
	oscInfo = OscFiles_malloc(sizeof(OSCInfoStruct));
	if (!oscInfo)
	{
		return NULL;
	}
	
	memset(oscInfo, 0, sizeof(OSCInfoStruct));

	// инициализация заголовка
	memcpy(oscInfo, oscHeader, OSC_HEADER_SIZE);
	// интерфейс и приватные поля
	oscInfo->iface = iface;
	oscInfo->pAnalog = NULL;
	oscInfo->pBool = NULL;


	analogCount = OSCInfo_getAnalogCount(oscInfo);
	// не может быть без аналоговых каналов
	if (analogCount == 0)
	{
		OSCInfo_destroy(oscInfo);
		return NULL;
	}
	oscInfo->pAnalog = OscFiles_malloc(analogCount * sizeof(OSCInfoAnalog));
	if (!oscInfo->pAnalog)
	{
		OSCInfo_destroy(oscInfo);
		return NULL;
	}


	boolCount = OSCInfo_getBoolCount(oscInfo);
	if (boolCount != 0)
	{
		oscInfo->pBool = OscFiles_malloc(boolCount * sizeof(OSCInfoBool));
		if (!oscInfo->pBool)
		{
			OSCInfo_destroy(oscInfo);
			return NULL;
		}
	}


	contentSize = getContentInfoSize(oscInfo);
	if (contentSize == 0)
	{
		OSCInfo_destroy(oscInfo);
		return NULL;
	}

	// инициализация количества кадров
	oscInfo->frameCount = getFrameCount(oscInfo);
	if (oscInfo->frameCount == 0)
	{
		OSCInfo_destroy(oscInfo);
		return FALSE;
	}

	oscInfo->firstFrameOffset = OSCInfo_getHeaderSize(oscInfo) +
		contentSize;

	// буфер под состав осцилограммы
	if (!OscWriteBuffer_create(&oscInfo->wbContent, contentSize))
	{
		OSCInfo_destroy(oscInfo);
		return NULL;
	}
	
	// буфер под фрейм
	oscInfo->frameSize = getFrameSize(oscInfo);
	if (oscInfo->frameSize == 0)
	{
		OSCInfo_destroy(oscInfo);
		return NULL;
	}

	if (!OscWriteBuffer_create(&oscInfo->wbFrame, oscInfo->frameSize))
	{
		OSCInfo_destroy(oscInfo);
		return NULL;
	}

	return oscInfo;
}

void OSCInfo_destroy(OSCInfoStruct *oscInfo)
{
	if (oscInfo->pAnalog)
	{
		OscFiles_free(oscInfo->pAnalog);
	}
	if (oscInfo->pBool)
	{
		OscFiles_free(oscInfo->pBool);
	}

	OscWriteBuffer_destroy(&oscInfo->wbContent);
	OscWriteBuffer_destroy(&oscInfo->wbFrame);

	OscFiles_free(oscInfo);
}

OscWriteBuffer * OSCInfo_lockHeaderBuf(void)
{
	CriticalSection_Lock(&csHeaderBuf);
	return &oscHeaderBuf;
}

void OSCInfo_unlockHeaderBuf(void)
{
	CriticalSection_Unlock(&csHeaderBuf);
}

unsigned long OSCInfo_getUTCDate(OSCInfoStruct *oscInfo)
{
	return oscInfo->iface->getUTCDate(oscInfo);
}

unsigned long OSCInfo_getDateMS(OSCInfoStruct *oscInfo)
{
	return oscInfo->iface->getDateMS(oscInfo);
}

unsigned long OSCInfo_getOscVersion(OSCInfoStruct *oscInfo)
{
	return oscInfo->iface->getOscVersion(oscInfo);
}

unsigned long OSCInfo_getADCClkFreq(OSCInfoStruct *oscInfo)
{
	return oscInfo->iface->getADCClkFreq(oscInfo);
}

unsigned long OSCInfo_getPrehistFrameCount(OSCInfoStruct *oscInfo)
{
	return oscInfo->iface->getPrehistFrameCount(oscInfo);
}

unsigned long OSCInfo_getPrehistFirstFrameNum(OSCInfoStruct *oscInfo)
{
	return oscInfo->iface->getPrehistFirstFrameNum(oscInfo);
}

unsigned long OSCInfo_getPointPerFrameCount(OSCInfoStruct *oscInfo)
{
	return oscInfo->iface->getPointPerFrameCount(oscInfo);
}

unsigned long OSCInfo_getAnalogCount(OSCInfoStruct *oscInfo)
{
	return oscInfo->iface->getAnalogInCount(oscInfo);
}

unsigned long OSCInfo_getBoolCount(OSCInfoStruct *oscInfo)
{
	return oscInfo->iface->getDigInCount(oscInfo);
}

unsigned long OSCInfo_getHeaderSize(OSCInfoStruct *oscInfo)
{
	return oscInfo->iface->getHeaderSize(oscInfo);
}

unsigned long OSCInfo_getFrameCount(OSCInfoStruct *oscInfo)
{
	return oscInfo->frameCount;
}

unsigned int OSCInfo_getFrameOffset(OSCInfoStruct *oscInfo, unsigned int frameNum)
{
	unsigned int prehistFrameCount = OSCInfo_getPrehistFrameCount(oscInfo);
	// если кадр из предыстории
	if (frameNum < prehistFrameCount)
	{
		int firstFrameNum = OSCInfo_getPrehistFirstFrameNum(oscInfo);
		unsigned int relativeFrameNum = firstFrameNum + frameNum;

		if (relativeFrameNum >= prehistFrameCount)
		{
			relativeFrameNum = relativeFrameNum - prehistFrameCount;
		}
		return oscInfo->firstFrameOffset + relativeFrameNum * oscInfo->frameSize;
	}
	else
	{
		return oscInfo->firstFrameOffset + frameNum * oscInfo->frameSize;
	}
}

int OSCInfo_getADCSampleSize(OSCInfoStruct *oscInfo)
{
	return oscInfo->iface->getADCSampleSize(oscInfo);
}

int OSCInfo_getADCFractionSize(OSCInfoStruct *oscInfo)
{
	return oscInfo->iface->getADCFractionSize(oscInfo);
}

int OSCInfo_getOscContentOffset(OSCInfoStruct *oscInfo)
{
	return OSCInfo_getHeaderSize(oscInfo);
}

OscWriteBuffer * OSCInfo_getBufferContent(OSCInfoStruct *oscInfo)
{
	return &oscInfo->wbContent;
}

OscWriteBuffer * OSCInfo_getFrameBuffer(OSCInfoStruct *oscInfo)
{
	return &oscInfo->wbFrame;
}

//! период текущего фрейма
int OSCFrame_getADCPeriod(OSCInfoStruct *oscInfo)
{
	unsigned int result;
	unsigned char *frame = OscWriteBuffer_data(&oscInfo->wbFrame);
	result = frame[0];
	result += frame[1] << 8;
	result += frame[2] << 16;
	result += frame[3] << 24;
	return result;
}
static int extractWithSignExtension(void* pData, size_t byteCount)
{
	int result = 0;
	int insignificantBitCount;
	if (byteCount > sizeof(int))
	{
		//Не лезет в int
		return 0;
	}


	memcpy(&result, pData, byteCount);
	//Расширяем знак
	insignificantBitCount = (sizeof(int) - byteCount) * 8;
	result <<= insignificantBitCount;
	result >>= insignificantBitCount;
	return result;
}


#define MAX_COMTRADE_SAMPLE_BIT_WIDTH 20

static void updateADCMaxAbsValue(OSCInfoStruct *oscInfo, unsigned int analogNum)
{
	OSCInfoAnalog *pAnalog = &oscInfo->pAnalog[analogNum];
	//Разрядность целой части выборки АЦП
	int adcIntPartSize = pAnalog->pDescr->bitDepth;

	//Разрядность выборки COMTRADE в битах
	int comtradeSampleSize = adcIntPartSize +
		OSCInfo_getADCFractionSize(oscInfo);

	int adcMaxAbsValue = (1 << (comtradeSampleSize - 1)) - 1;
	pAnalog->maxValue = adcMaxAbsValue;
	pAnalog->minValue = -adcMaxAbsValue;
}

static void updateAnalogCft(OSCInfoStruct *oscInfo, unsigned int analogNum)
{
	OSCInfoAnalog *pAnalog = &oscInfo->pAnalog[analogNum];
	//Общий коэффициент канала с учётом коэффициента вторички и
	//сдвига для попаданию в разрядную сетку COMTRADE
	double channelCoef;
	double secondaryCoef = pAnalog->pDescr->cft;
	double fixedPointCompensation = 1 << OSCInfo_getADCFractionSize(oscInfo);
	channelCoef = 1 / (secondaryCoef * fixedPointCompensation);

	pAnalog->cft = (float)channelCoef;
}

int OSCFrame_getAnalogValue(OSCInfoStruct *oscInfo, unsigned int pointNum, unsigned int analogNum)
{
	//	OSCInfoAnalog *pAnalog = &oscInfo->pAnalog[analogNum];
	//	int analogCount = OSCInfo_getAnalogCount(oscInfo);
	unsigned char *frame = OscWriteBuffer_data(&oscInfo->wbFrame);
	unsigned char *value;
	int result = 0;
	int adcSampleSize = OSCInfo_getADCSampleSize(oscInfo);
	int adcSampleCount = OSCInfo_getPointPerFrameCount(oscInfo);

		
	// смещение на 0 аналоговое значение
	value = frame + getAdcPeriodSize(oscInfo);
	// значение с учетом номер отсчета
	value += analogNum * adcSampleCount * adcSampleSize;
	// значение с учетом номера канала
	value += adcSampleSize * pointNum;
	// память в знаковое значение
	result = extractWithSignExtension(value, adcSampleSize);

	return result;
}


int OSCFrame_getBoolValue(OSCInfoStruct *oscInfo, int sampleNum, int boolNum)
{
	unsigned char *frame = OscWriteBuffer_data(&oscInfo->wbFrame);
	unsigned char *value;
	int analogCount = OSCInfo_getAnalogCount(oscInfo);
	int adcSampleSize = OSCInfo_getADCSampleSize(oscInfo);
	int pointPerFrame = OSCInfo_getPointPerFrameCount(oscInfo);

	value = frame + getAdcPeriodSize(oscInfo) + pointPerFrame * analogCount * adcSampleSize;
	value += boolNum;
	return *value;
}


bool OSCFrame_getTick(OSCInfoStruct *oscInfo, double *tick)
{
	unsigned char *frame = OscWriteBuffer_data(&oscInfo->wbFrame);
	unsigned long adcTick;
	double mainFreq = OSCInfo_getADCClkFreq(oscInfo);
	double result ;
	memcpy(&adcTick, frame, getAdcPeriodSize(oscInfo));
	if (adcTick != 0)
	{
		result = 1000000 / (mainFreq / adcTick);
		*tick = result;
		return true;
	}
	{
		return false;
	}
}

bool OSCFrame_getFreq(OSCInfoStruct *oscInfo, float *freq)
{
	unsigned char *frame = OscWriteBuffer_data(&oscInfo->wbFrame);
	unsigned long adcFreq;
	double mainFreq = OSCInfo_getADCClkFreq(oscInfo);
	double result;
	memcpy(&adcFreq, frame, getAdcPeriodSize(oscInfo));
	if (adcFreq != 0)
	{
		result = mainFreq / adcFreq;
		*freq = (float)result;
		return true;
	}
	else
	{
		return false;
	}
}

//! указатель состав аналоговых каналов
static void *getAnalogContentPtr(OSCInfoStruct *oscInfo)
{
	unsigned char *data = OscWriteBuffer_data(&oscInfo->wbContent);
	unsigned short *pIndexs = (unsigned short*)data;
	return pIndexs;
}
//! указатель на состав дискретных каналов
static void *getBoolContentPtr(OSCInfoStruct *oscInfo)
{
	size_t offset = OSCInfo_getAnalogCount(oscInfo) * getAnalogContentIdSize(oscInfo);
	unsigned char *data = OscWriteBuffer_data(&oscInfo->wbContent);
	return (unsigned short*)(data + offset);
}

static bool boolInit(OSCInfoStruct *oscInfo)
{
	size_t count = OSCInfo_getBoolCount(oscInfo);
	unsigned short *pOffsets = getBoolContentPtr(oscInfo);

	unsigned int i;
	OSCInfoBool *pBool = oscInfo->pBool;


	for (i = 0; i < count; ++i)
	{
		OscDescrBool *descr = OSCDescr_findDescrBoolItem(pOffsets[i]);
		if (!descr)
		{
			return false;
		}

		pBool[i].pDescr = descr;
	}
	return true;
}

static bool analogInit(OSCInfoStruct *oscInfo)
{
	size_t count = OSCInfo_getAnalogCount(oscInfo);
	unsigned short *pIndexs = getAnalogContentPtr(oscInfo);
	unsigned int i;
	OSCInfoAnalog *pAnalog = oscInfo->pAnalog;
	
	for (i = 0; i < count; ++i)
	{
		OscDescrAnalog *descr = OSCDescr_findDescrAnalogItem(pIndexs[i]);
		if (!descr)
		{
			return false;
		}

		pAnalog[i].pDescr = descr;
		updateADCMaxAbsValue(oscInfo, i);
		updateAnalogCft(oscInfo, i);
	}
	return true;
}

bool OSCInfo_initContent(OSCInfoStruct *oscInfo)
{
	return analogInit(oscInfo) && boolInit(oscInfo);
}

OscDescrAnalog * OSCInfo_getAnalog(OSCInfoStruct *oscInfo, int index)
{
	return oscInfo->pAnalog[index].pDescr;
}

OscDescrBool * OSCInfo_getBool(OSCInfoStruct *oscInfo, int index)
{
	return oscInfo->pBool[index].pDescr;
}

float OSCInfo_getAnalogCft(OSCInfoStruct *oscInfo, int analogNum)
{
	OSCInfoAnalog *pAnalog = &oscInfo->pAnalog[analogNum];
	return pAnalog->cft;
}

int OSCInfo_getAnalogMax(OSCInfoStruct *oscInfo, int index)
{
	OSCInfoAnalog *pAnalog = &oscInfo->pAnalog[index];
	return pAnalog->maxValue;
}

int OSCInfo_getAnalogMin(OSCInfoStruct *oscInfo, int index)
{
	OSCInfoAnalog *pAnalog = &oscInfo->pAnalog[index];
	return pAnalog->minValue;
}
