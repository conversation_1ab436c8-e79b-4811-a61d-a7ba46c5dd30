#include "MemoryManager.h"
#include <stdint.h>
#include <stdlib.h>

//Комментарий для проверки кодировки

#define MEMORY_SIZE (2048 * 1024)

static uint8_t* g_memory = NULL;
static size_t g_pos = 0;

bool MM_init(void)
{
    g_pos = 0;
    g_memory = malloc(MEMORY_SIZE);
    if(g_memory != NULL)
    {

        return true;
    }
    else
    {
        return false;
    }
}

void* MM_alloc(size_t size)
{
    void* allocated;
    if (g_memory == NULL)
    {
        return NULL;
    }
    //Округляем в большую сторону до 4
    if ((size & 3) != 0)
    {
        size += 4 - (size & 3);
    }

    if (g_pos + size > MEMORY_SIZE)
    {
        return false;
    }
    allocated = g_memory + g_pos;
    g_pos += size;
    return allocated;
}

size_t MM_getAllocated(void)
{
    return g_pos;
}

size_t MM_getRemaining(void)
{
    return MEMORY_SIZE - g_pos;
}
