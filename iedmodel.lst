                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=iedmodel.c -o gh_es81.o -list=iedmodel.lst C:\Users\<USER>\AppData\Local\Temp\gh_es81.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
Source File: iedmodel.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile iedmodel.c -o

                      10 ;		iedmodel.o

                      11 ;Source File:   iedmodel.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:56:03 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "iedmodel.h"


                      21 ;2: #include "AsnEncoding.h"


                      22 ;3: #include "MmsConst.h"


                      23 ;4: #include "mms_data.h"


                      24 ;5: #include "mms_rcb.h"


                      25 ;6: #include "mms_gocb.h"


                      26 ;7: #include "bufViewBER.h"


                      27 ;8: #include "tools.h"


                      28 ;9: #include "IEDCompile/InnerAttributeTypes.h"


                      29 ;10: #include <debug.h>


                      30 ;11: #include <string.h>


                      31 ;12: #include <stdbool.h>


                      32 ;13: 


                      33 ;14: static int encodeStructAccessAttrs(uint8_t* outBuf, int bufPos, int objectPos,


                      34 ;15:     bool determineSize, bool topStruct);


                      35 ;16: 


                      36 ;17: // Нужно писать перед байтовыми массивами, потому что


                      37 ;18: // без этого иногда собирается падающая программа.


                      38 ;19: // Возможно, баг postlink


                      39 ;20: #pragma alignvar (4)


                      40 ;21: 


                      41 ;22: unsigned char defaultIed[] = {


                      42 ;23:         0xea, 0x3a, 0x1a, 0x03, 0x49, 0x45, 0x44, 0xe2,


                      43 ;24:         0x33, 0x1a, 0x07, 0x4c, 0x44, 0x65, 0x76, 0x69,


                      44 ;25:         0x63, 0x65, 0xe4, 0x28, 0x1a, 0x05, 0x4d, 0x4d,


                      45 ;26:         0x58, 0x55, 0x30, 0xe6, 0x1f, 0x1a, 0x04, 0x54,


                      46 ;27:         0x6f, 0x74, 0x57, 0xe8, 0x17, 0x1a, 0x03, 0x6d,


                      47 ;28:         0x61, 0x67, 0xe9, 0x10, 0x1a, 0x01, 0x66, 0x02,


                      48 ;29:         0x01, 0x02, 0x04, 0x08, 0xc0, 0x00, 0x00, 0x00,


                      49 ;30:         0xcf, 0x1a, 0x31, 0x35


                      50 ;31: };



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
                      51 ;32: 


                      52 ;33: unsigned char* iedModel = defaultIed;


                      53 ;34: int iedModelSize = sizeof(defaultIed);


                      54 ;35: 


                      55 ;36: void setIedModel(unsigned char* pModel, int modelSize)


                      56 ;37: {


                      57 ;38:     iedModel = pModel;


                      58 ;39:     iedModelSize = modelSize;


                      59 ;40: }


                      60 ;41: 


                      61 ;42: int readTL(int pos, uint8_t* pTag, int* pLen, int* pFullLen)


                      62 ;43: {


                      63 ;44:     BufferView bv;


                      64 ;45: 


                      65 ;46:     bv.len = iedModelSize;


                      66 ;47:     bv.p = iedModel;


                      67 ;48:     bv.pos = pos;


                      68 ;49: 


                      69 ;50:     if (BerDecoder_decodeTLFromBufferView(&bv, pTag, pLen, pFullLen))


                      70 ;51:     {


                      71 ;52:         return bv.pos;


                      72 ;53:     }


                      73 ;54: 


                      74 ;55:     return 0;


                      75 ;56: }


                      76 ;57: 


                      77 ;58: int skipObject(int pos)


                      78 ;59: {


                      79 ;60:     int len;


                      80 ;61:     //пропускаем тэг


                      81 ;62:     pos++;


                      82 ;63:     //определяем и пропускаем длину


                      83 ;64:     pos = BerDecoder_decodeLength(iedModel, &len, pos, iedModelSize);


                      84 ;65:     if( pos <= 0)


                      85 ;66:     {


                      86 ;67:         return 0;


                      87 ;68:     }


                      88 ;69:     //пропускаем содержимое


                      89 ;70:     pos+=len;


                      90 ;71:     return pos;


                      91 ;72: }


                      92 ;73: 


                      93 ;74: //Возвращает полную длину строки (вместе с тегом и длиной),


                      94 ;75: // закодированной BER(например, VisibleString)


                      95 ;76: //Работает только с тэгом в один байт


                      96 ;77: // При ошибке возвращает 0


                      97 ;78: int getBerStringLength(int berStringPos)


                      98 ;79: {


                      99 ;80:     int length;


                     100 ;81:     int newPos;


                     101 ;82:     berStringPos++;//Пропускаем тэг


                     102 ;83:     newPos = BerDecoder_decodeLength(iedModel, &length, berStringPos ,


                     103 ;84:                                      iedModelSize);


                     104 ;85:     if(newPos < 1)


                     105 ;86:     {


                     106 ;87:         return 0;


                     107 ;88:     }


                     108 ;89:     else


                     109 ;90:     {


                     110 ;91:         return 1 + (newPos - berStringPos) + length;


                     111 ;92:     }



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
                     112 ;93: }


                     113 ;94: 


                     114 ;95: // Возвращает имя объекта IED из описания информационной модели


                     115 ;96: // в виде BER VisibleString.


                     116 ;97: // Результат складывается в buf, размер результата возвращается.


                     117 ;98: // pObjPos принимает позицию объекта, и возвращает позицию,


                     118 ;99: // следующую за объектом.


                     119 ;100: // Если buf == NULL, то функция не пишет в buf (используется


                     120 ;101: // для определения полного размера имени).


                     121 ;102: // При ошибке возвращает 0


                     122 ;103: int getIEDObjectNameString(int *pObjPos, unsigned char* buf)


                     123 ;104: {


                     124 ;105:     int objLen;


                     125 ;106:     int nameLen;


                     126 ;107:     int namePos;


                     127 ;108:     int pos = *pObjPos;


                     128 ;109:     pos++;//Пропускаем тэг


                     129 ;110:     //Получаем длину объекта и позицию имени.


                     130 ;111:     namePos = BerDecoder_decodeLength(iedModel, &objLen, pos , iedModelSize);


                     131 ;112:     if(namePos < 1)


                     132 ;113:     {


                     133 ;114:         return 0;


                     134 ;115:     }


                     135 ;116: 


                     136 ;117:     nameLen = getBerStringLength(namePos);


                     137 ;118:     if( !nameLen )


                     138 ;119:     {


                     139 ;120:         return 0;


                     140 ;121:     }


                     141 ;122: 


                     142 ;123:     if(buf != NULL)


                     143 ;124:     {


                     144 ;125:         memcpy(buf, iedModel + namePos, nameLen);


                     145 ;126:     }


                     146 ;127:     *pObjPos = namePos + objLen;


                     147 ;128:     return nameLen;


                     148 ;129: }


                     149 ;130: 


                     150 ;131: bool getObjectName(int objPos, StringView* result)


                     151 ;132: {


                     152 ;133:     uint8_t nameTag;


                     153 ;134:     int nameLen;


                     154 ;135:     int namePos;


                     155 ;136:     int objectLen;


                     156 ;137: 


                     157 ;138:     //Пропускаем тэг и длину


                     158 ;139:     int pos = readTL(objPos, NULL, &objectLen, NULL);


                     159 ;140:     if(!pos)


                     160 ;141:     {


                     161 ;142:         return FALSE;


                     162 ;143:     }


                     163 ;144:     if (objectLen == 0)


                     164 ;145:     {


                     165 ;146:         ERROR_REPORT("Empty object");


                     166 ;147:         return FALSE;


                     167 ;148:     }


                     168 ;149: 


                     169 ;150:     namePos = readTL(pos, &nameTag, &nameLen, NULL);


                     170 ;151: 


                     171 ;152:     if(!namePos || nameTag != ASN_VISIBLE_STRING)


                     172 ;153:     {



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
                     173 ;154:         return FALSE;


                     174 ;155:     }


                     175 ;156: 


                     176 ;157: 


                     177 ;158:     StringView_init(result, (char*)iedModel + namePos, nameLen);


                     178 ;159: 


                     179 ;160:     return TRUE;


                     180 ;161: }


                     181 ;162: 


                     182 ;163: bool IEDModel_isServiceInfo(uint8_t tag)


                     183 ;164: {


                     184 ;165:     return tag == ASN_VISIBLE_STRING


                     185 ;166:             || tag ==  IED_GSE_LIST


                     186 ;167:             || tag == IED_CONTROL_INFO


                     187 ;168:             || tag == IED_OBJ_FLAGS;


                     188 ;169: }


                     189 ;170: 


                     190 ;171: int getSubObjectsPos(int rootObjPos, int* pEndPos)


                     191 ;172: {


                     192 ;173:     int endPos;


                     193 ;174:     int rootObjLen;


                     194 ;175:     int pos = rootObjPos;


                     195 ;176:     //Пропускаем тэг корневого объекта


                     196 ;177:     pos++;


                     197 ;178: 


                     198 ;179:     //Получаем длину объекта


                     199 ;180:     pos = BerDecoder_decodeLength(iedModel, &rootObjLen, pos, iedModelSize);


                     200 ;181:     if( pos <= 0)


                     201 ;182:     {


                     202 ;183:         return 0;


                     203 ;184:     }


                     204 ;185: 


                     205 ;186:     //Получаем позицию конца объекта


                     206 ;187:     endPos = pos + rootObjLen;


                     207 ;188:     if(endPos > iedModelSize)


                     208 ;189:     {


                     209 ;190:         //Объект неправдоподобно большой


                     210 ;191:         return 0;


                     211 ;192:     }


                     212 ;193:     *pEndPos = endPos;


                     213 ;194: 


                     214 ;195:     if (pos >= endPos)


                     215 ;196:     {


                     216 ;197:         //Пустой объект


                     217 ;198:         return pos;


                     218 ;199:     }


                     219 ;200: 


                     220 ;201:     //Пропускаем служебную информацию


                     221 ;202:     while (pos < endPos)


                     222 ;203:     {


                     223 ;204:         if(IEDModel_isServiceInfo(iedModel[pos]))


                     224 ;205:         {


                     225 ;206:             pos = skipObject(pos);


                     226 ;207:             if (pos == 0)


                     227 ;208:             {


                     228 ;209:                 return 0;


                     229 ;210:             }


                     230 ;211:         }


                     231 ;212:         else


                     232 ;213:         {


                     233 ;214:             return pos;



                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
                     234 ;215:         }


                     235 ;216:     }


                     236 ;217: 


                     237 ;218:     //Пустой объект


                     238 ;219:     return pos;


                     239 ;220: }


                     240 ;221: 


                     241 ;222: int findObjectBySimpleName(int rootObjPos, unsigned char* name, int argNameLen)


                     242 ;223: {


                     243 ;224:     int objPos;


                     244 ;225:     int endPos;


                     245 ;226: 


                     246 ;227:     objPos = getSubObjectsPos(rootObjPos, &endPos);


                     247 ;228:     if( objPos == 0)


                     248 ;229:     {


                     249 ;230:         return 0;


                     250 ;231:     }


                     251 ;232: 


                     252 ;233:     while (objPos < endPos)


                     253 ;234:     {


                     254 ;235:         int nameCompareResult;


                     255 ;236:         int nameLen;


                     256 ;237:         int objLen;


                     257 ;238:         int namePos;


                     258 ;239:         int nextObjPos;


                     259 ;240:         int pos = objPos;


                     260 ;241:         pos++;//Пропускаем тэг


                     261 ;242:         //Получаем длину объекта и позицию имени.


                     262 ;243:         namePos = BerDecoder_decodeLength(iedModel, &objLen, pos, iedModelSize);


                     263 ;244:         if (namePos < 1)


                     264 ;245:         {


                     265 ;246:             return 0;


                     266 ;247:         }


                     267 ;248:         if (iedModel[namePos] != ASN_VISIBLE_STRING)


                     268 ;249:         {


                     269 ;250:             //Не найдено имя объекта


                     270 ;251:             return 0;


                     271 ;252:         }


                     272 ;253:         nextObjPos = namePos + objLen;


                     273 ;254: 


                     274 ;255:         // Пропускаем тэг


                     275 ;256:         pos = namePos + 1;


                     276 ;257: 


                     277 ;258:         //Получаем длину имени


                     278 ;259:         //namePos в результате должен указывать на саму строку


                     279 ;260:         namePos = BerDecoder_decodeLength(iedModel, &nameLen, pos, iedModelSize);


                     280 ;261:         if (namePos < 1)


                     281 ;262:         {


                     282 ;263:             return 0;


                     283 ;264:         }


                     284 ;265:         //Сравниваем имена


                     285 ;266:         nameCompareResult = memcmp(name, iedModel + namePos, nameLen);


                     286 ;267:         if (nameLen == argNameLen &&  nameCompareResult == 0)


                     287 ;268:         {


                     288 ;269:             return objPos;


                     289 ;270:         }


                     290 ;271:         objPos = nextObjPos;


                     291 ;272:     }


                     292 ;273: 


                     293 ;274:     //debugSendText("\t!!!!!!!!!!Object is not found");


                     294 ;275:     return 0;



                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
                     295 ;276: }


                     296 ;277: 


                     297 ;278: int findObjectByTag(int rootObjPos, uint8_t tagToFind)


                     298 ;279: {


                     299 ;280:     int objPos;


                     300 ;281:     int endPos;


                     301 ;282: 


                     302 ;283:     objPos = getSubObjectsPos(rootObjPos, &endPos);


                     303 ;284:     if( objPos == 0)


                     304 ;285:     {


                     305 ;286:         return 0;


                     306 ;287:     }


                     307 ;288: 


                     308 ;289:     while (objPos < endPos)


                     309 ;290:     {


                     310 ;291:         int objLen;


                     311 ;292:         int pos = objPos;


                     312 ;293:         uint8_t tag = iedModel[pos];


                     313 ;294:         if(tag == tagToFind)


                     314 ;295:         {


                     315 ;296:             return pos;


                     316 ;297:         }


                     317 ;298:         pos++;


                     318 ;299:         //Получаем длину объекта и позицию имени.


                     319 ;300:         pos = BerDecoder_decodeLength(iedModel, &objLen, pos, iedModelSize);


                     320 ;301:         if (pos < 1)


                     321 ;302:         {


                     322 ;303:             ERROR_REPORT("Unable to decode length");


                     323 ;304:             return 0;


                     324 ;305:         }


                     325 ;306:         objPos = pos + objLen;


                     326 ;307:     }


                     327 ;308: 


                     328 ;309:     ERROR_REPORT("Object tag is not found");


                     329 ;310:     return 0;


                     330 ;311: }


                     331 ;312: 


                     332 ;313: int findDomainSection(int section, uint8_t* domainId, int domainIdLen)


                     333 ;314: {


                     334 ;315:     int vmdPos = findObjectBySimpleName(0, domainId, domainIdLen);


                     335 ;316:     if (vmdPos == 0)


                     336 ;317:     {


                     337 ;318:         return 0;


                     338 ;319:     }


                     339 ;320:     return findObjectByTag(vmdPos, section);


                     340 ;321: }


                     341 ;322: 


                     342 ;323: // Длина первого простого в сложном имени до разделителя или конца строки


                     343 ;324: int getSimpleNameLen(uint8_t* name, int fullNameLen, bool* delimiterFound)


                     344 ;325: {


                     345 ;326:     int len = 0;


                     346 ;327:     *delimiterFound = FALSE;


                     347 ;328:     while (len < fullNameLen)


                     348 ;329:     {


                     349 ;330:         if (name[len] == '$')


                     350 ;331:         {


                     351 ;332:             *delimiterFound = TRUE;


                     352 ;333:             return len;


                     353 ;334:         }


                     354 ;335:         len++;


                     355 ;336:     }



                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
                     356 ;337:     return len;


                     357 ;338: }


                     358 ;339: 


                     359 ;340: int findObjectByPath(int rootObjPos, uint8_t* name, int fullNameLen)


                     360 ;341: {


                     361 ;342:     int objPos = rootObjPos;


                     362 ;343:     while (fullNameLen != 0)


                     363 ;344:     {


                     364 ;345:         bool delimiterfound;


                     365 ;346:         int simpleNameLen = getSimpleNameLen(name, fullNameLen,


                     366 ;347:             &delimiterfound);


                     367 ;348:         objPos = findObjectBySimpleName(objPos, name, simpleNameLen);


                     368 ;349:         if (objPos == 0)


                     369 ;350:         {


                     370 ;351:             return 0;


                     371 ;352:         }


                     372 ;353: 


                     373 ;354:         if (delimiterfound)


                     374 ;355:         {


                     375 ;356:             simpleNameLen++;


                     376 ;357:         }


                     377 ;358: 


                     378 ;359:         name += simpleNameLen;


                     379 ;360:         fullNameLen -= simpleNameLen;


                     380 ;361:     }


                     381 ;362:     return objPos;


                     382 ;363: }


                     383 ;364: 


                     384 ;365: int findObjectByFullName(int section, StringView* domainName, StringView* objectName)


                     385 ;366: {


                     386 ;367:     int sectionPos;


                     387 ;368:     int objectPos;


                     388 ;369:     sectionPos = findDomainSection(section, (uint8_t*)domainName->p, domainName->len);


                     389 ;370:     if(sectionPos == 0)


                     390 ;371:     {


                     391 ;372:         return 0;


                     392 ;373:     }


                     393 ;374:     objectPos = findObjectByPath(sectionPos, (uint8_t*)objectName->p, objectName->len);


                     394 ;375:     if(objectPos == 0)


                     395 ;376:     {


                     396 ;377:         return 0;


                     397 ;378:     }


                     398 ;379:     return objectPos;


                     399 ;380: }


                     400 ;381: 


                     401 ;382: int getDataSetByPath(StringView* pDatasetPath)


                     402 ;383: {


                     403 ;384:     StringView domainName;


                     404 ;385:     StringView objectName;


                     405 ;386:     int dataSetPos;


                     406 ;387: 


                     407 ;388:     if(! StringView_splitChar(pDatasetPath, '/', &domainName, &objectName))


                     408 ;389:     {


                     409 ;390:         ERROR_REPORT("Unable to split dataset name");


                     410 ;391:         return 0;


                     411 ;392:     }


                     412 ;393: 


                     413 ;394:     dataSetPos = findObjectByFullName(IED_VMD_DATA_SET_SECTION,


                     414 ;395:                                       &domainName, &objectName);


                     415 ;396:     if(dataSetPos == 0)


                     416 ;397:     {



                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
                     417 ;398:         ERROR_REPORT("Unable to find dataset");


                     418 ;399:         return 0;


                     419 ;400:     }


                     420 ;401: 


                     421 ;402:     return dataSetPos;


                     422 ;403: }


                     423 ;404: 


                     424 ;405: void writeChildrenNames(int rootObjPos, DomainNameWriter* writer,


                     425 ;406:     bool recursive, int objectsTagToWrite)


                     426 ;407: {


                     427 ;408:     int objEndPos;


                     428 ;409:     int childObjectPos = getSubObjectsPos(rootObjPos, &objEndPos);


                     429 ;410:     if (childObjectPos == 0)


                     430 ;411:     {


                     431 ;412:         //Подобъектов нет или ошибка.


                     432 ;413:         //Это нормальное завешение функции для IED_DA_FINAL, например.


                     433 ;414:         return;


                     434 ;415:     }


                     435 ;416:     while (childObjectPos < objEndPos)


                     436 ;417:     {


                     437 ;418:         int subObjLen;


                     438 ;419:         int nameLen;


                     439 ;420:         int namePos;


                     440 ;421:         int objContentPos;


                     441 ;422:         int pos = childObjectPos;


                     442 ;423:         uint8_t tag = iedModel[pos++];


                     443 ;424:         //Позиция имени в формате ber и размер подобъекта


                     444 ;425:         namePos = BerDecoder_decodeLength(iedModel, &subObjLen, pos,


                     445 ;426:             iedModelSize);


                     446 ;427:         objContentPos = namePos;


                     447 ;428:         if (namePos < 1)


                     448 ;429:         {


                     449 ;430:             //Если ошибка, считаем что больше детей нет


                     450 ;431:             return;


                     451 ;432:         }


                     452 ;433:         if (iedModel[namePos++] != ASN_VISIBLE_STRING)


                     453 ;434:         {


                     454 ;435:             //Если ошибка, считаем что больше детей нет


                     455 ;436:             return;


                     456 ;437:         }


                     457 ;438:         //Начало непосредственно строки имени и длина этой строки


                     458 ;439:         namePos = BerDecoder_decodeLength(iedModel, &nameLen, namePos,


                     459 ;440:             iedModelSize);


                     460 ;441:         if (namePos < 1)


                     461 ;442:         {


                     462 ;443:             //Если ошибка, считаем что больше детей нет


                     463 ;444:             return;


                     464 ;445:         }


                     465 ;446:         DomainNameWriter_pushName(writer, iedModel + namePos, nameLen);


                     466 ;447:         if (objectsTagToWrite == IED_ANY_TAG || objectsTagToWrite == tag)


                     467 ;448:         {


                     468 ;449:             DomainNameWriter_encode(writer);


                     469 ;450:         }


                     470 ;451:         if(recursive)


                     471 ;452:         {


                     472 ;453:             writeChildrenNames(childObjectPos, writer, TRUE, objectsTagToWrite);


                     473 ;454:         }


                     474 ;455:         DomainNameWriter_discardName(writer);


                     475 ;456:         childObjectPos = objContentPos + subObjLen;


                     476 ;457:     }


                     477 ;458: }



                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
                     478 ;459: 


                     479 ;460: int encodeReadConst(uint8_t* outBuf, int bufPos, int constObjPos, bool determineSize)


                     480 ;461: {


                     481 ;462:     //Размер длины и тэга вместе


                     482 ;463:     int sizeOfTL;


                     483 ;464: 


                     484 ;465:     int fullSize;


                     485 ;466:     int objectSize;


                     486 ;467:     uint8_t constTypeTag;


                     487 ;468:     uint8_t mmsTag;


                     488 ;469:     //Пропускаем тэг


                     489 ;470:     int pos = constObjPos +1;


                     490 ;471:     pos = BerDecoder_decodeLength(iedModel, &objectSize, pos, iedModelSize);


                     491 ;472:     sizeOfTL = pos - constObjPos;


                     492 ;473:     fullSize = sizeOfTL + objectSize;


                     493 ;474:     if(determineSize)


                     494 ;475:     {


                     495 ;476:         return fullSize;


                     496 ;477:     }


                     497 ;478: 


                     498 ;479:     memcpy(outBuf + bufPos, iedModel + constObjPos, fullSize);


                     499 ;480: 


                     500 ;481:     constTypeTag = outBuf[bufPos];


                     501 ;482: 


                     502 ;483:     if ((constTypeTag & BER_TAG_CLASS_MASK) != BER_CONTEXT_SPECIFIC)


                     503 ;484:     {


                     504 ;485:         ERROR_REPORT("Invalid constant tag");


                     505 ;486:         return 0;


                     506 ;487:     }


                     507 ;488:     constTypeTag &= ~BER_TAG_CLASS_MASK;


                     508 ;489: 


                     509 ;490:     switch (constTypeTag)


                     510 ;491:     {


                     511 ;492:     case IEC61850_BOOLEAN:


                     512 ;493:         mmsTag = IEC61850_BER_BOOLEAN;


                     513 ;494:         break;


                     514 ;495:     case IEC61850_INT32:


                     515 ;496:     case IEC61850_INT64:


                     516 ;497:     case IEC61850_ENUMERATED:


                     517 ;498:         mmsTag = IEC61850_BER_INTEGER;


                     518 ;499:         break;


                     519 ;500:     case IEC61850_INT8U:


                     520 ;501:     case IEC61850_INT16U:


                     521 ;502:     case IEC61850_INT32U:


                     522 ;503:         mmsTag = IEC61850_BER_UNSIGNED_INTEGER;


                     523 ;504:         break;


                     524 ;505:     case IEC61850_FLOAT32:


                     525 ;506:         mmsTag = IEC61850_BER_FLOAT;


                     526 ;507:         break;


                     527 ;508:     case IEC61850_VISIBLE_STRING_32:


                     528 ;509:     case IEC61850_VISIBLE_STRING_64:


                     529 ;510:     case IEC61850_VISIBLE_STRING_65:


                     530 ;511:     case IEC61850_VISIBLE_STRING_129:


                     531 ;512:     case IEC61850_VISIBLE_STRING_255:


                     532 ;513:         mmsTag = IEC61850_BER_VISIBLE_STRING;


                     533 ;514:         break;


                     534 ;515:     case IEC61850_UNICODE_STRING_255:


                     535 ;516:         mmsTag = IEC61850_BER_MMS_STRING;


                     536 ;517:         break;


                     537 ;518:     case IEC61850_OCTET_STRING_6:


                     538 ;519:     case IEC61850_OCTET_STRING_64:



                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
                     539 ;520:         mmsTag = IEC61850_BER_OCTET_STRING;


                     540 ;521:         break;


                     541 ;522:     case IEC61850_GENERIC_BITSTRING:


                     542 ;523:     case IEC61850_QUALITY:


                     543 ;524:         mmsTag = IEC61850_BER_BIT_STRING;


                     544 ;525:         break;


                     545 ;526:     case IEC61850_ENTRY_TIME:


                     546 ;527:         mmsTag = IEC61850_BER_BINARY_TIME;


                     547 ;528:         break;


                     548 ;529:     default:


                     549 ;530:         //Если неизвестно что, то пусть будет строка


                     550 ;531:         ERROR_REPORT("Unknown constant tag %02X", constTypeTag);


                     551 ;532:         mmsTag = IEC61850_BER_VISIBLE_STRING;


                     552 ;533:     }


                     553 ;534:     outBuf[bufPos] = mmsTag;


                     554 ;535:     return bufPos + fullSize;


                     555 ;536: }


                     556 ;537: 


                     557 ;538: uint8_t* getAlignedDescrStruct(int pos)


                     558 ;539: {


                     559 ;540:     //Какое смещение использовано для выравнивания структуры описания


                     560 ;541:     uint8_t* pDescrStructAlignOffset;


                     561 ;542:     uint8_t descrTag;


                     562 ;543:     int descrLen;


                     563 ;544:     //Получаем указатель на структуру описания


                     564 ;545:     descrTag = iedModel[pos++];


                     565 ;546:     if (descrTag != ASN_OCTET_STRING)


                     566 ;547:     {


                     567 ;548:         return NULL;


                     568 ;549:     };


                     569 ;550:     pos = BerDecoder_decodeLength(iedModel, &descrLen, pos, iedModelSize);


                     570 ;551:     if (pos == -1)


                     571 ;552:     {


                     572 ;553:         return NULL;


                     573 ;554:     }


                     574 ;555: 


                     575 ;556:     //Получаем структуру описания с учётом выравнивания


                     576 ;557:     pDescrStructAlignOffset = iedModel + pos;


                     577 ;558:     RET_IF_NOT(*pDescrStructAlignOffset < 4, "Invalid alignment");


                     578 ;559:     return pDescrStructAlignOffset + *pDescrStructAlignOffset + 1;


                     579 ;560: }


                     580 ;561: 


                     581 ;562: int encodeTypeAccessAttrs(uint8_t* outBuf, int bufPos, enum InnerAttributeType type,


                     582 ;563:                     //Позиция константы или структуры для доступа к значению


                     583 ;564:                           int attrDataPos,


                     584 ;565:                           bool determineSize)


                     585 ;566: {


                     586 ;567:     switch(type)


                     587 ;568:     {


                     588 ;569:     case INNER_TYPE_CONST:


                     589 ;570:         return encodeAccessAttrConst(outBuf, bufPos, attrDataPos, determineSize);


                     590 ;571:     case INNER_TYPE_REAL_VALUE:


                     591 ;572:     case INNER_TYPE_REAL_SETT:


                     592 ;573:     case INNER_TYPE_FLOAT_VALUE:


                     593 ;574:     case INNER_TYPE_FLOAT_SETT:


                     594 ;575:         return encodeAccessAttrFloat(outBuf, bufPos, determineSize);


                     595 ;576:     case INNER_TYPE_QUALITY:


                     596 ;577:         return encodeAccessAttrQuality(outBuf, bufPos, determineSize);


                     597 ;578:     case INNER_TYPE_TIME_STAMP:


                     598 ;579:         return encodeAccessAttrTimeStamp(outBuf, bufPos, determineSize);


                     599 ;580:     case INNER_TYPE_INT32_SETTS:



                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
                     600 ;581:     case INNER_TYPE_INT32:


                     601 ;582:         return encodeAccessAttrInt(outBuf, bufPos, 32, determineSize);


                     602 ;583:     case INNER_TYPE_ENUMERATED:


                     603 ;584:     case INNER_TYPE_ENUMERATED_SETTS:


                     604 ;585:         return encodeAccessAttrInt(outBuf, bufPos, 8, determineSize);


                     605 ;586:     case INNER_TYPE_INT32U_SETTS:


                     606 ;587:     case INNER_TYPE_INT32U:


                     607 ;588:         return encodeAccessAttrUInt(outBuf, bufPos, 32, determineSize);


                     608 ;589:     case INNER_TYPE_INT8U:


                     609 ;590:         return encodeAccessAttrUInt(outBuf, bufPos, 8, determineSize);


                     610 ;591:     case INNER_TYPE_REAL_AS_INT64:


                     611 ;592:             return encodeAccessAttrInt(outBuf, bufPos, 64, determineSize);


                     612 ;593:     case INNER_TYPE_BOOLEAN:


                     613 ;594:         return encodeAccessAttrBoolean(outBuf, bufPos, determineSize);


                     614 ;595:     case INNER_TYPE_RCB:


                     615 ;596:         return encodeAccessAttrRCB(outBuf, bufPos, attrDataPos, determineSize);


                     616 ;597:     case INNER_TYPE_GOCB:


                     617 ;598:         return encodeAccessAttrGoCB(outBuf, bufPos, attrDataPos, determineSize);


                     618 ;599:     case INNER_TYPE_CODEDENUM:


                     619 ;600:         return encodeAccessAttrCodedEnum(outBuf, bufPos, attrDataPos,


                     620 ;601:             determineSize);


                     621 ;602:     default:


                     622 ;603:         ERROR_REPORT("Unsupported inner type %d", type);


                     623 ;604:         return 0;


                     624 ;605:     }


                     625 ;606: }


                     626 ;607: 


                     627 ;608: //Для GetValibaleAccessAttributes


                     628 ;609: //Пишет имя в буфер с тэгом 0х80


                     629 ;610: //или определяет размер


                     630 ;611: static int encodeNameAttr(uint8_t* outBuf, int bufPos, int namePos ,


                     631 ;612:                           bool determineSize)


                     632 ;613: {


                     633 ;614:     int totalLen;


                     634 ;615:     int len;


                     635 ;616:     int pos  = namePos;


                     636 ;617:     //пропускаем тэг


                     637 ;618:     pos++;


                     638 ;619:     //определяем и пропускаем длину


                     639 ;620:     pos = BerDecoder_decodeLength(iedModel, &len, pos, iedModelSize);


                     640 ;621:     if( pos <= 0)


                     641 ;622:     {


                     642 ;623:         return 0;


                     643 ;624:     }


                     644 ;625: 


                     645 ;626:     pos+=len;


                     646 ;627:     totalLen = pos - namePos;


                     647 ;628:     if(determineSize)


                     648 ;629:     {


                     649 ;630:         return totalLen;


                     650 ;631:     }


                     651 ;632:     memcpy(outBuf + bufPos, iedModel + namePos, totalLen);


                     652 ;633:     outBuf[bufPos] = 0x80;


                     653 ;634: 


                     654 ;635:     return bufPos + totalLen;


                     655 ;636: }


                     656 ;637: 


                     657 ;638: int encodeSimpleDataAccessAttrs(uint8_t* outBuf, int bufPos, int objectPos, bool determineSize,


                     658 ;639:                                 bool topStruct)


                     659 ;640: {


                     660 ;641:     uint8_t typeIdTag;



                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
                     661 ;642:     int typeIdLen;


                     662 ;643:     enum InnerAttributeType typeId;


                     663 ;644:     int typeDescrSize;


                     664 ;645:     int pos = objectPos;


                     665 ;646:     int objectSize;


                     666 ;647:     int sequenceSize;


                     667 ;648:     int totalSize;


                     668 ;649:     int objectNamePos;


                     669 ;650:     int nameLen;


                     670 ;651:     int daDataPos;


                     671 ;652:     //!!!


                     672 ;653:     int result;


                     673 ;654: 


                     674 ;655:     //======================Получаем иденитификатор типа============


                     675 ;656: 


                     676 ;657:     //Пропускаем тэг


                     677 ;658:     pos++;


                     678 ;659:     //Опеределяем длину


                     679 ;660:     pos = BerDecoder_decodeLength(iedModel, &objectSize, pos, iedModelSize);


                     680 ;661:     if(pos == -1)


                     681 ;662:     {


                     682 ;663:         return 0;


                     683 ;664:     }


                     684 ;665: 


                     685 ;666:     //Запоминаем положение имени и пропускаем


                     686 ;667:     objectNamePos = pos;


                     687 ;668:     pos = skipObject(pos);


                     688 ;669:     if (pos == 0)


                     689 ;670:     {


                     690 ;671:         return 0;


                     691 ;672:     }


                     692 ;673: 


                     693 ;674:     //Пропускаем прочую служебную информацию


                     694 ;675:     while(IEDModel_isServiceInfo(iedModel[pos]))


                     695 ;676:     {


                     696 ;677:         pos = skipObject(pos);


                     697 ;678:     }


                     698 ;679: 


                     699 ;680:     //Получаем идентификатор типа


                     700 ;681:     typeIdTag = iedModel[pos++];


                     701 ;682:     if(typeIdTag != ASN_INTEGER)


                     702 ;683:     {


                     703 ;684:         return 0;


                     704 ;685:     };


                     705 ;686:     typeIdLen = iedModel[pos++];


                     706 ;687:     //Получаем идентификатор типа


                     707 ;688:     typeId = (enum InnerAttributeType)BerDecoder_decodeUint32(iedModel, typeIdLen, pos);


                     708 ;689:     pos+=typeIdLen;


                     709 ;690:     daDataPos = pos;


                     710 ;691: 


                     711 ;692:     //===================== Определяем длину =========================


                     712 ;693:     typeDescrSize = encodeTypeAccessAttrs(outBuf, bufPos, typeId, daDataPos , TRUE);


                     713 ;694:     if(topStruct)


                     714 ;695:     {


                     715 ;696:         sequenceSize = typeDescrSize;


                     716 ;697:         //+Deletable size


                     717 ;698:         sequenceSize += 3;


                     718 ;699:     }


                     719 ;700:     else


                     720 ;701:     {


                     721 ;702:         sequenceSize = 1



                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
                     722 ;703:                 + BerEncoder_determineLengthSize(typeDescrSize)


                     723 ;704:                 + typeDescrSize;


                     724 ;705:         //+Name size


                     725 ;706:         nameLen = encodeNameAttr(NULL, 0, objectNamePos, TRUE);


                     726 ;707:         if(nameLen < 1)


                     727 ;708:         {


                     728 ;709:             return 0;


                     729 ;710:         }


                     730 ;711:         sequenceSize += nameLen;


                     731 ;712:     }


                     732 ;713: 


                     733 ;714: 


                     734 ;715:     if(determineSize)


                     735 ;716:     {


                     736 ;717:         totalSize = 1


                     737 ;718:                 + BerEncoder_determineLengthSize(sequenceSize)


                     738 ;719:                 + sequenceSize;


                     739 ;720:         return totalSize;


                     740 ;721:     }


                     741 ;722: 


                     742 ;723:     //=======================Пишем================================


                     743 ;724:     if(!topStruct)


                     744 ;725:     {


                     745 ;726:         //Если не topStruct пишем sequence


                     746 ;727:         bufPos = BerEncoder_encodeTL(ASN_SEQUENCE, sequenceSize,


                     747 ;728:                                      outBuf, bufPos);


                     748 ;729:         //Имя объекта


                     749 ;730:         bufPos = encodeNameAttr(outBuf, bufPos, objectNamePos, FALSE);


                     750 ;731:         //Тэг и размер описания типа


                     751 ;732:         bufPos = BerEncoder_encodeTL(0xA1, typeDescrSize, outBuf, bufPos);


                     752 ;733:     }


                     753 ;734:     else


                     754 ;735:     {


                     755 ;736:         //Deletable


                     756 ;737:         bufPos = BerEncoder_encodeBoolean(VAR_DELETABLE, FALSE, outBuf, bufPos);


                     757 ;738:         //Тэг и размер описания типа


                     758 ;739:         bufPos = BerEncoder_encodeTL(0xA2, typeDescrSize, outBuf, bufPos);


                     759 ;740:     }


                     760 ;741:     


                     761 ;742:     //Вызываем соответствующую типу функцию


                     762 ;743:     result = encodeTypeAccessAttrs(outBuf, bufPos, typeId, daDataPos, FALSE);


                     763 ;744:     return result;


                     764 ;745: }


                     765 ;746: 


                     766 ;747: int encodeObjectAccessAttrs(uint8_t* outBuf, int bufPos, int objectPos, bool determineSize,


                     767 ;748:                             bool topStruct)


                     768 ;749: {


                     769 ;750:     uint8_t tag = iedModel[objectPos];


                     770 ;751:     if(tag == IED_DA_FINAL)


                     771 ;752:     {


                     772 ;753:         return encodeSimpleDataAccessAttrs( outBuf, bufPos, objectPos, determineSize, topStruct);


                     773 ;754:     }


                     774 ;755:     else


                     775 ;756:     {


                     776 ;757:         return encodeStructAccessAttrs(outBuf, bufPos, objectPos, determineSize, topStruct);


                     777 ;758:     }


                     778 ;759: }


                     779 ;760: 


                     780 ;761: int encodeChildrenAccessAttrs(uint8_t* outBuf, int bufPos, int rootObjPos, bool determineSize)


                     781 ;762: {


                     782 ;763:     int totalSize = 0;



                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
                     783 ;764:     int objEndPos;


                     784 ;765:     int childObjectPos = getSubObjectsPos(rootObjPos, &objEndPos);


                     785 ;766:     if (childObjectPos == 0)


                     786 ;767:     {


                     787 ;768:         //Если ошибка, считаем что детей нет


                     788 ;769:         return 0;


                     789 ;770:     }


                     790 ;771:     while (childObjectPos < objEndPos)


                     791 ;772:     {


                     792 ;773:         int subObjLen;


                     793 ;774:         int objContentPos;


                     794 ;775:         int pos = childObjectPos;


                     795 ;776:         //Пропускаем тэг


                     796 ;777:         pos++;


                     797 ;778:         //Получаем размер подобъекта


                     798 ;779:         objContentPos = BerDecoder_decodeLength(iedModel, &subObjLen, pos,


                     799 ;780:             iedModelSize);


                     800 ;781:         if (objContentPos < 1)


                     801 ;782:         {


                     802 ;783:             //Если ошибка, считаем что больше детей нет


                     803 ;784:             return 0;


                     804 ;785:         }


                     805 ;786:         bufPos = encodeObjectAccessAttrs(outBuf, bufPos, childObjectPos, determineSize,


                     806 ;787:                                          FALSE);


                     807 ;788:         if (determineSize)


                     808 ;789:         {


                     809 ;790:             totalSize += bufPos;


                     810 ;791:         }


                     811 ;792: 


                     812 ;793:         childObjectPos = objContentPos + subObjLen;


                     813 ;794:     }


                     814 ;795: 


                     815 ;796:     if (determineSize)


                     816 ;797:     {


                     817 ;798:         return totalSize;


                     818 ;799:     }


                     819 ;800:     else


                     820 ;801:     {


                     821 ;802:         return bufPos;


                     822 ;803:     }


                     823 ;804: }


                     824 ;805: 


                     825 ;806: static int encodeStructAccessAttrs(uint8_t* outBuf, int bufPos, int objectPos, bool determineSize,


                     826 

                     827 ;906: }


                     828 

                     829 	.text

                     830 	.align	4

                     831 setIedModel::

00000000 e59f2884*   832 	ldr	r2,.L101

00000004 e5820000    833 	str	r0,[r2]

00000008 e59f0880*   834 	ldr	r0,.L102

0000000c e5801000    835 	str	r1,[r0]

00000010 e12fff1e*   836 	ret	

                     837 	.endf	setIedModel

                     838 	.align	4

                     839 

                     840 ;pModel	r0	param

                     841 ;modelSize	r1	param

                     842 

                     843 	.data


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
                     844 .L94:

                     845 	.text

                     846 

                     847 

                     848 	.align	4

                     849 	.align	4

                     850 readTL::

00000014 e92d4100    851 	stmfd	[sp]!,{r8,lr}

00000018 e59f8870*   852 	ldr	r8,.L102

0000001c e24dd00c    853 	sub	sp,sp,12

00000020 e598c000    854 	ldr	r12,[r8]

00000024 e59f8860*   855 	ldr	r8,.L101

00000028 e58dc008    856 	str	r12,[sp,8]

0000002c e598c000    857 	ldr	r12,[r8]

00000030 e1a0e000    858 	mov	lr,r0

00000034 e88d5000    859 	stmea	[sp],{r12,lr}

00000038 e1a0000d    860 	mov	r0,sp

0000003c eb000000*   861 	bl	BerDecoder_decodeTLFromBufferView

00000040 e3500000    862 	cmp	r0,0

00000044 159d0004    863 	ldrne	r0,[sp,4]

00000048 e28dd00c    864 	add	sp,sp,12

0000004c e8bd8100    865 	ldmfd	[sp]!,{r8,pc}

                     866 	.endf	readTL

                     867 	.align	4

                     868 ;bv	[sp]	local

                     869 

                     870 ;pos	r0	param

                     871 ;pTag	none	param

                     872 ;pLen	none	param

                     873 ;pFullLen	none	param

                     874 

                     875 	.section ".bss","awb"

                     876 .L149:

                     877 	.data

                     878 	.text

                     879 

                     880 

                     881 	.align	4

                     882 	.align	4

                     883 skipObject::

00000050 e92d4000    884 	stmfd	[sp]!,{lr}

00000054 e24dd004    885 	sub	sp,sp,4

00000058 e2802001    886 	add	r2,r0,1

0000005c e59f082c*   887 	ldr	r0,.L102

00000060 e59fc824*   888 	ldr	r12,.L101

00000064 e5903000    889 	ldr	r3,[r0]

00000068 e59c0000    890 	ldr	r0,[r12]

0000006c e1a0100d    891 	mov	r1,sp

00000070 eb000000*   892 	bl	BerDecoder_decodeLength

00000074 e3500000    893 	cmp	r0,0

00000078 c59d1000    894 	ldrgt	r1,[sp]

0000007c d3a00000    895 	movle	r0,0

00000080 c0800001    896 	addgt	r0,r0,r1

00000084 e28dd004    897 	add	sp,sp,4

00000088 e8bd8000    898 	ldmfd	[sp]!,{pc}

                     899 	.endf	skipObject

                     900 	.align	4

                     901 ;len	[sp]	local

                     902 

                     903 ;pos	r0	param

                     904 


                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
                     905 	.section ".bss","awb"

                     906 .L198:

                     907 	.data

                     908 	.text

                     909 

                     910 

                     911 	.align	4

                     912 	.align	4

                     913 getBerStringLength::

0000008c e92d4010    914 	stmfd	[sp]!,{r4,lr}

00000090 e2804001    915 	add	r4,r0,1

00000094 e1a02004    916 	mov	r2,r4

00000098 e24dd004    917 	sub	sp,sp,4

0000009c e59f07ec*   918 	ldr	r0,.L102

000000a0 e59fc7e4*   919 	ldr	r12,.L101

000000a4 e5903000    920 	ldr	r3,[r0]

000000a8 e59c0000    921 	ldr	r0,[r12]

000000ac e1a0100d    922 	mov	r1,sp

000000b0 eb000000*   923 	bl	BerDecoder_decodeLength

000000b4 e3500000    924 	cmp	r0,0

000000b8 d3a00000    925 	movle	r0,0

000000bc c59d1000    926 	ldrgt	r1,[sp]

000000c0 c0400004    927 	subgt	r0,r0,r4

000000c4 c0811000    928 	addgt	r1,r1,r0

000000c8 c2810001    929 	addgt	r0,r1,1

000000cc e28dd004    930 	add	sp,sp,4

000000d0 e8bd8010    931 	ldmfd	[sp]!,{r4,pc}

                     932 	.endf	getBerStringLength

                     933 	.align	4

                     934 ;length	[sp]	local

                     935 ;newPos	r0	local

                     936 

                     937 ;berStringPos	r4	param

                     938 

                     939 	.section ".bss","awb"

                     940 .L246:

                     941 	.data

                     942 	.text

                     943 

                     944 

                     945 	.align	4

                     946 	.align	4

                     947 getIEDObjectNameString::

000000d4 e92d40f0    948 	stmfd	[sp]!,{r4-r7,lr}

000000d8 e24dd004    949 	sub	sp,sp,4

000000dc e1a06000    950 	mov	r6,r0

000000e0 e5960000    951 	ldr	r0,[r6]

000000e4 e1a07001    952 	mov	r7,r1

000000e8 e2802001    953 	add	r2,r0,1

000000ec e59f079c*   954 	ldr	r0,.L102

000000f0 e59fc794*   955 	ldr	r12,.L101

000000f4 e5903000    956 	ldr	r3,[r0]

000000f8 e59c0000    957 	ldr	r0,[r12]

000000fc e1a0100d    958 	mov	r1,sp

00000100 eb000000*   959 	bl	BerDecoder_decodeLength

00000104 e2505000    960 	subs	r5,r0,0

00000108 da000002    961 	ble	.L265

0000010c ebffffde*   962 	bl	getBerStringLength

00000110 e1b04000    963 	movs	r4,r0

00000114 1a000001    964 	bne	.L264

                     965 .L265:


                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
00000118 e3a00000    966 	mov	r0,0

0000011c ea00000b    967 	b	.L259

                     968 .L264:

00000120 e3570000    969 	cmp	r7,0

00000124 0a000005    970 	beq	.L267

00000128 e59f175c*   971 	ldr	r1,.L101

0000012c e5910000    972 	ldr	r0,[r1]

00000130 e1a02004    973 	mov	r2,r4

00000134 e0851000    974 	add	r1,r5,r0

00000138 e1a00007    975 	mov	r0,r7

0000013c eb000000*   976 	bl	memcpy

                     977 .L267:

00000140 e59d0000    978 	ldr	r0,[sp]

00000144 e0800005    979 	add	r0,r0,r5

00000148 e5860000    980 	str	r0,[r6]

0000014c e1a00004    981 	mov	r0,r4

                     982 .L259:

00000150 e28dd004    983 	add	sp,sp,4

00000154 e8bd80f0    984 	ldmfd	[sp]!,{r4-r7,pc}

                     985 	.endf	getIEDObjectNameString

                     986 	.align	4

                     987 ;objLen	[sp]	local

                     988 ;nameLen	r4	local

                     989 ;namePos	r5	local

                     990 ;pos	r0	local

                     991 

                     992 ;pObjPos	r6	param

                     993 ;buf	r7	param

                     994 

                     995 	.section ".bss","awb"

                     996 .L338:

                     997 	.data

                     998 	.text

                     999 

                    1000 

                    1001 	.align	4

                    1002 	.align	4

                    1003 getObjectName::

00000158 e92d4010   1004 	stmfd	[sp]!,{r4,lr}

0000015c e24dd00c   1005 	sub	sp,sp,12

00000160 e28d2008   1006 	add	r2,sp,8

00000164 e1a04001   1007 	mov	r4,r1

00000168 e3a03000   1008 	mov	r3,0

0000016c e1a01003   1009 	mov	r1,r3

00000170 ebffffa7*  1010 	bl	readTL

00000174 e3500000   1011 	cmp	r0,0

00000178 159d1008   1012 	ldrne	r1,[sp,8]

0000017c 13510000   1013 	cmpne	r1,0

00000180 0a000008   1014 	beq	.L364

00000184 e28d2004   1015 	add	r2,sp,4

00000188 e28d1003   1016 	add	r1,sp,3

0000018c e3a03000   1017 	mov	r3,0

00000190 ebffff9f*  1018 	bl	readTL

00000194 e3500000   1019 	cmp	r0,0

00000198 0a000002   1020 	beq	.L364

0000019c e5dd1003   1021 	ldrb	r1,[sp,3]

000001a0 e351001a   1022 	cmp	r1,26

000001a4 0a000001   1023 	beq	.L363

                    1024 .L364:

000001a8 e3a00000   1025 	mov	r0,0

000001ac ea000006   1026 	b	.L355


                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
                    1027 .L363:

000001b0 e59f36d4*  1028 	ldr	r3,.L101

000001b4 e5931000   1029 	ldr	r1,[r3]

000001b8 e59d2004   1030 	ldr	r2,[sp,4]

000001bc e0801001   1031 	add	r1,r0,r1

000001c0 e1a00004   1032 	mov	r0,r4

000001c4 eb000000*  1033 	bl	StringView_init

000001c8 e3a00001   1034 	mov	r0,1

                    1035 .L355:

000001cc e28dd00c   1036 	add	sp,sp,12

000001d0 e8bd8010   1037 	ldmfd	[sp]!,{r4,pc}

                    1038 	.endf	getObjectName

                    1039 	.align	4

                    1040 ;nameTag	[sp,3]	local

                    1041 ;nameLen	[sp,4]	local

                    1042 ;namePos	r0	local

                    1043 ;objectLen	[sp,8]	local

                    1044 ;pos	r0	local

                    1045 

                    1046 ;objPos	none	param

                    1047 ;result	r4	param

                    1048 

                    1049 	.section ".bss","awb"

                    1050 .L430:

                    1051 	.data

                    1052 	.text

                    1053 

                    1054 

                    1055 	.align	4

                    1056 	.align	4

                    1057 IEDModel_isServiceInfo::

000001d4 e350001a   1058 	cmp	r0,26

000001d8 135000f0   1059 	cmpne	r0,240

000001dc 135000f1   1060 	cmpne	r0,241

000001e0 135000c0   1061 	cmpne	r0,192

000001e4 e3a00001   1062 	mov	r0,1

000001e8 13a00000   1063 	movne	r0,0

000001ec e20000ff   1064 	and	r0,r0,255

000001f0 e12fff1e*  1065 	ret	

                    1066 	.endf	IEDModel_isServiceInfo

                    1067 	.align	4

                    1068 

                    1069 ;tag	r1	param

                    1070 

                    1071 	.section ".bss","awb"

                    1072 .L522:

                    1073 	.data

                    1074 	.text

                    1075 

                    1076 

                    1077 	.align	4

                    1078 	.align	4

                    1079 getSubObjectsPos::

000001f4 e92d4070   1080 	stmfd	[sp]!,{r4-r6,lr}

000001f8 e1a06001   1081 	mov	r6,r1

000001fc e24dd004   1082 	sub	sp,sp,4

00000200 e2802001   1083 	add	r2,r0,1

00000204 e59f0684*  1084 	ldr	r0,.L102

00000208 e59fc67c*  1085 	ldr	r12,.L101

0000020c e5903000   1086 	ldr	r3,[r0]

00000210 e59c0000   1087 	ldr	r0,[r12]


                                                                      Page 19
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
00000214 e1a0100d   1088 	mov	r1,sp

00000218 eb000000*  1089 	bl	BerDecoder_decodeLength

0000021c e2504000   1090 	subs	r4,r0,0

00000220 da000005   1091 	ble	.L544

00000224 e59f1664*  1092 	ldr	r1,.L102

00000228 e59d5000   1093 	ldr	r5,[sp]

0000022c e5910000   1094 	ldr	r0,[r1]

00000230 e0855004   1095 	add	r5,r5,r4

00000234 e1550000   1096 	cmp	r5,r0

00000238 da000001   1097 	ble	.L543

                    1098 .L544:

0000023c e3a00000   1099 	mov	r0,0

00000240 ea00000f   1100 	b	.L538

                    1101 .L543:

00000244 e5865000   1102 	str	r5,[r6]

00000248 e1540005   1103 	cmp	r4,r5

0000024c aa00000b   1104 	bge	.L550

00000250 e59f6634*  1105 	ldr	r6,.L101

                    1106 .L551:

00000254 e5960000   1107 	ldr	r0,[r6]

00000258 e7d00004   1108 	ldrb	r0,[r0,r4]

0000025c ebffffdc*  1109 	bl	IEDModel_isServiceInfo

00000260 e3500000   1110 	cmp	r0,0

00000264 0a000005   1111 	beq	.L550

00000268 e1a00004   1112 	mov	r0,r4

0000026c ebffff77*  1113 	bl	skipObject

00000270 e1b04000   1114 	movs	r4,r0

00000274 0afffff0   1115 	beq	.L544

00000278 e1540005   1116 	cmp	r4,r5

0000027c bafffff4   1117 	blt	.L551

                    1118 .L550:

00000280 e1a00004   1119 	mov	r0,r4

                    1120 .L538:

00000284 e28dd004   1121 	add	sp,sp,4

00000288 e8bd8070   1122 	ldmfd	[sp]!,{r4-r6,pc}

                    1123 	.endf	getSubObjectsPos

                    1124 	.align	4

                    1125 ;endPos	r5	local

                    1126 ;rootObjLen	[sp]	local

                    1127 ;pos	r4	local

                    1128 

                    1129 ;rootObjPos	r0	param

                    1130 ;pEndPos	r6	param

                    1131 

                    1132 	.section ".bss","awb"

                    1133 .L705:

                    1134 	.data

                    1135 	.text

                    1136 

                    1137 

                    1138 	.align	4

                    1139 	.align	4

                    1140 findObjectBySimpleName::

0000028c e92d4cf0   1141 	stmfd	[sp]!,{r4-r7,r10-fp,lr}

00000290 e1a0a002   1142 	mov	r10,r2

00000294 e24dd00c   1143 	sub	sp,sp,12

00000298 e1a0b001   1144 	mov	fp,r1

0000029c e1a0100d   1145 	mov	r1,sp

000002a0 ebffffd3*  1146 	bl	getSubObjectsPos

000002a4 e1b04000   1147 	movs	r4,r0

000002a8 0a000026   1148 	beq	.L743


                                                                      Page 20
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
000002ac e59f65d8*  1149 	ldr	r6,.L101

000002b0 e59d0000   1150 	ldr	r0,[sp]

000002b4 e59f75d4*  1151 	ldr	r7,.L102

000002b8 e1540000   1152 	cmp	r4,r0

000002bc aa000021   1153 	bge	.L743

                    1154 .L744:

000002c0 e2842001   1155 	add	r2,r4,1

000002c4 e5973000   1156 	ldr	r3,[r7]

000002c8 e5960000   1157 	ldr	r0,[r6]

000002cc e28d1008   1158 	add	r1,sp,8

000002d0 eb000000*  1159 	bl	BerDecoder_decodeLength

000002d4 e3500000   1160 	cmp	r0,0

000002d8 da00001a   1161 	ble	.L743

000002dc e596c000   1162 	ldr	r12,[r6]

000002e0 e7dc1000   1163 	ldrb	r1,[r12,r0]

000002e4 e351001a   1164 	cmp	r1,26

000002e8 1a000016   1165 	bne	.L743

000002ec e5973000   1166 	ldr	r3,[r7]

000002f0 e59d5008   1167 	ldr	r5,[sp,8]

000002f4 e28d1004   1168 	add	r1,sp,4

000002f8 e0855000   1169 	add	r5,r5,r0

000002fc e2802001   1170 	add	r2,r0,1

00000300 e1a0000c   1171 	mov	r0,r12

00000304 eb000000*  1172 	bl	BerDecoder_decodeLength

00000308 e3500000   1173 	cmp	r0,0

0000030c da00000d   1174 	ble	.L743

00000310 e5961000   1175 	ldr	r1,[r6]

00000314 e59d2004   1176 	ldr	r2,[sp,4]

00000318 e0801001   1177 	add	r1,r0,r1

0000031c e1a0000b   1178 	mov	r0,fp

00000320 eb000000*  1179 	bl	memcmp

00000324 e59d1004   1180 	ldr	r1,[sp,4]

00000328 e151000a   1181 	cmp	r1,r10

0000032c 03500000   1182 	cmpeq	r0,0

00000330 01a00004   1183 	moveq	r0,r4

00000334 0a000004   1184 	beq	.L737

00000338 e59d0000   1185 	ldr	r0,[sp]

0000033c e1a04005   1186 	mov	r4,r5

00000340 e1540000   1187 	cmp	r4,r0

00000344 baffffdd   1188 	blt	.L744

                    1189 .L743:

00000348 e3a00000   1190 	mov	r0,0

                    1191 .L737:

0000034c e28dd00c   1192 	add	sp,sp,12

00000350 e8bd8cf0   1193 	ldmfd	[sp]!,{r4-r7,r10-fp,pc}

                    1194 	.endf	findObjectBySimpleName

                    1195 	.align	4

                    1196 ;objPos	r4	local

                    1197 ;endPos	[sp]	local

                    1198 ;nameCompareResult	r0	local

                    1199 ;nameLen	[sp,4]	local

                    1200 ;objLen	[sp,8]	local

                    1201 ;namePos	r0	local

                    1202 ;nextObjPos	r5	local

                    1203 

                    1204 ;rootObjPos	none	param

                    1205 ;name	fp	param

                    1206 ;argNameLen	r10	param

                    1207 

                    1208 	.section ".bss","awb"

                    1209 .L900:


                                                                      Page 21
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
                    1210 	.data

                    1211 	.text

                    1212 

                    1213 

                    1214 	.align	4

                    1215 	.align	4

                    1216 findObjectByTag::

00000354 e92d40f0   1217 	stmfd	[sp]!,{r4-r7,lr}

00000358 e24dd008   1218 	sub	sp,sp,8

0000035c e1a07001   1219 	mov	r7,r1

00000360 e1a0100d   1220 	mov	r1,sp

00000364 ebffffa2*  1221 	bl	getSubObjectsPos

00000368 e1b01000   1222 	movs	r1,r0

0000036c 0a000014   1223 	beq	.L940

00000370 e59f4514*  1224 	ldr	r4,.L101

00000374 e59f5514*  1225 	ldr	r5,.L102

00000378 e59d0000   1226 	ldr	r0,[sp]

0000037c e28d6004   1227 	add	r6,sp,4

00000380 e1510000   1228 	cmp	r1,r0

00000384 aa00000e   1229 	bge	.L940

                    1230 .L941:

00000388 e594c000   1231 	ldr	r12,[r4]

0000038c e1a00001   1232 	mov	r0,r1

00000390 e7dc1000   1233 	ldrb	r1,[r12,r0]

00000394 e1510007   1234 	cmp	r1,r7

00000398 0a00000a   1235 	beq	.L934

0000039c e5953000   1236 	ldr	r3,[r5]

000003a0 e1a01006   1237 	mov	r1,r6

000003a4 e2802001   1238 	add	r2,r0,1

000003a8 e1a0000c   1239 	mov	r0,r12

000003ac eb000000*  1240 	bl	BerDecoder_decodeLength

000003b0 e3500000   1241 	cmp	r0,0

000003b4 c89d000c   1242 	ldmgtfd	[sp],{r2-r3}

000003b8 c0831000   1243 	addgt	r1,r3,r0

000003bc c0710002   1244 	rsbgts	r0,r1,r2

000003c0 cafffff0   1245 	bgt	.L941

                    1246 .L940:

000003c4 e3a00000   1247 	mov	r0,0

                    1248 .L934:

000003c8 e28dd008   1249 	add	sp,sp,8

000003cc e8bd80f0   1250 	ldmfd	[sp]!,{r4-r7,pc}

                    1251 	.endf	findObjectByTag

                    1252 	.align	4

                    1253 ;objPos	r1	local

                    1254 ;endPos	[sp]	local

                    1255 ;objLen	[sp,4]	local

                    1256 ;pos	r0	local

                    1257 ;tag	r1	local

                    1258 

                    1259 ;rootObjPos	none	param

                    1260 ;tagToFind	r7	param

                    1261 

                    1262 	.section ".bss","awb"

                    1263 .L1044:

                    1264 	.data

                    1265 	.text

                    1266 

                    1267 

                    1268 	.align	4

                    1269 	.align	4

                    1270 findDomainSection::


                                                                      Page 22
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
000003d0 e92d4010   1271 	stmfd	[sp]!,{r4,lr}

000003d4 e1a04000   1272 	mov	r4,r0

000003d8 e3a00000   1273 	mov	r0,0

000003dc ebffffaa*  1274 	bl	findObjectBySimpleName

000003e0 e1b01000   1275 	movs	r1,r0

000003e4 120410ff   1276 	andne	r1,r4,255

000003e8 18bd4010   1277 	ldmnefd	[sp]!,{r4,lr}

000003ec 1affffd8*  1278 	bne	findObjectByTag

000003f0 e8bd8010   1279 	ldmfd	[sp]!,{r4,pc}

                    1280 	.endf	findDomainSection

                    1281 	.align	4

                    1282 ;vmdPos	r1	local

                    1283 

                    1284 ;section	r4	param

                    1285 ;domainId	none	param

                    1286 ;domainIdLen	none	param

                    1287 

                    1288 	.section ".bss","awb"

                    1289 .L1125:

                    1290 	.data

                    1291 	.text

                    1292 

                    1293 

                    1294 	.align	4

                    1295 	.align	4

                    1296 getSimpleNameLen::

000003f4 e92d0030   1297 	stmfd	[sp]!,{r4-r5}

000003f8 e3a04001   1298 	mov	r4,1

000003fc e3a03000   1299 	mov	r3,0

00000400 e5c23000   1300 	strb	r3,[r2]

00000404 e3510000   1301 	cmp	r1,0

00000408 a1a0c001   1302 	movge	r12,r1

0000040c b3a0c000   1303 	movlt	r12,0

00000410 e1b011ac   1304 	movs	r1,r12 lsr 3

00000414 0a00001a   1305 	beq	.L1179

                    1306 .L1180:

00000418 e7d05003   1307 	ldrb	r5,[r0,r3]

0000041c e3550024   1308 	cmp	r5,36

00000420 12833001   1309 	addne	r3,r3,1

00000424 17d05003   1310 	ldrneb	r5,[r0,r3]

00000428 13550024   1311 	cmpne	r5,36

0000042c 12833001   1312 	addne	r3,r3,1

00000430 17d05003   1313 	ldrneb	r5,[r0,r3]

00000434 13550024   1314 	cmpne	r5,36

00000438 12833001   1315 	addne	r3,r3,1

0000043c 17d05003   1316 	ldrneb	r5,[r0,r3]

00000440 13550024   1317 	cmpne	r5,36

00000444 12833001   1318 	addne	r3,r3,1

00000448 17d05003   1319 	ldrneb	r5,[r0,r3]

0000044c 13550024   1320 	cmpne	r5,36

00000450 12833001   1321 	addne	r3,r3,1

00000454 17d05003   1322 	ldrneb	r5,[r0,r3]

00000458 13550024   1323 	cmpne	r5,36

0000045c 12833001   1324 	addne	r3,r3,1

00000460 17d05003   1325 	ldrneb	r5,[r0,r3]

00000464 13550024   1326 	cmpne	r5,36

00000468 12833001   1327 	addne	r3,r3,1

0000046c 17d05003   1328 	ldrneb	r5,[r0,r3]

00000470 13550024   1329 	cmpne	r5,36

00000474 0a000006   1330 	beq	.L1215

00000478 e2833001   1331 	add	r3,r3,1


                                                                      Page 23
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
0000047c e2511001   1332 	subs	r1,r1,1

00000480 1affffe4   1333 	bne	.L1180

                    1334 .L1179:

00000484 e21c1007   1335 	ands	r1,r12,7

00000488 0a000006   1336 	beq	.L1139

                    1337 .L1214:

0000048c e7d0c003   1338 	ldrb	r12,[r0,r3]

00000490 e35c0024   1339 	cmp	r12,36

                    1340 .L1215:

00000494 05c24000   1341 	streqb	r4,[r2]

00000498 0a000002   1342 	beq	.L1139

                    1343 .L1217:

0000049c e2833001   1344 	add	r3,r3,1

000004a0 e2511001   1345 	subs	r1,r1,1

000004a4 1afffff8   1346 	bne	.L1214

                    1347 .L1139:

000004a8 e1a00003   1348 	mov	r0,r3

000004ac e8bd0030   1349 	ldmfd	[sp]!,{r4-r5}

000004b0 e12fff1e*  1350 	ret	

                    1351 	.endf	getSimpleNameLen

                    1352 	.align	4

                    1353 ;len	r3	local

                    1354 

                    1355 ;name	r0	param

                    1356 ;fullNameLen	r1	param

                    1357 ;delimiterFound	r2	param

                    1358 

                    1359 	.section ".bss","awb"

                    1360 .L1464:

                    1361 	.data

                    1362 	.text

                    1363 

                    1364 

                    1365 	.align	4

                    1366 	.align	4

                    1367 findObjectByPath::

000004b4 e92d44f0   1368 	stmfd	[sp]!,{r4-r7,r10,lr}

000004b8 e1a05001   1369 	mov	r5,r1

000004bc e1b06002   1370 	movs	r6,r2

000004c0 e1a04000   1371 	mov	r4,r0

000004c4 e24dd004   1372 	sub	sp,sp,4

000004c8 e28da003   1373 	add	r10,sp,3

000004cc 0a000010   1374 	beq	.L1508

                    1375 .L1509:

000004d0 e1a0200a   1376 	mov	r2,r10

000004d4 e1a01006   1377 	mov	r1,r6

000004d8 e1a00005   1378 	mov	r0,r5

000004dc ebffffc4*  1379 	bl	getSimpleNameLen

000004e0 e1a07000   1380 	mov	r7,r0

000004e4 e1a02007   1381 	mov	r2,r7

000004e8 e1a01005   1382 	mov	r1,r5

000004ec e1a00004   1383 	mov	r0,r4

000004f0 ebffff65*  1384 	bl	findObjectBySimpleName

000004f4 e1b04000   1385 	movs	r4,r0

000004f8 0a000005   1386 	beq	.L1508

000004fc e5dd0003   1387 	ldrb	r0,[sp,3]

00000500 e3500000   1388 	cmp	r0,0

00000504 12877001   1389 	addne	r7,r7,1

00000508 e0855007   1390 	add	r5,r5,r7

0000050c e0566007   1391 	subs	r6,r6,r7

00000510 1affffee   1392 	bne	.L1509


                                                                      Page 24
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
                    1393 .L1508:

00000514 e1a00004   1394 	mov	r0,r4

00000518 e28dd004   1395 	add	sp,sp,4

0000051c e8bd84f0   1396 	ldmfd	[sp]!,{r4-r7,r10,pc}

                    1397 	.endf	findObjectByPath

                    1398 	.align	4

                    1399 ;objPos	r4	local

                    1400 ;delimiterfound	[sp,3]	local

                    1401 ;simpleNameLen	r7	local

                    1402 

                    1403 ;rootObjPos	r0	param

                    1404 ;name	r5	param

                    1405 ;fullNameLen	r6	param

                    1406 

                    1407 	.section ".bss","awb"

                    1408 .L1594:

                    1409 	.data

                    1410 	.text

                    1411 

                    1412 

                    1413 	.align	4

                    1414 	.align	4

                    1415 findObjectByFullName::

00000520 e92d4010   1416 	stmfd	[sp]!,{r4,lr}

00000524 e1a04002   1417 	mov	r4,r2

00000528 e891000c   1418 	ldmfd	[r1],{r2-r3}

0000052c e1a01003   1419 	mov	r1,r3

00000530 ebffffa6*  1420 	bl	findDomainSection

00000534 e1b03000   1421 	movs	r3,r0

00000538 0a000004   1422 	beq	.L1617

0000053c e894000c   1423 	ldmfd	[r4],{r2-r3}

00000540 e1a01003   1424 	mov	r1,r3

00000544 ebffffda*  1425 	bl	findObjectByPath

00000548 e3500000   1426 	cmp	r0,0

0000054c 03a00000   1427 	moveq	r0,0

                    1428 .L1617:

00000550 e8bd8010   1429 	ldmfd	[sp]!,{r4,pc}

                    1430 	.endf	findObjectByFullName

                    1431 	.align	4

                    1432 ;sectionPos	r3	local

                    1433 

                    1434 ;section	none	param

                    1435 ;domainName	r1	param

                    1436 ;objectName	r4	param

                    1437 

                    1438 	.section ".bss","awb"

                    1439 .L1670:

                    1440 	.data

                    1441 	.text

                    1442 

                    1443 

                    1444 	.align	4

                    1445 	.align	4

                    1446 getDataSetByPath::

00000554 e92d4000   1447 	stmfd	[sp]!,{lr}

00000558 e24dd010   1448 	sub	sp,sp,16

0000055c e1a0300d   1449 	mov	r3,sp

00000560 e28d2008   1450 	add	r2,sp,8

00000564 e3a0102f   1451 	mov	r1,47

00000568 eb000000*  1452 	bl	StringView_splitChar

0000056c e3500000   1453 	cmp	r0,0


                                                                      Page 25
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
00000570 0a000005   1454 	beq	.L1684

00000574 e1a0200d   1455 	mov	r2,sp

00000578 e28d1008   1456 	add	r1,sp,8

0000057c e3a000ee   1457 	mov	r0,238

00000580 ebffffe6*  1458 	bl	findObjectByFullName

00000584 e3500000   1459 	cmp	r0,0

00000588 03a00000   1460 	moveq	r0,0

                    1461 .L1684:

0000058c e28dd010   1462 	add	sp,sp,16

00000590 e8bd8000   1463 	ldmfd	[sp]!,{pc}

                    1464 	.endf	getDataSetByPath

                    1465 	.align	4

                    1466 ;domainName	[sp,8]	local

                    1467 ;objectName	[sp]	local

                    1468 

                    1469 ;pDatasetPath	none	param

                    1470 

                    1471 	.section ".bss","awb"

                    1472 .L1734:

                    1473 	.data

                    1474 	.text

                    1475 

                    1476 

                    1477 	.align	4

                    1478 	.align	4

                    1479 writeChildrenNames::

00000594 e92d4df0   1480 	stmfd	[sp]!,{r4-r8,r10-fp,lr}

00000598 e24dd014   1481 	sub	sp,sp,20

0000059c e5cd2003   1482 	strb	r2,[sp,3]

000005a0 e1a07003   1483 	mov	r7,r3

000005a4 e1a05001   1484 	mov	r5,r1

000005a8 e28d1004   1485 	add	r1,sp,4

000005ac ebffff10*  1486 	bl	getSubObjectsPos

000005b0 e1b04000   1487 	movs	r4,r0

000005b4 0a000033   1488 	beq	.L1748

000005b8 e59f62cc*  1489 	ldr	r6,.L101

000005bc e59fa2cc*  1490 	ldr	r10,.L102

000005c0 e2971001   1491 	adds	r1,r7,1

000005c4 13a01001   1492 	movne	r1,1

000005c8 e59d0004   1493 	ldr	r0,[sp,4]

000005cc e1a08001   1494 	mov	r8,r1

000005d0 e1540000   1495 	cmp	r4,r0

000005d4 aa00002b   1496 	bge	.L1748

                    1497 .L1755:

000005d8 e5960000   1498 	ldr	r0,[r6]

000005dc e2842001   1499 	add	r2,r4,1

000005e0 e7d01004   1500 	ldrb	r1,[r0,r4]

000005e4 e59a3000   1501 	ldr	r3,[r10]

000005e8 e5cd1002   1502 	strb	r1,[sp,2]

000005ec e28d1008   1503 	add	r1,sp,8

000005f0 eb000000*  1504 	bl	BerDecoder_decodeLength

000005f4 e2501000   1505 	subs	r1,r0,0

000005f8 e1a0b001   1506 	mov	fp,r1

000005fc da000021   1507 	ble	.L1748

00000600 e5960000   1508 	ldr	r0,[r6]

00000604 e7d03001   1509 	ldrb	r3,[r0,r1]

00000608 e2811001   1510 	add	r1,r1,1

0000060c e353001a   1511 	cmp	r3,26

00000610 1a00001c   1512 	bne	.L1748

00000614 e59a3000   1513 	ldr	r3,[r10]

00000618 e1a02001   1514 	mov	r2,r1


                                                                      Page 26
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
0000061c e28d100c   1515 	add	r1,sp,12

00000620 eb000000*  1516 	bl	BerDecoder_decodeLength

00000624 e2501000   1517 	subs	r1,r0,0

00000628 da000016   1518 	ble	.L1748

0000062c e5960000   1519 	ldr	r0,[r6]

00000630 e59d200c   1520 	ldr	r2,[sp,12]

00000634 e0811000   1521 	add	r1,r1,r0

00000638 e1a00005   1522 	mov	r0,r5

0000063c eb000000*  1523 	bl	DomainNameWriter_pushName

00000640 e2581000   1524 	subs	r1,r8,0

00000644 15dd1002   1525 	ldrneb	r1,[sp,2]

00000648 11570001   1526 	cmpne	r7,r1

0000064c 01a00005   1527 	moveq	r0,r5

00000650 0b000000*  1528 	bleq	DomainNameWriter_encode

00000654 e5dd1003   1529 	ldrb	r1,[sp,3]

00000658 e3510000   1530 	cmp	r1,0

0000065c 11a03007   1531 	movne	r3,r7

00000660 11a01005   1532 	movne	r1,r5

00000664 11a00004   1533 	movne	r0,r4

00000668 13a02001   1534 	movne	r2,1

0000066c 1bffffc8*  1535 	blne	writeChildrenNames

00000670 e1a00005   1536 	mov	r0,r5

00000674 eb000000*  1537 	bl	DomainNameWriter_discardName

00000678 e99d0011   1538 	ldmed	[sp],{r0,r4}

0000067c e084400b   1539 	add	r4,r4,fp

00000680 e1540000   1540 	cmp	r4,r0

00000684 baffffd3   1541 	blt	.L1755

                    1542 .L1748:

00000688 e28dd014   1543 	add	sp,sp,20

0000068c e8bd8df0   1544 	ldmfd	[sp]!,{r4-r8,r10-fp,pc}

                    1545 	.endf	writeChildrenNames

                    1546 	.align	4

                    1547 ;objEndPos	[sp,4]	local

                    1548 ;childObjectPos	r4	local

                    1549 ;subObjLen	[sp,8]	local

                    1550 ;nameLen	[sp,12]	local

                    1551 ;namePos	r1	local

                    1552 ;objContentPos	fp	local

                    1553 ;tag	[sp,2]	local

                    1554 

                    1555 ;rootObjPos	none	param

                    1556 ;writer	r5	param

                    1557 ;recursive	[sp,3]	param

                    1558 ;objectsTagToWrite	r7	param

                    1559 

                    1560 	.section ".bss","awb"

                    1561 .L1880:

                    1562 	.data

                    1563 	.text

                    1564 

                    1565 

                    1566 	.align	4

                    1567 	.align	4

                    1568 encodeReadConst::

00000690 e92d44f0   1569 	stmfd	[sp]!,{r4-r7,r10,lr}

00000694 e1a07002   1570 	mov	r7,r2

00000698 e2872001   1571 	add	r2,r7,1

0000069c e1a05001   1572 	mov	r5,r1

000006a0 e24dd004   1573 	sub	sp,sp,4

000006a4 e1a0a003   1574 	mov	r10,r3

000006a8 e1a06000   1575 	mov	r6,r0


                                                                      Page 27
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
000006ac e59f01dc*  1576 	ldr	r0,.L102

000006b0 e59fc1d4*  1577 	ldr	r12,.L101

000006b4 e5903000   1578 	ldr	r3,[r0]

000006b8 e59c0000   1579 	ldr	r0,[r12]

000006bc e1a0100d   1580 	mov	r1,sp

000006c0 eb000000*  1581 	bl	BerDecoder_decodeLength

000006c4 e59d4000   1582 	ldr	r4,[sp]

000006c8 e0400007   1583 	sub	r0,r0,r7

000006cc e0844000   1584 	add	r4,r4,r0

000006d0 e35a0000   1585 	cmp	r10,0

000006d4 11a00004   1586 	movne	r0,r4

000006d8 1a000045   1587 	bne	.L1914

000006dc e59f11a8*  1588 	ldr	r1,.L101

000006e0 e5910000   1589 	ldr	r0,[r1]

000006e4 e1a02004   1590 	mov	r2,r4

000006e8 e0801007   1591 	add	r1,r0,r7

000006ec e0850006   1592 	add	r0,r5,r6

000006f0 eb000000*  1593 	bl	memcpy

000006f4 e7d6c005   1594 	ldrb	r12,[r6,r5]

000006f8 e20c00c0   1595 	and	r0,r12,192

000006fc e3500080   1596 	cmp	r0,128

00000700 13a00000   1597 	movne	r0,0

00000704 1a00003a   1598 	bne	.L1914

00000708 e20cc03f   1599 	and	r12,r12,63

0000070c e25cc00c   1600 	subs	r12,r12,12

00000710 2a000012   1601 	bhs	.L2043

00000714 e29cc002   1602 	adds	r12,r12,2

00000718 03a00087   1603 	moveq	r0,135

0000071c 07c60005   1604 	streqb	r0,[r6,r5]

00000720 00840005   1605 	addeq	r0,r4,r5

00000724 0a000032   1606 	beq	.L1914

00000728 e29cc001   1607 	adds	r12,r12,1

0000072c 0a000025   1608 	beq	.L1926

00000730 e29cc001   1609 	adds	r12,r12,1

00000734 e29cc002   1610 	adds	r12,r12,2

00000738 2a000022   1611 	bhs	.L1926

0000073c e29cc001   1612 	adds	r12,r12,1

00000740 e29cc002   1613 	adds	r12,r12,2

00000744 2a00001b   1614 	bhs	.L1925

00000748 e29cc003   1615 	adds	r12,r12,3

0000074c 03a00083   1616 	moveq	r0,131

00000750 07c60005   1617 	streqb	r0,[r6,r5]

00000754 00840005   1618 	addeq	r0,r4,r5

00000758 0a000025   1619 	beq	.L1914

0000075c ea000021   1620 	b	.L1933

                    1621 .L2043:

                    1622 

00000760 e25cc000   1623 	subs	r12,r12,0

00000764 0a000013   1624 	beq	.L1925

00000768 e25cc002   1625 	subs	r12,r12,2

0000076c 93a00089   1626 	movls	r0,137

00000770 97c60005   1627 	strlsb	r0,[r6,r5]

00000774 90840005   1628 	addls	r0,r4,r5

00000778 9a00001d   1629 	bls	.L1914

0000077c e25cc007   1630 	subs	r12,r12,7

00000780 03a00090   1631 	moveq	r0,144

00000784 07c60005   1632 	streqb	r0,[r6,r5]

00000788 00840005   1633 	addeq	r0,r4,r5

0000078c 0a000018   1634 	beq	.L1914

00000790 e25cc002   1635 	subs	r12,r12,2

00000794 0a00000f   1636 	beq	.L1931


                                                                      Page 28
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
00000798 e25cc003   1637 	subs	r12,r12,3

0000079c 0a00000d   1638 	beq	.L1931

000007a0 e35c0002   1639 	cmp	r12,2

000007a4 03a0008c   1640 	moveq	r0,140

000007a8 07c60005   1641 	streqb	r0,[r6,r5]

000007ac 00840005   1642 	addeq	r0,r4,r5

000007b0 0a00000f   1643 	beq	.L1914

000007b4 ea00000b   1644 	b	.L1933

                    1645 .L1925:

000007b8 e3a00085   1646 	mov	r0,133

000007bc e7c60005   1647 	strb	r0,[r6,r5]

000007c0 e0840005   1648 	add	r0,r4,r5

000007c4 ea00000a   1649 	b	.L1914

                    1650 .L1926:

000007c8 e3a00086   1651 	mov	r0,134

000007cc e7c60005   1652 	strb	r0,[r6,r5]

000007d0 e0840005   1653 	add	r0,r4,r5

000007d4 ea000006   1654 	b	.L1914

                    1655 .L1931:

000007d8 e3a00084   1656 	mov	r0,132

000007dc e7c60005   1657 	strb	r0,[r6,r5]

000007e0 e0840005   1658 	add	r0,r4,r5

000007e4 ea000002   1659 	b	.L1914

                    1660 .L1933:

000007e8 e3a0008a   1661 	mov	r0,138

000007ec e7c60005   1662 	strb	r0,[r6,r5]

000007f0 e0840005   1663 	add	r0,r4,r5

                    1664 .L1914:

000007f4 e28dd004   1665 	add	sp,sp,4

000007f8 e8bd84f0   1666 	ldmfd	[sp]!,{r4-r7,r10,pc}

                    1667 	.endf	encodeReadConst

                    1668 	.align	4

                    1669 ;sizeOfTL	r0	local

                    1670 ;fullSize	r4	local

                    1671 ;objectSize	[sp]	local

                    1672 ;constTypeTag	r12	local

                    1673 ;mmsTag	r0	local

                    1674 

                    1675 ;outBuf	r6	param

                    1676 ;bufPos	r5	param

                    1677 ;constObjPos	r7	param

                    1678 ;determineSize	r10	param

                    1679 

                    1680 	.section ".bss","awb"

                    1681 .L2042:

                    1682 	.data

                    1683 	.text

                    1684 

                    1685 

                    1686 	.align	4

                    1687 	.align	4

                    1688 getAlignedDescrStruct::

000007fc e92d4000   1689 	stmfd	[sp]!,{lr}

00000800 e59f1084*  1690 	ldr	r1,.L101

00000804 e24dd004   1691 	sub	sp,sp,4

00000808 e591c000   1692 	ldr	r12,[r1]

0000080c e2802001   1693 	add	r2,r0,1

00000810 e7dc0000   1694 	ldrb	r0,[r12,r0]

00000814 e3500004   1695 	cmp	r0,4

00000818 1a00000e   1696 	bne	.L2093

0000081c e59f006c*  1697 	ldr	r0,.L102


                                                                      Page 29
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
00000820 e1a0100d   1698 	mov	r1,sp

00000824 e5903000   1699 	ldr	r3,[r0]

00000828 e1a0000c   1700 	mov	r0,r12

0000082c eb000000*  1701 	bl	BerDecoder_decodeLength

00000830 e1a02000   1702 	mov	r2,r0

00000834 e3720001   1703 	cmn	r2,1

00000838 0a000006   1704 	beq	.L2093

0000083c e59f1048*  1705 	ldr	r1,.L101

00000840 e5910000   1706 	ldr	r0,[r1]

00000844 e7f01002   1707 	ldrb	r1,[r0,r2]!

00000848 e3510004   1708 	cmp	r1,4

0000084c 30810000   1709 	addlo	r0,r1,r0

00000850 32800001   1710 	addlo	r0,r0,1

00000854 3a000000   1711 	blo	.L2084

                    1712 .L2093:

00000858 e3a00000   1713 	mov	r0,0

                    1714 .L2084:

0000085c e28dd004   1715 	add	sp,sp,4

00000860 e8bd8000   1716 	ldmfd	[sp]!,{pc}

                    1717 	.endf	getAlignedDescrStruct

                    1718 	.align	4

                    1719 ;pDescrStructAlignOffset	r0	local

                    1720 ;descrTag	r0	local

                    1721 ;descrLen	[sp]	local

                    1722 

                    1723 ;pos	r2	param

                    1724 

                    1725 	.section ".bss","awb"

                    1726 .L2150:

                    1727 	.data

                    1728 	.text

                    1729 

                    1730 

                    1731 	.align	4

                    1732 	.align	4

                    1733 encodeTypeAccessAttrs::

00000864 e92d4010   1734 	stmfd	[sp]!,{r4,lr}

00000868 e5ddc008   1735 	ldrb	r12,[sp,8]

0000086c e1a04003   1736 	mov	r4,r3

00000870 e59f301c*  1737 	ldr	r3,.L2316

00000874 e252201f   1738 	subs	r2,r2,31

00000878 2a000014   1739 	bhs	.L2279

0000087c e292201f   1740 	adds	r2,r2,31

00000880 e352000b   1741 	cmp	r2,11

00000884 8a000050   1742 	bhi	.L2200

00000888 ea000002   1743 	b	.L2317

                    1744 	.align	4

                    1745 .L101:

0000088c 00000000*  1746 	.data.w	iedModel

                    1747 	.type	.L101,$object

                    1748 	.size	.L101,4

                    1749 

                    1750 .L102:

00000890 00000000*  1751 	.data.w	iedModelSize

                    1752 	.type	.L102,$object

                    1753 	.size	.L102,4

                    1754 

                    1755 .L2316:

00000894 00000000*  1756 	.data.w	encodeAccessAttrUInt

                    1757 	.type	.L2316,$object

                    1758 	.size	.L2316,4


                                                                      Page 30
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
                    1759 

                    1760 .L2317:

                    1761 

00000898 e08ff102   1762 	add	pc,pc,r2 lsl 2

                    1763 .L2280:

                    1764 

0000089c e1a00000   1765 	nop	

000008a0 ea000020   1766 	b	.L2178

000008a4 ea000048   1767 	b	.L2200

000008a8 ea00001b   1768 	b	.L2176

000008ac ea000020   1769 	b	.L2180

000008b0 ea000022   1770 	b	.L2182

000008b4 ea000044   1771 	b	.L2200

000008b8 ea000034   1772 	b	.L2192

000008bc ea000023   1773 	b	.L2184

000008c0 ea000026   1774 	b	.L2186

000008c4 ea00003c   1775 	b	.L2198

000008c8 ea00003f   1776 	b	.L2200

000008cc ea000027   1777 	b	.L2188

                    1778 	.align	4

                    1779 .L2279:

                    1780 

000008d0 e352000a   1781 	cmp	r2,10

000008d4 8a00003c   1782 	bhi	.L2200

000008d8 e08ff102   1783 	add	pc,pc,r2 lsl 2

                    1784 .L2281:

                    1785 

000008dc e1a00000   1786 	nop	

000008e0 ea000009   1787 	b	.L2174

000008e4 ea00002c   1788 	b	.L2194

000008e8 ea00000b   1789 	b	.L2176

000008ec ea000013   1790 	b	.L2182

000008f0 ea00001a   1791 	b	.L2186

000008f4 ea000034   1792 	b	.L2200

000008f8 ea000014   1793 	b	.L2184

000008fc ea00002a   1794 	b	.L2196

00000900 ea000005   1795 	b	.L2176

00000904 ea000004   1796 	b	.L2176

00000908 ea00001c   1797 	b	.L2190

                    1798 .L2174:

0000090c e1a0300c   1799 	mov	r3,r12

00000910 e1a02004   1800 	mov	r2,r4

00000914 e8bd4010   1801 	ldmfd	[sp]!,{r4,lr}

00000918 ea000000*  1802 	b	encodeAccessAttrConst

                    1803 .L2176:

0000091c e1a0200c   1804 	mov	r2,r12

00000920 e8bd4010   1805 	ldmfd	[sp]!,{r4,lr}

00000924 ea000000*  1806 	b	encodeAccessAttrFloat

                    1807 .L2178:

00000928 e1a0200c   1808 	mov	r2,r12

0000092c e8bd4010   1809 	ldmfd	[sp]!,{r4,lr}

00000930 ea000000*  1810 	b	encodeAccessAttrQuality

                    1811 .L2180:

00000934 e1a0200c   1812 	mov	r2,r12

00000938 e8bd4010   1813 	ldmfd	[sp]!,{r4,lr}

0000093c ea000000*  1814 	b	encodeAccessAttrTimeStamp

                    1815 .L2182:

00000940 e1a0300c   1816 	mov	r3,r12

00000944 e3a02020   1817 	mov	r2,32

00000948 eb000000*  1818 	bl	encodeAccessAttrInt

0000094c ea00001f   1819 	b	.L2170


                                                                      Page 31
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
                    1820 .L2184:

00000950 e1a0300c   1821 	mov	r3,r12

00000954 e3a02008   1822 	mov	r2,8

00000958 eb000000*  1823 	bl	encodeAccessAttrInt

0000095c ea00001b   1824 	b	.L2170

                    1825 .L2186:

00000960 e1a0300c   1826 	mov	r3,r12

00000964 e3a02020   1827 	mov	r2,32

00000968 eb000000*  1828 	bl	encodeAccessAttrUInt

0000096c ea000017   1829 	b	.L2170

                    1830 .L2188:

00000970 e1a0300c   1831 	mov	r3,r12

00000974 e3a02008   1832 	mov	r2,8

00000978 eb000000*  1833 	bl	encodeAccessAttrUInt

0000097c ea000013   1834 	b	.L2170

                    1835 .L2190:

00000980 e1a0300c   1836 	mov	r3,r12

00000984 e3a02040   1837 	mov	r2,64

00000988 eb000000*  1838 	bl	encodeAccessAttrInt

0000098c ea00000f   1839 	b	.L2170

                    1840 .L2192:

00000990 e1a0200c   1841 	mov	r2,r12

00000994 eb000000*  1842 	bl	encodeAccessAttrBoolean

00000998 ea00000c   1843 	b	.L2170

                    1844 .L2194:

0000099c e1a0300c   1845 	mov	r3,r12

000009a0 e1a02004   1846 	mov	r2,r4

000009a4 eb000000*  1847 	bl	encodeAccessAttrRCB

000009a8 ea000008   1848 	b	.L2170

                    1849 .L2196:

000009ac e1a0300c   1850 	mov	r3,r12

000009b0 e1a02004   1851 	mov	r2,r4

000009b4 eb000000*  1852 	bl	encodeAccessAttrGoCB

000009b8 ea000004   1853 	b	.L2170

                    1854 .L2198:

000009bc e1a0300c   1855 	mov	r3,r12

000009c0 e1a02004   1856 	mov	r2,r4

000009c4 eb000000*  1857 	bl	encodeAccessAttrCodedEnum

000009c8 ea000000   1858 	b	.L2170

                    1859 .L2200:

000009cc e3a00000   1860 	mov	r0,0

                    1861 .L2170:

000009d0 e8bd8010   1862 	ldmfd	[sp]!,{r4,pc}

                    1863 	.endf	encodeTypeAccessAttrs

                    1864 	.align	4

                    1865 

                    1866 ;outBuf	r0	param

                    1867 ;bufPos	r1	param

                    1868 ;type	r2	param

                    1869 ;attrDataPos	r4	param

                    1870 ;determineSize	r12	param

                    1871 

                    1872 	.section ".bss","awb"

                    1873 .L2278:

                    1874 	.data

                    1875 	.ghsnote jtable,5,.L2280,.L2280,.L2280,13

                    1876 	.ghsnote jtable,5,.L2281,.L2281,.L2281,12

                    1877 	.text

                    1878 

                    1879 

                    1880 	.align	4


                                                                      Page 32
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
                    1881 	.align	4

                    1882 encodeNameAttr:

000009d4 e92d44f0   1883 	stmfd	[sp]!,{r4-r7,r10,lr}

000009d8 e1a05002   1884 	mov	r5,r2

000009dc e2852001   1885 	add	r2,r5,1

000009e0 e1a04001   1886 	mov	r4,r1

000009e4 e24dd004   1887 	sub	sp,sp,4

000009e8 e1a07003   1888 	mov	r7,r3

000009ec e1a06000   1889 	mov	r6,r0

000009f0 e59f084c*  1890 	ldr	r0,.L2392

000009f4 e59fc84c*  1891 	ldr	r12,.L2393

000009f8 e5903000   1892 	ldr	r3,[r0]

000009fc e59c0000   1893 	ldr	r0,[r12]

00000a00 e1a0100d   1894 	mov	r1,sp

00000a04 eb000000*  1895 	bl	BerDecoder_decodeLength

00000a08 e3500000   1896 	cmp	r0,0

00000a0c d3a00000   1897 	movle	r0,0

00000a10 da00000e   1898 	ble	.L2318

00000a14 e59d1000   1899 	ldr	r1,[sp]

00000a18 e3570000   1900 	cmp	r7,0

00000a1c e0800001   1901 	add	r0,r0,r1

00000a20 e040a005   1902 	sub	r10,r0,r5

00000a24 11a0000a   1903 	movne	r0,r10

00000a28 1a000008   1904 	bne	.L2318

00000a2c e59f1814*  1905 	ldr	r1,.L2393

00000a30 e5910000   1906 	ldr	r0,[r1]

00000a34 e1a0200a   1907 	mov	r2,r10

00000a38 e0801005   1908 	add	r1,r0,r5

00000a3c e0840006   1909 	add	r0,r4,r6

00000a40 eb000000*  1910 	bl	memcpy

00000a44 e3a00080   1911 	mov	r0,128

00000a48 e7c60004   1912 	strb	r0,[r6,r4]

00000a4c e08a0004   1913 	add	r0,r10,r4

                    1914 .L2318:

00000a50 e28dd004   1915 	add	sp,sp,4

00000a54 e8bd44f0   1916 	ldmfd	[sp]!,{r4-r7,r10,lr}

00000a58 e12fff1e*  1917 	ret	

                    1918 	.endf	encodeNameAttr

                    1919 	.align	4

                    1920 ;totalLen	r10	local

                    1921 ;len	[sp]	local

                    1922 ;pos	r0	local

                    1923 

                    1924 ;outBuf	r6	param

                    1925 ;bufPos	r4	param

                    1926 ;namePos	r5	param

                    1927 ;determineSize	r7	param

                    1928 

                    1929 	.section ".bss","awb"

                    1930 .L2373:

                    1931 	.data

                    1932 	.text

                    1933 

                    1934 

                    1935 	.align	4

                    1936 	.align	4

                    1937 encodeSimpleDataAccessAttrs::

00000a5c e92d4ff0   1938 	stmfd	[sp]!,{r4-fp,lr}

00000a60 e24dd014   1939 	sub	sp,sp,20

00000a64 e5dda038   1940 	ldrb	r10,[sp,56]

00000a68 e2822001   1941 	add	r2,r2,1


                                                                      Page 33
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
00000a6c e1a06001   1942 	mov	r6,r1

00000a70 e5cd3007   1943 	strb	r3,[sp,7]

00000a74 e1a05000   1944 	mov	r5,r0

00000a78 e59f07c4*  1945 	ldr	r0,.L2392

00000a7c e59fc7c4*  1946 	ldr	r12,.L2393

00000a80 e5903000   1947 	ldr	r3,[r0]

00000a84 e59c0000   1948 	ldr	r0,[r12]

00000a88 e28d1008   1949 	add	r1,sp,8

00000a8c eb000000*  1950 	bl	BerDecoder_decodeLength

00000a90 e1a04000   1951 	mov	r4,r0

00000a94 e3740001   1952 	cmn	r4,1

00000a98 0a000037   1953 	beq	.L2412

00000a9c e1a08004   1954 	mov	r8,r4

00000aa0 ebfffd6a*  1955 	bl	skipObject

00000aa4 e1b04000   1956 	movs	r4,r0

00000aa8 0a000033   1957 	beq	.L2412

00000aac e59f7794*  1958 	ldr	r7,.L2393

00000ab0 e5970000   1959 	ldr	r0,[r7]

00000ab4 e7d00004   1960 	ldrb	r0,[r0,r4]

00000ab8 ebfffdc5*  1961 	bl	IEDModel_isServiceInfo

00000abc e3500000   1962 	cmp	r0,0

00000ac0 0a000007   1963 	beq	.L2403

                    1964 .L2404:

00000ac4 e1a00004   1965 	mov	r0,r4

00000ac8 ebfffd60*  1966 	bl	skipObject

00000acc e1a04000   1967 	mov	r4,r0

00000ad0 e5970000   1968 	ldr	r0,[r7]

00000ad4 e7d00004   1969 	ldrb	r0,[r0,r4]

00000ad8 ebfffdbd*  1970 	bl	IEDModel_isServiceInfo

00000adc e3500000   1971 	cmp	r0,0

00000ae0 1afffff7   1972 	bne	.L2404

                    1973 .L2403:

00000ae4 e59f375c*  1974 	ldr	r3,.L2393

00000ae8 e5930000   1975 	ldr	r0,[r3]

00000aec e7d01004   1976 	ldrb	r1,[r0,r4]

00000af0 e2844001   1977 	add	r4,r4,1

00000af4 e3510002   1978 	cmp	r1,2

00000af8 1a00001f   1979 	bne	.L2412

00000afc e7d07004   1980 	ldrb	r7,[r0,r4]

00000b00 e2844001   1981 	add	r4,r4,1

00000b04 e084b007   1982 	add	fp,r4,r7

00000b08 e1a02004   1983 	mov	r2,r4

00000b0c e1a01007   1984 	mov	r1,r7

00000b10 eb000000*  1985 	bl	BerDecoder_decodeUint32

00000b14 e1a0300b   1986 	mov	r3,fp

00000b18 e1a01006   1987 	mov	r1,r6

00000b1c e1a09000   1988 	mov	r9,r0

00000b20 e1a02000   1989 	mov	r2,r0

00000b24 e3a00001   1990 	mov	r0,1

00000b28 e58d0000   1991 	str	r0,[sp]

00000b2c e1a00005   1992 	mov	r0,r5

00000b30 ebffff4b*  1993 	bl	encodeTypeAccessAttrs

00000b34 e1a07000   1994 	mov	r7,r0

00000b38 e35a0000   1995 	cmp	r10,0

00000b3c 0a000004   1996 	beq	.L2408

00000b40 e5dd0007   1997 	ldrb	r0,[sp,7]

00000b44 e2874003   1998 	add	r4,r7,3

00000b48 e3500000   1999 	cmp	r0,0

00000b4c 0a000015   2000 	beq	.L2414

00000b50 ea00000f   2001 	b	.L2415

                    2002 .L2408:


                                                                      Page 34
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
00000b54 eb000000*  2003 	bl	BerEncoder_determineLengthSize

00000b58 e1a02008   2004 	mov	r2,r8

00000b5c e3a03001   2005 	mov	r3,1

00000b60 e0870000   2006 	add	r0,r7,r0

00000b64 e2804001   2007 	add	r4,r0,1

00000b68 e3a01000   2008 	mov	r1,0

00000b6c e1a00001   2009 	mov	r0,r1

00000b70 ebffff97*  2010 	bl	encodeNameAttr

00000b74 e3500000   2011 	cmp	r0,0

00000b78 ca000001   2012 	bgt	.L2411

                    2013 .L2412:

00000b7c e3a00000   2014 	mov	r0,0

00000b80 ea000030   2015 	b	.L2394

                    2016 .L2411:

00000b84 e0844000   2017 	add	r4,r4,r0

00000b88 e5dd0007   2018 	ldrb	r0,[sp,7]

00000b8c e3500000   2019 	cmp	r0,0

00000b90 0a000004   2020 	beq	.L2414

                    2021 .L2415:

00000b94 e1a00004   2022 	mov	r0,r4

00000b98 eb000000*  2023 	bl	BerEncoder_determineLengthSize

00000b9c e0840000   2024 	add	r0,r4,r0

00000ba0 e2800001   2025 	add	r0,r0,1

00000ba4 ea000027   2026 	b	.L2394

                    2027 .L2414:

00000ba8 e1a03006   2028 	mov	r3,r6

00000bac e1a02005   2029 	mov	r2,r5

00000bb0 e35a0000   2030 	cmp	r10,0

00000bb4 1a000014   2031 	bne	.L2417

00000bb8 e1a01004   2032 	mov	r1,r4

00000bbc e3a00030   2033 	mov	r0,48

00000bc0 eb000000*  2034 	bl	BerEncoder_encodeTL

00000bc4 e1a02008   2035 	mov	r2,r8

00000bc8 e1a01000   2036 	mov	r1,r0

00000bcc e1a00005   2037 	mov	r0,r5

00000bd0 e3a03000   2038 	mov	r3,0

00000bd4 ebffff7e*  2039 	bl	encodeNameAttr

00000bd8 e1a03000   2040 	mov	r3,r0

00000bdc e3a000a1   2041 	mov	r0,161

00000be0 e1a02005   2042 	mov	r2,r5

00000be4 e1a01007   2043 	mov	r1,r7

00000be8 eb000000*  2044 	bl	BerEncoder_encodeTL

00000bec e1a0300b   2045 	mov	r3,fp

00000bf0 e1a02009   2046 	mov	r2,r9

00000bf4 e1a01000   2047 	mov	r1,r0

00000bf8 e3a00000   2048 	mov	r0,0

00000bfc e58d0000   2049 	str	r0,[sp]

00000c00 e1a00005   2050 	mov	r0,r5

00000c04 ebffff16*  2051 	bl	encodeTypeAccessAttrs

00000c08 ea00000e   2052 	b	.L2394

                    2053 .L2417:

00000c0c e3a01000   2054 	mov	r1,0

00000c10 e3a00080   2055 	mov	r0,128

00000c14 eb000000*  2056 	bl	BerEncoder_encodeBoolean

00000c18 e1a03000   2057 	mov	r3,r0

00000c1c e3a000a2   2058 	mov	r0,162

00000c20 e1a02005   2059 	mov	r2,r5

00000c24 e1a01007   2060 	mov	r1,r7

00000c28 eb000000*  2061 	bl	BerEncoder_encodeTL

00000c2c e1a0300b   2062 	mov	r3,fp

00000c30 e1a02009   2063 	mov	r2,r9


                                                                      Page 35
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
00000c34 e1a01000   2064 	mov	r1,r0

00000c38 e3a00000   2065 	mov	r0,0

00000c3c e58d0000   2066 	str	r0,[sp]

00000c40 e1a00005   2067 	mov	r0,r5

00000c44 ebffff06*  2068 	bl	encodeTypeAccessAttrs

                    2069 .L2394:

00000c48 e28dd014   2070 	add	sp,sp,20

00000c4c e8bd8ff0   2071 	ldmfd	[sp]!,{r4-fp,pc}

                    2072 	.endf	encodeSimpleDataAccessAttrs

                    2073 	.align	4

                    2074 ;typeIdTag	r1	local

                    2075 ;typeIdLen	r7	local

                    2076 ;typeId	r9	local

                    2077 ;typeDescrSize	r7	local

                    2078 ;pos	r4	local

                    2079 ;objectSize	[sp,8]	local

                    2080 ;sequenceSize	r4	local

                    2081 ;objectNamePos	r8	local

                    2082 ;nameLen	r0	local

                    2083 ;daDataPos	fp	local

                    2084 

                    2085 ;outBuf	r5	param

                    2086 ;bufPos	r6	param

                    2087 ;objectPos	r2	param

                    2088 ;determineSize	[sp,7]	param

                    2089 ;topStruct	r10	param

                    2090 

                    2091 	.section ".bss","awb"

                    2092 .L2603:

                    2093 	.data

                    2094 	.text

                    2095 

                    2096 

                    2097 	.align	4

                    2098 	.align	4

                    2099 encodeObjectAccessAttrs::

00000c50 e92d4ff0   2100 	stmfd	[sp]!,{r4-fp,lr}

00000c54 e24dd018   2101 	sub	sp,sp,24

00000c58 e59fc5e8*  2102 	ldr	r12,.L2393

00000c5c e1a06000   2103 	mov	r6,r0

00000c60 e59c0000   2104 	ldr	r0,[r12]

00000c64 e7d0c002   2105 	ldrb	r12,[r0,r2]

00000c68 e5dd503c   2106 	ldrb	r5,[sp,60]

00000c6c e35c00e9   2107 	cmp	r12,233

00000c70 1a000003   2108 	bne	.L2640

00000c74 e58d5000   2109 	str	r5,[sp]

00000c78 e1a00006   2110 	mov	r0,r6

00000c7c ebffff76*  2111 	bl	encodeSimpleDataAccessAttrs

00000c80 ea00005f   2112 	b	.L2638

                    2113 .L2640:

00000c84 e58d200c   2114 	str	r2,[sp,12]

00000c88 e1a07002   2115 	mov	r7,r2

00000c8c e2822001   2116 	add	r2,r2,1

                    2117 ;817:     int objectSize;


                    2118 ;818: 


                    2119 ;819: 


                    2120 ;820:     //Пропускаем тэг


                    2121 ;821:     pos++;


                    2122 

                    2123 ;822: 


                    2124 ;823:     //Пропускаем длину



                                                                      Page 36
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
                    2125 ;824:     pos = BerDecoder_decodeLength(iedModel, &objectSize, pos, iedModelSize);


                    2126 

00000c90 e1a04001   2127 	mov	r4,r1

00000c94 e59f15a8*  2128 	ldr	r1,.L2392

00000c98 e5cd3007   2129 	strb	r3,[sp,7]

                    2130 ;807:                           bool topStruct)


                    2131 ;808: {


                    2132 

                    2133 ;809:     int totalSize;


                    2134 ;810:     int sequenceSize;


                    2135 ;811:     int descrTypeSize;


                    2136 ;812:     int structureSize;


                    2137 ;813:     int fieldListSize;


                    2138 ;814:     int nameLen;


                    2139 ;815:     int objectNamePos;


                    2140 ;816:     int pos = objectPos;


                    2141 

00000c9c e5913000   2142 	ldr	r3,[r1]

00000ca0 e28d1008   2143 	add	r1,sp,8

00000ca4 eb000000*  2144 	bl	BerDecoder_decodeLength

                    2145 ;825:     if(pos == -1)


                    2146 

00000ca8 e3700001   2147 	cmn	r0,1

00000cac 0a00001f   2148 	beq	.L2651

                    2149 ;826:     {


                    2150 

                    2151 ;827:         return 0;


                    2152 

                    2153 ;828:     }


                    2154 ;829: 


                    2155 ;830:     objectNamePos = pos;


                    2156 

00000cb0 e1a02007   2157 	mov	r2,r7

00000cb4 e1a01004   2158 	mov	r1,r4

00000cb8 e1a08000   2159 	mov	r8,r0

                    2160 ;831: 


                    2161 ;832: 


                    2162 ;833:     //================= Определяем размеры ===================


                    2163 ;834:     fieldListSize = encodeChildrenAccessAttrs(outBuf, bufPos,  objectPos, TRUE);


                    2164 

00000cbc e1a00006   2165 	mov	r0,r6

00000cc0 e3a03001   2166 	mov	r3,1

00000cc4 eb000050*  2167 	bl	encodeChildrenAccessAttrs

                    2168 ;835:     structureSize = 1


                    2169 

00000cc8 e1a09000   2170 	mov	r9,r0

00000ccc eb000000*  2171 	bl	BerEncoder_determineLengthSize

00000cd0 e0890000   2172 	add	r0,r9,r0

00000cd4 e280a001   2173 	add	r10,r0,1

                    2174 ;836:             + BerEncoder_determineLengthSize(fieldListSize)


                    2175 ;837:             + fieldListSize;


                    2176 ;838: 


                    2177 ;839:     descrTypeSize = 1


                    2178 

00000cd8 e1a0000a   2179 	mov	r0,r10

00000cdc eb000000*  2180 	bl	BerEncoder_determineLengthSize

00000ce0 e08a0000   2181 	add	r0,r10,r0

00000ce4 e280b001   2182 	add	fp,r0,1

                    2183 ;840:             + BerEncoder_determineLengthSize(structureSize)


                    2184 ;841:             + structureSize;


                    2185 ;842: 



                                                                      Page 37
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
                    2186 ;843:     sequenceSize = 1


                    2187 

00000ce8 e1a0000b   2188 	mov	r0,fp

00000cec eb000000*  2189 	bl	BerEncoder_determineLengthSize

00000cf0 e08b0000   2190 	add	r0,fp,r0

00000cf4 e2807001   2191 	add	r7,r0,1

                    2192 ;844:         + BerEncoder_determineLengthSize(descrTypeSize)


                    2193 ;845:         + descrTypeSize;


                    2194 ;846: 


                    2195 ;847:     if(topStruct)


                    2196 

00000cf8 e3550000   2197 	cmp	r5,0

00000cfc 0a000004   2198 	beq	.L2650

                    2199 ;848:     {


                    2200 

                    2201 ;849:         //+Deletable size


                    2202 ;850:         totalSize = sequenceSize + 3;


                    2203 

00000d00 e5dd1007   2204 	ldrb	r1,[sp,7]

00000d04 e2800004   2205 	add	r0,r0,4

                    2206 ;864:             + BerEncoder_determineLengthSize(sequenceSize)


                    2207 ;865:             + sequenceSize;


                    2208 ;866:     }


                    2209 ;867: 


                    2210 ;868:     if(determineSize)


                    2211 

00000d08 e3510000   2212 	cmp	r1,0

00000d0c 1a000010   2213 	bne	.L2654

00000d10 ea000010   2214 	b	.L2655

                    2215 .L2650:

                    2216 ;851:     }


                    2217 ;852:     else


                    2218 ;853:     {


                    2219 

                    2220 ;854: 


                    2221 ;855:         //+Name size


                    2222 ;856:         nameLen = encodeNameAttr(NULL, 0, objectNamePos, TRUE);


                    2223 

00000d14 e1a02008   2224 	mov	r2,r8

00000d18 e3a03001   2225 	mov	r3,1

00000d1c e3a01000   2226 	mov	r1,0

00000d20 e1a00001   2227 	mov	r0,r1

00000d24 ebffff2a*  2228 	bl	encodeNameAttr

                    2229 ;857:         if(nameLen < 1)


                    2230 

00000d28 e3500000   2231 	cmp	r0,0

00000d2c ca000001   2232 	bgt	.L2652

                    2233 .L2651:

                    2234 ;858:         {


                    2235 

                    2236 ;859:             return 0;


                    2237 

00000d30 e3a00000   2238 	mov	r0,0

00000d34 ea000032   2239 	b	.L2638

                    2240 .L2652:

                    2241 ;860:         }


                    2242 ;861:         sequenceSize += nameLen;


                    2243 

00000d38 e0877000   2244 	add	r7,r7,r0

                    2245 ;862: 


                    2246 ;863:         totalSize = 1



                                                                      Page 38
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
                    2247 

00000d3c e1a00007   2248 	mov	r0,r7

00000d40 eb000000*  2249 	bl	BerEncoder_determineLengthSize

00000d44 e0870000   2250 	add	r0,r7,r0

00000d48 e5dd1007   2251 	ldrb	r1,[sp,7]

00000d4c e2800001   2252 	add	r0,r0,1

                    2253 ;864:             + BerEncoder_determineLengthSize(sequenceSize)


                    2254 ;865:             + sequenceSize;


                    2255 ;866:     }


                    2256 ;867: 


                    2257 ;868:     if(determineSize)


                    2258 

00000d50 e3510000   2259 	cmp	r1,0

                    2260 .L2654:

                    2261 ;869:     {


                    2262 

                    2263 ;870:         return totalSize;


                    2264 

00000d54 1a00002a   2265 	bne	.L2638

                    2266 .L2655:

                    2267 ;871:     }


                    2268 ;872: 


                    2269 ;873:     //=================== Пишем ==============================


                    2270 ;874:     if(!topStruct)


                    2271 

00000d58 e1a03004   2272 	mov	r3,r4

00000d5c e1a02006   2273 	mov	r2,r6

00000d60 e3550000   2274 	cmp	r5,0

00000d64 1a00000c   2275 	bne	.L2657

                    2276 ;875:     {


                    2277 

                    2278 ;876:         //Если не topStruct пишем sequence


                    2279 ;877:         bufPos = BerEncoder_encodeTL(ASN_SEQUENCE, sequenceSize,


                    2280 

00000d68 e1a01007   2281 	mov	r1,r7

00000d6c e3a00030   2282 	mov	r0,48

00000d70 eb000000*  2283 	bl	BerEncoder_encodeTL

                    2284 ;878:                                      outBuf, bufPos);


                    2285 ;879:         //Имя объекта


                    2286 ;880:         bufPos = encodeNameAttr(outBuf, bufPos, objectNamePos, FALSE);


                    2287 

00000d74 e1a02008   2288 	mov	r2,r8

00000d78 e1a01000   2289 	mov	r1,r0

00000d7c e1a00006   2290 	mov	r0,r6

00000d80 e3a03000   2291 	mov	r3,0

00000d84 ebffff12*  2292 	bl	encodeNameAttr

00000d88 e1a04000   2293 	mov	r4,r0

                    2294 ;886:     }


                    2295 ;887: 


                    2296 ;888:     //Описание типа


                    2297 ;889:     bufPos = BerEncoder_encodeTL(


                    2298 

00000d8c e3a000a1   2299 	mov	r0,161

00000d90 e3550000   2300 	cmp	r5,0

00000d94 1a000006   2301 	bne	.L2659

00000d98 ea000006   2302 	b	.L2660

                    2303 .L2657:

                    2304 ;881:     }


                    2305 ;882:     else


                    2306 ;883:     {


                    2307 


                                                                      Page 39
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
                    2308 ;884:         //Deletable


                    2309 ;885:         bufPos = BerEncoder_encodeBoolean(VAR_DELETABLE, FALSE, outBuf, bufPos);


                    2310 

00000d9c e3a01000   2311 	mov	r1,0

00000da0 e3a00080   2312 	mov	r0,128

00000da4 eb000000*  2313 	bl	BerEncoder_encodeBoolean

00000da8 e1a04000   2314 	mov	r4,r0

                    2315 ;886:     }


                    2316 ;887: 


                    2317 ;888:     //Описание типа


                    2318 ;889:     bufPos = BerEncoder_encodeTL(


                    2319 

00000dac e3a000a1   2320 	mov	r0,161

00000db0 e3550000   2321 	cmp	r5,0

                    2322 .L2659:

00000db4 13a000a2   2323 	movne	r0,162

                    2324 .L2660:

00000db8 e1a03004   2325 	mov	r3,r4

00000dbc e1a02006   2326 	mov	r2,r6

00000dc0 e1a0100b   2327 	mov	r1,fp

00000dc4 eb000000*  2328 	bl	BerEncoder_encodeTL

                    2329 ;890: 


                    2330 ;891:                 //Дурацкий хак


                    2331 ;892:                 topStruct? ASN_TYPEDESCRIPTION_STRUCTURE:


                    2332 ;893:                            ASN_TYPEDESCRIPTION_COMPONENT_TYPE,


                    2333 ;894: 


                    2334 ;895:                                  descrTypeSize, outBuf, bufPos);


                    2335 ;896: 


                    2336 ;897:     //Structure


                    2337 ;898:     bufPos = BerEncoder_encodeTL(ASN_TYPEDESCRIPTION_STRUCTURE,


                    2338 

00000dc8 e1a02006   2339 	mov	r2,r6

00000dcc e1a0100a   2340 	mov	r1,r10

00000dd0 e1a03000   2341 	mov	r3,r0

00000dd4 e3a000a2   2342 	mov	r0,162

00000dd8 eb000000*  2343 	bl	BerEncoder_encodeTL

                    2344 ;899:                                  structureSize, outBuf, bufPos);


                    2345 ;900:     //Тэг списка полей tag=0xa1


                    2346 ;901:     bufPos = BerEncoder_encodeTL(0xA1, fieldListSize, outBuf, bufPos);


                    2347 

00000ddc e1a02006   2348 	mov	r2,r6

00000de0 e1a01009   2349 	mov	r1,r9

00000de4 e1a03000   2350 	mov	r3,r0

00000de8 e3a000a1   2351 	mov	r0,161

00000dec eb000000*  2352 	bl	BerEncoder_encodeTL

                    2353 ;902: 


                    2354 ;903:     //Сами поля


                    2355 ;904:     bufPos = encodeChildrenAccessAttrs(outBuf, bufPos,  objectPos, FALSE);


                    2356 

00000df0 e59d200c   2357 	ldr	r2,[sp,12]

00000df4 e1a01000   2358 	mov	r1,r0

00000df8 e1a00006   2359 	mov	r0,r6

00000dfc e3a03000   2360 	mov	r3,0

00000e00 eb000001*  2361 	bl	encodeChildrenAccessAttrs

                    2362 ;905:     return bufPos;


                    2363 

                    2364 .L2638:

00000e04 e28dd018   2365 	add	sp,sp,24

00000e08 e8bd8ff0   2366 	ldmfd	[sp]!,{r4-fp,pc}

                    2367 	.endf	encodeObjectAccessAttrs

                    2368 	.align	4


                                                                      Page 40
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
                    2369 ;tag	r12	local

                    2370 ;bufPos	r4	local

                    2371 ;objectPos	[sp,12]	local

                    2372 ;determineSize	[sp,7]	local

                    2373 ;topStruct	r5	local

                    2374 ;totalSize	r0	local

                    2375 ;sequenceSize	r7	local

                    2376 ;descrTypeSize	fp	local

                    2377 ;structureSize	r10	local

                    2378 ;fieldListSize	r9	local

                    2379 ;nameLen	r0	local

                    2380 ;objectNamePos	r8	local

                    2381 ;pos	r0	local

                    2382 ;objectSize	[sp,8]	local

                    2383 

                    2384 ;outBuf	r6	param

                    2385 ;bufPos	r1	param

                    2386 ;objectPos	r2	param

                    2387 ;determineSize	r3	param

                    2388 ;topStruct	r5	param

                    2389 

                    2390 	.section ".bss","awb"

                    2391 .L2815:

                    2392 	.data

                    2393 	.text

                    2394 

                    2395 

                    2396 	.align	4

                    2397 	.align	4

                    2398 encodeChildrenAccessAttrs::

00000e0c e92d4df0   2399 	stmfd	[sp]!,{r4-r8,r10-fp,lr}

00000e10 e3a05000   2400 	mov	r5,0

00000e14 e1a0a003   2401 	mov	r10,r3

00000e18 e24dd010   2402 	sub	sp,sp,16

00000e1c e1a04001   2403 	mov	r4,r1

00000e20 e28d1004   2404 	add	r1,sp,4

00000e24 e1a08000   2405 	mov	r8,r0

00000e28 e1a00002   2406 	mov	r0,r2

00000e2c ebfffcf0*  2407 	bl	getSubObjectsPos

00000e30 e1b06000   2408 	movs	r6,r0

00000e34 0a000004   2409 	beq	.L2851

00000e38 e59d1004   2410 	ldr	r1,[sp,4]

00000e3c e59fb404*  2411 	ldr	fp,.L2393

00000e40 e1560001   2412 	cmp	r6,r1

00000e44 aa000018   2413 	bge	.L2854

00000e48 ea000001   2414 	b	.L2855

                    2415 .L2851:

00000e4c e3a00000   2416 	mov	r0,0

00000e50 ea000018   2417 	b	.L2848

                    2418 .L2855:

00000e54 e59f03e8*  2419 	ldr	r0,.L2392

00000e58 e2862001   2420 	add	r2,r6,1

00000e5c e5903000   2421 	ldr	r3,[r0]

00000e60 e59b0000   2422 	ldr	r0,[fp]

00000e64 e28d1008   2423 	add	r1,sp,8

00000e68 eb000000*  2424 	bl	BerDecoder_decodeLength

00000e6c e2507000   2425 	subs	r7,r0,0

00000e70 dafffff5   2426 	ble	.L2851

00000e74 e1a0300a   2427 	mov	r3,r10

00000e78 e1a02006   2428 	mov	r2,r6

00000e7c e3a01000   2429 	mov	r1,0


                                                                      Page 41
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
00000e80 e58d1000   2430 	str	r1,[sp]

00000e84 e1a01004   2431 	mov	r1,r4

00000e88 e1a00008   2432 	mov	r0,r8

00000e8c ebffff6f*  2433 	bl	encodeObjectAccessAttrs

00000e90 e1a04000   2434 	mov	r4,r0

00000e94 e35a0000   2435 	cmp	r10,0

00000e98 10855004   2436 	addne	r5,r5,r4

00000e9c e99d0042   2437 	ldmed	[sp],{r1,r6}

00000ea0 e0866007   2438 	add	r6,r6,r7

00000ea4 e1560001   2439 	cmp	r6,r1

00000ea8 baffffe9   2440 	blt	.L2855

                    2441 .L2854:

00000eac e35a0000   2442 	cmp	r10,0

00000eb0 11a00005   2443 	movne	r0,r5

00000eb4 01a00004   2444 	moveq	r0,r4

                    2445 .L2848:

00000eb8 e28dd010   2446 	add	sp,sp,16

00000ebc e8bd8df0   2447 	ldmfd	[sp]!,{r4-r8,r10-fp,pc}

                    2448 	.endf	encodeChildrenAccessAttrs

                    2449 	.align	4

                    2450 ;totalSize	r5	local

                    2451 ;objEndPos	[sp,4]	local

                    2452 ;childObjectPos	r6	local

                    2453 ;subObjLen	[sp,8]	local

                    2454 ;objContentPos	r7	local

                    2455 

                    2456 ;outBuf	r8	param

                    2457 ;bufPos	r4	param

                    2458 ;rootObjPos	r2	param

                    2459 ;determineSize	r10	param

                    2460 

                    2461 	.section ".bss","awb"

                    2462 .L2995:

                    2463 	.data

                    2464 	.text

                    2465 

                    2466 

                    2467 ;907: 


                    2468 ;908: void* IEDModel_ptrFromPos(size_t pos)


                    2469 	.align	4

                    2470 	.align	4

                    2471 IEDModel_ptrFromPos::

                    2472 ;909: {


                    2473 

                    2474 ;910:     if (pos >= (size_t)iedModelSize)


                    2475 

00000ec0 e59f237c*  2476 	ldr	r2,.L2392

00000ec4 e1a01000   2477 	mov	r1,r0

00000ec8 e5920000   2478 	ldr	r0,[r2]

00000ecc e1510000   2479 	cmp	r1,r0

                    2480 ;911:     {


                    2481 

                    2482 ;912:         return NULL;


                    2483 

00000ed0 359f2370*  2484 	ldrlo	r2,.L2393

00000ed4 23a00000   2485 	movhs	r0,0

                    2486 ;913:     }


                    2487 ;914:     return iedModel + pos;


                    2488 

00000ed8 35920000   2489 	ldrlo	r0,[r2]

00000edc 30800001   2490 	addlo	r0,r0,r1


                                                                      Page 42
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
00000ee0 e12fff1e*  2491 	ret	

                    2492 	.endf	IEDModel_ptrFromPos

                    2493 	.align	4

                    2494 

                    2495 ;pos	r1	param

                    2496 

                    2497 	.section ".bss","awb"

                    2498 .L3062:

                    2499 	.data

                    2500 	.text

                    2501 

                    2502 ;915: }


                    2503 

                    2504 ;916: 


                    2505 ;917: void processSubobjects(int parentPos, void(*func)(int))


                    2506 	.align	4

                    2507 	.align	4

                    2508 processSubobjects::

00000ee4 e92d4cf0   2509 	stmfd	[sp]!,{r4-r7,r10-fp,lr}

00000ee8 e24dd008   2510 	sub	sp,sp,8

00000eec e1a06001   2511 	mov	r6,r1

                    2512 ;918: {


                    2513 

                    2514 ;919:     int objPos;


                    2515 ;920:     int endPos;


                    2516 ;921: 


                    2517 ;922:     objPos = getSubObjectsPos(parentPos, &endPos);


                    2518 

00000ef0 e1a0100d   2519 	mov	r1,sp

00000ef4 ebfffcbe*  2520 	bl	getSubObjectsPos

00000ef8 e1b04000   2521 	movs	r4,r0

                    2522 ;923:     if (objPos == 0)


                    2523 

00000efc 0a000013   2524 	beq	.L3075

00000f00 e59f733c*  2525 	ldr	r7,.L2392

00000f04 e59fb33c*  2526 	ldr	fp,.L2393

00000f08 e59d0000   2527 	ldr	r0,[sp]

00000f0c e28da004   2528 	add	r10,sp,4

00000f10 e1540000   2529 	cmp	r4,r0

00000f14 aa00000d   2530 	bge	.L3075

                    2531 .L3082:

                    2532 ;924:     {


                    2533 

                    2534 ;925:         ERROR_REPORT("Error reading objects at pos = %d", parentPos);


                    2535 ;926:         return;


                    2536 

                    2537 ;927:     }


                    2538 ;928:     while (objPos < endPos)


                    2539 

                    2540 ;929:     {


                    2541 

                    2542 ;930:         int objLen;


                    2543 ;931:         int pos = objPos;


                    2544 

00000f18 e2842001   2545 	add	r2,r4,1

                    2546 ;932:         //Skip tag


                    2547 ;933:         pos++;


                    2548 

                    2549 ;934:         pos = BerDecoder_decodeLength(iedModel, &objLen, pos, iedModelSize);


                    2550 

00000f1c e5973000   2551 	ldr	r3,[r7]


                                                                      Page 43
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
00000f20 e59b0000   2552 	ldr	r0,[fp]

00000f24 e1a0100a   2553 	mov	r1,r10

00000f28 eb000000*  2554 	bl	BerDecoder_decodeLength

00000f2c e2505000   2555 	subs	r5,r0,0

                    2556 ;935:         if (pos < 1)


                    2557 

00000f30 da000006   2558 	ble	.L3075

                    2559 ;936:         {


                    2560 

                    2561 ;937:             ERROR_REPORT("Error reading object length");


                    2562 ;938:             return;


                    2563 

                    2564 ;939:         }


                    2565 ;940:         func(objPos);


                    2566 

00000f34 e1a00004   2567 	mov	r0,r4

00000f38 e1a0e00f   2568 	mov	lr,pc

00000f3c e12fff16*  2569 	bx	r6

                    2570 ;941:         objPos = pos + objLen;


                    2571 

00000f40 e89d0011   2572 	ldmfd	[sp],{r0,r4}

00000f44 e0844005   2573 	add	r4,r4,r5

00000f48 e1540000   2574 	cmp	r4,r0

00000f4c bafffff1   2575 	blt	.L3082

                    2576 .L3075:

00000f50 e28dd008   2577 	add	sp,sp,8

00000f54 e8bd8cf0   2578 	ldmfd	[sp]!,{r4-r7,r10-fp,pc}

                    2579 	.endf	processSubobjects

                    2580 	.align	4

                    2581 ;objPos	r4	local

                    2582 ;endPos	[sp]	local

                    2583 ;objLen	[sp,4]	local

                    2584 ;pos	r5	local

                    2585 

                    2586 ;parentPos	none	param

                    2587 ;func	r6	param

                    2588 

                    2589 	.section ".bss","awb"

                    2590 .L3145:

                    2591 	.data

                    2592 	.text

                    2593 

                    2594 ;942:     }


                    2595 ;943: }


                    2596 

                    2597 ;944: 


                    2598 ;945: int getDAValuePos(int rootObjPos, uint8_t* name, int nameLen,


                    2599 	.align	4

                    2600 	.align	4

                    2601 getDAValuePos::

00000f58 e92d4070   2602 	stmfd	[sp]!,{r4-r6,lr}

00000f5c e24dd004   2603 	sub	sp,sp,4

00000f60 e1a04003   2604 	mov	r4,r3

                    2605 ;946:     enum InnerAttributeType* attrType)


                    2606 ;947: {


                    2607 

                    2608 ;948:     int pos;


                    2609 ;949:     uint8_t tag;


                    2610 ;950:     uint8_t typeIdTag;


                    2611 ;951:     int typeIdLen;


                    2612 ;952:     int daPos = findObjectBySimpleName(rootObjPos, name, nameLen);



                                                                      Page 44
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
                    2613 

00000f64 ebfffcc8*  2614 	bl	findObjectBySimpleName

                    2615 ;953:     RET_IF_NOT(daPos, "Unable to find DA by name");


                    2616 

00000f68 e3500000   2617 	cmp	r0,0

00000f6c 0a00000e   2618 	beq	.L3171

                    2619 ;954:     pos = readTL(daPos, &tag, NULL, NULL);


                    2620 

00000f70 e28d1003   2621 	add	r1,sp,3

00000f74 e3a03000   2622 	mov	r3,0

00000f78 e1a02003   2623 	mov	r2,r3

00000f7c ebfffc24*  2624 	bl	readTL

                    2625 ;955:     RET_IF_NOT(pos, "Error reading DA at %d", daPos);


                    2626 

00000f80 e3500000   2627 	cmp	r0,0

00000f84 0a000008   2628 	beq	.L3171

                    2629 ;956:     //Skip name


                    2630 ;957:     pos = skipObject(pos);


                    2631 

00000f88 ebfffc30*  2632 	bl	skipObject

00000f8c e1b05000   2633 	movs	r5,r0

                    2634 ;958:     RET_IF_NOT(pos, "Error reading DA at %d", daPos);


                    2635 

00000f90 0a000005   2636 	beq	.L3171

                    2637 ;959: 


                    2638 ;960:     //Получаем идентификатор типа


                    2639 ;961:     typeIdTag = iedModel[pos++];


                    2640 

00000f94 e59f32ac*  2641 	ldr	r3,.L2393

00000f98 e5930000   2642 	ldr	r0,[r3]

00000f9c e7d01005   2643 	ldrb	r1,[r0,r5]

00000fa0 e2855001   2644 	add	r5,r5,1

                    2645 ;962:     RET_IF_NOT(typeIdTag == ASN_INTEGER, "Error reading DA at %d", daPos);


                    2646 

00000fa4 e3510002   2647 	cmp	r1,2

00000fa8 0a000001   2648 	beq	.L3170

                    2649 .L3171:

00000fac e3a00000   2650 	mov	r0,0

00000fb0 ea000006   2651 	b	.L3159

                    2652 .L3170:

                    2653 ;963:     typeIdLen = iedModel[pos++];


                    2654 

00000fb4 e7d06005   2655 	ldrb	r6,[r0,r5]

00000fb8 e2855001   2656 	add	r5,r5,1

                    2657 ;964:     //Получаем идентификатор типа


                    2658 ;965:     *attrType = (enum InnerAttributeType)


                    2659 

00000fbc e1a02005   2660 	mov	r2,r5

00000fc0 e1a01006   2661 	mov	r1,r6

00000fc4 eb000000*  2662 	bl	BerDecoder_decodeUint32

00000fc8 e5840000   2663 	str	r0,[r4]

                    2664 ;966:         BerDecoder_decodeUint32(iedModel, typeIdLen, pos);


                    2665 ;967:     pos += typeIdLen;


                    2666 

00000fcc e0850006   2667 	add	r0,r5,r6

                    2668 ;968:     //pos указывает на константу или OCTET_STRING со структурой


                    2669 ;969:     return pos;


                    2670 

                    2671 .L3159:

00000fd0 e28dd004   2672 	add	sp,sp,4

00000fd4 e8bd8070   2673 	ldmfd	[sp]!,{r4-r6,pc}


                                                                      Page 45
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
                    2674 	.endf	getDAValuePos

                    2675 	.align	4

                    2676 ;pos	r5	local

                    2677 ;tag	[sp,3]	local

                    2678 ;typeIdTag	r1	local

                    2679 ;typeIdLen	r6	local

                    2680 ;daPos	r1	local

                    2681 

                    2682 ;rootObjPos	none	param

                    2683 ;name	none	param

                    2684 ;nameLen	none	param

                    2685 ;attrType	r4	param

                    2686 

                    2687 	.section ".bss","awb"

                    2688 .L3248:

                    2689 	.data

                    2690 	.text

                    2691 

                    2692 ;970: }


                    2693 

                    2694 ;971: 


                    2695 ;972: //Возвращает значение константного DA в виде StringView


                    2696 ;973: bool getConstDAString(size_t parentPos, const char* attrName,


                    2697 	.align	4

                    2698 	.align	4

                    2699 getConstDAString::

00000fd8 e92d4070   2700 	stmfd	[sp]!,{r4-r6,lr}

                    2701 ;974:     StringView* result)


                    2702 ;975: {


                    2703 

                    2704 ;976:     uint8_t tag;


                    2705 ;977:     int len;


                    2706 ;978:     enum InnerAttributeType attrType;


                    2707 ;979:     int daPos = getDAValuePos(parentPos, (uint8_t*) attrName, strlen(attrName),


                    2708 

00000fdc e24dd00c   2709 	sub	sp,sp,12

00000fe0 e1a06002   2710 	mov	r6,r2

00000fe4 e1a05000   2711 	mov	r5,r0

00000fe8 e1a04001   2712 	mov	r4,r1

00000fec e1a00004   2713 	mov	r0,r4

00000ff0 eb000000*  2714 	bl	strlen

00000ff4 e28d3008   2715 	add	r3,sp,8

00000ff8 e1a01004   2716 	mov	r1,r4

00000ffc e1a02000   2717 	mov	r2,r0

00001000 e1a00005   2718 	mov	r0,r5

00001004 ebffffd3*  2719 	bl	getDAValuePos

                    2720 ;980:         &attrType);


                    2721 ;981:     RET_IF_NOT(daPos, "Error reading %s", attrName);


                    2722 

00001008 e3500000   2723 	cmp	r0,0

0000100c 0a000004   2724 	beq	.L3271

                    2725 ;982:     //pos указывает на константу


                    2726 ;983:     daPos = readTL(daPos, &tag, &len, NULL);


                    2727 

00001010 e28d2004   2728 	add	r2,sp,4

00001014 e28d1003   2729 	add	r1,sp,3

00001018 e3a03000   2730 	mov	r3,0

0000101c ebfffbfc*  2731 	bl	readTL

00001020 e1b03000   2732 	movs	r3,r0

                    2733 ;984:     RET_IF_NOT(daPos, "Error reading value at %d", daPos);


                    2734 


                                                                      Page 46
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
                    2735 .L3271:

00001024 03a00000   2736 	moveq	r0,0

00001028 0a000006   2737 	beq	.L3265

                    2738 .L3270:

                    2739 ;985:     StringView_init(result, (char*)iedModel + daPos, len);


                    2740 

0000102c e59f1214*  2741 	ldr	r1,.L2393

00001030 e5910000   2742 	ldr	r0,[r1]

00001034 e59d2004   2743 	ldr	r2,[sp,4]

00001038 e0831000   2744 	add	r1,r3,r0

0000103c e1a00006   2745 	mov	r0,r6

00001040 eb000000*  2746 	bl	StringView_init

                    2747 ;986:     return TRUE;


                    2748 

00001044 e3a00001   2749 	mov	r0,1

                    2750 .L3265:

00001048 e28dd00c   2751 	add	sp,sp,12

0000104c e8bd8070   2752 	ldmfd	[sp]!,{r4-r6,pc}

                    2753 	.endf	getConstDAString

                    2754 	.align	4

                    2755 ;tag	[sp,3]	local

                    2756 ;len	[sp,4]	local

                    2757 ;attrType	[sp,8]	local

                    2758 ;daPos	r3	local

                    2759 

                    2760 ;parentPos	r5	param

                    2761 ;attrName	r4	param

                    2762 ;result	r6	param

                    2763 

                    2764 	.section ".bss","awb"

                    2765 .L3324:

                    2766 	.data

                    2767 	.text

                    2768 

                    2769 ;987: }


                    2770 

                    2771 ;988: 


                    2772 ;989: //Возвращает значение константного DA в виде uint32_t


                    2773 ;990: bool getConstDAULong(size_t parentPos, const char* attrName,


                    2774 	.align	4

                    2775 	.align	4

                    2776 getConstDAULong::

00001050 e92d4010   2777 	stmfd	[sp]!,{r4,lr}

                    2778 ;991:     uint32_t* result)


                    2779 ;992: {


                    2780 

                    2781 ;993:     uint8_t tag;


                    2782 ;994:     int len;


                    2783 ;995:     enum InnerAttributeType attrType;


                    2784 ;996:     int daPos = getDAValuePos(parentPos, (uint8_t*) attrName, 7, &attrType);


                    2785 

00001054 e24dd00c   2786 	sub	sp,sp,12

00001058 e28d3008   2787 	add	r3,sp,8

0000105c e1a04002   2788 	mov	r4,r2

00001060 e3a02007   2789 	mov	r2,7

00001064 ebffffbb*  2790 	bl	getDAValuePos

                    2791 ;997:     RET_IF_NOT(daPos, "Error reading %s", attrName);


                    2792 

00001068 e3500000   2793 	cmp	r0,0

0000106c 0a000004   2794 	beq	.L3343

                    2795 ;998:     daPos = readTL(daPos, &tag, &len, NULL);



                                                                      Page 47
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
                    2796 

00001070 e28d2004   2797 	add	r2,sp,4

00001074 e28d1003   2798 	add	r1,sp,3

00001078 e3a03000   2799 	mov	r3,0

0000107c ebfffbe4*  2800 	bl	readTL

00001080 e1b02000   2801 	movs	r2,r0

                    2802 ;999:     RET_IF_NOT(daPos, "Error reading %s", attrName);


                    2803 

                    2804 .L3343:

00001084 03a00000   2805 	moveq	r0,0

00001088 0a000005   2806 	beq	.L3337

                    2807 .L3342:

                    2808 ;1000:     *result = BerDecoder_decodeUint32(iedModel, len, daPos);


                    2809 

0000108c e59f31b4*  2810 	ldr	r3,.L2393

00001090 e59d1004   2811 	ldr	r1,[sp,4]

00001094 e5930000   2812 	ldr	r0,[r3]

00001098 eb000000*  2813 	bl	BerDecoder_decodeUint32

0000109c e5840000   2814 	str	r0,[r4]

                    2815 ;1001:     return TRUE;


                    2816 

000010a0 e3a00001   2817 	mov	r0,1

                    2818 .L3337:

000010a4 e28dd00c   2819 	add	sp,sp,12

000010a8 e8bd8010   2820 	ldmfd	[sp]!,{r4,pc}

                    2821 	.endf	getConstDAULong

                    2822 	.align	4

                    2823 ;tag	[sp,3]	local

                    2824 ;len	[sp,4]	local

                    2825 ;attrType	[sp,8]	local

                    2826 ;daPos	r2	local

                    2827 

                    2828 ;parentPos	none	param

                    2829 ;attrName	none	param

                    2830 ;result	r4	param

                    2831 

                    2832 	.section ".bss","awb"

                    2833 .L3404:

                    2834 	.data

                    2835 	.text

                    2836 

                    2837 ;1002: }


                    2838 

                    2839 ;1003: 


                    2840 ;1004: bool IEDModel_skipServiceInfo(BufferView* subObjects)


                    2841 	.align	4

                    2842 	.align	4

                    2843 IEDModel_skipServiceInfo::

000010ac e92d4030   2844 	stmfd	[sp]!,{r4-r5,lr}

000010b0 e24dd004   2845 	sub	sp,sp,4

000010b4 e1a04000   2846 	mov	r4,r0

                    2847 ;1005: {


                    2848 

                    2849 ;1006:     while(!BufferView_endOfBuf(subObjects))


                    2850 

000010b8 e9940003   2851 	ldmed	[r4],{r0-r1}

000010bc e1500001   2852 	cmp	r0,r1

000010c0 0a000010   2853 	beq	.L3427

                    2854 .L3421:

000010c4 e28d1003   2855 	add	r1,sp,3

000010c8 e1a00004   2856 	mov	r0,r4


                                                                      Page 48
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
000010cc eb000000*  2857 	bl	BufferView_peekTag

                    2858 ;1007:     {


                    2859 

                    2860 ;1008:         uint8_t objInfoTag;


                    2861 ;1009:         if(!BufferView_peekTag(subObjects, &objInfoTag))


                    2862 

000010d0 e3500000   2863 	cmp	r0,0

                    2864 ;1010:         {


                    2865 

                    2866 ;1011:             return false;


                    2867 

000010d4 0a00000c   2868 	beq	.L3417

                    2869 ;1012:         }


                    2870 ;1013: 


                    2871 ;1014:         if(!IEDModel_isServiceInfo(objInfoTag))


                    2872 

000010d8 e5dd5003   2873 	ldrb	r5,[sp,3]

000010dc e1a00005   2874 	mov	r0,r5

000010e0 ebfffc3b*  2875 	bl	IEDModel_isServiceInfo

000010e4 e3500000   2876 	cmp	r0,0

000010e8 0a000006   2877 	beq	.L3427

                    2878 ;1015:         {


                    2879 

                    2880 ;1016:             break;


                    2881 

                    2882 ;1017:         }


                    2883 ;1018:         BufferView_skipObject(subObjects, objInfoTag, false);


                    2884 

000010ec e1a01005   2885 	mov	r1,r5

000010f0 e1a00004   2886 	mov	r0,r4

000010f4 e3a02000   2887 	mov	r2,0

000010f8 eb000000*  2888 	bl	BufferView_skipObject

000010fc e9940003   2889 	ldmed	[r4],{r0-r1}

00001100 e1500001   2890 	cmp	r0,r1

00001104 1affffee   2891 	bne	.L3421

                    2892 .L3427:

                    2893 ;1019:     }


                    2894 ;1020:     return true;


                    2895 

00001108 e3a00001   2896 	mov	r0,1

                    2897 .L3417:

0000110c e28dd004   2898 	add	sp,sp,4

00001110 e8bd8030   2899 	ldmfd	[sp]!,{r4-r5,pc}

                    2900 	.endf	IEDModel_skipServiceInfo

                    2901 	.align	4

                    2902 ;objInfoTag	[sp,3]	local

                    2903 

                    2904 ;subObjects	r4	param

                    2905 

                    2906 	.section ".bss","awb"

                    2907 .L3518:

                    2908 	.data

                    2909 	.text

                    2910 

                    2911 ;1021: }


                    2912 

                    2913 ;1022: 


                    2914 ;1023: 


                    2915 ;1024: bool IEDModel_getChildren(const BufferView* berObject,  BufferView* children)


                    2916 	.align	4

                    2917 	.align	4


                                                                      Page 49
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
                    2918 IEDModel_getChildren::

00001114 e92d4010   2919 	stmfd	[sp]!,{r4,lr}

00001118 e24dd004   2920 	sub	sp,sp,4

0000111c e1a04001   2921 	mov	r4,r1

                    2922 ;1025: {


                    2923 

                    2924 ;1026:     size_t len;


                    2925 ;1027:     *children = *berObject;


                    2926 

00001120 e8900007   2927 	ldmfd	[r0],{r0-r2}

00001124 e8840007   2928 	stmea	[r4],{r0-r2}

                    2929 ;1028:     if(!BufferView_decodeTL(children, NULL, &len, NULL))


                    2930 

00001128 e1a0200d   2931 	mov	r2,sp

0000112c e1a00004   2932 	mov	r0,r4

00001130 e3a03000   2933 	mov	r3,0

00001134 e1a01003   2934 	mov	r1,r3

00001138 eb000000*  2935 	bl	BufferView_decodeTL

0000113c e3500000   2936 	cmp	r0,0

00001140 0a000009   2937 	beq	.L3548

                    2938 ;1029:     {


                    2939 

                    2940 ;1030:         return false;


                    2941 

                    2942 ;1031:     }


                    2943 ;1032: 


                    2944 ;1033:     BufferView_init(children, children->p + children->pos, len, 0);


                    2945 

00001144 e59d2000   2946 	ldr	r2,[sp]

00001148 e894000a   2947 	ldmfd	[r4],{r1,r3}

0000114c e1a00004   2948 	mov	r0,r4

00001150 e0831001   2949 	add	r1,r3,r1

00001154 e3a03000   2950 	mov	r3,0

00001158 eb000000*  2951 	bl	BufferView_init

                    2952 ;1034: 


                    2953 ;1035:     if(!IEDModel_skipServiceInfo(children))


                    2954 

0000115c e1a00004   2955 	mov	r0,r4

00001160 ebffffd1*  2956 	bl	IEDModel_skipServiceInfo

00001164 e3500000   2957 	cmp	r0,0

                    2958 ;1038:     }


                    2959 ;1039:     return true;


                    2960 

00001168 13a00001   2961 	movne	r0,1

                    2962 .L3548:

                    2963 ;1036:     {


                    2964 

                    2965 ;1037:         return false;


                    2966 

0000116c 03a00000   2967 	moveq	r0,0

                    2968 .L3542:

00001170 e28dd004   2969 	add	sp,sp,4

00001174 e8bd8010   2970 	ldmfd	[sp]!,{r4,pc}

                    2971 	.endf	IEDModel_getChildren

                    2972 	.align	4

                    2973 ;len	[sp]	local

                    2974 

                    2975 ;berObject	r0	param

                    2976 ;children	r4	param

                    2977 

                    2978 	.section ".bss","awb"


                                                                      Page 50
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
                    2979 .L3596:

                    2980 	.data

                    2981 	.text

                    2982 

                    2983 ;1040: }


                    2984 

                    2985 ;1041: 


                    2986 ;1042: bool IEDModel_getTermItemDescrStruct(BufferView* descrObject,


                    2987 	.align	4

                    2988 	.align	4

                    2989 IEDModel_getTermItemDescrStruct::

00001178 e92d4070   2990 	stmfd	[sp]!,{r4-r6,lr}

0000117c e24dd008   2991 	sub	sp,sp,8

00001180 e1a04000   2992 	mov	r4,r0

00001184 e1a06001   2993 	mov	r6,r1

                    2994 ;1043:                                     void** pDescrStruct)


                    2995 ;1044: {


                    2996 

                    2997 ;1045:     //Какое смещение использовано для выравнивания структуры описания


                    2998 ;1046:     uint8_t* pDescrStructAlignOffset;


                    2999 ;1047:     uint8_t descrTag;


                    3000 ;1048:     size_t descrLen;


                    3001 ;1049:     //Получаем указатель на структуру описания


                    3002 ;1050:     if(BufferView_endOfBuf(descrObject))


                    3003 

00001188 e9940003   3004 	ldmed	[r4],{r0-r1}

0000118c e1500001   3005 	cmp	r0,r1

00001190 0a00001a   3006 	beq	.L3632

                    3007 ;1051:     {


                    3008 

                    3009 ;1052:         return false;


                    3010 

                    3011 ;1053:     }


                    3012 ;1054:     if(!BufferView_decodeTL(descrObject, &descrTag, &descrLen, NULL))


                    3013 

00001194 e28d2004   3014 	add	r2,sp,4

00001198 e28d1003   3015 	add	r1,sp,3

0000119c e1a00004   3016 	mov	r0,r4

000011a0 e3a03000   3017 	mov	r3,0

000011a4 eb000000*  3018 	bl	BufferView_decodeTL

000011a8 e3500000   3019 	cmp	r0,0

000011ac 0a000013   3020 	beq	.L3632

                    3021 ;1055:     {


                    3022 

                    3023 ;1056:         return false;


                    3024 

                    3025 ;1057:     }


                    3026 ;1058:     if (descrTag != ASN_OCTET_STRING)


                    3027 

000011b0 e5dd0003   3028 	ldrb	r0,[sp,3]

000011b4 e3500004   3029 	cmp	r0,4

000011b8 1a000010   3030 	bne	.L3632

                    3031 ;1059:     {


                    3032 

                    3033 ;1060:         ERROR_REPORT("Invalid tag");


                    3034 ;1061:         return false;


                    3035 

                    3036 ;1062:     };


                    3037 ;1063:     //Получаем структуру описания с учётом выравнивания


                    3038 ;1064:     pDescrStructAlignOffset = descrObject->p + descrObject->pos;


                    3039 


                                                                      Page 51
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
000011bc e8940021   3040 	ldmfd	[r4],{r0,r5}

                    3041 ;1065:     if(*pDescrStructAlignOffset >= 4)


                    3042 

000011c0 e7f51000   3043 	ldrb	r1,[r5,r0]!

000011c4 e3510004   3044 	cmp	r1,4

000011c8 2a00000c   3045 	bhs	.L3632

                    3046 ;1066:     {


                    3047 

                    3048 ;1067:         ERROR_REPORT("Invalid alignment");


                    3049 ;1068:         return false;


                    3050 

                    3051 ;1069:     }


                    3052 ;1070: 


                    3053 ;1071:     if(!BufferView_advance(descrObject, descrLen))


                    3054 

000011cc e59d1004   3055 	ldr	r1,[sp,4]

000011d0 e1a00004   3056 	mov	r0,r4

000011d4 eb000000*  3057 	bl	BufferView_advance

000011d8 e3500000   3058 	cmp	r0,0

000011dc 0a000007   3059 	beq	.L3632

                    3060 ;1072:     {


                    3061 

                    3062 ;1073:         ERROR_REPORT("Invalid object length");


                    3063 ;1074:         return false;


                    3064 

                    3065 ;1075:     }


                    3066 ;1076: 


                    3067 ;1077:     *pDescrStruct = pDescrStructAlignOffset + *pDescrStructAlignOffset + 1;


                    3068 

000011e0 e5d50000   3069 	ldrb	r0,[r5]

000011e4 e0800005   3070 	add	r0,r0,r5

000011e8 e2800001   3071 	add	r0,r0,1

000011ec e5860000   3072 	str	r0,[r6]

                    3073 ;1078:     if(( ((uint32_t)(*pDescrStruct)) & 3) != 0)


                    3074 

                    3075 

                    3076 

                    3077 

000011f0 e3100003   3078 	tst	r0,3

000011f4 03a00001   3079 	moveq	r0,1

000011f8 13a00000   3080 	movne	r0,0

000011fc ea000000   3081 	b	.L3613

                    3082 .L3632:

                    3083 ;1079:     {


                    3084 

                    3085 ;1080:         ERROR_REPORT("Invalid alignment");


                    3086 ;1081:         return false;


                    3087 

00001200 e3a00000   3088 	mov	r0,0

                    3089 .L3613:

00001204 e28dd008   3090 	add	sp,sp,8

00001208 e8bd8070   3091 	ldmfd	[sp]!,{r4-r6,pc}

                    3092 	.endf	IEDModel_getTermItemDescrStruct

                    3093 	.align	4

                    3094 ;pDescrStructAlignOffset	r5	local

                    3095 ;descrTag	[sp,3]	local

                    3096 ;descrLen	[sp,4]	local

                    3097 

                    3098 ;descrObject	r4	param

                    3099 ;pDescrStruct	r6	param

                    3100 


                                                                      Page 52
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
                    3101 	.section ".bss","awb"

                    3102 .L3728:

                    3103 	.data

                    3104 	.text

                    3105 

                    3106 ;1084: }


                    3107 

                    3108 ;1085: 


                    3109 ;1086: bool IEDModel_getBufferView(size_t pos, BufferView* bv)


                    3110 	.align	4

                    3111 	.align	4

                    3112 IEDModel_getBufferView::

0000120c e92d4000   3113 	stmfd	[sp]!,{lr}

00001210 e1a0c001   3114 	mov	r12,r1

                    3115 ;1087: {


                    3116 

                    3117 ;1088:     if(pos > (size_t)iedModelSize)


                    3118 

00001214 e59f1028*  3119 	ldr	r1,.L2392

00001218 e5912000   3120 	ldr	r2,[r1]

0000121c e1500002   3121 	cmp	r0,r2

                    3122 ;1089:     {


                    3123 

                    3124 ;1090:         return false;


                    3125 

00001220 83a00000   3126 	movhi	r0,0

00001224 8a000005   3127 	bhi	.L3747

                    3128 ;1091:     }


                    3129 ;1092:     BufferView_init(bv, iedModel, iedModelSize, pos);


                    3130 

00001228 e1a03000   3131 	mov	r3,r0

0000122c e59f0014*  3132 	ldr	r0,.L2393

00001230 e5901000   3133 	ldr	r1,[r0]

00001234 e1a0000c   3134 	mov	r0,r12

00001238 eb000000*  3135 	bl	BufferView_init

                    3136 ;1093:     return true;


                    3137 

0000123c e3a00001   3138 	mov	r0,1

                    3139 .L3747:

00001240 e8bd8000   3140 	ldmfd	[sp]!,{pc}

                    3141 	.endf	IEDModel_getBufferView

                    3142 	.align	4

                    3143 

                    3144 ;pos	r0	param

                    3145 ;bv	r12	param

                    3146 

                    3147 	.section ".bss","awb"

                    3148 .L3782:

                    3149 	.data

                    3150 	.text

                    3151 

                    3152 ;1094: }


                    3153 	.align	4

                    3154 .L2392:

00001244 00000000*  3155 	.data.w	iedModelSize

                    3156 	.type	.L2392,$object

                    3157 	.size	.L2392,4

                    3158 

                    3159 .L2393:

00001248 00000000*  3160 	.data.w	iedModel

                    3161 	.type	.L2393,$object


                                                                      Page 53
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_es81.s
                    3162 	.size	.L2393,4

                    3163 

                    3164 	.align	4

                    3165 

                    3166 	.data

                    3167 .L3812:

                    3168 	.globl	defaultIed

00000000 031a3aea   3169 defaultIed:	.data.b	234,58,26,3

00000004 e2444549   3170 	.data.b	73,69,68,226

00000008 4c071a33   3171 	.data.b	51,26,7,76

0000000c 69766544   3172 	.data.b	68,101,118,105

00000010 28e46563   3173 	.data.b	99,101,228,40

00000014 4d4d051a   3174 	.data.b	26,5,77,77

00000018 e6305558   3175 	.data.b	88,85,48,230

0000001c 54041a1f   3176 	.data.b	31,26,4,84

00000020 e857746f   3177 	.data.b	111,116,87,232

00000024 6d031a17   3178 	.data.b	23,26,3,109

00000028 10e96761   3179 	.data.b	97,103,233,16

0000002c 0266011a   3180 	.data.b	26,1,102,2

00000030 08040201   3181 	.data.b	1,2,4,8

00000034 c0        3182 	.data.b	192

00000035 00        3183 	.space	1

00000036 00        3184 	.space	1

00000037 00        3185 	.space	1

00000038 35311acf   3186 	.data.b	207,26,49,53

                    3187 	.type	defaultIed,$object

                    3188 	.size	defaultIed,60

                    3189 .L3813:

                    3190 	.globl	iedModel

0000003c 00000000*  3191 iedModel:	.data.w	.L3812

                    3192 	.type	iedModel,$object

                    3193 	.size	iedModel,4

                    3194 .L3814:

                    3195 	.globl	iedModelSize

00000040 0000003c   3196 iedModelSize:	.data.b	60,0,0,0

                    3197 	.type	iedModelSize,$object

                    3198 	.size	iedModelSize,4

                    3199 	.ghsnote version,6

                    3200 	.ghsnote tools,3

                    3201 	.ghsnote options,0

                    3202 	.text

                    3203 	.align	4

                    3204 	.data

                    3205 	.align	4

                    3206 	.text

