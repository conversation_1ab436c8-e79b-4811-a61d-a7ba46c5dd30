                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2181.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=FinalDA.c -o gh_2181.o -list=FinalDA.lst C:\Users\<USER>\AppData\Local\Temp\gh_2181.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_2181.s
Source File: FinalDA.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile FinalDA.c -o

                      10 ;		FinalDA.o

                      11 ;Source File:   FinalDA.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Oct 07 09:39:27 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: //В этом файле FinalDA исключительно для GOOSE


                      21 ;2: 


                      22 ;3: #include "FinalDA.h"


                      23 ;4: #include "MemoryManager.h"


                      24 ;5: #include "IEDCompile/AccessInfo.h"


                      25 ;6: #include "IEDCompile/InnerAttributeTypes.h"


                      26 ;7: #include "iedmodel.h"


                      27 ;8: #include "BaseAsnTypes.h"


                      28 ;9: #include "AsnEncoding.h"


                      29 ;10: #include "mms_data.h"


                      30 ;11: #include "DataSlice.h"


                      31 ;12: #include <debug.h>


                      32 ;13: #include <stdint.h>


                      33 ;14: 


                      34 ;15: #define QUALITY_ENCODED_SIZE 5


                      35 ;16: #define CODED_ENUM_ENCODED_SIZE 4


                      36 ;17: 


                      37 ;18: struct FDAQualityStruct


                      38 ;19: {


                      39 ;20: 	enum InnerAttributeType attrType;


                      40 ;21:     QualityAccsessInfo accessInfo;


                      41 ;22: 	uint16_t value;


                      42 ;23: 	uint8_t* fixedEncodeBuf;


                      43 ;24: };


                      44 ;25: 


                      45 ;26: typedef struct FDAQualityStruct* FDAQuality;


                      46 ;27: 


                      47 ;28: static void FDAQuality_init(FDAQuality q, QualityAccsessInfo* accessInfo)


                      48 

                      49 ;59:             DataSlice_getBoolOffset(info->operatorBlockedOffset);


                      50 ;60: }



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2181.s
                      51 

                      52 ;61: 


                      53 ;62: FDAQuality FDAQuality_create(QualityAccsessInfo* accessInfo)


                      54 ;63: {


                      55 ;64: 	FDAQuality q = MM_alloc(sizeof(struct FDAQualityStruct));


                      56 ;65: 	if (q == NULL)


                      57 ;66: 	{


                      58 ;67: 		return NULL;


                      59 ;68: 	}


                      60 ;69: 	FDAQuality_init(q, accessInfo);


                      61 ;70: 	return q;


                      62 ;71: }


                      63 ;72: 


                      64 ;73: bool FDAQuality_getFixedEncodedSize(FDAQuality q, size_t* size)


                      65 ;74: {


                      66 ;75: 	*size = QUALITY_ENCODED_SIZE;


                      67 ;76: 	return TRUE;


                      68 ;77: }


                      69 ;78: 


                      70 ;79: bool FDAQuality_encodeGOOSETemplate(FDAQuality q, BufferView* outBuf)


                      71 ;80: {


                      72 ;81: 	uint16_t dummy = 0;


                      73 ;82: 	uint8_t *pEncodeBuf;	


                      74 ;83: 


                      75 ;84: 	if (!BufferView_alloc(outBuf, QUALITY_ENCODED_SIZE, &pEncodeBuf))


                      76 ;85: 	{


                      77 ;86: 		ERROR_REPORT("Trouble creating Quality template");


                      78 ;87: 		return FALSE;


                      79 ;88: 	}


                      80 ;89: 


                      81 ;90: 	q->fixedEncodeBuf = pEncodeBuf;


                      82 ;91: 


                      83 ;92: 	BerEncoder_encodeBitStringUshortBuf(ASN_TYPEDESCRIPTION_BIT_STRING,


                      84 ;93: 		13, dummy, pEncodeBuf, 0);


                      85 ;94: 	if (!BufferView_advance(outBuf, QUALITY_ENCODED_SIZE))


                      86 ;95: 	{


                      87 ;96: 		ERROR_REPORT("Trouble creating Quality template");


                      88 ;97: 		return FALSE;


                      89 ;98: 	}


                      90 ;99: 	return TRUE;


                      91 ;100: }


                      92 ;101: 


                      93 ;102: 


                      94 ;103: bool FDAQuality_readAndCompare(FDAQuality q, void* dataSliceWnd,


                      95 ;104:                                  bool* changed)


                      96 ;105: {


                      97 ;106:     uint16_t newValue = qualityFromBitsFast(dataSliceWnd, &q->accessInfo);


                      98 ;107: 	*changed = (q->value != newValue);


                      99 ;108: 	q->value = newValue;


                     100 ;109: 	return TRUE;


                     101 ;110: }


                     102 ;111: 


                     103 ;112: 


                     104 ;113: void FDAQuality_encodeFixedData(FDAQuality q)


                     105 ;114: {


                     106 ;115: 	BerEncoder_encodeBitStringUshortBuf(ASN_TYPEDESCRIPTION_BIT_STRING,


                     107 ;116: 		13, q->value, q->fixedEncodeBuf, 0);


                     108 ;117: }


                     109 ;118: 


                     110 ;119: //=========================Boolean===================================


                     111 ;120: struct FDABooleanStruct



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2181.s
                     112 ;121: {


                     113 ;122: 	enum InnerAttributeType attrType;


                     114 ;123:     IntBoolAccessInfo* accessInfo;


                     115 ;124:     //Смещение в окне DataSlice


                     116 ;125:     int dataSliceOffset;


                     117 ;126: 	bool value;


                     118 ;127: 	uint8_t* fixedEncodeBuf;


                     119 ;128: };


                     120 ;129: 


                     121 ;130: typedef struct FDABooleanStruct* FDABoolean;


                     122 ;131: 


                     123 ;132: 


                     124 ;133: static void FDABoolean_init(FDABoolean daBool, IntBoolAccessInfo* accessInfo)


                     125 

                     126 ;139: }


                     127 

                     128 ;140: 


                     129 ;141: static FDABoolean FDABoolean_create(IntBoolAccessInfo* accessInfo)


                     130 

                     131 ;150: }


                     132 

                     133 ;151: 


                     134 ;152: static bool FDABoolean_getFixedEncodedSize(FDABoolean da, size_t* size)


                     135 

                     136 ;156: }


                     137 

                     138 ;157: 


                     139 ;158: static bool FDABoolean_encodeGOOSETemplate(FDABoolean da, BufferView* outBuf)


                     140 

                     141 ;168: }


                     142 

                     143 ;169: 


                     144 ;170: static bool FDABoolean_readAndCompare(FDABoolean da,


                     145 

                     146 ;183: }


                     147 

                     148 ;184: 


                     149 ;185: static void FDABoolean_encodeFixedData(FDABoolean da)


                     150 

                     151 ;188: }


                     152 

                     153 ;189: 


                     154 ;190: //================================CODEDENUM===================================


                     155 ;191: //Поддерживает только два бита. Например, DPS.stVal


                     156 ;192: 


                     157 ;193: struct FDACodedEnumStruct


                     158 ;194: {


                     159 ;195: 	enum InnerAttributeType attrType;


                     160 ;196: 	CodedEnumAccessInfo* accessInfo;	


                     161 ;197:     //Смещения в окне DataSlice


                     162 ;198:     int dataSliceOffset0;


                     163 ;199:     int dataSliceOffset1;


                     164 ;200: 	uint8_t value;


                     165 ;201: 	uint8_t* fixedEncodeBuf;


                     166 ;202: };


                     167 ;203: 


                     168 ;204: typedef struct FDACodedEnumStruct* FDACodedEnum;


                     169 ;205: 


                     170 ;206: static void FDACodedEnum_init(FDACodedEnum da, CodedEnumAccessInfo* accessInfo)


                     171 

                     172 ;212: }



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2181.s
                     173 

                     174 ;213: 


                     175 ;214: static FDACodedEnum FDACodedEnum_create(CodedEnumAccessInfo* accessInfo)


                     176 

                     177 ;230: }


                     178 

                     179 ;231: 


                     180 ;232: static bool FDACodedEnum_getFixedEncodedSize(FDACodedEnum da, size_t* size)


                     181 

                     182 ;236: }


                     183 

                     184 ;237: 


                     185 ;238: static bool FDACodedEnum_encodeGOOSETemplate(FDACodedEnum da, BufferView* outBuf)


                     186 

                     187 ;259: }


                     188 

                     189 ;260: 


                     190 ;261: static bool FDACodedEnum_readAndCompare(FDACodedEnum da,


                     191 

                     192 ;279: }


                     193 

                     194 ;280: 


                     195 ;281: 


                     196 ;282: static void FDACodedEnum_encodeFixedData(FDACodedEnum da)


                     197 

                     198 ;285: 		2, da->value, da->fixedEncodeBuf, 0);


                     199 ;286: }


                     200 

                     201 	.text

                     202 	.align	4

                     203 FDAQuality_create::

00000000 e92d4070    204 	stmfd	[sp]!,{r4-r6,lr}

00000004 e1a04000    205 	mov	r4,r0

00000008 e3a00048    206 	mov	r0,72

0000000c eb000000*   207 	bl	MM_alloc

00000010 e1b05000    208 	movs	r5,r0

00000014 0a000031    209 	beq	.L257

                     210 ;29: {	


                     211 

                     212 ;30:     QualityAccsessInfo* info = &q->accessInfo;


                     213 

00000018 e2856004    214 	add	r6,r5,4

                     215 ;31: 	q->attrType = INNER_TYPE_QUALITY;


                     216 

0000001c e3a00000    217 	mov	r0,0

00000020 e5850000    218 	str	r0,[r5]

                     219 ;32:     *info = *accessInfo;


                     220 

00000024 e8b4000f    221 	ldmfd	[r4]!,{r0-r3}

00000028 e8a6000f    222 	stmea	[r6]!,{r0-r3}

0000002c e8b4000f    223 	ldmfd	[r4]!,{r0-r3}

00000030 e8a6000f    224 	stmea	[r6]!,{r0-r3}

00000034 e8b4000f    225 	ldmfd	[r4]!,{r0-r3}

00000038 e8a6000f    226 	stmea	[r6]!,{r0-r3}

0000003c e8940007    227 	ldmfd	[r4],{r0-r2}

00000040 e8860007    228 	stmea	[r6],{r0-r2}

                     229 ;33:     //Заменяем абсолютные смещения на смещения в DataSlice


                     230 ;34:     info->goodInvalidOffset =


                     231 

00000044 e5950008    232 	ldr	r0,[r5,8]

00000048 eb000000*   233 	bl	DataSlice_getBoolOffset


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2181.s
0000004c e5850008    234 	str	r0,[r5,8]

                     235 ;35:             DataSlice_getBoolOffset(info->goodInvalidOffset);


                     236 ;36:     info->reservedQuestionableOffset =


                     237 

00000050 e595000c    238 	ldr	r0,[r5,12]

00000054 eb000000*   239 	bl	DataSlice_getBoolOffset

00000058 e585000c    240 	str	r0,[r5,12]

                     241 ;37:             DataSlice_getBoolOffset(info->reservedQuestionableOffset);


                     242 ;38:     info->overflowOffset =


                     243 

0000005c e5950010    244 	ldr	r0,[r5,16]

00000060 eb000000*   245 	bl	DataSlice_getBoolOffset

00000064 e5850010    246 	str	r0,[r5,16]

                     247 ;39:             DataSlice_getBoolOffset(info->overflowOffset);


                     248 ;40:     info->outOfRangeOffset =


                     249 

00000068 e5950014    250 	ldr	r0,[r5,20]

0000006c eb000000*   251 	bl	DataSlice_getBoolOffset

00000070 e5850014    252 	str	r0,[r5,20]

                     253 ;41:             DataSlice_getBoolOffset(info->outOfRangeOffset);


                     254 ;42:     info->badReferenceOffset =


                     255 

00000074 e5950018    256 	ldr	r0,[r5,24]

00000078 eb000000*   257 	bl	DataSlice_getBoolOffset

0000007c e5850018    258 	str	r0,[r5,24]

                     259 ;43:             DataSlice_getBoolOffset(info->badReferenceOffset);


                     260 ;44:     info->oscillatoryOffset =


                     261 

00000080 e595001c    262 	ldr	r0,[r5,28]

00000084 eb000000*   263 	bl	DataSlice_getBoolOffset

00000088 e585001c    264 	str	r0,[r5,28]

                     265 ;45:             DataSlice_getBoolOffset(info->oscillatoryOffset);


                     266 ;46:     info->failureOffset =


                     267 

0000008c e5950020    268 	ldr	r0,[r5,32]

00000090 eb000000*   269 	bl	DataSlice_getBoolOffset

00000094 e5850020    270 	str	r0,[r5,32]

                     271 ;47:             DataSlice_getBoolOffset(info->failureOffset);


                     272 ;48:     info->oldDataOffset =


                     273 

00000098 e5950024    274 	ldr	r0,[r5,36]

0000009c eb000000*   275 	bl	DataSlice_getBoolOffset

000000a0 e5850024    276 	str	r0,[r5,36]

                     277 ;49:             DataSlice_getBoolOffset(info->oldDataOffset);


                     278 ;50:     info->inconsistentOffset =


                     279 

000000a4 e5950028    280 	ldr	r0,[r5,40]

000000a8 eb000000*   281 	bl	DataSlice_getBoolOffset

000000ac e5850028    282 	str	r0,[r5,40]

                     283 ;51:             DataSlice_getBoolOffset(info->inconsistentOffset);


                     284 ;52:     info->inaccurateOffset =


                     285 

000000b0 e595002c    286 	ldr	r0,[r5,44]

000000b4 eb000000*   287 	bl	DataSlice_getBoolOffset

000000b8 e585002c    288 	str	r0,[r5,44]

                     289 ;53:             DataSlice_getBoolOffset(info->inaccurateOffset);


                     290 ;54:     info->processSubstitutedOffset =


                     291 

000000bc e5950030    292 	ldr	r0,[r5,48]

000000c0 eb000000*   293 	bl	DataSlice_getBoolOffset

000000c4 e5850030    294 	str	r0,[r5,48]


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2181.s
                     295 ;55:             DataSlice_getBoolOffset(info->processSubstitutedOffset);


                     296 ;56:     info->testOffset =


                     297 

000000c8 e5950034    298 	ldr	r0,[r5,52]

000000cc eb000000*   299 	bl	DataSlice_getBoolOffset

000000d0 e5850034    300 	str	r0,[r5,52]

                     301 ;57:             DataSlice_getBoolOffset(info->testOffset);


                     302 ;58:     info->operatorBlockedOffset =


                     303 

000000d4 e5950038    304 	ldr	r0,[r5,56]

000000d8 eb000000*   305 	bl	DataSlice_getBoolOffset

000000dc e5850038    306 	str	r0,[r5,56]

                     307 .L257:

000000e0 e1a00005    308 	mov	r0,r5

000000e4 e8bd8070    309 	ldmfd	[sp]!,{r4-r6,pc}

                     310 	.endf	FDAQuality_create

                     311 	.align	4

                     312 ;q	r5	local

                     313 

                     314 ;accessInfo	r4	param

                     315 

                     316 	.section ".bss","awb"

                     317 .L280:

                     318 	.data

                     319 	.text

                     320 

                     321 

                     322 	.align	4

                     323 	.align	4

                     324 FDAQuality_getFixedEncodedSize::

000000e8 e3a00005    325 	mov	r0,5

000000ec e5810000    326 	str	r0,[r1]

000000f0 e3a00001    327 	mov	r0,1

000000f4 e12fff1e*   328 	ret	

                     329 	.endf	FDAQuality_getFixedEncodedSize

                     330 	.align	4

                     331 

                     332 ;q	none	param

                     333 ;size	r1	param

                     334 

                     335 	.section ".bss","awb"

                     336 .L318:

                     337 	.data

                     338 	.text

                     339 

                     340 

                     341 	.align	4

                     342 	.align	4

                     343 FDAQuality_encodeGOOSETemplate::

000000f8 e92d4030    344 	stmfd	[sp]!,{r4-r5,lr}

000000fc e24dd008    345 	sub	sp,sp,8

00000100 e28d2004    346 	add	r2,sp,4

00000104 e1a05000    347 	mov	r5,r0

00000108 e1a04001    348 	mov	r4,r1

0000010c e1a00004    349 	mov	r0,r4

00000110 e3a01005    350 	mov	r1,5

00000114 eb000000*   351 	bl	BufferView_alloc

00000118 e3500000    352 	cmp	r0,0

0000011c 0a00000b    353 	beq	.L331

00000120 e59d3004    354 	ldr	r3,[sp,4]

00000124 e3a02000    355 	mov	r2,0


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2181.s
00000128 e5853044    356 	str	r3,[r5,68]

0000012c e58d2000    357 	str	r2,[sp]

00000130 e3a0100d    358 	mov	r1,13

00000134 e3a00084    359 	mov	r0,132

00000138 eb000000*   360 	bl	BerEncoder_encodeBitStringUshortBuf

0000013c e1a00004    361 	mov	r0,r4

00000140 e3a01005    362 	mov	r1,5

00000144 eb000000*   363 	bl	BufferView_advance

00000148 e3500000    364 	cmp	r0,0

0000014c 13a00001    365 	movne	r0,1

                     366 .L331:

00000150 03a00000    367 	moveq	r0,0

                     368 .L325:

00000154 e28dd008    369 	add	sp,sp,8

00000158 e8bd8030    370 	ldmfd	[sp]!,{r4-r5,pc}

                     371 	.endf	FDAQuality_encodeGOOSETemplate

                     372 	.align	4

                     373 ;pEncodeBuf	[sp,4]	local

                     374 

                     375 ;q	r5	param

                     376 ;outBuf	r4	param

                     377 

                     378 	.section ".bss","awb"

                     379 .L380:

                     380 	.data

                     381 	.text

                     382 

                     383 

                     384 	.align	4

                     385 	.align	4

                     386 FDAQuality_readAndCompare::

0000015c e92d4030    387 	stmfd	[sp]!,{r4-r5,lr}

00000160 e1a05002    388 	mov	r5,r2

00000164 e1a04000    389 	mov	r4,r0

00000168 e1a00001    390 	mov	r0,r1

0000016c e2841004    391 	add	r1,r4,4

00000170 eb000000*   392 	bl	qualityFromBitsFast

00000174 e1d414b0    393 	ldrh	r1,[r4,64]

00000178 e0511000    394 	subs	r1,r1,r0

0000017c 13a01001    395 	movne	r1,1

00000180 e5c51000    396 	strb	r1,[r5]

00000184 e1c404b0    397 	strh	r0,[r4,64]

00000188 e3a00001    398 	mov	r0,1

0000018c e8bd8030    399 	ldmfd	[sp]!,{r4-r5,pc}

                     400 	.endf	FDAQuality_readAndCompare

                     401 	.align	4

                     402 

                     403 ;q	r4	param

                     404 ;dataSliceWnd	r3	param

                     405 ;changed	r5	param

                     406 

                     407 	.section ".bss","awb"

                     408 .L414:

                     409 	.data

                     410 	.text

                     411 

                     412 

                     413 	.align	4

                     414 	.align	4

                     415 FDAQuality_encodeFixedData::

00000190 e92d4000    416 	stmfd	[sp]!,{lr}


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2181.s
00000194 e3a01000    417 	mov	r1,0

00000198 e52d1004    418 	str	r1,[sp,-4]!

0000019c e3a0100d    419 	mov	r1,13

000001a0 e5903044    420 	ldr	r3,[r0,68]

000001a4 e1d024b0    421 	ldrh	r2,[r0,64]

000001a8 e3a00084    422 	mov	r0,132

000001ac eb000000*   423 	bl	BerEncoder_encodeBitStringUshortBuf

000001b0 e28dd004    424 	add	sp,sp,4

000001b4 e8bd8000    425 	ldmfd	[sp]!,{pc}

                     426 	.endf	FDAQuality_encodeFixedData

                     427 	.align	4

                     428 

                     429 ;q	r0	param

                     430 

                     431 	.section ".bss","awb"

                     432 .L446:

                     433 	.data

                     434 	.text

                     435 

                     436 

                     437 ;287: 


                     438 ;288: //===================================================================


                     439 ;289: 


                     440 ;290: void* FDA_create(enum InnerAttributeType attrType, void* accessInfo)


                     441 	.align	4

                     442 	.align	4

                     443 FDA_create::

000001b8 e92d4030    444 	stmfd	[sp]!,{r4-r5,lr}

                     445 ;291: {	


                     446 

                     447 ;292: 	switch (attrType)


                     448 

000001bc e1a05001    449 	mov	r5,r1

000001c0 e2500000    450 	subs	r0,r0,0

000001c4 0a000005    451 	beq	.L457

000001c8 e2500006    452 	subs	r0,r0,6

000001cc 0a000006    453 	beq	.L462

000001d0 e3500003    454 	cmp	r0,3

                     455 ;300: 	default:


                     456 ;301:         ERROR_REPORT("GOOSE: Unsupported DA type");


                     457 ;302: 		return NULL;


                     458 

000001d4 13a00000    459 	movne	r0,0

000001d8 1a000020    460 	bne	.L453

000001dc ea00000d    461 	b	.L470

                     462 .L457:

                     463 ;293: 	{


                     464 ;294: 	case INNER_TYPE_QUALITY:


                     465 ;295: 		return FDAQuality_create(accessInfo);


                     466 

000001e0 e1a00005    467 	mov	r0,r5

000001e4 e8bd4030    468 	ldmfd	[sp]!,{r4-r5,lr}

000001e8 eaffff84*   469 	b	FDAQuality_create

                     470 .L462:

                     471 ;296: 	case INNER_TYPE_BOOLEAN:


                     472 ;297: 		return FDABoolean_create(accessInfo);


                     473 

                     474 ;142: {


                     475 

                     476 ;143: 	FDABoolean da = MM_alloc(sizeof(struct FDABooleanStruct));


                     477 


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2181.s
000001ec e3a00014    478 	mov	r0,20

000001f0 eb000000*   479 	bl	MM_alloc

000001f4 e1b04000    480 	movs	r4,r0

                     481 ;144: 	if (da == NULL)


                     482 

000001f8 0a000004    483 	beq	.L460

                     484 ;145: 	{


                     485 

                     486 ;146: 		return NULL;


                     487 

                     488 ;147: 	}


                     489 ;148: 	FDABoolean_init(da, accessInfo);


                     490 

                     491 ;134: {


                     492 

                     493 ;135: 	daBool->attrType = INNER_TYPE_BOOLEAN;


                     494 

000001fc e3a00006    495 	mov	r0,6

00000200 e8840021    496 	stmea	[r4],{r0,r5}

                     497 ;136:     daBool->accessInfo = accessInfo;


                     498 

                     499 ;137: 


                     500 ;138:     daBool->dataSliceOffset = DataSlice_getBoolOffset(accessInfo->valueOffset);


                     501 

00000204 e5950004    502 	ldr	r0,[r5,4]

00000208 eb000000*   503 	bl	DataSlice_getBoolOffset

0000020c e5840008    504 	str	r0,[r4,8]

                     505 ;149: 	return da;


                     506 

                     507 .L460:

00000210 e1a00004    508 	mov	r0,r4

00000214 ea000011    509 	b	.L453

                     510 .L470:

                     511 ;298: 	case INNER_TYPE_CODEDENUM:


                     512 ;299: 		return FDACodedEnum_create(accessInfo);


                     513 

                     514 ;215: {


                     515 

                     516 ;216: 	FDACodedEnum da;


                     517 ;217: 	if (accessInfo->bitCount != 2)


                     518 

00000218 e5950004    519 	ldr	r0,[r5,4]

0000021c e3500002    520 	cmp	r0,2

00000220 1a000003    521 	bne	.L473

                     522 ;218: 	{


                     523 

                     524 ;219: 		ERROR_REPORT("Only two-bit coded enum supported (like DPS.stVal)");


                     525 ;220: 		return NULL;


                     526 

                     527 ;221: 	}


                     528 ;222: 


                     529 ;223: 	da = MM_alloc(sizeof(struct FDACodedEnumStruct));


                     530 

00000224 e3a00018    531 	mov	r0,24

00000228 eb000000*   532 	bl	MM_alloc

0000022c e1b04000    533 	movs	r4,r0

                     534 ;224: 	if (da == NULL)


                     535 

00000230 1a000001    536 	bne	.L474

                     537 .L473:

                     538 ;225: 	{



                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2181.s
                     539 

                     540 ;226: 		return NULL;


                     541 

00000234 e3a00000    542 	mov	r0,0

00000238 ea000008    543 	b	.L453

                     544 .L474:

                     545 ;227: 	}


                     546 ;228: 	FDACodedEnum_init(da, accessInfo);


                     547 

                     548 ;207: {


                     549 

                     550 ;208: 	da->attrType = INNER_TYPE_CODEDENUM;


                     551 

0000023c e3a00009    552 	mov	r0,9

00000240 e8840021    553 	stmea	[r4],{r0,r5}

                     554 ;209: 	da->accessInfo = accessInfo;


                     555 

                     556 ;210:     da->dataSliceOffset0 = DataSlice_getBoolOffset(accessInfo->valueOffsets[0]);


                     557 

00000244 e5950008    558 	ldr	r0,[r5,8]

00000248 eb000000*   559 	bl	DataSlice_getBoolOffset

0000024c e5840008    560 	str	r0,[r4,8]

                     561 ;211:     da->dataSliceOffset1 = DataSlice_getBoolOffset(accessInfo->valueOffsets[1]);


                     562 

00000250 e595000c    563 	ldr	r0,[r5,12]

00000254 eb000000*   564 	bl	DataSlice_getBoolOffset

00000258 e584000c    565 	str	r0,[r4,12]

                     566 ;229: 	return da;


                     567 

0000025c e1a00004    568 	mov	r0,r4

                     569 .L453:

00000260 e8bd8030    570 	ldmfd	[sp]!,{r4-r5,pc}

                     571 	.endf	FDA_create

                     572 	.align	4

                     573 ;da	r4	local

                     574 ;da	r4	local

                     575 

                     576 ;attrType	r0	param

                     577 ;accessInfo	r5	param

                     578 

                     579 	.section ".bss","awb"

                     580 .L597:

                     581 	.data

                     582 	.text

                     583 

                     584 ;303: 	}	


                     585 ;304: }


                     586 

                     587 ;305: 


                     588 ;306: bool FDA_getFixedEncodedSize(void* da, size_t* size)


                     589 	.align	4

                     590 	.align	4

                     591 FDA_getFixedEncodedSize::

                     592 ;307: {


                     593 

                     594 ;308: 	enum InnerAttributeType* attrType = da;


                     595 

00000264 e1a02000    596 	mov	r2,r0

                     597 ;309: 


                     598 ;310: 	//Проверка на всякий случай


                     599 ;311: 	if ((((uint32_t)attrType) & 3) != 0)



                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2181.s
                     600 

00000268 e3120003    601 	tst	r2,3

0000026c 1a00000e    602 	bne	.L653

                     603 ;312: 	{


                     604 

                     605 ;313: 		ERROR_REPORT("Alignment error");


                     606 ;314: 		return false;


                     607 

                     608 ;315: 	}


                     609 ;316: 


                     610 ;317: 


                     611 ;318: 	switch (*attrType)


                     612 

00000270 e5922000    613 	ldr	r2,[r2]

00000274 e2522000    614 	subs	r2,r2,0

00000278 0a00000a    615 	beq	.L639

0000027c e2522006    616 	subs	r2,r2,6

                     617 ;322: 	case INNER_TYPE_BOOLEAN:


                     618 ;323: 		return FDABoolean_getFixedEncodedSize(da, size);


                     619 

                     620 ;153: {


                     621 

                     622 ;154: 	*size = 3;


                     623 

00000280 03a00003    624 	moveq	r0,3

00000284 05810000    625 	streq	r0,[r1]

                     626 ;155: 	return TRUE;


                     627 

00000288 03a00001    628 	moveq	r0,1

0000028c 0a000007    629 	beq	.L632

00000290 e3520003    630 	cmp	r2,3

                     631 ;324: 	case INNER_TYPE_CODEDENUM:


                     632 ;325: 		return FDACodedEnum_getFixedEncodedSize(da, size);


                     633 

                     634 ;233: {


                     635 

                     636 ;234: 	*size = CODED_ENUM_ENCODED_SIZE;


                     637 

00000294 03a00004    638 	moveq	r0,4

00000298 05810000    639 	streq	r0,[r1]

                     640 ;235: 	return TRUE;


                     641 

0000029c 03a00001    642 	moveq	r0,1

000002a0 0a000002    643 	beq	.L632

000002a4 ea000000    644 	b	.L653

                     645 .L639:

                     646 ;319: 	{


                     647 ;320: 	case INNER_TYPE_QUALITY:


                     648 ;321: 		return FDAQuality_getFixedEncodedSize(da, size);


                     649 

000002a8 eaffff8e*   650 	b	FDAQuality_getFixedEncodedSize

                     651 .L653:

                     652 ;326: 	default:


                     653 ;327: 		ERROR_REPORT("Unsupported attribute type");


                     654 ;328: 		return FALSE;


                     655 

000002ac e3a00000    656 	mov	r0,0

                     657 .L632:

000002b0 e12fff1e*   658 	ret	

                     659 	.endf	FDA_getFixedEncodedSize

                     660 	.align	4


                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2181.s
                     661 ;attrType	r2	local

                     662 

                     663 ;da	r0	param

                     664 ;size	r1	param

                     665 

                     666 	.section ".bss","awb"

                     667 .L720:

                     668 	.data

                     669 	.text

                     670 

                     671 ;329: 	}


                     672 ;330: }


                     673 

                     674 ;331: 


                     675 ;332: bool FDA_encodeGOOSETemplate(void* da, BufferView* templateBuf)


                     676 	.align	4

                     677 	.align	4

                     678 FDA_encodeGOOSETemplate::

000002b4 e92d4030    679 	stmfd	[sp]!,{r4-r5,lr}

000002b8 e24dd008    680 	sub	sp,sp,8

                     681 ;333: {


                     682 

                     683 ;334: 	enum InnerAttributeType* attrType = da;


                     684 

                     685 ;335: 	switch (*attrType)


                     686 

000002bc e5902000    687 	ldr	r2,[r0]

000002c0 e2522000    688 	subs	r2,r2,0

000002c4 0a000005    689 	beq	.L746

000002c8 e2522006    690 	subs	r2,r2,6

000002cc 0a000005    691 	beq	.L749

000002d0 e3520003    692 	cmp	r2,3

                     693 ;343: 	default:


                     694 ;344: 		ERROR_REPORT("Unsupported attribute type");


                     695 ;345: 		return FALSE;


                     696 

000002d4 13a00000    697 	movne	r0,0

000002d8 1a000022    698 	bne	.L742

000002dc ea00000c    699 	b	.L756

                     700 .L746:

                     701 ;336: 	{


                     702 ;337: 	case INNER_TYPE_QUALITY:


                     703 ;338: 		return FDAQuality_encodeGOOSETemplate(da, templateBuf);


                     704 

000002e0 ebffff84*   705 	bl	FDAQuality_encodeGOOSETemplate

000002e4 ea00001f    706 	b	.L742

                     707 .L749:

000002e8 e8910018    708 	ldmfd	[r1],{r3-r4}

000002ec e0842003    709 	add	r2,r4,r3

000002f0 e2822002    710 	add	r2,r2,2

000002f4 e5802010    711 	str	r2,[r0,16]

000002f8 e3a02000    712 	mov	r2,0

000002fc e1a00001    713 	mov	r0,r1

00000300 e3a01083    714 	mov	r1,131

00000304 eb000000*   715 	bl	BufferView_encodeBoolean

00000308 e3500000    716 	cmp	r0,0

0000030c 13a00001    717 	movne	r0,1

                     718 ;339: 	case INNER_TYPE_BOOLEAN:


                     719 ;340: 		return FDABoolean_encodeGOOSETemplate(da, templateBuf);


                     720 

                     721 ;159: {	



                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2181.s
                     722 

                     723 ;160: 	//В рабочем режиме будем кодировать только значение, без длины и тэга


                     724 ;161: 	da->fixedEncodeBuf = outBuf->p + outBuf->pos + 2;


                     725 

                     726 ;162: 	


                     727 ;163: 	if (!BufferView_encodeBoolean(outBuf, IEC61850_BER_BOOLEAN, FALSE))


                     728 

                     729 ;164: 	{


                     730 

                     731 ;165: 		return FALSE;


                     732 

                     733 ;166: 	}	


                     734 ;167: 	return TRUE;


                     735 

00000310 ea000014    736 	b	.L742

                     737 .L756:

                     738 ;341: 	case INNER_TYPE_CODEDENUM:


                     739 ;342: 		return FDACodedEnum_encodeGOOSETemplate(da, templateBuf);


                     740 

00000314 e28d2004    741 	add	r2,sp,4

00000318 e1a04000    742 	mov	r4,r0

0000031c e1a05001    743 	mov	r5,r1

                     744 ;239: {	


                     745 

                     746 ;240: 	uint8_t dummy = 0;


                     747 

                     748 ;241: 	uint8_t *pEncodeBuf;


                     749 ;242: 


                     750 ;243: 	if (!BufferView_alloc(outBuf, CODED_ENUM_ENCODED_SIZE, &pEncodeBuf))


                     751 

00000320 e1a00005    752 	mov	r0,r5

00000324 e3a01004    753 	mov	r1,4

00000328 eb000000*   754 	bl	BufferView_alloc

0000032c e3500000    755 	cmp	r0,0

00000330 0a00000b    756 	beq	.L762

                     757 ;244: 	{


                     758 

                     759 ;245: 		ERROR_REPORT("Trouble creating double point value template");


                     760 ;246: 		return FALSE;


                     761 

                     762 ;247: 	}


                     763 ;248: 


                     764 ;249: 	da->fixedEncodeBuf = pEncodeBuf;


                     765 

00000334 e59d3004    766 	ldr	r3,[sp,4]

00000338 e3a02000    767 	mov	r2,0

0000033c e5843014    768 	str	r3,[r4,20]

                     769 ;250: 


                     770 ;251: 	BerEncoder_encodeUcharBitString(ASN_TYPEDESCRIPTION_BIT_STRING,


                     771 

00000340 e58d2000    772 	str	r2,[sp]

00000344 e3a01002    773 	mov	r1,2

00000348 e3a00084    774 	mov	r0,132

0000034c eb000000*   775 	bl	BerEncoder_encodeUcharBitString

                     776 ;252: 		2, dummy, pEncodeBuf, 0);


                     777 ;253: 	if (!BufferView_advance(outBuf, CODED_ENUM_ENCODED_SIZE))


                     778 

00000350 e1a00005    779 	mov	r0,r5

00000354 e3a01004    780 	mov	r1,4

00000358 eb000000*   781 	bl	BufferView_advance

0000035c e3500000    782 	cmp	r0,0


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2181.s
                     783 ;257: 	}


                     784 ;258: 	return TRUE;


                     785 

00000360 13a00001    786 	movne	r0,1

                     787 .L762:

                     788 ;254: 	{


                     789 

                     790 ;255: 		ERROR_REPORT("Trouble creating double point value template");


                     791 ;256: 		return FALSE;


                     792 

00000364 03a00000    793 	moveq	r0,0

                     794 .L742:

00000368 e28dd008    795 	add	sp,sp,8

0000036c e8bd8030    796 	ldmfd	[sp]!,{r4-r5,pc}

                     797 	.endf	FDA_encodeGOOSETemplate

                     798 	.align	4

                     799 ;da	r4	local

                     800 ;outBuf	r5	local

                     801 ;pEncodeBuf	[sp,4]	local

                     802 

                     803 ;da	r0	param

                     804 ;templateBuf	r1	param

                     805 

                     806 	.section ".bss","awb"

                     807 .L864:

                     808 	.data

                     809 	.text

                     810 

                     811 ;346: 	}


                     812 ;347: }


                     813 

                     814 ;348: 


                     815 ;349: bool FDA_readAndCompare(void* da, void *dataSliceWnd,


                     816 	.align	4

                     817 	.align	4

                     818 FDA_readAndCompare::

00000370 e92d40f0    819 	stmfd	[sp]!,{r4-r7,lr}

                     820 ;350:                           bool* changed)


                     821 ;351: {


                     822 

                     823 ;352: 	enum InnerAttributeType* attrType = da;


                     824 

                     825 ;353: 	switch (*attrType)


                     826 

00000374 e5903000    827 	ldr	r3,[r0]

00000378 e2533000    828 	subs	r3,r3,0

0000037c 0a000006    829 	beq	.L896

00000380 e2533006    830 	subs	r3,r3,6

00000384 0a000006    831 	beq	.L898

00000388 e3530003    832 	cmp	r3,3

                     833 ;361: 	default:


                     834 ;362: 		ERROR_REPORT("Unsupported attribute type");


                     835 ;363: 		*changed = FALSE;


                     836 

0000038c 13a00000    837 	movne	r0,0

00000390 15c20000    838 	strneb	r0,[r2]

                     839 ;364: 		return FALSE;


                     840 

00000394 1a000030    841 	bne	.L892

00000398 ea000013    842 	b	.L906

                     843 .L896:


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2181.s
                     844 ;354: 	{


                     845 ;355: 	case INNER_TYPE_QUALITY:


                     846 ;356:         return FDAQuality_readAndCompare(da, dataSliceWnd, changed);


                     847 

0000039c e8bd40f0    848 	ldmfd	[sp]!,{r4-r7,lr}

000003a0 eaffff6d*   849 	b	FDAQuality_readAndCompare

                     850 .L898:

                     851 ;357: 	case INNER_TYPE_BOOLEAN:


                     852 ;358:         return FDABoolean_readAndCompare(da, dataSliceWnd, changed);


                     853 

000003a4 e1a04002    854 	mov	r4,r2

                     855 ;171:                                         void* dataSliceWnd, bool* changed)


                     856 ;172: {


                     857 

000003a8 e1a05000    858 	mov	r5,r0

000003ac e1a00001    859 	mov	r0,r1

000003b0 e5951008    860 	ldr	r1,[r5,8]

000003b4 e3a02000    861 	mov	r2,0

                     862 ;173:     bool newValue = 0;


                     863 

                     864 ;174: 


                     865 ;175:     if(da->dataSliceOffset != -1)


                     866 

000003b8 e3710001    867 	cmn	r1,1

000003bc 0a000003    868 	beq	.L903

                     869 ;176:     {


                     870 

                     871 ;177:         newValue = DataSlice_getBoolFast(dataSliceWnd, da->dataSliceOffset);


                     872 

000003c0 e1a01801    873 	mov	r1,r1 lsl 16

000003c4 e1a01821    874 	mov	r1,r1 lsr 16

000003c8 eb000000*   875 	bl	DataSlice_getBoolFast

000003cc e1a02000    876 	mov	r2,r0

                     877 .L903:

                     878 ;178:     }


                     879 ;179: 


                     880 ;180: 	*changed = (da->value != newValue);


                     881 

000003d0 e5d5000c    882 	ldrb	r0,[r5,12]

000003d4 e0500002    883 	subs	r0,r0,r2

000003d8 13a00001    884 	movne	r0,1

000003dc e5c40000    885 	strb	r0,[r4]

                     886 ;181: 	da->value = newValue;


                     887 

000003e0 e5c5200c    888 	strb	r2,[r5,12]

                     889 ;182: 	return TRUE;	


                     890 

000003e4 e3a00001    891 	mov	r0,1

000003e8 ea00001b    892 	b	.L892

                     893 .L906:

                     894 ;359: 	case INNER_TYPE_CODEDENUM:


                     895 ;360:         return FDACodedEnum_readAndCompare(da, dataSliceWnd, changed);


                     896 

000003ec e1a07002    897 	mov	r7,r2

                     898 ;262:                                           void* dataSliceWnd, bool* changed)


                     899 ;263: {	


                     900 

000003f0 e1a05000    901 	mov	r5,r0

000003f4 e1a06001    902 	mov	r6,r1

000003f8 e5951008    903 	ldr	r1,[r5,8]

000003fc e3a04000    904 	mov	r4,0


                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2181.s
                     905 ;264: 	uint8_t newValue = 0;


                     906 

                     907 ;265: 


                     908 ;266:     if(da->dataSliceOffset0 != -1)


                     909 

00000400 e3710001    910 	cmn	r1,1

00000404 0a000004    911 	beq	.L911

                     912 ;267:     {


                     913 

                     914 ;268:         newValue |= DataSlice_getBoolFast(dataSliceWnd, da->dataSliceOffset0);


                     915 

00000408 e1a01801    916 	mov	r1,r1 lsl 16

0000040c e1a01821    917 	mov	r1,r1 lsr 16

00000410 e1a00006    918 	mov	r0,r6

00000414 eb000000*   919 	bl	DataSlice_getBoolFast

00000418 e1a04000    920 	mov	r4,r0

                     921 .L911:

                     922 ;269:     }


                     923 ;270:     newValue <<= 1;


                     924 

0000041c e1a04084    925 	mov	r4,r4 lsl 1

00000420 e595100c    926 	ldr	r1,[r5,12]

00000424 e20440ff    927 	and	r4,r4,255

                     928 ;271:     if(da->dataSliceOffset1 != -1)


                     929 

00000428 e3710001    930 	cmn	r1,1

0000042c 0a000004    931 	beq	.L913

                     932 ;272:     {


                     933 

                     934 ;273:         newValue |= DataSlice_getBoolFast(dataSliceWnd, da->dataSliceOffset1);


                     935 

00000430 e1a01801    936 	mov	r1,r1 lsl 16

00000434 e1a01821    937 	mov	r1,r1 lsr 16

00000438 e1a00006    938 	mov	r0,r6

0000043c eb000000*   939 	bl	DataSlice_getBoolFast

00000440 e1844000    940 	orr	r4,r4,r0

                     941 .L913:

                     942 ;274:     }


                     943 ;275: 					


                     944 ;276: 	*changed = (da->value != newValue);


                     945 

00000444 e5d50010    946 	ldrb	r0,[r5,16]

00000448 e0500004    947 	subs	r0,r0,r4

0000044c 13a00001    948 	movne	r0,1

00000450 e5c70000    949 	strb	r0,[r7]

                     950 ;277: 	da->value = newValue;


                     951 

00000454 e5c54010    952 	strb	r4,[r5,16]

                     953 ;278: 	return true;	


                     954 

00000458 e3a00001    955 	mov	r0,1

                     956 .L892:

0000045c e8bd80f0    957 	ldmfd	[sp]!,{r4-r7,pc}

                     958 	.endf	FDA_readAndCompare

                     959 	.align	4

                     960 ;da	r5	local

                     961 ;dataSliceWnd	r0	local

                     962 ;changed	r4	local

                     963 ;newValue	r2	local

                     964 ;da	r5	local

                     965 ;dataSliceWnd	r6	local


                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2181.s
                     966 ;changed	r7	local

                     967 ;newValue	r4	local

                     968 

                     969 ;da	r0	param

                     970 ;dataSliceWnd	r1	param

                     971 ;changed	r2	param

                     972 

                     973 	.section ".bss","awb"

                     974 .L1014:

                     975 	.data

                     976 	.text

                     977 

                     978 ;365: 	}


                     979 ;366: }


                     980 

                     981 ;367: 


                     982 ;368: void FDA_encodeFixedData(void* da)


                     983 	.align	4

                     984 	.align	4

                     985 FDA_encodeFixedData::

00000460 e92d4000    986 	stmfd	[sp]!,{lr}

00000464 e24dd004    987 	sub	sp,sp,4

                     988 ;369: {


                     989 

                     990 ;370: 	enum InnerAttributeType* attrType = da;


                     991 

                     992 ;371: 	switch (*attrType)


                     993 

00000468 e5901000    994 	ldr	r1,[r0]

0000046c e2511000    995 	subs	r1,r1,0

00000470 0a000007    996 	beq	.L1056

00000474 e2511006    997 	subs	r1,r1,6

                     998 ;376: 	case INNER_TYPE_BOOLEAN:


                     999 ;377: 		FDABoolean_encodeFixedData(da);


                    1000 

                    1001 ;186: {


                    1002 

                    1003 ;187: 	*da->fixedEncodeBuf = da->value;


                    1004 

00000478 05901010   1005 	ldreq	r1,[r0,16]

0000047c 05d0000c   1006 	ldreqb	r0,[r0,12]

00000480 05c10000   1007 	streqb	r0,[r1]

00000484 0a00000b   1008 	beq	.L1052

00000488 e3510003   1009 	cmp	r1,3

0000048c 0a000002   1010 	beq	.L1067

00000490 ea000008   1011 	b	.L1052

                    1012 .L1056:

                    1013 ;372: 	{


                    1014 ;373: 	case INNER_TYPE_QUALITY:


                    1015 ;374:         FDAQuality_encodeFixedData(da);


                    1016 

00000494 ebffff3d*  1017 	bl	FDAQuality_encodeFixedData

                    1018 ;375:         return;


                    1019 

00000498 ea000006   1020 	b	.L1052

                    1021 .L1067:

                    1022 ;378: 		return;


                    1023 

                    1024 ;379: 	case INNER_TYPE_CODEDENUM:


                    1025 ;380: 		FDACodedEnum_encodeFixedData(da);


                    1026 


                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2181.s
                    1027 ;283: {


                    1028 

                    1029 ;284: 	BerEncoder_encodeUcharBitString(ASN_TYPEDESCRIPTION_BIT_STRING,


                    1030 

0000049c e3a01000   1031 	mov	r1,0

000004a0 e58d1000   1032 	str	r1,[sp]

000004a4 e3a01002   1033 	mov	r1,2

000004a8 e5903014   1034 	ldr	r3,[r0,20]

000004ac e5d02010   1035 	ldrb	r2,[r0,16]

000004b0 e3a00084   1036 	mov	r0,132

000004b4 eb000000*  1037 	bl	BerEncoder_encodeUcharBitString

                    1038 .L1052:

                    1039 ;381: 		return;


                    1040 

                    1041 ;382: 	default:


                    1042 ;383: 		ERROR_REPORT("Unsupported attribute type");	


                    1043 ;384:         return;


                    1044 

000004b8 e28dd004   1045 	add	sp,sp,4

000004bc e8bd8000   1046 	ldmfd	[sp]!,{pc}

                    1047 	.endf	FDA_encodeFixedData

                    1048 	.align	4

                    1049 

                    1050 ;da	r0	param

                    1051 

                    1052 	.section ".bss","awb"

                    1053 .L1106:

                    1054 	.data

                    1055 	.text

                    1056 

                    1057 ;385: 	}


                    1058 ;386: }


                    1059 	.align	4

                    1060 

                    1061 	.data

                    1062 	.ghsnote version,6

                    1063 	.ghsnote tools,3

                    1064 	.ghsnote options,0

                    1065 	.text

                    1066 	.align	4

