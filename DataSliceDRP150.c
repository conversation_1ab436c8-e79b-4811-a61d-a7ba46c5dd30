#include "DataSlice.h"
#include <debug.h>
#include "../../../../DataSlice/DataSliceClient/datasliceif.h"

//Интерфейс DataSlice
DataSliceIf* dataSliceIf;

DataSliceWnd* currentDataSlice;


static OnUpdateDataSliceFunc g_oldCallBack;
static OnUpdateDataSliceFunc g_newCallBack;

bool dataSliceInit(void)
{

    return TRUE;
}

void dataSliceCapture(void)
{

}

void dataSliceRelease(void)
{

}

unsigned long long getCurrentDataSliceTime(void)
{
	return 0;
}

unsigned long long dataSliceGetTimeStamp(void)
{
	return 0;
}

long dataSliceGetFloatValue(uint16_t offset)
{
	long value = 0;
    return value;
}

float dataSliceGetRealValue(uint16_t offset)
{
	float value = 0;

    return value;
}

bool dataSliceGetBoolValue(uint16_t offset)
{

	return FALSE;
}

int dataSliceGetIntValue(uint16_t offset)
{    
	return 0;
}

bool DataSlice_getBoolFast(void* dataSliceWnd, uint16_t offset)
{ 
	return false;
}

void* DataSlice_getDataSliceWnd(void)
{
	return NULL;
}

int DataSlice_getBoolOffset(int offset)
{    
	return 0;
}

void DataSlice_setCallBack(void (*func)(void))
{    
}

