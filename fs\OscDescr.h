#pragma once
#include "../../../../OscDescrMake/OscDescr.h"
#include <types.h>
#include <stdbool.h>
#include "../bufView.h"


//! инициализация, должна вызываться 1 раз при старте
bool OSCDescr_init(void);

//! имя блока
const char *OSCDescr_getTerminalName(void);
//! версия блока
unsigned int OSCDescr_getTerminalVersion(void);

//! функции/обертки для получения строковых значений
const char *OSCDescr_analogName(OscDescrAnalog *analog);
const char *OSCDescr_analogUnits(OscDescrAnalog *analog);
const char *OSCDescr_boolName(OscDescrBool *pBool);

//! главная частота
float OSCDescr_getFreq(void);

//! поиск аналогового канала по номеру
OscDescrAnalog *OSCDescr_findDescrAnalogItem(unsigned int itemIdex);
//! поиск дискретного канала по смещению
OscDescrBool *OSCDescr_findDescrBoolItem(unsigned int itemOffset);



