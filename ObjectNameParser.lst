                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_d4c1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=ObjectNameParser.c -o gh_d4c1.o -list=ObjectNameParser.lst C:\Users\<USER>\AppData\Local\Temp\gh_d4c1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_d4c1.s
Source File: ObjectNameParser.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      10 ;		ObjectNameParser.c -o ObjectNameParser.o

                      11 ;Source File:   ObjectNameParser.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:55:49 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "ObjectNameParser.h"


                      21 ;2: 


                      22 ;3: 


                      23 ;4: #include "iedTree/iedTree.h"


                      24 ;5: #include "iedTree/iedNoEntity.h"


                      25 ;6: 


                      26 ;7: #include "BaseAsnTypes.h"


                      27 ;8: 


                      28 ;9: #include <debug.h>


                      29 ;10: 


                      30 ;11: // Получает имя из BER и находит подобъект объекта entity.


                      31 ;12: // Смещение bv должно указывать на BER со строкой имени.


                      32 ;13: // Если объект не найден, возвращает NoEntity.


                      33 ;14: // При прочих ошибках возвращает NULL


                      34 ;15: static IEDEntity parseName(BufferView* bv, uint8_t tag, IEDEntity entity)


                      35 ;16: {


                      36 ;17:     StringView name;


                      37 ;18:     if(!BufferView_decodeStringViewTL(bv, tag, &name))


                      38 ;19:     {


                      39 ;20:         ERROR_REPORT("Error parsing alternate object name");


                      40 ;21:         return NULL;


                      41 ;22:     }


                      42 ;23:     entity = IEDEntity_getChildByName(entity, &name);


                      43 ;24:     if(entity == NULL)


                      44 ;25:     {


                      45 ;26:         // Элемент не найден в дереве. Возвращаем NoEntity


                      46 ;27:         return IEDNoEntity_get();


                      47 ;28:     }


                      48 ;29:     return entity;


                      49 ;30: }


                      50 ;31:     



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_d4c1.s
                      51 ;32: 


                      52 ;33: // Смещение bv должно указывать на первый байт (0xA0).


                      53 ;34: // Буфер должен заканчиваться с последний байтом имени.


                      54 ;35: // При ошибке разбора возвращает NULL


                      55 ;36: // Если объект с указанным именем не найден, возвращает NoEntity


                      56 ;37: static IEDEntity parseAlternateSpec(BufferView* bv, IEDEntity entity)


                      57 

                      58 ;86:         }        


                      59 ;87:     }


                      60 ;88:     


                      61 ;89: }


                      62 

                      63 	.text

                      64 	.align	4

                      65 parseName:

00000000 e92d4010     66 	stmfd	[sp]!,{r4,lr}

00000004 e24dd008     67 	sub	sp,sp,8

00000008 e1a04002     68 	mov	r4,r2

0000000c e1a0200d     69 	mov	r2,sp

00000010 eb000000*    70 	bl	BufferView_decodeStringViewTL

00000014 e3500000     71 	cmp	r0,0

00000018 0a000004     72 	beq	.L128

0000001c e1a0100d     73 	mov	r1,sp

00000020 e1a00004     74 	mov	r0,r4

00000024 eb000000*    75 	bl	IEDEntity_getChildByName

00000028 e1b04000     76 	movs	r4,r0

0000002c 0b000000*    77 	bleq	IEDNoEntity_get

                      78 .L128:

00000030 e28dd008     79 	add	sp,sp,8

00000034 e8bd4010     80 	ldmfd	[sp]!,{r4,lr}

00000038 e12fff1e*    81 	ret	

                      82 	.endf	parseName

                      83 	.align	4

                      84 ;name	[sp]	local

                      85 

                      86 ;bv	none	param

                      87 ;tag	none	param

                      88 ;entity	r4	param

                      89 

                      90 	.section ".bss","awb"

                      91 .L195:

                      92 	.data

                      93 	.text

                      94 

                      95 

                      96 ;90: 


                      97 ;91: // Парсит элемент списка спецификации имён объектов.


                      98 ;92: // bv должен указывать на элемент списка, т.е. на ASN_SEQUENCE (0x30).


                      99 ;93: IEDEntity ObjectNameParser_parse(BufferView* bv)


                     100 	.align	4

                     101 	.align	4

                     102 ObjectNameParser_parse::

0000003c e92d4070    103 	stmfd	[sp]!,{r4-r6,lr}

                     104 ;94: {


                     105 

                     106 ;95:     uint8_t tag;


                     107 ;96:     size_t len;


                     108 ;97:     BufferView nameSpecification;


                     109 ;98:     StringView ldName;


                     110 ;99:     StringView objectName;


                     111 ;100:     IEDEntity entity;



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_d4c1.s
                     112 ;101: 


                     113 ;102:     if(!BufferView_decodeTL(bv, &tag, &len, NULL) || tag != ASN_SEQUENCE)


                     114 

00000040 e24dd028    115 	sub	sp,sp,40

00000044 e28d2004    116 	add	r2,sp,4

00000048 e28d1002    117 	add	r1,sp,2

0000004c e1a04000    118 	mov	r4,r0

00000050 e3a03000    119 	mov	r3,0

00000054 eb000000*   120 	bl	BufferView_decodeTL

00000058 e3500000    121 	cmp	r0,0

0000005c 0a000045    122 	beq	.L245

00000060 e5dd0002    123 	ldrb	r0,[sp,2]

00000064 e3500030    124 	cmp	r0,48

00000068 1a000042    125 	bne	.L245

                     126 ;103:     {    


                     127 

                     128 ;104:         ERROR_REPORT("Error parsing object name");


                     129 ;105:         return NULL;


                     130 

                     131 ;106:     }


                     132 ;107: 


                     133 ;108:     BufferView_init(&nameSpecification, BufferView_currentPtr(bv), len, 0);


                     134 

0000006c e59d2004    135 	ldr	r2,[sp,4]

00000070 e894000a    136 	ldmfd	[r4],{r1,r3}

00000074 e28d001c    137 	add	r0,sp,28

00000078 e0831001    138 	add	r1,r3,r1

0000007c e3a03000    139 	mov	r3,0

00000080 eb000000*   140 	bl	BufferView_init

                     141 ;109: 


                     142 ;110:     // Продвигаемся вперёд на всю длину элемента списка чтобы следующий


                     143 ;111:     // вызов ObjectNameParser_parse попал на следующий элемент списка.


                     144 ;112:     // Дальше будем работать через nameSpecification


                     145 ;113:     if(!BufferView_advance(bv, len))


                     146 

00000084 e59d1004    147 	ldr	r1,[sp,4]

00000088 e1a00004    148 	mov	r0,r4

0000008c eb000000*   149 	bl	BufferView_advance

00000090 e3500000    150 	cmp	r0,0

00000094 0a000037    151 	beq	.L245

                     152 ;114:     {


                     153 

                     154 ;115:         ERROR_REPORT("Error 1 parsing object name");


                     155 ;116:         return NULL;


                     156 

                     157 ;117:     }


                     158 ;118: 


                     159 ;119:     if(!BufferView_decodeTL(&nameSpecification, &tag, &len, NULL) 


                     160 

00000098 e28d2004    161 	add	r2,sp,4

0000009c e28d1002    162 	add	r1,sp,2

000000a0 e28d001c    163 	add	r0,sp,28

000000a4 e3a03000    164 	mov	r3,0

000000a8 eb000000*   165 	bl	BufferView_decodeTL

000000ac e3500000    166 	cmp	r0,0

000000b0 0a000030    167 	beq	.L245

000000b4 e5dd0002    168 	ldrb	r0,[sp,2]

000000b8 e35000a0    169 	cmp	r0,160

000000bc 1a00002d    170 	bne	.L245

                     171 ;120:         || tag != 0xA0)


                     172 ;121:     {



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_d4c1.s
                     173 

                     174 ;122:         ERROR_REPORT("Error 2 parsing object name");


                     175 ;123:         return NULL;


                     176 

                     177 ;124:     }


                     178 ;125:     if(!BufferView_decodeTL(&nameSpecification, &tag, &len, NULL) 


                     179 

000000c0 e28d2004    180 	add	r2,sp,4

000000c4 e28d1002    181 	add	r1,sp,2

000000c8 e28d001c    182 	add	r0,sp,28

000000cc e3a03000    183 	mov	r3,0

000000d0 eb000000*   184 	bl	BufferView_decodeTL

000000d4 e3500000    185 	cmp	r0,0

000000d8 0a000026    186 	beq	.L245

000000dc e5dd0002    187 	ldrb	r0,[sp,2]

000000e0 e35000a1    188 	cmp	r0,161

000000e4 1a000023    189 	bne	.L245

                     190 ;126:         || tag != 0xA1)


                     191 ;127:     {


                     192 

                     193 ;128:         ERROR_REPORT("Error 3 parsing object name");


                     194 ;129:         return NULL;


                     195 

                     196 ;130:     }


                     197 ;131: 


                     198 ;132:     // Здесь должно быть две строки - LD и имя объекта


                     199 ;133:     if(!BufferView_decodeStringViewTL(&nameSpecification, ASN_VISIBLE_STRING,


                     200 

000000e8 e28d2014    201 	add	r2,sp,20

000000ec e28d001c    202 	add	r0,sp,28

000000f0 e3a0101a    203 	mov	r1,26

000000f4 eb000000*   204 	bl	BufferView_decodeStringViewTL

000000f8 e3500000    205 	cmp	r0,0

000000fc 0a00001d    206 	beq	.L245

                     207 ;134:         &ldName))


                     208 ;135:     {


                     209 

                     210 ;136:         ERROR_REPORT("Error parsing object LD name string");


                     211 ;137:         return NULL;


                     212 

                     213 ;138:     }


                     214 ;139:     if(!BufferView_decodeStringViewTL(&nameSpecification, ASN_VISIBLE_STRING,


                     215 

00000100 e28d200c    216 	add	r2,sp,12

00000104 e28d001c    217 	add	r0,sp,28

00000108 e3a0101a    218 	mov	r1,26

0000010c eb000000*   219 	bl	BufferView_decodeStringViewTL

00000110 e3500000    220 	cmp	r0,0

00000114 0a000017    221 	beq	.L245

                     222 ;140:         &objectName))


                     223 ;141:     {


                     224 

                     225 ;142:         ERROR_REPORT("Error parsing object name string");


                     226 ;143:         return NULL;


                     227 

                     228 ;144:     }


                     229 ;145:         


                     230 ;146:     // Ищем элемент дерева полному имени


                     231 ;147:     entity = IEDTree_findDataByFullName(&ldName, &objectName);


                     232 

00000118 e28d100c    233 	add	r1,sp,12


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_d4c1.s
0000011c e28d0014    234 	add	r0,sp,20

00000120 eb000000*   235 	bl	IEDTree_findDataByFullName

00000124 e1b04000    236 	movs	r4,r0

                     237 ;148:     if(entity == NULL)


                     238 

00000128 1a000001    239 	bne	.L237

                     240 ;149:     {


                     241 

                     242 ;150:         // Элемент не найден в дереве. Возвращаем NoEntity


                     243 ;151:         return IEDNoEntity_get();


                     244 

0000012c eb000000*   245 	bl	IEDNoEntity_get

00000130 ea000045    246 	b	.L213

                     247 .L237:

                     248 ;152:     }


                     249 ;153: 


                     250 ;154:     if(BufferView_endOfBuf(&nameSpecification))


                     251 

00000134 e59d1024    252 	ldr	r1,[sp,36]

00000138 e59d0020    253 	ldr	r0,[sp,32]

0000013c e1500001    254 	cmp	r0,r1

                     255 ;155:     {


                     256 

                     257 ;156:         // Это был последний элемент в спецификации имени


                     258 ;157:         return entity;


                     259 

00000140 01a00004    260 	moveq	r0,r4

00000144 0a000040    261 	beq	.L213

                     262 ;158:     }


                     263 ;159: 


                     264 ;160:     // Alternate access


                     265 ;161:     if(!BufferView_decodeTL(&nameSpecification, &tag, &len, NULL) 


                     266 

00000148 e28d2004    267 	add	r2,sp,4

0000014c e28d1002    268 	add	r1,sp,2

00000150 e28d001c    269 	add	r0,sp,28

00000154 e3a03000    270 	mov	r3,0

00000158 eb000000*   271 	bl	BufferView_decodeTL

0000015c e3500000    272 	cmp	r0,0

00000160 0a000004    273 	beq	.L245

00000164 e5dd0002    274 	ldrb	r0,[sp,2]

00000168 e35000a5    275 	cmp	r0,165

                     276 ;166:     }


                     277 ;167: 


                     278 ;168:     return parseAlternateSpec(&nameSpecification, entity);


                     279 

0000016c 028d501c    280 	addeq	r5,sp,28

00000170 028d6003    281 	addeq	r6,sp,3

00000174 0a000001    282 	beq	.L251

                     283 .L245:

                     284 ;162:         || tag != 0xA5)


                     285 ;163:     {


                     286 

                     287 ;164:         ERROR_REPORT("Error parsing alternate access");


                     288 ;165:         return NULL;


                     289 

00000178 e3a00000    290 	mov	r0,0

0000017c ea000032    291 	b	.L213

                     292 .L251:

                     293 ;38: {


                     294 


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_d4c1.s
                     295 ;39:     uint8_t tag;


                     296 ;40:     size_t len;


                     297 ;41:     


                     298 ;42:     while (true)


                     299 

                     300 ;43:     {


                     301 

                     302 ;44:         if(!BufferView_peekTag(bv, &tag))


                     303 

00000180 e1a01006    304 	mov	r1,r6

00000184 e1a00005    305 	mov	r0,r5

00000188 eb000000*   306 	bl	BufferView_peekTag

0000018c e3500000    307 	cmp	r0,0

00000190 0a00002c    308 	beq	.L270

                     309 ;45:         {


                     310 

                     311 ;46:             ERROR_REPORT("Error 7 parsing alternate object name");


                     312 ;47:             return NULL;


                     313 

                     314 ;48:         }


                     315 ;49:         


                     316 ;50:         if(tag == 0x81)


                     317 

00000194 e5dd1003    318 	ldrb	r1,[sp,3]

00000198 e1a00005    319 	mov	r0,r5

0000019c e3510081    320 	cmp	r1,129

000001a0 1a000002    321 	bne	.L255

                     322 ;51:         {


                     323 

                     324 ;52:             // Alternate access из одного имени


                     325 ;53:             return parseName(bv, tag, entity);            


                     326 

000001a4 e1a02004    327 	mov	r2,r4

000001a8 ebffff94*   328 	bl	parseName

000001ac ea000026    329 	b	.L213

                     330 .L255:

                     331 ;54:         }


                     332 ;55: 


                     333 ;56: 


                     334 ;57:         if(!BufferView_decodeTL(bv, &tag, &len, NULL))


                     335 

000001b0 e28d2008    336 	add	r2,sp,8

000001b4 e1a01006    337 	mov	r1,r6

000001b8 e3a03000    338 	mov	r3,0

000001bc eb000000*   339 	bl	BufferView_decodeTL

000001c0 e3500000    340 	cmp	r0,0

000001c4 0a00001f    341 	beq	.L270

                     342 ;58:         {


                     343 

                     344 ;59:             ERROR_REPORT("Error 2 parsing alternate object name");


                     345 ;60:             return NULL;


                     346 

                     347 ;61:         };


                     348 ;62:         if(tag != 0xA0)


                     349 

000001c8 e5dd1003    350 	ldrb	r1,[sp,3]

000001cc e35100a0    351 	cmp	r1,160

000001d0 1a00001c    352 	bne	.L270

                     353 ;63:         {


                     354 

                     355 ;64:             ERROR_REPORT("Error 3 parsing alternate object name");



                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_d4c1.s
                     356 ;65:             return NULL;


                     357 

                     358 ;66:         }


                     359 ;67:         if(!BufferView_peekTag(bv, &tag) || (tag != 0x80 && tag != 0x81))


                     360 

000001d4 e1a01006    361 	mov	r1,r6

000001d8 e1a00005    362 	mov	r0,r5

000001dc eb000000*   363 	bl	BufferView_peekTag

000001e0 e3500000    364 	cmp	r0,0

000001e4 0a000017    365 	beq	.L270

000001e8 e5dd1003    366 	ldrb	r1,[sp,3]

000001ec e3510080    367 	cmp	r1,128

000001f0 13510081    368 	cmpne	r1,129

000001f4 1a000013    369 	bne	.L270

                     370 ;68:         {


                     371 

                     372 ;69:             ERROR_REPORT("Error 4 parsing alternate object name");


                     373 ;70:             return NULL;


                     374 

                     375 ;71:         }


                     376 ;72:         entity = parseName(bv, tag, entity);


                     377 

000001f8 e1a02004    378 	mov	r2,r4

000001fc e1a00005    379 	mov	r0,r5

00000200 ebffff7e*   380 	bl	parseName

00000204 e1b04000    381 	movs	r4,r0

                     382 ;73:         if(entity == NULL)


                     383 

00000208 0a00000e    384 	beq	.L270

                     385 ;74:         {


                     386 

                     387 ;75:             return NULL;


                     388 

                     389 ;76:         }


                     390 ;77:         if(BufferView_endOfBuf(bv))


                     391 

0000020c e59d2024    392 	ldr	r2,[sp,36]

00000210 e59d1020    393 	ldr	r1,[sp,32]

00000214 e1510002    394 	cmp	r1,r2

                     395 ;78:         {


                     396 

                     397 ;79:             return entity;


                     398 

00000218 01a00004    399 	moveq	r0,r4

0000021c 0a00000a    400 	beq	.L213

                     401 ;80:         }


                     402 ;81:         


                     403 ;82:         if(!BufferView_decodeTL(bv, &tag, &len, NULL) || tag != ASN_SEQUENCE)


                     404 

00000220 e28d2008    405 	add	r2,sp,8

00000224 e1a01006    406 	mov	r1,r6

00000228 e1a00005    407 	mov	r0,r5

0000022c e3a03000    408 	mov	r3,0

00000230 eb000000*   409 	bl	BufferView_decodeTL

00000234 e3500000    410 	cmp	r0,0

00000238 0a000002    411 	beq	.L270

0000023c e5dd1003    412 	ldrb	r1,[sp,3]

00000240 e3510030    413 	cmp	r1,48

00000244 0affffcd    414 	beq	.L251

                     415 .L270:

                     416 ;83:         {



                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_d4c1.s
                     417 

                     418 ;84:             ERROR_REPORT("Error 6 parsing alternate object name");


                     419 ;85:             return NULL;


                     420 

00000248 e3a00000    421 	mov	r0,0

                     422 .L213:

0000024c e28dd028    423 	add	sp,sp,40

00000250 e8bd8070    424 	ldmfd	[sp]!,{r4-r6,pc}

                     425 	.endf	ObjectNameParser_parse

                     426 	.align	4

                     427 ;tag	[sp,2]	local

                     428 ;len	[sp,4]	local

                     429 ;nameSpecification	[sp,28]	local

                     430 ;ldName	[sp,20]	local

                     431 ;objectName	[sp,12]	local

                     432 ;entity	r4	local

                     433 ;entity	r4	local

                     434 ;tag	[sp,3]	local

                     435 ;len	[sp,8]	local

                     436 

                     437 ;bv	r4	param

                     438 

                     439 	.section ".bss","awb"

                     440 .L594:

                     441 	.data

                     442 	.text

                     443 

                     444 ;169: }


                     445 	.align	4

                     446 

                     447 	.data

                     448 	.ghsnote version,6

                     449 	.ghsnote tools,3

                     450 	.ghsnote options,0

                     451 	.text

                     452 	.align	4

