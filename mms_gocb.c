#include "mms_gocb.h"
#include "IEDCompile/AccessInfo.h"
#include "IEDCompile/InnerAttributeTypes.h"
#include "iedmodel.h"
#include "mms_data.h"
#include "goose.h"
#include "BaseAsnTypes.h"
#include <stddef.h>

//Комментарий для проверки кодировки

int encodeAccessAttrGoCB(uint8_t* outBuf, int bufPos, int accessDataPos,
	bool determineSize)
{
	CBAttrAccessInfo* pAccessInfo =
		(CBAttrAccessInfo*)getAlignedDescrStruct(accessDataPos);
	if (pAccessInfo == NULL)
	{
		ERROR_REPORT("Unable to get access info struct");
		return 0;
	}

	switch (pAccessInfo->attrCode)
	{
	case GoEna:	
	case NdsCom:
		return encodeAccessAttrBoolean(outBuf, bufPos, determineSize);	
	default:
		ERROR_REPORT("Invalid GoCB DA code");
		return 0;
	}
}

static int encodeReadGoEna(uint8_t* outBuf, int bufPos,
	CBAttrAccessInfo* descrStruct, bool determineSize)
{
	size_t cbIndex = descrStruct->rcbIndex;
	bool value;
	GOOSE_getGoEna(cbIndex, &value);

	return encodeBoolValue(outBuf, bufPos, value, determineSize);
}

static int encodeReadNdsCom(uint8_t* outBuf, int bufPos,
	CBAttrAccessInfo* descrStruct, bool determineSize)
{
	size_t cbIndex = descrStruct->rcbIndex;
	bool value;
	GOOSE_getNdsCom(cbIndex, &value);
	return encodeBoolValue(outBuf, bufPos, value, determineSize);
}

int encodeReadAttrGoCB(uint8_t* outBuf, int bufPos, void* descrStruct, bool determineSize)
{
	CBAttrAccessInfo* pAccessInfo = descrStruct;

	switch (pAccessInfo->attrCode) {
	case GoEna:
		return encodeReadGoEna(outBuf, bufPos, pAccessInfo, determineSize);
	case NdsCom:
		return encodeReadNdsCom(outBuf, bufPos, pAccessInfo, determineSize);
	default:
		ERROR_REPORT("Invalid  GoCB DA code");
		return 0;
	}
}

static void writeGoEna(int cbIndex, uint8_t* dataToWrite)
{
	bool value;
	
	if (dataToWrite[0] != IEC61850_BER_BOOLEAN || dataToWrite[1] != 1)
	{
		return;
	}
	value = dataToWrite[2] != 0;
	
	GOOSE_setGoEna(cbIndex, value);
}

void writeAttrGoCB(void* descrStruct, uint8_t* dataToWrite)
{
	CBAttrAccessInfo* pAccessInfo = descrStruct;

	switch (pAccessInfo->attrCode)
	{
	case GoEna:
		writeGoEna(pAccessInfo->rcbIndex, dataToWrite);
		break;
	default:
		ERROR_REPORT("Unsupported GoCB attribute");
	}

}

