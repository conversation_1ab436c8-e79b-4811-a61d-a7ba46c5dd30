                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_enc1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=debug.c -o gh_enc1.o -list=debug.lst C:\Users\<USER>\AppData\Local\Temp\gh_enc1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_enc1.s
Source File: debug.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile debug.c

                      10 ;Source File:   debug.c

                      11 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      12 ;Compile Date:  Tue Oct 07 09:39:35 2025

                      13 ;Host OS:       Win32

                      14 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      15 ;Release:       MULTI v4.2.3

                      16 ;Revision Date: Wed Mar 29 05:25:47 2006

                      17 ;Release Date:  Fri Mar 31 10:02:14 2006

                      18 

                      19 ;1: #include <debug.h>


                      20 ;2: 


                      21 ;3: 


                      22 ;4: #ifdef SYSLOG


                      23 ;5: 


                      24 ;6: #include <syslog.h>


                      25 ;7: #include "Clib.h"


                      26 ;8: #include <string.h>


                      27 ;9: #include <stdarg.h>


                      28 ;10: 


                      29 ;11: int g_syslogHandler;


                      30 ;12: volatile STIMER g_pauseTimer;


                      31 ;13: 


                      32 ;14: static void pause(void)


                      33 ;15: {


                      34 ;16:     memset((STIMER*)&g_pauseTimer, 0, sizeof(g_pauseTimer));


                      35 ;17:     CreateTimer((STIMER*)&g_pauseTimer);


                      36 ;18:     g_pauseTimer.AlarmTime = 5000;


                      37 ;19:     g_pauseTimer.Precision = 1;


                      38 ;20:     g_pauseTimer.Started = 1;


                      39 ;21:     while ( !g_pauseTimer.Alarm) {


                      40 ;22:         Idle();


                      41 ;23:     }


                      42 ;24: }


                      43 ;25: 


                      44 ;26: void printf_wrapper(char* fmtMsg, ...)


                      45 ;27: {


                      46 ;28:     va_list args;


                      47 ;29:     va_start(args, fmtMsg);


                      48 ;30: 


                      49 ;31:     vsyslog(g_syslogHandler,LOG_NOTICE, fmtMsg, args);


                      50 ;32: 



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_enc1.s
                      51 ;33:     va_end(args);


                      52 ;34: }


                      53 ;35: 


                      54 ;36: void debugStart()


                      55 ;37: {    


                      56 ;38:     pause();


                      57 ;39:     syslog_init();


                      58 ;40:     g_syslogHandler = openlog ("MMS",0,LOG_LOCAL1);


                      59 ;41: }


                      60 ;42: 


                      61 ;43: #else


                      62 ;44: 


                      63 ;45: void debugStart()


                      64 

                      65 ;47: }


                      66 

                      67 ;48: 


                      68 ;49: #endif


                      69 ;50: 


                      70 ;51: 


                      71 ;52: void debugSendUshort(char* text, unsigned short value)


                      72 

                      73 ;54: }


                      74 

                      75 ;55: 


                      76 ;56: void debugSendStrL(char* text, unsigned char* str, int strLen)


                      77 

                      78 ;58: }


                      79 

                      80 ;59: 


                      81 ;60: void debugSendDump(char* text, unsigned char* data, int byteCount)


                      82 

                      83 ;62: }


                      84 

                      85 ;63: 


                      86 ;64: void debugSendText(char* text)


                      87 

                      88 ;66: }


                      89 	.text

                      90 	.align	4

                      91 debugStart::

                      92 ;46: {


                      93 

00000000 e12fff1e*    94 	ret	

                      95 	.endf	debugStart

                      96 	.align	4

                      97 

                      98 	.section ".bss","awb"

                      99 .L110:

                     100 	.data

                     101 	.text

                     102 	.align	4

                     103 	.align	4

                     104 debugSendUshort::

                     105 ;53: {


                     106 

00000004 e12fff1e*   107 	ret	

                     108 	.endf	debugSendUshort

                     109 	.align	4

                     110 

                     111 ;text	none	param


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_enc1.s
                     112 ;value	none	param

                     113 

                     114 	.section ".bss","awb"

                     115 .L142:

                     116 	.data

                     117 	.text

                     118 	.align	4

                     119 	.align	4

                     120 debugSendStrL::

                     121 ;57: {


                     122 

00000008 e12fff1e*   123 	ret	

                     124 	.endf	debugSendStrL

                     125 	.align	4

                     126 

                     127 ;text	none	param

                     128 ;str	none	param

                     129 ;strLen	none	param

                     130 

                     131 	.section ".bss","awb"

                     132 .L174:

                     133 	.data

                     134 	.text

                     135 	.align	4

                     136 	.align	4

                     137 debugSendDump::

                     138 ;61: {


                     139 

0000000c e12fff1e*   140 	ret	

                     141 	.endf	debugSendDump

                     142 	.align	4

                     143 

                     144 ;text	none	param

                     145 ;data	none	param

                     146 ;byteCount	none	param

                     147 

                     148 	.section ".bss","awb"

                     149 .L206:

                     150 	.data

                     151 	.text

                     152 	.align	4

                     153 	.align	4

                     154 debugSendText::

                     155 ;65: {


                     156 

00000010 e12fff1e*   157 	ret	

                     158 	.endf	debugSendText

                     159 	.align	4

                     160 

                     161 ;text	none	param

                     162 

                     163 	.section ".bss","awb"

                     164 .L238:

                     165 	.data

                     166 	.text

                     167 	.align	4

                     168 

                     169 	.data

                     170 	.ghsnote version,6

                     171 	.ghsnote tools,3

                     172 	.ghsnote options,0


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_enc1.s
                     173 	.text

                     174 	.align	4

