                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fo41.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=BERCoder.c -o gh_fo41.o -list=BERCoder.lst C:\Users\<USER>\AppData\Local\Temp\gh_fo41.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_fo41.s
Source File: BERCoder.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile BERCoder.c -o

                      10 ;		BERCoder.o

                      11 ;Source File:   BERCoder.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:55:51 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "BERCoder.h"


                      21 ;2: 


                      22 ;3: 


                      23 ;4: size_t BERCoder_calcIntEncodedLen(const void* pValue, size_t intSize)


                      24 	.text

                      25 	.align	4

                      26 BERCoder_calcIntEncodedLen::

00000000 e92d0070     27 	stmfd	[sp]!,{r4-r6}

                      28 ;5: {


                      29 

                      30 ;6:     // Функция работает в big endian. То есть начинаем с последнего


                      31 ;7:     // байта, он же - старший


                      32 ;8:     const uint8_t* pValueBytes = ((const uint8_t*)pValue) + (intSize - 1);


                      33 

00000004 e2512001     34 	subs	r2,r1,1

                      35 ;9:     size_t i;


                      36 ;10:     uint8_t byte = *pValueBytes;


                      37 

00000008 e7f05002     38 	ldrb	r5,[r0,r2]!

                      39 ;11: 


                      40 ;12:     // Пропускаем лидирующие знаковые байты. Цикл по старшим 7 байтам


                      41 ;13:     for (i = 0; i < (intSize - 1); i++)


                      42 

0000000c e3a03000     43 	mov	r3,0

00000010 51a04002     44 	movpl	r4,r2

00000014 43a04000     45 	movmi	r4,0

00000018 e1b0c1a4     46 	movs	r12,r4 lsr 3

0000001c 0a000059     47 	beq	.L47

                      48 .L48:

00000020 e5502001     49 	ldrb	r2,[r0,-1]

00000024 e35500ff     50 	cmp	r5,255


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fo41.s
00000028 e2026080     51 	and	r6,r2,128

0000002c 1a000001     52 	bne	.L50

00000030 e3560000     53 	cmp	r6,0

00000034 1a000001     54 	bne	.L53

                      55 .L50:

00000038 e1965005     56 	orrs	r5,r6,r5

0000003c 1a000060     57 	bne	.L2

                      58 .L53:

00000040 e2833001     59 	add	r3,r3,1

00000044 e1a05002     60 	mov	r5,r2

00000048 e5502002     61 	ldrb	r2,[r0,-2]

0000004c e2400001     62 	sub	r0,r0,1

00000050 e2026080     63 	and	r6,r2,128

00000054 e35500ff     64 	cmp	r5,255

00000058 1a000001     65 	bne	.L55

0000005c e3560000     66 	cmp	r6,0

00000060 1a000001     67 	bne	.L58

                      68 .L55:

00000064 e1965005     69 	orrs	r5,r6,r5

00000068 1a000055     70 	bne	.L2

                      71 .L58:

0000006c e2833001     72 	add	r3,r3,1

00000070 e1a05002     73 	mov	r5,r2

00000074 e5502002     74 	ldrb	r2,[r0,-2]

00000078 e2400001     75 	sub	r0,r0,1

0000007c e2026080     76 	and	r6,r2,128

00000080 e35500ff     77 	cmp	r5,255

00000084 1a000001     78 	bne	.L60

00000088 e3560000     79 	cmp	r6,0

0000008c 1a000001     80 	bne	.L63

                      81 .L60:

00000090 e1965005     82 	orrs	r5,r6,r5

00000094 1a00004a     83 	bne	.L2

                      84 .L63:

00000098 e2833001     85 	add	r3,r3,1

0000009c e1a05002     86 	mov	r5,r2

000000a0 e5502002     87 	ldrb	r2,[r0,-2]

000000a4 e2400001     88 	sub	r0,r0,1

000000a8 e2026080     89 	and	r6,r2,128

000000ac e35500ff     90 	cmp	r5,255

000000b0 1a000001     91 	bne	.L65

000000b4 e3560000     92 	cmp	r6,0

000000b8 1a000001     93 	bne	.L68

                      94 .L65:

000000bc e1965005     95 	orrs	r5,r6,r5

000000c0 1a00003f     96 	bne	.L2

                      97 .L68:

000000c4 e2833001     98 	add	r3,r3,1

000000c8 e1a05002     99 	mov	r5,r2

000000cc e5502002    100 	ldrb	r2,[r0,-2]

000000d0 e2400001    101 	sub	r0,r0,1

000000d4 e2026080    102 	and	r6,r2,128

000000d8 e35500ff    103 	cmp	r5,255

000000dc 1a000001    104 	bne	.L70

000000e0 e3560000    105 	cmp	r6,0

000000e4 1a000001    106 	bne	.L73

                     107 .L70:

000000e8 e1965005    108 	orrs	r5,r6,r5

000000ec 1a000034    109 	bne	.L2

                     110 .L73:

000000f0 e2833001    111 	add	r3,r3,1


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fo41.s
000000f4 e1a05002    112 	mov	r5,r2

000000f8 e5502002    113 	ldrb	r2,[r0,-2]

000000fc e2400001    114 	sub	r0,r0,1

00000100 e2026080    115 	and	r6,r2,128

00000104 e35500ff    116 	cmp	r5,255

00000108 1a000001    117 	bne	.L75

0000010c e3560000    118 	cmp	r6,0

00000110 1a000001    119 	bne	.L78

                     120 .L75:

00000114 e1965005    121 	orrs	r5,r6,r5

00000118 1a000029    122 	bne	.L2

                     123 .L78:

0000011c e2833001    124 	add	r3,r3,1

00000120 e1a05002    125 	mov	r5,r2

00000124 e5502002    126 	ldrb	r2,[r0,-2]

00000128 e2400001    127 	sub	r0,r0,1

0000012c e2026080    128 	and	r6,r2,128

00000130 e35500ff    129 	cmp	r5,255

00000134 1a000001    130 	bne	.L80

00000138 e3560000    131 	cmp	r6,0

0000013c 1a000001    132 	bne	.L83

                     133 .L80:

00000140 e1965005    134 	orrs	r5,r6,r5

00000144 1a00001e    135 	bne	.L2

                     136 .L83:

00000148 e2833001    137 	add	r3,r3,1

0000014c e1a05002    138 	mov	r5,r2

00000150 e5502002    139 	ldrb	r2,[r0,-2]

00000154 e2400001    140 	sub	r0,r0,1

00000158 e2026080    141 	and	r6,r2,128

0000015c e35500ff    142 	cmp	r5,255

00000160 1a000001    143 	bne	.L85

00000164 e3560000    144 	cmp	r6,0

00000168 1a000001    145 	bne	.L87

                     146 .L85:

0000016c e1965005    147 	orrs	r5,r6,r5

00000170 1a000013    148 	bne	.L2

                     149 .L87:

00000174 e1a05002    150 	mov	r5,r2

00000178 e2400001    151 	sub	r0,r0,1

0000017c e2833001    152 	add	r3,r3,1

00000180 e25cc001    153 	subs	r12,r12,1

00000184 1affffa5    154 	bne	.L48

                     155 .L47:

00000188 e214c007    156 	ands	r12,r4,7

0000018c 0a00000c    157 	beq	.L2

                     158 .L90:

00000190 e5502001    159 	ldrb	r2,[r0,-1]

00000194 e35500ff    160 	cmp	r5,255

00000198 e2026080    161 	and	r6,r2,128

0000019c 1a000001    162 	bne	.L92

000001a0 e3560000    163 	cmp	r6,0

000001a4 1a000001    164 	bne	.L94

                     165 .L92:

000001a8 e1964005    166 	orrs	r4,r6,r5

000001ac 1a000004    167 	bne	.L2

                     168 .L94:

000001b0 e1a05002    169 	mov	r5,r2

000001b4 e2400001    170 	sub	r0,r0,1

000001b8 e2833001    171 	add	r3,r3,1

000001bc e25cc001    172 	subs	r12,r12,1


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fo41.s
000001c0 1afffff2    173 	bne	.L90

                     174 ;26:     }


                     175 ;27: 


                     176 ;28:     // Return the minimum number of bytes needed to represent the value


                     177 ;29:     return intSize - i;


                     178 

                     179 .L2:

000001c4 e0410003    180 	sub	r0,r1,r3

000001c8 e8bd0070    181 	ldmfd	[sp]!,{r4-r6}

000001cc e12fff1e*   182 	ret	

                     183 	.endf	BERCoder_calcIntEncodedLen

                     184 	.align	4

                     185 ;pValueBytes	r0	local

                     186 ;i	r3	local

                     187 ;byte	r5	local

                     188 ;nextByte	r2	local

                     189 ;nextBit	r6	local

                     190 

                     191 ;pValue	r0	param

                     192 ;intSize	r1	param

                     193 

                     194 	.section ".bss","awb"

                     195 .L519:

                     196 	.data

                     197 	.text

                     198 

                     199 ;30: }


                     200 

                     201 ;31: 


                     202 ;32: void BERCoder_reverseCopy(const void* src, uint8_t* dst, size_t len)


                     203 	.align	4

                     204 	.align	4

                     205 BERCoder_reverseCopy::

                     206 ;33: {    


                     207 

                     208 ;34:     size_t i;


                     209 ;35:     const uint8_t* pSrc = src;


                     210 

000001d0 e0823000    211 	add	r3,r2,r0

000001d4 e2430001    212 	sub	r0,r3,1

                     213 ;36:     pSrc += len - 1;


                     214 

                     215 ;37:     


                     216 ;38:     for (i = 0; i < len; i++)


                     217 

000001d8 e3520000    218 	cmp	r2,0

000001dc a1a0c002    219 	movge	r12,r2

000001e0 b3a0c000    220 	movlt	r12,0

000001e4 e1b021ac    221 	movs	r2,r12 lsr 3

000001e8 0a000011    222 	beq	.L657

                     223 .L673:

000001ec e4503008    224 	ldrb	r3,[r0],-8

000001f0 e4c13001    225 	strb	r3,[r1],1

000001f4 e5d03007    226 	ldrb	r3,[r0,7]

000001f8 e4c13001    227 	strb	r3,[r1],1

000001fc e5d03006    228 	ldrb	r3,[r0,6]

00000200 e4c13001    229 	strb	r3,[r1],1

00000204 e5d03005    230 	ldrb	r3,[r0,5]

00000208 e4c13001    231 	strb	r3,[r1],1

0000020c e5d03004    232 	ldrb	r3,[r0,4]

00000210 e4c13001    233 	strb	r3,[r1],1


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fo41.s
00000214 e5d03003    234 	ldrb	r3,[r0,3]

00000218 e4c13001    235 	strb	r3,[r1],1

0000021c e5d03002    236 	ldrb	r3,[r0,2]

00000220 e4c13001    237 	strb	r3,[r1],1

00000224 e5d03001    238 	ldrb	r3,[r0,1]

00000228 e2522001    239 	subs	r2,r2,1

0000022c e4c13001    240 	strb	r3,[r1],1

00000230 1affffed    241 	bne	.L673

                     242 .L657:

00000234 e21c2007    243 	ands	r2,r12,7

                     244 .L677:

00000238 14503001    245 	ldrneb	r3,[r0],-1

0000023c 14c13001    246 	strneb	r3,[r1],1

00000240 12522001    247 	subnes	r2,r2,1

00000244 1afffffb    248 	bne	.L677

                     249 .L625:

00000248 e12fff1e*   250 	ret	

                     251 	.endf	BERCoder_reverseCopy

                     252 	.align	4

                     253 ;pSrc	r0	local

                     254 

                     255 ;src	r0	param

                     256 ;dst	r1	param

                     257 ;len	r2	param

                     258 

                     259 	.section ".bss","awb"

                     260 .L830:

                     261 	.data

                     262 	.text

                     263 

                     264 ;41:     }


                     265 ;42: }


                     266 	.align	4

                     267 

                     268 	.data

                     269 	.ghsnote version,6

                     270 	.ghsnote tools,3

                     271 	.ghsnote options,0

                     272 	.text

                     273 	.align	4

