                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=reports.c -o gh_b281.o -list=reports.lst C:\Users\<USER>\AppData\Local\Temp\gh_b281.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
Source File: reports.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile reports.c -o

                      10 ;		reports.o

                      11 ;Source File:   reports.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:56:06 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "reports.h"


                      21 ;2: #include "rcb.h"


                      22 ;3: #include "reporter.h"


                      23 ;4: #include "mms.h"


                      24 ;5: #include "platform_thread.h"


                      25 ;6: #include "DataSlice.h"


                      26 ;7: #include "IEDCompile/InnerAttributeTypes.h"


                      27 ;8: #include "iedmodel.h"


                      28 ;9: #include"iedTree/iedTree.h"


                      29 ;10: #include "reportItems/RptDataSet.h"


                      30 ;11: #include "BaseAsnTypes.h"


                      31 ;12: #include "AsnEncoding.h"


                      32 ;13: #include "mms_data.h"


                      33 ;14: #include "control.h"


                      34 ;15: #include "timers.h"


                      35 ;16: #include "BusError.h"


                      36 ;17: #include <Clib.h>


                      37 ;18: #include <stddef.h>


                      38 ;19: #include <string.h>


                      39 ;20: #include <debug.h>


                      40 ;21: 


                      41 ;22: 


                      42 ;23: #define TRGOP_INTEGRITY (1 << 1)


                      43 ;24: #define MIN_INTG_PD 100


                      44 ;25: 


                      45 ;26: 


                      46 ;27: // Сколько байт занимает inclusion-bitstring.


                      47 ;28: // Нежелательно делать очень большим, потому что переменная создаётся


                      48 ;29: // на стеке. Желательно делать кратным 4.


                      49 ;30: #define BYTES_IN_INCLUSION_BITSTING 40


                      50 ;31: #define MAX_DATASET_OBJECT_COUNT (BYTES_IN_INCLUSION_BITSTING * 8)



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
                      51 ;32: 


                      52 ;33: #define TRG_OPS_BIT_COUNT 6


                      53 ;34: 


                      54 ;35: //Reason for Inclusion


                      55 ;36: #define REASON_DCHG DCHG


                      56 ;37: #define REASON_QCHG QCHG


                      57 ;38: #define REASON_GI 1


                      58 ;39: #define REASON_INTEGRITY (1 << 1)


                      59 ;40: 


                      60 ;41: 


                      61 ;42: #pragma alignvar (4)


                      62 ;43: 


                      63 ;44: //Fixed report "RPT" variable


                      64 ;45: uint8_t reportVarNameSequence[7] = { 0xA1, 0x05, 0x80, 0x03, 0x52, 0x50, 0x54 };


                      65 ;46: 


                      66 ;47: size_t g_reportCount = 0;


                      67 ;48: 


                      68 ;49: 


                      69 ;50: Reporter g_reports[MAX_REPORT_COUNT];


                      70 ;51: 


                      71 ;52: //Причина включения для каждого значения в отчёте


                      72 ;53: //Используется для генерации Reason-For-Inclusion


                      73 ;54: //и для генерации Inclusion-bitstring.


                      74 ;55: //Каждый элемент соответствует элементу DataSet.


                      75 ;56: //При подготовке отчёта записавается:


                      76 ;57: // 0 если элемент не включен в отчёт


                      77 ;58: // причина включения если элемент включен в отчёт


                      78 ;59: static uint8_t reportInclusionReasons[MAX_DATASET_OBJECT_COUNT];


                      79 ;60: 


                      80 ;61: //Здесь лежат закодированные значения, попавшие в отчёт.


                      81 ;62: static uint8_t reportValuesBuf[DEFAULT_REPORT_BUFFER_SIZE];


                      82 ;63: 


                      83 ;64: //Здесь лежат закодированные значения, попавшие в отчёт вместе с


                      84 ;65: //обязательными и необязательными служебными данными


                      85 ;66: static uint8_t reportAccessResultsBuf[DEFAULT_REPORT_BUFFER_SIZE];


                      86 ;67: //Здесь лежит отчёт в виде пакета MMS


                      87 ;68: static uint8_t reportMmsBuf[DEFAULT_REPORT_BUFFER_SIZE];


                      88 ;69: //Здесь лежит отчёт в виде пакета Presentation


                      89 ;70: static uint8_t reportPresentationBuf[DEFAULT_REPORT_BUFFER_SIZE];


                      90 ;71: 


                      91 ;72: // Режим обработки набора данных в отчёте:


                      92 ;73: // инициализация начальных значений


                      93 ;74: // General Interrorgation


                      94 ;75: // Режим сравнения на data change или quality change


                      95 ;76: typedef enum {RPT_GI, RPT_INTG, RPT_INIT, RPT_CMP} RptProcessMode;


                      96 ;77: 


                      97 ;78: 


                      98 ;79: // Структура для параметров обработки отчёта (проверки на изменения, GI, 


                      99 ;80: // инициализации начальных значений)


                     100 ;81: typedef struct {


                     101 ;82: 	RptProcessMode mode;	


                     102 ;83: 	//Индекс в массиве значений для сравнения


                     103 ;84: 	int valIdx;	


                     104 ;85: 	//Индекс элемета DataSet


                     105 ;86: 	size_t itemIdx;


                     106 ;87: 	BufferView* outBuf;


                     107 ;88: 	Reporter* reporter;


                     108 ;89: } RptProcContext;


                     109 ;90: 


                     110 ;91: /* 61850 - 8 - 1


                     111 ;92: 	The MMS Btime6 (TimeOfDay) type shall be an OCTET STRING. A value of the TimeOfDay



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
                     112 ;93: type may contain either 4 or 6 octets. The first form specifies the time as the number of


                     113 ;94: milliseconds since midnight on the current date (the date is not contained in the value), while


                     114 ;95: the second form contains the time and a date, expressed as the relative day since 1 January


                     115 ;96: 1984. The first four octets shall contain a value indicating the number of milliseconds since


                     116 ;97: midnight for the current date in both forms.


                     117 ;98: 


                     118 ;99: https://www.kepware.com/getattachment/be373908-e367-4779-b8ae-033c73a99b1e/iec-61850-mms-client-manual.pdf


                     119 ;100: 


                     120 ;101: 	6 byte structure containing days since Jan 1, 1984 and milliseconds since


                     121 ;102: 	midnight.It uses the format "MM/DD/YYYY_HH:MM:SS.mmm".


                     122 ;103: */


                     123 ;104: 


                     124 ;105: 


                     125 ;106: #define TIME3232_TO_TIME(t) ((unsigned long)(t/0x100000000L))


                     126 ;107: #define TIME3232FRACT_TO_MS(t)  ((unsigned long)((t&0xFFFFFFFF)*1000/0x100000000L))


                     127 ;108: 


                     128 ;109: #define msPerDay 86400000LL


                     129 ;110: #define Years14_msCount 441763200000LL


                     130 ;111: 


                     131 ;112: 


                     132 ;113: bool getRCB(size_t index, RCB** pRCB)


                     133 ;114: {


                     134 ;115: 	if (index >= g_reportCount)


                     135 ;116: 	{


                     136 ;117: 		return false;


                     137 ;118: 	}


                     138 ;119: 	*pRCB = &g_reports[index].rcb;


                     139 ;120: 	return true;


                     140 ;121: }


                     141 ;122: 


                     142 ;123: PReporter getReporterByIndex(size_t index)


                     143 ;124: {


                     144 ;125: 	if (index >= g_reportCount) 


                     145 ;126: 	{


                     146 ;127: 		return NULL;


                     147 ;128: 	}


                     148 ;129: 	return g_reports + index;


                     149 ;130: }


                     150 ;131: 


                     151 ;132: static void encodeBinaryTime(uint8_t* outBuf, unsigned long long preсiseTime)


                     152 

                     153 ;170: 


                     154 ;171: 


                     155 ;172: }


                     156 

                     157 ;173: 


                     158 ;174: 


                     159 ;175: bool isRCBConnected(PReporter pReport)


                     160 ;176: {


                     161 ;177: 	IsoConnection* conn = pReport->connection;


                     162 ;178: 	return conn != NULL && conn->connected;	


                     163 ;179: }


                     164 ;180: 


                     165 ;181: //=====Сравнение данных DataSet и создание отчёта при наличии изменений======


                     166 ;182: 


                     167 ;183: //Готовит данные отчёта для Integrity или General Interrogation


                     168 ;184: static bool processDataSetIntgGi(DataSetItem* firstDSItem, RptProcContext* context)


                     169 

                     170 ;212: }


                     171 

                     172 ;213: 



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
                     173 ;214: //Готовит данные отчёта при изменении данных


                     174 ;215: static bool processDataSetChange(DataSetItem* firstDSItem, RptProcContext* context)


                     175 

                     176 ;255: }


                     177 

                     178 ;256: 


                     179 ;257: //Записывает в буфер значения для отчёта с учтётом trgOps 


                     180 ;258: //mode. Одновременно заполняет массив с причинами передачи (Reason-For-Inclusion)


                     181 ;259: static bool writeReportValues(Reporter* pRCB, RptProcessMode mode, BufferView* outBuf)


                     182 

                     183 ;283: 	}


                     184 ;284: }


                     185 

                     186 ;285: 


                     187 ;286: //=======Инициализация текущих значений отчёта для дальнейшего сравнения==


                     188 ;287: 


                     189 ;288: bool initReportCompareDataset(PReporter reporter)


                     190 ;289: {


                     191 ;290: 		


                     192 ;291: 	DataSet* dataSet = reporter->dataSet;


                     193 ;292: 


                     194 ;293: 	if (dataSet->itemCount > MAX_DATASET_OBJECT_COUNT)


                     195 ;294: 	{


                     196 ;295: 		ERROR_REPORT("Too many dataset objects");


                     197 ;296: 		return false;


                     198 ;297: 	}


                     199 ;298: 


                     200 ;299: 	return true;


                     201 ;300: }


                     202 ;301: 


                     203 ;302: //===========================================================================


                     204 ;303: 


                     205 ;304: static int writeRptID(RCB* pRCB, uint8_t* outBuf, int outBufPos)


                     206 

                     207 ;308: 		(const uint8_t*)pRCB->rptID.p, pRCB->rptID.len, outBuf, outBufPos);


                     208 ;309: }


                     209 

                     210 ;310: 


                     211 ;311: static int writeOptFlds(uint16_t optFlds, uint8_t* outBuf, int outBufPos)


                     212 

                     213 ;317: }


                     214 

                     215 ;318: 


                     216 ;319: static int writeTimeOfEntry(RCB* pRCB, uint8_t* outBuf, int outBufPos)


                     217 

                     218 ;334: }


                     219 

                     220 ;335: 


                     221 ;336: static int writeDatSetRef(RCB* pRCB, uint8_t* outBuf, int outBufPos)


                     222 

                     223 ;340: 		pRCB->dataSetName, pRCB->dataSetNameLength, outBuf, outBufPos);


                     224 ;341: }


                     225 

                     226 ;342: 


                     227 ;343: static int writeBufferOverflow(Reporter* pRCB, uint8_t* outBuf, int outBufPos)


                     228 

                     229 ;347: 		outBuf, outBufPos);


                     230 ;348: }


                     231 

                     232 ;349: 


                     233 ;350: static int writeEntryID(RCB* pRCB, uint8_t* outBuf, int outBufPos)



                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
                     234 

                     235 ;353: }


                     236 

                     237 ;354: 


                     238 ;355: static int writeConfRev(RCB* pRCB, uint8_t* outBuf, int outBufPos)


                     239 

                     240 ;358: 		pRCB->confRev, outBuf, outBufPos);	


                     241 ;359: }


                     242 

                     243 ;360: 


                     244 ;361: static int writeInclusionBitstring(Reporter* pRCB, uint8_t* outBuf, int outBufPos)


                     245 

                     246 ;384: }


                     247 

                     248 ;385: 


                     249 ;386: static int writeReasonForInclusion(Reporter* pRCB, uint8_t* outBuf, int outBufPos)


                     250 

                     251 ;403: }


                     252 

                     253 ;404: 


                     254 ;405: static int writeReportAccessResults(Reporter* pReporter, uint8_t* outBuf, 


                     255 

                     256 ;465: }


                     257 

                     258 ;466: 


                     259 ;467: //Собирает данные для отчёта и пишет в буфер


                     260 ;468: static int writeReport(Reporter* pReporter, RptProcessMode mode)


                     261 ;469: {		


                     262 ;470: 	BufferView reportValBufView;


                     263 ;471: 	BufferView_init(&reportValBufView, reportValuesBuf, sizeof(reportValuesBuf),0);


                     264 ;472: 		


                     265 ;473: 	if(! writeReportValues(pReporter, mode, &reportValBufView))


                     266 ;474: 	{


                     267 ;475: 		ERROR_REPORT("Error processing dataset");


                     268 ;476: 		return 0;


                     269 ;477: 	}


                     270 ;478: 


                     271 ;479: 	if (reportValBufView.pos == 0)


                     272 ;480: 	{


                     273 ;481: 		//Никаких данных не собрано, отчёт не посылаем.


                     274 ;482: 		return 0;


                     275 ;483: 	}


                     276 ;484: 


                     277 ;485: 	return writeReportAccessResults(pReporter,


                     278 ;486: 		reportAccessResultsBuf, reportValuesBuf, reportValBufView.pos);


                     279 ;487: }


                     280 ;488: 


                     281 ;489: //Оформляет и отправляет отчёт


                     282 ;490: static void sendReport(IsoConnection* isoConn, uint8_t* mmsReport, int byteCount,


                     283 ;491:                 SessionOutBuffer* sessionOutBuf)


                     284 ;492: {


                     285 ;493: 	int sessionDataLen;


                     286 ;494: 	int presentationDataLen;


                     287 ;495: 	int bufPos = 0;


                     288 ;496:     int reportLen;


                     289 ;497:     int varNameLen;


                     290 ;498:     int reportVarLen;


                     291 ;499:     int reportContentLen;


                     292 ;500: 


                     293 ;501: 	if (isoConn == NULL)


                     294 ;502: 	{



                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
                     295 ;503: 		return;


                     296 ;504: 	}


                     297 ;505: 


                     298 ;506: 	//Определяем длины


                     299 ;507: 


                     300 ;508: 	//A0


                     301 ;509:     reportLen = 1 +


                     302 ;510: 		BerEncoder_determineLengthSize(byteCount) + byteCount;


                     303 ;511: 


                     304 ;512: 	//A1


                     305 ;513:     varNameLen = sizeof(reportVarNameSequence);


                     306 ;514: 	


                     307 ;515: 	//A0 без тэга


                     308 ;516:     reportVarLen = reportLen + varNameLen;


                     309 ;517: 


                     310 ;518: 	//A0 с тегом и длиной


                     311 ;519:     reportContentLen = 1 +


                     312 ;520: 		BerEncoder_determineLengthSize(reportVarLen) + reportVarLen;


                     313 ;521: 


                     314 ;522: 	// Кодируем


                     315 ;523:     //Unconfirmed PDU


                     316 ;524:     bufPos = BerEncoder_encodeTL(0xA3, reportContentLen, reportMmsBuf, bufPos);


                     317 ;525: 


                     318 ;526: 	bufPos = BerEncoder_encodeTL(0xA0, reportVarLen, reportMmsBuf, bufPos);


                     319 ;527: 	memcpy(reportMmsBuf + bufPos, reportVarNameSequence, varNameLen);


                     320 ;528: 	bufPos += varNameLen;


                     321 ;529: 	bufPos = BerEncoder_encodeTL(0xA0, byteCount, reportMmsBuf, bufPos);


                     322 ;530: 	memcpy(reportMmsBuf + bufPos, mmsReport, byteCount);


                     323 ;531: 	bufPos += byteCount;


                     324 ;532: 	byteCount = bufPos;


                     325 ;533: 	


                     326 ;534: 	presentationDataLen = IsoPresentation_createUserData(&isoConn->presentation,


                     327 ;535: 		reportPresentationBuf, reportMmsBuf, byteCount);


                     328 ;536: 


                     329 ;537:     sessionDataLen = isoSession_createDataSpdu(sessionOutBuf->cotpOutBuf,


                     330 ;538:         SESSION_OUT_BUF_SIZE, reportPresentationBuf, presentationDataLen);


                     331 ;539:     sessionOutBuf->byteCount = sessionDataLen;


                     332 ;540:     if (!OutQueue_insert(&isoConn->outQueue, sessionOutBuf))


                     333 ;541: 	{


                     334 ;542: 		ERROR_REPORT("Out queue overflow");


                     335 ;543: 		return;


                     336 ;544: 	}


                     337 ;545: }


                     338 ;546: 


                     339 ;547: static void writeReportToBuffer(Reporter* pRCB, uint8_t* pData, int dataLen)


                     340 ;548: {


                     341 ;549: 	ReportQueue_write(&pRCB->buffer, pData, dataLen);


                     342 ;550: }


                     343 ;551: 


                     344 ;552: //Выделяет SessionOutBuf и посылает данные отчёта


                     345 ;553: static void allocateBufAndSendReport(Reporter* pRCB,


                     346 ;554: 	uint8_t* reportData, int reportDataSize)


                     347 ;555: {


                     348 ;556:     SessionOutBuffer* outBuf;


                     349 ;557: 	IsoConnection* pConn = pRCB->connection;


                     350 ;558: 	if (pConn == NULL)


                     351 ;559: 	{


                     352 ;560: 		return;


                     353 ;561: 	}


                     354 ;562:     outBuf = allocSessionOutBuffer(&pConn->outBuffers, SESSION_OUT_BUF_SIZE);


                     355 ;563: 	if (outBuf)



                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
                     356 ;564: 	{


                     357 ;565: 		sendReport(pConn, reportData, reportDataSize, outBuf);


                     358 ;566: 	}


                     359 ;567: 	else


                     360 ;568: 	{


                     361 ;569: 		ERROR_REPORT("Unable to allocate buffer for the report");


                     362 ;570: 	}


                     363 ;571: }


                     364 ;572: 


                     365 ;573: //Вызывается каждую миллисекунду для таймера integrity


                     366 ;574: static void integrityTimerProc(void)


                     367 ;575: {


                     368 ;576: 	size_t rcbIdx;


                     369 ;577: 	for (rcbIdx = 0; rcbIdx < g_reportCount; ++rcbIdx)


                     370 ;578: 	{


                     371 ;579: 		Reporter* pReporter = g_reports + rcbIdx;


                     372 ;580: 		RCB* pRCB = &pReporter->rcb;


                     373 ;581: 		if ((pRCB->trgOps & TRGOP_INTEGRITY) == 0 || pRCB->intgPd == 0)


                     374 ;582: 		{


                     375 ;583: 			continue;


                     376 ;584: 		}


                     377 ;585: 		if (pReporter->intgPdCounter < pRCB->intgPd)


                     378 ;586: 		{


                     379 ;587: 			pReporter->intgPdCounter++;


                     380 ;588: 		}


                     381 ;589: 		else


                     382 ;590: 		{


                     383 ;591: 			pReporter->intgPdCounter = 0;


                     384 ;592: 			pReporter->intgTimerAlam = true;


                     385 ;593: 		}


                     386 ;594: 	}


                     387 ;595: }


                     388 ;596: 


                     389 ;597: //Обнаруживает изменения в наборах данных, зарегистрированных в отчётах,


                     390 ;598: //и при наличии посылает соответствующий отчёт(небуферизированные) 


                     391 ;599: //или помещает его в буфер (буферизированные)


                     392 ;600: static void processAllReportsData()


                     393 

                     394 ;607: 	}


                     395 ;608: }


                     396 

                     397 ;609: 


                     398 ;610: 


                     399 ;611: static void processAllReportsData_old()


                     400 ;612: {	


                     401 ;613: 	size_t rcbIdx;


                     402 ;614: 	int reportDataSize;


                     403 ;615: 	 


                     404 ;616: 	for(rcbIdx = 0; rcbIdx < g_reportCount; ++rcbIdx)


                     405 ;617: 	{


                     406 ;618: 		Reporter* pReporter = g_reports + rcbIdx;


                     407 ;619: 		RCB* pRCB = &pReporter->rcb;


                     408 ;620: 


                     409 ;621: 		if ((pRCB->rptEna && isRCBConnected(pReporter))


                     410 ;622: 			|| pRCB->buffered)


                     411 ;623: 		{			


                     412 ;624: 			//Готовим отчёт для буферизированного или небуферизированного


                     413 ;625: 			reportDataSize = writeReport(pReporter, RPT_CMP);


                     414 ;626: 			if (reportDataSize > 0)


                     415 ;627: 			{


                     416 ;628: 				if (pRCB->buffered)



                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
                     417 ;629: 				{


                     418 ;630: 					//Пишем в буфер буферизированного отчёта


                     419 ;631: 					writeReportToBuffer(pReporter, reportAccessResultsBuf,


                     420 ;632: 						reportDataSize);


                     421 ;633: 				}


                     422 ;634: 				else


                     423 ;635: 				{


                     424 ;636: 					allocateBufAndSendReport(pReporter, reportAccessResultsBuf,


                     425 ;637: 						reportDataSize);


                     426 ;638: 				}


                     427 ;639: 			}


                     428 ;640: 		}


                     429 ;641: 	}


                     430 ;642: }


                     431 ;643: 


                     432 ;644: 


                     433 ;645: static bool bufferedReportHasData(Reporter* pCurrReport)


                     434 

                     435 ;648: }


                     436 

                     437 ;649: 


                     438 ;650: static int readReportFromBuffer(Reporter* pRCB, uint8_t* bufferToRead, int bufSize)


                     439 

                     440 ;653: }


                     441 

                     442 ;654: 


                     443 ;655: static void sendFromReportBuf(Reporter* pRCB)


                     444 

                     445 ;666: 				&pRCB->sessionOutBuffer);


                     446 ;667: 		}


                     447 ;668: 	}		


                     448 ;669: }


                     449 

                     450 ;670: 


                     451 ;671: 


                     452 ;672: //Посылает GI если установлены соответствующие флаги в RCB.


                     453 ;673: //Сбрасывает GI после посылки.


                     454 ;674: //Возвращает FALSE если GI не нужно.


                     455 ;675: static bool processGI(Reporter* pReporter)


                     456 ;676: {


                     457 ;677: 	int reportDataSize;


                     458 ;678: 	if (!pReporter->rcb.gi)


                     459 ;679: 	{


                     460 ;680: 		return false;


                     461 ;681: 	}	


                     462 ;682: 	reportDataSize = writeReport(pReporter, RPT_GI);


                     463 ;683: 	if (reportDataSize > 0)


                     464 ;684: 	{


                     465 ;685: 		allocateBufAndSendReport(pReporter, reportAccessResultsBuf, reportDataSize);


                     466 ;686: 		pReporter->rcb.gi = false;


                     467 ;687: 	}	


                     468 ;688: 	return true;


                     469 ;689: }


                     470 ;690: 


                     471 ;691: //Посылает Integrity report если установлены соответствующие флаги в RCB


                     472 ;692: //и время пришло.


                     473 ;693: //Возвращает false если Intregrity report не послан.


                     474 ;694: static bool processIntegrity(Reporter* pReporter)


                     475 

                     476 ;729: }


                     477 


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
                     478 	.text

                     479 	.align	4

                     480 getRCB::

00000000 e59f3d8c*   481 	ldr	r3,.L649

00000004 e5932000    482 	ldr	r2,[r3]

00000008 e1500002    483 	cmp	r0,r2

0000000c 23a00000    484 	movhs	r0,0

00000010 2a000008    485 	bhs	.L590

00000014 e1a02100    486 	mov	r2,r0 lsl 2

00000018 e0400002    487 	sub	r0,r0,r2

0000001c e0800102    488 	add	r0,r0,r2 lsl 2

00000020 e0800400    489 	add	r0,r0,r0 lsl 8

00000024 e0800582    490 	add	r0,r0,r2 lsl 11

00000028 e59f2d68*   491 	ldr	r2,.L650

0000002c e0820180    492 	add	r0,r2,r0 lsl 3

00000030 e5810000    493 	str	r0,[r1]

00000034 e3a00001    494 	mov	r0,1

                     495 .L590:

00000038 e12fff1e*   496 	ret	

                     497 	.endf	getRCB

                     498 	.align	4

                     499 

                     500 ;index	r0	param

                     501 ;pRCB	r1	param

                     502 

                     503 	.section ".bss","awb"

                     504 .L635:

                     505 	.data

                     506 	.text

                     507 

                     508 

                     509 	.align	4

                     510 	.align	4

                     511 getReporterByIndex::

0000003c e59f2d50*   512 	ldr	r2,.L649

00000040 e5921000    513 	ldr	r1,[r2]

00000044 e1500001    514 	cmp	r0,r1

00000048 23a00000    515 	movhs	r0,0

0000004c 2a000006    516 	bhs	.L651

00000050 e1a01100    517 	mov	r1,r0 lsl 2

00000054 e0400001    518 	sub	r0,r0,r1

00000058 e0800101    519 	add	r0,r0,r1 lsl 2

0000005c e0800400    520 	add	r0,r0,r0 lsl 8

00000060 e0800581    521 	add	r0,r0,r1 lsl 11

00000064 e59f1d2c*   522 	ldr	r1,.L650

00000068 e0810180    523 	add	r0,r1,r0 lsl 3

                     524 .L651:

0000006c e12fff1e*   525 	ret	

                     526 	.endf	getReporterByIndex

                     527 	.align	4

                     528 

                     529 ;index	r0	param

                     530 

                     531 	.section ".bss","awb"

                     532 .L678:

                     533 	.data

                     534 	.text

                     535 

                     536 

                     537 	.align	4

                     538 	.align	4


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
                     539 isRCBConnected::

00000070 e5901040    540 	ldr	r1,[r0,64]

00000074 e3510000    541 	cmp	r1,0

00000078 13a02bf2    542 	movne	r2,242<<10

0000007c 12822098    543 	addne	r2,r2,152

00000080 17d21001    544 	ldrneb	r1,[r2,r1]

00000084 e3a00000    545 	mov	r0,0

00000088 13510000    546 	cmpne	r1,0

0000008c 13a00001    547 	movne	r0,1

00000090 e20000ff    548 	and	r0,r0,255

00000094 e12fff1e*   549 	ret	

                     550 	.endf	isRCBConnected

                     551 	.align	4

                     552 ;conn	r1	local

                     553 

                     554 ;pReport	r0	param

                     555 

                     556 	.section ".bss","awb"

                     557 .L746:

                     558 	.data

                     559 	.text

                     560 

                     561 

                     562 	.align	4

                     563 	.align	4

                     564 initReportCompareDataset::

00000098 e5900038    565 	ldr	r0,[r0,56]

0000009c e5900004    566 	ldr	r0,[r0,4]

000000a0 e3500f50    567 	cmp	r0,0x0140

000000a4 93a00001    568 	movls	r0,1

000000a8 83a00000    569 	movhi	r0,0

000000ac e12fff1e*   570 	ret	

                     571 	.endf	initReportCompareDataset

                     572 	.align	4

                     573 ;dataSet	r0	local

                     574 

                     575 ;reporter	r0	param

                     576 

                     577 	.section ".bss","awb"

                     578 .L789:

                     579 	.data

                     580 	.text

                     581 

                     582 

                     583 	.align	4

                     584 	.align	4

                     585 	.align	4

                     586 writeReport:

000000b0 e92d4ff0    587 	stmfd	[sp]!,{r4-fp,lr}

000000b4 e59fbce0*   588 	ldr	fp,.L2600

000000b8 e24dd084    589 	sub	sp,sp,132

000000bc e1a08000    590 	mov	r8,r0

000000c0 e1a06000    591 	mov	r6,r0

000000c4 e1a04001    592 	mov	r4,r1

000000c8 e59f1cd0*   593 	ldr	r1,.L2601

000000cc e1a05000    594 	mov	r5,r0

000000d0 e28d0078    595 	add	r0,sp,120

000000d4 e3a03000    596 	mov	r3,0

000000d8 e3a02d80    597 	mov	r2,1<<13

000000dc eb000000*   598 	bl	BufferView_init

                     599 ;260: {



                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
                     600 

                     601 ;261: 	RptProcContext rptProcContext;


                     602 ;262: 	DataSet* dataSet = pRCB->dataSet;


                     603 

000000e0 e5951038    604 	ldr	r1,[r5,56]

                     605 ;263: 


                     606 ;264: 	if (dataSet->itemCount > MAX_DATASET_OBJECT_COUNT)


                     607 

000000e4 e591c004    608 	ldr	r12,[r1,4]

000000e8 e3a00000    609 	mov	r0,0

000000ec e35c0f50    610 	cmp	r12,0x0140

                     611 ;265: 	{


                     612 

                     613 ;266: 		ERROR_REPORT("Too many dataset objects");


                     614 ;267: 		return false;


                     615 

000000f0 8a0001e2    616 	bhi	.L796

                     617 ;268: 	}


                     618 ;269: 


                     619 ;270: 	rptProcContext.mode = mode;


                     620 

000000f4 e1a0c004    621 	mov	r12,r4

000000f8 e58dc064    622 	str	r12,[sp,100]

                     623 ;271: 	rptProcContext.valIdx = 0;	


                     624 

000000fc e58d0068    625 	str	r0,[sp,104]

                     626 ;272: 	rptProcContext.outBuf = outBuf;	


                     627 

00000100 e28d0078    628 	add	r0,sp,120

00000104 e58d0070    629 	str	r0,[sp,112]

                     630 ;273: 	rptProcContext.reporter = pRCB;


                     631 

00000108 e1a05006    632 	mov	r5,r6

0000010c e58d5074    633 	str	r5,[sp,116]

                     634 ;274: 


                     635 ;275: 


                     636 ;276: 	if(mode == RPT_CMP)


                     637 

00000110 e5911000    638 	ldr	r1,[r1]

00000114 e3540003    639 	cmp	r4,3

00000118 1a000028    640 	bne	.L817

                     641 ;277: 	{


                     642 

                     643 ;278: 		return processDataSetChange(dataSet->firstItem, &rptProcContext);


                     644 

                     645 ;216: {


                     646 

                     647 ;217: 	DataSetItem* dsItem = firstDSItem;		


                     648 

0000011c e5d5001a    649 	ldrb	r0,[r5,26]

00000120 e1b04001    650 	movs	r4,r1

                     651 ;218: 	TrgOps changed;


                     652 ;219: 	TrgOps trgOps = (TrgOps)context->reporter->rcb.trgOps;


                     653 

00000124 e58d0014    654 	str	r0,[sp,20]

                     655 ;220: 


                     656 ;221: 	context->itemIdx = 0;


                     657 

00000128 e3a01000    658 	mov	r1,0

0000012c e58d106c    659 	str	r1,[sp,108]

                     660 ;222: 



                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
                     661 ;223: 	while (dsItem != NULL)


                     662 

00000130 e28d7064    663 	add	r7,sp,100

00000134 0a000020    664 	beq	.L815

                     665 .L806:

                     666 ;224: 	{


                     667 

                     668 ;225: 		IEDEntity iedObj = dsItem->obj;


                     669 

00000138 e5946014    670 	ldr	r6,[r4,20]

                     671 ;226: 		if(iedObj == NULL)


                     672 

0000013c e3560000    673 	cmp	r6,0

00000140 0a00000b    674 	beq	.L810

                     675 ;227: 		{


                     676 

                     677 ;228: 			ERROR_REPORT("Invalid pointer to IEDEntity");


                     678 ;229: 			return false;


                     679 

                     680 ;230: 		}		


                     681 ;231: 


                     682 ;232: 		//Для объекта данных проверяем наличие изменений


                     683 ;233: 		changed =  IEDEntity_findChanges(iedObj, trgOps);


                     684 

00000144 e59d1014    685 	ldr	r1,[sp,20]

00000148 e1a00006    686 	mov	r0,r6

0000014c eb000000*   687 	bl	IEDEntity_findChanges

00000150 e1a05000    688 	mov	r5,r0

00000154 e1b0c005    689 	movs	r12,r5

                     690 ;234: 


                     691 ;235: 		if(changed != TRGOP_NONE)


                     692 

00000158 0a00000f    693 	beq	.L812

                     694 ;236: 		{


                     695 

                     696 ;237: 			//Добавляем в отчёт


                     697 ;238: 			if(!iedObj->encodeRead(iedObj, context->outBuf ))


                     698 

0000015c e596c05c    699 	ldr	r12,[r6,92]

00000160 e28d1078    700 	add	r1,sp,120

00000164 e1a00006    701 	mov	r0,r6

00000168 e1a0e00f    702 	mov	lr,pc

0000016c e12fff1c*   703 	bx	r12

00000170 e3500000    704 	cmp	r0,0

                     705 .L810:

                     706 ;239: 			{


                     707 

                     708 ;240: 				ERROR_REPORT("Report data reading error");


                     709 ;241: 				return false;


                     710 

00000174 0a00002e    711 	beq	.L831

                     712 .L811:

                     713 ;242: 			}


                     714 ;243: 


                     715 ;244: 			reportInclusionReasons[context->itemIdx] = changed;


                     716 

                     717 ;250: 		}


                     718 ;251: 		context->itemIdx++;


                     719 

00000178 e59f0c24*   720 	ldr	r0,.L2602

0000017c e5971008    721 	ldr	r1,[r7,8]


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
00000180 e7c05001    722 	strb	r5,[r0,r1]

00000184 e2811001    723 	add	r1,r1,1

00000188 e5944000    724 	ldr	r4,[r4]

0000018c e5871008    725 	str	r1,[r7,8]

                     726 ;252: 		dsItem = dsItem->next;


                     727 

00000190 e3540000    728 	cmp	r4,0

00000194 1affffe7    729 	bne	.L806

00000198 ea000007    730 	b	.L815

                     731 .L812:

                     732 ;245: 		}


                     733 ;246: 		else


                     734 ;247: 		{


                     735 

                     736 ;248: 			// Элемент не изменился, причины добавлять нет


                     737 ;249: 			reportInclusionReasons[context->itemIdx] = 0;


                     738 

                     739 ;250: 		}


                     740 ;251: 		context->itemIdx++;


                     741 

0000019c e59f0c00*   742 	ldr	r0,.L2602

000001a0 e5971008    743 	ldr	r1,[r7,8]

000001a4 e7c0c001    744 	strb	r12,[r0,r1]

000001a8 e2811001    745 	add	r1,r1,1

000001ac e5944000    746 	ldr	r4,[r4]

000001b0 e5871008    747 	str	r1,[r7,8]

                     748 ;252: 		dsItem = dsItem->next;


                     749 

000001b4 e3540000    750 	cmp	r4,0

000001b8 1affffde    751 	bne	.L806

                     752 .L815:

                     753 ;253: 	}


                     754 ;254: 	return true;


                     755 

000001bc ea00001a    756 	b	.L798

                     757 .L817:

                     758 ;279: 	}


                     759 ;280: 	else


                     760 ;281: 	{


                     761 

                     762 ;282: 		return processDataSetIntgGi(dataSet->firstItem, &rptProcContext);


                     763 

                     764 ;185: {


                     765 

                     766 ;186: 	DataSetItem* dsItem = firstDSItem;	


                     767 

000001c0 e1a04001    768 	mov	r4,r1

                     769 ;187: 	uint8_t reason = context->mode == RPT_GI ? REASON_GI : REASON_INTEGRITY;


                     770 

000001c4 e3a01000    771 	mov	r1,0

000001c8 e58d106c    772 	str	r1,[sp,108]

                     773 ;190: 


                     774 ;191: 	while (dsItem != NULL)


                     775 

000001cc e28d6064    776 	add	r6,sp,100

000001d0 e1a07000    777 	mov	r7,r0

000001d4 e3a05002    778 	mov	r5,2

000001d8 e35c0000    779 	cmp	r12,0

000001dc 03a05001    780 	moveq	r5,1

                     781 ;188: 


                     782 ;189: 	context->itemIdx = 0;



                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
                     783 

000001e0 e3540000    784 	cmp	r4,0

000001e4 0a000010    785 	beq	.L798

                     786 .L820:

                     787 ;192: 	{


                     788 

                     789 ;193: 		IEDEntity iedObj = dsItem->obj;


                     790 

000001e8 e5940014    791 	ldr	r0,[r4,20]

                     792 ;194: 		if(iedObj == NULL)


                     793 

000001ec e3500000    794 	cmp	r0,0

000001f0 0a000004    795 	beq	.L823

                     796 ;195: 		{


                     797 

                     798 ;196: 			ERROR_REPORT("Invalid poiter to IEDEntity");


                     799 ;197: 			return false;


                     800 

                     801 ;198: 		}


                     802 ;199: 


                     803 ;200: 		if(!iedObj->encodeRead(iedObj, context->outBuf ))


                     804 

000001f4 e590c05c    805 	ldr	r12,[r0,92]

000001f8 e1a01007    806 	mov	r1,r7

000001fc e1a0e00f    807 	mov	lr,pc

00000200 e12fff1c*   808 	bx	r12

00000204 e3500000    809 	cmp	r0,0

                     810 .L823:

                     811 ;201: 		{


                     812 

                     813 ;202: 			ERROR_REPORT("Report data reading error");


                     814 ;203: 			return false;


                     815 

00000208 0a000009    816 	beq	.L831

                     817 .L824:

                     818 ;204: 		}


                     819 ;205: 		reportInclusionReasons[context->itemIdx] = reason;


                     820 

0000020c e59f0b90*   821 	ldr	r0,.L2602

00000210 e5961008    822 	ldr	r1,[r6,8]

00000214 e7c05001    823 	strb	r5,[r0,r1]

                     824 ;206: 


                     825 ;207: 		context->itemIdx++;


                     826 

00000218 e2811001    827 	add	r1,r1,1

0000021c e5944000    828 	ldr	r4,[r4]

00000220 e5861008    829 	str	r1,[r6,8]

                     830 ;208: 


                     831 ;209: 		dsItem = dsItem->next;


                     832 

00000224 e3540000    833 	cmp	r4,0

00000228 1affffee    834 	bne	.L820

                     835 ;210: 	}


                     836 ;211: 	return true;


                     837 

                     838 .L798:

0000022c e59d107c    839 	ldr	r1,[sp,124]

00000230 e3510000    840 	cmp	r1,0

                     841 .L831:

00000234 03a00000    842 	moveq	r0,0

00000238 0a000190    843 	beq	.L796


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
                     844 .L830:

0000023c e1d862b0    845 	ldrh	r6,[r8,32]

00000240 e1a09008    846 	mov	r9,r8

                     847 ;410: 	uint16_t optFlds = pRCB->optFlds;


                     848 

00000244 e1cd60ba    849 	strh	r6,[sp,10]

                     850 ;411: 	


                     851 ;412: 


                     852 ;413: 	outBufPos = writeRptID(pRCB, outBuf, outBufPos);


                     853 

                     854 ;305: {		


                     855 

                     856 ;306: 	//Используется OctetString потому что она позволяет указать длину строки


                     857 ;307: 	return BerEncoder_encodeOctetString(IEC61850_BER_VISIBLE_STRING,


                     858 

00000248 e58d1014    859 	str	r1,[sp,20]

                     860 ;406: 	uint8_t* encodedValues, int encodedValuesSize)


                     861 ;407: {


                     862 

                     863 ;408: 	int outBufPos = 0;


                     864 

                     865 ;409: 	RCB* pRCB = &pReporter->rcb;


                     866 

0000024c e3a01000    867 	mov	r1,0

00000250 e59f5b50*   868 	ldr	r5,.L2603

00000254 e58d1000    869 	str	r1,[sp]

00000258 e1a03005    870 	mov	r3,r5

0000025c e9980003    871 	ldmed	[r8],{r0-r1}

00000260 e1a02000    872 	mov	r2,r0

00000264 e3a0008a    873 	mov	r0,138

00000268 eb000000*   874 	bl	BerEncoder_encodeOctetString

0000026c e2504000    875 	subs	r4,r0,0

                     876 ;414: 	RET_IF_NOT(outBufPos > 0, "Error writing RptID");


                     877 

00000270 da000181    878 	ble	.L873

                     879 ;415: 	


                     880 ;416: 	outBufPos = writeOptFlds(optFlds, outBuf, outBufPos);


                     881 

                     882 ;312: {	


                     883 

                     884 ;313: 	outBufPos = BerEncoder_encodeUshortBitString(IEC61850_BER_BIT_STRING, 10, 


                     885 

00000274 e58d4000    886 	str	r4,[sp]

00000278 e1a03005    887 	mov	r3,r5

0000027c e1a02006    888 	mov	r2,r6

00000280 e3a0100a    889 	mov	r1,10

00000284 e3a00084    890 	mov	r0,132

00000288 eb000000*   891 	bl	BerEncoder_encodeUshortBitString

                     892 ;314: 		optFlds, outBuf, outBufPos);


                     893 ;315: 


                     894 ;316: 	return outBufPos;		


                     895 

0000028c e2504000    896 	subs	r4,r0,0

                     897 ;417: 	RET_IF_NOT(outBufPos > 0, "Error writing OptFlds");


                     898 

00000290 da000179    899 	ble	.L873

                     900 ;418: 		


                     901 ;419: 	if (optFlds & OPTFLDS_TIME_STAMP)


                     902 

00000294 e1dd00ba    903 	ldrh	r0,[sp,10]

00000298 e3100080    904 	tst	r0,128


                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
0000029c 0a00004d    905 	beq	.L845

                     906 ;420: 	{


                     907 

                     908 ;421: 		outBufPos = writeTimeOfEntry(pRCB, outBuf, outBufPos);


                     909 

                     910 ;320: {	


                     911 

                     912 ;321: 	//0x8C, 0x06, 0x01, 0x90, 0xE4, 0x3A, 0x32, 0x13


                     913 ;322: 	/*


                     914 ;323: 	uint8_t timeStampBuf[8] = { 0x01, 0x90, 0xE4, 0x3A, 0x32, 0x13 };


                     915 ;324: 	RCB* pReport = g_reports + reportIndex;


                     916 ;325: 	return BerEncoder_encodeOctetString( 0x8C, timeStampBuf, 6, outBuf,


                     917 ;326: 		outBufPos);


                     918 ;327: 	*/


                     919 ;328: 	unsigned long long timestamp;	


                     920 ;329: 	timestamp = dataSliceGetTimeStamp();


                     921 

000002a0 eb000000*   922 	bl	dataSliceGetTimeStamp

000002a4 e58d1028    923 	str	r1,[sp,40]

                     924 ;330: 	outBuf[outBufPos++] = 0x8C;


                     925 

000002a8 e2846001    926 	add	r6,r4,1

000002ac e58d0024    927 	str	r0,[sp,36]

000002b0 e3a0208c    928 	mov	r2,140

000002b4 e7c52004    929 	strb	r2,[r5,r4]

                     930 ;331: 	outBuf[outBufPos++] = 0x06;


                     931 

000002b8 e3a02006    932 	mov	r2,6

000002bc e7c52006    933 	strb	r2,[r5,r6]

000002c0 e3a02000    934 	mov	r2,0

000002c4 e3a03001    935 	mov	r3,1

000002c8 eb000000*   936 	bl	__gh_udiv64

000002cc e59d2024    937 	ldr	r2,[sp,36]

000002d0 e3a0affa    938 	mov	r10,0x03e8

000002d4 e08ec29a    939 	umull	r12,lr,r10,r2

000002d8 e2866001    940 	add	r6,r6,1

                     941 ;332: 	encodeBinaryTime(outBuf + outBufPos, timestamp);


                     942 

000002dc e58d001c    943 	str	r0,[sp,28]

000002e0 e58de038    944 	str	lr,[sp,56]

000002e4 e1a03102    945 	mov	r3,r2 lsl 2

000002e8 e0422003    946 	sub	r2,r2,r3

000002ec e0822283    947 	add	r2,r2,r3 lsl 5

000002f0 e1a00182    948 	mov	r0,r2 lsl 3

000002f4 e58d0034    949 	str	r0,[sp,52]

000002f8 e3a03001    950 	mov	r3,1

000002fc e3a01000    951 	mov	r1,0

00000300 e58d1020    952 	str	r1,[sp,32]

                     953 ;143: 	uint32_t mSecCount = TIME3232FRACT_TO_MS(preсiseTime);


                     954 

00000304 e1a02001    955 	mov	r2,r1

00000308 e1a0100e    956 	mov	r1,lr

0000030c eb000000*   957 	bl	__gh_udiv64

                     958 ;144: 	timestamp = secCount * 1000 + mSecCount;


                     959 

00000310 e59d301c    960 	ldr	r3,[sp,28]

00000314 e1a07003    961 	mov	r7,r3

00000318 e082339a    962 	umull	r3,r2,r10,r3

0000031c e59d3020    963 	ldr	r3,[sp,32]

00000320 e0864005    964 	add	r4,r6,r5

                     965 ;133: {



                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
                     966 

                     967 ;134: 	


                     968 ;135: 	unsigned long long mmsTime;


                     969 ;136: 	unsigned long long timestamp;


                     970 ;137:     uint8_t* binaryTimeBuf;


                     971 ;138:     uint16_t daysDiff;


                     972 ;139:     uint8_t* daysDiffBuf;


                     973 ;140:     uint32_t msSinceMidnight;


                     974 ;141:     uint8_t* msSinceMidnightBuf;


                     975 ;142: 	unsigned long long secCount = TIME3232_TO_TIME(preсiseTime);


                     976 

00000324 e1a0c103    977 	mov	r12,r3 lsl 2

00000328 e043300c    978 	sub	r3,r3,r12

0000032c e083328c    979 	add	r3,r3,r12 lsl 5

00000330 e0821183    980 	add	r1,r2,r3 lsl 3

00000334 e0472107    981 	sub	r2,r7,r7 lsl 2

00000338 e0822387    982 	add	r2,r2,r7 lsl 7

0000033c e0900182    983 	adds	r0,r0,r2 lsl 3

00000340 e2a11000    984 	adc	r1,r1,0

                     985 ;145: 


                     986 ;146: 	if (timestamp > Years14_msCount)


                     987 

00000344 e59f2a60*   988 	ldr	r2,.L2604

00000348 e3510066    989 	cmp	r1,102

0000034c 01500002    990 	cmpeq	r0,r2

                     991 ;148: 	else


                     992 ;149: 		mmsTime = 0;


                     993 

00000350 93a00000    994 	movls	r0,0

00000354 958d0034    995 	strls	r0,[sp,52]

00000358 9a000002    996 	bls	.L843

                     997 ;147: 		mmsTime = timestamp - (Years14_msCount);


                     998 

0000035c e0502002    999 	subs	r2,r0,r2

00000360 e58d2034   1000 	str	r2,[sp,52]

00000364 e2c10066   1001 	sbc	r0,r1,102

                    1002 .L843:

00000368 e59f2a40*  1003 	ldr	r2,.L2605

0000036c e58d0038   1004 	str	r0,[sp,56]

00000370 e1a01000   1005 	mov	r1,r0

                    1006 ;150: 


                    1007 ;151:     binaryTimeBuf = outBuf;


                    1008 

                    1009 ;152: 


                    1010 ;153: 


                    1011 ;154:     daysDiff = (uint16_t)(mmsTime / (msPerDay));


                    1012 

00000374 e59d0034   1013 	ldr	r0,[sp,52]

00000378 e3a03000   1014 	mov	r3,0

0000037c eb000000*  1015 	bl	__gh_udiv64

00000380 e1cd00b8   1016 	strh	r0,[sp,8]

                    1017 ;155:     daysDiffBuf = (uint8_t*)&daysDiff;


                    1018 

                    1019 ;156: 


                    1020 ;157: 


                    1021 ;158: 	binaryTimeBuf[4] = daysDiffBuf[1];


                    1022 

00000384 e5dd2009   1023 	ldrb	r2,[sp,9]

00000388 e5c42004   1024 	strb	r2,[r4,4]

                    1025 ;159: 	binaryTimeBuf[5] = daysDiffBuf[0];


                    1026 


                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
0000038c e5dd2008   1027 	ldrb	r2,[sp,8]

00000390 e59d1038   1028 	ldr	r1,[sp,56]

00000394 e5c42005   1029 	strb	r2,[r4,5]

                    1030 ;160: 


                    1031 ;161: 


                    1032 ;162:     msSinceMidnight = mmsTime % (msPerDay);


                    1033 

00000398 e59f2a10*  1034 	ldr	r2,.L2605

0000039c e59d0034   1035 	ldr	r0,[sp,52]

000003a0 e3a03000   1036 	mov	r3,0

000003a4 eb000000*  1037 	bl	__gh_urem64

000003a8 e58d0004   1038 	str	r0,[sp,4]

                    1039 ;163:     msSinceMidnightBuf = (uint8_t*)&msSinceMidnight;


                    1040 

                    1041 ;164: 


                    1042 ;165: 


                    1043 ;166: 	binaryTimeBuf[0] = msSinceMidnightBuf[3];


                    1044 

000003ac e5dd1007   1045 	ldrb	r1,[sp,7]

000003b0 e5c41000   1046 	strb	r1,[r4]

                    1047 ;167: 	binaryTimeBuf[1] = msSinceMidnightBuf[2];


                    1048 

000003b4 e5dd1006   1049 	ldrb	r1,[sp,6]

000003b8 e5c41001   1050 	strb	r1,[r4,1]

                    1051 ;168: 	binaryTimeBuf[2] = msSinceMidnightBuf[1];


                    1052 

000003bc e5dd1005   1053 	ldrb	r1,[sp,5]

000003c0 e5c41002   1054 	strb	r1,[r4,2]

                    1055 ;169: 	binaryTimeBuf[3] = msSinceMidnightBuf[0];


                    1056 

000003c4 e5dd1004   1057 	ldrb	r1,[sp,4]

000003c8 e5c41003   1058 	strb	r1,[r4,3]

                    1059 ;333: 	return outBufPos + 6;


                    1060 

000003cc e2864006   1061 	add	r4,r6,6

                    1062 ;422: 		RET_IF_NOT(outBufPos > 0, "Error writing TimeOfEntry");


                    1063 

000003d0 e3540000   1064 	cmp	r4,0

000003d4 da000128   1065 	ble	.L873

                    1066 .L845:

                    1067 ;423: 	}


                    1068 ;424: 	


                    1069 ;425: 	if (optFlds & OPTFLDS_DATA_SET)


                    1070 

000003d8 e1dd00ba   1071 	ldrh	r0,[sp,10]

000003dc e3100020   1072 	tst	r0,32

000003e0 0a000007   1073 	beq	.L848

                    1074 ;426: 	{


                    1075 

                    1076 ;427: 		outBufPos = writeDatSetRef(pRCB, outBuf, outBufPos);


                    1077 

                    1078 ;337: {	


                    1079 

                    1080 ;338: 	//Используется OctetString потому что она позволяет указать длину строки


                    1081 ;339: 	return BerEncoder_encodeOctetString(IEC61850_BER_VISIBLE_STRING,


                    1082 

000003e4 e1a03005   1083 	mov	r3,r5

000003e8 e58d4000   1084 	str	r4,[sp]

000003ec e5992010   1085 	ldr	r2,[r9,16]

000003f0 e599100c   1086 	ldr	r1,[r9,12]

000003f4 e3a0008a   1087 	mov	r0,138


                                                                      Page 19
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
000003f8 eb000000*  1088 	bl	BerEncoder_encodeOctetString

000003fc e2504000   1089 	subs	r4,r0,0

                    1090 ;428: 		RET_IF_NOT(outBufPos > 0, "Error writing DatSet");


                    1091 

00000400 da00011d   1092 	ble	.L873

                    1093 .L848:

                    1094 ;429: 	}


                    1095 ;430: 


                    1096 ;431: 	if (optFlds & OPTFLDS_BUFFER_OVERFLOW)


                    1097 

00000404 e1dd00ba   1098 	ldrh	r0,[sp,10]

00000408 e3100008   1099 	tst	r0,8

0000040c 0a000008   1100 	beq	.L851

                    1101 ;432: 	{


                    1102 

                    1103 ;433: 		outBufPos = writeBufferOverflow(pReporter, outBuf, outBufPos);


                    1104 

                    1105 ;344: {


                    1106 

                    1107 ;345: 	bool overflow = ReportQueue_isOverflow(&pRCB->buffer);


                    1108 

00000410 e2880044   1109 	add	r0,r8,68

00000414 eb000000*  1110 	bl	ReportQueue_isOverflow

00000418 e1a03004   1111 	mov	r3,r4

0000041c e1a02005   1112 	mov	r2,r5

00000420 e20010ff   1113 	and	r1,r0,255

                    1114 ;346: 	return BerEncoder_encodeBoolean(IEC61850_BER_BOOLEAN, overflow,


                    1115 

00000424 e3a00083   1116 	mov	r0,131

00000428 eb000000*  1117 	bl	BerEncoder_encodeBoolean

0000042c e2504000   1118 	subs	r4,r0,0

                    1119 ;434: 		RET_IF_NOT(outBufPos > 0, "Error writing <buffer overflow> field");


                    1120 

00000430 da000111   1121 	ble	.L873

                    1122 .L851:

                    1123 ;435: 	}


                    1124 ;436: 


                    1125 ;437: 


                    1126 ;438: 	if (optFlds & OPTFLDS_ENTRY_ID)


                    1127 

00000434 e1dd00ba   1128 	ldrh	r0,[sp,10]

00000438 e3100004   1129 	tst	r0,4

0000043c 0a000006   1130 	beq	.L854

                    1131 ;439: 	{


                    1132 

                    1133 ;440: 		outBufPos = writeEntryID(pRCB, outBuf, outBufPos);


                    1134 

                    1135 ;351: {	


                    1136 

                    1137 ;352: 	return encodeOctetString8Value(outBuf, outBufPos, &pRCB->entryID, FALSE);


                    1138 

00000440 e2892024   1139 	add	r2,r9,36

00000444 e1a01004   1140 	mov	r1,r4

00000448 e1a00005   1141 	mov	r0,r5

0000044c e3a03000   1142 	mov	r3,0

00000450 eb000000*  1143 	bl	encodeOctetString8Value

00000454 e2504000   1144 	subs	r4,r0,0

                    1145 ;441: 		RET_IF_NOT(outBufPos > 0, "Error writing EntryID");


                    1146 

00000458 da000107   1147 	ble	.L873

                    1148 .L854:


                                                                      Page 20
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
                    1149 ;442: 	}


                    1150 ;443: 	pRCB->entryID++;


                    1151 

0000045c e1a07009   1152 	mov	r7,r9

00000460 e5971024   1153 	ldr	r1,[r7,36]

00000464 e597c028   1154 	ldr	r12,[r7,40]

00000468 e2912001   1155 	adds	r2,r1,1

0000046c e5872024   1156 	str	r2,[r7,36]

00000470 e2ac3000   1157 	adc	r3,r12,0

00000474 e1dd00ba   1158 	ldrh	r0,[sp,10]

00000478 e5873028   1159 	str	r3,[r7,40]

                    1160 ;444: 


                    1161 ;445: 	if (optFlds & OPTFLDS_CONF_REV)


                    1162 

0000047c e3100002   1163 	tst	r0,2

00000480 0a000006   1164 	beq	.L857

                    1165 ;446: 	{


                    1166 

                    1167 ;447: 		outBufPos = writeConfRev(pRCB, outBuf, outBufPos);


                    1168 

                    1169 ;356: {


                    1170 

                    1171 ;357: 	return BerEncoder_encodeUInt32WithTL(IEC61850_BER_UNSIGNED_INTEGER,


                    1172 

00000484 e1a03004   1173 	mov	r3,r4

00000488 e1a02005   1174 	mov	r2,r5

0000048c e5971014   1175 	ldr	r1,[r7,20]

00000490 e3a00086   1176 	mov	r0,134

00000494 eb000000*  1177 	bl	BerEncoder_encodeUInt32WithTL

00000498 e2504000   1178 	subs	r4,r0,0

                    1179 ;448: 		RET_IF_NOT(outBufPos > 0, "Error writing ConfRev");


                    1180 

0000049c da0000f6   1181 	ble	.L873

                    1182 .L857:

                    1183 ;449: 	}


                    1184 ;450: 	


                    1185 ;451: 	outBufPos = writeInclusionBitstring(pReporter, outBuf, outBufPos);


                    1186 

000004a0 e58d4010   1187 	str	r4,[sp,16]

                    1188 ;362: {    


                    1189 

                    1190 ;363:     //inclusion-bitstring


                    1191 ;364:     uint8_t inclusionBitString[BYTES_IN_INCLUSION_BITSTING];


                    1192 ;365: 	int inclusionFlagNum;


                    1193 ;366: 	int dataSetObjectCount = pRCB->dataSet->itemCount;


                    1194 

000004a4 e28d003c   1195 	add	r0,sp,60

000004a8 e5981038   1196 	ldr	r1,[r8,56]

000004ac e3a02028   1197 	mov	r2,40

000004b0 e5919004   1198 	ldr	r9,[r1,4]

                    1199 ;367: 


                    1200 ;368:     memset(inclusionBitString, 0, BYTES_IN_INCLUSION_BITSTING);


                    1201 

000004b4 e3a01000   1202 	mov	r1,0

000004b8 eb000000*  1203 	bl	memset

                    1204 ;369: 	


                    1205 ;370: 	for (inclusionFlagNum = 0; inclusionFlagNum < dataSetObjectCount; ++inclusionFlagNum)


                    1206 

000004bc e3a0c000   1207 	mov	r12,0

000004c0 e3590000   1208 	cmp	r9,0

000004c4 a1a00009   1209 	movge	r0,r9


                                                                      Page 21
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
000004c8 b3a00000   1210 	movlt	r0,0

000004cc e1b041a0   1211 	movs	r4,r0 lsr 3

000004d0 0a00004c   1212 	beq	.L1214

000004d4 e59f78c8*  1213 	ldr	r7,.L2602

000004d8 e28d603c   1214 	add	r6,sp,60

000004dc e3a0e001   1215 	mov	lr,1

                    1216 .L1215:

000004e0 e5d72000   1217 	ldrb	r2,[r7]

000004e4 e3520000   1218 	cmp	r2,0

000004e8 120c3007   1219 	andne	r3,r12,7

000004ec 17d611cc   1220 	ldrneb	r1,[r6,r12 asr 3]

000004f0 12633007   1221 	rsbne	r3,r3,7

000004f4 1181131e   1222 	orrne	r1,r1,lr lsl r3

000004f8 e5d72001   1223 	ldrb	r2,[r7,1]

000004fc 17c611cc   1224 	strneb	r1,[r6,r12 asr 3]

00000500 e3520000   1225 	cmp	r2,0

00000504 0a000005   1226 	beq	.L1223

00000508 e28c2001   1227 	add	r2,r12,1

0000050c e2023007   1228 	and	r3,r2,7

00000510 e7d611c2   1229 	ldrb	r1,[r6,r2 asr 3]

00000514 e2633007   1230 	rsb	r3,r3,7

00000518 e181131e   1231 	orr	r1,r1,lr lsl r3

0000051c e7c611c2   1232 	strb	r1,[r6,r2 asr 3]

                    1233 .L1223:

00000520 e5d72002   1234 	ldrb	r2,[r7,2]

00000524 e3520000   1235 	cmp	r2,0

00000528 0a000005   1236 	beq	.L1227

0000052c e28c2002   1237 	add	r2,r12,2

00000530 e2023007   1238 	and	r3,r2,7

00000534 e7d611c2   1239 	ldrb	r1,[r6,r2 asr 3]

00000538 e2633007   1240 	rsb	r3,r3,7

0000053c e181131e   1241 	orr	r1,r1,lr lsl r3

00000540 e7c611c2   1242 	strb	r1,[r6,r2 asr 3]

                    1243 .L1227:

00000544 e5d72003   1244 	ldrb	r2,[r7,3]

00000548 e3520000   1245 	cmp	r2,0

0000054c 0a000005   1246 	beq	.L1231

00000550 e28c2003   1247 	add	r2,r12,3

00000554 e2023007   1248 	and	r3,r2,7

00000558 e7d611c2   1249 	ldrb	r1,[r6,r2 asr 3]

0000055c e2633007   1250 	rsb	r3,r3,7

00000560 e181131e   1251 	orr	r1,r1,lr lsl r3

00000564 e7c611c2   1252 	strb	r1,[r6,r2 asr 3]

                    1253 .L1231:

00000568 e5d72004   1254 	ldrb	r2,[r7,4]

0000056c e3520000   1255 	cmp	r2,0

00000570 0a000005   1256 	beq	.L1235

00000574 e28c2004   1257 	add	r2,r12,4

00000578 e2023007   1258 	and	r3,r2,7

0000057c e7d611c2   1259 	ldrb	r1,[r6,r2 asr 3]

00000580 e2633007   1260 	rsb	r3,r3,7

00000584 e181131e   1261 	orr	r1,r1,lr lsl r3

00000588 e7c611c2   1262 	strb	r1,[r6,r2 asr 3]

                    1263 .L1235:

0000058c e5d72005   1264 	ldrb	r2,[r7,5]

00000590 e3520000   1265 	cmp	r2,0

00000594 0a000005   1266 	beq	.L1239

00000598 e28c2005   1267 	add	r2,r12,5

0000059c e2023007   1268 	and	r3,r2,7

000005a0 e7d611c2   1269 	ldrb	r1,[r6,r2 asr 3]

000005a4 e2633007   1270 	rsb	r3,r3,7


                                                                      Page 22
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
000005a8 e181131e   1271 	orr	r1,r1,lr lsl r3

000005ac e7c611c2   1272 	strb	r1,[r6,r2 asr 3]

                    1273 .L1239:

000005b0 e5d72006   1274 	ldrb	r2,[r7,6]

000005b4 e3520000   1275 	cmp	r2,0

000005b8 0a000005   1276 	beq	.L1243

000005bc e28c2006   1277 	add	r2,r12,6

000005c0 e2023007   1278 	and	r3,r2,7

000005c4 e7d611c2   1279 	ldrb	r1,[r6,r2 asr 3]

000005c8 e2633007   1280 	rsb	r3,r3,7

000005cc e181131e   1281 	orr	r1,r1,lr lsl r3

000005d0 e7c611c2   1282 	strb	r1,[r6,r2 asr 3]

                    1283 .L1243:

000005d4 e5d72007   1284 	ldrb	r2,[r7,7]

000005d8 e3520000   1285 	cmp	r2,0

000005dc 0a000005   1286 	beq	.L1246

000005e0 e28c2007   1287 	add	r2,r12,7

000005e4 e2023007   1288 	and	r3,r2,7

000005e8 e7d611c2   1289 	ldrb	r1,[r6,r2 asr 3]

000005ec e2633007   1290 	rsb	r3,r3,7

000005f0 e181131e   1291 	orr	r1,r1,lr lsl r3

000005f4 e7c611c2   1292 	strb	r1,[r6,r2 asr 3]

                    1293 .L1246:

000005f8 e2877008   1294 	add	r7,r7,8

000005fc e28cc008   1295 	add	r12,r12,8

00000600 e2544001   1296 	subs	r4,r4,1

00000604 1affffb5   1297 	bne	.L1215

                    1298 .L1214:

00000608 e2104007   1299 	ands	r4,r0,7

0000060c 0a00000d   1300 	beq	.L862

00000610 e59f078c*  1301 	ldr	r0,.L2602

00000614 e28d703c   1302 	add	r7,sp,60

00000618 e08c6000   1303 	add	r6,r12,r0

0000061c e3a0e001   1304 	mov	lr,1

                    1305 .L1249:

00000620 e4d62001   1306 	ldrb	r2,[r6],1

00000624 e3520000   1307 	cmp	r2,0

00000628 120c3007   1308 	andne	r3,r12,7

0000062c 17d711cc   1309 	ldrneb	r1,[r7,r12 asr 3]

00000630 12633007   1310 	rsbne	r3,r3,7

00000634 1181131e   1311 	orrne	r1,r1,lr lsl r3

00000638 17c711cc   1312 	strneb	r1,[r7,r12 asr 3]

0000063c e28cc001   1313 	add	r12,r12,1

00000640 e2544001   1314 	subs	r4,r4,1

00000644 1afffff5   1315 	bne	.L1249

                    1316 .L862:

                    1317 ;378: 		}


                    1318 ;379: 	}		


                    1319 ;380: 


                    1320 ;381: 	outBufPos = BerEncoder_encodeBitString(IEC61850_BER_BIT_STRING,


                    1321 

00000648 e1a03005   1322 	mov	r3,r5

0000064c e28d203c   1323 	add	r2,sp,60

00000650 e59d0010   1324 	ldr	r0,[sp,16]

00000654 e1a01009   1325 	mov	r1,r9

00000658 e58d0000   1326 	str	r0,[sp]

0000065c e3a00084   1327 	mov	r0,132

00000660 eb000000*  1328 	bl	BerEncoder_encodeBitString

                    1329 ;382:         dataSetObjectCount, inclusionBitString, outBuf, outBufPos);


                    1330 ;383: 	return outBufPos;


                    1331 


                                                                      Page 23
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
00000664 e2504000   1332 	subs	r4,r0,0

                    1333 ;452: 	RET_IF_NOT(outBufPos > 0, "Error writing Inclusion Bit string");


                    1334 

00000668 da000083   1335 	ble	.L873

                    1336 ;453: 	


                    1337 ;454: 	//Write values


                    1338 ;455: 	memcpy(outBuf + outBufPos, encodedValues, encodedValuesSize);


                    1339 

0000066c e59d2014   1340 	ldr	r2,[sp,20]

00000670 e59f1728*  1341 	ldr	r1,.L2601

00000674 e0840005   1342 	add	r0,r4,r5

00000678 eb000000*  1343 	bl	memcpy

                    1344 ;456: 	outBufPos += encodedValuesSize;


                    1345 

0000067c e59d0014   1346 	ldr	r0,[sp,20]

00000680 e0844000   1347 	add	r4,r4,r0

                    1348 ;457: 


                    1349 ;458: 	if (optFlds & OPTFLDS_REASON_FOR_INCLUSION)


                    1350 

00000684 e1dd00ba   1351 	ldrh	r0,[sp,10]

00000688 e3100040   1352 	tst	r0,64

                    1353 ;462: 	}


                    1354 ;463: 


                    1355 ;464: 	return outBufPos;


                    1356 

0000068c 01a00004   1357 	moveq	r0,r4

00000690 0a00007a   1358 	beq	.L796

                    1359 ;459: 	{


                    1360 

                    1361 ;460: 		outBufPos = writeReasonForInclusion(pReporter, outBuf, outBufPos);


                    1362 

00000694 e5981038   1363 	ldr	r1,[r8,56]

00000698 e1a00004   1364 	mov	r0,r4

                    1365 ;387: {	


                    1366 

                    1367 ;388: 	int objIdx;


                    1368 ;389: 	int dataSetObjectCount = pRCB->dataSet->itemCount;


                    1369 

0000069c e5911004   1370 	ldr	r1,[r1,4]

                    1371 ;390: 


                    1372 ;391: 	for (objIdx = 0; objIdx < dataSetObjectCount; ++objIdx)


                    1373 

000006a0 e3a06000   1374 	mov	r6,0

000006a4 e3510000   1375 	cmp	r1,0

000006a8 a1a08001   1376 	movge	r8,r1

000006ac b3a08000   1377 	movlt	r8,0

000006b0 e1b071a8   1378 	movs	r7,r8 lsr 3

000006b4 0a00005a   1379 	beq	.L1255

000006b8 e59f46e4*  1380 	ldr	r4,.L2602

                    1381 .L1256:

000006bc e7d42006   1382 	ldrb	r2,[r4,r6]

000006c0 e3520000   1383 	cmp	r2,0

000006c4 0a000006   1384 	beq	.L1261

000006c8 e1a03005   1385 	mov	r3,r5

000006cc e3a01006   1386 	mov	r1,6

000006d0 e58d0000   1387 	str	r0,[sp]

000006d4 e3a00084   1388 	mov	r0,132

000006d8 eb000000*  1389 	bl	BerEncoder_encodeUcharBitString

000006dc e3500000   1390 	cmp	r0,0

000006e0 da00005c   1391 	ble	.L1300

                    1392 .L1261:


                                                                      Page 24
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
000006e4 e2866001   1393 	add	r6,r6,1

000006e8 e7d42006   1394 	ldrb	r2,[r4,r6]

000006ec e3520000   1395 	cmp	r2,0

000006f0 0a000006   1396 	beq	.L1266

000006f4 e1a03005   1397 	mov	r3,r5

000006f8 e3a01006   1398 	mov	r1,6

000006fc e58d0000   1399 	str	r0,[sp]

00000700 e3a00084   1400 	mov	r0,132

00000704 eb000000*  1401 	bl	BerEncoder_encodeUcharBitString

00000708 e3500000   1402 	cmp	r0,0

0000070c da000051   1403 	ble	.L1300

                    1404 .L1266:

00000710 e2866001   1405 	add	r6,r6,1

00000714 e7d42006   1406 	ldrb	r2,[r4,r6]

00000718 e3520000   1407 	cmp	r2,0

0000071c 0a000006   1408 	beq	.L1271

00000720 e1a03005   1409 	mov	r3,r5

00000724 e3a01006   1410 	mov	r1,6

00000728 e58d0000   1411 	str	r0,[sp]

0000072c e3a00084   1412 	mov	r0,132

00000730 eb000000*  1413 	bl	BerEncoder_encodeUcharBitString

00000734 e3500000   1414 	cmp	r0,0

00000738 da000046   1415 	ble	.L1300

                    1416 .L1271:

0000073c e2866001   1417 	add	r6,r6,1

00000740 e7d42006   1418 	ldrb	r2,[r4,r6]

00000744 e3520000   1419 	cmp	r2,0

00000748 0a000006   1420 	beq	.L1276

0000074c e1a03005   1421 	mov	r3,r5

00000750 e3a01006   1422 	mov	r1,6

00000754 e58d0000   1423 	str	r0,[sp]

00000758 e3a00084   1424 	mov	r0,132

0000075c eb000000*  1425 	bl	BerEncoder_encodeUcharBitString

00000760 e3500000   1426 	cmp	r0,0

00000764 da00003b   1427 	ble	.L1300

                    1428 .L1276:

00000768 e2866001   1429 	add	r6,r6,1

0000076c e7d42006   1430 	ldrb	r2,[r4,r6]

00000770 e3520000   1431 	cmp	r2,0

00000774 0a000006   1432 	beq	.L1281

00000778 e1a03005   1433 	mov	r3,r5

0000077c e3a01006   1434 	mov	r1,6

00000780 e58d0000   1435 	str	r0,[sp]

00000784 e3a00084   1436 	mov	r0,132

00000788 eb000000*  1437 	bl	BerEncoder_encodeUcharBitString

0000078c e3500000   1438 	cmp	r0,0

00000790 da000030   1439 	ble	.L1300

                    1440 .L1281:

00000794 e2866001   1441 	add	r6,r6,1

00000798 e7d42006   1442 	ldrb	r2,[r4,r6]

0000079c e3520000   1443 	cmp	r2,0

000007a0 0a000006   1444 	beq	.L1286

000007a4 e1a03005   1445 	mov	r3,r5

000007a8 e3a01006   1446 	mov	r1,6

000007ac e58d0000   1447 	str	r0,[sp]

000007b0 e3a00084   1448 	mov	r0,132

000007b4 eb000000*  1449 	bl	BerEncoder_encodeUcharBitString

000007b8 e3500000   1450 	cmp	r0,0

000007bc da000025   1451 	ble	.L1300

                    1452 .L1286:

000007c0 e2866001   1453 	add	r6,r6,1


                                                                      Page 25
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
000007c4 e7d42006   1454 	ldrb	r2,[r4,r6]

000007c8 e3520000   1455 	cmp	r2,0

000007cc 0a000006   1456 	beq	.L1291

000007d0 e1a03005   1457 	mov	r3,r5

000007d4 e3a01006   1458 	mov	r1,6

000007d8 e58d0000   1459 	str	r0,[sp]

000007dc e3a00084   1460 	mov	r0,132

000007e0 eb000000*  1461 	bl	BerEncoder_encodeUcharBitString

000007e4 e3500000   1462 	cmp	r0,0

000007e8 da00001a   1463 	ble	.L1300

                    1464 .L1291:

000007ec e2866001   1465 	add	r6,r6,1

000007f0 e7d42006   1466 	ldrb	r2,[r4,r6]

000007f4 e3520000   1467 	cmp	r2,0

000007f8 0a000006   1468 	beq	.L1295

000007fc e1a03005   1469 	mov	r3,r5

00000800 e3a01006   1470 	mov	r1,6

00000804 e58d0000   1471 	str	r0,[sp]

00000808 e3a00084   1472 	mov	r0,132

0000080c eb000000*  1473 	bl	BerEncoder_encodeUcharBitString

00000810 e3500000   1474 	cmp	r0,0

00000814 da00000f   1475 	ble	.L1300

                    1476 .L1295:

00000818 e2866001   1477 	add	r6,r6,1

0000081c e2577001   1478 	subs	r7,r7,1

00000820 1affffa5   1479 	bne	.L1256

                    1480 .L1255:

00000824 e2187007   1481 	ands	r7,r8,7

00000828 0a000010   1482 	beq	.L871

0000082c e59f4570*  1483 	ldr	r4,.L2602

                    1484 .L1298:

00000830 e7d42006   1485 	ldrb	r2,[r4,r6]

00000834 e3520000   1486 	cmp	r2,0

00000838 0a000009   1487 	beq	.L1302

0000083c e1a03005   1488 	mov	r3,r5

00000840 e3a01006   1489 	mov	r1,6

00000844 e58d0000   1490 	str	r0,[sp]

00000848 e3a00084   1491 	mov	r0,132

0000084c eb000000*  1492 	bl	BerEncoder_encodeUcharBitString

00000850 e3500000   1493 	cmp	r0,0

00000854 ca000002   1494 	bgt	.L1302

                    1495 .L1300:

00000858 e3b00000   1496 	movs	r0,0

                    1497 ;461: 		RET_IF_NOT(outBufPos > 0, "Error writing Reason-For-Inclusion");


                    1498 

                    1499 

0000085c 43a00000   1500 	movmi	r0,0

00000860 ea000006   1501 	b	.L796

                    1502 .L1302:

00000864 e2866001   1503 	add	r6,r6,1

00000868 e2577001   1504 	subs	r7,r7,1

0000086c 1affffef   1505 	bne	.L1298

                    1506 .L871:

                    1507 ;399: 		}


                    1508 ;400: 	}


                    1509 ;401: 


                    1510 ;402: 	return outBufPos;


                    1511 

                    1512 ;461: 		RET_IF_NOT(outBufPos > 0, "Error writing Reason-For-Inclusion");


                    1513 

                    1514 


                                                                      Page 26
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
00000870 e3500000   1515 	cmp	r0,0

00000874 b3a00000   1516 	movlt	r0,0

00000878 ea000000   1517 	b	.L796

                    1518 .L873:

0000087c e3a00000   1519 	mov	r0,0

                    1520 .L796:

00000880 e28dd084   1521 	add	sp,sp,132

00000884 e8bd4ff0   1522 	ldmfd	[sp]!,{r4-fp,lr}

00000888 e12fff1e*  1523 	ret	

                    1524 	.endf	writeReport

                    1525 	.align	4

                    1526 ;reportValBufView	[sp,120]	local

                    1527 ;rptProcContext	[sp,100]	local

                    1528 ;dataSet	r1	local

                    1529 ;dsItem	r4	local

                    1530 ;changed	r5	local

                    1531 ;trgOps	[sp,20]	local

                    1532 ;iedObj	r6	local

                    1533 ;dsItem	r4	local

                    1534 ;reason	r5	local

                    1535 ;iedObj	r0	local

                    1536 ;encodedValuesSize	[sp,20]	local

                    1537 ;outBufPos	r4	local

                    1538 ;pRCB	r9	local

                    1539 ;optFlds	[sp,10]	local

                    1540 ;outBufPos	r6	local

                    1541 ;timestamp	[sp,36]	local

                    1542 ;outBuf	r4	local

                    1543 ;mmsTime	[sp,52]	local

                    1544 ;timestamp	r0	local

                    1545 ;daysDiff	[sp,8]	local

                    1546 ;msSinceMidnight	[sp,4]	local

                    1547 ;secCount	[sp,28]	local

                    1548 ;overflow	r0	local

                    1549 ;outBufPos	[sp,16]	local

                    1550 ;inclusionBitString	[sp,60]	local

                    1551 ;inclusionFlagNum	r12	local

                    1552 ;dataSetObjectCount	r9	local

                    1553 ;byteNum	r2	local

                    1554 ;bitNum	r3	local

                    1555 ;bit	r3	local

                    1556 ;outBufPos	r0	local

                    1557 ;objIdx	r6	local

                    1558 ;dataSetObjectCount	r1	local

                    1559 ;reason	r2	local

                    1560 

                    1561 ;pReporter	r8	param

                    1562 ;mode	r4	param

                    1563 

                    1564 	.section ".bss","awb"

                    1565 .L2334:

00000000 00000000   1566 reportInclusionReasons:	.space	320

00000004 00000000 
00000008 00000000 
0000000c 00000000 
00000010 00000000 
00000014 00000000 
00000018 00000000 
0000001c 00000000 
00000020 00000000 
00000024 00000000 

                                                                      Page 27
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
00000028 00000000 
0000002c 00000000 
00000030 00000000 
00000034 00000000 
00000038 00000000 
0000003c 00000000 
00000040 00000000 
00000044 00000000 
00000048 00000000 
0000004c 00000000 
00000050 00000000 
00000054 00000000 
00000058 00000000 
0000005c 00000000 
00000060 00000000 
00000064 00000000 
00000068 00000000 
0000006c 00000000 
00000070 00000000 
00000074 00000000 
00000078 00000000 
0000007c 00000000 
00000080 00000000 
00000084 00000000 
00000088 00000000 
0000008c 00000000 
00000090 00000000 
00000094 00000000 
00000098 00000000 
0000009c 00000000 
000000a0 00000000 
000000a4 00000000 
000000a8 00000000 
000000ac 00000000 
000000b0 00000000 
000000b4 00000000 
000000b8 00000000 
000000bc 00000000 
000000c0 00000000 
000000c4 00000000 
000000c8 00000000 
000000cc 00000000 
000000d0 00000000 
000000d4 00000000 
000000d8 00000000 
000000dc 00000000 
000000e0 00000000 
000000e4 00000000 
000000e8 00000000 
000000ec 00000000 
000000f0 00000000 
000000f4 00000000 
000000f8 00000000 
000000fc 00000000 
00000140 00000000   1567 reportValuesBuf:	.space	8192

00000144 00000000 
00000148 00000000 
0000014c 00000000 
00000150 00000000 
00000154 00000000 
00000158 00000000 

                                                                      Page 28
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
0000015c 00000000 
00000160 00000000 
00000164 00000000 
00000168 00000000 
0000016c 00000000 
00000170 00000000 
00000174 00000000 
00000178 00000000 
0000017c 00000000 
00000180 00000000 
00000184 00000000 
00000188 00000000 
0000018c 00000000 
00000190 00000000 
00000194 00000000 
00000198 00000000 
0000019c 00000000 
000001a0 00000000 
000001a4 00000000 
000001a8 00000000 
000001ac 00000000 
000001b0 00000000 
000001b4 00000000 
000001b8 00000000 
000001bc 00000000 
000001c0 00000000 
000001c4 00000000 
000001c8 00000000 
000001cc 00000000 
000001d0 00000000 
000001d4 00000000 
000001d8 00000000 
000001dc 00000000 
000001e0 00000000 
000001e4 00000000 
000001e8 00000000 
000001ec 00000000 
000001f0 00000000 
000001f4 00000000 
000001f8 00000000 
000001fc 00000000 
00000200 00000000 
00000204 00000000 
00000208 00000000 
0000020c 00000000 
00000210 00000000 
00000214 00000000 
00000218 00000000 
0000021c 00000000 
00000220 00000000 
00000224 00000000 
00000228 00000000 
0000022c 00000000 
00000230 00000000 
00000234 00000000 
00000238 00000000 
0000023c 00000000 
00002140 00000000   1568 reportAccessResultsBuf:	.space	8192

00002144 00000000 
00002148 00000000 
0000214c 00000000 

                                                                      Page 29
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
00002150 00000000 
00002154 00000000 
00002158 00000000 
0000215c 00000000 
00002160 00000000 
00002164 00000000 
00002168 00000000 
0000216c 00000000 
00002170 00000000 
00002174 00000000 
00002178 00000000 
0000217c 00000000 
00002180 00000000 
00002184 00000000 
00002188 00000000 
0000218c 00000000 
00002190 00000000 
00002194 00000000 
00002198 00000000 
0000219c 00000000 
000021a0 00000000 
000021a4 00000000 
000021a8 00000000 
000021ac 00000000 
000021b0 00000000 
000021b4 00000000 
000021b8 00000000 
000021bc 00000000 
000021c0 00000000 
000021c4 00000000 
000021c8 00000000 
000021cc 00000000 
000021d0 00000000 
000021d4 00000000 
000021d8 00000000 
000021dc 00000000 
000021e0 00000000 
000021e4 00000000 
000021e8 00000000 
000021ec 00000000 
000021f0 00000000 
000021f4 00000000 
000021f8 00000000 
000021fc 00000000 
00002200 00000000 
00002204 00000000 
00002208 00000000 
0000220c 00000000 
00002210 00000000 
00002214 00000000 
00002218 00000000 
0000221c 00000000 
00002220 00000000 
00002224 00000000 
00002228 00000000 
0000222c 00000000 
00002230 00000000 
00002234 00000000 
00002238 00000000 
0000223c 00000000 
00004140 00000000   1569 reportMmsBuf:	.space	8192


                                                                      Page 30
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
00004144 00000000 
00004148 00000000 
0000414c 00000000 
00004150 00000000 
00004154 00000000 
00004158 00000000 
0000415c 00000000 
00004160 00000000 
00004164 00000000 
00004168 00000000 
0000416c 00000000 
00004170 00000000 
00004174 00000000 
00004178 00000000 
0000417c 00000000 
00004180 00000000 
00004184 00000000 
00004188 00000000 
0000418c 00000000 
00004190 00000000 
00004194 00000000 
00004198 00000000 
0000419c 00000000 
000041a0 00000000 
000041a4 00000000 
000041a8 00000000 
000041ac 00000000 
000041b0 00000000 
000041b4 00000000 
000041b8 00000000 
000041bc 00000000 
000041c0 00000000 
000041c4 00000000 
000041c8 00000000 
000041cc 00000000 
000041d0 00000000 
000041d4 00000000 
000041d8 00000000 
000041dc 00000000 
000041e0 00000000 
000041e4 00000000 
000041e8 00000000 
000041ec 00000000 
000041f0 00000000 
000041f4 00000000 
000041f8 00000000 
000041fc 00000000 
00004200 00000000 
00004204 00000000 
00004208 00000000 
0000420c 00000000 
00004210 00000000 
00004214 00000000 
00004218 00000000 
0000421c 00000000 
00004220 00000000 
00004224 00000000 
00004228 00000000 
0000422c 00000000 
00004230 00000000 
00004234 00000000 

                                                                      Page 31
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
00004238 00000000 
0000423c 00000000 
00006140 00000000   1570 reportPresentationBuf:	.space	8192

00006144 00000000 
00006148 00000000 
0000614c 00000000 
00006150 00000000 
00006154 00000000 
00006158 00000000 
0000615c 00000000 
00006160 00000000 
00006164 00000000 
00006168 00000000 
0000616c 00000000 
00006170 00000000 
00006174 00000000 
00006178 00000000 
0000617c 00000000 
00006180 00000000 
00006184 00000000 
00006188 00000000 
0000618c 00000000 
00006190 00000000 
00006194 00000000 
00006198 00000000 
0000619c 00000000 
000061a0 00000000 
000061a4 00000000 
000061a8 00000000 
000061ac 00000000 
000061b0 00000000 
000061b4 00000000 
000061b8 00000000 
000061bc 00000000 
000061c0 00000000 
000061c4 00000000 
000061c8 00000000 
000061cc 00000000 
000061d0 00000000 
000061d4 00000000 
000061d8 00000000 
000061dc 00000000 
000061e0 00000000 
000061e4 00000000 
000061e8 00000000 
000061ec 00000000 
000061f0 00000000 
000061f4 00000000 
000061f8 00000000 
000061fc 00000000 
00006200 00000000 
00006204 00000000 
00006208 00000000 
0000620c 00000000 
00006210 00000000 
00006214 00000000 
00006218 00000000 
0000621c 00000000 
00006220 00000000 
00006224 00000000 
00006228 00000000 

                                                                      Page 32
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
0000622c 00000000 
00006230 00000000 
00006234 00000000 
00006238 00000000 
0000623c 00000000 
                    1571 	.data

                    1572 	.text

                    1573 

                    1574 

                    1575 	.align	4

                    1576 	.align	4

                    1577 sendReport:

0000088c e92d4ce2   1578 	stmfd	[sp]!,{r1,r5-r7,r10-fp,lr}

00000890 e1b06000   1579 	movs	r6,r0

00000894 e1a05002   1580 	mov	r5,r2

00000898 e1a07003   1581 	mov	r7,r3

0000089c e59fb510*  1582 	ldr	fp,.L2656

000008a0 0a000030   1583 	beq	.L2606

000008a4 e1a00005   1584 	mov	r0,r5

000008a8 eb000000*  1585 	bl	BerEncoder_determineLengthSize

000008ac e0850000   1586 	add	r0,r5,r0

000008b0 e280a008   1587 	add	r10,r0,8

000008b4 e1a0000a   1588 	mov	r0,r10

000008b8 eb000000*  1589 	bl	BerEncoder_determineLengthSize

000008bc e1a0200b   1590 	mov	r2,fp

000008c0 e3a03000   1591 	mov	r3,0

000008c4 e08a0000   1592 	add	r0,r10,r0

000008c8 e2801001   1593 	add	r1,r0,1

000008cc e3a000a3   1594 	mov	r0,163

000008d0 eb000000*  1595 	bl	BerEncoder_encodeTL

000008d4 e1a0200b   1596 	mov	r2,fp

000008d8 e1a0100a   1597 	mov	r1,r10

000008dc e1a03000   1598 	mov	r3,r0

000008e0 e3a000a0   1599 	mov	r0,160

000008e4 eb000000*  1600 	bl	BerEncoder_encodeTL

000008e8 e1a0a000   1601 	mov	r10,r0

000008ec e59f14c4*  1602 	ldr	r1,.L2657

000008f0 e08a000b   1603 	add	r0,r10,fp

000008f4 e3a02007   1604 	mov	r2,7

000008f8 eb000000*  1605 	bl	memcpy

000008fc e28a3007   1606 	add	r3,r10,7

00000900 e1a0200b   1607 	mov	r2,fp

00000904 e1a01005   1608 	mov	r1,r5

00000908 e3a000a0   1609 	mov	r0,160

0000090c eb000000*  1610 	bl	BerEncoder_encodeTL

00000910 e1a02005   1611 	mov	r2,r5

00000914 e1a0a000   1612 	mov	r10,r0

00000918 e59d1000   1613 	ldr	r1,[sp]

0000091c e08a000b   1614 	add	r0,r10,fp

00000920 eb000000*  1615 	bl	memcpy

00000924 e08a3005   1616 	add	r3,r10,r5

00000928 e59f548c*  1617 	ldr	r5,.L2658

0000092c e1a0200b   1618 	mov	r2,fp

00000930 e1a01005   1619 	mov	r1,r5

00000934 e2860edb   1620 	add	r0,r6,0x0db0

00000938 e2800bf0   1621 	add	r0,r0,15<<14

0000093c eb000000*  1622 	bl	IsoPresentation_createUserData

00000940 e1a02005   1623 	mov	r2,r5

00000944 e1a03000   1624 	mov	r3,r0

00000948 e2870008   1625 	add	r0,r7,8

0000094c e3a01c60   1626 	mov	r1,3<<13


                                                                      Page 33
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
00000950 eb000000*  1627 	bl	isoSession_createDataSpdu

00000954 e1a01007   1628 	mov	r1,r7

00000958 e5870004   1629 	str	r0,[r7,4]

0000095c e2860bf2   1630 	add	r0,r6,242<<10

00000960 e280006c   1631 	add	r0,r0,108

00000964 eb000000*  1632 	bl	OutQueue_insert

                    1633 .L2606:

00000968 e8bd4ce2   1634 	ldmfd	[sp]!,{r1,r5-r7,r10-fp,lr}

0000096c e12fff1e*  1635 	ret	

                    1636 	.endf	sendReport

                    1637 	.align	4

                    1638 ;bufPos	r10	local

                    1639 ;reportVarLen	r10	local

                    1640 

                    1641 ;isoConn	r6	param

                    1642 ;mmsReport	[sp]	param

                    1643 ;byteCount	r5	param

                    1644 ;sessionOutBuf	r7	param

                    1645 

                    1646 	.data

                    1647 	.text

                    1648 

                    1649 

                    1650 	.align	4

                    1651 	.align	4

                    1652 writeReportToBuffer:

00000970 e92d4000   1653 	stmfd	[sp]!,{lr}

00000974 e2800044   1654 	add	r0,r0,68

00000978 eb000000*  1655 	bl	ReportQueue_write

0000097c e8bd4000   1656 	ldmfd	[sp]!,{lr}

00000980 e12fff1e*  1657 	ret	

                    1658 	.endf	writeReportToBuffer

                    1659 	.align	4

                    1660 

                    1661 ;pRCB	r0	param

                    1662 ;pData	none	param

                    1663 ;dataLen	none	param

                    1664 

                    1665 	.section ".bss","awb"

                    1666 .L2686:

                    1667 	.data

                    1668 	.text

                    1669 

                    1670 

                    1671 	.align	4

                    1672 	.align	4

                    1673 allocateBufAndSendReport:

00000984 e92d4070   1674 	stmfd	[sp]!,{r4-r6,lr}

00000988 e1a04001   1675 	mov	r4,r1

0000098c e5906040   1676 	ldr	r6,[r0,64]

00000990 e1a05002   1677 	mov	r5,r2

00000994 e3560000   1678 	cmp	r6,0

00000998 0a000008   1679 	beq	.L2693

0000099c e2860e80   1680 	add	r0,r6,1<<11

000009a0 e2800018   1681 	add	r0,r0,24

000009a4 e3a01c60   1682 	mov	r1,3<<13

000009a8 eb000000*  1683 	bl	allocSessionOutBuffer

000009ac e1b03000   1684 	movs	r3,r0

000009b0 11a02005   1685 	movne	r2,r5

000009b4 11a01004   1686 	movne	r1,r4

000009b8 11a00006   1687 	movne	r0,r6


                                                                      Page 34
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
000009bc 1bffffb2*  1688 	blne	sendReport

                    1689 .L2693:

000009c0 e8bd4070   1690 	ldmfd	[sp]!,{r4-r6,lr}

000009c4 e12fff1e*  1691 	ret	

                    1692 	.endf	allocateBufAndSendReport

                    1693 	.align	4

                    1694 ;outBuf	r3	local

                    1695 ;pConn	r6	local

                    1696 

                    1697 ;pRCB	r0	param

                    1698 ;reportData	r4	param

                    1699 ;reportDataSize	r5	param

                    1700 

                    1701 	.section ".bss","awb"

                    1702 .L2735:

                    1703 	.data

                    1704 	.text

                    1705 

                    1706 

                    1707 	.align	4

                    1708 	.align	4

                    1709 integrityTimerProc:

000009c8 e92d4ff0   1710 	stmfd	[sp]!,{r4-fp,lr}

000009cc e24dd010   1711 	sub	sp,sp,16

000009d0 e3a00000   1712 	mov	r0,0

000009d4 e59f13b8*  1713 	ldr	r1,.L649

000009d8 e58d0008   1714 	str	r0,[sp,8]

000009dc e5910000   1715 	ldr	r0,[r1]

000009e0 e3500000   1716 	cmp	r0,0

000009e4 b3a00000   1717 	movlt	r0,0

000009e8 e58d000c   1718 	str	r0,[sp,12]

000009ec e1b0c1a0   1719 	movs	r12,r0 lsr 3

000009f0 0a0000c0   1720 	beq	.L2790

000009f4 e59f03c4*  1721 	ldr	r0,.L3446

000009f8 e59f4398*  1722 	ldr	r4,.L650

000009fc e3a02001   1723 	mov	r2,1

00000a00 e0849000   1724 	add	r9,r4,r0

00000a04 e2840f9c   1725 	add	r0,r4,0x0270

00000a08 e2805a87   1726 	add	r5,r0,135<<12

00000a0c e59f03b0*  1727 	ldr	r0,.L3447

00000a10 e3a03000   1728 	mov	r3,0

00000a14 e0841000   1729 	add	r1,r4,r0

00000a18 e2840f68   1730 	add	r0,r4,0x01a0

00000a1c e2806a5a   1731 	add	r6,r0,90<<12

00000a20 e59f03a0*  1732 	ldr	r0,.L3448

00000a24 e1a0818c   1733 	mov	r8,r12 lsl 3

00000a28 e0847000   1734 	add	r7,r4,r0

00000a2c e2840bb4   1735 	add	r0,r4,45<<12

00000a30 e280a0d0   1736 	add	r10,r0,208

00000a34 e2840b5a   1737 	add	r0,r4,90<<10

00000a38 e280b068   1738 	add	fp,r0,104

00000a3c e98d0102   1739 	stmfa	[sp],{r1,r8}

                    1740 .L2791:

00000a40 e1a00004   1741 	mov	r0,r4

00000a44 e1a01000   1742 	mov	r1,r0

00000a48 e5d1e01a   1743 	ldrb	lr,[r1,26]

00000a4c e31e0002   1744 	tst	lr,2

00000a50 1591e01c   1745 	ldrne	lr,[r1,28]

00000a54 135e0000   1746 	cmpne	lr,0

00000a58 0a00000c   1747 	beq	.L2798

00000a5c e590102c   1748 	ldr	r1,[r0,44]


                                                                      Page 35
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
00000a60 e151000e   1749 	cmp	r1,lr

00000a64 2580302c   1750 	strhs	r3,[r0,44]

00000a68 25c02030   1751 	strhsb	r2,[r0,48]

00000a6c 2a000007   1752 	bhs	.L2798

00000a70 e2811001   1753 	add	r1,r1,1

00000a74 e580102c   1754 	str	r1,[r0,44]

00000a78 e1a0000b   1755 	mov	r0,fp

00000a7c e1a01000   1756 	mov	r1,r0

00000a80 e5d1e01a   1757 	ldrb	lr,[r1,26]

00000a84 e31e0002   1758 	tst	lr,2

00000a88 1a000005   1759 	bne	.L2799

00000a8c ea000014   1760 	b	.L2805

                    1761 .L2798:

00000a90 e1a0000b   1762 	mov	r0,fp

00000a94 e1a01000   1763 	mov	r1,r0

00000a98 e5d1e01a   1764 	ldrb	lr,[r1,26]

00000a9c e31e0002   1765 	tst	lr,2

00000aa0 0a00000f   1766 	beq	.L2805

                    1767 .L2799:

00000aa4 e591e01c   1768 	ldr	lr,[r1,28]

00000aa8 e35e0000   1769 	cmp	lr,0

00000aac 0a00000c   1770 	beq	.L2805

00000ab0 e590102c   1771 	ldr	r1,[r0,44]

00000ab4 e151000e   1772 	cmp	r1,lr

00000ab8 2580302c   1773 	strhs	r3,[r0,44]

00000abc 25c02030   1774 	strhsb	r2,[r0,48]

00000ac0 2a000007   1775 	bhs	.L2805

00000ac4 e2811001   1776 	add	r1,r1,1

00000ac8 e580102c   1777 	str	r1,[r0,44]

00000acc e1a0000a   1778 	mov	r0,r10

00000ad0 e1a01000   1779 	mov	r1,r0

00000ad4 e5d1e01a   1780 	ldrb	lr,[r1,26]

00000ad8 e31e0002   1781 	tst	lr,2

00000adc 1a000005   1782 	bne	.L2806

00000ae0 ea000014   1783 	b	.L2812

                    1784 .L2805:

00000ae4 e1a0000a   1785 	mov	r0,r10

00000ae8 e1a01000   1786 	mov	r1,r0

00000aec e5d1e01a   1787 	ldrb	lr,[r1,26]

00000af0 e31e0002   1788 	tst	lr,2

00000af4 0a00000f   1789 	beq	.L2812

                    1790 .L2806:

00000af8 e591e01c   1791 	ldr	lr,[r1,28]

00000afc e35e0000   1792 	cmp	lr,0

00000b00 0a00000c   1793 	beq	.L2812

00000b04 e590102c   1794 	ldr	r1,[r0,44]

00000b08 e151000e   1795 	cmp	r1,lr

00000b0c 2580302c   1796 	strhs	r3,[r0,44]

00000b10 25c02030   1797 	strhsb	r2,[r0,48]

00000b14 2a000007   1798 	bhs	.L2812

00000b18 e2811001   1799 	add	r1,r1,1

00000b1c e580102c   1800 	str	r1,[r0,44]

00000b20 e1a00007   1801 	mov	r0,r7

00000b24 e1a01000   1802 	mov	r1,r0

00000b28 e5d1e01a   1803 	ldrb	lr,[r1,26]

00000b2c e31e0002   1804 	tst	lr,2

00000b30 1a000005   1805 	bne	.L2813

00000b34 ea000014   1806 	b	.L2819

                    1807 .L2812:

00000b38 e1a00007   1808 	mov	r0,r7

00000b3c e1a01000   1809 	mov	r1,r0


                                                                      Page 36
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
00000b40 e5d1e01a   1810 	ldrb	lr,[r1,26]

00000b44 e31e0002   1811 	tst	lr,2

00000b48 0a00000f   1812 	beq	.L2819

                    1813 .L2813:

00000b4c e591e01c   1814 	ldr	lr,[r1,28]

00000b50 e35e0000   1815 	cmp	lr,0

00000b54 0a00000c   1816 	beq	.L2819

00000b58 e590102c   1817 	ldr	r1,[r0,44]

00000b5c e151000e   1818 	cmp	r1,lr

00000b60 2580302c   1819 	strhs	r3,[r0,44]

00000b64 25c02030   1820 	strhsb	r2,[r0,48]

00000b68 2a000007   1821 	bhs	.L2819

00000b6c e2811001   1822 	add	r1,r1,1

00000b70 e580102c   1823 	str	r1,[r0,44]

00000b74 e1a00006   1824 	mov	r0,r6

00000b78 e1a01000   1825 	mov	r1,r0

00000b7c e5d1e01a   1826 	ldrb	lr,[r1,26]

00000b80 e31e0002   1827 	tst	lr,2

00000b84 1a000005   1828 	bne	.L2820

00000b88 ea000014   1829 	b	.L2826

                    1830 .L2819:

00000b8c e1a00006   1831 	mov	r0,r6

00000b90 e1a01000   1832 	mov	r1,r0

00000b94 e5d1e01a   1833 	ldrb	lr,[r1,26]

00000b98 e31e0002   1834 	tst	lr,2

00000b9c 0a00000f   1835 	beq	.L2826

                    1836 .L2820:

00000ba0 e591e01c   1837 	ldr	lr,[r1,28]

00000ba4 e35e0000   1838 	cmp	lr,0

00000ba8 0a00000c   1839 	beq	.L2826

00000bac e590102c   1840 	ldr	r1,[r0,44]

00000bb0 e151000e   1841 	cmp	r1,lr

00000bb4 2580302c   1842 	strhs	r3,[r0,44]

00000bb8 25c02030   1843 	strhsb	r2,[r0,48]

00000bbc 2a000007   1844 	bhs	.L2826

00000bc0 e2811001   1845 	add	r1,r1,1

00000bc4 e580102c   1846 	str	r1,[r0,44]

00000bc8 e59d0004   1847 	ldr	r0,[sp,4]

00000bcc e1a01000   1848 	mov	r1,r0

00000bd0 e5d1e01a   1849 	ldrb	lr,[r1,26]

00000bd4 e31e0002   1850 	tst	lr,2

00000bd8 1a000005   1851 	bne	.L2827

00000bdc ea000014   1852 	b	.L2833

                    1853 .L2826:

00000be0 e59d0004   1854 	ldr	r0,[sp,4]

00000be4 e1a01000   1855 	mov	r1,r0

00000be8 e5d1e01a   1856 	ldrb	lr,[r1,26]

00000bec e31e0002   1857 	tst	lr,2

00000bf0 0a00000f   1858 	beq	.L2833

                    1859 .L2827:

00000bf4 e591e01c   1860 	ldr	lr,[r1,28]

00000bf8 e35e0000   1861 	cmp	lr,0

00000bfc 0a00000c   1862 	beq	.L2833

00000c00 e590102c   1863 	ldr	r1,[r0,44]

00000c04 e151000e   1864 	cmp	r1,lr

00000c08 2580302c   1865 	strhs	r3,[r0,44]

00000c0c 25c02030   1866 	strhsb	r2,[r0,48]

00000c10 2a000007   1867 	bhs	.L2833

00000c14 e2811001   1868 	add	r1,r1,1

00000c18 e580102c   1869 	str	r1,[r0,44]

00000c1c e1a00005   1870 	mov	r0,r5


                                                                      Page 37
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
00000c20 e1a01000   1871 	mov	r1,r0

00000c24 e5d1e01a   1872 	ldrb	lr,[r1,26]

00000c28 e31e0002   1873 	tst	lr,2

00000c2c 1a000005   1874 	bne	.L2834

00000c30 ea000014   1875 	b	.L2840

                    1876 .L2833:

00000c34 e1a00005   1877 	mov	r0,r5

00000c38 e1a01000   1878 	mov	r1,r0

00000c3c e5d1e01a   1879 	ldrb	lr,[r1,26]

00000c40 e31e0002   1880 	tst	lr,2

00000c44 0a00000f   1881 	beq	.L2840

                    1882 .L2834:

00000c48 e591e01c   1883 	ldr	lr,[r1,28]

00000c4c e35e0000   1884 	cmp	lr,0

00000c50 0a00000c   1885 	beq	.L2840

00000c54 e590102c   1886 	ldr	r1,[r0,44]

00000c58 e151000e   1887 	cmp	r1,lr

00000c5c 2580302c   1888 	strhs	r3,[r0,44]

00000c60 25c02030   1889 	strhsb	r2,[r0,48]

00000c64 2a000007   1890 	bhs	.L2840

00000c68 e2811001   1891 	add	r1,r1,1

00000c6c e580102c   1892 	str	r1,[r0,44]

00000c70 e1a00009   1893 	mov	r0,r9

00000c74 e1a01000   1894 	mov	r1,r0

00000c78 e5d1e01a   1895 	ldrb	lr,[r1,26]

00000c7c e31e0002   1896 	tst	lr,2

00000c80 1a000005   1897 	bne	.L2841

00000c84 ea00000d   1898 	b	.L2846

                    1899 .L2840:

00000c88 e1a00009   1900 	mov	r0,r9

00000c8c e1a01000   1901 	mov	r1,r0

00000c90 e5d1e01a   1902 	ldrb	lr,[r1,26]

00000c94 e31e0002   1903 	tst	lr,2

00000c98 0a000008   1904 	beq	.L2846

                    1905 .L2841:

00000c9c e591e01c   1906 	ldr	lr,[r1,28]

00000ca0 e35e0000   1907 	cmp	lr,0

00000ca4 0a000005   1908 	beq	.L2846

00000ca8 e590102c   1909 	ldr	r1,[r0,44]

00000cac e151000e   1910 	cmp	r1,lr

00000cb0 32811001   1911 	addlo	r1,r1,1

00000cb4 3580102c   1912 	strlo	r1,[r0,44]

00000cb8 2580302c   1913 	strhs	r3,[r0,44]

00000cbc 25c02030   1914 	strhsb	r2,[r0,48]

                    1915 .L2846:

00000cc0 e3a00fd0   1916 	mov	r0,0x0340

00000cc4 e2800ab4   1917 	add	r0,r0,45<<14

00000cc8 e0899000   1918 	add	r9,r9,r0

00000ccc e59d8004   1919 	ldr	r8,[sp,4]

00000cd0 e0855000   1920 	add	r5,r5,r0

00000cd4 e0888000   1921 	add	r8,r8,r0

00000cd8 e58d8004   1922 	str	r8,[sp,4]

00000cdc e0866000   1923 	add	r6,r6,r0

00000ce0 e0877000   1924 	add	r7,r7,r0

00000ce4 e08aa000   1925 	add	r10,r10,r0

00000ce8 e08bb000   1926 	add	fp,fp,r0

00000cec e0844000   1927 	add	r4,r4,r0

00000cf0 e25cc001   1928 	subs	r12,r12,1

00000cf4 1affff51   1929 	bne	.L2791

                    1930 .L2790:

00000cf8 e59d000c   1931 	ldr	r0,[sp,12]


                                                                      Page 38
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
00000cfc e210c007   1932 	ands	r12,r0,7

00000d00 0a000020   1933 	beq	.L2749

00000d04 e59d2008   1934 	ldr	r2,[sp,8]

00000d08 e3a04b5a   1935 	mov	r4,90<<10

00000d0c e1a01202   1936 	mov	r1,r2 lsl 4

00000d10 e0420102   1937 	sub	r0,r2,r2 lsl 2

00000d14 e0800001   1938 	add	r0,r0,r1

00000d18 e0800400   1939 	add	r0,r0,r0 lsl 8

00000d1c e0800481   1940 	add	r0,r0,r1 lsl 9

00000d20 e59f1070*  1941 	ldr	r1,.L650

00000d24 e2844068   1942 	add	r4,r4,104

00000d28 e0812180   1943 	add	r2,r1,r0 lsl 3

00000d2c e3a05001   1944 	mov	r5,1

00000d30 e3a06000   1945 	mov	r6,0

                    1946 .L2849:

00000d34 e1a00002   1947 	mov	r0,r2

00000d38 e1a01000   1948 	mov	r1,r0

00000d3c e5d1301a   1949 	ldrb	r3,[r1,26]

00000d40 e3130002   1950 	tst	r3,2

00000d44 1591e01c   1951 	ldrne	lr,[r1,28]

00000d48 135e0000   1952 	cmpne	lr,0

00000d4c 0a00000a   1953 	beq	.L2855

00000d50 e590102c   1954 	ldr	r1,[r0,44]

00000d54 e151000e   1955 	cmp	r1,lr

00000d58 2580602c   1956 	strhs	r6,[r0,44]

00000d5c 25c05030   1957 	strhsb	r5,[r0,48]

00000d60 2a000005   1958 	bhs	.L2855

00000d64 e2811001   1959 	add	r1,r1,1

00000d68 e580102c   1960 	str	r1,[r0,44]

00000d6c e0822004   1961 	add	r2,r2,r4

00000d70 e25cc001   1962 	subs	r12,r12,1

00000d74 1affffee   1963 	bne	.L2849

00000d78 ea000002   1964 	b	.L2749

                    1965 .L2855:

00000d7c e0822004   1966 	add	r2,r2,r4

00000d80 e25cc001   1967 	subs	r12,r12,1

00000d84 1affffea   1968 	bne	.L2849

                    1969 .L2749:

00000d88 e28dd010   1970 	add	sp,sp,16

00000d8c e8bd4ff0   1971 	ldmfd	[sp]!,{r4-fp,lr}

00000d90 e12fff1e*  1972 	ret	

                    1973 	.endf	integrityTimerProc

                    1974 	.align	4

                    1975 .L649:

00000d94 00000000*  1976 	.data.w	g_reportCount

                    1977 	.type	.L649,$object

                    1978 	.size	.L649,4

                    1979 

                    1980 .L650:

00000d98 00000000*  1981 	.data.w	g_reports

                    1982 	.type	.L650,$object

                    1983 	.size	.L650,4

                    1984 

                    1985 .L2600:

00000d9c 00000000*  1986 	.data.w	.L2334

                    1987 	.type	.L2600,$object

                    1988 	.size	.L2600,4

                    1989 

                    1990 .L2601:

00000da0 00000000*  1991 	.data.w	reportValuesBuf

                    1992 	.type	.L2601,$object


                                                                      Page 39
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
                    1993 	.size	.L2601,4

                    1994 

                    1995 .L2602:

00000da4 00000000*  1996 	.data.w	reportInclusionReasons

                    1997 	.type	.L2602,$object

                    1998 	.size	.L2602,4

                    1999 

                    2000 .L2603:

00000da8 00000000*  2001 	.data.w	reportAccessResultsBuf

                    2002 	.type	.L2603,$object

                    2003 	.size	.L2603,4

                    2004 

                    2005 .L2604:

00000dac db237c00   2006 	.data.w	0xdb237c00

                    2007 	.type	.L2604,$object

                    2008 	.size	.L2604,4

                    2009 

                    2010 .L2605:

00000db0 05265c00   2011 	.data.w	0x05265c00

                    2012 	.type	.L2605,$object

                    2013 	.size	.L2605,4

                    2014 

                    2015 .L2656:

00000db4 00000000*  2016 	.data.w	reportMmsBuf

                    2017 	.type	.L2656,$object

                    2018 	.size	.L2656,4

                    2019 

                    2020 .L2657:

00000db8 00000000*  2021 	.data.w	reportVarNameSequence

                    2022 	.type	.L2657,$object

                    2023 	.size	.L2657,4

                    2024 

                    2025 .L2658:

00000dbc 00000000*  2026 	.data.w	reportPresentationBuf

                    2027 	.type	.L2658,$object

                    2028 	.size	.L2658,4

                    2029 

                    2030 .L3446:

00000dc0 0009dad8   2031 	.data.w	0x0009dad8

                    2032 	.type	.L3446,$object

                    2033 	.size	.L3446,4

                    2034 

                    2035 .L3447:

00000dc4 00070a08   2036 	.data.w	0x00070a08

                    2037 	.type	.L3447,$object

                    2038 	.size	.L3447,4

                    2039 

                    2040 .L3448:

00000dc8 00043938   2041 	.data.w	0x00043938

                    2042 	.type	.L3448,$object

                    2043 	.size	.L3448,4

                    2044 

                    2045 	.align	4

                    2046 ;rcbIdx	[sp,8]	local

                    2047 ;pReporter	r0	local

                    2048 ;pRCB	r1	local

                    2049 

                    2050 	.section ".bss","awb"

                    2051 .L3296:

                    2052 	.data

                    2053 	.text


                                                                      Page 40
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
                    2054 

                    2055 

                    2056 	.align	4

                    2057 	.align	4

                    2058 processGI:

00000dcc e92d4010   2059 	stmfd	[sp]!,{r4,lr}

00000dd0 e1a04000   2060 	mov	r4,r0

00000dd4 e5d40022   2061 	ldrb	r0,[r4,34]

00000dd8 e3500000   2062 	cmp	r0,0

00000ddc 0a00000a   2063 	beq	.L3449

00000de0 e1a00004   2064 	mov	r0,r4

00000de4 e3a01000   2065 	mov	r1,0

00000de8 ebfffcb0*  2066 	bl	writeReport

00000dec e2502000   2067 	subs	r2,r0,0

00000df0 da000004   2068 	ble	.L3454

00000df4 e51f1054*  2069 	ldr	r1,.L2603

00000df8 e1a00004   2070 	mov	r0,r4

00000dfc ebfffee0*  2071 	bl	allocateBufAndSendReport

00000e00 e3a00000   2072 	mov	r0,0

00000e04 e5c40022   2073 	strb	r0,[r4,34]

                    2074 .L3454:

00000e08 e3a00001   2075 	mov	r0,1

                    2076 .L3449:

00000e0c e8bd4010   2077 	ldmfd	[sp]!,{r4,lr}

00000e10 e12fff1e*  2078 	ret	

                    2079 	.endf	processGI

                    2080 	.align	4

                    2081 ;reportDataSize	r2	local

                    2082 

                    2083 ;pReporter	r4	param

                    2084 

                    2085 	.data

                    2086 	.text

                    2087 

                    2088 

                    2089 ;730: 


                    2090 ;731: static void reportsThread(void* data)


                    2091 	.align	4

                    2092 	.align	4

                    2093 reportsThread:

00000e14 e92d48f0   2094 	stmfd	[sp]!,{r4-r7,fp,lr}

                    2095 ;732: {


                    2096 

00000e18 e24dd00c   2097 	sub	sp,sp,12

00000e1c e3a00000   2098 	mov	r0,0

00000e20 e1a01000   2099 	mov	r1,r0

00000e24 e98d0003   2100 	stmfa	[sp],{r0-r1}

                    2101 ;733:     unsigned long long timeStamp = 0;


                    2102 

                    2103 ;734:     unsigned long long newTimeStamp;    


                    2104 ;735: 	static bool newBusOK = true;


                    2105 ;736: 	static bool oldBusOK = true;


                    2106 ;737: 


                    2107 ;738: 	//Для цикла по отчётам


                    2108 ;739: 	size_t rptIdx;


                    2109 ;740: 	bool reportsProcessed;


                    2110 ;741:     while(1)


                    2111 

                    2112 .L3539:

                    2113 ;742:     {


                    2114 


                                                                      Page 41
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
                    2115 ;743: 		reportsProcessed = FALSE;


                    2116 

00000e28 e3a06000   2117 	mov	r6,0

                    2118 ;744:         


                    2119 ;745: 		newTimeStamp = getCurrentDataSliceTime();


                    2120 

00000e2c eb000000*  2121 	bl	getCurrentDataSliceTime

00000e30 e1a04000   2122 	mov	r4,r0

00000e34 e1a05001   2123 	mov	r5,r1

                    2124 ;746: 		newBusOK = BusError_check();


                    2125 

00000e38 eb000000*  2126 	bl	BusError_check

                    2127 ;747: 


                    2128 ;748: 		//Если изменилось время или возникла ошибка шины


                    2129 ;749: 		if (timeStamp != newTimeStamp || (!newBusOK && oldBusOK))


                    2130 

00000e3c e99d0006   2131 	ldmed	[sp],{r1-r2}

00000e40 e1520005   2132 	cmp	r2,r5

00000e44 01510004   2133 	cmpeq	r1,r4

00000e48 1a000005   2134 	bne	.L3541

00000e4c e3500000   2135 	cmp	r0,0

00000e50 1a00001c   2136 	bne	.L3540

00000e54 e59f2214*  2137 	ldr	r2,.L4045

00000e58 e5d21000   2138 	ldrb	r1,[r2]

00000e5c e3510000   2139 	cmp	r1,0

00000e60 0a000018   2140 	beq	.L3540

                    2141 .L3541:

                    2142 ;750: 		{			


                    2143 

                    2144 ;751: 			oldBusOK = newBusOK;


                    2145 

00000e64 e59f1204*  2146 	ldr	r1,.L4045

00000e68 e5c10000   2147 	strb	r0,[r1]

                    2148 ;752: 			timeStamp = newTimeStamp;


                    2149 

00000e6c e98d0030   2150 	stmfa	[sp],{r4-r5}

                    2151 ;753:             // Изменилось время, значит могли измениться данные


                    2152 ;754: 			dataSliceCapture();


                    2153 

00000e70 eb000000*  2154 	bl	dataSliceCapture

                    2155 ;755: 


                    2156 ;756: 			//Устанавливам/снимаем флаги изменения в DA, содержащих


                    2157 ;757: 			//данные из DataSlice


                    2158 ;758: 


                    2159 ;759: 


                    2160 ;760: 			IEDTree_lock();


                    2161 

00000e74 eb000000*  2162 	bl	IEDTree_lock

                    2163 ;761: 


                    2164 ;762: 			IEDTree_updateFromDataSlice();


                    2165 

00000e78 eb000000*  2166 	bl	IEDTree_updateFromDataSlice

                    2167 ;763: 


                    2168 ;764:             // Здесь, при наличии изменений, происходит отправка небуферизированных


                    2169 ;765:             // отчётов или помещение в буфер буферизированных			


                    2170 ;766: 			processAllReportsData();			


                    2171 

                    2172 ;601: {


                    2173 

                    2174 ;602: 	size_t rcbIdx;


                    2175 ;603: 	for (rcbIdx = 0; rcbIdx < g_reportCount; ++rcbIdx)



                                                                      Page 42
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
                    2176 

00000e7c e51f50ec*  2177 	ldr	r5,.L650

00000e80 e3a06b5a   2178 	mov	r6,90<<10

00000e84 e51f70f8*  2179 	ldr	r7,.L649

00000e88 e2866068   2180 	add	r6,r6,104

00000e8c e5970000   2181 	ldr	r0,[r7]

00000e90 e3a04000   2182 	mov	r4,0

00000e94 e1540000   2183 	cmp	r4,r0

00000e98 2a000006   2184 	bhs	.L3544

                    2185 .L3547:

                    2186 ;604: 	{


                    2187 

                    2188 ;605: 		Reporter* pReporter = g_reports + rcbIdx;


                    2189 

                    2190 ;606: 		RptDataSet_updateChanges(pReporter->rptDataSet);


                    2191 

00000e9c e595003c   2192 	ldr	r0,[r5,60]

00000ea0 e0855006   2193 	add	r5,r5,r6

00000ea4 eb000000*  2194 	bl	RptDataSet_updateChanges

00000ea8 e5970000   2195 	ldr	r0,[r7]

00000eac e2844001   2196 	add	r4,r4,1

00000eb0 e1540000   2197 	cmp	r4,r0

00000eb4 3afffff8   2198 	blo	.L3547

                    2199 .L3544:

                    2200 ;767: 			IEDTree_unlock();


                    2201 

00000eb8 eb000000*  2202 	bl	IEDTree_unlock

                    2203 ;768: 


                    2204 ;769:             Control_processCtrlObjects();


                    2205 

00000ebc eb000000*  2206 	bl	Control_processCtrlObjects

                    2207 ;770: 			dataSliceRelease();


                    2208 

00000ec0 eb000000*  2209 	bl	dataSliceRelease

                    2210 ;771: 			reportsProcessed = TRUE;


                    2211 

00000ec4 e3a06001   2212 	mov	r6,1

                    2213 .L3540:

                    2214 ;772: 		} 


                    2215 ;773: 		


                    2216 ;774: 		//Отправка Intergrity


                    2217 ;775: 		for (rptIdx = 0; rptIdx < g_reportCount; rptIdx++)


                    2218 

00000ec8 e51f113c*  2219 	ldr	r1,.L649

00000ecc e51f713c*  2220 	ldr	r7,.L650

00000ed0 e5910000   2221 	ldr	r0,[r1]

00000ed4 e3a05000   2222 	mov	r5,0

00000ed8 e1550000   2223 	cmp	r5,r0

00000edc 2a00002c   2224 	bhs	.L3550

                    2225 .L3552:

                    2226 ;776: 		{


                    2227 

                    2228 ;777: 			Reporter* pReporter = g_reports + rptIdx;			


                    2229 

00000ee0 e1a04007   2230 	mov	r4,r7

                    2231 ;778: 			if (processIntegrity(pReporter))


                    2232 

                    2233 ;695: {


                    2234 

00000ee4 e594101c   2235 	ldr	r1,[r4,28]

00000ee8 e3510000   2236 	cmp	r1,0


                                                                      Page 43
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
00000eec 15d41030   2237 	ldrneb	r1,[r4,48]

00000ef0 e3a0b000   2238 	mov	fp,0

                    2239 ;696: 	int reportDataSize;


                    2240 ;697:     bool sent = false;


                    2241 

                    2242 ;698: 	


                    2243 ;699: 	if (pReporter->rcb.intgPd == 0 || !pReporter->intgTimerAlam)


                    2244 

00000ef4 13510000   2245 	cmpne	r1,0

00000ef8 0a00001e   2246 	beq	.L3551

                    2247 ;700: 	{


                    2248 

                    2249 ;701:         return sent;


                    2250 

                    2251 ;702: 	}


                    2252 ;703: 		


                    2253 ;704:     if (pReporter->rcb.buffered)


                    2254 

00000efc e5d41000   2255 	ldrb	r1,[r4]

00000f00 e3510000   2256 	cmp	r1,0

00000f04 0a00000b   2257 	beq	.L3564

                    2258 ;705:     {


                    2259 

                    2260 ;706:         reportDataSize = writeReport(pReporter, RPT_INTG);


                    2261 

00000f08 e1a00004   2262 	mov	r0,r4

00000f0c e3a01001   2263 	mov	r1,1

00000f10 ebfffc66*  2264 	bl	writeReport

00000f14 e2502000   2265 	subs	r2,r0,0

                    2266 ;707:         if (reportDataSize > 0)


                    2267 

00000f18 da000012   2268 	ble	.L3895

                    2269 ;708:         {


                    2270 

                    2271 ;709:             //Пишем в буфер буферизированного отчёта


                    2272 ;710:             writeReportToBuffer(pReporter, reportAccessResultsBuf,


                    2273 

00000f1c e51f117c*  2274 	ldr	r1,.L2603

00000f20 e1a00004   2275 	mov	r0,r4

00000f24 ebfffe91*  2276 	bl	writeReportToBuffer

                    2277 ;711:                 reportDataSize);


                    2278 ;712:             sent = true;


                    2279 

00000f28 e51f119c*  2280 	ldr	r1,.L649

00000f2c e3a0b001   2281 	mov	fp,1

00000f30 e5910000   2282 	ldr	r0,[r1]

00000f34 ea00000d   2283 	b	.L3567

                    2284 .L3564:

                    2285 ;713:         }


                    2286 ;714:     }


                    2287 ;715:     else if(pReporter->rcb.rptEna)


                    2288 

00000f38 e5d41001   2289 	ldrb	r1,[r4,1]

00000f3c e3510000   2290 	cmp	r1,0

00000f40 0a00000a   2291 	beq	.L3567

                    2292 ;716:     {


                    2293 

                    2294 ;717:         reportDataSize = writeReport(pReporter, RPT_INTG);


                    2295 

00000f44 e1a00004   2296 	mov	r0,r4

00000f48 e3a01001   2297 	mov	r1,1


                                                                      Page 44
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
00000f4c ebfffc57*  2298 	bl	writeReport

00000f50 e2502000   2299 	subs	r2,r0,0

                    2300 ;718:         if (reportDataSize > 0)


                    2301 

00000f54 da000003   2302 	ble	.L3895

                    2303 ;719:         {


                    2304 

                    2305 ;720:             allocateBufAndSendReport(pReporter, reportAccessResultsBuf,


                    2306 

00000f58 e51f11b8*  2307 	ldr	r1,.L2603

00000f5c e1a00004   2308 	mov	r0,r4

00000f60 ebfffe87*  2309 	bl	allocateBufAndSendReport

                    2310 ;721:                 reportDataSize);


                    2311 ;722:             sent = true;


                    2312 

00000f64 e3a0b001   2313 	mov	fp,1

                    2314 .L3895:

00000f68 e51f11dc*  2315 	ldr	r1,.L649

00000f6c e5910000   2316 	ldr	r0,[r1]

                    2317 .L3567:

                    2318 ;723:         }


                    2319 ;724:     }


                    2320 ;725: 


                    2321 ;726:     pReporter->intgTimerAlam = false;


                    2322 

00000f70 e3a01000   2323 	mov	r1,0

00000f74 e5c41030   2324 	strb	r1,[r4,48]

                    2325 ;727: 


                    2326 ;728:     return sent;


                    2327 

                    2328 .L3551:

00000f78 e2871b5a   2329 	add	r1,r7,90<<10

00000f7c e2817068   2330 	add	r7,r1,104

00000f80 e35b0000   2331 	cmp	fp,0

00000f84 13a06001   2332 	movne	r6,1

                    2333 

                    2334 

00000f88 e2855001   2335 	add	r5,r5,1

00000f8c e1550000   2336 	cmp	r5,r0

00000f90 3affffd2   2337 	blo	.L3552

                    2338 .L3550:

                    2339 ;781: 			}


                    2340 ;782: 		}


                    2341 ;783: 


                    2342 ;784:         // В этом цикле отправляются буфера буфериризированных отчётов


                    2343 ;785:         // и General Interrogation.


                    2344 ;786:         // Для небуферизированных отчётов GI просто посылается.


                    2345 ;787:         // Для буферизированных GI посылается только если буфер пуст.


                    2346 ;788: 		for (rptIdx = 0; rptIdx < g_reportCount; rptIdx++)


                    2347 

00000f94 e51f0208*  2348 	ldr	r0,.L649

00000f98 e51fb208*  2349 	ldr	fp,.L650

00000f9c e5901000   2350 	ldr	r1,[r0]

00000fa0 e3a05000   2351 	mov	r5,0

00000fa4 e1550001   2352 	cmp	r5,r1

00000fa8 2a00002d   2353 	bhs	.L3569

                    2354 .L3571:

                    2355 ;789: 		{


                    2356 

                    2357 ;790: 			Reporter* pReporter = g_reports + rptIdx;


                    2358 


                                                                      Page 45
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
00000fac e1a0400b   2359 	mov	r4,fp

                    2360 ;791: 			RCB* pRCB = &pReporter->rcb;


                    2361 

00000fb0 e1a00004   2362 	mov	r0,r4

                    2363 ;792: 			if (!pRCB->rptEna)


                    2364 

00000fb4 e5d02001   2365 	ldrb	r2,[r0,1]

00000fb8 e3520000   2366 	cmp	r2,0

00000fbc 0a000023   2367 	beq	.L3570

                    2368 ;793: 			{


                    2369 

                    2370 ;794: 				continue;


                    2371 

                    2372 ;795: 			}


                    2373 ;796: 			if (pRCB->buffered)


                    2374 

00000fc0 e5d00000   2375 	ldrb	r0,[r0]

00000fc4 e3500000   2376 	cmp	r0,0

00000fc8 0a00001b   2377 	beq	.L3577

                    2378 ;797: 			{


                    2379 

                    2380 ;798: 				//Для буферизированных отчётов посылается либо содержимое буфера


                    2381 ;799: 				//либо, если буфер пустой, можно послать GI


                    2382 ;800: 				if (bufferedReportHasData(pReporter))


                    2383 

                    2384 ;646: {	


                    2385 

                    2386 ;647: 	return !ReportQueue_isEmpty(&pCurrReport->buffer);


                    2387 

00000fcc e2840044   2388 	add	r0,r4,68

00000fd0 eb000000*  2389 	bl	ReportQueue_isEmpty

00000fd4 e3500000   2390 	cmp	r0,0

00000fd8 1a000017   2391 	bne	.L3577

                    2392 ;801: 				{


                    2393 

                    2394 ;802: 					sendFromReportBuf(pReporter);


                    2395 

                    2396 ;656: {		


                    2397 

                    2398 ;657: 	//Буферизированный отчёт и в буфере есть данные


                    2399 ;658: 	if (!pRCB->sessionOutBuffer.busy)


                    2400 

00000fdc e2846b42   2401 	add	r6,r4,66<<10

00000fe0 e5f60060   2402 	ldrb	r0,[r6,96]!

00000fe4 e3500000   2403 	cmp	r0,0

00000fe8 1a00000c   2404 	bne	.L3585

                    2405 ;659: 	{


                    2406 

                    2407 ;660: 		int reportSize = readReportFromBuffer(pRCB, reportAccessResultsBuf,


                    2408 

                    2409 ;651: {


                    2410 

                    2411 ;652: 	return ReportQueue_read(&pRCB->buffer, bufferToRead, bufSize);


                    2412 

00000fec e51f724c*  2413 	ldr	r7,.L2603

00000ff0 e2840044   2414 	add	r0,r4,68

00000ff4 e1a01007   2415 	mov	r1,r7

00000ff8 e3a02d80   2416 	mov	r2,1<<13

00000ffc eb000000*  2417 	bl	ReportQueue_read

00001000 e1b02000   2418 	movs	r2,r0

                    2419 ;661: 			sizeof(reportAccessResultsBuf));



                                                                      Page 46
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
                    2420 ;662: 		if (reportSize != 0)


                    2421 

00001004 0a000005   2422 	beq	.L3585

                    2423 ;663: 		{


                    2424 

                    2425 ;664: 			pRCB->sessionOutBuffer.busy = TRUE;


                    2426 

00001008 e1a03006   2427 	mov	r3,r6

0000100c e3a00001   2428 	mov	r0,1

00001010 e5c60000   2429 	strb	r0,[r6]

                    2430 ;665: 			sendReport(pRCB->connection, reportAccessResultsBuf, reportSize,


                    2431 

00001014 e5940040   2432 	ldr	r0,[r4,64]

00001018 e1a01007   2433 	mov	r1,r7

0000101c ebfffe1a*  2434 	bl	sendReport

                    2435 .L3585:

                    2436 ;803: 					reportsProcessed = TRUE;


                    2437 

00001020 e51f0294*  2438 	ldr	r0,.L649

00001024 e3a06001   2439 	mov	r6,1

00001028 e5901000   2440 	ldr	r1,[r0]

0000102c e28b0b5a   2441 	add	r0,fp,90<<10

00001030 e280b068   2442 	add	fp,r0,104

00001034 e2855001   2443 	add	r5,r5,1

00001038 ea000007   2444 	b	.L3572

                    2445 .L3577:

                    2446 ;804: 				}


                    2447 ;805: 				else


                    2448 ;806: 				{


                    2449 

                    2450 ;807: 					reportsProcessed = processGI(pReporter);


                    2451 

                    2452 ;808: 				}


                    2453 ;809: 			}


                    2454 ;810: 			else


                    2455 ;811: 			{


                    2456 

                    2457 ;812: 				reportsProcessed = processGI(pReporter);


                    2458 

0000103c e1a00004   2459 	mov	r0,r4

00001040 ebffff61*  2460 	bl	processGI

00001044 e1a06000   2461 	mov	r6,r0

00001048 e51f02bc*  2462 	ldr	r0,.L649

0000104c e5901000   2463 	ldr	r1,[r0]

                    2464 .L3570:

00001050 e28b0b5a   2465 	add	r0,fp,90<<10

00001054 e280b068   2466 	add	fp,r0,104

00001058 e2855001   2467 	add	r5,r5,1

                    2468 .L3572:

0000105c e1550001   2469 	cmp	r5,r1

00001060 3affffd1   2470 	blo	.L3571

                    2471 .L3569:

                    2472 ;813: 			}


                    2473 ;814: 			


                    2474 ;815: 		}			


                    2475 ;816: 		if(!reportsProcessed)


                    2476 

00001064 e3560000   2477 	cmp	r6,0

00001068 1affff6e   2478 	bne	.L3539

                    2479 ;817:         {         


                    2480 


                                                                      Page 47
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
                    2481 ;818:             Idle();            


                    2482 

0000106c ea000000   2483 	b	.L4046

                    2484 	.align	4

                    2485 .L4045:

00001070 00000000*  2486 	.data.w	.L3977

                    2487 	.type	.L4045,$object

                    2488 	.size	.L4045,4

                    2489 

                    2490 .L4046:

                    2491 

00001074 e6000010   2492 	.word	0xE6000010

                    2493 

00001078 eaffff6a   2494 	b	.L3539

                    2495 	.endf	reportsThread

                    2496 	.align	4

                    2497 ;newBusOK	r0	local

                    2498 ;oldBusOK	.L3977	static

                    2499 ;timeStamp	[sp,4]	local

                    2500 ;newTimeStamp	r4	local

                    2501 ;rptIdx	r5	local

                    2502 ;reportsProcessed	r6	local

                    2503 ;rcbIdx	r4	local

                    2504 ;pReporter	r4	local

                    2505 ;reportDataSize	r2	local

                    2506 ;sent	fp	local

                    2507 ;pReporter	r4	local

                    2508 ;pRCB	r0	local

                    2509 ;reportSize	r2	local

                    2510 

                    2511 ;data	none	param

                    2512 

                    2513 	.data

00000000 01        2514 .L3977:	.data.b	1

                    2515 	.text

                    2516 

                    2517 ;819:         }		


                    2518 ;820:     }


                    2519 ;821: }


                    2520 

                    2521 ;822: 


                    2522 ;823: void initReports(void)


                    2523 	.align	4

                    2524 	.align	4

                    2525 initReports::

0000107c e92d4010   2526 	stmfd	[sp]!,{r4,lr}

                    2527 ;824: {


                    2528 

                    2529 ;825:     g_reportCount = 0;


                    2530 

00001080 e59f0424*  2531 	ldr	r0,.L4085

00001084 e3a04000   2532 	mov	r4,0

00001088 e5804000   2533 	str	r4,[r0]

                    2534 ;826:     registerAllRCB();


                    2535 

0000108c eb000000*  2536 	bl	registerAllRCB

                    2537 ;827:     createThread(reportsThread,NULL);


                    2538 

00001090 e59f0418*  2539 	ldr	r0,.L4086

00001094 e1a01004   2540 	mov	r1,r4

00001098 eb000000*  2541 	bl	createThread


                                                                      Page 48
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
                    2542 ;828: 	Timers_setIntegrity1msCallBack(integrityTimerProc);


                    2543 

0000109c e59f0410*  2544 	ldr	r0,.L4087

000010a0 e8bd4010   2545 	ldmfd	[sp]!,{r4,lr}

000010a4 ea000000*  2546 	b	Timers_setIntegrity1msCallBack

                    2547 	.endf	initReports

                    2548 	.align	4

                    2549 

                    2550 	.section ".bss","awb"

                    2551 .L4078:

                    2552 	.data

                    2553 	.text

                    2554 

                    2555 ;829: }


                    2556 

                    2557 ;830: 


                    2558 ;831: PReporter getFreeReport(void)


                    2559 	.align	4

                    2560 	.align	4

                    2561 getFreeReport::

                    2562 ;832: {


                    2563 

                    2564 ;833: 	if (g_reportCount == MAX_REPORT_COUNT)


                    2565 

000010a8 e59f13fc*  2566 	ldr	r1,.L4085

000010ac e5910000   2567 	ldr	r0,[r1]

000010b0 e3500028   2568 	cmp	r0,40

                    2569 ;834: 	{


                    2570 

                    2571 ;835: 		return NULL;


                    2572 

000010b4 03a00000   2573 	moveq	r0,0

000010b8 0a000006   2574 	beq	.L4088

                    2575 ;836: 	}


                    2576 ;837: 


                    2577 ;838: 	return g_reports + g_reportCount;


                    2578 

000010bc e1a01100   2579 	mov	r1,r0 lsl 2

000010c0 e0400001   2580 	sub	r0,r0,r1

000010c4 e0800101   2581 	add	r0,r0,r1 lsl 2

000010c8 e0800400   2582 	add	r0,r0,r0 lsl 8

000010cc e0800581   2583 	add	r0,r0,r1 lsl 11

000010d0 e59f13e0*  2584 	ldr	r1,.L4132

000010d4 e0810180   2585 	add	r0,r1,r0 lsl 3

                    2586 .L4088:

000010d8 e12fff1e*  2587 	ret	

                    2588 	.endf	getFreeReport

                    2589 	.align	4

                    2590 

                    2591 	.section ".bss","awb"

                    2592 .L4118:

                    2593 	.data

                    2594 	.text

                    2595 

                    2596 ;839: }


                    2597 

                    2598 ;840: 


                    2599 ;841: void finalizeReportRegistration(void)


                    2600 	.align	4

                    2601 	.align	4

                    2602 finalizeReportRegistration::


                                                                      Page 49
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
                    2603 ;842: {


                    2604 

                    2605 ;843: 	if (g_reportCount == MAX_REPORT_COUNT)


                    2606 

000010dc e59f13c8*  2607 	ldr	r1,.L4085

000010e0 e5910000   2608 	ldr	r0,[r1]

000010e4 e3500028   2609 	cmp	r0,40

                    2610 ;844: 	{


                    2611 

                    2612 ;845: 		ERROR_REPORT("Too many reports");


                    2613 ;846: 		return;


                    2614 

                    2615 ;847: 	}


                    2616 ;848: 	g_reportCount++;


                    2617 

000010e8 12800001   2618 	addne	r0,r0,1

000010ec 15810000   2619 	strne	r0,[r1]

000010f0 e12fff1e*  2620 	ret	

                    2621 	.endf	finalizeReportRegistration

                    2622 	.align	4

                    2623 

                    2624 	.section ".bss","awb"

                    2625 .L4168:

                    2626 	.data

                    2627 	.text

                    2628 

                    2629 ;849: }


                    2630 

                    2631 ;850: 


                    2632 ;851: void disableDisconnectedReports(void)


                    2633 	.align	4

                    2634 	.align	4

                    2635 disableDisconnectedReports::

000010f4 e92d4ff0   2636 	stmfd	[sp]!,{r4-fp,lr}

                    2637 ;852: {


                    2638 

                    2639 ;853: 	size_t i;


                    2640 ;854: 	Reporter* pReporter;


                    2641 ;855: 


                    2642 ;856: 	for (i = 0; i < g_reportCount; ++i)


                    2643 

000010f8 e3a02b42   2644 	mov	r2,66<<10

000010fc e24dd00c   2645 	sub	sp,sp,12

00001100 e3a00000   2646 	mov	r0,0

00001104 e59f13a0*  2647 	ldr	r1,.L4085

00001108 e58d0004   2648 	str	r0,[sp,4]

0000110c e5910000   2649 	ldr	r0,[r1]

00001110 e2822060   2650 	add	r2,r2,96

00001114 e3500000   2651 	cmp	r0,0

00001118 b3a00000   2652 	movlt	r0,0

0000111c e58d0008   2653 	str	r0,[sp,8]

00001120 e1b031a0   2654 	movs	r3,r0 lsr 3

00001124 0a00007d   2655 	beq	.L4210

00001128 e59f038c*  2656 	ldr	r0,.L4725

0000112c e59fc384*  2657 	ldr	r12,.L4132

00001130 e08c9000   2658 	add	r9,r12,r0

00001134 e28c0f9c   2659 	add	r0,r12,0x0270

00001138 e2804a87   2660 	add	r4,r0,135<<12

0000113c e59f037c*  2661 	ldr	r0,.L4726

00001140 e08c5000   2662 	add	r5,r12,r0

00001144 e28c0f68   2663 	add	r0,r12,0x01a0


                                                                      Page 50
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
00001148 e2806a5a   2664 	add	r6,r0,90<<12

0000114c e59f0370*  2665 	ldr	r0,.L4727

00001150 e3a01000   2666 	mov	r1,0

00001154 e08c7000   2667 	add	r7,r12,r0

00001158 e28c0bb4   2668 	add	r0,r12,45<<12

0000115c e280a0d0   2669 	add	r10,r0,208

00001160 e28c0b5a   2670 	add	r0,r12,90<<10

00001164 e280b068   2671 	add	fp,r0,104

00001168 e1a00183   2672 	mov	r0,r3 lsl 3

0000116c e58d0004   2673 	str	r0,[sp,4]

                    2674 .L4211:

00001170 e1a0000c   2675 	mov	r0,r12

00001174 e590e040   2676 	ldr	lr,[r0,64]

00001178 e35e0000   2677 	cmp	lr,0

0000117c 0a000007   2678 	beq	.L4216

00001180 e3a08bf2   2679 	mov	r8,242<<10

00001184 e2888098   2680 	add	r8,r8,152

00001188 e7d8e00e   2681 	ldrb	lr,[r8,lr]

0000118c e35e0000   2682 	cmp	lr,0

00001190 05c0e001   2683 	streqb	lr,[r0,1]

00001194 05c01002   2684 	streqb	r1,[r0,2]

00001198 05801040   2685 	streq	r1,[r0,64]

0000119c 07c01002   2686 	streqb	r1,[r0,r2]

                    2687 .L4216:

000011a0 e1a0000b   2688 	mov	r0,fp

000011a4 e590e040   2689 	ldr	lr,[r0,64]

000011a8 e35e0000   2690 	cmp	lr,0

000011ac 0a000007   2691 	beq	.L4221

000011b0 e3a08bf2   2692 	mov	r8,242<<10

000011b4 e2888098   2693 	add	r8,r8,152

000011b8 e7d8e00e   2694 	ldrb	lr,[r8,lr]

000011bc e35e0000   2695 	cmp	lr,0

000011c0 05c0e001   2696 	streqb	lr,[r0,1]

000011c4 05c01002   2697 	streqb	r1,[r0,2]

000011c8 05801040   2698 	streq	r1,[r0,64]

000011cc 07c01002   2699 	streqb	r1,[r0,r2]

                    2700 .L4221:

000011d0 e1a0000a   2701 	mov	r0,r10

000011d4 e590e040   2702 	ldr	lr,[r0,64]

000011d8 e35e0000   2703 	cmp	lr,0

000011dc 0a000007   2704 	beq	.L4226

000011e0 e3a08bf2   2705 	mov	r8,242<<10

000011e4 e2888098   2706 	add	r8,r8,152

000011e8 e7d8e00e   2707 	ldrb	lr,[r8,lr]

000011ec e35e0000   2708 	cmp	lr,0

000011f0 05c0e001   2709 	streqb	lr,[r0,1]

000011f4 05c01002   2710 	streqb	r1,[r0,2]

000011f8 05801040   2711 	streq	r1,[r0,64]

000011fc 07c01002   2712 	streqb	r1,[r0,r2]

                    2713 .L4226:

00001200 e1a00007   2714 	mov	r0,r7

00001204 e590e040   2715 	ldr	lr,[r0,64]

00001208 e35e0000   2716 	cmp	lr,0

0000120c 0a000007   2717 	beq	.L4231

00001210 e3a08bf2   2718 	mov	r8,242<<10

00001214 e2888098   2719 	add	r8,r8,152

00001218 e7d8e00e   2720 	ldrb	lr,[r8,lr]

0000121c e35e0000   2721 	cmp	lr,0

00001220 05c0e001   2722 	streqb	lr,[r0,1]

00001224 05c01002   2723 	streqb	r1,[r0,2]

00001228 05801040   2724 	streq	r1,[r0,64]


                                                                      Page 51
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
0000122c 07c01002   2725 	streqb	r1,[r0,r2]

                    2726 .L4231:

00001230 e1a00006   2727 	mov	r0,r6

00001234 e590e040   2728 	ldr	lr,[r0,64]

00001238 e35e0000   2729 	cmp	lr,0

0000123c 0a000007   2730 	beq	.L4236

00001240 e3a08bf2   2731 	mov	r8,242<<10

00001244 e2888098   2732 	add	r8,r8,152

00001248 e7d8e00e   2733 	ldrb	lr,[r8,lr]

0000124c e35e0000   2734 	cmp	lr,0

00001250 05c0e001   2735 	streqb	lr,[r0,1]

00001254 05c01002   2736 	streqb	r1,[r0,2]

00001258 05801040   2737 	streq	r1,[r0,64]

0000125c 07c01002   2738 	streqb	r1,[r0,r2]

                    2739 .L4236:

00001260 e1a00005   2740 	mov	r0,r5

00001264 e590e040   2741 	ldr	lr,[r0,64]

00001268 e35e0000   2742 	cmp	lr,0

0000126c 0a000007   2743 	beq	.L4241

00001270 e3a08bf2   2744 	mov	r8,242<<10

00001274 e2888098   2745 	add	r8,r8,152

00001278 e7d8e00e   2746 	ldrb	lr,[r8,lr]

0000127c e35e0000   2747 	cmp	lr,0

00001280 05c0e001   2748 	streqb	lr,[r0,1]

00001284 05c01002   2749 	streqb	r1,[r0,2]

00001288 05801040   2750 	streq	r1,[r0,64]

0000128c 07c01002   2751 	streqb	r1,[r0,r2]

                    2752 .L4241:

00001290 e1a00004   2753 	mov	r0,r4

00001294 e590e040   2754 	ldr	lr,[r0,64]

00001298 e35e0000   2755 	cmp	lr,0

0000129c 0a000007   2756 	beq	.L4246

000012a0 e3a08bf2   2757 	mov	r8,242<<10

000012a4 e2888098   2758 	add	r8,r8,152

000012a8 e7d8e00e   2759 	ldrb	lr,[r8,lr]

000012ac e35e0000   2760 	cmp	lr,0

000012b0 05c0e001   2761 	streqb	lr,[r0,1]

000012b4 05c01002   2762 	streqb	r1,[r0,2]

000012b8 05801040   2763 	streq	r1,[r0,64]

000012bc 07c01002   2764 	streqb	r1,[r0,r2]

                    2765 .L4246:

000012c0 e1a00009   2766 	mov	r0,r9

000012c4 e590e040   2767 	ldr	lr,[r0,64]

000012c8 e35e0000   2768 	cmp	lr,0

000012cc 0a000007   2769 	beq	.L4250

000012d0 e3a08bf2   2770 	mov	r8,242<<10

000012d4 e2888098   2771 	add	r8,r8,152

000012d8 e7d8e00e   2772 	ldrb	lr,[r8,lr]

000012dc e35e0000   2773 	cmp	lr,0

000012e0 05c0e001   2774 	streqb	lr,[r0,1]

000012e4 05c01002   2775 	streqb	r1,[r0,2]

000012e8 05801040   2776 	streq	r1,[r0,64]

000012ec 07c01002   2777 	streqb	r1,[r0,r2]

                    2778 .L4250:

000012f0 e3a00fd0   2779 	mov	r0,0x0340

000012f4 e2800ab4   2780 	add	r0,r0,45<<14

000012f8 e0899000   2781 	add	r9,r9,r0

000012fc e0844000   2782 	add	r4,r4,r0

00001300 e0855000   2783 	add	r5,r5,r0

00001304 e0866000   2784 	add	r6,r6,r0

00001308 e0877000   2785 	add	r7,r7,r0


                                                                      Page 52
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
0000130c e08aa000   2786 	add	r10,r10,r0

00001310 e08bb000   2787 	add	fp,fp,r0

00001314 e08cc000   2788 	add	r12,r12,r0

00001318 e2533001   2789 	subs	r3,r3,1

0000131c 1affff93   2790 	bne	.L4211

                    2791 .L4210:

00001320 e59d0008   2792 	ldr	r0,[sp,8]

00001324 e2103007   2793 	ands	r3,r0,7

00001328 0a000019   2794 	beq	.L4179

0000132c e59dc004   2795 	ldr	r12,[sp,4]

00001330 e3a05b5a   2796 	mov	r5,90<<10

00001334 e1a0120c   2797 	mov	r1,r12 lsl 4

00001338 e04c010c   2798 	sub	r0,r12,r12 lsl 2

0000133c e0800001   2799 	add	r0,r0,r1

00001340 e0800400   2800 	add	r0,r0,r0 lsl 8

00001344 e0800481   2801 	add	r0,r0,r1 lsl 9

00001348 e59f1168*  2802 	ldr	r1,.L4132

0000134c e3a0c000   2803 	mov	r12,0

00001350 e0811180   2804 	add	r1,r1,r0 lsl 3

00001354 e2855068   2805 	add	r5,r5,104

                    2806 .L4253:

00001358 e1a00001   2807 	mov	r0,r1

0000135c e590e040   2808 	ldr	lr,[r0,64]

00001360 e35e0000   2809 	cmp	lr,0

00001364 0a000007   2810 	beq	.L4257

00001368 e3a08bf2   2811 	mov	r8,242<<10

0000136c e2888098   2812 	add	r8,r8,152

00001370 e7d8400e   2813 	ldrb	r4,[r8,lr]

00001374 e3540000   2814 	cmp	r4,0

00001378 05c04001   2815 	streqb	r4,[r0,1]

0000137c 05c0c002   2816 	streqb	r12,[r0,2]

00001380 0580c040   2817 	streq	r12,[r0,64]

00001384 07c0c002   2818 	streqb	r12,[r0,r2]

                    2819 .L4257:

00001388 e0811005   2820 	add	r1,r1,r5

0000138c e2533001   2821 	subs	r3,r3,1

00001390 1afffff0   2822 	bne	.L4253

                    2823 .L4179:

00001394 e28dd00c   2824 	add	sp,sp,12

00001398 e8bd8ff0   2825 	ldmfd	[sp]!,{r4-fp,pc}

                    2826 	.endf	disableDisconnectedReports

                    2827 	.align	4

                    2828 ;i	[sp,4]	local

                    2829 ;pReporter	r0	local

                    2830 

                    2831 	.section ".bss","awb"

                    2832 .L4627:

                    2833 	.data

                    2834 	.text

                    2835 

                    2836 ;867: 			}


                    2837 ;868: 		}


                    2838 ;869: 	}


                    2839 ;870: }


                    2840 

                    2841 ;871: 


                    2842 ;872: bool Reporter_isOwnerConnection(PReporter pReport, IsoConnection* conn)


                    2843 	.align	4

                    2844 	.align	4

                    2845 Reporter_isOwnerConnection::

                    2846 ;873: {



                                                                      Page 53
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
                    2847 

                    2848 ;874:     IsoConnection* owner = pReport->connection;


                    2849 

0000139c e5900040   2850 	ldr	r0,[r0,64]

                    2851 ;875:     return owner == conn;


                    2852 

000013a0 e1500001   2853 	cmp	r0,r1

000013a4 03a00001   2854 	moveq	r0,1

000013a8 13a00000   2855 	movne	r0,0

000013ac e12fff1e*  2856 	ret	

                    2857 	.endf	Reporter_isOwnerConnection

                    2858 	.align	4

                    2859 ;owner	r0	local

                    2860 

                    2861 ;pReport	r0	param

                    2862 ;conn	r1	param

                    2863 

                    2864 	.section ".bss","awb"

                    2865 .L4753:

                    2866 	.data

                    2867 	.text

                    2868 

                    2869 ;876: }


                    2870 

                    2871 ;877: 


                    2872 ;878: bool Reporter_setEnable(PReporter rpt, IsoConnection* isoConn, bool enable)


                    2873 	.align	4

                    2874 	.align	4

                    2875 Reporter_setEnable::

000013b0 e92d4070   2876 	stmfd	[sp]!,{r4-r6,lr}

                    2877 ;879: {    


                    2878 

                    2879 ;880: 	if (isRCBConnected(rpt))


                    2880 

000013b4 e1a04001   2881 	mov	r4,r1

000013b8 e1a06002   2882 	mov	r6,r2

000013bc e1a05000   2883 	mov	r5,r0

000013c0 ebfffb2a*  2884 	bl	isRCBConnected

000013c4 e3500000   2885 	cmp	r0,0

000013c8 0a00000b   2886 	beq	.L4762

                    2887 ;881: 	{


                    2888 

                    2889 ;882: 		//Захваченный RCB


                    2890 ;883: 		if (rpt->connection == isoConn)


                    2891 

000013cc e5950040   2892 	ldr	r0,[r5,64]

000013d0 e1500004   2893 	cmp	r0,r4

                    2894 ;889: 			}


                    2895 ;890: 			rpt->rcb.rptEna = enable;


                    2896 

                    2897 ;891: 		}


                    2898 ;892: 		else


                    2899 ;893: 		{


                    2900 

                    2901 ;894: 			//Чужой RCB


                    2902 ;895: 			TRACE("Attempt to write captured RCB");


                    2903 ;896: 			return FALSE;


                    2904 

000013d4 13a00000   2905 	movne	r0,0

000013d8 1a00000b   2906 	bne	.L4760

                    2907 ;884: 		{



                                                                      Page 54
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
                    2908 

                    2909 ;885: 			//Свой RCB


                    2910 ;886:             if (!enable && !rpt->rcb.resv)


                    2911 

000013dc e3560000   2912 	cmp	r6,0

000013e0 05d51002   2913 	ldreqb	r1,[r5,2]

000013e4 03510000   2914 	cmpeq	r1,0

                    2915 ;887: 			{


                    2916 

                    2917 ;888: 				rpt->connection = NULL;


                    2918 

000013e8 05851040   2919 	streq	r1,[r5,64]

000013ec 05c56001   2920 	streqb	r6,[r5,1]

                    2921 ;906: 		}


                    2922 ;907: 		rpt->rcb.rptEna = enable;


                    2923 

                    2924 ;908: 	}


                    2925 ;909: 	return TRUE;


                    2926 

000013f0 03a00001   2927 	moveq	r0,1

000013f4 0a000004   2928 	beq	.L4760

000013f8 ea000001   2929 	b	.L4771

                    2930 .L4762:

                    2931 ;897: 		}


                    2932 ;898: 	}


                    2933 ;899: 	else


                    2934 ;900: 	{


                    2935 

                    2936 ;901: 		//Свободный RCB


                    2937 ;902: 		if (enable)


                    2938 

000013fc e3560000   2939 	cmp	r6,0

                    2940 ;903: 		{


                    2941 

                    2942 ;904: 			//Захватываем RCB


                    2943 ;905: 			rpt->connection = isoConn;


                    2944 

00001400 15854040   2945 	strne	r4,[r5,64]

                    2946 .L4771:

00001404 e5c56001   2947 	strb	r6,[r5,1]

                    2948 ;906: 		}


                    2949 ;907: 		rpt->rcb.rptEna = enable;


                    2950 

                    2951 ;908: 	}


                    2952 ;909: 	return TRUE;


                    2953 

00001408 e3a00001   2954 	mov	r0,1

                    2955 .L4760:

0000140c e8bd8070   2956 	ldmfd	[sp]!,{r4-r6,pc}

                    2957 	.endf	Reporter_setEnable

                    2958 	.align	4

                    2959 

                    2960 ;rpt	r5	param

                    2961 ;isoConn	r4	param

                    2962 ;enable	r6	param

                    2963 

                    2964 	.section ".bss","awb"

                    2965 .L4897:

                    2966 	.data

                    2967 	.text

                    2968 


                                                                      Page 55
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
                    2969 ;910: }


                    2970 

                    2971 ;911: 


                    2972 ;912: bool Reporter_setResv(PReporter rpt, IsoConnection* isoConn, bool value)


                    2973 	.align	4

                    2974 	.align	4

                    2975 Reporter_setResv::

00001410 e92d4070   2976 	stmfd	[sp]!,{r4-r6,lr}

                    2977 ;913: {


                    2978 

                    2979 ;914:     if (isRCBConnected(rpt))


                    2980 

00001414 e1a04001   2981 	mov	r4,r1

00001418 e1a06002   2982 	mov	r6,r2

0000141c e1a05000   2983 	mov	r5,r0

00001420 ebfffb12*  2984 	bl	isRCBConnected

00001424 e3500000   2985 	cmp	r0,0

00001428 0a00000b   2986 	beq	.L4927

                    2987 ;915:     {


                    2988 

                    2989 ;916:         //Захваченный RCB


                    2990 ;917:         if (rpt->connection == isoConn)


                    2991 

0000142c e5950040   2992 	ldr	r0,[r5,64]

00001430 e1500004   2993 	cmp	r0,r4

                    2994 ;923:             }


                    2995 ;924:             rpt->rcb.resv = value;


                    2996 

                    2997 ;925:         }


                    2998 ;926:         else


                    2999 ;927:         {


                    3000 

                    3001 ;928:             //Чужой RCB


                    3002 ;929:             TRACE("Attempt to write captured RCB");


                    3003 ;930:             return FALSE;


                    3004 

00001434 13a00000   3005 	movne	r0,0

00001438 1a00000b   3006 	bne	.L4925

                    3007 ;918:         {


                    3008 

                    3009 ;919:             //Свой RCB


                    3010 ;920:             if (!value && !rpt->rcb.rptEna)


                    3011 

0000143c e3560000   3012 	cmp	r6,0

00001440 05d51001   3013 	ldreqb	r1,[r5,1]

00001444 03510000   3014 	cmpeq	r1,0

                    3015 ;921:             {


                    3016 

                    3017 ;922:                 rpt->connection = NULL;


                    3018 

00001448 05851040   3019 	streq	r1,[r5,64]

0000144c 05c56002   3020 	streqb	r6,[r5,2]

                    3021 ;940:         }


                    3022 ;941:         rpt->rcb.resv = value;


                    3023 

                    3024 ;942:     }


                    3025 ;943:     return TRUE;


                    3026 

00001450 03a00001   3027 	moveq	r0,1

00001454 0a000004   3028 	beq	.L4925

00001458 ea000001   3029 	b	.L4936


                                                                      Page 56
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
                    3030 .L4927:

                    3031 ;931:         }


                    3032 ;932:     }


                    3033 ;933:     else


                    3034 ;934:     {


                    3035 

                    3036 ;935:         //Свободный RCB


                    3037 ;936:         if (value)


                    3038 

0000145c e3560000   3039 	cmp	r6,0

                    3040 ;937:         {


                    3041 

                    3042 ;938:             //Захватываем RCB


                    3043 ;939:             rpt->connection = isoConn;


                    3044 

00001460 15854040   3045 	strne	r4,[r5,64]

                    3046 .L4936:

00001464 e5c56002   3047 	strb	r6,[r5,2]

                    3048 ;940:         }


                    3049 ;941:         rpt->rcb.resv = value;


                    3050 

                    3051 ;942:     }


                    3052 ;943:     return TRUE;


                    3053 

00001468 e3a00001   3054 	mov	r0,1

                    3055 .L4925:

0000146c e8bd8070   3056 	ldmfd	[sp]!,{r4-r6,pc}

                    3057 	.endf	Reporter_setResv

                    3058 	.align	4

                    3059 

                    3060 ;rpt	r5	param

                    3061 ;isoConn	r4	param

                    3062 ;value	r6	param

                    3063 

                    3064 	.section ".bss","awb"

                    3065 .L5057:

                    3066 	.data

                    3067 	.text

                    3068 

                    3069 ;944: }


                    3070 

                    3071 ;945: 


                    3072 ;946: void Reporter_setDataSetName(PReporter rpt, StringView* name)


                    3073 	.align	4

                    3074 	.align	4

                    3075 Reporter_setDataSetName::

                    3076 ;947: {


                    3077 

                    3078 ;948: 	rpt->rcb.dataSetName = (uint8_t*)name->p;


                    3079 

00001470 e5912004   3080 	ldr	r2,[r1,4]

00001474 e5911000   3081 	ldr	r1,[r1]

00001478 e580200c   3082 	str	r2,[r0,12]

                    3083 ;949: 	rpt->rcb.dataSetNameLength = name->len;


                    3084 

0000147c e5801010   3085 	str	r1,[r0,16]

00001480 e12fff1e*  3086 	ret	

                    3087 	.endf	Reporter_setDataSetName

                    3088 	.align	4

                    3089 

                    3090 ;rpt	r0	param


                                                                      Page 57
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
                    3091 ;name	r1	param

                    3092 

                    3093 	.section ".bss","awb"

                    3094 .L5102:

                    3095 	.data

                    3096 	.text

                    3097 

                    3098 ;950: }


                    3099 

                    3100 ;951: 


                    3101 ;952: void Reporter_setIntgPd(PReporter rpt, uint32_t intgPd)


                    3102 	.align	4

                    3103 	.align	4

                    3104 Reporter_setIntgPd::

                    3105 ;953: {


                    3106 

                    3107 ;954: 	//Если значение меньше минимального допустимого, пишем минимально допустимое


                    3108 ;955: 	//0 - специальное значение для выключения Integrity


                    3109 ;956: 	if (intgPd != 0 && intgPd < MIN_INTG_PD)


                    3110 

00001484 e3510000   3111 	cmp	r1,0

                    3112 

                    3113 

00001488 13510064   3114 	cmpne	r1,100

0000148c 33a01064   3115 	movlo	r1,100

                    3116 .L5111:

                    3117 ;959: 	}


                    3118 ;960: 


                    3119 ;961: 	//Останавливаем таймер


                    3120 ;962: 	rpt->rcb.intgPd = 0;


                    3121 

00001490 e3a02000   3122 	mov	r2,0

                    3123 ;963: 	//Сбрасываем таймер


                    3124 ;964: 	rpt->intgPdCounter = 0;


                    3125 

00001494 e580202c   3126 	str	r2,[r0,44]

                    3127 ;965: 	rpt->intgTimerAlam = false;


                    3128 

00001498 e5c02030   3129 	strb	r2,[r0,48]

                    3130 ;966: 	//Пишем новое значение	


                    3131 ;967: 	rpt->rcb.intgPd = intgPd;


                    3132 

0000149c e580101c   3133 	str	r1,[r0,28]

000014a0 e12fff1e*  3134 	ret	

                    3135 	.endf	Reporter_setIntgPd

                    3136 	.align	4

                    3137 

                    3138 ;rpt	r0	param

                    3139 ;intgPd	r1	param

                    3140 

                    3141 	.section ".bss","awb"

                    3142 .L5152:

                    3143 	.data

                    3144 	.text

                    3145 

                    3146 ;968: }


                    3147 

                    3148 ;969: 


                    3149 ;970: void Reporter_setGI(PReporter rpt, bool gi)


                    3150 	.align	4

                    3151 	.align	4


                                                                      Page 58
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
                    3152 Reporter_setGI::

                    3153 ;971: {


                    3154 

                    3155 ;972:     rpt->rcb.gi = gi;


                    3156 

000014a4 e5c01022   3157 	strb	r1,[r0,34]

000014a8 e12fff1e*  3158 	ret	

                    3159 	.endf	Reporter_setGI

                    3160 	.align	4

                    3161 

                    3162 ;rpt	r0	param

                    3163 ;gi	r1	param

                    3164 

                    3165 	.section ".bss","awb"

                    3166 .L5182:

                    3167 	.data

                    3168 	.text

                    3169 

                    3170 ;973: }


                    3171 	.align	4

                    3172 .L4085:

000014ac 00000000*  3173 	.data.w	g_reportCount

                    3174 	.type	.L4085,$object

                    3175 	.size	.L4085,4

                    3176 

                    3177 .L4086:

000014b0 00000000*  3178 	.data.w	reportsThread

                    3179 	.type	.L4086,$object

                    3180 	.size	.L4086,4

                    3181 

                    3182 .L4087:

000014b4 00000000*  3183 	.data.w	integrityTimerProc

                    3184 	.type	.L4087,$object

                    3185 	.size	.L4087,4

                    3186 

                    3187 .L4132:

000014b8 00000000*  3188 	.data.w	g_reports

                    3189 	.type	.L4132,$object

                    3190 	.size	.L4132,4

                    3191 

                    3192 .L4725:

000014bc 0009dad8   3193 	.data.w	0x0009dad8

                    3194 	.type	.L4725,$object

                    3195 	.size	.L4725,4

                    3196 

                    3197 .L4726:

000014c0 00070a08   3198 	.data.w	0x00070a08

                    3199 	.type	.L4726,$object

                    3200 	.size	.L4726,4

                    3201 

                    3202 .L4727:

000014c4 00043938   3203 	.data.w	0x00043938

                    3204 	.type	.L4727,$object

                    3205 	.size	.L4727,4

                    3206 

                    3207 	.align	4

                    3208 ;reportInclusionReasons	reportInclusionReasons	static

                    3209 ;reportValuesBuf	reportValuesBuf	static

                    3210 ;reportAccessResultsBuf	reportAccessResultsBuf	static

                    3211 ;reportMmsBuf	reportMmsBuf	static

                    3212 ;reportPresentationBuf	reportPresentationBuf	static


                                                                      Page 59
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b281.s
                    3213 

                    3214 	.data

00000001 000000    3215 	.space	3

                    3216 .L5204:

                    3217 	.globl	reportVarNameSequence

00000004 038005a1   3218 reportVarNameSequence:	.data.b	161,5,128,3

00000008 5052      3219 	.data.b	82,80

0000000a 54        3220 	.data.b	84

0000000b 00        3221 	.space	1

                    3222 	.type	reportVarNameSequence,$object

                    3223 	.size	reportVarNameSequence,8

                    3224 .L5205:

                    3225 	.globl	g_reportCount

0000000c 00000000   3226 g_reportCount:	.data.b	0,0,0,0

                    3227 	.type	g_reportCount,$object

                    3228 	.size	g_reportCount,4

                    3229 	.comm	g_reports,3690560,4

                    3230 	.type	g_reports,$object

                    3231 	.size	g_reports,3690560

                    3232 	.ghsnote version,6

                    3233 	.ghsnote tools,1

                    3234 	.ghsnote options,0

                    3235 	.text

                    3236 	.align	4

                    3237 	.data

                    3238 	.align	4

                    3239 	.section ".bss","awb"

                    3240 	.align	4

                    3241 	.text

