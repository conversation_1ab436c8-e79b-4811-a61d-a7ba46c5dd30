                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cdc1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=OscFiles.c -o fs\gh_cdc1.o -list=fs/OscFiles.lst C:\Users\<USER>\AppData\Local\Temp\gh_cdc1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_cdc1.s
Source File: OscFiles.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile fs/OscFiles.c

                      10 ;		-o fs/OscFiles.o

                      11 ;Source File:   fs/OscFiles.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:55:53 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "OscFiles.h"


                      21 ;2: #include "OscDescr.h"


                      22 ;3: #include "../pwin_access.h"


                      23 ;4: #include <string.h>


                      24 ;5: #include <stdlib.h>


                      25 ;6: #include "../tlsf/tlsf.h"


                      26 ;7: #include "OscConverter.h"


                      27 ;8: #include "platform_critical_section.h"


                      28 ;9: #define OSC_POOL_SIZE	(8 * 1024 * 1024)


                      29 ;10: //! пул для конвертилки осцилограммы


                      30 ;11: tlsf_t osc_tlsf;


                      31 ;12: //! для malloc,free


                      32 ;13: #pragma alignvar (4)


                      33 ;14: CriticalSection tlsfCS;


                      34 ;15: 


                      35 ;16: bool OSCFS_init(void)


                      36 ;17: {


                      37 ;18: 	void *osc_pool = malloc(OSC_POOL_SIZE);


                      38 ;19: 	


                      39 ;20: 	if (!osc_pool)


                      40 ;21: 	{


                      41 ;22: 		return FALSE;


                      42 ;23: 	}


                      43 ;24: 


                      44 ;25: 	osc_tlsf = tlsf_create_with_pool(osc_pool, OSC_POOL_SIZE);


                      45 ;26: 	if (!osc_tlsf)


                      46 ;27: 	{


                      47 ;28: 		return FALSE;


                      48 ;29: 	}


                      49 ;30: 


                      50 ;31: 	CriticalSection_Init(&tlsfCS);



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cdc1.s
                      51 ;32: 	


                      52 ;33: 


                      53 ;34: 	if (!OscConverter_init())


                      54 ;35: 	{


                      55 ;36: 		return FALSE;


                      56 ;37: 	}


                      57 ;38: 	// инициализация описаний


                      58 ;39: 	if (!OSCDescr_init())


                      59 ;40: 	{


                      60 ;41: 		return FALSE;


                      61 ;42: 	}


                      62 ;43: 	// инициализация заголовка


                      63 ;44: 	if (!OSCInfo_init())


                      64 ;45: 	{


                      65 ;46: 		return FALSE;


                      66 ;47: 	}


                      67 ;48: 


                      68 ;49: 	return TRUE;


                      69 ;50: }


                      70 ;51: 


                      71 ;52: void * OscFiles_malloc(size_t size)


                      72 ;53: {


                      73 ;54: 	void *result;


                      74 ;55: 	CriticalSection_Lock(&tlsfCS);


                      75 ;56: 	result = tlsf_malloc(osc_tlsf, size);


                      76 ;57: 	//result = malloc(size);


                      77 ;58: 	CriticalSection_Unlock(&tlsfCS);


                      78 ;59: 	return result;


                      79 ;60: }


                      80 ;61: void * OscFiles_realloc(void *p, size_t size)


                      81 ;62: {


                      82 ;63: 	void *result;


                      83 ;64: 	CriticalSection_Lock(&tlsfCS);


                      84 ;65: 	result = tlsf_realloc(osc_tlsf, p, size);


                      85 ;66: 	//result = realloc(p, size);


                      86 ;67: 	CriticalSection_Unlock(&tlsfCS);


                      87 ;68: 


                      88 ;69: 	return result;


                      89 ;70: }


                      90 ;71: 


                      91 ;72: void OscFiles_free(void *p)


                      92 ;73: {


                      93 ;74: 	CriticalSection_Lock(&tlsfCS);


                      94 ;75: 	tlsf_free(osc_tlsf, p);


                      95 ;76: 	//free(p);


                      96 ;77: 	CriticalSection_Unlock(&tlsfCS);


                      97 ;78: }


                      98 ;79: 


                      99 ;80: static FNameErrCode fillFindData(FSFindData* findData, BufferView* fnameBuf,


                     100 ;81: 	int findResult, PWFileInfo* fileInfo)


                     101 ;82: {


                     102 ;83: 	//Сюда запишем номер осциллограммы в текстовом виде


                     103 ;84:     char* pNum;


                     104 ;85: 	if (findResult == 0)


                     105 ;86: 	{


                     106 ;87: 		//Больше нет осциллограмм


                     107 ;88: 		return FNAME_NOT_FOUND;


                     108 ;89: 	}


                     109 ;90: 	else if (findResult < 0)


                     110 ;91: 	{


                     111 ;92: 		//Ошибка



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cdc1.s
                     112 ;93: 		return FNAME_ERROR;


                     113 ;94: 	}


                     114 ;95: 


                     115 ;96: 	findData->fileIndex = findResult;


                     116 ;97: 	findData->attr.fileSize = 0;


                     117 ;98: 	findData->attr.time = fileInfo->t;


                     118 ;99: 	findData->attr.ms = fileInfo->ms;


                     119 ;100: 


                     120 ;101: 	//Пишем имя


                     121 ;102: 	if (!BufferView_writeStr(fnameBuf, "osc"))


                     122 ;103: 	{


                     123 ;104: 		return FNAME_BUF_ERROR;


                     124 ;105: 	}


                     125 ;106:     if (!BufferView_alloc(fnameBuf, 11, (uint8_t**)&pNum))


                     126 ;107: 	{


                     127 ;108: 		return FNAME_BUF_ERROR;


                     128 ;109: 	}


                     129 ;110: 	_itoa(fileInfo->name, pNum, 10);


                     130 ;111: 	fnameBuf->pos += strlen(pNum);


                     131 ;112: 	if (!BufferView_writeStr(fnameBuf, ".zip"))


                     132 ;113: 	{


                     133 ;114: 		return FNAME_BUF_ERROR;


                     134 ;115: 	}


                     135 ;116: 	return FNAME_OK;


                     136 ;117: }


                     137 ;118: 


                     138 ;119: FNameErrCode OSCFS_findFirst(StringView* startFileName, FSFindData* findData,


                     139 ;120: 	BufferView* fnameBuf)


                     140 ;121: {


                     141 ;122: 	PWFileInfo fileInfo;


                     142 ;123: 	int findResult;	


                     143 ;124: 	int oscNum = 0;


                     144 ;125: 	if (startFileName != NULL) {


                     145 ;126: 		//Пропускаем "osc", а все оставшиеся цифры интерпертируем как номер


                     146 ;127:         oscNum = atoi((char*)startFileName->p + 3);


                     147 ;128: 		if (oscNum < 1)


                     148 ;129: 		{


                     149 ;130: 			return FNAME_NOT_FOUND;


                     150 ;131: 		}


                     151 ;132: 	}


                     152 ;133: 


                     153 ;134: 	findResult = pwaOscFindFirst(oscNum, &fileInfo);		


                     154 ;135: 	return fillFindData(findData, fnameBuf, findResult, &fileInfo);


                     155 ;136: }


                     156 ;137: 


                     157 ;138: FNameErrCode OSCFS_findNext(FSFindData* findData, BufferView* fnameBuf)


                     158 ;139: {


                     159 ;140: 	PWFileInfo fileInfo;


                     160 ;141: 	int findResult = pwaOscFindNext(findData->fileIndex, &fileInfo);		


                     161 ;142: 	return fillFindData(findData, fnameBuf, findResult, &fileInfo);


                     162 ;143: }


                     163 ;144: 


                     164 ;145: void OSCFS_findClose(FSFindData *findData)


                     165 ;146: {


                     166 ;147: 	pwaOscFindClose();


                     167 ;148: }


                     168 ;149: 


                     169 ;150: //! читает осцилограмму в OscWriteBuffer согласно его размеру


                     170 ;151: static int readOsc(FRSM *frsm, int offset, OscWriteBuffer *wb)


                     171 ;152: {


                     172 ;153: 	PWFileInfo *fileInfo;



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cdc1.s
                     173 ;154: 	int result;


                     174 ;155: 	unsigned char *pData;


                     175 ;156: 	int readSize;


                     176 ;157: 


                     177 ;158: 	fileInfo = &frsm->readOscContext->fileInfo;


                     178 ;159: 


                     179 ;160: 	OscWriteBuffer_reset(wb);


                     180 ;161: 	pData = OscWriteBuffer_data(wb);


                     181 ;162: 	readSize = OscWriteBuffer_size(wb);


                     182 ;163: 


                     183 ;164: 	// читается сколько влезет в буфер


                     184 ;165: 	result = pwaOscOscRead(fileInfo, offset, pData, readSize);


                     185 ;166: 	if (result > 0)


                     186 ;167: 	{


                     187 ;168: 		// успешно прочитали, новый размер


                     188 ;169: 		if (!OscWriteBuffer_resize(wb, result))


                     189 ;170: 		{


                     190 ;171: 			return -1;


                     191 ;172: 		}


                     192 ;173: 	}


                     193 ;174: 


                     194 ;175: 	return result;


                     195 ;176: }


                     196 ;177: 


                     197 ;178: //! из имени файла в номер осцилограмма


                     198 ;179: static int fileNameToOscNum(StringView* fileName)


                     199 

                     200 ;184: }


                     201 

                     202 ;185: 


                     203 ;186: //! создает контекст чтения осцилограммы


                     204 ;187: static OscReadFileContext *createOscReadContext(StringView* fileName)


                     205 

                     206 ;207: }


                     207 

                     208 	.text

                     209 	.align	4

                     210 OSCFS_init::

00000000 e92d4000    211 	stmfd	[sp]!,{lr}

00000004 e3a00880    212 	mov	r0,1<<23

00000008 eb000000*   213 	bl	malloc

0000000c e3500000    214 	cmp	r0,0

00000010 0a000010    215 	beq	.L72

00000014 e3a01880    216 	mov	r1,1<<23

00000018 eb000000*   217 	bl	tlsf_create_with_pool

0000001c e59f11e8*   218 	ldr	r1,.L180

00000020 e3500000    219 	cmp	r0,0

00000024 e5810000    220 	str	r0,[r1]

00000028 0a00000a    221 	beq	.L72

0000002c e59f01dc*   222 	ldr	r0,.L181

00000030 eb000000*   223 	bl	CriticalSection_Init

00000034 eb000000*   224 	bl	OscConverter_init

00000038 e3500000    225 	cmp	r0,0

0000003c 0a000005    226 	beq	.L72

00000040 eb000000*   227 	bl	OSCDescr_init

00000044 e3500000    228 	cmp	r0,0

00000048 0a000002    229 	beq	.L72

0000004c eb000000*   230 	bl	OSCInfo_init

00000050 e3500000    231 	cmp	r0,0

00000054 13a00001    232 	movne	r0,1

                     233 .L72:


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cdc1.s
00000058 03a00000    234 	moveq	r0,0

                     235 .L57:

0000005c e8bd8000    236 	ldmfd	[sp]!,{pc}

                     237 	.endf	OSCFS_init

                     238 	.align	4

                     239 ;osc_pool	r1	local

                     240 

                     241 	.section ".bss","awb"

                     242 .L154:

                     243 	.data

                     244 	.text

                     245 

                     246 

                     247 	.align	4

                     248 	.align	4

                     249 OscFiles_malloc::

00000060 e92d4010    250 	stmfd	[sp]!,{r4,lr}

00000064 e1a04000    251 	mov	r4,r0

00000068 e59f01a0*   252 	ldr	r0,.L181

0000006c eb000000*   253 	bl	CriticalSection_Lock

00000070 e1a01004    254 	mov	r1,r4

00000074 e59f4190*   255 	ldr	r4,.L180

00000078 e5940000    256 	ldr	r0,[r4]

0000007c eb000000*   257 	bl	tlsf_malloc

00000080 e1a04000    258 	mov	r4,r0

00000084 e59f0184*   259 	ldr	r0,.L181

00000088 eb000000*   260 	bl	CriticalSection_Unlock

0000008c e1a00004    261 	mov	r0,r4

00000090 e8bd8010    262 	ldmfd	[sp]!,{r4,pc}

                     263 	.endf	OscFiles_malloc

                     264 	.align	4

                     265 

                     266 ;size	r4	param

                     267 

                     268 	.section ".bss","awb"

                     269 .L206:

                     270 	.data

                     271 	.text

                     272 

                     273 

                     274 	.align	4

                     275 	.align	4

                     276 OscFiles_realloc::

00000094 e92d4030    277 	stmfd	[sp]!,{r4-r5,lr}

00000098 e1a04000    278 	mov	r4,r0

0000009c e59f016c*   279 	ldr	r0,.L181

000000a0 e1a05001    280 	mov	r5,r1

000000a4 eb000000*   281 	bl	CriticalSection_Lock

000000a8 e1a01004    282 	mov	r1,r4

000000ac e59f4158*   283 	ldr	r4,.L180

000000b0 e5940000    284 	ldr	r0,[r4]

000000b4 e1a02005    285 	mov	r2,r5

000000b8 eb000000*   286 	bl	tlsf_realloc

000000bc e1a04000    287 	mov	r4,r0

000000c0 e59f0148*   288 	ldr	r0,.L181

000000c4 eb000000*   289 	bl	CriticalSection_Unlock

000000c8 e1a00004    290 	mov	r0,r4

000000cc e8bd8030    291 	ldmfd	[sp]!,{r4-r5,pc}

                     292 	.endf	OscFiles_realloc

                     293 	.align	4

                     294 


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cdc1.s
                     295 ;p	r4	param

                     296 ;size	r5	param

                     297 

                     298 	.section ".bss","awb"

                     299 .L238:

                     300 	.data

                     301 	.text

                     302 

                     303 

                     304 	.align	4

                     305 	.align	4

                     306 OscFiles_free::

000000d0 e92d4010    307 	stmfd	[sp]!,{r4,lr}

000000d4 e1a04000    308 	mov	r4,r0

000000d8 e59f0130*   309 	ldr	r0,.L181

000000dc eb000000*   310 	bl	CriticalSection_Lock

000000e0 e1a01004    311 	mov	r1,r4

000000e4 e59f4120*   312 	ldr	r4,.L180

000000e8 e5940000    313 	ldr	r0,[r4]

000000ec eb000000*   314 	bl	tlsf_free

000000f0 e59f0118*   315 	ldr	r0,.L181

000000f4 e8bd4010    316 	ldmfd	[sp]!,{r4,lr}

000000f8 ea000000*   317 	b	CriticalSection_Unlock

                     318 	.endf	OscFiles_free

                     319 	.align	4

                     320 

                     321 ;p	r4	param

                     322 

                     323 	.section ".bss","awb"

                     324 .L270:

                     325 	.data

                     326 	.text

                     327 

                     328 

                     329 	.align	4

                     330 	.align	4

                     331 fillFindData:

000000fc e92d4030    332 	stmfd	[sp]!,{r4-r5,lr}

00000100 e24dd004    333 	sub	sp,sp,4

00000104 e3520000    334 	cmp	r2,0

00000108 03a00001    335 	moveq	r0,1

0000010c 0a000025    336 	beq	.L277

00000110 e1a04001    337 	mov	r4,r1

00000114 e1a05003    338 	mov	r5,r3

00000118 e3520000    339 	cmp	r2,0

0000011c b3a00003    340 	movlt	r0,3

00000120 ba000020    341 	blt	.L277

00000124 e5802004    342 	str	r2,[r0,4]

00000128 e3a01000    343 	mov	r1,0

0000012c e5801014    344 	str	r1,[r0,20]

00000130 e5951008    345 	ldr	r1,[r5,8]

00000134 e5801018    346 	str	r1,[r0,24]

00000138 e5951010    347 	ldr	r1,[r5,16]

0000013c e580101c    348 	str	r1,[r0,28]

00000140 e28f1000*   349 	adr	r1,.L427

00000144 e1a00004    350 	mov	r0,r4

00000148 eb000000*   351 	bl	BufferView_writeStr

0000014c e3500000    352 	cmp	r0,0

00000150 0a000013    353 	beq	.L293

00000154 e1a0200d    354 	mov	r2,sp

00000158 e1a00004    355 	mov	r0,r4


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cdc1.s
0000015c e3a0100b    356 	mov	r1,11

00000160 eb000000*   357 	bl	BufferView_alloc

00000164 e3500000    358 	cmp	r0,0

00000168 0a00000d    359 	beq	.L293

0000016c e59d1000    360 	ldr	r1,[sp]

00000170 e5950000    361 	ldr	r0,[r5]

00000174 e3a0200a    362 	mov	r2,10

00000178 eb000000*   363 	bl	_itoa

0000017c e59d0000    364 	ldr	r0,[sp]

00000180 eb000000*   365 	bl	strlen

00000184 e5941004    366 	ldr	r1,[r4,4]

00000188 e0811000    367 	add	r1,r1,r0

0000018c e5841004    368 	str	r1,[r4,4]

00000190 e28f1000*   369 	adr	r1,.L428

00000194 e1a00004    370 	mov	r0,r4

00000198 eb000000*   371 	bl	BufferView_writeStr

0000019c e3500000    372 	cmp	r0,0

000001a0 13a00000    373 	movne	r0,0

                     374 .L293:

000001a4 03a00002    375 	moveq	r0,2

                     376 .L277:

000001a8 e28dd004    377 	add	sp,sp,4

000001ac e8bd4030    378 	ldmfd	[sp]!,{r4-r5,lr}

000001b0 e12fff1e*   379 	ret	

                     380 	.endf	fillFindData

                     381 	.align	4

                     382 ;pNum	[sp]	local

                     383 ;.L394	.L398	static

                     384 ;.L395	.L399	static

                     385 

                     386 ;findData	r0	param

                     387 ;fnameBuf	r4	param

                     388 ;findResult	r2	param

                     389 ;fileInfo	r5	param

                     390 

                     391 	.section ".bss","awb"

                     392 .L393:

                     393 	.data

                     394 	.text

                     395 

                     396 

                     397 	.align	4

                     398 	.align	4

                     399 OSCFS_findFirst::

000001b4 e92d4030    400 	stmfd	[sp]!,{r4-r5,lr}

000001b8 e24dd01c    401 	sub	sp,sp,28

000001bc e1a04001    402 	mov	r4,r1

000001c0 e1a05002    403 	mov	r5,r2

000001c4 e1b03000    404 	movs	r3,r0

000001c8 e3a00000    405 	mov	r0,0

000001cc 0a000005    406 	beq	.L431

000001d0 e5930004    407 	ldr	r0,[r3,4]

000001d4 e2800003    408 	add	r0,r0,3

000001d8 eb000000*   409 	bl	atoi

000001dc e3500000    410 	cmp	r0,0

000001e0 d3a00001    411 	movle	r0,1

000001e4 da000006    412 	ble	.L429

                     413 .L431:

000001e8 e1a0100d    414 	mov	r1,sp

000001ec eb000000*   415 	bl	pwaOscFindFirst

000001f0 e1a0300d    416 	mov	r3,sp


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cdc1.s
000001f4 e1a01005    417 	mov	r1,r5

000001f8 e1a02000    418 	mov	r2,r0

000001fc e1a00004    419 	mov	r0,r4

00000200 ebffffbd*   420 	bl	fillFindData

                     421 .L429:

00000204 e28dd01c    422 	add	sp,sp,28

00000208 e8bd8030    423 	ldmfd	[sp]!,{r4-r5,pc}

                     424 	.endf	OSCFS_findFirst

                     425 	.align	4

                     426 .L180:

0000020c 00000000*   427 	.data.w	osc_tlsf

                     428 	.type	.L180,$object

                     429 	.size	.L180,4

                     430 

                     431 .L181:

00000210 00000000*   432 	.data.w	tlsfCS

                     433 	.type	.L181,$object

                     434 	.size	.L181,4

                     435 

                     436 .L427:

                     437 ;	"osc\000"

00000214 0063736f    438 	.data.b	111,115,99,0

                     439 	.align 4

                     440 

                     441 	.type	.L427,$object

                     442 	.size	.L427,4

                     443 

                     444 .L428:

                     445 ;	".zip\000"

00000218 70697a2e    446 	.data.b	46,122,105,112

0000021c 00         447 	.data.b	0

0000021d 000000     448 	.align 4

                     449 

                     450 	.type	.L428,$object

                     451 	.size	.L428,4

                     452 

                     453 	.align	4

                     454 ;fileInfo	[sp]	local

                     455 ;oscNum	r0	local

                     456 

                     457 ;startFileName	r3	param

                     458 ;findData	r4	param

                     459 ;fnameBuf	r5	param

                     460 

                     461 	.section ".bss","awb"

                     462 .L484:

                     463 	.data

                     464 	.text

                     465 

                     466 

                     467 	.align	4

                     468 	.align	4

                     469 OSCFS_findNext::

00000220 e92d4030    470 	stmfd	[sp]!,{r4-r5,lr}

00000224 e1a05001    471 	mov	r5,r1

00000228 e24dd01c    472 	sub	sp,sp,28

0000022c e1a04000    473 	mov	r4,r0

00000230 e5940004    474 	ldr	r0,[r4,4]

00000234 e1a0100d    475 	mov	r1,sp

00000238 eb000000*   476 	bl	pwaOscFindNext

0000023c e1a0300d    477 	mov	r3,sp


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cdc1.s
00000240 e1a01005    478 	mov	r1,r5

00000244 e1a02000    479 	mov	r2,r0

00000248 e1a00004    480 	mov	r0,r4

0000024c ebffffaa*   481 	bl	fillFindData

00000250 e28dd01c    482 	add	sp,sp,28

00000254 e8bd8030    483 	ldmfd	[sp]!,{r4-r5,pc}

                     484 	.endf	OSCFS_findNext

                     485 	.align	4

                     486 ;fileInfo	[sp]	local

                     487 

                     488 ;findData	r4	param

                     489 ;fnameBuf	r5	param

                     490 

                     491 	.section ".bss","awb"

                     492 .L526:

                     493 	.data

                     494 	.text

                     495 

                     496 

                     497 	.align	4

                     498 	.align	4

                     499 OSCFS_findClose::

00000258 ea000000*   500 	b	pwaOscFindClose

                     501 	.endf	OSCFS_findClose

                     502 	.align	4

                     503 

                     504 ;findData	none	param

                     505 

                     506 	.section ".bss","awb"

                     507 .L558:

                     508 	.data

                     509 	.text

                     510 

                     511 

                     512 	.align	4

                     513 	.align	4

                     514 readOsc:

0000025c e92d40f0    515 	stmfd	[sp]!,{r4-r7,lr}

00000260 e1a07001    516 	mov	r7,r1

00000264 e1a05002    517 	mov	r5,r2

00000268 e5904018    518 	ldr	r4,[r0,24]

0000026c e1a00005    519 	mov	r0,r5

00000270 eb000000*   520 	bl	OscWriteBuffer_reset

00000274 e1a00005    521 	mov	r0,r5

00000278 eb000000*   522 	bl	OscWriteBuffer_data

0000027c e1a06000    523 	mov	r6,r0

00000280 e1a00005    524 	mov	r0,r5

00000284 eb000000*   525 	bl	OscWriteBuffer_size

00000288 e1a02006    526 	mov	r2,r6

0000028c e1a01007    527 	mov	r1,r7

00000290 e1a03000    528 	mov	r3,r0

00000294 e1a00004    529 	mov	r0,r4

00000298 eb000000*   530 	bl	pwaOscOscRead

0000029c e2504000    531 	subs	r4,r0,0

000002a0 da000005    532 	ble	.L567

000002a4 e1a01004    533 	mov	r1,r4

000002a8 e1a00005    534 	mov	r0,r5

000002ac eb000000*   535 	bl	OscWriteBuffer_resize

000002b0 e3500000    536 	cmp	r0,0

000002b4 03e00000    537 	mvneq	r0,0

000002b8 0a000000    538 	beq	.L565


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cdc1.s
                     539 .L567:

000002bc e1a00004    540 	mov	r0,r4

                     541 .L565:

000002c0 e8bd40f0    542 	ldmfd	[sp]!,{r4-r7,lr}

000002c4 e12fff1e*   543 	ret	

                     544 	.endf	readOsc

                     545 	.align	4

                     546 ;fileInfo	r4	local

                     547 ;result	r4	local

                     548 ;pData	r6	local

                     549 

                     550 ;frsm	r0	param

                     551 ;offset	r7	param

                     552 ;wb	r5	param

                     553 

                     554 	.section ".bss","awb"

                     555 .L612:

                     556 	.data

                     557 	.text

                     558 

                     559 

                     560 ;208: 


                     561 ;209: //! освобождает контекст чтения, всегда возвращает FALSE


                     562 ;210: static bool freeOscReadContext(FRSM* frsm)


                     563 	.align	4

                     564 	.align	4

                     565 freeOscReadContext:

000002c8 e92d4030    566 	stmfd	[sp]!,{r4-r5,lr}

000002cc e1a05000    567 	mov	r5,r0

                     568 ;211: {


                     569 

                     570 ;212: 	OscReadFileContext *readFileContext = frsm->readOscContext;


                     571 

000002d0 e5950018    572 	ldr	r0,[r5,24]

000002d4 e1a04000    573 	mov	r4,r0

                     574 ;213: 	// т.к. oscInfo создается createOscReadContext, освобождаем здесь


                     575 ;214: 	if (readFileContext->oscInfo)


                     576 

000002d8 e594102c    577 	ldr	r1,[r4,44]

000002dc e3510000    578 	cmp	r1,0

000002e0 0a000004    579 	beq	.L631

                     580 ;215: 	{


                     581 

                     582 ;216: 		OSCInfo_destroy(readFileContext->oscInfo);


                     583 

000002e4 e1a00001    584 	mov	r0,r1

000002e8 eb000000*   585 	bl	OSCInfo_destroy

                     586 ;217: 		readFileContext->oscInfo = NULL;


                     587 

000002ec e3a00000    588 	mov	r0,0

000002f0 e584002c    589 	str	r0,[r4,44]

000002f4 e5950018    590 	ldr	r0,[r5,24]

                     591 .L631:

                     592 ;218: 	}


                     593 ;219: 


                     594 ;220: 	if (frsm->readOscContext)


                     595 

000002f8 e3500000    596 	cmp	r0,0

                     597 ;221: 	{


                     598 

                     599 ;222: 		OscReadFileContext_destroy(frsm->readOscContext);



                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cdc1.s
                     600 

000002fc 1b000000*   601 	blne	OscReadFileContext_destroy

                     602 ;223: 	}


                     603 ;224: 


                     604 ;225: 	return FALSE;


                     605 

00000300 e3a00000    606 	mov	r0,0

00000304 e8bd4030    607 	ldmfd	[sp]!,{r4-r5,lr}

00000308 e12fff1e*   608 	ret	

                     609 	.endf	freeOscReadContext

                     610 	.align	4

                     611 ;readFileContext	r4	local

                     612 

                     613 ;frsm	r5	param

                     614 

                     615 	.section ".bss","awb"

                     616 .L681:

                     617 	.data

                     618 	.text

                     619 

                     620 ;226: }


                     621 

                     622 ;227: bool OSCFS_openFile(StringView* fileName, FRSM* frsm, FSFileAttr* attr)


                     623 	.align	4

                     624 	.align	4

                     625 OSCFS_openFile::

0000030c e92d40f0    626 	stmfd	[sp]!,{r4-r7,lr}

                     627 ;228: {


                     628 

                     629 ;229: 	int readSize;


                     630 ;230: 	OscWriteBuffer *headerBuf;


                     631 ;231: 	OscWriteBuffer *contentBuf;


                     632 ;232: 	OSCInfoStruct *oscInfo;


                     633 ;233: 


                     634 ;234: 	// контекст чтения


                     635 ;235: 	frsm->readOscContext = createOscReadContext(fileName);


                     636 

                     637 ;188: {


                     638 

                     639 ;189: 	int findResult;


                     640 ;190: 	PWFileInfo fileInfo;


                     641 ;191: 


                     642 ;192: 	int oscNum = fileNameToOscNum(fileName);


                     643 

                     644 ;180: {


                     645 

                     646 ;181: 	// приходит что-то вроде osc10.zip


                     647 ;182: 	// срезается osc, а .zip не конвертится


                     648 ;183: 	return atoi((char*)fileName->p + 3);


                     649 

00000310 e1a04001    650 	mov	r4,r1

00000314 e24dd01c    651 	sub	sp,sp,28

00000318 e5900004    652 	ldr	r0,[r0,4]

0000031c e1a05002    653 	mov	r5,r2

00000320 e2800003    654 	add	r0,r0,3

00000324 eb000000*   655 	bl	atoi

                     656 ;193: 	if (oscNum < 1)


                     657 

00000328 e3500000    658 	cmp	r0,0

0000032c da000004    659 	ble	.L707

                     660 ;194: 	{



                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cdc1.s
                     661 

                     662 ;195: 		return NULL;


                     663 

                     664 ;196: 	}


                     665 ;197: 


                     666 ;198: 	// ищем осцилограмму с соответствущим именем


                     667 ;199: 	findResult = pwaOscFindFirst(oscNum, &fileInfo);


                     668 

00000330 e1a0100d    669 	mov	r1,sp

00000334 eb000000*   670 	bl	pwaOscFindFirst

                     671 ;200: 	if (findResult <= 0)


                     672 

00000338 e1a06000    673 	mov	r6,r0

0000033c eb000000*   674 	bl	pwaOscFindClose

00000340 e3560000    675 	cmp	r6,0

                     676 .L707:

                     677 ;201: 	{


                     678 

                     679 ;202: 		pwaOscFindClose();


                     680 

                     681 ;203: 		return NULL;


                     682 

00000344 d3a00000    683 	movle	r0,0

00000348 d5840018    684 	strle	r0,[r4,24]

                     685 ;236: 	if (!frsm->readOscContext)


                     686 

0000034c da000004    687 	ble	.L711

                     688 .L708:

                     689 ;204: 	}


                     690 ;205: 	pwaOscFindClose();


                     691 

                     692 ;206: 	return OscReadFileContext_create(&fileInfo);


                     693 

00000350 e1a0000d    694 	mov	r0,sp

00000354 eb000000*   695 	bl	OscReadFileContext_create

00000358 e5840018    696 	str	r0,[r4,24]

                     697 ;236: 	if (!frsm->readOscContext)


                     698 

0000035c e3500000    699 	cmp	r0,0

00000360 1a000001    700 	bne	.L710

                     701 .L711:

                     702 ;237: 	{


                     703 

                     704 ;238: 		return FALSE;


                     705 

00000364 e20000ff    706 	and	r0,r0,255

00000368 ea00002d    707 	b	.L700

                     708 .L710:

                     709 ;239: 	}


                     710 ;240: 	


                     711 ;241: 	// чтение заголовка об осcилограмме


                     712 ;242: 	// указатель на временный буфер


                     713 ;243: 	headerBuf = OSCInfo_lockHeaderBuf();


                     714 

0000036c eb000000*   715 	bl	OSCInfo_lockHeaderBuf

00000370 e1a06000    716 	mov	r6,r0

                     717 ;244: 	readSize = readOsc(frsm, 0, headerBuf);


                     718 

00000374 e1a02006    719 	mov	r2,r6

00000378 e1a00004    720 	mov	r0,r4

0000037c e3a01000    721 	mov	r1,0


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cdc1.s
00000380 ebffffb5*   722 	bl	readOsc

                     723 ;245: 	if ( readSize <= 0)


                     724 

00000384 e3500000    725 	cmp	r0,0

00000388 ca000003    726 	bgt	.L713

                     727 ;246: 	{


                     728 

                     729 ;247: 		OSCInfo_unlockHeaderBuf();


                     730 

0000038c eb000000*   731 	bl	OSCInfo_unlockHeaderBuf

                     732 ;248: 		return freeOscReadContext(frsm);


                     733 

00000390 e1a00004    734 	mov	r0,r4

00000394 ebffffcb*   735 	bl	freeOscReadContext

00000398 ea000021    736 	b	.L700

                     737 .L713:

                     738 ;249: 	}


                     739 ;250: 


                     740 ;251: 	// инициализация заголовка осцилограммы


                     741 ;252: 	oscInfo = OSCInfo_create(headerBuf);


                     742 

0000039c e1a00006    743 	mov	r0,r6

000003a0 eb000000*   744 	bl	OSCInfo_create

000003a4 e1a06000    745 	mov	r6,r0

                     746 ;253: 	OSCInfo_unlockHeaderBuf();


                     747 

000003a8 eb000000*   748 	bl	OSCInfo_unlockHeaderBuf

                     749 ;254: 	if (!oscInfo)


                     750 

000003ac e3560000    751 	cmp	r6,0

000003b0 0a000010    752 	beq	.L723

                     753 ;255: 	{


                     754 

                     755 ;256: 		return freeOscReadContext(frsm);


                     756 

                     757 ;257: 	}


                     758 ;258: 	frsm->readOscContext->oscInfo = oscInfo;


                     759 

000003b4 e5940018    760 	ldr	r0,[r4,24]

000003b8 e580602c    761 	str	r6,[r0,44]

                     762 ;259: 


                     763 ;260: 	


                     764 ;261: 	// инициализация состава


                     765 ;262: 	// буфер под состав


                     766 ;263: 	contentBuf = OSCInfo_getBufferContent(oscInfo);


                     767 

000003bc e1a00006    768 	mov	r0,r6

000003c0 eb000000*   769 	bl	OSCInfo_getBufferContent

000003c4 e1a07000    770 	mov	r7,r0

                     771 ;264: 	readSize = readOsc(frsm, OSCInfo_getHeaderSize(oscInfo), contentBuf);


                     772 

000003c8 e1a00006    773 	mov	r0,r6

000003cc eb000000*   774 	bl	OSCInfo_getHeaderSize

000003d0 e1a02007    775 	mov	r2,r7

000003d4 e1a01000    776 	mov	r1,r0

000003d8 e1a00004    777 	mov	r0,r4

000003dc ebffff9e*   778 	bl	readOsc

                     779 ;265: 	if ( readSize <= 0)


                     780 

000003e0 e3500000    781 	cmp	r0,0

000003e4 da000003    782 	ble	.L723


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cdc1.s
                     783 ;266: 	{


                     784 

                     785 ;267: 		return freeOscReadContext(frsm);


                     786 

                     787 ;268: 	}


                     788 ;269: 


                     789 ;270: 	// состав осцилограммы


                     790 ;271: 	// oscInfo содержит указатель на contentBuf, поэтому он не передается


                     791 ;272: 	if (!OSCInfo_initContent(oscInfo))


                     792 

000003e8 e1a00006    793 	mov	r0,r6

000003ec eb000000*   794 	bl	OSCInfo_initContent

000003f0 e3500000    795 	cmp	r0,0

000003f4 1a000002    796 	bne	.L722

                     797 .L723:

                     798 ;273: 	{


                     799 

                     800 ;274: 		return freeOscReadContext(frsm);


                     801 

000003f8 e1a00004    802 	mov	r0,r4

000003fc ebffffb1*   803 	bl	freeOscReadContext

00000400 ea000007    804 	b	.L700

                     805 .L722:

                     806 ;275: 	}


                     807 ;276: 	attr->fileSize = 0;


                     808 

00000404 e3a00000    809 	mov	r0,0

00000408 e5850000    810 	str	r0,[r5]

                     811 ;277: 	attr->time = frsm->readOscContext->fileInfo.t;


                     812 

0000040c e5940018    813 	ldr	r0,[r4,24]

00000410 e5901008    814 	ldr	r1,[r0,8]

00000414 e5851004    815 	str	r1,[r5,4]

                     816 ;278: 	attr->ms = frsm->readOscContext->fileInfo.ms;


                     817 

00000418 e5900010    818 	ldr	r0,[r0,16]

0000041c e5850008    819 	str	r0,[r5,8]

                     820 ;279: 	return TRUE;


                     821 

00000420 e3a00001    822 	mov	r0,1

                     823 .L700:

00000424 e28dd01c    824 	add	sp,sp,28

00000428 e8bd80f0    825 	ldmfd	[sp]!,{r4-r7,pc}

                     826 	.endf	OSCFS_openFile

                     827 	.align	4

                     828 ;headerBuf	r6	local

                     829 ;contentBuf	r7	local

                     830 ;oscInfo	r6	local

                     831 ;fileInfo	[sp]	local

                     832 ;oscNum	r1	local

                     833 

                     834 ;fileName	r0	param

                     835 ;frsm	r4	param

                     836 ;attr	r5	param

                     837 

                     838 	.section ".bss","awb"

                     839 .L905:

                     840 	.data

                     841 	.text

                     842 

                     843 ;280: }



                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cdc1.s
                     844 

                     845 ;281: 


                     846 ;282: bool OSCFS_closeFile(FRSM* frsm)


                     847 	.align	4

                     848 	.align	4

                     849 OSCFS_closeFile::

0000042c e92d4000    850 	stmfd	[sp]!,{lr}

                     851 ;283: {


                     852 

                     853 ;284: 	freeOscReadContext(frsm);


                     854 

00000430 ebffffa4*   855 	bl	freeOscReadContext

                     856 ;285: 	return TRUE;


                     857 

00000434 e3a00001    858 	mov	r0,1

00000438 e8bd8000    859 	ldmfd	[sp]!,{pc}

                     860 	.endf	OSCFS_closeFile

                     861 	.align	4

                     862 

                     863 ;frsm	none	param

                     864 

                     865 	.section ".bss","awb"

                     866 .L958:

                     867 	.data

                     868 	.text

                     869 

                     870 ;286: }


                     871 

                     872 ;287: 


                     873 ;288: bool OSCFS_readFile(FRSM* frsm, BufferView* readBuf, bool* moreFollows)


                     874 	.align	4

                     875 	.align	4

                     876 OSCFS_readFile::

0000043c e92d4ff0    877 	stmfd	[sp]!,{r4-fp,lr}

                     878 ;289: {


                     879 

                     880 ;290: 	OscReadFileContext *readFileContext = frsm->readOscContext;


                     881 

00000440 e1a07001    882 	mov	r7,r1

00000444 e24dd008    883 	sub	sp,sp,8

00000448 e1a0b000    884 	mov	fp,r0

0000044c e59b0018    885 	ldr	r0,[fp,24]

00000450 e1a06002    886 	mov	r6,r2

00000454 e1b04000    887 	movs	r4,r0

                     888 ;291: 	OSCInfoStruct *oscInfo;


                     889 ;292: 	OscWriteBuffer *oscPeriodBuf;


                     890 ;293: 	int readSize;


                     891 ;294: 	size_t frameOffset;


                     892 ;295: 	unsigned int frameNum;


                     893 ;296: 	OscWriteBuffer *streamBuffer = &readFileContext->streamBuffer;


                     894 

00000458 1590502c    895 	ldrne	r5,[r0,44]

                     896 ;304: 	if (!oscInfo)


                     897 

0000045c e2848e4a    898 	add	r8,r4,0x04a0

                     899 ;297: 


                     900 ;298: 	if (!readFileContext)


                     901 

                     902 ;299: 	{


                     903 

                     904 ;300: 		return FALSE;



                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cdc1.s
                     905 

                     906 ;301: 	}


                     907 ;302: 


                     908 ;303: 	oscInfo = frsm->readOscContext->oscInfo;


                     909 

00000460 13550000    910 	cmpne	r5,0

00000464 0a000064    911 	beq	.L1012

                     912 ;305: 	{


                     913 

                     914 ;306: 		return FALSE;


                     915 

                     916 ;307: 	}


                     917 ;308: 


                     918 ;309: 	oscPeriodBuf = OSCInfo_getFrameBuffer(oscInfo);


                     919 

00000468 e1a00005    920 	mov	r0,r5

0000046c eb000000*   921 	bl	OSCInfo_getFrameBuffer

00000470 e1a09000    922 	mov	r9,r0

                     923 ;310: 	if (!oscPeriodBuf)


                     924 

00000474 e3500000    925 	cmp	r0,0

00000478 0a00005f    926 	beq	.L1012

                     927 ;311: 	{


                     928 

                     929 ;312: 		return FALSE;


                     930 

                     931 ;313: 	}


                     932 ;314: 


                     933 ;315: 	// пока есть буфер с предыдущего запуска - пишем его


                     934 ;316: 	if (!OscWriteBuffer_empty(streamBuffer))


                     935 

0000047c e1a00008    936 	mov	r0,r8

00000480 eb000000*   937 	bl	OscWriteBuffer_empty

00000484 e3500000    938 	cmp	r0,0

00000488 0a00005d    939 	beq	.L1000

                     940 ;317: 	{


                     941 

                     942 ;318: 		OscWriteBuffer_toBufferView(readBuf, streamBuffer);


                     943 

                     944 ;319: 		*moreFollows = TRUE;


                     945 

                     946 ;320: 		return TRUE;


                     947 

                     948 ;321: 	}


                     949 ;322: 


                     950 ;323: 


                     951 ;324: 	frameNum = readFileContext->curFrame;


                     952 

0000048c e594a030    953 	ldr	r10,[r4,48]

                     954 ;325: 	// все фреймы обработаны, можем обрабатывать остальные файлы


                     955 ;326: 	if (frameNum >= OSCInfo_getFrameCount(oscInfo))


                     956 

00000490 e1a00005    957 	mov	r0,r5

00000494 eb000000*   958 	bl	OSCInfo_getFrameCount

00000498 e15a0000    959 	cmp	r10,r0

0000049c 3a000019    960 	blo	.L979

                     961 ;327: 	{


                     962 

                     963 ;328: 		// cfg файл


                     964 ;329: 		OscWriteBuffer *cfgBuffer = &readFileContext->cfgBuffer;


                     965 


                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cdc1.s
000004a0 e2845e45    966 	add	r5,r4,0x0450

                     967 ;330: 		// hdr file


                     968 ;331: 		OscWriteBuffer *hdrBuffer = &readFileContext->hdrBuffer;


                     969 

000004a4 e2840e40    970 	add	r0,r4,1<<10

000004a8 e2804078    971 	add	r4,r0,120

                     972 ;332: 		if (!OscWriteBuffer_empty(cfgBuffer))


                     973 

000004ac e1a00005    974 	mov	r0,r5

000004b0 eb000000*   975 	bl	OscWriteBuffer_empty

000004b4 e3500000    976 	cmp	r0,0

000004b8 1a000005    977 	bne	.L981

                     978 ;333: 		{


                     979 

                     980 ;334: 			OscWriteBuffer_toBufferView(readBuf, cfgBuffer);


                     981 

000004bc e1a01005    982 	mov	r1,r5

000004c0 e1a00007    983 	mov	r0,r7

000004c4 eb000000*   984 	bl	OscWriteBuffer_toBufferView

                     985 ;335: 			*moreFollows = TRUE;


                     986 

000004c8 e3a00001    987 	mov	r0,1

000004cc e5c60000    988 	strb	r0,[r6]

                     989 ;336: 			return TRUE;


                     990 

000004d0 ea000050    991 	b	.L965

                     992 .L981:

                     993 ;337: 		}


                     994 ;338: 


                     995 ;339: 


                     996 ;340: 		if (!OscWriteBuffer_empty(hdrBuffer))


                     997 

000004d4 e1a00004    998 	mov	r0,r4

000004d8 eb000000*   999 	bl	OscWriteBuffer_empty

000004dc e3500000   1000 	cmp	r0,0

                    1001 ;345: 		}


                    1002 ;346: 


                    1003 ;347: 		*moreFollows = FALSE;


                    1004 

000004e0 13a00000   1005 	movne	r0,0

000004e4 15c60000   1006 	strneb	r0,[r6]

                    1007 ;348: 		return TRUE;


                    1008 

000004e8 13a00001   1009 	movne	r0,1

000004ec 1a000049   1010 	bne	.L965

                    1011 ;341: 		{


                    1012 

                    1013 ;342: 			OscWriteBuffer_toBufferView(readBuf, hdrBuffer);


                    1014 

000004f0 e1a01004   1015 	mov	r1,r4

000004f4 e1a00007   1016 	mov	r0,r7

000004f8 eb000000*  1017 	bl	OscWriteBuffer_toBufferView

                    1018 ;343: 			*moreFollows = TRUE;


                    1019 

000004fc e3a00001   1020 	mov	r0,1

00000500 e5c60000   1021 	strb	r0,[r6]

                    1022 ;344: 			return TRUE;


                    1023 

00000504 ea000043   1024 	b	.L965

                    1025 .L979:

                    1026 ;349: 	}



                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cdc1.s
                    1027 ;350: 


                    1028 ;351: 	// смещение нового фрейма


                    1029 ;352: 	frameOffset = OSCInfo_getFrameOffset(oscInfo, frameNum);


                    1030 

00000508 e1a0100a   1031 	mov	r1,r10

0000050c e1a00005   1032 	mov	r0,r5

00000510 eb000000*  1033 	bl	OSCInfo_getFrameOffset

                    1034 ;353: 	// чтение фрейма


                    1035 ;354: 	readSize = readOsc(frsm, frameOffset, oscPeriodBuf);


                    1036 

00000514 e1a02009   1037 	mov	r2,r9

00000518 e1a01000   1038 	mov	r1,r0

0000051c e1a0000b   1039 	mov	r0,fp

00000520 ebffff4d*  1040 	bl	readOsc

                    1041 ;355: 


                    1042 ;356: 	// нечего читать (по идее сюда дойти не должно), т.к. 


                    1043 ;357: 	// выше сработает проверка if (frameNum >= OSCInfo_getFrameCount(oscInfo))


                    1044 ;358: 	if (readSize == 0)


                    1045 

00000524 e3500000   1046 	cmp	r0,0

00000528 0a000033   1047 	beq	.L1012

                    1048 ;359: 	{


                    1049 

                    1050 ;360: 		return FALSE;


                    1051 

                    1052 ;361: 	}


                    1053 ;362: 	// ошибка чтения


                    1054 ;363: 	if (readSize < 0)


                    1055 

0000052c e3500000   1056 	cmp	r0,0

00000530 ba000031   1057 	blt	.L1012

                    1058 ;364: 	{


                    1059 

                    1060 ;365: 		return FALSE;


                    1061 

                    1062 ;366: 	}


                    1063 ;367: 


                    1064 ;368: 	// конвертация периода


                    1065 ;369: 	if (!OscConverter_processPeriod(readFileContext))


                    1066 

00000534 e1a00004   1067 	mov	r0,r4

00000538 eb000000*  1068 	bl	OscConverter_processPeriod

0000053c e3500000   1069 	cmp	r0,0

00000540 0a00002d   1070 	beq	.L1012

                    1071 ;370: 	{


                    1072 

                    1073 ;371: 		return FALSE;


                    1074 

                    1075 ;372: 	}


                    1076 ;373: 	


                    1077 ;374: 	// 


                    1078 ;375: 	if (!OscReadFileContext_writeDatToStream(readFileContext))


                    1079 

00000544 e1a00004   1080 	mov	r0,r4

00000548 eb000000*  1081 	bl	OscReadFileContext_writeDatToStream

0000054c e3500000   1082 	cmp	r0,0

00000550 0a000029   1083 	beq	.L1012

                    1084 ;376: 	{


                    1085 

                    1086 ;377: 		return FALSE;


                    1087 


                                                                      Page 19
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cdc1.s
                    1088 ;378: 	}


                    1089 ;379: 


                    1090 ;380: 


                    1091 ;381: 	readFileContext->curFrame++;


                    1092 

00000554 e594a030   1093 	ldr	r10,[r4,48]

00000558 e1a00005   1094 	mov	r0,r5

0000055c e28aa001   1095 	add	r10,r10,1

00000560 e584a030   1096 	str	r10,[r4,48]

                    1097 ;382: 	// последний фрейм, нужно записать cfg


                    1098 ;383: 	if (readFileContext->curFrame >= OSCInfo_getFrameCount(oscInfo))


                    1099 

00000564 eb000000*  1100 	bl	OSCInfo_getFrameCount

00000568 e15a0000   1101 	cmp	r10,r0

0000056c 3a000024   1102 	blo	.L1000

                    1103 ;384: 	{


                    1104 

                    1105 ;385: 		


                    1106 ;386: 		// закрываем dat и пишем все остальные файлы


                    1107 ;387: 		bool result = 


                    1108 

00000570 e3a05000   1109 	mov	r5,0

00000574 e1a00004   1110 	mov	r0,r4

00000578 eb000000*  1111 	bl	OscReadFileContext_closeDat

0000057c e3500000   1112 	cmp	r0,0

00000580 0a00001b   1113 	beq	.L1003

00000584 e1a00004   1114 	mov	r0,r4

00000588 eb000000*  1115 	bl	OscConverter_processCfg

0000058c e3500000   1116 	cmp	r0,0

00000590 0a000017   1117 	beq	.L1003

00000594 e1a00004   1118 	mov	r0,r4

00000598 eb000000*  1119 	bl	OscReadFileContext_writeCfgToStream

0000059c e3500000   1120 	cmp	r0,0

000005a0 0a000013   1121 	beq	.L1003

000005a4 e1a00004   1122 	mov	r0,r4

000005a8 eb000000*  1123 	bl	OscReadFileContext_closeCfg

000005ac e3500000   1124 	cmp	r0,0

000005b0 0a00000f   1125 	beq	.L1003

000005b4 e1a00004   1126 	mov	r0,r4

000005b8 eb000000*  1127 	bl	OscConverter_processHdr

000005bc e3500000   1128 	cmp	r0,0

000005c0 0a00000b   1129 	beq	.L1003

000005c4 e1a00004   1130 	mov	r0,r4

000005c8 eb000000*  1131 	bl	OscReadFileContext_writeHdrToStream

000005cc e3500000   1132 	cmp	r0,0

000005d0 0a000007   1133 	beq	.L1003

000005d4 e1a00004   1134 	mov	r0,r4

000005d8 eb000000*  1135 	bl	OscReadFileContext_closeHdr

000005dc e3500000   1136 	cmp	r0,0

000005e0 0a000003   1137 	beq	.L1003

000005e4 e1a00004   1138 	mov	r0,r4

000005e8 eb000000*  1139 	bl	OscReadFileContext_flushAndClose

000005ec e3500000   1140 	cmp	r0,0

000005f0 13a05001   1141 	movne	r5,1

                    1142 .L1003:

000005f4 e31500ff   1143 	tst	r5,255

                    1144 ;388: 			// dat


                    1145 ;389: 			OscReadFileContext_closeDat(readFileContext) && 


                    1146 ;390: 			// cfg


                    1147 ;391: 			OscConverter_processCfg(readFileContext) &&


                    1148 ;392: 			OscReadFileContext_writeCfgToStream(readFileContext) &&



                                                                      Page 20
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cdc1.s
                    1149 ;393: 			OscReadFileContext_closeCfg(readFileContext) &&


                    1150 ;394: 			// hdr


                    1151 ;395: 			OscConverter_processHdr(readFileContext) &&


                    1152 ;396: 			OscReadFileContext_writeHdrToStream(readFileContext) &&


                    1153 ;397: 			OscReadFileContext_closeHdr(readFileContext) &&


                    1154 ;398: 			// все записать в stream


                    1155 ;399: 			OscReadFileContext_flushAndClose(readFileContext);


                    1156 ;400: 	


                    1157 ;401: 		if (!result)


                    1158 

000005f8 1a000001   1159 	bne	.L1000

                    1160 .L1012:

                    1161 ;402: 		{


                    1162 

                    1163 ;403: 			return FALSE;


                    1164 

000005fc e3a00000   1165 	mov	r0,0

00000600 ea000004   1166 	b	.L965

                    1167 .L1000:

                    1168 ;404: 		}


                    1169 ;405: 


                    1170 ;406: 	}


                    1171 ;407: 


                    1172 ;408: 


                    1173 ;409: 	// пишем сколько влезет в readBuf


                    1174 ;410: 	OscWriteBuffer_toBufferView(readBuf, streamBuffer);


                    1175 

00000604 e1a01008   1176 	mov	r1,r8

00000608 e1a00007   1177 	mov	r0,r7

0000060c eb000000*  1178 	bl	OscWriteBuffer_toBufferView

                    1179 ;411: 	*moreFollows = TRUE;


                    1180 

00000610 e3a00001   1181 	mov	r0,1

00000614 e5c60000   1182 	strb	r0,[r6]

                    1183 ;412: 


                    1184 ;413: 	return TRUE;


                    1185 

                    1186 .L965:

00000618 e28dd008   1187 	add	sp,sp,8

0000061c e8bd8ff0   1188 	ldmfd	[sp]!,{r4-fp,pc}

                    1189 	.endf	OSCFS_readFile

                    1190 	.align	4

                    1191 ;readFileContext	r4	local

                    1192 ;oscInfo	r5	local

                    1193 ;oscPeriodBuf	r9	local

                    1194 ;readSize	r0	local

                    1195 ;frameNum	r10	local

                    1196 ;streamBuffer	r8	local

                    1197 ;cfgBuffer	r5	local

                    1198 ;hdrBuffer	r4	local

                    1199 ;result	r5	local

                    1200 

                    1201 ;frsm	fp	param

                    1202 ;readBuf	r7	param

                    1203 ;moreFollows	r6	param

                    1204 

                    1205 	.section ".bss","awb"

                    1206 .L1254:

                    1207 	.data

                    1208 	.text

                    1209 


                                                                      Page 21
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cdc1.s
                    1210 ;414: }


                    1211 	.align	4

                    1212 

                    1213 	.data

                    1214 	.comm	osc_tlsf,4,4

                    1215 	.type	osc_tlsf,$object

                    1216 	.size	osc_tlsf,4

                    1217 	.comm	tlsfCS,4,4

                    1218 	.type	tlsfCS,$object

                    1219 	.size	tlsfCS,4

                    1220 	.ghsnote version,6

                    1221 	.ghsnote tools,3

                    1222 	.ghsnote options,0

                    1223 	.text

                    1224 	.align	4

