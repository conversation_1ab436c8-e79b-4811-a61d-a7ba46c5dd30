#include "platform_socket.h"
#include <platform_socket_def.h>
#include "MmsConst.h"
#include "debug.h"
#include <lwiplib.h>
#include <lwip/sockets.h>

#define MMS_PORT 102

SOCKET listenSocket;

static struct sockaddr_in  listenAddr = { 0 };

bool socketInit(void)
{
    return lwiplib_init() == 0;
}



bool acceptConnection(SOCKET* pConnSocket, struct sockaddr*addr, int *addrlen)
{
    int keepalive = 1;
    int keepcnt = 10;
    int keepidle = 10;
    int keepintvl = 1;

    *pConnSocket = accept (listenSocket, addr, (socklen_t*)addrlen);

    TRACE("Setting keepalive options");

    //Michael: Я не был пьян, goto написал по приколу и чтобы угодить Денису.
    if(0 != setsockopt(*pConnSocket, SOL_SOCKET, SO_KEEPALIVE, &keepalive , sizeof(int)))
    {
        ERROR_REPORT("Unable to set SO_KEEPALIVE");
        goto setOptError;
    }
    if(0 != setsockopt(*pConnSocket, IPPROTO_TCP, TCP_KEEPCNT, &keepcnt, sizeof(int)))
    {
        ERROR_REPORT("Unable to set TCP_KEEPCNT");
        goto setOptError;
    }
    if(0 != setsockopt(*pConnSocket, IPPROTO_TCP, TCP_KEEPIDLE, &keepidle, sizeof(int)))
    {
        ERROR_REPORT("Unable to set TCP_KEEPIDLE");
        goto setOptError;
    }
    if(0 != setsockopt(*pConnSocket, IPPROTO_TCP, TCP_KEEPINTVL, &keepintvl, sizeof(int)))
    {
        ERROR_REPORT("Unable to set TCP_KEEPINTVL");
        goto setOptError;
    }
setOptError:

    return *pConnSocket >= 0;
}

int readSocket(SERVER_SOCKET socket, void* buf, int byteCount)
{
	unsigned char* byteBuf = buf;
	while (byteCount)
	{
		int recvCount;
        recvCount = recv((SOCKET)socket, byteBuf, byteCount, 0);
        if (recvCount <= 0)
		{			
			return 0;
		}
		byteCount -= recvCount;
		byteBuf += recvCount;		
	}
    return 1;
}

int writeSocket(SERVER_SOCKET socket, void* buf, int byteCount)
{
	int bytesSent;
	bytesSent = send((SOCKET)socket, buf, byteCount, 0);

    if (bytesSent < 0)
	{		
		return -1;

	}
	return bytesSent;    
}


int startListening()
{    
	int sockResult;

	listenSocket = socket(AF_INET, SOCK_STREAM, 0);

    if (listenSocket < 0)
	{
		ERROR_REPORT("'socket' function has returned error");
		return 0;
	}

	listenAddr.sin_family = AF_INET;
	listenAddr.sin_port = htons(MMS_PORT);
	listenAddr.sin_addr.s_addr = INADDR_ANY;

	sockResult = bind(listenSocket, (struct sockaddr*)&listenAddr, sizeof(listenAddr));
    if (sockResult < 0)
	{
		ERROR_REPORT("Bind error");
		closesocket(listenSocket);
		return 0;
	}
	
	sockResult = listen(listenSocket, MAX_CONN_COUNT);
    if (sockResult  <  0)
	{
		ERROR_REPORT("'listen' function has returned error");
		closesocket(listenSocket);
		return 0;
	}
	return 1;
}

