#pragma once

#include "stringView.h"

#include <stdbool.h>
#include <stdint.h>

//Буфер и смещение в нём в виде одного объекта

typedef struct {
    uint8_t* p;
    size_t pos;
    size_t len;
} BufferView;

void BufferView_init(BufferView* bv, uint8_t* buf, size_t len, size_t pos);

inline bool BufferView_endOfBuf(BufferView* bv)
{
    return bv->pos == bv->len;
}

inline uint8_t* BufferView_currentPtr(BufferView* bv)
{
    return bv->p + bv->pos;
}

//! Запрашивает в буфере byteCount байт и возвращает в pFreeSpace
//! указатель на выделенное место.
//! Фактически производится только проверка на количество свободного места.
//! Состояние буфера при этом не меняется.
bool BufferView_alloc(BufferView* bv, size_t byteCount, uint8_t** pFreeSpace);

//! Записывает StringView в буфер. Возвращает FALSE если не помещается.
bool BufferView_writeStringView(BufferView* bv, const StringView* strView);


//! Записывает строку в буфер. Возвращает FALSE если не помещается.
bool BufferView_writeStr(BufferView* bv, const char* str);

//! Записывает uint16_t в буфер в виде Big Endian.
//! Возвращает FALSE если не помещается.
bool BufferView_writeUshortBE(BufferView* bv, uint16_t data);

//! Записывает данные в буфер. Если данные не помещаются, записывет
//! сколько поместится.
//! Возвращает число фактически записанных байтов.
size_t BufferView_writeData(BufferView* bv, void* data, size_t byteCount);

//! Читает StringView указанного размера. Возвращает FALSE
//! если в буфере осталось меньше байт
bool BufferView_readStringView(BufferView* bv, size_t strLen,
    StringView* result);

//! Продвигает позицию буфера вперёд на указанное число байтов.
//! Если раньше времени достигнут конец буфера, возврвщает FALSE.
bool BufferView_advance(BufferView* bv, size_t len);


