                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gb01.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=lwip_socket.c -o gh_gb01.o -list=lwip_socket.lst C:\Users\<USER>\AppData\Local\Temp\gh_gb01.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_gb01.s
Source File: lwip_socket.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile lwip_socket.c

                      10 ;		-o lwip_socket.o

                      11 ;Source File:   lwip_socket.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:55:51 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "platform_socket.h"


                      21 ;2: #include <platform_socket_def.h>


                      22 ;3: #include "MmsConst.h"


                      23 ;4: #include "debug.h"


                      24 ;5: #include <lwiplib.h>


                      25 ;6: #include <lwip/sockets.h>


                      26 ;7: 


                      27 ;8: #define MMS_PORT 102


                      28 ;9: 


                      29 ;10: SOCKET listenSocket;


                      30 ;11: 


                      31 ;12: static struct sockaddr_in  listenAddr = { 0 };


                      32 ;13: 


                      33 ;14: bool socketInit(void)


                      34 	.text

                      35 	.align	4

                      36 socketInit::

00000000 e92d4000     37 	stmfd	[sp]!,{lr}

                      38 ;15: {


                      39 

                      40 ;16:     return lwiplib_init() == 0;


                      41 

00000004 eb000000*    42 	bl	lwiplib_init

00000008 e3500000     43 	cmp	r0,0

0000000c 03a00001     44 	moveq	r0,1

00000010 13a00000     45 	movne	r0,0

00000014 e8bd8000     46 	ldmfd	[sp]!,{pc}

                      47 	.endf	socketInit

                      48 	.align	4

                      49 

                      50 	.section ".bss","awb"


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gb01.s
                      51 .L33:

                      52 	.data

                      53 	.text

                      54 

                      55 ;17: }


                      56 

                      57 ;18: 


                      58 ;19: 


                      59 ;20: 


                      60 ;21: bool acceptConnection(SOCKET* pConnSocket, struct sockaddr*addr, int *addrlen)


                      61 	.align	4

                      62 	.align	4

                      63 acceptConnection::

00000018 e92d4010     64 	stmfd	[sp]!,{r4,lr}

                      65 ;22: {


                      66 

0000001c e24dd014     67 	sub	sp,sp,20

00000020 e1a04000     68 	mov	r4,r0

00000024 e3a00001     69 	mov	r0,1

00000028 e3a0300a     70 	mov	r3,10

0000002c e1a0c003     71 	mov	r12,r3

00000030 e98d1009     72 	stmfa	[sp],{r0,r3,r12}

00000034 e59f318c*    73 	ldr	r3,.L129

00000038 e58d0010     74 	str	r0,[sp,16]

                      75 ;23:     int keepalive = 1;


                      76 

                      77 ;24:     int keepcnt = 10;


                      78 

                      79 ;25:     int keepidle = 10;


                      80 

                      81 ;26:     int keepintvl = 1;


                      82 

                      83 ;27: 


                      84 ;28:     *pConnSocket = accept (listenSocket, addr, (socklen_t*)addrlen);


                      85 

0000003c e5930000     86 	ldr	r0,[r3]

00000040 eb000000*    87 	bl	lwip_accept

00000044 e28d3004     88 	add	r3,sp,4

00000048 e5840000     89 	str	r0,[r4]

                      90 ;29: 


                      91 ;30:     TRACE("Setting keepalive options");


                      92 ;31: 


                      93 ;32:     //Michael: Я не был пьян, goto написал по приколу и чтобы угодить Денису.


                      94 ;33:     if(0 != setsockopt(*pConnSocket, SOL_SOCKET, SO_KEEPALIVE, &keepalive , sizeof(int)))


                      95 

0000004c e3a01004     96 	mov	r1,4

00000050 e58d1000     97 	str	r1,[sp]

00000054 e3a01ef0     98 	mov	r1,15<<8

00000058 e28110ff     99 	add	r1,r1,255

0000005c e3a02008    100 	mov	r2,8

00000060 eb000000*   101 	bl	lwip_setsockopt

00000064 e3500000    102 	cmp	r0,0

00000068 1a000017    103 	bne	.L44

                     104 ;34:     {


                     105 

                     106 ;35:         ERROR_REPORT("Unable to set SO_KEEPALIVE");


                     107 ;36:         goto setOptError;


                     108 

                     109 ;37:     }


                     110 ;38:     if(0 != setsockopt(*pConnSocket, IPPROTO_TCP, TCP_KEEPCNT, &keepcnt, sizeof(int)))


                     111 


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gb01.s
0000006c e28d3008    112 	add	r3,sp,8

00000070 e3a00004    113 	mov	r0,4

00000074 e58d0000    114 	str	r0,[sp]

00000078 e5940000    115 	ldr	r0,[r4]

0000007c e3a02005    116 	mov	r2,5

00000080 e3a01006    117 	mov	r1,6

00000084 eb000000*   118 	bl	lwip_setsockopt

00000088 e3500000    119 	cmp	r0,0

0000008c 1a00000e    120 	bne	.L44

                     121 ;39:     {


                     122 

                     123 ;40:         ERROR_REPORT("Unable to set TCP_KEEPCNT");


                     124 ;41:         goto setOptError;


                     125 

                     126 ;42:     }


                     127 ;43:     if(0 != setsockopt(*pConnSocket, IPPROTO_TCP, TCP_KEEPIDLE, &keepidle, sizeof(int)))


                     128 

00000090 e28d300c    129 	add	r3,sp,12

00000094 e3a00004    130 	mov	r0,4

00000098 e58d0000    131 	str	r0,[sp]

0000009c e5940000    132 	ldr	r0,[r4]

000000a0 e3a02003    133 	mov	r2,3

000000a4 e3a01006    134 	mov	r1,6

000000a8 eb000000*   135 	bl	lwip_setsockopt

000000ac e3500000    136 	cmp	r0,0

000000b0 1a000005    137 	bne	.L44

                     138 ;44:     {


                     139 

                     140 ;45:         ERROR_REPORT("Unable to set TCP_KEEPIDLE");


                     141 ;46:         goto setOptError;


                     142 

                     143 ;47:     }


                     144 ;48:     if(0 != setsockopt(*pConnSocket, IPPROTO_TCP, TCP_KEEPINTVL, &keepintvl, sizeof(int)))


                     145 

000000b4 e28d3010    146 	add	r3,sp,16

000000b8 e3a02004    147 	mov	r2,4

000000bc e58d2000    148 	str	r2,[sp]

000000c0 e5940000    149 	ldr	r0,[r4]

000000c4 e3a01006    150 	mov	r1,6

000000c8 eb000000*   151 	bl	lwip_setsockopt

                     152 .L44:

                     153 ;49:     {


                     154 

                     155 ;50:         ERROR_REPORT("Unable to set TCP_KEEPINTVL");


                     156 ;51:         goto setOptError;


                     157 

                     158 ;52:     }


                     159 ;53: setOptError:


                     160 

                     161 ;54: 


                     162 ;55:     return *pConnSocket >= 0;


                     163 

000000cc e5940000    164 	ldr	r0,[r4]

000000d0 e1a00fa0    165 	mov	r0,r0 lsr 31

000000d4 e2200001    166 	eor	r0,r0,1

000000d8 e28dd014    167 	add	sp,sp,20

000000dc e8bd8010    168 	ldmfd	[sp]!,{r4,pc}

                     169 	.endf	acceptConnection

                     170 	.align	4

                     171 ;keepalive	[sp,4]	local

                     172 ;keepcnt	[sp,8]	local


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gb01.s
                     173 ;keepidle	[sp,12]	local

                     174 ;keepintvl	[sp,16]	local

                     175 

                     176 ;pConnSocket	r4	param

                     177 ;addr	none	param

                     178 ;addrlen	none	param

                     179 

                     180 	.section ".bss","awb"

                     181 .L114:

                     182 	.data

                     183 	.text

                     184 

                     185 ;56: }


                     186 

                     187 ;57: 


                     188 ;58: int readSocket(SERVER_SOCKET socket, void* buf, int byteCount)


                     189 	.align	4

                     190 	.align	4

                     191 readSocket::

000000e0 e92d4070    192 	stmfd	[sp]!,{r4-r6,lr}

                     193 ;59: {


                     194 

                     195 ;60: 	unsigned char* byteBuf = buf;


                     196 

000000e4 e1a06000    197 	mov	r6,r0

000000e8 e1b05002    198 	movs	r5,r2

000000ec e1a04001    199 	mov	r4,r1

                     200 ;61: 	while (byteCount)


                     201 

000000f0 0a00000a    202 	beq	.L133

                     203 .L134:

                     204 ;62: 	{


                     205 

                     206 ;63: 		int recvCount;


                     207 ;64:         recvCount = recv((SOCKET)socket, byteBuf, byteCount, 0);


                     208 

000000f4 e1a02005    209 	mov	r2,r5

000000f8 e1a01004    210 	mov	r1,r4

000000fc e1a00006    211 	mov	r0,r6

00000100 e3a03000    212 	mov	r3,0

00000104 eb000000*   213 	bl	lwip_recv

                     214 ;65:         if (recvCount <= 0)


                     215 

00000108 e3500000    216 	cmp	r0,0

                     217 ;66: 		{			


                     218 

                     219 ;67: 			return 0;


                     220 

0000010c d3a00000    221 	movle	r0,0

00000110 da000003    222 	ble	.L130

                     223 ;68: 		}


                     224 ;69: 		byteCount -= recvCount;


                     225 

00000114 e0555000    226 	subs	r5,r5,r0

                     227 ;70: 		byteBuf += recvCount;		


                     228 

00000118 e0844000    229 	add	r4,r4,r0

0000011c 1afffff4    230 	bne	.L134

                     231 .L133:

                     232 ;71: 	}


                     233 ;72:     return 1;



                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gb01.s
                     234 

00000120 e3a00001    235 	mov	r0,1

                     236 .L130:

00000124 e8bd8070    237 	ldmfd	[sp]!,{r4-r6,pc}

                     238 	.endf	readSocket

                     239 	.align	4

                     240 ;byteBuf	r4	local

                     241 ;recvCount	r0	local

                     242 

                     243 ;socket	r6	param

                     244 ;buf	r1	param

                     245 ;byteCount	r5	param

                     246 

                     247 	.section ".bss","awb"

                     248 .L178:

                     249 	.data

                     250 	.text

                     251 

                     252 ;73: }


                     253 

                     254 ;74: 


                     255 ;75: int writeSocket(SERVER_SOCKET socket, void* buf, int byteCount)


                     256 	.align	4

                     257 	.align	4

                     258 writeSocket::

00000128 e92d4000    259 	stmfd	[sp]!,{lr}

                     260 ;76: {


                     261 

                     262 ;77: 	int bytesSent;


                     263 ;78: 	bytesSent = send((SOCKET)socket, buf, byteCount, 0);


                     264 

0000012c e3a03000    265 	mov	r3,0

00000130 eb000000*   266 	bl	lwip_send

                     267 ;79: 


                     268 ;80:     if (bytesSent < 0)


                     269 

                     270 

                     271 

                     272 

00000134 e3500000    273 	cmp	r0,0

00000138 b3e00000    274 	mvnlt	r0,0

0000013c e8bd8000    275 	ldmfd	[sp]!,{pc}

                     276 	.endf	writeSocket

                     277 	.align	4

                     278 

                     279 ;socket	none	param

                     280 ;buf	none	param

                     281 ;byteCount	none	param

                     282 

                     283 	.section ".bss","awb"

                     284 .L228:

                     285 	.data

                     286 	.text

                     287 

                     288 ;86: }


                     289 

                     290 ;87: 


                     291 ;88: 


                     292 ;89: int startListening()


                     293 	.align	4

                     294 	.align	4


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gb01.s
                     295 	.align	4

                     296 startListening::

00000140 e92d4030    297 	stmfd	[sp]!,{r4-r5,lr}

00000144 e59f507c*   298 	ldr	r5,.L129

                     299 ;90: {    


                     300 

                     301 ;91: 	int sockResult;


                     302 ;92: 


                     303 ;93: 	listenSocket = socket(AF_INET, SOCK_STREAM, 0);


                     304 

00000148 e3a02000    305 	mov	r2,0

0000014c e3a01001    306 	mov	r1,1

00000150 e3a00002    307 	mov	r0,2

00000154 eb000000*   308 	bl	lwip_socket

00000158 e5850000    309 	str	r0,[r5]

                     310 ;94: 


                     311 ;95:     if (listenSocket < 0)


                     312 

0000015c e3500000    313 	cmp	r0,0

                     314 ;96: 	{


                     315 

                     316 ;97: 		ERROR_REPORT("'socket' function has returned error");


                     317 ;98: 		return 0;


                     318 

00000160 b3a00000    319 	movlt	r0,0

00000164 ba000016    320 	blt	.L235

                     321 ;99: 	}


                     322 ;100: 


                     323 ;101: 	listenAddr.sin_family = AF_INET;


                     324 

00000168 e59f405c*   325 	ldr	r4,.L343

0000016c e3a00002    326 	mov	r0,2

00000170 e5c40001    327 	strb	r0,[r4,1]

                     328 ;102: 	listenAddr.sin_port = htons(MMS_PORT);


                     329 

00000174 e3a00066    330 	mov	r0,102

00000178 eb000000*   331 	bl	lwip_htons

0000017c e1a01004    332 	mov	r1,r4

00000180 e1c400b2    333 	strh	r0,[r4,2]

                     334 ;103: 	listenAddr.sin_addr.s_addr = INADDR_ANY;


                     335 

00000184 e3a00000    336 	mov	r0,0

00000188 e5840004    337 	str	r0,[r4,4]

                     338 ;104: 


                     339 ;105: 	sockResult = bind(listenSocket, (struct sockaddr*)&listenAddr, sizeof(listenAddr));


                     340 

0000018c e5950000    341 	ldr	r0,[r5]

00000190 e3a02010    342 	mov	r2,16

00000194 eb000000*   343 	bl	lwip_bind

                     344 ;106:     if (sockResult < 0)


                     345 

00000198 e3500000    346 	cmp	r0,0

0000019c ba000005    347 	blt	.L244

                     348 ;107: 	{


                     349 

                     350 ;108: 		ERROR_REPORT("Bind error");


                     351 ;109: 		closesocket(listenSocket);


                     352 

                     353 ;110: 		return 0;


                     354 

                     355 ;111: 	}



                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gb01.s
                     356 ;112: 	


                     357 ;113: 	sockResult = listen(listenSocket, MAX_CONN_COUNT);


                     358 

000001a0 e5950000    359 	ldr	r0,[r5]

000001a4 e3a01004    360 	mov	r1,4

000001a8 eb000000*   361 	bl	lwip_listen

                     362 ;114:     if (sockResult  <  0)


                     363 

000001ac e3500000    364 	cmp	r0,0

                     365 ;119: 	}


                     366 ;120: 	return 1;


                     367 

000001b0 a3a00001    368 	movge	r0,1

000001b4 aa000002    369 	bge	.L235

                     370 .L244:

                     371 ;115: 	{


                     372 

                     373 ;116: 		ERROR_REPORT("'listen' function has returned error");


                     374 ;117: 		closesocket(listenSocket);


                     375 

000001b8 e5950000    376 	ldr	r0,[r5]

000001bc eb000000*   377 	bl	lwip_close

                     378 ;118: 		return 0;


                     379 

000001c0 e3a00000    380 	mov	r0,0

                     381 .L235:

000001c4 e8bd8030    382 	ldmfd	[sp]!,{r4-r5,pc}

                     383 	.endf	startListening

                     384 	.align	4

                     385 

                     386 	.section ".bss","awb"

                     387 .L320:

                     388 	.data

                     389 .L321:

00000000 00         390 listenAddr:	.space	1

00000001 00         391 	.space	1

00000002 0000       392 	.space	2

00000004 00000000    393 	.space	4

00000008 00000000    394 	.space	8

0000000c 00000000 
                     395 	.type	listenAddr,$object

                     396 	.size	listenAddr,16

                     397 	.text

                     398 

                     399 ;121: }


                     400 	.align	4

                     401 .L129:

000001c8 00000000*   402 	.data.w	listenSocket

                     403 	.type	.L129,$object

                     404 	.size	.L129,4

                     405 

                     406 .L343:

000001cc 00000000*   407 	.data.w	.L321

                     408 	.type	.L343,$object

                     409 	.size	.L343,4

                     410 

                     411 	.align	4

                     412 ;listenAddr	.L321	static

                     413 

                     414 	.data

                     415 	.comm	listenSocket,4,4


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gb01.s
                     416 	.type	listenSocket,$object

                     417 	.size	listenSocket,4

                     418 	.ghsnote version,6

                     419 	.ghsnote tools,3

                     420 	.ghsnote options,0

                     421 	.text

                     422 	.align	4

                     423 	.data

                     424 	.align	4

                     425 	.text

