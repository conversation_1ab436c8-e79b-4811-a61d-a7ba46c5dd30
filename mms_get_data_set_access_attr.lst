                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2rc1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=mms_get_data_set_access_attr.c -o gh_2rc1.o -list=mms_get_data_set_access_attr.lst C:\Users\<USER>\AppData\Local\Temp\gh_2rc1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_2rc1.s
Source File: mms_get_data_set_access_attr.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      10 ;		mms_get_data_set_access_attr.c -o mms_get_data_set_access_attr.o

                      11 ;Source File:   mms_get_data_set_access_attr.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Oct 07 09:39:34 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "mms_get_data_set_access_attr.h"


                      21 ;2: 


                      22 ;3: #include "stringView.h"


                      23 ;4: #include "MmsConst.h"


                      24 ;5: #include "iedmodel.h"


                      25 ;6: #include "BaseAsnTypes.h"


                      26 ;7: #include "AsnEncoding.h"


                      27 ;8: #include "mms_error.h"


                      28 ;9: #include "debug.h"


                      29 ;10: #include <string.h>


                      30 ;11: 


                      31 ;12: 


                      32 ;13: //Одна ссылка


                      33 ;14: int encodeDataSetRefAttr(uint8_t* outBuf, int bufPos, int objectPos, bool determineSize)


                      34 	.text

                      35 	.align	4

                      36 encodeDataSetRefAttr::

00000000 e92d4cf0     37 	stmfd	[sp]!,{r4-r7,r10-fp,lr}

                      38 ;15: {


                      39 

                      40 ;16:     int domainPos;


                      41 ;17:     int domainSize;


                      42 ;18:     int namePos;


                      43 ;19:     int nameSize;


                      44 ;20:     int size1;


                      45 ;21:     int size2;


                      46 ;22:     int seqenceSize;


                      47 ;23:     uint8_t tag;


                      48 ;24:     int pos = objectPos;


                      49 

                      50 ;25: 



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2rc1.s
                      51 ;26: 


                      52 ;27:     //============Определяем размеры и позиции==============


                      53 ;28:     pos = readTL(pos, &tag, NULL, NULL);


                      54 

00000004 e1a05001     55 	mov	r5,r1

00000008 e24dd00c     56 	sub	sp,sp,12

0000000c e28d1002     57 	add	r1,sp,2

00000010 e1a06000     58 	mov	r6,r0

00000014 e1a00002     59 	mov	r0,r2

00000018 e5cd3003     60 	strb	r3,[sp,3]

0000001c e3a03000     61 	mov	r3,0

00000020 e1a02003     62 	mov	r2,r3

00000024 eb000000*    63 	bl	readTL

00000028 e1b04000     64 	movs	r4,r0

                      65 ;29: 


                      66 ;30:     if(pos == 0 || tag != ASN_SEQUENCE)


                      67 

0000002c 0a000018     68 	beq	.L13

00000030 e5dd0002     69 	ldrb	r0,[sp,2]

00000034 e3500030     70 	cmp	r0,48

00000038 1a000015     71 	bne	.L13

                      72 ;31:     {


                      73 

                      74 ;32:         ERROR_REPORT("Error reading reference TL");


                      75 ;33:         return 0;


                      76 

                      77 ;34:     }


                      78 ;35:     


                      79 ;36:     //Домен


                      80 ;37:     domainPos = pos;


                      81 

                      82 ;38:     pos = readTL(domainPos, &tag, NULL, &domainSize);


                      83 

0000003c e28d3004     84 	add	r3,sp,4

00000040 e28d1002     85 	add	r1,sp,2

00000044 e1a00004     86 	mov	r0,r4

00000048 e3a02000     87 	mov	r2,0

0000004c eb000000*    88 	bl	readTL

                      89 ;39:     if(pos == 0 || tag != ASN_VISIBLE_STRING)


                      90 

00000050 e3500000     91 	cmp	r0,0

00000054 0a00000e     92 	beq	.L13

00000058 e5dd0002     93 	ldrb	r0,[sp,2]

0000005c e350001a     94 	cmp	r0,26

00000060 1a00000b     95 	bne	.L13

                      96 ;40:     {


                      97 

                      98 ;41:         ERROR_REPORT("Error reading domain name TL");


                      99 ;42:         return 0;


                     100 

                     101 ;43:     }


                     102 ;44:     namePos = domainPos + domainSize;


                     103 

00000064 e28d3008    104 	add	r3,sp,8

00000068 e59d7004    105 	ldr	r7,[sp,4]

0000006c e28d1002    106 	add	r1,sp,2

00000070 e0877004    107 	add	r7,r7,r4

                     108 ;45: 


                     109 ;46:     //Имя


                     110 ;47:     pos = readTL(namePos, &tag, NULL, &nameSize);


                     111 


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2rc1.s
00000074 e1a00007    112 	mov	r0,r7

00000078 e3a02000    113 	mov	r2,0

0000007c eb000000*   114 	bl	readTL

                     115 ;48:     if(pos == 0 || tag != ASN_VISIBLE_STRING)


                     116 

00000080 e3500000    117 	cmp	r0,0

00000084 0a000002    118 	beq	.L13

00000088 e5dd0002    119 	ldrb	r0,[sp,2]

0000008c e350001a    120 	cmp	r0,26

00000090 0a000001    121 	beq	.L12

                     122 .L13:

                     123 ;49:     {


                     124 

                     125 ;50:         ERROR_REPORT("Error reading object name TL");


                     126 ;51:         return 0;


                     127 

00000094 e3a00000    128 	mov	r0,0

00000098 ea000030    129 	b	.L2

                     130 .L12:

                     131 ;52:     }


                     132 ;53: 


                     133 ;54:     //A1


                     134 ;55:     size1 = 1 + BerEncoder_determineLengthSize(domainSize + nameSize)


                     135 

0000009c e99d0006    136 	ldmed	[sp],{r1-r2}

000000a0 e0820001    137 	add	r0,r2,r1

000000a4 eb000000*   138 	bl	BerEncoder_determineLengthSize

000000a8 e99d000c    139 	ldmed	[sp],{r2-r3}

000000ac e0830000    140 	add	r0,r3,r0

000000b0 e0820000    141 	add	r0,r2,r0

000000b4 e280a001    142 	add	r10,r0,1

                     143 ;56:             + domainSize + nameSize;


                     144 ;57: 


                     145 ;58:     //A0


                     146 ;59:     size2 = 1 + BerEncoder_determineLengthSize(size1) + size1;


                     147 

000000b8 e1a0000a    148 	mov	r0,r10

000000bc eb000000*   149 	bl	BerEncoder_determineLengthSize

000000c0 e08a0000    150 	add	r0,r10,r0

000000c4 e280b001    151 	add	fp,r0,1

                     152 ;60: 


                     153 ;61:     //Sequence


                     154 ;62:     seqenceSize = 1 + BerEncoder_determineLengthSize(size2) + size2;


                     155 

000000c8 e1a0000b    156 	mov	r0,fp

000000cc eb000000*   157 	bl	BerEncoder_determineLengthSize

                     158 ;63: 


                     159 ;64:     if(determineSize)


                     160 

000000d0 e5dd1003    161 	ldrb	r1,[sp,3]

000000d4 e3510000    162 	cmp	r1,0

000000d8 108b0000    163 	addne	r0,fp,r0

000000dc 12800001    164 	addne	r0,r0,1

                     165 ;65:     {


                     166 

                     167 ;66:         return seqenceSize;


                     168 

000000e0 1a00001e    169 	bne	.L2

                     170 ;67:     }


                     171 ;68: 


                     172 ;69:     //================Кодируем====================



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2rc1.s
                     173 ;70:     //Sequence


                     174 ;71:     bufPos = BerEncoder_encodeTL(ASN_SEQUENCE, size2, outBuf, bufPos);


                     175 

000000e4 e1a03005    176 	mov	r3,r5

000000e8 e1a02006    177 	mov	r2,r6

000000ec e1a0100b    178 	mov	r1,fp

000000f0 e3a00030    179 	mov	r0,48

000000f4 eb000000*   180 	bl	BerEncoder_encodeTL

                     181 ;72:     //A0


                     182 ;73:     bufPos = BerEncoder_encodeTL(0xA0, size1, outBuf, bufPos);


                     183 

000000f8 e1a02006    184 	mov	r2,r6

000000fc e1a0100a    185 	mov	r1,r10

00000100 e1a03000    186 	mov	r3,r0

00000104 e3a000a0    187 	mov	r0,160

00000108 eb000000*   188 	bl	BerEncoder_encodeTL

                     189 ;74:     //A1


                     190 ;75:     bufPos = BerEncoder_encodeTL(0xA1, domainSize + nameSize, outBuf, bufPos);


                     191 

0000010c e1a03000    192 	mov	r3,r0

00000110 e1a02006    193 	mov	r2,r6

00000114 e99d0022    194 	ldmed	[sp],{r1,r5}

00000118 e3a000a1    195 	mov	r0,161

0000011c e0851001    196 	add	r1,r5,r1

00000120 eb000000*   197 	bl	BerEncoder_encodeTL

00000124 e59fa24c*   198 	ldr	r10,.L157

00000128 e1a05000    199 	mov	r5,r0

                     200 ;76:     //Домен


                     201 ;77:     memcpy(outBuf + bufPos, iedModel + domainPos, domainSize);


                     202 

0000012c e59a0000    203 	ldr	r0,[r10]

00000130 e59d2004    204 	ldr	r2,[sp,4]

00000134 e0841000    205 	add	r1,r4,r0

00000138 e0850006    206 	add	r0,r5,r6

0000013c eb000000*   207 	bl	memcpy

                     208 ;78:     bufPos += domainSize;


                     209 

00000140 e99d0005    210 	ldmed	[sp],{r0,r2}

00000144 e0855000    211 	add	r5,r5,r0

                     212 ;79:     //Имя


                     213 ;80:     memcpy(outBuf + bufPos, iedModel + namePos, nameSize);


                     214 

00000148 e59a0000    215 	ldr	r0,[r10]

0000014c e0871000    216 	add	r1,r7,r0

00000150 e0850006    217 	add	r0,r5,r6

00000154 eb000000*   218 	bl	memcpy

                     219 ;81:     bufPos += nameSize;


                     220 

00000158 e59d0008    221 	ldr	r0,[sp,8]

0000015c e0850000    222 	add	r0,r5,r0

                     223 ;82:     return bufPos;


                     224 

                     225 .L2:

00000160 e28dd00c    226 	add	sp,sp,12

00000164 e8bd8cf0    227 	ldmfd	[sp]!,{r4-r7,r10-fp,pc}

                     228 	.endf	encodeDataSetRefAttr

                     229 	.align	4

                     230 ;domainPos	r4	local

                     231 ;domainSize	[sp,4]	local

                     232 ;namePos	r7	local

                     233 ;nameSize	[sp,8]	local


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2rc1.s
                     234 ;size1	r10	local

                     235 ;size2	fp	local

                     236 ;seqenceSize	r0	local

                     237 ;tag	[sp,2]	local

                     238 ;pos	r4	local

                     239 

                     240 ;outBuf	r6	param

                     241 ;bufPos	r5	param

                     242 ;objectPos	r2	param

                     243 ;determineSize	[sp,3]	param

                     244 

                     245 	.section ".bss","awb"

                     246 .L122:

                     247 	.data

                     248 	.text

                     249 

                     250 ;83: }


                     251 

                     252 ;84: 


                     253 ;85: 


                     254 ;86: static int encodeDataSetRefAttrs(uint8_t* outBuf, int bufPos, int dataSetPos,


                     255 	.align	4

                     256 	.align	4

                     257 encodeDataSetRefAttrs:

00000168 e92d4cf0    258 	stmfd	[sp]!,{r4-r7,r10-fp,lr}

                     259 ;87: 	bool determineSize)


                     260 ;88: {


                     261 

0000016c e24dd004    262 	sub	sp,sp,4

00000170 e1a0b000    263 	mov	fp,r0

00000174 e1a00002    264 	mov	r0,r2

00000178 e1a0200d    265 	mov	r2,sp

0000017c e1a07003    266 	mov	r7,r3

00000180 e3a06000    267 	mov	r6,0

                     268 ;89:     int endPos;


                     269 ;90:     int dataSetLen;


                     270 ;91:     int refPos;


                     271 ;92:     uint8_t tag;


                     272 ;93: 	int size = 0;


                     273 

                     274 ;94:     int pos = readTL(dataSetPos, NULL, &dataSetLen, NULL );


                     275 

00000184 e1a03006    276 	mov	r3,r6

00000188 e1a04001    277 	mov	r4,r1

0000018c e1a01006    278 	mov	r1,r6

00000190 eb000000*   279 	bl	readTL

                     280 ;95:     endPos = pos + dataSetLen;


                     281 

00000194 e59da000    282 	ldr	r10,[sp]

00000198 e08aa000    283 	add	r10,r10,r0

                     284 ;96:     //Пропускаем имя


                     285 ;97:     pos = skipObject(pos);


                     286 

0000019c eb000000*   287 	bl	skipObject

000001a0 e59f21d0*   288 	ldr	r2,.L157

000001a4 e5921000    289 	ldr	r1,[r2]

000001a8 e1a05000    290 	mov	r5,r0

                     291 ;98: 	VERIFY(pos);


                     292 ;99: 


                     293 ;100:     //Пропускаем дополнительную инфу для отчётов, если она есть.


                     294 ;101:     //Она должна быть всегда, но в отладочных моделях может и отсутствовать.



                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2rc1.s
                     295 ;102:     tag = iedModel[pos];


                     296 

000001ac e7d11005    297 	ldrb	r1,[r1,r5]

                     298 ;103:     if(tag == IED_DATASET_DESCRIPTION)


                     299 

000001b0 e3510004    300 	cmp	r1,4

000001b4 1a000001    301 	bne	.L162

                     302 ;104:     {


                     303 

                     304 ;105:         pos = skipObject(pos);


                     305 

000001b8 eb000000*   306 	bl	skipObject

000001bc e1a05000    307 	mov	r5,r0

                     308 .L162:

                     309 ;106:     }


                     310 ;107:     else


                     311 ;108:     {


                     312 

                     313 ;109:         TRACE("Dataset description is not found");


                     314 ;110:     }


                     315 ;111: 


                     316 ;112:     refPos = pos;


                     317 

                     318 ;113:     while(refPos < endPos)


                     319 

000001c0 e155000a    320 	cmp	r5,r10

000001c4 aa00000d    321 	bge	.L164

                     322 .L165:

                     323 ;114:     {


                     324 

                     325 ;115: 		bufPos = encodeDataSetRefAttr(outBuf, bufPos, refPos, determineSize);


                     326 

000001c8 e1a03007    327 	mov	r3,r7

000001cc e1a02005    328 	mov	r2,r5

000001d0 e1a01004    329 	mov	r1,r4

000001d4 e1a0000b    330 	mov	r0,fp

000001d8 ebffff88*   331 	bl	encodeDataSetRefAttr

000001dc e3570000    332 	cmp	r7,0

                     333 ;118: 		{


                     334 

                     335 ;119: 			size += bufPos;


                     336 

000001e0 e1a04000    337 	mov	r4,r0

                     338 ;116: 		VERIFY(bufPos);		


                     339 ;117: 		if (determineSize)


                     340 

000001e4 10866004    341 	addne	r6,r6,r4

                     342 ;120: 			bufPos = size;


                     343 

000001e8 11a04006    344 	movne	r4,r6

                     345 ;121: 		}				


                     346 ;122: 		refPos = skipObject(refPos);


                     347 

000001ec e1a00005    348 	mov	r0,r5

000001f0 eb000000*   349 	bl	skipObject

000001f4 e1a05000    350 	mov	r5,r0

000001f8 e155000a    351 	cmp	r5,r10

000001fc bafffff1    352 	blt	.L165

                     353 .L164:

                     354 ;123: 		VERIFY(refPos);


                     355 ;124:     }



                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2rc1.s
                     356 ;125: 	return bufPos;


                     357 

00000200 e1a00004    358 	mov	r0,r4

00000204 e28dd004    359 	add	sp,sp,4

00000208 e8bd4cf0    360 	ldmfd	[sp]!,{r4-r7,r10-fp,lr}

0000020c e12fff1e*   361 	ret	

                     362 	.endf	encodeDataSetRefAttrs

                     363 	.align	4

                     364 ;endPos	r10	local

                     365 ;dataSetLen	[sp]	local

                     366 ;refPos	r5	local

                     367 ;tag	r1	local

                     368 ;size	r6	local

                     369 ;pos	r5	local

                     370 

                     371 ;outBuf	fp	param

                     372 ;bufPos	r4	param

                     373 ;dataSetPos	r12	param

                     374 ;determineSize	r7	param

                     375 

                     376 	.section ".bss","awb"

                     377 .L232:

                     378 	.data

                     379 	.text

                     380 

                     381 ;126: }


                     382 

                     383 ;127: 


                     384 ;128: //Весь dataset


                     385 ;129: int encodeDataSetAttrs(uint32_t invokeId ,uint8_t* outBuf, int dataSetPos)


                     386 	.align	4

                     387 	.align	4

                     388 encodeDataSetAttrs::

00000210 e92d4cf0    389 	stmfd	[sp]!,{r4-r7,r10-fp,lr}

                     390 ;130: {


                     391 

                     392 ;131: 	int allRefsSize;


                     393 ;132: 	int accessResultSize;


                     394 ;133: 	int fullConfirmedResponseSize;


                     395 ;134: 	int invokeIdSize;


                     396 ;135: 	int bufPos = 0;


                     397 

                     398 ;136: 


                     399 ;137: 


                     400 ;138: 	//==================Определяем размеры====================	


                     401 ;139: 	allRefsSize = encodeDataSetRefAttrs(outBuf, bufPos, dataSetPos, TRUE);


                     402 

00000214 e1a0a002    403 	mov	r10,r2

00000218 e1a07000    404 	mov	r7,r0

0000021c e1a04001    405 	mov	r4,r1

00000220 e1a00004    406 	mov	r0,r4

00000224 e3a03001    407 	mov	r3,1

00000228 e3a01000    408 	mov	r1,0

0000022c ebffffcd*   409 	bl	encodeDataSetRefAttrs

00000230 e1a05000    410 	mov	r5,r0

                     411 ;140: 


                     412 ;141: 	invokeIdSize = BerEncoder_UInt32determineEncodedSize(invokeId) + 2;


                     413 

00000234 e1a00007    414 	mov	r0,r7

00000238 eb000000*   415 	bl	BerEncoder_UInt32determineEncodedSize

0000023c e280b002    416 	add	fp,r0,2


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2rc1.s
                     417 ;142: 


                     418 ;143: 	accessResultSize = allRefsSize


                     419 

00000240 e1a00005    420 	mov	r0,r5

00000244 eb000000*   421 	bl	BerEncoder_determineLengthSize

00000248 e0850000    422 	add	r0,r5,r0

0000024c e2806004    423 	add	r6,r0,4

                     424 ;144: 		+ BerEncoder_determineLengthSize(allRefsSize) + 1 // A1 TL


                     425 ;145: 		+ 3; //Размер флага Deletable


                     426 ;146: 	fullConfirmedResponseSize = accessResultSize


                     427 

00000250 e1a00006    428 	mov	r0,r6

00000254 eb000000*   429 	bl	BerEncoder_determineLengthSize

00000258 e1a02004    430 	mov	r2,r4

0000025c e3a03000    431 	mov	r3,0

00000260 e0860000    432 	add	r0,r6,r0

00000264 e08b0000    433 	add	r0,fp,r0

00000268 e2801001    434 	add	r1,r0,1

                     435 ;147: 		//confirmed response


                     436 ;148: 		+ BerEncoder_determineLengthSize(accessResultSize) + 1


                     437 ;149: 		//invokeId


                     438 ;150: 		+ invokeIdSize;


                     439 ;151: 		


                     440 ;152:     //==================Кодируем====================


                     441 ;153:     bufPos = 0;


                     442 

                     443 ;154: 


                     444 ;155:     // confirmed response PDU


                     445 ;156:     bufPos = BerEncoder_encodeTL(MMS_CONFIRMED_RESPONSE_PDU,  


                     446 

0000026c e3a000a1    447 	mov	r0,161

00000270 eb000000*   448 	bl	BerEncoder_encodeTL

                     449 ;157: 		fullConfirmedResponseSize, outBuf, bufPos);


                     450 ;158: 


                     451 ;159:     // invoke id  


                     452 ;160: 	bufPos = BerEncoder_encodeUInt32WithTL(ASN_INTEGER, invokeId, outBuf,


                     453 

00000274 e1a02004    454 	mov	r2,r4

00000278 e1a01007    455 	mov	r1,r7

0000027c e1a03000    456 	mov	r3,r0

00000280 e3a00002    457 	mov	r0,2

00000284 eb000000*   458 	bl	BerEncoder_encodeUInt32WithTL

                     459 ;161: 		bufPos);


                     460 ;162: 		


                     461 ;163:     // confirmed-service-response getNamedVariableAccessAttributes


                     462 ;164:     bufPos = BerEncoder_encodeTL(0xAC, accessResultSize,


                     463 

00000288 e1a02004    464 	mov	r2,r4

0000028c e1a01006    465 	mov	r1,r6

00000290 e1a03000    466 	mov	r3,r0

00000294 e3a000ac    467 	mov	r0,172

00000298 eb000000*   468 	bl	BerEncoder_encodeTL

                     469 ;165:                                  outBuf, bufPos);


                     470 ;166: 


                     471 ;167: 	//Deletable


                     472 ;168: 	bufPos = BerEncoder_encodeBoolean(VAR_DELETABLE, FALSE, outBuf, bufPos);	


                     473 

0000029c e1a02004    474 	mov	r2,r4

000002a0 e3a01000    475 	mov	r1,0

000002a4 e1a03000    476 	mov	r3,r0

000002a8 e3a00080    477 	mov	r0,128


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2rc1.s
000002ac eb000000*   478 	bl	BerEncoder_encodeBoolean

                     479 ;169: 	// A1


                     480 ;170: 	bufPos = BerEncoder_encodeTL(0xa1, allRefsSize, outBuf, bufPos);


                     481 

000002b0 e1a02004    482 	mov	r2,r4

000002b4 e1a01005    483 	mov	r1,r5

000002b8 e1a03000    484 	mov	r3,r0

000002bc e3a000a1    485 	mov	r0,161

000002c0 eb000000*   486 	bl	BerEncoder_encodeTL

                     487 ;171: 


                     488 ;172: 	// кодируем ссылки


                     489 ;173: 	bufPos = encodeDataSetRefAttrs(outBuf, bufPos, dataSetPos, FALSE);


                     490 

000002c4 e1a0200a    491 	mov	r2,r10

000002c8 e1a01000    492 	mov	r1,r0

000002cc e1a00004    493 	mov	r0,r4

000002d0 e3a03000    494 	mov	r3,0

000002d4 e8bd4cf0    495 	ldmfd	[sp]!,{r4-r7,r10-fp,lr}

000002d8 eaffffa2*   496 	b	encodeDataSetRefAttrs

                     497 	.endf	encodeDataSetAttrs

                     498 	.align	4

                     499 ;allRefsSize	r5	local

                     500 ;accessResultSize	r6	local

                     501 ;invokeIdSize	fp	local

                     502 

                     503 ;invokeId	r7	param

                     504 ;outBuf	r4	param

                     505 ;dataSetPos	r10	param

                     506 

                     507 	.section ".bss","awb"

                     508 .L286:

                     509 	.data

                     510 	.text

                     511 

                     512 ;176: }


                     513 

                     514 ;177: 


                     515 ;178: 


                     516 ;179: int getDataSetAccessAttrs(unsigned int invokeId, 


                     517 	.align	4

                     518 	.align	4

                     519 getDataSetAccessAttrs::

000002dc e92d4030    520 	stmfd	[sp]!,{r4-r5,lr}

                     521 ;180: 	StringView* domainId, StringView* itemId, uint8_t* outBuf)


                     522 ;181: {


                     523 

                     524 ;182:     //=============Находим dataset===============


                     525 ;183:     int dataSetPos = findObjectByFullName(IED_VMD_DATA_SET_SECTION, domainId, itemId);


                     526 

000002e0 e1a05003    527 	mov	r5,r3

000002e4 e1a04000    528 	mov	r4,r0

000002e8 e3a000ee    529 	mov	r0,238

000002ec eb000000*   530 	bl	findObjectByFullName

000002f0 e1a01005    531 	mov	r1,r5

000002f4 e1b02000    532 	movs	r2,r0

                     533 ;184: 	if (dataSetPos == 0)


                     534 

000002f8 e1a00004    535 	mov	r0,r4

                     536 ;187: 			MMS_ERROR_ACCESS_OBJECT_NON_EXISTENT);


                     537 ;188: 	}


                     538 ;189: 



                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2rc1.s
                     539 ;190: 	return encodeDataSetAttrs(invokeId, outBuf, dataSetPos);


                     540 

000002fc 18bd4030    541 	ldmnefd	[sp]!,{r4-r5,lr}

00000300 1affffc2*   542 	bne	encodeDataSetAttrs

                     543 ;185: 	{


                     544 

                     545 ;186: 		return CreateMmsConfirmedErrorPdu(invokeId, outBuf,


                     546 

00000304 e3a02051    547 	mov	r2,81

00000308 e8bd4030    548 	ldmfd	[sp]!,{r4-r5,lr}

0000030c ea000000*   549 	b	CreateMmsConfirmedErrorPdu

                     550 	.endf	getDataSetAccessAttrs

                     551 	.align	4

                     552 ;dataSetPos	r2	local

                     553 

                     554 ;invokeId	r4	param

                     555 ;domainId	none	param

                     556 ;itemId	none	param

                     557 ;outBuf	r5	param

                     558 

                     559 	.section ".bss","awb"

                     560 .L326:

                     561 	.data

                     562 	.text

                     563 

                     564 ;191: }


                     565 

                     566 ;192: 


                     567 ;193: 


                     568 ;194: int mms_handleGetDataSetAccessAttr(MmsConnection* mmsConn,


                     569 	.align	4

                     570 	.align	4

                     571 mms_handleGetDataSetAccessAttr::

00000310 e92d4060    572 	stmfd	[sp]!,{r5-r6,lr}

                     573 ;195:                                 unsigned char* inBuf, int bufPos, int maxBufPos,


                     574 ;196:                                 unsigned int invokeId, unsigned char* response)


                     575 ;197: {


                     576 

                     577 ;198: 	StringView domainId;


                     578 ;199: 	StringView itemId;		


                     579 ;200: 	


                     580 ;201: 	bufPos = BerDecoder_DecodeObjectNameToStringView(inBuf, bufPos, 


                     581 

00000314 e1a0c001    582 	mov	r12,r1

00000318 e1a01002    583 	mov	r1,r2

0000031c e1a02003    584 	mov	r2,r3

00000320 e24dd014    585 	sub	sp,sp,20

00000324 e28d300c    586 	add	r3,sp,12

00000328 e59d5020    587 	ldr	r5,[sp,32]

0000032c e59d6024    588 	ldr	r6,[sp,36]

00000330 e28d0004    589 	add	r0,sp,4

00000334 e58d0000    590 	str	r0,[sp]

00000338 e1a0000c    591 	mov	r0,r12

0000033c eb000000*   592 	bl	BerDecoder_DecodeObjectNameToStringView

                     593 ;202: 		maxBufPos, &domainId, &itemId);


                     594 ;203: 	if (bufPos < 0)


                     595 

00000340 e3500000    596 	cmp	r0,0

00000344 aa000004    597 	bge	.L342

                     598 ;204: 	{


                     599 


                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2rc1.s
                     600 ;205: 		ERROR_REPORT("Unable to find object");


                     601 ;206: 		return CreateMmsConfirmedErrorPdu(invokeId, response,


                     602 

00000348 e1a01006    603 	mov	r1,r6

0000034c e1a00005    604 	mov	r0,r5

00000350 e3a02051    605 	mov	r2,81

00000354 eb000000*   606 	bl	CreateMmsConfirmedErrorPdu

00000358 ea000004    607 	b	.L340

                     608 .L342:

                     609 ;207: 			MMS_ERROR_ACCESS_OBJECT_NON_EXISTENT);


                     610 ;208: 	}	


                     611 ;209: 	


                     612 ;210: 	//TODO Обработать случай когда domainId и itemId не инициализированы


                     613 ;211: 	return getDataSetAccessAttrs(invokeId, &domainId, &itemId, response);


                     614 

0000035c e1a03006    615 	mov	r3,r6

00000360 e28d2004    616 	add	r2,sp,4

00000364 e28d100c    617 	add	r1,sp,12

00000368 e1a00005    618 	mov	r0,r5

0000036c ebffffda*   619 	bl	getDataSetAccessAttrs

                     620 .L340:

00000370 e28dd014    621 	add	sp,sp,20

00000374 e8bd8060    622 	ldmfd	[sp]!,{r5-r6,pc}

                     623 	.endf	mms_handleGetDataSetAccessAttr

                     624 	.align	4

                     625 ;domainId	[sp,12]	local

                     626 ;itemId	[sp,4]	local

                     627 

                     628 ;mmsConn	none	param

                     629 ;inBuf	r12	param

                     630 ;bufPos	none	param

                     631 ;maxBufPos	r4	param

                     632 ;invokeId	r5	param

                     633 ;response	r6	param

                     634 

                     635 	.section ".bss","awb"

                     636 .L374:

                     637 	.data

                     638 	.text

                     639 

                     640 ;212: }


                     641 	.align	4

                     642 .L157:

00000378 00000000*   643 	.data.w	iedModel

                     644 	.type	.L157,$object

                     645 	.size	.L157,4

                     646 

                     647 	.align	4

                     648 ;iedModel	iedModel	import

                     649 

                     650 	.data

                     651 	.ghsnote version,6

                     652 	.ghsnote tools,3

                     653 	.ghsnote options,0

                     654 	.text

                     655 	.align	4

