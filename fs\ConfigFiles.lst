                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3f01.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=ConfigFiles.c -o fs\gh_3f01.o -list=fs/ConfigFiles.lst C:\Users\<USER>\AppData\Local\Temp\gh_3f01.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_3f01.s
Source File: ConfigFiles.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      10 ;		fs/ConfigFiles.c -o fs/ConfigFiles.o

                      11 ;Source File:   fs/ConfigFiles.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Oct 07 09:39:26 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "ConfigFiles.h"


                      21 ;2: #include "../pwin_access.h"


                      22 ;3: #include <debug.h>


                      23 ;4: #include <string.h>


                      24 ;5: #include <stdlib.h>


                      25 ;6: 


                      26 ;7: //Комментарий для проверки кодировки


                      27 ;8: 


                      28 ;9: 


                      29 ;10: // информация о конфигурационном файле


                      30 ;11: typedef struct 


                      31 ;12: {


                      32 ;13: 	//! сигнатура (заполняется пользователем)


                      33 ;14: 	unsigned long signature;


                      34 ;15: 	//! имя файла, отображаемое в файловой системе (заполняется пользователем)


                      35 ;16: 	char fileName[8];


                      36 ;17: 	// указатель, где файл хранится в памяти или NULL если файл не найден


                      37 ;18: 	void *pHeader;


                      38 ;19: 	// указатель на данные


                      39 ;20: 	unsigned char *pData;


                      40 ;21: 	//! атрибуты


                      41 ;22: 	size_t fileSize;


                      42 ;23: 	__time32_t time;	


                      43 ;24: }CfgFileInfo;


                      44 ;25: 


                      45 ;26: 


                      46 ;27: //! количество конфигурационных файлов (автоматически)


                      47 ;28: #define CFG_FILES_COUNT (sizeof(cfgFiles)/sizeof(cfgFiles[0]))


                      48 ;29: 


                      49 ;30: //! icd файл


                      50 ;31: CfgFileInfo icdFile = {'ICDF',"icd.zip" ,NULL}; 



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3f01.s
                      51 ;32: //! cid файл


                      52 ;33: CfgFileInfo  cidFile = {'CIDF', "cid.zip" ,NULL}; 


                      53 ;34: //! все файлы 


                      54 ;35: CfgFileInfo *cfgFiles[] = { &icdFile, &cidFile };


                      55 ;36: 


                      56 ;37: 


                      57 ;38: static FNameErrCode getCfgFileInfo(FSFindData* fileInfo,


                      58 ;39: 	BufferView* bufToWrite)


                      59 ;40: {


                      60 ;41: 	CfgFileInfo *info = NULL;


                      61 ;42: 	if (fileInfo->fileIndex >= CFG_FILES_COUNT)


                      62 ;43: 	{


                      63 ;44: 		return FNAME_NOT_FOUND;


                      64 ;45: 	}


                      65 ;46: 


                      66 ;47: 	info = cfgFiles[fileInfo->fileIndex];


                      67 ;48: 	if (!info || !info->pData)


                      68 ;49: 	{


                      69 ;50: 		return FNAME_NOT_FOUND;


                      70 ;51: 	}


                      71 ;52: 


                      72 ;53: 	fileInfo->attr.fileSize = info->fileSize;


                      73 ;54: 	fileInfo->attr.time = info->time;


                      74 ;55: 	fileInfo->attr.ms = 0;


                      75 ;56: 


                      76 ;57: 	if (!BufferView_writeStr(bufToWrite, info->fileName))


                      77 ;58: 	{


                      78 ;59: 		return FNAME_BUF_ERROR;


                      79 ;60: 	}


                      80 ;61: 	return FNAME_OK;


                      81 ;62: }


                      82 ;63: 


                      83 ;64: //! загружает файл по сигнатуре, всегда успешно


                      84 ;65: static void loadCfgFile(uint32_t signature, CfgFileInfo *info)


                      85 

                      86 ;69: 		&info->fileSize,


                      87 ;70: 		&info->time);


                      88 ;71: }


                      89 

                      90 ;72: 


                      91 ;73: bool CFGFS_init(void)


                      92 ;74: {


                      93 ;75: 	int i;


                      94 ;76: 	// чтение файлов в память, без проверки на результат 


                      95 ;77: 	for (i = 0; i < CFG_FILES_COUNT; ++i)


                      96 ;78: 	{


                      97 ;79: 		loadCfgFile(cfgFiles[i]->signature, cfgFiles[i]);


                      98 ;80: 	}


                      99 ;81: 	return TRUE;


                     100 ;82: }


                     101 ;83: 


                     102 ;84: FNameErrCode CFGFS_findFirst(StringView* startFileName, FSFindData* findData,


                     103 ;85: 	BufferView* fnameBuf)


                     104 ;86: {


                     105 ;87: 	FNameErrCode result;


                     106 ;88: 	


                     107 ;89: 	findData->fileIndex = 0;


                     108 ;90: 


                     109 ;91: 	while (findData->fileIndex < CFG_FILES_COUNT)


                     110 ;92: 	{


                     111 ;93: 		result = getCfgFileInfo(findData, fnameBuf);



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3f01.s
                     112 ;94: 		findData->fileIndex++;


                     113 ;95: 		// файл найден


                     114 ;96: 		if (result == FNAME_OK)


                     115 ;97: 		{


                     116 ;98: 			break;


                     117 ;99: 		}


                     118 ;100: 		


                     119 ;101: 		// файл не найден - ищем дальше


                     120 ;102: 	}


                     121 ;103: 


                     122 ;104: 	return result;


                     123 ;105: }


                     124 ;106: 


                     125 ;107: FNameErrCode CFGFS_findNext(FSFindData* findData, BufferView* fnameBuf)


                     126 ;108: {


                     127 ;109: 	FNameErrCode result;	


                     128 ;110: 	result = getCfgFileInfo(findData, fnameBuf);	


                     129 ;111: 	findData->fileIndex++;


                     130 ;112: 	return result;


                     131 ;113: }


                     132 ;114: 


                     133 ;115: void CFGFS_findClose(FSFindData* findData)


                     134 

                     135 ;117: 	


                     136 ;118: }


                     137 

                     138 ;119: bool CFGFS_openFile(StringView* fileName, FRSM* frsm, FSFileAttr* attr)


                     139 ;120: {	


                     140 ;121: 	CfgFileInfo *info = NULL;


                     141 ;122: 	int i;


                     142 ;123: 


                     143 ;124: 	for (i = 0; i < CFG_FILES_COUNT; ++i)


                     144 ;125: 	{


                     145 ;126: 		if (StringView_cmpCStr(fileName, cfgFiles[i]->fileName) == 0)


                     146 ;127: 		{


                     147 ;128: 			info = cfgFiles[i];


                     148 ;129: 		}


                     149 ;130: 	}


                     150 ;131:     


                     151 ;132: 	// по идее такого быть не должно, т.к. если файл не найден - он не должен запрашиваться


                     152 ;133: 	if (!info || !info->pData)


                     153 ;134: 	{


                     154 ;135: 		return FALSE;


                     155 ;136: 	}


                     156 ;137: 


                     157 ;138: 	frsm->start = info->pData;


                     158 ;139: 	frsm->size = info->fileSize;


                     159 ;140: 	frsm->pos = 0;


                     160 ;141: 


                     161 ;142: 	attr->fileSize = info->fileSize;


                     162 ;143: 	attr->time = info->time;


                     163 ;144: 	attr->ms = 0;


                     164 ;145: 


                     165 ;146: 	return TRUE;


                     166 ;147: }


                     167 ;148: 


                     168 ;149: bool CFGFS_closeFile(FRSM* frsm)


                     169 

                     170 ;152: }


                     171 

                     172 	.text


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3f01.s
                     173 	.align	4

                     174 getCfgFileInfo:

00000000 e92d4000    175 	stmfd	[sp]!,{lr}

00000004 e1a02001    176 	mov	r2,r1

00000008 e5901004    177 	ldr	r1,[r0,4]

0000000c e3510002    178 	cmp	r1,2

00000010 2a000005    179 	bhs	.L59

00000014 e59f31ec*   180 	ldr	r3,.L176

00000018 e7931101    181 	ldr	r1,[r3,r1 lsl 2]

0000001c e3510000    182 	cmp	r1,0

00000020 15913010    183 	ldrne	r3,[r1,16]

00000024 13530000    184 	cmpne	r3,0

00000028 1a000001    185 	bne	.L58

                     186 .L59:

0000002c e3a00001    187 	mov	r0,1

00000030 ea00000a    188 	b	.L53

                     189 .L58:

00000034 e5913014    190 	ldr	r3,[r1,20]

00000038 e5a03014    191 	str	r3,[r0,20]!

0000003c e5913018    192 	ldr	r3,[r1,24]

00000040 e3a0c000    193 	mov	r12,0

00000044 e9801008    194 	stmfa	[r0],{r3,r12}

00000048 e2811004    195 	add	r1,r1,4

0000004c e1a00002    196 	mov	r0,r2

00000050 eb000000*   197 	bl	BufferView_writeStr

00000054 e3500000    198 	cmp	r0,0

00000058 13a00000    199 	movne	r0,0

0000005c 03a00002    200 	moveq	r0,2

                     201 .L53:

00000060 e8bd4000    202 	ldmfd	[sp]!,{lr}

00000064 e12fff1e*   203 	ret	

                     204 	.endf	getCfgFileInfo

                     205 	.align	4

                     206 ;info	r1	local

                     207 

                     208 ;fileInfo	r0	param

                     209 ;bufToWrite	r2	param

                     210 

                     211 	.data

                     212 .L153:

                     213 	.text

                     214 

                     215 

                     216 	.align	4

                     217 	.align	4

                     218 CFGFS_init::

00000068 e92d4030    219 	stmfd	[sp]!,{r4-r5,lr}

0000006c e59f5194*   220 	ldr	r5,.L176

00000070 e3a04000    221 	mov	r4,0

00000074 e5951000    222 	ldr	r1,[r5]

00000078 e1a02004    223 	mov	r2,r4

0000007c e491000c    224 	ldr	r0,[r1],12

00000080 e8810014    225 	stmea	[r1],{r2,r4}

00000084 e2813008    226 	add	r3,r1,8

00000088 e281200c    227 	add	r2,r1,12

0000008c e52d2004    228 	str	r2,[sp,-4]!

00000090 e2812004    229 	add	r2,r1,4

00000094 eb000000*   230 	bl	loadRomModule

00000098 e5951004    231 	ldr	r1,[r5,4]

0000009c e1a05004    232 	mov	r5,r4

000000a0 e491000c    233 	ldr	r0,[r1],12


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3f01.s
000000a4 e8810030    234 	stmea	[r1],{r4-r5}

000000a8 e2813008    235 	add	r3,r1,8

000000ac e281200c    236 	add	r2,r1,12

000000b0 e58d2000    237 	str	r2,[sp]

000000b4 e2812004    238 	add	r2,r1,4

000000b8 eb000000*   239 	bl	loadRomModule

000000bc e3a00001    240 	mov	r0,1

000000c0 e28dd004    241 	add	sp,sp,4

000000c4 e8bd8030    242 	ldmfd	[sp]!,{r4-r5,pc}

                     243 	.endf	CFGFS_init

                     244 	.align	4

                     245 

                     246 	.section ".bss","awb"

                     247 .L245:

                     248 	.data

                     249 	.text

                     250 

                     251 

                     252 	.align	4

                     253 	.align	4

                     254 CFGFS_findFirst::

000000c8 e92d4030    255 	stmfd	[sp]!,{r4-r5,lr}

000000cc e1a05002    256 	mov	r5,r2

000000d0 e1a04001    257 	mov	r4,r1

000000d4 e3a03000    258 	mov	r3,0

000000d8 e5843004    259 	str	r3,[r4,4]

                     260 .L256:

000000dc e1a01005    261 	mov	r1,r5

000000e0 e1a00004    262 	mov	r0,r4

000000e4 ebffffc5*   263 	bl	getCfgFileInfo

000000e8 e5943004    264 	ldr	r3,[r4,4]

000000ec e3500000    265 	cmp	r0,0

000000f0 e2833001    266 	add	r3,r3,1

000000f4 e5843004    267 	str	r3,[r4,4]

000000f8 13530002    268 	cmpne	r3,2

000000fc 3afffff6    269 	blo	.L256

00000100 e8bd8030    270 	ldmfd	[sp]!,{r4-r5,pc}

                     271 	.endf	CFGFS_findFirst

                     272 	.align	4

                     273 ;result	r0	local

                     274 

                     275 ;startFileName	none	param

                     276 ;findData	r4	param

                     277 ;fnameBuf	r5	param

                     278 

                     279 	.section ".bss","awb"

                     280 .L320:

                     281 	.data

                     282 	.text

                     283 

                     284 

                     285 	.align	4

                     286 	.align	4

                     287 CFGFS_findNext::

00000104 e92d4010    288 	stmfd	[sp]!,{r4,lr}

00000108 e1a04000    289 	mov	r4,r0

0000010c ebffffbb*   290 	bl	getCfgFileInfo

00000110 e5941004    291 	ldr	r1,[r4,4]

00000114 e2811001    292 	add	r1,r1,1

00000118 e5841004    293 	str	r1,[r4,4]

0000011c e8bd8010    294 	ldmfd	[sp]!,{r4,pc}


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3f01.s
                     295 	.endf	CFGFS_findNext

                     296 	.align	4

                     297 

                     298 ;findData	r4	param

                     299 ;fnameBuf	none	param

                     300 

                     301 	.section ".bss","awb"

                     302 .L366:

                     303 	.data

                     304 	.text

                     305 

                     306 

                     307 	.align	4

                     308 	.align	4

                     309 CFGFS_openFile::

00000120 e92d44f0    310 	stmfd	[sp]!,{r4-r7,r10,lr}

00000124 e3a04000    311 	mov	r4,0

00000128 e59f50d8*   312 	ldr	r5,.L176

0000012c e1a06001    313 	mov	r6,r1

00000130 e5951000    314 	ldr	r1,[r5]

00000134 e1a07002    315 	mov	r7,r2

00000138 e2811004    316 	add	r1,r1,4

0000013c e1a0a000    317 	mov	r10,r0

00000140 eb000000*   318 	bl	StringView_cmpCStr

00000144 e3500000    319 	cmp	r0,0

00000148 e5950004    320 	ldr	r0,[r5,4]

0000014c 05954000    321 	ldreq	r4,[r5]

00000150 e2801004    322 	add	r1,r0,4

00000154 e1a0000a    323 	mov	r0,r10

00000158 eb000000*   324 	bl	StringView_cmpCStr

0000015c e3500000    325 	cmp	r0,0

00000160 05954004    326 	ldreq	r4,[r5,4]

00000164 e3540000    327 	cmp	r4,0

00000168 15940010    328 	ldrne	r0,[r4,16]

0000016c 13500000    329 	cmpne	r0,0

00000170 03a00000    330 	moveq	r0,0

00000174 0a000007    331 	beq	.L373

00000178 e5a6000c    332 	str	r0,[r6,12]!

0000017c e5942014    333 	ldr	r2,[r4,20]

00000180 e3a01000    334 	mov	r1,0

00000184 e9860006    335 	stmfa	[r6],{r1-r2}

00000188 e5940018    336 	ldr	r0,[r4,24]

0000018c e5872000    337 	str	r2,[r7]

00000190 e9870003    338 	stmfa	[r7],{r0-r1}

00000194 e3a00001    339 	mov	r0,1

                     340 .L373:

00000198 e8bd84f0    341 	ldmfd	[sp]!,{r4-r7,r10,pc}

                     342 	.endf	CFGFS_openFile

                     343 	.align	4

                     344 ;info	r4	local

                     345 

                     346 ;fileName	r10	param

                     347 ;frsm	r6	param

                     348 ;attr	r7	param

                     349 

                     350 	.section ".bss","awb"

                     351 .L522:

                     352 	.data

                     353 	.text

                     354 

                     355 


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3f01.s
                     356 ;153: 


                     357 ;154: bool CFGFS_readFile(FRSM* frsm, BufferView* readBuf, bool* moreFollows)


                     358 	.align	4

                     359 	.align	4

                     360 CFGFS_readFile::

0000019c e92d4030    361 	stmfd	[sp]!,{r4-r5,lr}

                     362 ;155: {


                     363 

                     364 ;156: 	size_t readCount = frsm->size - frsm->pos;


                     365 

000001a0 e1a03001    366 	mov	r3,r1

000001a4 e1a04000    367 	mov	r4,r0

000001a8 e5941014    368 	ldr	r1,[r4,20]

000001ac e5940010    369 	ldr	r0,[r4,16]

000001b0 e1a05002    370 	mov	r5,r2

000001b4 e0512000    371 	subs	r2,r1,r0

                     372 ;157: 


                     373 ;158: 	if (readCount == 0)


                     374 

                     375 ;159: 	{


                     376 

                     377 ;160: 		return FALSE;


                     378 

000001b8 020200ff    379 	andeq	r0,r2,255

000001bc 0a00000d    380 	beq	.L545

                     381 ;161: 	}


                     382 ;162: 


                     383 ;163: 	readCount = BufferView_writeData(readBuf,


                     384 

000001c0 e5b4100c    385 	ldr	r1,[r4,12]!

000001c4 e0801001    386 	add	r1,r0,r1

000001c8 e1a00003    387 	mov	r0,r3

000001cc eb000000*   388 	bl	BufferView_writeData

                     389 ;164: 		frsm->start + frsm->pos, readCount);


                     390 ;165: 	*moreFollows = (readCount < frsm->size - frsm->pos);


                     391 

000001d0 e994000c    392 	ldmed	[r4],{r2-r3}

000001d4 e0431002    393 	sub	r1,r3,r2

000001d8 e1510000    394 	cmp	r1,r0

000001dc 83a01001    395 	movhi	r1,1

000001e0 93a01000    396 	movls	r1,0

000001e4 e5c51000    397 	strb	r1,[r5]

                     398 ;166: 	frsm->pos += readCount;


                     399 

000001e8 e5941004    400 	ldr	r1,[r4,4]

000001ec e0811000    401 	add	r1,r1,r0

000001f0 e5841004    402 	str	r1,[r4,4]

                     403 ;167: 	return TRUE;


                     404 

000001f4 e3a00001    405 	mov	r0,1

                     406 .L545:

000001f8 e8bd8030    407 	ldmfd	[sp]!,{r4-r5,pc}

                     408 	.endf	CFGFS_readFile

                     409 	.align	4

                     410 ;readCount	r2	local

                     411 

                     412 ;frsm	r4	param

                     413 ;readBuf	r3	param

                     414 ;moreFollows	r5	param

                     415 

                     416 	.section ".bss","awb"


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3f01.s
                     417 .L597:

                     418 	.data

                     419 	.text

                     420 

                     421 ;168: }


                     422 	.align	4

                     423 	.align	4

                     424 CFGFS_findClose::

                     425 ;116: {


                     426 

000001fc e12fff1e*   427 	ret	

                     428 	.endf	CFGFS_findClose

                     429 	.align	4

                     430 

                     431 ;findData	none	param

                     432 

                     433 	.section ".bss","awb"

                     434 .L638:

                     435 	.data

                     436 	.text

                     437 	.align	4

                     438 	.align	4

                     439 CFGFS_closeFile::

                     440 ;150: {


                     441 

                     442 ;151: 	return TRUE;


                     443 

00000200 e3a00001    444 	mov	r0,1

00000204 e12fff1e*   445 	ret	

                     446 	.endf	CFGFS_closeFile

                     447 	.align	4

                     448 

                     449 ;frsm	none	param

                     450 

                     451 	.section ".bss","awb"

                     452 .L670:

                     453 	.data

                     454 	.text

                     455 	.align	4

                     456 .L176:

00000208 00000000*   457 	.data.w	cfgFiles

                     458 	.type	.L176,$object

                     459 	.size	.L176,4

                     460 

                     461 	.align	4

                     462 

                     463 	.data

                     464 .L692:

                     465 	.globl	icdFile

00000000 49434446    466 icdFile:	.data.b	70,68,67,73

                     467 ;	"icd.zip\000"

00000004 2e646369    468 	.data.b	105,99,100,46

00000008 0070697a    469 	.data.b	122,105,112,0

0000000c 00000000    470 	.space	4

00000010 00000000    471 	.space	4

00000014 00000000    472 	.space	4

00000018 00000000    473 	.space	4

                     474 	.type	icdFile,$object

                     475 	.size	icdFile,28

                     476 .L693:

                     477 	.globl	cidFile


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3f01.s
0000001c 43494446    478 cidFile:	.data.b	70,68,73,67

                     479 ;	"cid.zip\000"

00000020 2e646963    480 	.data.b	99,105,100,46

00000024 0070697a    481 	.data.b	122,105,112,0

00000028 00000000    482 	.space	4

0000002c 00000000    483 	.space	4

00000030 00000000    484 	.space	4

00000034 00000000    485 	.space	4

                     486 	.type	cidFile,$object

                     487 	.size	cidFile,28

                     488 .L694:

                     489 	.globl	cfgFiles

00000038 00000000*   490 cfgFiles:	.data.w	.L692

0000003c 00000000*   491 	.data.w	.L693

                     492 	.type	cfgFiles,$object

                     493 	.size	cfgFiles,8

                     494 	.ghsnote version,6

                     495 	.ghsnote tools,3

                     496 	.ghsnote options,0

                     497 	.text

                     498 	.align	4

                     499 	.data

                     500 	.align	4

                     501 	.text

