                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_geg1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=fast_memcpy.c -o gh_geg1.o -list=fast_memcpy.lst C:\Users\<USER>\AppData\Local\Temp\gh_geg1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_geg1.s
Source File: fast_memcpy.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile fast_memcpy.c

                      10 ;		-o fast_memcpy.o

                      11 ;Source File:   fast_memcpy.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:55:50 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include <stdint.h>


                      21 ;2: #include <stddef.h>


                      22 ;3: #include "fast_memcpy.h"


                      23 ;4: 


                      24 ;5: // функция выдрана почти без переделки из lvgl


                      25 ;6: // копирует по 4 байта.


                      26 ;7: // если данные не выровненные - по байту или


                      27 ;8: // если их можно выровнять - выравнивание и затем по 4 


                      28 ;9: 


                      29 ;10: #define COPY32 *d32 = *s32; d32++; s32++;


                      30 ;11: #define COPY8 *d8 = *s8; d8++; s8++;


                      31 ;12: #define SET32(x) *d32 = x; d32++;


                      32 ;13: #define REPEAT8(expr) expr expr expr expr expr expr expr expr


                      33 ;14: 


                      34 ;15: #define ALIGN_MASK  0x3


                      35 ;16: void* fast_memcpy(void * dst, const void * src, size_t len)


                      36 	.text

                      37 	.align	4

                      38 fast_memcpy::

00000000 e92d0030     39 	stmfd	[sp]!,{r4-r5}

                      40 ;17: {


                      41 

                      42 ;18:     uint8_t * d8 = dst;


                      43 

00000004 e1a03000     44 	mov	r3,r0

                      45 ;19:     const uint8_t * s8 = src;


                      46 

                      47 ;20: 


                      48 ;21:     uintptr_t d_align = (uintptr_t)d8 & ALIGN_MASK;


                      49 

00000008 e203c003     50 	and	r12,r3,3


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_geg1.s
                      51 ;22:     uintptr_t s_align = (uintptr_t)s8 & ALIGN_MASK;


                      52 

0000000c e2014003     53 	and	r4,r1,3

                      54 ;23: 


                      55 ;24:     /*Byte copy for unaligned memories*/


                      56 ;25:     if(s_align != d_align) {


                      57 

00000010 e154000c     58 	cmp	r4,r12

00000014 0a000060     59 	beq	.L4

00000018 e3520020     60 	cmp	r2,32

0000001c 9a000042     61 	bls	.L57

                      62 .L8:

                      63 ;26:         while(len > 32) {


                      64 

                      65 ;27:             REPEAT8(COPY8);


                      66 

00000020 e4d1c001     67 	ldrb	r12,[r1],1

00000024 e4c3c001     68 	strb	r12,[r3],1

00000028 e4d1c001     69 	ldrb	r12,[r1],1

0000002c e4c3c001     70 	strb	r12,[r3],1

00000030 e4d1c001     71 	ldrb	r12,[r1],1

00000034 e4c3c001     72 	strb	r12,[r3],1

00000038 e4d1c001     73 	ldrb	r12,[r1],1

0000003c e4c3c001     74 	strb	r12,[r3],1

00000040 e4d1c001     75 	ldrb	r12,[r1],1

00000044 e4c3c001     76 	strb	r12,[r3],1

00000048 e4d1c001     77 	ldrb	r12,[r1],1

0000004c e4c3c001     78 	strb	r12,[r3],1

00000050 e4d1c001     79 	ldrb	r12,[r1],1

00000054 e4c3c001     80 	strb	r12,[r3],1

00000058 e4d1c001     81 	ldrb	r12,[r1],1

0000005c e4c3c001     82 	strb	r12,[r3],1

                      83 ;28:             REPEAT8(COPY8);


                      84 

00000060 e4d1c001     85 	ldrb	r12,[r1],1

00000064 e4c3c001     86 	strb	r12,[r3],1

00000068 e4d1c001     87 	ldrb	r12,[r1],1

0000006c e4c3c001     88 	strb	r12,[r3],1

00000070 e4d1c001     89 	ldrb	r12,[r1],1

00000074 e4c3c001     90 	strb	r12,[r3],1

00000078 e4d1c001     91 	ldrb	r12,[r1],1

0000007c e4c3c001     92 	strb	r12,[r3],1

00000080 e4d1c001     93 	ldrb	r12,[r1],1

00000084 e4c3c001     94 	strb	r12,[r3],1

00000088 e4d1c001     95 	ldrb	r12,[r1],1

0000008c e4c3c001     96 	strb	r12,[r3],1

00000090 e4d1c001     97 	ldrb	r12,[r1],1

00000094 e4c3c001     98 	strb	r12,[r3],1

00000098 e4d1c001     99 	ldrb	r12,[r1],1

0000009c e4c3c001    100 	strb	r12,[r3],1

                     101 ;29:             REPEAT8(COPY8);


                     102 

000000a0 e4d1c001    103 	ldrb	r12,[r1],1

000000a4 e4c3c001    104 	strb	r12,[r3],1

000000a8 e4d1c001    105 	ldrb	r12,[r1],1

000000ac e4c3c001    106 	strb	r12,[r3],1

000000b0 e4d1c001    107 	ldrb	r12,[r1],1

000000b4 e4c3c001    108 	strb	r12,[r3],1

000000b8 e4d1c001    109 	ldrb	r12,[r1],1

000000bc e4c3c001    110 	strb	r12,[r3],1

000000c0 e4d1c001    111 	ldrb	r12,[r1],1


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_geg1.s
000000c4 e4c3c001    112 	strb	r12,[r3],1

000000c8 e4d1c001    113 	ldrb	r12,[r1],1

000000cc e4c3c001    114 	strb	r12,[r3],1

000000d0 e4d1c001    115 	ldrb	r12,[r1],1

000000d4 e4c3c001    116 	strb	r12,[r3],1

000000d8 e4d1c001    117 	ldrb	r12,[r1],1

000000dc e4c3c001    118 	strb	r12,[r3],1

                     119 ;30:             REPEAT8(COPY8);


                     120 

000000e0 e4d1c001    121 	ldrb	r12,[r1],1

000000e4 e4c3c001    122 	strb	r12,[r3],1

000000e8 e4d1c001    123 	ldrb	r12,[r1],1

000000ec e4c3c001    124 	strb	r12,[r3],1

000000f0 e4d1c001    125 	ldrb	r12,[r1],1

000000f4 e4c3c001    126 	strb	r12,[r3],1

000000f8 e4d1c001    127 	ldrb	r12,[r1],1

000000fc e4c3c001    128 	strb	r12,[r3],1

00000100 e4d1c001    129 	ldrb	r12,[r1],1

00000104 e4c3c001    130 	strb	r12,[r3],1

00000108 e4d1c001    131 	ldrb	r12,[r1],1

0000010c e4c3c001    132 	strb	r12,[r3],1

00000110 e4d1c001    133 	ldrb	r12,[r1],1

00000114 e2422020    134 	sub	r2,r2,32

00000118 e4c3c001    135 	strb	r12,[r3],1

0000011c e4d1c001    136 	ldrb	r12,[r1],1

00000120 e3520020    137 	cmp	r2,32

00000124 e4c3c001    138 	strb	r12,[r3],1

                     139 ;31:             len -= 32;


                     140 

00000128 8affffbc    141 	bhi	.L8

                     142 .L57:

0000012c e1a04002    143 	mov	r4,r2

00000130 e1b0c1a4    144 	movs	r12,r4 lsr 3

00000134 0a000011    145 	beq	.L133

                     146 .L149:

00000138 e4d12001    147 	ldrb	r2,[r1],1

0000013c e4c32001    148 	strb	r2,[r3],1

00000140 e4d12001    149 	ldrb	r2,[r1],1

00000144 e4c32001    150 	strb	r2,[r3],1

00000148 e4d12001    151 	ldrb	r2,[r1],1

0000014c e4c32001    152 	strb	r2,[r3],1

00000150 e4d12001    153 	ldrb	r2,[r1],1

00000154 e4c32001    154 	strb	r2,[r3],1

00000158 e4d12001    155 	ldrb	r2,[r1],1

0000015c e4c32001    156 	strb	r2,[r3],1

00000160 e4d12001    157 	ldrb	r2,[r1],1

00000164 e4c32001    158 	strb	r2,[r3],1

00000168 e4d12001    159 	ldrb	r2,[r1],1

0000016c e4c32001    160 	strb	r2,[r3],1

00000170 e4d12001    161 	ldrb	r2,[r1],1

00000174 e25cc001    162 	subs	r12,r12,1

00000178 e4c32001    163 	strb	r2,[r3],1

0000017c 1affffed    164 	bne	.L149

                     165 .L133:

00000180 e214c007    166 	ands	r12,r4,7

00000184 0a0000a9    167 	beq	.L2

                     168 .L153:

00000188 e4d12001    169 	ldrb	r2,[r1],1

0000018c e25cc001    170 	subs	r12,r12,1

00000190 e4c32001    171 	strb	r2,[r3],1

00000194 1afffffb    172 	bne	.L153


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_geg1.s
00000198 ea0000a4    173 	b	.L2

                     174 .L4:

                     175 ;36:         }


                     176 ;37:         return dst;


                     177 

                     178 ;38:     }


                     179 ;39: 


                     180 ;40: 


                     181 ;41:     /*Make the memories aligned*/


                     182 ;42:     if(d_align) {


                     183 

0000019c e35c0000    184 	cmp	r12,0

                     185 ;43:         d_align = ALIGN_MASK + 1 - d_align;


                     186 

000001a0 126cc004    187 	rsbne	r12,r12,4

                     188 ;44:         while(d_align && len) {


                     189 

000001a4 135c0000    190 	cmpne	r12,0

000001a8 13520000    191 	cmpne	r2,0

000001ac 0a000006    192 	beq	.L13

                     193 .L17:

                     194 ;45:             COPY8;


                     195 

000001b0 e4d14001    196 	ldrb	r4,[r1],1

000001b4 e2422001    197 	sub	r2,r2,1

000001b8 e4c34001    198 	strb	r4,[r3],1

                     199 ;46:             d_align--;


                     200 

000001bc e24cc001    201 	sub	r12,r12,1

                     202 ;47:             len--;


                     203 

000001c0 e35c0000    204 	cmp	r12,0

000001c4 13520000    205 	cmpne	r2,0

000001c8 1afffff8    206 	bne	.L17

                     207 .L13:

                     208 ;48:         }


                     209 ;49:     }


                     210 ;50: 	


                     211 ;51: 	{


                     212 

                     213 ;52: 		uint32_t * d32 = (uint32_t *)d8;


                     214 

                     215 ;53: 		const uint32_t * s32 = (uint32_t *)s8;


                     216 

                     217 ;54: 		while(len > 32) {


                     218 

000001cc e262c021    219 	rsb	r12,r2,33

000001d0 e26cc000    220 	rsb	r12,r12,0

000001d4 e1a0c2cc    221 	mov	r12,r12 asr 5

000001d8 e29c4001    222 	adds	r4,r12,1

000001dc 43a04000    223 	movmi	r4,0

000001e0 e1b0c124    224 	movs	r12,r4 lsr 2

000001e4 0a000042    225 	beq	.L202

000001e8 e042238c    226 	sub	r2,r2,r12 lsl 7

                     227 .L210:

000001ec e4915004    228 	ldr	r5,[r1],4

000001f0 e4835004    229 	str	r5,[r3],4

000001f4 e4915004    230 	ldr	r5,[r1],4

000001f8 e4835004    231 	str	r5,[r3],4

000001fc e4915004    232 	ldr	r5,[r1],4

00000200 e4835004    233 	str	r5,[r3],4


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_geg1.s
00000204 e4915004    234 	ldr	r5,[r1],4

00000208 e4835004    235 	str	r5,[r3],4

0000020c e4915004    236 	ldr	r5,[r1],4

00000210 e4835004    237 	str	r5,[r3],4

00000214 e4915004    238 	ldr	r5,[r1],4

00000218 e4835004    239 	str	r5,[r3],4

0000021c e4915004    240 	ldr	r5,[r1],4

00000220 e4835004    241 	str	r5,[r3],4

00000224 e4915004    242 	ldr	r5,[r1],4

00000228 e4835004    243 	str	r5,[r3],4

0000022c e4915004    244 	ldr	r5,[r1],4

00000230 e4835004    245 	str	r5,[r3],4

00000234 e4915004    246 	ldr	r5,[r1],4

00000238 e4835004    247 	str	r5,[r3],4

0000023c e4915004    248 	ldr	r5,[r1],4

00000240 e4835004    249 	str	r5,[r3],4

00000244 e4915004    250 	ldr	r5,[r1],4

00000248 e4835004    251 	str	r5,[r3],4

0000024c e4915004    252 	ldr	r5,[r1],4

00000250 e4835004    253 	str	r5,[r3],4

00000254 e4915004    254 	ldr	r5,[r1],4

00000258 e4835004    255 	str	r5,[r3],4

0000025c e4915004    256 	ldr	r5,[r1],4

00000260 e4835004    257 	str	r5,[r3],4

00000264 e4915004    258 	ldr	r5,[r1],4

00000268 e4835004    259 	str	r5,[r3],4

0000026c e4915004    260 	ldr	r5,[r1],4

00000270 e4835004    261 	str	r5,[r3],4

00000274 e4915004    262 	ldr	r5,[r1],4

00000278 e4835004    263 	str	r5,[r3],4

0000027c e4915004    264 	ldr	r5,[r1],4

00000280 e4835004    265 	str	r5,[r3],4

00000284 e4915004    266 	ldr	r5,[r1],4

00000288 e4835004    267 	str	r5,[r3],4

0000028c e4915004    268 	ldr	r5,[r1],4

00000290 e4835004    269 	str	r5,[r3],4

00000294 e4915004    270 	ldr	r5,[r1],4

00000298 e4835004    271 	str	r5,[r3],4

0000029c e4915004    272 	ldr	r5,[r1],4

000002a0 e4835004    273 	str	r5,[r3],4

000002a4 e4915004    274 	ldr	r5,[r1],4

000002a8 e4835004    275 	str	r5,[r3],4

000002ac e4915004    276 	ldr	r5,[r1],4

000002b0 e4835004    277 	str	r5,[r3],4

000002b4 e4915004    278 	ldr	r5,[r1],4

000002b8 e4835004    279 	str	r5,[r3],4

000002bc e4915004    280 	ldr	r5,[r1],4

000002c0 e4835004    281 	str	r5,[r3],4

000002c4 e4915004    282 	ldr	r5,[r1],4

000002c8 e4835004    283 	str	r5,[r3],4

000002cc e4915004    284 	ldr	r5,[r1],4

000002d0 e4835004    285 	str	r5,[r3],4

000002d4 e4915004    286 	ldr	r5,[r1],4

000002d8 e4835004    287 	str	r5,[r3],4

000002dc e4915004    288 	ldr	r5,[r1],4

000002e0 e4835004    289 	str	r5,[r3],4

000002e4 e4915004    290 	ldr	r5,[r1],4

000002e8 e25cc001    291 	subs	r12,r12,1

000002ec e4835004    292 	str	r5,[r3],4

000002f0 1affffbd    293 	bne	.L210

                     294 .L202:


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_geg1.s
000002f4 e214c003    295 	ands	r12,r4,3

000002f8 0a000012    296 	beq	.L58

000002fc e042228c    297 	sub	r2,r2,r12 lsl 5

                     298 .L214:

00000300 e4914004    299 	ldr	r4,[r1],4

00000304 e4834004    300 	str	r4,[r3],4

00000308 e4914004    301 	ldr	r4,[r1],4

0000030c e4834004    302 	str	r4,[r3],4

00000310 e4914004    303 	ldr	r4,[r1],4

00000314 e4834004    304 	str	r4,[r3],4

00000318 e4914004    305 	ldr	r4,[r1],4

0000031c e4834004    306 	str	r4,[r3],4

00000320 e4914004    307 	ldr	r4,[r1],4

00000324 e4834004    308 	str	r4,[r3],4

00000328 e4914004    309 	ldr	r4,[r1],4

0000032c e4834004    310 	str	r4,[r3],4

00000330 e4914004    311 	ldr	r4,[r1],4

00000334 e4834004    312 	str	r4,[r3],4

00000338 e4914004    313 	ldr	r4,[r1],4

0000033c e25cc001    314 	subs	r12,r12,1

00000340 e4834004    315 	str	r4,[r3],4

00000344 1affffed    316 	bne	.L214

                     317 .L58:

00000348 e262c005    318 	rsb	r12,r2,5

0000034c e26cc000    319 	rsb	r12,r12,0

00000350 e1a0c14c    320 	mov	r12,r12 asr 2

00000354 e29c4001    321 	adds	r4,r12,1

00000358 43a04000    322 	movmi	r4,0

0000035c e1b0c1a4    323 	movs	r12,r4 lsr 3

00000360 0a000012    324 	beq	.L156

00000364 e042228c    325 	sub	r2,r2,r12 lsl 5

                     326 .L172:

00000368 e4915004    327 	ldr	r5,[r1],4

0000036c e4835004    328 	str	r5,[r3],4

00000370 e4915004    329 	ldr	r5,[r1],4

00000374 e4835004    330 	str	r5,[r3],4

00000378 e4915004    331 	ldr	r5,[r1],4

0000037c e4835004    332 	str	r5,[r3],4

00000380 e4915004    333 	ldr	r5,[r1],4

00000384 e4835004    334 	str	r5,[r3],4

00000388 e4915004    335 	ldr	r5,[r1],4

0000038c e4835004    336 	str	r5,[r3],4

00000390 e4915004    337 	ldr	r5,[r1],4

00000394 e4835004    338 	str	r5,[r3],4

00000398 e4915004    339 	ldr	r5,[r1],4

0000039c e4835004    340 	str	r5,[r3],4

000003a0 e4915004    341 	ldr	r5,[r1],4

000003a4 e25cc001    342 	subs	r12,r12,1

000003a8 e4835004    343 	str	r5,[r3],4

000003ac 1affffed    344 	bne	.L172

                     345 .L156:

000003b0 e214c007    346 	ands	r12,r4,7

000003b4 1042210c    347 	subne	r2,r2,r12 lsl 2

                     348 .L176:

000003b8 14914004    349 	ldrne	r4,[r1],4

000003bc 14834004    350 	strne	r4,[r3],4

000003c0 125cc001    351 	subnes	r12,r12,1

000003c4 1afffffb    352 	bne	.L176

                     353 .L23:

                     354 ;62: 		}


                     355 ;63: 	



                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_geg1.s
                     356 ;64: 		d8 = (uint8_t *)d32;


                     357 

                     358 ;65: 		s8 = (const uint8_t *)s32;


                     359 

                     360 ;66: 		while(len) {


                     361 

000003c8 e1a04002    362 	mov	r4,r2

000003cc e1b0c1a4    363 	movs	r12,r4 lsr 3

000003d0 0a000011    364 	beq	.L179

                     365 .L195:

000003d4 e4d12001    366 	ldrb	r2,[r1],1

000003d8 e4c32001    367 	strb	r2,[r3],1

000003dc e4d12001    368 	ldrb	r2,[r1],1

000003e0 e4c32001    369 	strb	r2,[r3],1

000003e4 e4d12001    370 	ldrb	r2,[r1],1

000003e8 e4c32001    371 	strb	r2,[r3],1

000003ec e4d12001    372 	ldrb	r2,[r1],1

000003f0 e4c32001    373 	strb	r2,[r3],1

000003f4 e4d12001    374 	ldrb	r2,[r1],1

000003f8 e4c32001    375 	strb	r2,[r3],1

000003fc e4d12001    376 	ldrb	r2,[r1],1

00000400 e4c32001    377 	strb	r2,[r3],1

00000404 e4d12001    378 	ldrb	r2,[r1],1

00000408 e4c32001    379 	strb	r2,[r3],1

0000040c e4d12001    380 	ldrb	r2,[r1],1

00000410 e25cc001    381 	subs	r12,r12,1

00000414 e4c32001    382 	strb	r2,[r3],1

00000418 1affffed    383 	bne	.L195

                     384 .L179:

0000041c e214c007    385 	ands	r12,r4,7

                     386 .L199:

00000420 14d12001    387 	ldrneb	r2,[r1],1

00000424 14c32001    388 	strneb	r2,[r3],1

00000428 125cc001    389 	subnes	r12,r12,1

0000042c 1afffffb    390 	bne	.L199

                     391 .L26:

                     392 ;69: 		}


                     393 ;70: 	}


                     394 ;71: 


                     395 ;72:     return dst;


                     396 

                     397 .L2:

00000430 e8bd0030    398 	ldmfd	[sp]!,{r4-r5}

00000434 e12fff1e*   399 	ret	

                     400 	.endf	fast_memcpy

                     401 	.align	4

                     402 ;d8	r3	local

                     403 ;s8	r1	local

                     404 ;d_align	r12	local

                     405 ;s_align	r4	local

                     406 ;d32	r3	local

                     407 ;s32	r1	local

                     408 

                     409 ;dst	r0	param

                     410 ;src	r1	param

                     411 ;len	r2	param

                     412 

                     413 	.section ".bss","awb"

                     414 .L807:

                     415 	.data

                     416 	.text


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_geg1.s
                     417 

                     418 ;73: } 


                     419 	.align	4

                     420 

                     421 	.data

                     422 	.ghsnote version,6

                     423 	.ghsnote tools,3

                     424 	.ghsnote options,0

                     425 	.text

                     426 	.align	4

