#pragma once

#include "RptItemTypes.h"
#include "../iedTree/iedEntity.h"


//! Тип функции которая вызывается начальной инициализации значения (старого)
typedef void (*RptItem_InitValue)(RptItem item);


//! Тип функции которая вызывается для обнаружения изменений в элементе
typedef void (*RptItem_UpdateChanges)(RptItem item);

//! Тип функции для чтения и кодирования данных в буфер
typedef bool (*RptItem_EncodeRead)(RptItem item, BufferView* outBuf);

//! Тип функции для определения размера данных при кодировании
typedef bool (*RptItem_СalcReadLen)(RptItem item, size_t* pLen );

//! Тип функции для копирования новых данных поверх старых
typedef void (*RptItem_OverwriteOld)(RptItem item);


struct RptItemBehavior
{
    RptItem_InitValue initValue;
    RptItem_UpdateChanges updateChanges;
    RptItem_EncodeRead encodeRead;
    RptItem_СalcReadLen calcReadLen;
    RptItem_OverwriteOld overwriteOld;
};

struct  RptItemStruct {    
    RptItem nextSibling;
    struct RptItemBehavior* behaviour;

    //! ========== Только для FinalDA
    //! Указатель на объект IEDTree
    IEDEntity iedObj;
    //! Указатель на элемент DataSet, соответствующий данному элементу отчёта
    RptDataSetItem rptDsItem;
    //! Следующий элемент FinalDA в списке для обнаружения изменений
    RptItem nextFinalDARptItem;
        
    //! Свежие изменения
    TrgOps changedNew;    

    union {
        bool boolValue;
    } newValue;

    //! Предыдущие изменения
    TrgOps changedOld;
    union {
        bool boolValue;
    } oldValue;
    

    //! ============Только для составных элементов
    RptItem firstChild;
    RptItem lastChild;

};

void *RptItem_alloc(size_t size);
RptItem RptItem_create(IEDEntity iedObj, RptDataSetItem rptDsItem);
