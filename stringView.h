#pragma once

#include "local_types.h"
#include <stddef.h>
#include <stdint.h>

typedef struct {
    size_t len;
    const char* p;
}StringView;


void StringView_init(StringView* strView, const char * str, size_t len);

void StringView_fromStringView(StringView* strView, StringView* src);

void StringView_fromCStr(StringView* strView, char * str);

//! Возвращает позицию указанного символа или -1 если символ не найден
int StringView_findChar(StringView * strView, unsigned char ch);

//! Возвращает позицию указанного символа или -1 если символ не найден
//! Поиск с конца строки
int StringView_findCharBack(StringView * strView, unsigned char ch);

//!Разделяет строку на две по указанному символу.
//!Возвращает 0 если символ не найден
//! 1 если успешно
int StringView_splitChar(StringView* src, unsigned char splitChar,
	StringView* dst1, StringView* dst2);

//! Сравнивает с другим StringView
//! Работает аналогично strcmp
int StringView_cmp(StringView * strView1, StringView * strView2);

//! Сравнивает с ASCIIZ срокой
//! Работает аналогично strcmp
int StringView_cmpCStr(StringView * strView, const char* cstr);


