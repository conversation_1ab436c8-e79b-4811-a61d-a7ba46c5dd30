#pragma once
#include <stdbool.h>
#include "OscReadFileContext.h"

//! должна вызываться один раз при старте блока
bool OscConverter_init(void);
//! обрабатывает один цикл, пишет в dat файл
bool  OscConverter_processPeriod(OscReadFileContext * readFileContext);
//! полностью записывает cfg (должна вызываться после записи dat)
bool OscConverter_processCfg(OscReadFileContext * readFileContext);
//! полностью записывает hdr (фактически пустой файл)
bool OscConverter_processHdr(OscReadFileContext * readFileContext);
