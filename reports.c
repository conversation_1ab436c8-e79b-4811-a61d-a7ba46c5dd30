#include "reports.h"
#include "rcb.h"
#include "reporter.h"
#include "mms.h"
#include "platform_thread.h"
#include "DataSlice.h"
#include "IEDCompile/InnerAttributeTypes.h"
#include "iedmodel.h"
#include"iedTree/iedTree.h"
#include "reportItems/RptDataSet.h"
#include "BaseAsnTypes.h"
#include "AsnEncoding.h"
#include "mms_data.h"
#include "control.h"
#include "timers.h"
#include "BusError.h"
#include <Clib.h>
#include <stddef.h>
#include <string.h>
#include <debug.h>


#define TRGOP_INTEGRITY (1 << 1)
#define MIN_INTG_PD 100


// Сколько байт занимает inclusion-bitstring.
// Нежелательно делать очень большим, потому что переменная создаётся
// на стеке. Желательно делать кратным 4.
#define BYTES_IN_INCLUSION_BITSTING 40
#define MAX_DATASET_OBJECT_COUNT (BYTES_IN_INCLUSION_BITSTING * 8)

#define TRG_OPS_BIT_COUNT 6

#pragma alignvar (4)

//Fixed report "RPT" variable
uint8_t reportVarNameSequence[7] = { 0xA1, 0x05, 0x80, 0x03, 0x52, 0x50, 0x54 };

size_t g_reportCount = 0;


Reporter g_reports[MAX_REPORT_COUNT];

//Причина включения для каждого значения в отчёте
//Используется для генерации Reason-For-Inclusion
//и для генерации Inclusion-bitstring.
//Каждый элемент соответствует элементу DataSet.
//При подготовке отчёта записавается:
// 0 если элемент не включен в отчёт
// причина включения если элемент включен в отчёт
static uint8_t reportInclusionReasons[MAX_DATASET_OBJECT_COUNT];

//Здесь лежат закодированные значения, попавшие в отчёт.
static uint8_t reportValuesBuf[DEFAULT_REPORT_BUFFER_SIZE];

//Здесь лежат закодированные значения, попавшие в отчёт вместе с
//обязательными и необязательными служебными данными
static uint8_t reportAccessResultsBuf[DEFAULT_REPORT_BUFFER_SIZE];
//Здесь лежит отчёт в виде пакета MMS
static uint8_t reportMmsBuf[DEFAULT_REPORT_BUFFER_SIZE];
//Здесь лежит отчёт в виде пакета Presentation
static uint8_t reportPresentationBuf[DEFAULT_REPORT_BUFFER_SIZE];

/* 61850 - 8 - 1
	The MMS Btime6 (TimeOfDay) type shall be an OCTET STRING. A value of the TimeOfDay
type may contain either 4 or 6 octets. The first form specifies the time as the number of
milliseconds since midnight on the current date (the date is not contained in the value), while
the second form contains the time and a date, expressed as the relative day since 1 January
1984. The first four octets shall contain a value indicating the number of milliseconds since
midnight for the current date in both forms.

https://www.kepware.com/getattachment/be373908-e367-4779-b8ae-033c73a99b1e/iec-61850-mms-client-manual.pdf

	6 byte structure containing days since Jan 1, 1984 and milliseconds since
	midnight.It uses the format "MM/DD/YYYY_HH:MM:SS.mmm".
*/


#define TIME3232_TO_TIME(t) ((unsigned long)(t/0x100000000L))
#define TIME3232FRACT_TO_MS(t)  ((unsigned long)((t&0xFFFFFFFF)*1000/0x100000000L))

#define msPerDay 86400000LL
#define Years14_msCount 441763200000LL


bool getRCB(size_t index, RCB** pRCB)
{
	if (index >= g_reportCount)
	{
		return false;
	}
	*pRCB = &g_reports[index].rcb;
	return true;
}

PReporter getReporterByIndex(size_t index)
{
	if (index >= g_reportCount) 
	{
		return NULL;
	}
	return g_reports + index;
}

static void encodeBinaryTime(uint8_t* outBuf, unsigned long long preсiseTime)
{
	
	unsigned long long mmsTime;
	unsigned long long timestamp;
    uint8_t* binaryTimeBuf;
    uint16_t daysDiff;
    uint8_t* daysDiffBuf;
    uint32_t msSinceMidnight;
    uint8_t* msSinceMidnightBuf;
	unsigned long long secCount = TIME3232_TO_TIME(preсiseTime);
	uint32_t mSecCount = TIME3232FRACT_TO_MS(preсiseTime);
	timestamp = secCount * 1000 + mSecCount;

	if (timestamp > Years14_msCount)
		mmsTime = timestamp - (Years14_msCount);
	else
		mmsTime = 0;

    binaryTimeBuf = outBuf;


    daysDiff = (uint16_t)(mmsTime / (msPerDay));
    daysDiffBuf = (uint8_t*)&daysDiff;


	binaryTimeBuf[4] = daysDiffBuf[1];
	binaryTimeBuf[5] = daysDiffBuf[0];


    msSinceMidnight = mmsTime % (msPerDay);
    msSinceMidnightBuf = (uint8_t*)&msSinceMidnight;


	binaryTimeBuf[0] = msSinceMidnightBuf[3];
	binaryTimeBuf[1] = msSinceMidnightBuf[2];
	binaryTimeBuf[2] = msSinceMidnightBuf[1];
	binaryTimeBuf[3] = msSinceMidnightBuf[0];


}


bool isRCBConnected(PReporter pReport)
{
	IsoConnection* conn = pReport->connection;
	return conn != NULL && conn->connected;	
}

//=====Сравнение данных DataSet и создание отчёта при наличии изменений======

//Готовит данные отчёта для Integrity или General Interrogation
static bool processDataSetIntgGi(DataSetItem* firstDSItem, RptProcContext* context)
{
	DataSetItem* dsItem = firstDSItem;	
	uint8_t reason = context->mode == RPT_GI ? REASON_GI : REASON_INTEGRITY;

	context->itemIdx = 0;

	while (dsItem != NULL)
	{
		IEDEntity iedObj = dsItem->obj;
		if(iedObj == NULL)
		{
			ERROR_REPORT("Invalid poiter to IEDEntity");
			return false;
		}

		if(!iedObj->encodeRead(iedObj, context->outBuf ))
		{
			ERROR_REPORT("Report data reading error");
			return false;
		}
		reportInclusionReasons[context->itemIdx] = reason;

		context->itemIdx++;

		dsItem = dsItem->next;
	}
	return true;
}

//Готовит данные отчёта при изменении данных
static bool processDataSetChange(DataSetItem* firstDSItem, RptProcContext* context)
{
	DataSetItem* dsItem = firstDSItem;		
	TrgOps changed;
	TrgOps trgOps = (TrgOps)context->reporter->rcb.trgOps;

	context->itemIdx = 0;

	while (dsItem != NULL)
	{
		IEDEntity iedObj = dsItem->obj;
		if(iedObj == NULL)
		{
			ERROR_REPORT("Invalid pointer to IEDEntity");
			return false;
		}		

		//Для объекта данных проверяем наличие изменений
		changed =  IEDEntity_findChanges(iedObj, trgOps);

		if(changed != TRGOP_NONE)
		{
			//Добавляем в отчёт
			if(!iedObj->encodeRead(iedObj, context->outBuf ))
			{
				ERROR_REPORT("Report data reading error");
				return false;
			}

			reportInclusionReasons[context->itemIdx] = changed;
		}
		else
		{
			// Элемент не изменился, причины добавлять нет
			reportInclusionReasons[context->itemIdx] = 0;
		}
		context->itemIdx++;
		dsItem = dsItem->next;
	}
	return true;
}

//=======Инициализация текущих значений отчёта для дальнейшего сравнения==

bool initReportCompareDataset(PReporter reporter)
{
		
	DataSet* dataSet = reporter->dataSet;

	if (dataSet->itemCount > MAX_DATASET_OBJECT_COUNT)
	{
		ERROR_REPORT("Too many dataset objects");
		return false;
	}

	return true;
}

//===========================================================================

static int writeRptID(RCB* pRCB, uint8_t* outBuf, int outBufPos)
{		
	//Используется OctetString потому что она позволяет указать длину строки
	return BerEncoder_encodeOctetString(IEC61850_BER_VISIBLE_STRING,
		(const uint8_t*)pRCB->rptID.p, pRCB->rptID.len, outBuf, outBufPos);
}

static int writeOptFlds(uint16_t optFlds, uint8_t* outBuf, int outBufPos)
{	
	outBufPos = BerEncoder_encodeUshortBitString(IEC61850_BER_BIT_STRING, 10, 
		optFlds, outBuf, outBufPos);

	return outBufPos;		
}

static int writeTimeOfEntry(RCB* pRCB, uint8_t* outBuf, int outBufPos)
{	
	//0x8C, 0x06, 0x01, 0x90, 0xE4, 0x3A, 0x32, 0x13
	/*
	uint8_t timeStampBuf[8] = { 0x01, 0x90, 0xE4, 0x3A, 0x32, 0x13 };
	RCB* pReport = g_reports + reportIndex;
	return BerEncoder_encodeOctetString( 0x8C, timeStampBuf, 6, outBuf,
		outBufPos);
	*/
	unsigned long long timestamp;	
	timestamp = dataSliceGetTimeStamp();
	outBuf[outBufPos++] = 0x8C;
	outBuf[outBufPos++] = 0x06;
	encodeBinaryTime(outBuf + outBufPos, timestamp);
	return outBufPos + 6;
}

static int writeDatSetRef(RCB* pRCB, uint8_t* outBuf, int outBufPos)
{	
	//Используется OctetString потому что она позволяет указать длину строки
	return BerEncoder_encodeOctetString(IEC61850_BER_VISIBLE_STRING,
		pRCB->dataSetName, pRCB->dataSetNameLength, outBuf, outBufPos);
}

static int writeBufferOverflow(Reporter* pRCB, uint8_t* outBuf, int outBufPos)
{
	bool overflow = ReportQueue_isOverflow(&pRCB->buffer);
	return BerEncoder_encodeBoolean(IEC61850_BER_BOOLEAN, overflow,
		outBuf, outBufPos);
}

static int writeEntryID(RCB* pRCB, uint8_t* outBuf, int outBufPos)
{	
	return encodeOctetString8Value(outBuf, outBufPos, &pRCB->entryID, FALSE);
}

static int writeConfRev(RCB* pRCB, uint8_t* outBuf, int outBufPos)
{
	return BerEncoder_encodeUInt32WithTL(IEC61850_BER_UNSIGNED_INTEGER,
		pRCB->confRev, outBuf, outBufPos);	
}

static int writeInclusionBitstring(Reporter* pRCB, uint8_t* outBuf, int outBufPos)
{    
    //inclusion-bitstring
    uint8_t inclusionBitString[BYTES_IN_INCLUSION_BITSTING];
	int inclusionFlagNum;
	int dataSetObjectCount = pRCB->dataSet->itemCount;

    memset(inclusionBitString, 0, BYTES_IN_INCLUSION_BITSTING);
	
	for (inclusionFlagNum = 0; inclusionFlagNum < dataSetObjectCount; ++inclusionFlagNum)
	{
		if (reportInclusionReasons[inclusionFlagNum])
		{
			int byteNum = inclusionFlagNum >> 3;
			int bitNum = inclusionFlagNum & 7;
			int bit = 1 << (7 - bitNum);
            inclusionBitString[byteNum] |= bit;
		}
	}		

	outBufPos = BerEncoder_encodeBitString(IEC61850_BER_BIT_STRING,
        dataSetObjectCount, inclusionBitString, outBuf, outBufPos);
	return outBufPos;
}

static int writeReasonForInclusion(Reporter* pRCB, uint8_t* outBuf, int outBufPos)
{	
	int objIdx;
	int dataSetObjectCount = pRCB->dataSet->itemCount;

	for (objIdx = 0; objIdx < dataSetObjectCount; ++objIdx)
	{
		uint8_t reason = reportInclusionReasons[objIdx];
		if (reason != 0)
		{			
			outBufPos = BerEncoder_encodeUcharBitString(IEC61850_BER_BIT_STRING,
				TRG_OPS_BIT_COUNT, reason, outBuf, outBufPos);			
			RET_IF_NOT(outBufPos > 0, "Error encoding reason for inclusion");
		}
	}

	return outBufPos;
}

static int writeReportAccessResults(Reporter* pReporter, uint8_t* outBuf, 
	uint8_t* encodedValues, int encodedValuesSize)
{
	int outBufPos = 0;
	RCB* pRCB = &pReporter->rcb;
	uint16_t optFlds = pRCB->optFlds;
	

	outBufPos = writeRptID(pRCB, outBuf, outBufPos);
	RET_IF_NOT(outBufPos > 0, "Error writing RptID");
	
	outBufPos = writeOptFlds(optFlds, outBuf, outBufPos);
	RET_IF_NOT(outBufPos > 0, "Error writing OptFlds");
		
	if (optFlds & OPTFLDS_TIME_STAMP)
	{
		outBufPos = writeTimeOfEntry(pRCB, outBuf, outBufPos);
		RET_IF_NOT(outBufPos > 0, "Error writing TimeOfEntry");
	}
	
	if (optFlds & OPTFLDS_DATA_SET)
	{
		outBufPos = writeDatSetRef(pRCB, outBuf, outBufPos);
		RET_IF_NOT(outBufPos > 0, "Error writing DatSet");
	}

	if (optFlds & OPTFLDS_BUFFER_OVERFLOW)
	{
		outBufPos = writeBufferOverflow(pReporter, outBuf, outBufPos);
		RET_IF_NOT(outBufPos > 0, "Error writing <buffer overflow> field");
	}


	if (optFlds & OPTFLDS_ENTRY_ID)
	{
		outBufPos = writeEntryID(pRCB, outBuf, outBufPos);
		RET_IF_NOT(outBufPos > 0, "Error writing EntryID");
	}
	pRCB->entryID++;

	if (optFlds & OPTFLDS_CONF_REV)
	{
		outBufPos = writeConfRev(pRCB, outBuf, outBufPos);
		RET_IF_NOT(outBufPos > 0, "Error writing ConfRev");
	}
	
	outBufPos = writeInclusionBitstring(pReporter, outBuf, outBufPos);
	RET_IF_NOT(outBufPos > 0, "Error writing Inclusion Bit string");
	
	//Write values
	memcpy(outBuf + outBufPos, encodedValues, encodedValuesSize);
	outBufPos += encodedValuesSize;

	if (optFlds & OPTFLDS_REASON_FOR_INCLUSION)
	{
		outBufPos = writeReasonForInclusion(pReporter, outBuf, outBufPos);
		RET_IF_NOT(outBufPos > 0, "Error writing Reason-For-Inclusion");
	}

	return outBufPos;
}

//Оформляет и отправляет отчёт
static void sendReport(IsoConnection* isoConn, uint8_t* mmsReport, int byteCount,
                SessionOutBuffer* sessionOutBuf)
{
	int sessionDataLen;
	int presentationDataLen;
	int bufPos = 0;
    int reportLen;
    int varNameLen;
    int reportVarLen;
    int reportContentLen;

	if (isoConn == NULL)
	{
		return;
	}

	//Определяем длины

	//A0
    reportLen = 1 +
		BerEncoder_determineLengthSize(byteCount) + byteCount;

	//A1
    varNameLen = sizeof(reportVarNameSequence);
	
	//A0 без тэга
    reportVarLen = reportLen + varNameLen;

	//A0 с тегом и длиной
    reportContentLen = 1 +
		BerEncoder_determineLengthSize(reportVarLen) + reportVarLen;

	// Кодируем
    //Unconfirmed PDU
    bufPos = BerEncoder_encodeTL(0xA3, reportContentLen, reportMmsBuf, bufPos);

	bufPos = BerEncoder_encodeTL(0xA0, reportVarLen, reportMmsBuf, bufPos);
	memcpy(reportMmsBuf + bufPos, reportVarNameSequence, varNameLen);
	bufPos += varNameLen;
	bufPos = BerEncoder_encodeTL(0xA0, byteCount, reportMmsBuf, bufPos);
	memcpy(reportMmsBuf + bufPos, mmsReport, byteCount);
	bufPos += byteCount;
	byteCount = bufPos;
	
	presentationDataLen = IsoPresentation_createUserData(&isoConn->presentation,
		reportPresentationBuf, reportMmsBuf, byteCount);

    sessionDataLen = isoSession_createDataSpdu(sessionOutBuf->cotpOutBuf,
        SESSION_OUT_BUF_SIZE, reportPresentationBuf, presentationDataLen);
    sessionOutBuf->byteCount = sessionDataLen;
    if (!OutQueue_insert(&isoConn->outQueue, sessionOutBuf))
	{
		ERROR_REPORT("Out queue overflow");
		return;
	}
}

static void writeReportToBuffer(Reporter* pRCB, uint8_t* pData, int dataLen)
{
	ReportQueue_write(&pRCB->buffer, pData, dataLen);
}

//Выделяет SessionOutBuf и посылает данные отчёта
static void allocateBufAndSendReport(Reporter* pRCB,
	uint8_t* reportData, int reportDataSize)
{
    SessionOutBuffer* outBuf;
	IsoConnection* pConn = pRCB->connection;
	if (pConn == NULL)
	{
		return;
	}
    outBuf = allocSessionOutBuffer(&pConn->outBuffers, SESSION_OUT_BUF_SIZE);
	if (outBuf)
	{
		sendReport(pConn, reportData, reportDataSize, outBuf);
	}
	else
	{
		ERROR_REPORT("Unable to allocate buffer for the report");
	}
}

//Вызывается каждую миллисекунду для таймера integrity
static void integrityTimerProc(void)
{
	size_t rcbIdx;
	for (rcbIdx = 0; rcbIdx < g_reportCount; ++rcbIdx)
	{
		Reporter* pReporter = g_reports + rcbIdx;
		RCB* pRCB = &pReporter->rcb;
		if ((pRCB->trgOps & TRGOP_INTEGRITY) == 0 || pRCB->intgPd == 0)
		{
			continue;
		}
		if (pReporter->intgPdCounter < pRCB->intgPd)
		{
			pReporter->intgPdCounter++;
		}
		else
		{
			pReporter->intgPdCounter = 0;
			pReporter->intgTimerAlam = true;
		}
	}
}


// В с учётом режима mode посылает отчёт
static void processReport(Reporter* pReporter, RptProcessMode mode)
{	
	int reportDataSize;
	BufferView reportValBufView;
	RptProcContext rptProcContext;
	RCB* pRCB = &pReporter->rcb;	
	if (!pRCB->buffered && (!pRCB->rptEna || !isRCBConnected(pReporter)))
	{
		return;
	}
		
	BufferView_init(&reportValBufView, reportValuesBuf, sizeof(reportValuesBuf),0);
	
	
	rptProcContext.mode = RPT_CMP;
	rptProcContext.reporter = pReporter;
	rptProcContext.outBuf = &reportValBufView;
	rptProcContext.itemIdx = 0;												
	rptProcContext.inclusionReasons = reportInclusionReasons;			
	

	if(!RptDataSet_flushAndUpdate(&rptProcContext))
	{
		ERROR_REPORT("Error processing dataset");
		return;
	}

	if (reportValBufView.pos == 0)
	{
		//Никаких данных не собрано, отчёт не посылаем.
		return;
	}

	reportDataSize = writeReportAccessResults(pReporter,
		reportAccessResultsBuf, reportValuesBuf, reportValBufView.pos);
	if (reportDataSize > 0)
	{
		if (pRCB->buffered)
		{
			//Пишем в буфер буферизированного отчёта
			writeReportToBuffer(pReporter, reportAccessResultsBuf,
				reportDataSize);
		}
		else
		{
			allocateBufAndSendReport(pReporter, reportAccessResultsBuf,
				reportDataSize);
		}
	}	
}

//Обнаруживает изменения в наборах данных, зарегистрированных в отчётах,
//и при наличии посылает соответствующий отчёт(небуферизированные) 
//или помещает его в буфер (буферизированные)
static void processAllReportsData()
{
	size_t rcbIdx;
	Reporter* pReporter;
	RptDataSet rptDataSet;
	BufferView reportValBufView;	

	// Обновляем изменения в небуферезированных включенных 
	// и всех буферизированных отчётах
	for (rcbIdx = 0; rcbIdx < g_reportCount; ++rcbIdx)
	{
		RCB* pRCB;
		pReporter = g_reports + rcbIdx;
		pRCB = &pReporter->rcb;
		if ((pRCB->rptEna && isRCBConnected(pReporter))
			|| pRCB->buffered)
		{			
			RptDataSet_updateChanges(pReporter->rptDataSet);
		}
	}

	// Отправляем старые данные в отчёты если надо. 
	// Или просто переписывем их новыми.
	for (rcbIdx = 0; rcbIdx < g_reportCount; ++rcbIdx)
	{	
		pReporter = g_reports + rcbIdx;
		processReport(pReporter, RPT_CMP);	
	}	
}

static bool bufferedReportHasData(Reporter* pCurrReport)
{	
	return !ReportQueue_isEmpty(&pCurrReport->buffer);
}

static int readReportFromBuffer(Reporter* pRCB, uint8_t* bufferToRead, int bufSize)
{
	return ReportQueue_read(&pRCB->buffer, bufferToRead, bufSize);
}

static void sendFromReportBuf(Reporter* pRCB)
{		
	//Буферизированный отчёт и в буфере есть данные
	if (!pRCB->sessionOutBuffer.busy)
	{
		int reportSize = readReportFromBuffer(pRCB, reportAccessResultsBuf,
			sizeof(reportAccessResultsBuf));
		if (reportSize != 0)
		{
			pRCB->sessionOutBuffer.busy = TRUE;
			sendReport(pRCB->connection, reportAccessResultsBuf, reportSize,
				&pRCB->sessionOutBuffer);
		}
	}		
}


//Посылает GI если установлены соответствующие флаги в RCB.
//Сбрасывает GI после посылки.
//Возвращает FALSE если GI не нужно.
static bool processGI(Reporter* pReporter)
{
	int reportDataSize;
	if (!pReporter->rcb.gi)
	{
		return false;
	}	
	reportDataSize = writeReport(pReporter, RPT_GI);
	if (reportDataSize > 0)
	{
		allocateBufAndSendReport(pReporter, reportAccessResultsBuf, reportDataSize);
		pReporter->rcb.gi = false;
	}	
	return true;
}

//Посылает Integrity report если установлены соответствующие флаги в RCB
//и время пришло.
//Возвращает false если Intregrity report не послан.
static bool processIntegrity(Reporter* pReporter)
{
	int reportDataSize;
    bool sent = false;
	
	if (pReporter->rcb.intgPd == 0 || !pReporter->intgTimerAlam)
	{
        return sent;
	}
		
    if (pReporter->rcb.buffered)
    {
        reportDataSize = writeReport(pReporter, RPT_INTG);
        if (reportDataSize > 0)
        {
            //Пишем в буфер буферизированного отчёта
            writeReportToBuffer(pReporter, reportAccessResultsBuf,
                reportDataSize);
            sent = true;
        }
    }
    else if(pReporter->rcb.rptEna)
    {
        reportDataSize = writeReport(pReporter, RPT_INTG);
        if (reportDataSize > 0)
        {
            allocateBufAndSendReport(pReporter, reportAccessResultsBuf,
                reportDataSize);
            sent = true;
        }
    }

    pReporter->intgTimerAlam = false;

    return sent;
}

static void reportsThread(void* data)
{
    unsigned long long timeStamp = 0;
    unsigned long long newTimeStamp;    
	static bool newBusOK = true;
	static bool oldBusOK = true;

	//Для цикла по отчётам
	size_t rptIdx;
	bool reportsProcessed;
    while(1)
    {
		reportsProcessed = FALSE;
        
		newTimeStamp = getCurrentDataSliceTime();
		newBusOK = BusError_check();

		//Если изменилось время или возникла ошибка шины
		if (timeStamp != newTimeStamp || (!newBusOK && oldBusOK))
		{			
			oldBusOK = newBusOK;
			timeStamp = newTimeStamp;
            // Изменилось время, значит могли измениться данные
			dataSliceCapture();

			//Устанавливам/снимаем флаги изменения в DA, содержащих
			//данные из DataSlice


			IEDTree_lock();

			IEDTree_updateFromDataSlice();

            // Здесь, при наличии изменений, происходит отправка небуферизированных
            // отчётов или помещение в буфер буферизированных			
			processAllReportsData();			
			IEDTree_unlock();

            Control_processCtrlObjects();
			dataSliceRelease();
			reportsProcessed = TRUE;
		} 
		
		//Отправка Intergrity
		for (rptIdx = 0; rptIdx < g_reportCount; rptIdx++)
		{
			Reporter* pReporter = g_reports + rptIdx;			
			if (processIntegrity(pReporter))
			{
				reportsProcessed = true;
			}
		}

        // В этом цикле отправляются буфера буфериризированных отчётов
        // и General Interrogation.
        // Для небуферизированных отчётов GI просто посылается.
        // Для буферизированных GI посылается только если буфер пуст.
		for (rptIdx = 0; rptIdx < g_reportCount; rptIdx++)
		{
			Reporter* pReporter = g_reports + rptIdx;
			RCB* pRCB = &pReporter->rcb;
			if (!pRCB->rptEna)
			{
				continue;
			}
			if (pRCB->buffered)
			{
				//Для буферизированных отчётов посылается либо содержимое буфера
				//либо, если буфер пустой, можно послать GI
				if (bufferedReportHasData(pReporter))
				{
					sendFromReportBuf(pReporter);
					reportsProcessed = TRUE;
				}
				else
				{
					reportsProcessed = processGI(pReporter);
				}
			}
			else
			{
				reportsProcessed = processGI(pReporter);
			}
			
		}			
		if(!reportsProcessed)
        {         
            Idle();            
        }		
    }
}

void initReports(void)
{
    g_reportCount = 0;
    registerAllRCB();
    createThread(reportsThread,NULL);
	Timers_setIntegrity1msCallBack(integrityTimerProc);
}

PReporter getFreeReport(void)
{
	if (g_reportCount == MAX_REPORT_COUNT)
	{
		return NULL;
	}

	return g_reports + g_reportCount;
}

void finalizeReportRegistration(void)
{
	if (g_reportCount == MAX_REPORT_COUNT)
	{
		ERROR_REPORT("Too many reports");
		return;
	}
	g_reportCount++;
}

void disableDisconnectedReports(void)
{
	size_t i;
	Reporter* pReporter;

	for (i = 0; i < g_reportCount; ++i)
	{
		pReporter = g_reports + i;
		if (pReporter->connection != NULL)
		{
			if (!pReporter->connection->connected)
			{
				pReporter->rcb.rptEna = FALSE;
                pReporter->rcb.resv = false;
				pReporter->connection = NULL;
				pReporter->sessionOutBuffer.busy = FALSE;
			}
		}
	}
}

bool Reporter_isOwnerConnection(PReporter pReport, IsoConnection* conn)
{
    IsoConnection* owner = pReport->connection;
    return owner == conn;
}

bool Reporter_setEnable(PReporter rpt, IsoConnection* isoConn, bool enable)
{    
	if (isRCBConnected(rpt))
	{
		//Захваченный RCB
		if (rpt->connection == isoConn)
		{
			//Свой RCB
            if (!enable && !rpt->rcb.resv)
			{
				rpt->connection = NULL;
			}
			rpt->rcb.rptEna = enable;
		}
		else
		{
			//Чужой RCB
			TRACE("Attempt to write captured RCB");
			return FALSE;
		}
	}
	else
	{
		//Свободный RCB
		if (enable)
		{
			//Захватываем RCB
			rpt->connection = isoConn;
		}
		rpt->rcb.rptEna = enable;
	}
	return TRUE;
}

bool Reporter_setResv(PReporter rpt, IsoConnection* isoConn, bool value)
{
    if (isRCBConnected(rpt))
    {
        //Захваченный RCB
        if (rpt->connection == isoConn)
        {
            //Свой RCB
            if (!value && !rpt->rcb.rptEna)
            {
                rpt->connection = NULL;
            }
            rpt->rcb.resv = value;
        }
        else
        {
            //Чужой RCB
            TRACE("Attempt to write captured RCB");
            return FALSE;
        }
    }
    else
    {
        //Свободный RCB
        if (value)
        {
            //Захватываем RCB
            rpt->connection = isoConn;
        }
        rpt->rcb.resv = value;
    }
    return TRUE;
}

void Reporter_setDataSetName(PReporter rpt, StringView* name)
{
	rpt->rcb.dataSetName = (uint8_t*)name->p;
	rpt->rcb.dataSetNameLength = name->len;
}

void Reporter_setIntgPd(PReporter rpt, uint32_t intgPd)
{
	//Если значение меньше минимального допустимого, пишем минимально допустимое
	//0 - специальное значение для выключения Integrity
	if (intgPd != 0 && intgPd < MIN_INTG_PD)
	{
		intgPd = MIN_INTG_PD;
	}

	//Останавливаем таймер
	rpt->rcb.intgPd = 0;
	//Сбрасываем таймер
	rpt->intgPdCounter = 0;
	rpt->intgTimerAlam = false;
	//Пишем новое значение	
	rpt->rcb.intgPd = intgPd;
}

void Reporter_setGI(PReporter rpt, bool gi)
{
    rpt->rcb.gi = gi;
}
