                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gm41.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=frsm.c -o gh_gm41.o -list=frsm.lst C:\Users\<USER>\AppData\Local\Temp\gh_gm41.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_gm41.s
Source File: frsm.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile frsm.c -o

                      10 ;		frsm.o

                      11 ;Source File:   frsm.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:55:52 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "frsm.h"


                      21 ;2: 


                      22 ;3: 


                      23 ;4: FRSM g_FRSM;


                      24 ;5: 


                      25 ;6: //ID для одиночного FRSM


                      26 ;7: #define SOLITARY_FRSM_ID 0x55


                      27 ;8: 


                      28 ;9: static void frsm_lock()


                      29 

                      30 ;11: 


                      31 ;12: }


                      32 

                      33 ;13: 


                      34 ;14: static void frsm_unlock()


                      35 

                      36 ;16: 


                      37 ;17: }


                      38 

                      39 ;18: 


                      40 ;19: bool frsm_init(void)


                      41 	.text

                      42 	.align	4

                      43 frsm_init::

                      44 ;20: {


                      45 

                      46 ;21:     g_FRSM.busy = FALSE;


                      47 

00000000 e59f1074*    48 	ldr	r1,.L69

00000004 e3a00000     49 	mov	r0,0

00000008 e5c10000     50 	strb	r0,[r1]


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gm41.s
                      51 ;22:     return TRUE;


                      52 

0000000c e3a00001     53 	mov	r0,1

00000010 e12fff1e*    54 	ret	

                      55 	.endf	frsm_init

                      56 	.align	4

                      57 

                      58 	.section ".bss","awb"

                      59 .L62:

                      60 	.data

                      61 	.text

                      62 

                      63 ;23: }


                      64 

                      65 ;24: 


                      66 ;25: bool frsm_alloc(uint32_t* id)


                      67 	.align	4

                      68 	.align	4

                      69 frsm_alloc::

                      70 ;26: {


                      71 

                      72 ;27:     frsm_lock();


                      73 

                      74 ;10: {


                      75 

                      76 ;28:     if (g_FRSM.busy)


                      77 

00000014 e59f1060*    78 	ldr	r1,.L69

00000018 e5d12000     79 	ldrb	r2,[r1]

0000001c e3520000     80 	cmp	r2,0

                      81 ;29:     {


                      82 

                      83 ;30:         frsm_unlock();


                      84 

                      85 ;15: {


                      86 

                      87 ;31:         return FALSE;


                      88 

00000020 13a00000     89 	movne	r0,0

                      90 ;32:     }


                      91 ;33:     *id = SOLITARY_FRSM_ID;


                      92 

00000024 03a02055     93 	moveq	r2,85

00000028 05802000     94 	streq	r2,[r0]

                      95 ;34:     g_FRSM.busy = TRUE;


                      96 

0000002c 03a00001     97 	moveq	r0,1

00000030 05c10000     98 	streqb	r0,[r1]

                      99 ;35:     frsm_unlock();


                     100 

                     101 ;15: {


                     102 

                     103 ;36:     return TRUE;


                     104 

00000034 e12fff1e*   105 	ret	

                     106 	.endf	frsm_alloc

                     107 	.align	4

                     108 

                     109 ;id	r0	param

                     110 

                     111 	.section ".bss","awb"


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gm41.s
                     112 .L118:

                     113 	.data

                     114 	.text

                     115 

                     116 ;37: }


                     117 

                     118 ;38: 


                     119 ;39: bool frsm_free(uint32_t id)


                     120 	.align	4

                     121 	.align	4

                     122 frsm_free::

00000038 e92d4000    123 	stmfd	[sp]!,{lr}

0000003c e24dd004    124 	sub	sp,sp,4

00000040 e1a0100d    125 	mov	r1,sp

00000044 eb000006*   126 	bl	frsm_getById

                     127 ;40: {


                     128 

                     129 ;41:     FRSM* frsm;


                     130 ;42:     frsm_lock();


                     131 

                     132 ;10: {


                     133 

                     134 ;43: 


                     135 ;44:     if (!frsm_getById(id, &frsm))


                     136 

00000048 e3500000    137 	cmp	r0,0

                     138 ;48:     }


                     139 ;49: 


                     140 ;50:     frsm->busy = FALSE;


                     141 

0000004c 159d0000    142 	ldrne	r0,[sp]

00000050 13a01000    143 	movne	r1,0

00000054 15c01000    144 	strneb	r1,[r0]

                     145 ;51:     frsm_unlock();


                     146 

                     147 ;15: {


                     148 

                     149 ;52:     return TRUE;


                     150 

00000058 13a00001    151 	movne	r0,1

                     152 ;45:     {


                     153 

                     154 ;46:         frsm_unlock();


                     155 

                     156 ;15: {


                     157 

                     158 ;47:         return FALSE;


                     159 

0000005c e28dd004    160 	add	sp,sp,4

00000060 e8bd8000    161 	ldmfd	[sp]!,{pc}

                     162 	.endf	frsm_free

                     163 	.align	4

                     164 ;frsm	[sp]	local

                     165 

                     166 ;id	none	param

                     167 

                     168 	.section ".bss","awb"

                     169 .L197:

                     170 	.data

                     171 	.text

                     172 


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gm41.s
                     173 ;53: }


                     174 

                     175 ;54: 


                     176 ;55: bool frsm_getById(uint32_t id, FRSM** frsm)


                     177 	.align	4

                     178 	.align	4

                     179 frsm_getById::

                     180 ;56: {


                     181 

                     182 ;57:     if (id != SOLITARY_FRSM_ID)


                     183 

00000064 e3500055    184 	cmp	r0,85

                     185 ;58:     {


                     186 

                     187 ;59:         return FALSE;


                     188 

00000068 13a00000    189 	movne	r0,0

                     190 ;60:     }


                     191 ;61:     *frsm = &g_FRSM;


                     192 

0000006c 059f0008*   193 	ldreq	r0,.L69

00000070 05810000    194 	streq	r0,[r1]

                     195 ;62:     return TRUE;


                     196 

00000074 03a00001    197 	moveq	r0,1

00000078 e12fff1e*   198 	ret	

                     199 	.endf	frsm_getById

                     200 	.align	4

                     201 

                     202 ;id	r0	param

                     203 ;frsm	r1	param

                     204 

                     205 	.section ".bss","awb"

                     206 .L246:

                     207 	.data

                     208 	.text

                     209 

                     210 ;63: }


                     211 	.align	4

                     212 .L69:

0000007c 00000000*   213 	.data.w	g_FRSM

                     214 	.type	.L69,$object

                     215 	.size	.L69,4

                     216 

                     217 	.align	4

                     218 

                     219 	.data

                     220 	.comm	g_FRSM,28,4

                     221 	.type	g_FRSM,$object

                     222 	.size	g_FRSM,28

                     223 	.ghsnote version,6

                     224 	.ghsnote tools,3

                     225 	.ghsnote options,0

                     226 	.text

                     227 	.align	4

