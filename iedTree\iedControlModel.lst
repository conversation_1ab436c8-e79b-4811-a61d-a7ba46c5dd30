                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_efg1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=iedControlModel.c -o iedTree\gh_efg1.o -list=iedTree/iedControlModel.lst C:\Users\<USER>\AppData\Local\Temp\gh_efg1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_efg1.s
Source File: iedControlModel.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      10 ;		iedTree/iedControlModel.c -o iedTree/iedControlModel.o

                      11 ;Source File:   iedTree/iedControlModel.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:55:10 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "iedControlModel.h"


                      21 ;2: 


                      22 ;3: 


                      23 ;4: 


                      24 ;5: #include "../iedmodel.h"


                      25 ;6: #include "../timers.h"


                      26 ;7: #include "iedFinalDA.h"


                      27 ;8: #include "iedObjects.h"


                      28 ;9: #include "../mms_data.h"


                      29 ;10: #include "../pwin_access.h"


                      30 ;11: #include "../control.h"


                      31 ;12: 


                      32 ;13: #include "../AsnEncoding.h"


                      33 ;14: 


                      34 ;15: #include "IEDCompile/AccessInfo.h"


                      35 ;16: #include "IEDCompile/InnerAttributeTypes.h"


                      36 ;17: 


                      37 ;18: #include <Clib.h>


                      38 ;19: 


                      39 ;20: #include <string.h>


                      40 ;21: 


                      41 ;22: //Сколько ждать готовности после передачи сигнала объекта управления


                      42 ;23: //в алгоритмы.


                      43 ;24: //Таймаут сделан для избежания зависания протокола при ошибке в алгоритмах.


                      44 ;25: #define CTRL_READY_TIMEOUT 5000


                      45 ;26: 


                      46 ;27: typedef struct {


                      47 ;28:     size_t len;


                      48 ;29:     char orIdent[64];


                      49 ;30: } IEDOrIdent;


                      50 ;31: 



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_efg1.s
                      51 ;32: typedef struct {


                      52 ;33:     int orCat;


                      53 ;34: } IEDOrCat;


                      54 ;35: 


                      55 ;36: typedef enum {


                      56 ;37:     CONTROL_SBOW,


                      57 ;38:     CONTROL_OPER,


                      58 ;39:     CONTROL_CANCEL


                      59 ;40: } ControlType;


                      60 ;41: 


                      61 ;42: typedef struct {


                      62 ;43:     IEDOrIdent *orIdent;


                      63 ;44:     IEDOrCat *orCat;


                      64 ;45:     ControlType type;


                      65 ;46:     //true если операция в процессе выполнения и ожидает


                      66 ;47:     //сигнала terminate


                      67 ;48:     bool waitTerminate;


                      68 ;49:     //Соединение, в которое отправлять отчёт


                      69 ;50:     IsoConnection* isoConn;


                      70 ;51:     IntBoolAccessInfo* code;


                      71 ;52:     IntBoolAccessInfo* ready;


                      72 ;53:     IntBoolAccessInfo* ctlValOne;


                      73 ;54:     IntBoolAccessInfo* ctlValZero;


                      74 ;55:     IntBoolAccessInfo* terminate;


                      75 ;56: } IEDControlDA;


                      76 ;57: 


                      77 ;58: typedef struct {


                      78 ;59:     //Пока просто какой-то мусор


                      79 ;60:     int reserved;


                      80 ;61: } IEDControlDO;


                      81 ;62: 


                      82 ;63: 


                      83 ;64: bool IEDControlDA_isControlDA(IEDEntity entity)


                      84 ;65: {


                      85 ;66:     return entity->type == IED_ENTITY_DA


                      86 ;67:             && entity->subType == DA_SUBTYPE_CONTROL;


                      87 ;68: }


                      88 ;69: 


                      89 ;70: bool IEDControlDA_isReady(IEDEntity controlDA)


                      90 ;71: {


                      91 ;72:     IEDControlDA *extInfo;


                      92 ;73:     if(!IEDControlDA_isControlDA(controlDA))


                      93 ;74:     {


                      94 ;75:         ERROR_REPORT("Invalid control DA");


                      95 ;76:         return false;


                      96 ;77:     }


                      97 ;78:     extInfo = controlDA->extInfo;


                      98 ;79:     return readBoolValue(extInfo->ready);


                      99 ;80: }


                     100 ;81: 


                     101 ;82: bool IEDControlDA_waitReady(IEDEntity controlDA)


                     102 ;83: {


                     103 ;84:     uint32_t startTime;


                     104 ;85:     IEDControlDA *extInfo;


                     105 ;86:     if(!IEDControlDA_isControlDA(controlDA))


                     106 ;87:     {


                     107 ;88:         ERROR_REPORT("Invalid control DA");


                     108 ;89:         return false;


                     109 ;90:     }


                     110 ;91:     extInfo = controlDA->extInfo;


                     111 ;92: 



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_efg1.s
                     112 ;93:     startTime = Timers_getTickCount();


                     113 ;94:     while(!readBoolValue(extInfo->ready))


                     114 ;95:     {


                     115 ;96:         if(Timers_isTimeout(startTime, CTRL_READY_TIMEOUT))


                     116 ;97:         {


                     117 ;98:             ERROR_REPORT("Timeout in IEDControlDA_waitReady");


                     118 ;99:             return false;


                     119 ;100:         }


                     120 ;101:         Idle();


                     121 ;102:     };


                     122 ;103:     return true;


                     123 ;104: }


                     124 ;105: 


                     125 ;106: void IEDControlDA_checkTerminate(IEDEntity controlDA)


                     126 ;107: {


                     127 ;108:     IEDControlDA* extInfo;


                     128 ;109:     uint8_t terminateCode;


                     129 ;110: 


                     130 ;111:     if(!IEDControlDA_isControlDA(controlDA))


                     131 ;112:     {


                     132 ;113:         return;


                     133 ;114:     }


                     134 ;115:     extInfo = controlDA->extInfo;


                     135 ;116:     if(extInfo->type != CONTROL_OPER)


                     136 ;117:     {


                     137 ;118:         return;


                     138 ;119:     }


                     139 ;120: 


                     140 ;121:     if(!extInfo->waitTerminate)


                     141 ;122:     {


                     142 ;123:         return;


                     143 ;124:     }


                     144 ;125: 


                     145 ;126:     if(!readBoolValue(extInfo->terminate))


                     146 ;127:     {


                     147 ;128:         return;


                     148 ;129:     }


                     149 ;130: 


                     150 ;131:     extInfo->waitTerminate = false;


                     151 ;132:     if(extInfo->isoConn == NULL)


                     152 ;133:     {


                     153 ;134:         return;


                     154 ;135:     }


                     155 ;136:     terminateCode = readIntValue(extInfo->code);


                     156 ;137: 


                     157 ;138:     if(terminateCode == 0)


                     158 ;139:     {


                     159 ;140:         Control_sendPositiveCmdTermReport(extInfo->isoConn, controlDA);


                     160 ;141:     }


                     161 ;142:     else


                     162 ;143:     {


                     163 ;144:         Control_sendNegativeCmdTermReport(extInfo->isoConn, controlDA,


                     164 ;145:                                           terminateCode);


                     165 ;146:     }


                     166 ;147:     extInfo->isoConn = NULL;


                     167 ;148: }


                     168 ;149: 


                     169 ;150: void IEDControlDA_disconnect(IEDEntity controlDA)


                     170 ;151: {


                     171 ;152:     IEDControlDA* extInfo;


                     172 ;153: 



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_efg1.s
                     173 ;154:     if(!IEDControlDA_isControlDA(controlDA))


                     174 ;155:     {


                     175 ;156:         return;


                     176 ;157:     }


                     177 ;158:     extInfo = controlDA->extInfo;


                     178 ;159:     if(extInfo->type != CONTROL_OPER)


                     179 ;160:     {


                     180 ;161:         return;


                     181 ;162:     }


                     182 ;163: 


                     183 ;164:     if(!extInfo->waitTerminate || extInfo->isoConn == NULL)


                     184 ;165:     {


                     185 ;166:         return;


                     186 ;167:     }


                     187 ;168: 


                     188 ;169:     extInfo->waitTerminate = false;


                     189 ;170:     extInfo->isoConn = NULL;


                     190 ;171: }


                     191 ;172: 


                     192 ;173: bool IEDControlDA_getOrIdent(IEDEntity entity, StringView *orIdent)


                     193 ;174: {


                     194 ;175:     IEDControlDA *extInfo;


                     195 ;176:     if(!IEDControlDA_isControlDA(entity))


                     196 ;177:     {


                     197 ;178:         return false;


                     198 ;179:     }


                     199 ;180: 


                     200 ;181:     extInfo = entity->extInfo;


                     201 ;182:     if(extInfo->orIdent == NULL)


                     202 ;183:     {


                     203 ;184:         return false;


                     204 ;185:     }


                     205 ;186: 


                     206 ;187:     StringView_init(orIdent, extInfo->orIdent->orIdent, extInfo->orIdent->len);


                     207 ;188:     return true;


                     208 ;189: }


                     209 ;190: 


                     210 ;191: bool IEDControlDA_getOrCat(IEDEntity entity, int32_t *orCat)


                     211 ;192: {


                     212 ;193:     IEDControlDA *extInfo;


                     213 ;194:     if(!IEDControlDA_isControlDA(entity))


                     214 ;195:     {


                     215 ;196:         return false;


                     216 ;197:     }


                     217 ;198: 


                     218 ;199:     extInfo = entity->extInfo;


                     219 ;200:     if(extInfo->orCat == NULL)


                     220 ;201:     {


                     221 ;202:         return false;


                     222 ;203:     }


                     223 ;204: 


                     224 ;205:     *orCat = extInfo->orCat->orCat;


                     225 ;206: 


                     226 ;207:     return true;


                     227 ;208: }


                     228 ;209: 


                     229 ;210: bool IEDOrCat_calcReadLen(IEDEntity entity, size_t *pLen)


                     230 ;211: {


                     231 ;212:     IEDOrCat *extInfo;


                     232 ;213: 


                     233 ;214:     if(entity->subType != DA_SUBTYPE_ORCAT)



                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_efg1.s
                     234 ;215:     {


                     235 ;216:         return false;


                     236 ;217:     }


                     237 ;218: 


                     238 ;219:     extInfo = entity->extInfo;


                     239 ;220: 


                     240 ;221:     *pLen = 2 + BerEncoder_Int32DetermineEncodedSize(extInfo->orCat);


                     241 ;222: 


                     242 ;223:     return true;


                     243 ;224: }


                     244 ;225: 


                     245 ;226: MmsDataAccessError IEDOrCat_write(IEDEntity entity,


                     246 ;227:                                            IsoConnection* isoConn, BufferView* value)


                     247 ;228: {


                     248 ;229:     int32_t decodedVal;


                     249 ;230:     IEDOrCat *extInfo;


                     250 ;231: 


                     251 ;232:     if(entity->type != IED_ENTITY_DA_VAR


                     252 ;233:             || entity->subType != DA_SUBTYPE_ORCAT)


                     253 ;234:     {


                     254 ;235:         return DATA_ACCESS_ERROR_TYPE_INCONSISTENT;


                     255 ;236:     }


                     256 ;237: 


                     257 ;238:     if(!BufferView_decodeInt32TL(value, IEC61850_BER_INTEGER, &decodedVal))


                     258 ;239:     {


                     259 ;240:         return DATA_ACCESS_ERROR_OBJECT_VALUE_INVALID;


                     260 ;241:     }


                     261 ;242: 


                     262 ;243:     extInfo = entity->extInfo;


                     263 ;244: 


                     264 ;245:     extInfo->orCat = decodedVal;


                     265 ;246: 


                     266 ;247:     return DATA_ACCESS_ERROR_SUCCESS;


                     267 ;248: }


                     268 ;249: 


                     269 ;250: 


                     270 ;251: bool IEDOrIdent_init(IEDEntity entity)


                     271 ;252: {


                     272 ;253:     IEDOrIdent* extInfo;


                     273 ;254:     entity->type = IED_ENTITY_DA_VAR;


                     274 ;255:     entity->subType = DA_SUBTYPE_ORIDENT;


                     275 ;256:     extInfo = IEDEntity_alloc(sizeof(IEDOrIdent));


                     276 ;257:     if(extInfo == NULL)


                     277 ;258:     {


                     278 ;259:         return false;


                     279 ;260:     }


                     280 ;261:     entity->extInfo = extInfo;


                     281 ;262:     return true;


                     282 ;263: }


                     283 ;264: 


                     284 ;265: bool IEDOrCat_init(IEDEntity entity)


                     285 ;266: {


                     286 ;267:     IEDOrCat* extInfo;


                     287 ;268:     entity->type = IED_ENTITY_DA_VAR;


                     288 ;269:     entity->subType = DA_SUBTYPE_ORCAT;


                     289 ;270:     extInfo = IEDEntity_alloc(sizeof(IEDOrCat));


                     290 ;271:     if(extInfo == NULL)


                     291 ;272:     {


                     292 ;273:         return false;


                     293 ;274:     }


                     294 ;275: 



                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_efg1.s
                     295 ;276:     entity->extInfo = extInfo;


                     296 ;277:     return true;


                     297 ;278: }


                     298 ;279: 


                     299 ;280: bool IEDCtlNum_init(IEDEntity entity,BufferView* ber)


                     300 ;281: {	


                     301 ;282: 	//!!! Всё это временно пока нет ясности с ctlNum


                     302 ;283: 


                     303 ;284: 	entity->type = IED_ENTITY_DA_CONST;	


                     304 ;285: 	//Сохраняем позицию константы в информационной модели


                     305 ;286: 	entity->extInfo = (void*)(ber->p + ber->pos - iedModel);


                     306 ;287: 	return true;


                     307 ;288: }


                     308 ;289: 


                     309 ;290: bool IEDOrCat_encodeRead(IEDEntity entity, BufferView *outBuf)


                     310 ;291: {


                     311 ;292:     IEDOrCat *extInfo;


                     312 ;293: 


                     313 ;294:     if(entity->subType != DA_SUBTYPE_ORCAT)


                     314 ;295:     {


                     315 ;296:         return false;


                     316 ;297:     }


                     317 ;298: 


                     318 ;299:     extInfo = entity->extInfo;


                     319 ;300: 


                     320 ;301:     return BufferView_encodeInt32(outBuf, IEC61850_BER_INTEGER,


                     321 ;302:                                   extInfo->orCat);


                     322 ;303: }


                     323 ;304: 


                     324 ;305: bool IEDOrIdent_calcReadLen(IEDEntity entity, size_t *pLen)


                     325 ;306: {


                     326 ;307:     IEDOrIdent *extInfo;


                     327 ;308: 


                     328 ;309:     if(entity->subType != DA_SUBTYPE_ORIDENT)


                     329 ;310:     {


                     330 ;311:         return false;


                     331 ;312:     }


                     332 ;313:     extInfo = entity->extInfo;


                     333 ;314: 


                     334 ;315:     *pLen = extInfo->len + 2;


                     335 ;316: 


                     336 ;317:     return true;


                     337 ;318: }


                     338 ;319: 


                     339 ;320: bool IEDOrIdent_encodeRead(IEDEntity entity, BufferView *outBuf)


                     340 ;321: {


                     341 ;322:     IEDOrIdent *extInfo;


                     342 ;323: 


                     343 ;324:     if(entity->subType != DA_SUBTYPE_ORIDENT)


                     344 ;325:     {


                     345 ;326:         return false;


                     346 ;327:     }


                     347 ;328: 


                     348 ;329:     extInfo = entity->extInfo;


                     349 ;330: 


                     350 ;331:     return BufferView_encodeOctetString(outBuf, IEC61850_BER_OCTET_STRING,


                     351 ;332:                                      extInfo->orIdent, extInfo->len);


                     352 ;333: }


                     353 ;334: 


                     354 ;335: MmsDataAccessError IEDOrIdent_write(IEDEntity entity,


                     355 ;336:                                            IsoConnection* isoConn, BufferView* value)



                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_efg1.s
                     356 ;337: {


                     357 ;338:     StringView decodedVal;


                     358 ;339:     IEDOrIdent *extInfo;


                     359 ;340: 


                     360 ;341:     if(entity->type != IED_ENTITY_DA_VAR


                     361 ;342:             || entity->subType != DA_SUBTYPE_ORIDENT)


                     362 ;343:     {


                     363 ;344:         return DATA_ACCESS_ERROR_TYPE_INCONSISTENT;


                     364 ;345:     }


                     365 ;346: 


                     366 ;347: 


                     367 ;348:     if(!BufferView_decodeStringViewTL(value, IEC61850_BER_OCTET_STRING,


                     368 ;349:                                   &decodedVal))


                     369 ;350:     {


                     370 ;351:         return DATA_ACCESS_ERROR_OBJECT_VALUE_INVALID;


                     371 ;352:     }


                     372 ;353: 


                     373 ;354:     if(decodedVal.len > 64)


                     374 ;355:     {


                     375 ;356:         return DATA_ACCESS_ERROR_OBJECT_VALUE_INVALID;


                     376 ;357:     }


                     377 ;358: 


                     378 ;359:     extInfo = entity->extInfo;


                     379 ;360: 


                     380 ;361:     extInfo->len = decodedVal.len;


                     381 ;362:     if(extInfo->len != 0)


                     382 ;363:     {


                     383 ;364:         memcpy(extInfo->orIdent, decodedVal.p, extInfo->len);


                     384 ;365:     }


                     385 ;366: 


                     386 ;367:     return DATA_ACCESS_ERROR_SUCCESS;


                     387 ;368: }


                     388 ;369: 


                     389 ;370: bool IEDControlDA_init(IEDEntity entity, BufferView *ctrlInfoBER)


                     390 ;371: {


                     391 ;372:     uint8_t tag;


                     392 ;373:     size_t len;


                     393 ;374:     IEDEntity origin;


                     394 ;375:     IEDEntity orCat;


                     395 ;376:     IEDEntity orIdent;


                     396 ;377: 


                     397 ;378:     IEDControlDA* extInfo = IEDEntity_alloc(sizeof(IEDControlDA));


                     398 ;379:     if(extInfo == NULL)


                     399 ;380:     {


                     400 ;381:         return false;


                     401 ;382:     }


                     402 ;383:     entity->extInfo = extInfo;


                     403 ;384: 


                     404 ;385:     //Подтип


                     405 ;386:     entity->subType = DA_SUBTYPE_CONTROL;


                     406 ;387: 


                     407 ;388:     //По имени определить тип


                     408 ;389:     if(StringView_cmpCStr(&entity->name, "SBOw") == 0)


                     409 ;390:     {


                     410 ;391:         extInfo->type = CONTROL_SBOW;


                     411 ;392:     }


                     412 ;393:     else if(StringView_cmpCStr(&entity->name, "Oper") == 0)


                     413 ;394:     {


                     414 ;395:         extInfo->type = CONTROL_OPER;


                     415 ;396:     }


                     416 ;397:     else if(StringView_cmpCStr(&entity->name, "Cancel") == 0)



                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_efg1.s
                     417 ;398:     {


                     418 ;399:         extInfo->type = CONTROL_CANCEL;


                     419 ;400:     }


                     420 ;401:     else


                     421 ;402:     {


                     422 ;403:         ERROR_REPORT("Invalid DA name");


                     423 ;404:         return false;


                     424 ;405:     }


                     425 ;406: 


                     426 ;407:     if(!BufferView_decodeTL(ctrlInfoBER, &tag, &len, NULL))


                     427 ;408:     {


                     428 ;409:         ERROR_REPORT("Invalid Ctrl info");


                     429 ;410:         return false;


                     430 ;411:     }


                     431 ;412: 


                     432 ;413:     if(tag != IED_CONTROL_INFO)


                     433 ;414:     {


                     434 ;415:         //Нет дополнительной информации


                     435 ;416:         return true;


                     436 ;417:     }


                     437 ;418:     //Подобъекты дополнительной информации


                     438 ;419:     BufferView_init(ctrlInfoBER, ctrlInfoBER->p + ctrlInfoBER->pos, len, 0);


                     439 ;420: 


                     440 ;421:     //Доступ к коду ошибки


                     441 ;422:     if(!IEDModel_getTermItemDescrStruct(ctrlInfoBER, (void**)&extInfo->code))


                     442 ;423:     {


                     443 ;424:         return false;


                     444 ;425:     }


                     445 ;426: 


                     446 ;427:     //Доступ к сигналу готовности


                     447 ;428:     if(!IEDModel_getTermItemDescrStruct(ctrlInfoBER, (void**)&extInfo->ready))


                     448 ;429:     {


                     449 ;430:         return false;


                     450 ;431:     }


                     451 ;432: 


                     452 ;433:     // Доступ к "1"


                     453 ;434:     if(!IEDModel_getTermItemDescrStruct(ctrlInfoBER, (void**)&extInfo->ctlValOne))


                     454 ;435:     {


                     455 ;436:         return false;


                     456 ;437:     }


                     457 ;438: 


                     458 ;439: 


                     459 ;440: 


                     460 ;441:     // Доступ к "0"


                     461 ;442:     if(!IEDModel_getTermItemDescrStruct(ctrlInfoBER,


                     462 ;443:                                        (void**)&extInfo->ctlValZero))


                     463 ;444:     {


                     464 ;445:         return false;


                     465 ;446:     }


                     466 ;447: 


                     467 ;448:     if(extInfo->type == CONTROL_OPER)


                     468 ;449:     {


                     469 ;450:         // Доступ к готовности Terminate


                     470 ;451:         if(!IEDModel_getTermItemDescrStruct(ctrlInfoBER,


                     471 ;452:                                            (void**)&extInfo->terminate))


                     472 ;453:         {


                     473 ;454:             return false;


                     474 ;455:         }


                     475 ;456:         if(!Control_registerCtrlObj(entity))


                     476 ;457:         {


                     477 ;458:             return false;



                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_efg1.s
                     478 ;459:         }


                     479 ;460:     }


                     480 ;461: 


                     481 ;462:     origin = IEDEntity_getChildByCStrName(entity, "origin");


                     482 ;463:     if(origin == NULL)


                     483 ;464:     {


                     484 ;465:         return false;


                     485 ;466:     }


                     486 ;467: 


                     487 ;468:     orIdent = IEDEntity_getChildByCStrName(origin, "orIdent");


                     488 ;469:     if(orIdent != NULL)


                     489 ;470:     {


                     490 ;471:         extInfo->orIdent = orIdent->extInfo;


                     491 ;472:     }


                     492 ;473: 


                     493 ;474:     orCat = IEDEntity_getChildByCStrName(origin, "orCat");


                     494 ;475:     if(orCat != NULL)


                     495 ;476:     {


                     496 ;477:         extInfo->orCat = orCat->extInfo;


                     497 ;478:     }


                     498 ;479: 


                     499 ;480:     return true;


                     500 ;481: }


                     501 ;482: 


                     502 ;483: bool IEDControlDO_init(IEDEntity entity)


                     503 ;484: {


                     504 ;485:     IEDControlDO *extInfo;


                     505 ;486:     entity->type = IED_ENTITY_DO;


                     506 ;487:     extInfo = IEDEntity_alloc(sizeof(IEDControlDO));


                     507 ;488:     entity->subType = DO_SUBTYPE_CONTROL;


                     508 ;489:     if(extInfo == NULL)


                     509 ;490:     {


                     510 ;491:         return false;


                     511 ;492:     }


                     512 ;493:     return true;


                     513 ;494: }


                     514 ;495: 


                     515 ;496: static bool writeCtlValue(IEDControlDA* ctrlInfo, const BufferView* value)


                     516 

                     517 ;513: }


                     518 

                     519 	.text

                     520 	.align	4

                     521 IEDControlDA_isControlDA::

00000000 e1a01000    522 	mov	r1,r0

00000004 e5912050    523 	ldr	r2,[r1,80]

00000008 e3a00000    524 	mov	r0,0

0000000c e3520006    525 	cmp	r2,6

00000010 1a000002    526 	bne	.L30

00000014 e5910054    527 	ldr	r0,[r1,84]

00000018 e3500001    528 	cmp	r0,1

0000001c 13a00000    529 	movne	r0,0

                     530 .L30:

00000020 e20000ff    531 	and	r0,r0,255

00000024 e12fff1e*   532 	ret	

                     533 	.endf	IEDControlDA_isControlDA

                     534 	.align	4

                     535 

                     536 ;entity	r1	param

                     537 

                     538 	.section ".bss","awb"


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_efg1.s
                     539 .L82:

                     540 	.data

                     541 	.text

                     542 

                     543 

                     544 	.align	4

                     545 	.align	4

                     546 IEDControlDA_isReady::

00000028 e92d4010    547 	stmfd	[sp]!,{r4,lr}

0000002c e1a04000    548 	mov	r4,r0

00000030 ebfffff2*   549 	bl	IEDControlDA_isControlDA

00000034 e3500000    550 	cmp	r0,0

00000038 0a000003    551 	beq	.L93

0000003c e5941058    552 	ldr	r1,[r4,88]

00000040 e5910018    553 	ldr	r0,[r1,24]

00000044 eb000000*   554 	bl	readBoolValue

00000048 e20000ff    555 	and	r0,r0,255

                     556 .L93:

0000004c e8bd8010    557 	ldmfd	[sp]!,{r4,pc}

                     558 	.endf	IEDControlDA_isReady

                     559 	.align	4

                     560 ;extInfo	r1	local

                     561 

                     562 ;controlDA	r4	param

                     563 

                     564 	.section ".bss","awb"

                     565 .L154:

                     566 	.data

                     567 	.text

                     568 

                     569 

                     570 	.align	4

                     571 	.align	4

                     572 IEDControlDA_waitReady::

00000050 e92d4070    573 	stmfd	[sp]!,{r4-r6,lr}

00000054 e1a04000    574 	mov	r4,r0

00000058 ebffffe8*   575 	bl	IEDControlDA_isControlDA

0000005c e3500000    576 	cmp	r0,0

00000060 1a000001    577 	bne	.L170

                     578 .L171:

00000064 e3a00000    579 	mov	r0,0

00000068 ea000013    580 	b	.L168

                     581 .L170:

0000006c e3a06d4c    582 	mov	r6,19<<8

00000070 e5945058    583 	ldr	r5,[r4,88]

00000074 eb000000*   584 	bl	Timers_getTickCount

00000078 e1a04000    585 	mov	r4,r0

0000007c e5950018    586 	ldr	r0,[r5,24]

00000080 e2866088    587 	add	r6,r6,136

00000084 eb000000*   588 	bl	readBoolValue

00000088 e3500000    589 	cmp	r0,0

0000008c 1a000009    590 	bne	.L174

                     591 .L175:

00000090 e1a01006    592 	mov	r1,r6

00000094 e1a00004    593 	mov	r0,r4

00000098 eb000000*   594 	bl	Timers_isTimeout

0000009c e3500000    595 	cmp	r0,0

000000a0 1affffef    596 	bne	.L171

000000a4 e6000010    597 	.word	0xE6000010

                     598 

000000a8 e5950018    599 	ldr	r0,[r5,24]


                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_efg1.s
000000ac eb000000*   600 	bl	readBoolValue

000000b0 e3500000    601 	cmp	r0,0

000000b4 0afffff5    602 	beq	.L175

                     603 .L174:

000000b8 e3a00001    604 	mov	r0,1

                     605 .L168:

000000bc e8bd8070    606 	ldmfd	[sp]!,{r4-r6,pc}

                     607 	.endf	IEDControlDA_waitReady

                     608 	.align	4

                     609 ;startTime	r4	local

                     610 ;extInfo	r5	local

                     611 

                     612 ;controlDA	r4	param

                     613 

                     614 	.section ".bss","awb"

                     615 .L254:

                     616 	.data

                     617 	.text

                     618 

                     619 

                     620 	.align	4

                     621 	.align	4

                     622 IEDControlDA_checkTerminate::

000000c0 e92d4030    623 	stmfd	[sp]!,{r4-r5,lr}

000000c4 e1a05000    624 	mov	r5,r0

000000c8 ebffffcc*   625 	bl	IEDControlDA_isControlDA

000000cc e3500000    626 	cmp	r0,0

000000d0 0a00001b    627 	beq	.L272

000000d4 e5954058    628 	ldr	r4,[r5,88]

000000d8 e5940008    629 	ldr	r0,[r4,8]

000000dc e3500001    630 	cmp	r0,1

000000e0 1a000017    631 	bne	.L272

000000e4 e5d4000c    632 	ldrb	r0,[r4,12]

000000e8 e3500000    633 	cmp	r0,0

000000ec 0a000014    634 	beq	.L272

000000f0 e5940024    635 	ldr	r0,[r4,36]

000000f4 eb000000*   636 	bl	readBoolValue

000000f8 e3500000    637 	cmp	r0,0

000000fc 13a00000    638 	movne	r0,0

00000100 15c4000c    639 	strneb	r0,[r4,12]

00000104 15940010    640 	ldrne	r0,[r4,16]

00000108 13500000    641 	cmpne	r0,0

0000010c 0a00000c    642 	beq	.L272

00000110 e5940014    643 	ldr	r0,[r4,20]

00000114 eb000000*   644 	bl	readIntValue

00000118 e1a01005    645 	mov	r1,r5

0000011c e21020ff    646 	ands	r2,r0,255

00000120 e5940010    647 	ldr	r0,[r4,16]

00000124 1a000003    648 	bne	.L289

00000128 eb000000*   649 	bl	Control_sendPositiveCmdTermReport

0000012c e3a00000    650 	mov	r0,0

00000130 e5840010    651 	str	r0,[r4,16]

00000134 ea000002    652 	b	.L272

                     653 .L289:

00000138 eb000000*   654 	bl	Control_sendNegativeCmdTermReport

0000013c e3a00000    655 	mov	r0,0

00000140 e5840010    656 	str	r0,[r4,16]

                     657 .L272:

00000144 e8bd8030    658 	ldmfd	[sp]!,{r4-r5,pc}

                     659 	.endf	IEDControlDA_checkTerminate

                     660 	.align	4


                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_efg1.s
                     661 ;extInfo	r4	local

                     662 ;terminateCode	r2	local

                     663 

                     664 ;controlDA	r5	param

                     665 

                     666 	.section ".bss","awb"

                     667 .L371:

                     668 	.data

                     669 	.text

                     670 

                     671 

                     672 	.align	4

                     673 	.align	4

                     674 IEDControlDA_disconnect::

00000148 e92d4010    675 	stmfd	[sp]!,{r4,lr}

0000014c e1a04000    676 	mov	r4,r0

00000150 ebffffaa*   677 	bl	IEDControlDA_isControlDA

00000154 e3500000    678 	cmp	r0,0

00000158 0a00000a    679 	beq	.L391

0000015c e5941058    680 	ldr	r1,[r4,88]

00000160 e5910008    681 	ldr	r0,[r1,8]

00000164 e3500001    682 	cmp	r0,1

00000168 1a000006    683 	bne	.L391

0000016c e5d1000c    684 	ldrb	r0,[r1,12]

00000170 e3500000    685 	cmp	r0,0

00000174 15910010    686 	ldrne	r0,[r1,16]

00000178 13500000    687 	cmpne	r0,0

0000017c 13a02000    688 	movne	r2,0

00000180 15c1200c    689 	strneb	r2,[r1,12]

00000184 15812010    690 	strne	r2,[r1,16]

                     691 .L391:

00000188 e8bd8010    692 	ldmfd	[sp]!,{r4,pc}

                     693 	.endf	IEDControlDA_disconnect

                     694 	.align	4

                     695 ;extInfo	r1	local

                     696 

                     697 ;controlDA	r4	param

                     698 

                     699 	.section ".bss","awb"

                     700 .L461:

                     701 	.data

                     702 	.text

                     703 

                     704 

                     705 	.align	4

                     706 	.align	4

                     707 IEDControlDA_getOrIdent::

0000018c e92d4030    708 	stmfd	[sp]!,{r4-r5,lr}

00000190 e1a05001    709 	mov	r5,r1

00000194 e1a04000    710 	mov	r4,r0

00000198 ebffff98*   711 	bl	IEDControlDA_isControlDA

0000019c e3500000    712 	cmp	r0,0

000001a0 15942058    713 	ldrne	r2,[r4,88]

000001a4 15923000    714 	ldrne	r3,[r2]

000001a8 13530000    715 	cmpne	r3,0

000001ac 03a00000    716 	moveq	r0,0

000001b0 0a000004    717 	beq	.L479

000001b4 e4932004    718 	ldr	r2,[r3],4

000001b8 e1a01003    719 	mov	r1,r3

000001bc e1a00005    720 	mov	r0,r5

000001c0 eb000000*   721 	bl	StringView_init


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_efg1.s
000001c4 e3a00001    722 	mov	r0,1

                     723 .L479:

000001c8 e8bd8030    724 	ldmfd	[sp]!,{r4-r5,pc}

                     725 	.endf	IEDControlDA_getOrIdent

                     726 	.align	4

                     727 ;extInfo	r2	local

                     728 

                     729 ;entity	r4	param

                     730 ;orIdent	r5	param

                     731 

                     732 	.section ".bss","awb"

                     733 .L540:

                     734 	.data

                     735 	.text

                     736 

                     737 

                     738 	.align	4

                     739 	.align	4

                     740 IEDControlDA_getOrCat::

000001cc e92d4030    741 	stmfd	[sp]!,{r4-r5,lr}

000001d0 e1a05001    742 	mov	r5,r1

000001d4 e1a04000    743 	mov	r4,r0

000001d8 ebffff88*   744 	bl	IEDControlDA_isControlDA

000001dc e3500000    745 	cmp	r0,0

000001e0 15942058    746 	ldrne	r2,[r4,88]

000001e4 15922004    747 	ldrne	r2,[r2,4]

000001e8 13520000    748 	cmpne	r2,0

000001ec 15920000    749 	ldrne	r0,[r2]

000001f0 15850000    750 	strne	r0,[r5]

000001f4 13a00001    751 	movne	r0,1

000001f8 03a00000    752 	moveq	r0,0

000001fc e8bd8030    753 	ldmfd	[sp]!,{r4-r5,pc}

                     754 	.endf	IEDControlDA_getOrCat

                     755 	.align	4

                     756 ;extInfo	r2	local

                     757 

                     758 ;entity	r4	param

                     759 ;orCat	r5	param

                     760 

                     761 	.section ".bss","awb"

                     762 .L620:

                     763 	.data

                     764 	.text

                     765 

                     766 

                     767 	.align	4

                     768 	.align	4

                     769 IEDOrCat_calcReadLen::

00000200 e92d4010    770 	stmfd	[sp]!,{r4,lr}

00000204 e1a04001    771 	mov	r4,r1

00000208 e5901054    772 	ldr	r1,[r0,84]

0000020c e3510003    773 	cmp	r1,3

00000210 13a00000    774 	movne	r0,0

00000214 1a000005    775 	bne	.L634

00000218 e5900058    776 	ldr	r0,[r0,88]

0000021c e5900000    777 	ldr	r0,[r0]

00000220 eb000000*   778 	bl	BerEncoder_Int32DetermineEncodedSize

00000224 e2800002    779 	add	r0,r0,2

00000228 e5840000    780 	str	r0,[r4]

0000022c e3a00001    781 	mov	r0,1

                     782 .L634:


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_efg1.s
00000230 e8bd8010    783 	ldmfd	[sp]!,{r4,pc}

                     784 	.endf	IEDOrCat_calcReadLen

                     785 	.align	4

                     786 ;extInfo	r0	local

                     787 

                     788 ;entity	r0	param

                     789 ;pLen	r4	param

                     790 

                     791 	.section ".bss","awb"

                     792 .L662:

                     793 	.data

                     794 	.text

                     795 

                     796 

                     797 	.align	4

                     798 	.align	4

                     799 IEDOrCat_write::

00000234 e92d4010    800 	stmfd	[sp]!,{r4,lr}

00000238 e24dd004    801 	sub	sp,sp,4

0000023c e1a04000    802 	mov	r4,r0

00000240 e5940050    803 	ldr	r0,[r4,80]

00000244 e350000a    804 	cmp	r0,10

00000248 05940054    805 	ldreq	r0,[r4,84]

0000024c e1a03002    806 	mov	r3,r2

00000250 03500003    807 	cmpeq	r0,3

00000254 13a00007    808 	movne	r0,7

00000258 1a000009    809 	bne	.L676

0000025c e1a0200d    810 	mov	r2,sp

00000260 e1a00003    811 	mov	r0,r3

00000264 e3a01085    812 	mov	r1,133

00000268 eb000000*   813 	bl	BufferView_decodeInt32TL

0000026c e3500000    814 	cmp	r0,0

00000270 159d1000    815 	ldrne	r1,[sp]

00000274 15940058    816 	ldrne	r0,[r4,88]

00000278 15801000    817 	strne	r1,[r0]

0000027c 13e00000    818 	mvnne	r0,0

00000280 03a0000b    819 	moveq	r0,11

                     820 .L676:

00000284 e28dd004    821 	add	sp,sp,4

00000288 e8bd8010    822 	ldmfd	[sp]!,{r4,pc}

                     823 	.endf	IEDOrCat_write

                     824 	.align	4

                     825 ;decodedVal	[sp]	local

                     826 ;extInfo	r0	local

                     827 

                     828 ;entity	r4	param

                     829 ;isoConn	none	param

                     830 ;value	r3	param

                     831 

                     832 	.section ".bss","awb"

                     833 .L734:

                     834 	.data

                     835 	.text

                     836 

                     837 

                     838 	.align	4

                     839 	.align	4

                     840 IEDOrIdent_init::

0000028c e92d4010    841 	stmfd	[sp]!,{r4,lr}

00000290 e1a04000    842 	mov	r4,r0

00000294 e3a0000a    843 	mov	r0,10


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_efg1.s
00000298 e5840050    844 	str	r0,[r4,80]

0000029c e3a00002    845 	mov	r0,2

000002a0 e5840054    846 	str	r0,[r4,84]

000002a4 e3a00044    847 	mov	r0,68

000002a8 eb000000*   848 	bl	IEDEntity_alloc

000002ac e3500000    849 	cmp	r0,0

000002b0 020000ff    850 	andeq	r0,r0,255

000002b4 15840058    851 	strne	r0,[r4,88]

000002b8 13a00001    852 	movne	r0,1

000002bc e8bd8010    853 	ldmfd	[sp]!,{r4,pc}

                     854 	.endf	IEDOrIdent_init

                     855 	.align	4

                     856 ;extInfo	r0	local

                     857 

                     858 ;entity	r4	param

                     859 

                     860 	.section ".bss","awb"

                     861 .L805:

                     862 	.data

                     863 	.text

                     864 

                     865 

                     866 	.align	4

                     867 	.align	4

                     868 IEDOrCat_init::

000002c0 e92d4010    869 	stmfd	[sp]!,{r4,lr}

000002c4 e1a04000    870 	mov	r4,r0

000002c8 e3a0000a    871 	mov	r0,10

000002cc e5840050    872 	str	r0,[r4,80]

000002d0 e3a00003    873 	mov	r0,3

000002d4 e5840054    874 	str	r0,[r4,84]

000002d8 e3a00004    875 	mov	r0,4

000002dc eb000000*   876 	bl	IEDEntity_alloc

000002e0 e3500000    877 	cmp	r0,0

000002e4 020000ff    878 	andeq	r0,r0,255

000002e8 15840058    879 	strne	r0,[r4,88]

000002ec 13a00001    880 	movne	r0,1

000002f0 e8bd8010    881 	ldmfd	[sp]!,{r4,pc}

                     882 	.endf	IEDOrCat_init

                     883 	.align	4

                     884 ;extInfo	r0	local

                     885 

                     886 ;entity	r4	param

                     887 

                     888 	.section ".bss","awb"

                     889 .L869:

                     890 	.data

                     891 	.text

                     892 

                     893 

                     894 	.align	4

                     895 	.align	4

                     896 	.align	4

                     897 IEDCtlNum_init::

000002f4 e3a02007    898 	mov	r2,7

000002f8 e5802050    899 	str	r2,[r0,80]

000002fc e8910006    900 	ldmfd	[r1],{r1-r2}

00000300 e59f32c8*   901 	ldr	r3,.L917

00000304 e0822001    902 	add	r2,r2,r1

00000308 e5931000    903 	ldr	r1,[r3]

0000030c e0421001    904 	sub	r1,r2,r1


                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_efg1.s
00000310 e5801058    905 	str	r1,[r0,88]

00000314 e3a00001    906 	mov	r0,1

00000318 e12fff1e*   907 	ret	

                     908 	.endf	IEDCtlNum_init

                     909 	.align	4

                     910 

                     911 ;entity	r0	param

                     912 ;ber	r1	param

                     913 

                     914 	.section ".bss","awb"

                     915 .L910:

                     916 	.data

                     917 	.text

                     918 

                     919 

                     920 	.align	4

                     921 	.align	4

                     922 IEDOrCat_encodeRead::

0000031c e5902054    923 	ldr	r2,[r0,84]

00000320 e3520003    924 	cmp	r2,3

00000324 05900058    925 	ldreq	r0,[r0,88]

00000328 05902000    926 	ldreq	r2,[r0]

0000032c 01a00001    927 	moveq	r0,r1

00000330 03a01085    928 	moveq	r1,133

00000334 0a000000*   929 	beq	BufferView_encodeInt32

00000338 e3a00000    930 	mov	r0,0

0000033c e12fff1e*   931 	ret	

                     932 	.endf	IEDOrCat_encodeRead

                     933 	.align	4

                     934 ;extInfo	r0	local

                     935 

                     936 ;entity	r0	param

                     937 ;outBuf	r1	param

                     938 

                     939 	.section ".bss","awb"

                     940 .L955:

                     941 	.data

                     942 	.text

                     943 

                     944 

                     945 	.align	4

                     946 	.align	4

                     947 IEDOrIdent_calcReadLen::

00000340 e5902054    948 	ldr	r2,[r0,84]

00000344 e3520002    949 	cmp	r2,2

00000348 13a00000    950 	movne	r0,0

0000034c 05900058    951 	ldreq	r0,[r0,88]

00000350 05900000    952 	ldreq	r0,[r0]

00000354 02800002    953 	addeq	r0,r0,2

00000358 05810000    954 	streq	r0,[r1]

0000035c 03a00001    955 	moveq	r0,1

00000360 e12fff1e*   956 	ret	

                     957 	.endf	IEDOrIdent_calcReadLen

                     958 	.align	4

                     959 ;extInfo	r0	local

                     960 

                     961 ;entity	r0	param

                     962 ;pLen	r1	param

                     963 

                     964 	.section ".bss","awb"

                     965 .L998:


                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_efg1.s
                     966 	.data

                     967 	.text

                     968 

                     969 

                     970 	.align	4

                     971 	.align	4

                     972 IEDOrIdent_encodeRead::

00000364 e5902054    973 	ldr	r2,[r0,84]

00000368 e3520002    974 	cmp	r2,2

0000036c 05902058    975 	ldreq	r2,[r0,88]

00000370 01a00001    976 	moveq	r0,r1

00000374 04923004    977 	ldreq	r3,[r2],4

00000378 03a01089    978 	moveq	r1,137

0000037c 0a000000*   979 	beq	BufferView_encodeOctetString

00000380 e3a00000    980 	mov	r0,0

00000384 e12fff1e*   981 	ret	

                     982 	.endf	IEDOrIdent_encodeRead

                     983 	.align	4

                     984 ;extInfo	r0	local

                     985 

                     986 ;entity	r0	param

                     987 ;outBuf	r1	param

                     988 

                     989 	.section ".bss","awb"

                     990 .L1056:

                     991 	.data

                     992 	.text

                     993 

                     994 

                     995 	.align	4

                     996 	.align	4

                     997 IEDOrIdent_write::

00000388 e92d4010    998 	stmfd	[sp]!,{r4,lr}

0000038c e24dd008    999 	sub	sp,sp,8

00000390 e1a04000   1000 	mov	r4,r0

00000394 e5940050   1001 	ldr	r0,[r4,80]

00000398 e350000a   1002 	cmp	r0,10

0000039c 05940054   1003 	ldreq	r0,[r4,84]

000003a0 e1a03002   1004 	mov	r3,r2

000003a4 03500002   1005 	cmpeq	r0,2

000003a8 13a00007   1006 	movne	r0,7

000003ac 1a000011   1007 	bne	.L1070

000003b0 e1a0200d   1008 	mov	r2,sp

000003b4 e1a00003   1009 	mov	r0,r3

000003b8 e3a01089   1010 	mov	r1,137

000003bc eb000000*  1011 	bl	BufferView_decodeStringViewTL

000003c0 e3500000   1012 	cmp	r0,0

000003c4 0a000002   1013 	beq	.L1080

000003c8 e59d2000   1014 	ldr	r2,[sp]

000003cc e3520040   1015 	cmp	r2,64

000003d0 9a000001   1016 	bls	.L1079

                    1017 .L1080:

000003d4 e3a0000b   1018 	mov	r0,11

000003d8 ea000006   1019 	b	.L1070

                    1020 .L1079:

000003dc e5940058   1021 	ldr	r0,[r4,88]

000003e0 e3520000   1022 	cmp	r2,0

000003e4 e5802000   1023 	str	r2,[r0]

000003e8 159d1004   1024 	ldrne	r1,[sp,4]

000003ec 12800004   1025 	addne	r0,r0,4

000003f0 1b000000*  1026 	blne	memcpy


                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_efg1.s
000003f4 e3e00000   1027 	mvn	r0,0

                    1028 .L1070:

000003f8 e28dd008   1029 	add	sp,sp,8

000003fc e8bd8010   1030 	ldmfd	[sp]!,{r4,pc}

                    1031 	.endf	IEDOrIdent_write

                    1032 	.align	4

                    1033 ;decodedVal	[sp]	local

                    1034 ;extInfo	r0	local

                    1035 

                    1036 ;entity	r4	param

                    1037 ;isoConn	none	param

                    1038 ;value	r3	param

                    1039 

                    1040 	.section ".bss","awb"

                    1041 .L1179:

                    1042 	.data

                    1043 	.text

                    1044 

                    1045 

                    1046 	.align	4

                    1047 	.align	4

                    1048 IEDControlDA_init::

00000400 e92d4070   1049 	stmfd	[sp]!,{r4-r6,lr}

00000404 e24dd008   1050 	sub	sp,sp,8

00000408 e1a05001   1051 	mov	r5,r1

0000040c e1a06000   1052 	mov	r6,r0

00000410 e3a00028   1053 	mov	r0,40

00000414 eb000000*  1054 	bl	IEDEntity_alloc

00000418 e1b04000   1055 	movs	r4,r0

0000041c 0a00005b   1056 	beq	.L1249

00000420 e5864058   1057 	str	r4,[r6,88]

00000424 e3a00001   1058 	mov	r0,1

00000428 e5860054   1059 	str	r0,[r6,84]

0000042c e28f1000*  1060 	adr	r1,.L1622

00000430 e2860048   1061 	add	r0,r6,72

00000434 eb000000*  1062 	bl	StringView_cmpCStr

00000438 e3500000   1063 	cmp	r0,0

0000043c 1a000008   1064 	bne	.L1212

00000440 e28d2004   1065 	add	r2,sp,4

00000444 e28d1003   1066 	add	r1,sp,3

00000448 e5840008   1067 	str	r0,[r4,8]

0000044c e1a00005   1068 	mov	r0,r5

00000450 e3a03000   1069 	mov	r3,0

00000454 eb000000*  1070 	bl	BufferView_decodeTL

00000458 e3500000   1071 	cmp	r0,0

0000045c 0a00004b   1072 	beq	.L1249

00000460 ea00001c   1073 	b	.L1222

                    1074 .L1212:

00000464 e28f1000*  1075 	adr	r1,.L1623

00000468 e2860048   1076 	add	r0,r6,72

0000046c eb000000*  1077 	bl	StringView_cmpCStr

00000470 e3500000   1078 	cmp	r0,0

00000474 1a000009   1079 	bne	.L1215

00000478 e28d2004   1080 	add	r2,sp,4

0000047c e28d1003   1081 	add	r1,sp,3

00000480 e3a00001   1082 	mov	r0,1

00000484 e5840008   1083 	str	r0,[r4,8]

00000488 e1a00005   1084 	mov	r0,r5

0000048c e3a03000   1085 	mov	r3,0

00000490 eb000000*  1086 	bl	BufferView_decodeTL

00000494 e3500000   1087 	cmp	r0,0


                                                                      Page 19
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_efg1.s
00000498 0a00003c   1088 	beq	.L1249

0000049c ea00000d   1089 	b	.L1222

                    1090 .L1215:

000004a0 e28f1000*  1091 	adr	r1,.L1624

000004a4 e2860048   1092 	add	r0,r6,72

000004a8 eb000000*  1093 	bl	StringView_cmpCStr

000004ac e3500000   1094 	cmp	r0,0

000004b0 1a000036   1095 	bne	.L1249

000004b4 e28d2004   1096 	add	r2,sp,4

000004b8 e28d1003   1097 	add	r1,sp,3

000004bc e3a00002   1098 	mov	r0,2

000004c0 e5840008   1099 	str	r0,[r4,8]

000004c4 e1a00005   1100 	mov	r0,r5

000004c8 e3a03000   1101 	mov	r3,0

000004cc eb000000*  1102 	bl	BufferView_decodeTL

000004d0 e3500000   1103 	cmp	r0,0

000004d4 0a00002d   1104 	beq	.L1249

                    1105 .L1222:

000004d8 e5dd0003   1106 	ldrb	r0,[sp,3]

000004dc e35000f1   1107 	cmp	r0,241

000004e0 1a000037   1108 	bne	.L1253

000004e4 e59d2004   1109 	ldr	r2,[sp,4]

000004e8 e895000a   1110 	ldmfd	[r5],{r1,r3}

000004ec e1a00005   1111 	mov	r0,r5

000004f0 e0831001   1112 	add	r1,r3,r1

000004f4 e3a03000   1113 	mov	r3,0

000004f8 eb000000*  1114 	bl	BufferView_init

000004fc e2841014   1115 	add	r1,r4,20

00000500 e1a00005   1116 	mov	r0,r5

00000504 eb000000*  1117 	bl	IEDModel_getTermItemDescrStruct

00000508 e3500000   1118 	cmp	r0,0

0000050c 0a00001f   1119 	beq	.L1249

00000510 e2841018   1120 	add	r1,r4,24

00000514 e1a00005   1121 	mov	r0,r5

00000518 eb000000*  1122 	bl	IEDModel_getTermItemDescrStruct

0000051c e3500000   1123 	cmp	r0,0

00000520 0a00001a   1124 	beq	.L1249

00000524 e284101c   1125 	add	r1,r4,28

00000528 e1a00005   1126 	mov	r0,r5

0000052c eb000000*  1127 	bl	IEDModel_getTermItemDescrStruct

00000530 e3500000   1128 	cmp	r0,0

00000534 0a000015   1129 	beq	.L1249

00000538 e2841020   1130 	add	r1,r4,32

0000053c e1a00005   1131 	mov	r0,r5

00000540 eb000000*  1132 	bl	IEDModel_getTermItemDescrStruct

00000544 e3500000   1133 	cmp	r0,0

00000548 0a000010   1134 	beq	.L1249

0000054c e5940008   1135 	ldr	r0,[r4,8]

00000550 e3500001   1136 	cmp	r0,1

00000554 1a000008   1137 	bne	.L1240

00000558 e2841024   1138 	add	r1,r4,36

0000055c e1a00005   1139 	mov	r0,r5

00000560 eb000000*  1140 	bl	IEDModel_getTermItemDescrStruct

00000564 e3500000   1141 	cmp	r0,0

00000568 0a000008   1142 	beq	.L1249

0000056c e1a00006   1143 	mov	r0,r6

00000570 eb000000*  1144 	bl	Control_registerCtrlObj

00000574 e3500000   1145 	cmp	r0,0

00000578 0a000004   1146 	beq	.L1249

                    1147 .L1240:

0000057c e28f1000*  1148 	adr	r1,.L1625


                                                                      Page 20
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_efg1.s
00000580 e1a00006   1149 	mov	r0,r6

00000584 eb000000*  1150 	bl	IEDEntity_getChildByCStrName

00000588 e1b05000   1151 	movs	r5,r0

0000058c 1a000001   1152 	bne	.L1248

                    1153 .L1249:

00000590 e3a00000   1154 	mov	r0,0

00000594 ea00000b   1155 	b	.L1207

                    1156 .L1248:

00000598 e28f1000*  1157 	adr	r1,.L1626

0000059c eb000000*  1158 	bl	IEDEntity_getChildByCStrName

000005a0 e3500000   1159 	cmp	r0,0

000005a4 15900058   1160 	ldrne	r0,[r0,88]

000005a8 e28f1000*  1161 	adr	r1,.L1627

000005ac 15840000   1162 	strne	r0,[r4]

000005b0 e1a00005   1163 	mov	r0,r5

000005b4 eb000000*  1164 	bl	IEDEntity_getChildByCStrName

000005b8 e3500000   1165 	cmp	r0,0

000005bc 15900058   1166 	ldrne	r0,[r0,88]

000005c0 15840004   1167 	strne	r0,[r4,4]

                    1168 .L1253:

000005c4 e3a00001   1169 	mov	r0,1

                    1170 .L1207:

000005c8 e28dd008   1171 	add	sp,sp,8

000005cc e8bd8070   1172 	ldmfd	[sp]!,{r4-r6,pc}

                    1173 	.endf	IEDControlDA_init

                    1174 	.align	4

                    1175 .L917:

000005d0 00000000*  1176 	.data.w	iedModel

                    1177 	.type	.L917,$object

                    1178 	.size	.L917,4

                    1179 

                    1180 .L1622:

                    1181 ;	"SBOw\000"

000005d4 774f4253   1182 	.data.b	83,66,79,119

000005d8 00        1183 	.data.b	0

000005d9 000000    1184 	.align 4

                    1185 

                    1186 	.type	.L1622,$object

                    1187 	.size	.L1622,4

                    1188 

                    1189 .L1623:

                    1190 ;	"Oper\000"

000005dc 7265704f   1191 	.data.b	79,112,101,114

000005e0 00        1192 	.data.b	0

000005e1 000000    1193 	.align 4

                    1194 

                    1195 	.type	.L1623,$object

                    1196 	.size	.L1623,4

                    1197 

                    1198 .L1624:

                    1199 ;	"Cancel\000"

000005e4 636e6143   1200 	.data.b	67,97,110,99

000005e8 6c65      1201 	.data.b	101,108

000005ea 00        1202 	.data.b	0

000005eb 00        1203 	.align 4

                    1204 

                    1205 	.type	.L1624,$object

                    1206 	.size	.L1624,4

                    1207 

                    1208 .L1625:

                    1209 ;	"origin\000"


                                                                      Page 21
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_efg1.s
000005ec 6769726f   1210 	.data.b	111,114,105,103

000005f0 6e69      1211 	.data.b	105,110

000005f2 00        1212 	.data.b	0

000005f3 00        1213 	.align 4

                    1214 

                    1215 	.type	.L1625,$object

                    1216 	.size	.L1625,4

                    1217 

                    1218 .L1626:

                    1219 ;	"orIdent\000"

000005f4 6449726f   1220 	.data.b	111,114,73,100

000005f8 00746e65   1221 	.data.b	101,110,116,0

                    1222 	.align 4

                    1223 

                    1224 	.type	.L1626,$object

                    1225 	.size	.L1626,4

                    1226 

                    1227 .L1627:

                    1228 ;	"orCat\000"

000005fc 6143726f   1229 	.data.b	111,114,67,97

00000600 0074      1230 	.data.b	116,0

00000602 0000      1231 	.align 4

                    1232 

                    1233 	.type	.L1627,$object

                    1234 	.size	.L1627,4

                    1235 

                    1236 	.align	4

                    1237 ;tag	[sp,3]	local

                    1238 ;len	[sp,4]	local

                    1239 ;origin	r5	local

                    1240 ;orCat	r0	local

                    1241 ;orIdent	r0	local

                    1242 ;extInfo	r4	local

                    1243 ;.L1542	.L1552	static

                    1244 ;.L1543	.L1553	static

                    1245 ;.L1544	.L1554	static

                    1246 ;.L1545	.L1555	static

                    1247 ;.L1546	.L1550	static

                    1248 ;.L1547	.L1551	static

                    1249 

                    1250 ;entity	r6	param

                    1251 ;ctrlInfoBER	r5	param

                    1252 

                    1253 	.section ".bss","awb"

                    1254 .L1541:

                    1255 	.data

                    1256 	.text

                    1257 

                    1258 

                    1259 	.align	4

                    1260 	.align	4

                    1261 IEDControlDO_init::

00000604 e92d4010   1262 	stmfd	[sp]!,{r4,lr}

00000608 e1a04000   1263 	mov	r4,r0

0000060c e3a00005   1264 	mov	r0,5

00000610 e5840050   1265 	str	r0,[r4,80]

00000614 e3a00004   1266 	mov	r0,4

00000618 eb000000*  1267 	bl	IEDEntity_alloc

0000061c e3a01001   1268 	mov	r1,1

00000620 e5841054   1269 	str	r1,[r4,84]

00000624 e3500000   1270 	cmp	r0,0


                                                                      Page 22
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_efg1.s
00000628 13a00001   1271 	movne	r0,1

0000062c e8bd8010   1272 	ldmfd	[sp]!,{r4,pc}

                    1273 	.endf	IEDControlDO_init

                    1274 	.align	4

                    1275 

                    1276 ;entity	r4	param

                    1277 

                    1278 	.section ".bss","awb"

                    1279 .L1669:

                    1280 	.data

                    1281 	.text

                    1282 

                    1283 

                    1284 ;514: 


                    1285 ;515: MmsDataAccessError IEDControlDA_write(IEDEntity entity,


                    1286 	.align	4

                    1287 	.align	4

                    1288 IEDControlDA_write::

00000630 e92d4070   1289 	stmfd	[sp]!,{r4-r6,lr}

                    1290 ;516:                                          IsoConnection* isoConn, BufferView* value)


                    1291 ;517: {


                    1292 

                    1293 ;518:     uint8_t addCause;


                    1294 ;519:     IEDControlDA* extInfo = entity->extInfo;


                    1295 

00000634 e1a06001   1296 	mov	r6,r1

00000638 e1a05000   1297 	mov	r5,r0

0000063c e5954058   1298 	ldr	r4,[r5,88]

                    1299 ;520: 


                    1300 ;521:     //Пишем value 1 или 0


                    1301 ;522:     if(!writeCtlValue(extInfo, value))


                    1302 

                    1303 ;497: {


                    1304 

                    1305 ;498:     uint8_t* pValue = value->p + value->pos;


                    1306 

00000640 e892000a   1307 	ldmfd	[r2],{r1,r3}

00000644 e1a00003   1308 	mov	r0,r3

                    1309 ;499:     if (pValue[0] != IEC61850_BER_BOOLEAN || pValue[1] != 1)


                    1310 

00000648 e7f02001   1311 	ldrb	r2,[r0,r1]!

0000064c e3520083   1312 	cmp	r2,131

00000650 05d01001   1313 	ldreqb	r1,[r0,1]

00000654 03510001   1314 	cmpeq	r1,1

                    1315 ;511:     }


                    1316 ;512:     return true;


                    1317 

                    1318 ;523:     {


                    1319 

                    1320 ;524:         return DATA_ACCESS_ERROR_OBJECT_VALUE_INVALID;


                    1321 

00000658 13a0000b   1322 	movne	r0,11

0000065c 1a00001b   1323 	bne	.L1676

                    1324 ;500:     {


                    1325 

                    1326 ;501:         return false;


                    1327 

                    1328 ;502:     }


                    1329 ;503: 


                    1330 ;504:     if(pValue[2])


                    1331 


                                                                      Page 23
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_efg1.s
00000660 e5d00002   1332 	ldrb	r0,[r0,2]

00000664 e3500000   1333 	cmp	r0,0

                    1334 ;507:     }


                    1335 ;508:     else


                    1336 ;509:     {


                    1337 

                    1338 ;510:         writeTele(ctrlInfo->ctlValZero->valueOffset);


                    1339 

00000668 05940020   1340 	ldreq	r0,[r4,32]

                    1341 ;505:     {


                    1342 

                    1343 ;506:         writeTele(ctrlInfo->ctlValOne->valueOffset);


                    1344 

0000066c 1594001c   1345 	ldrne	r0,[r4,28]

00000670 e5900004   1346 	ldr	r0,[r0,4]

00000674 eb000000*  1347 	bl	writeTele

                    1348 ;525:     }


                    1349 ;526: 


                    1350 ;527:     //Читать ready пока не созреет


                    1351 ;528:     if(!IEDControlDA_waitReady(entity))


                    1352 

00000678 e1a00005   1353 	mov	r0,r5

0000067c ebfffe73*  1354 	bl	IEDControlDA_waitReady

00000680 e3500000   1355 	cmp	r0,0

00000684 0a000009   1356 	beq	.L1697

                    1357 ;529:     {


                    1358 

                    1359 ;530:         return DATA_ACCESS_ERROR_UNKNOWN;


                    1360 

                    1361 ;531:     }


                    1362 ;532: 


                    1363 ;533:     //Считать код


                    1364 ;534:     addCause = readIntValue(extInfo->code);


                    1365 

00000688 e5940014   1366 	ldr	r0,[r4,20]

0000068c eb000000*  1367 	bl	readIntValue

00000690 e21020ff   1368 	ands	r2,r0,255

                    1369 ;535: 


                    1370 ;536:     if(addCause != 0)


                    1371 

00000694 0a000007   1372 	beq	.L1694

                    1373 ;537:     {


                    1374 

                    1375 ;538:         if(!Control_sendServiceErrorReport(isoConn, entity, addCause))


                    1376 

00000698 e1a01005   1377 	mov	r1,r5

0000069c e1a00006   1378 	mov	r0,r6

000006a0 eb000000*  1379 	bl	Control_sendServiceErrorReport

000006a4 e3500000   1380 	cmp	r0,0

                    1381 ;541:         }


                    1382 ;542:         else


                    1383 ;543:         {


                    1384 

                    1385 ;544:             return DATA_ACCESS_ERROR_OBJECT_ACCESS_DENIED;


                    1386 

000006a8 13a00003   1387 	movne	r0,3

000006ac 1a000007   1388 	bne	.L1676

                    1389 .L1697:

                    1390 ;539:         {


                    1391 

                    1392 ;540:             return DATA_ACCESS_ERROR_UNKNOWN;



                                                                      Page 24
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_efg1.s
                    1393 

000006b0 e3a0000c   1394 	mov	r0,12

000006b4 ea000005   1395 	b	.L1676

                    1396 .L1694:

                    1397 ;545:         }


                    1398 ;546:     }


                    1399 ;547:     if(extInfo->type == CONTROL_OPER)


                    1400 

000006b8 e5940008   1401 	ldr	r0,[r4,8]

000006bc e3500001   1402 	cmp	r0,1

                    1403 ;548:     {


                    1404 

                    1405 ;549:         //Включаем контроль выполнения операции для посылки


                    1406 ;550:         // CommandTermination по окончании


                    1407 ;551:         extInfo->isoConn = isoConn;


                    1408 

000006c0 05846010   1409 	streq	r6,[r4,16]

                    1410 ;552:         extInfo->waitTerminate = true;


                    1411 

000006c4 03a00001   1412 	moveq	r0,1

000006c8 05c4000c   1413 	streqb	r0,[r4,12]

                    1414 ;553:     }


                    1415 ;554:     return DATA_ACCESS_ERROR_SUCCESS;


                    1416 

000006cc e3e00000   1417 	mvn	r0,0

                    1418 .L1676:

000006d0 e8bd8070   1419 	ldmfd	[sp]!,{r4-r6,pc}

                    1420 	.endf	IEDControlDA_write

                    1421 	.align	4

                    1422 ;addCause	r2	local

                    1423 ;extInfo	r4	local

                    1424 ;pValue	r0	local

                    1425 

                    1426 ;entity	r5	param

                    1427 ;isoConn	r6	param

                    1428 ;value	r2	param

                    1429 

                    1430 	.section ".bss","awb"

                    1431 .L1872:

                    1432 	.data

                    1433 	.text

                    1434 

                    1435 ;555: }


                    1436 	.align	4

                    1437 ;iedModel	iedModel	import

                    1438 

                    1439 	.data

                    1440 	.ghsnote version,6

                    1441 	.ghsnote tools,1

                    1442 	.ghsnote options,0

                    1443 	.text

                    1444 	.align	4

