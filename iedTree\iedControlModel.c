#include "iedControlModel.h"



#include "../iedmodel.h"
#include "../timers.h"
#include "iedFinalDA.h"
#include "iedObjects.h"
#include "../mms_data.h"
#include "../pwin_access.h"
#include "../control.h"

#include "../AsnEncoding.h"

#include "IEDCompile/AccessInfo.h"
#include "IEDCompile/InnerAttributeTypes.h"

#include <Clib.h>

#include <string.h>

//Сколько ждать готовности после передачи сигнала объекта управления
//в алгоритмы.
//Таймаут сделан для избежания зависания протокола при ошибке в алгоритмах.
#define CTRL_READY_TIMEOUT 5000

typedef struct {
    size_t len;
    char orIdent[64];
} IEDOrIdent;

typedef struct {
    int orCat;
} IEDOrCat;

typedef enum {
    CONTROL_SBOW,
    CONTROL_OPER,
    CONTROL_CANCEL
} ControlType;

typedef struct {
    IEDOrIdent *orIdent;
    IEDOrCat *orCat;
    ControlType type;
    //true если операция в процессе выполнения и ожидает
    //сигнала terminate
    bool waitTerminate;
    //Соединение, в которое отправлять отчёт
    IsoConnection* isoConn;
    IntBoolAccessInfo* code;
    IntBoolAccessInfo* ready;
    IntBoolAccessInfo* ctlValOne;
    IntBoolAccessInfo* ctlValZero;
    IntBoolAccessInfo* terminate;
} IEDControlDA;

typedef struct {
    //Пока просто какой-то мусор
    int reserved;
} IEDControlDO;


bool IEDControlDA_isControlDA(IEDEntity entity)
{
    return entity->type == IED_ENTITY_DA
            && entity->subType == DA_SUBTYPE_CONTROL;
}

bool IEDControlDA_isReady(IEDEntity controlDA)
{
    IEDControlDA *extInfo;
    if(!IEDControlDA_isControlDA(controlDA))
    {
        ERROR_REPORT("Invalid control DA");
        return false;
    }
    extInfo = controlDA->extInfo;
    return readBoolValue(extInfo->ready);
}

bool IEDControlDA_waitReady(IEDEntity controlDA)
{
    uint32_t startTime;
    IEDControlDA *extInfo;
    if(!IEDControlDA_isControlDA(controlDA))
    {
        ERROR_REPORT("Invalid control DA");
        return false;
    }
    extInfo = controlDA->extInfo;

    startTime = Timers_getTickCount();
    while(!readBoolValue(extInfo->ready))
    {
        if(Timers_isTimeout(startTime, CTRL_READY_TIMEOUT))
        {
            ERROR_REPORT("Timeout in IEDControlDA_waitReady");
            return false;
        }
        Idle();
    };
    return true;
}

void IEDControlDA_checkTerminate(IEDEntity controlDA)
{
    IEDControlDA* extInfo;
    uint8_t terminateCode;

    if(!IEDControlDA_isControlDA(controlDA))
    {
        return;
    }
    extInfo = controlDA->extInfo;
    if(extInfo->type != CONTROL_OPER)
    {
        return;
    }

    if(!extInfo->waitTerminate)
    {
        return;
    }

    if(!readBoolValue(extInfo->terminate))
    {
        return;
    }

    extInfo->waitTerminate = false;
    if(extInfo->isoConn == NULL)
    {
        return;
    }
    terminateCode = readIntValue(extInfo->code);

    if(terminateCode == 0)
    {
        Control_sendPositiveCmdTermReport(extInfo->isoConn, controlDA);
    }
    else
    {
        Control_sendNegativeCmdTermReport(extInfo->isoConn, controlDA,
                                          terminateCode);
    }
    extInfo->isoConn = NULL;
}

void IEDControlDA_disconnect(IEDEntity controlDA)
{
    IEDControlDA* extInfo;

    if(!IEDControlDA_isControlDA(controlDA))
    {
        return;
    }
    extInfo = controlDA->extInfo;
    if(extInfo->type != CONTROL_OPER)
    {
        return;
    }

    if(!extInfo->waitTerminate || extInfo->isoConn == NULL)
    {
        return;
    }

    extInfo->waitTerminate = false;
    extInfo->isoConn = NULL;
}

bool IEDControlDA_getOrIdent(IEDEntity entity, StringView *orIdent)
{
    IEDControlDA *extInfo;
    if(!IEDControlDA_isControlDA(entity))
    {
        return false;
    }

    extInfo = entity->extInfo;
    if(extInfo->orIdent == NULL)
    {
        return false;
    }

    StringView_init(orIdent, extInfo->orIdent->orIdent, extInfo->orIdent->len);
    return true;
}

bool IEDControlDA_getOrCat(IEDEntity entity, int32_t *orCat)
{
    IEDControlDA *extInfo;
    if(!IEDControlDA_isControlDA(entity))
    {
        return false;
    }

    extInfo = entity->extInfo;
    if(extInfo->orCat == NULL)
    {
        return false;
    }

    *orCat = extInfo->orCat->orCat;

    return true;
}

bool IEDOrCat_calcReadLen(IEDEntity entity, size_t *pLen)
{
    IEDOrCat *extInfo;

    if(entity->subType != DA_SUBTYPE_ORCAT)
    {
        return false;
    }

    extInfo = entity->extInfo;

    *pLen = 2 + BerEncoder_Int32DetermineEncodedSize(extInfo->orCat);

    return true;
}

MmsDataAccessError IEDOrCat_write(IEDEntity entity,
                                           IsoConnection* isoConn, BufferView* value)
{
    int32_t decodedVal;
    IEDOrCat *extInfo;

    if(entity->type != IED_ENTITY_DA_VAR
            || entity->subType != DA_SUBTYPE_ORCAT)
    {
        return DATA_ACCESS_ERROR_TYPE_INCONSISTENT;
    }

    if(!BufferView_decodeInt32TL(value, IEC61850_BER_INTEGER, &decodedVal))
    {
        return DATA_ACCESS_ERROR_OBJECT_VALUE_INVALID;
    }

    extInfo = entity->extInfo;

    extInfo->orCat = decodedVal;

    return DATA_ACCESS_ERROR_SUCCESS;
}


bool IEDOrIdent_init(IEDEntity entity)
{
    IEDOrIdent* extInfo;
    entity->type = IED_ENTITY_DA_VAR;
    entity->subType = DA_SUBTYPE_ORIDENT;
    extInfo = IEDEntity_alloc(sizeof(IEDOrIdent));
    if(extInfo == NULL)
    {
        return false;
    }
    entity->extInfo = extInfo;
    return true;
}

bool IEDOrCat_init(IEDEntity entity)
{
    IEDOrCat* extInfo;
    entity->type = IED_ENTITY_DA_VAR;
    entity->subType = DA_SUBTYPE_ORCAT;
    extInfo = IEDEntity_alloc(sizeof(IEDOrCat));
    if(extInfo == NULL)
    {
        return false;
    }

    entity->extInfo = extInfo;
    return true;
}

bool IEDCtlNum_init(IEDEntity entity,BufferView* ber)
{	
	//!!! Всё это временно пока нет ясности с ctlNum

	entity->type = IED_ENTITY_DA_CONST;	
	//Сохраняем позицию константы в информационной модели
	entity->extInfo = (void*)(ber->p + ber->pos - iedModel);
	return true;
}

bool IEDOrCat_encodeRead(IEDEntity entity, BufferView *outBuf)
{
    IEDOrCat *extInfo;

    if(entity->subType != DA_SUBTYPE_ORCAT)
    {
        return false;
    }

    extInfo = entity->extInfo;

    return BufferView_encodeInt32(outBuf, IEC61850_BER_INTEGER,
                                  extInfo->orCat);
}

bool IEDOrIdent_calcReadLen(IEDEntity entity, size_t *pLen)
{
    IEDOrIdent *extInfo;

    if(entity->subType != DA_SUBTYPE_ORIDENT)
    {
        return false;
    }
    extInfo = entity->extInfo;

    *pLen = extInfo->len + 2;

    return true;
}

bool IEDOrIdent_encodeRead(IEDEntity entity, BufferView *outBuf)
{
    IEDOrIdent *extInfo;

    if(entity->subType != DA_SUBTYPE_ORIDENT)
    {
        return false;
    }

    extInfo = entity->extInfo;

    return BufferView_encodeOctetString(outBuf, IEC61850_BER_OCTET_STRING,
                                     extInfo->orIdent, extInfo->len);
}

MmsDataAccessError IEDOrIdent_write(IEDEntity entity,
                                           IsoConnection* isoConn, BufferView* value)
{
    StringView decodedVal;
    IEDOrIdent *extInfo;

    if(entity->type != IED_ENTITY_DA_VAR
            || entity->subType != DA_SUBTYPE_ORIDENT)
    {
        return DATA_ACCESS_ERROR_TYPE_INCONSISTENT;
    }


    if(!BufferView_decodeStringViewTL(value, IEC61850_BER_OCTET_STRING,
                                  &decodedVal))
    {
        return DATA_ACCESS_ERROR_OBJECT_VALUE_INVALID;
    }

    if(decodedVal.len > 64)
    {
        return DATA_ACCESS_ERROR_OBJECT_VALUE_INVALID;
    }

    extInfo = entity->extInfo;

    extInfo->len = decodedVal.len;
    if(extInfo->len != 0)
    {
        memcpy(extInfo->orIdent, decodedVal.p, extInfo->len);
    }

    return DATA_ACCESS_ERROR_SUCCESS;
}

bool IEDControlDA_init(IEDEntity entity, BufferView *ctrlInfoBER)
{
    uint8_t tag;
    size_t len;
    IEDEntity origin;
    IEDEntity orCat;
    IEDEntity orIdent;

    IEDControlDA* extInfo = IEDEntity_alloc(sizeof(IEDControlDA));
    if(extInfo == NULL)
    {
        return false;
    }
    entity->extInfo = extInfo;

    //Подтип
    entity->subType = DA_SUBTYPE_CONTROL;

    //По имени определить тип
    if(StringView_cmpCStr(&entity->name, "SBOw") == 0)
    {
        extInfo->type = CONTROL_SBOW;
    }
    else if(StringView_cmpCStr(&entity->name, "Oper") == 0)
    {
        extInfo->type = CONTROL_OPER;
    }
    else if(StringView_cmpCStr(&entity->name, "Cancel") == 0)
    {
        extInfo->type = CONTROL_CANCEL;
    }
    else
    {
        ERROR_REPORT("Invalid DA name");
        return false;
    }

    if(!BufferView_decodeTL(ctrlInfoBER, &tag, &len, NULL))
    {
        ERROR_REPORT("Invalid Ctrl info");
        return false;
    }

    if(tag != IED_CONTROL_INFO)
    {
        //Нет дополнительной информации
        return true;
    }
    //Подобъекты дополнительной информации
    BufferView_init(ctrlInfoBER, ctrlInfoBER->p + ctrlInfoBER->pos, len, 0);

    //Доступ к коду ошибки
    if(!IEDModel_getTermItemDescrStruct(ctrlInfoBER, (void**)&extInfo->code))
    {
        return false;
    }

    //Доступ к сигналу готовности
    if(!IEDModel_getTermItemDescrStruct(ctrlInfoBER, (void**)&extInfo->ready))
    {
        return false;
    }

    // Доступ к "1"
    if(!IEDModel_getTermItemDescrStruct(ctrlInfoBER, (void**)&extInfo->ctlValOne))
    {
        return false;
    }



    // Доступ к "0"
    if(!IEDModel_getTermItemDescrStruct(ctrlInfoBER,
                                       (void**)&extInfo->ctlValZero))
    {
        return false;
    }

    if(extInfo->type == CONTROL_OPER)
    {
        // Доступ к готовности Terminate
        if(!IEDModel_getTermItemDescrStruct(ctrlInfoBER,
                                           (void**)&extInfo->terminate))
        {
            return false;
        }
        if(!Control_registerCtrlObj(entity))
        {
            return false;
        }
    }

    origin = IEDEntity_getChildByCStrName(entity, "origin");
    if(origin == NULL)
    {
        return false;
    }

    orIdent = IEDEntity_getChildByCStrName(origin, "orIdent");
    if(orIdent != NULL)
    {
        extInfo->orIdent = orIdent->extInfo;
    }

    orCat = IEDEntity_getChildByCStrName(origin, "orCat");
    if(orCat != NULL)
    {
        extInfo->orCat = orCat->extInfo;
    }

    return true;
}

bool IEDControlDO_init(IEDEntity entity)
{
    IEDControlDO *extInfo;
    entity->type = IED_ENTITY_DO;
    extInfo = IEDEntity_alloc(sizeof(IEDControlDO));
    entity->subType = DO_SUBTYPE_CONTROL;
    if(extInfo == NULL)
    {
        return false;
    }
    return true;
}

static bool writeCtlValue(IEDControlDA* ctrlInfo, const BufferView* value)
{
    uint8_t* pValue = value->p + value->pos;
    if (pValue[0] != IEC61850_BER_BOOLEAN || pValue[1] != 1)
    {
        return false;
    }

    if(pValue[2])
    {
        writeTele(ctrlInfo->ctlValOne->valueOffset);
    }
    else
    {
        writeTele(ctrlInfo->ctlValZero->valueOffset);
    }
    return true;
}

MmsDataAccessError IEDControlDA_write(IEDEntity entity,
                                         IsoConnection* isoConn, BufferView* value)
{
    uint8_t addCause;
    IEDControlDA* extInfo = entity->extInfo;

    //Пишем value 1 или 0
    if(!writeCtlValue(extInfo, value))
    {
        return DATA_ACCESS_ERROR_OBJECT_VALUE_INVALID;
    }

    //Читать ready пока не созреет
    if(!IEDControlDA_waitReady(entity))
    {
        return DATA_ACCESS_ERROR_UNKNOWN;
    }

    //Считать код
    addCause = readIntValue(extInfo->code);

    if(addCause != 0)
    {
        if(!Control_sendServiceErrorReport(isoConn, entity, addCause))
        {
            return DATA_ACCESS_ERROR_UNKNOWN;
        }
        else
        {
            return DATA_ACCESS_ERROR_OBJECT_ACCESS_DENIED;
        }
    }
    if(extInfo->type == CONTROL_OPER)
    {
        //Включаем контроль выполнения операции для посылки
        // CommandTermination по окончании
        extInfo->isoConn = isoConn;
        extInfo->waitTerminate = true;
    }
    return DATA_ACCESS_ERROR_SUCCESS;
}
