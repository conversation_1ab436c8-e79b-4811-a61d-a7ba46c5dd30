#include "iedCodedEnum.h"


#include "debug.h"
#include "../DataSlice.h"
#include "iedFinalDA.h"
#include "iedTree.h"
#include "../AsnEncoding.h"

#define CODEDENUM_ENCODED_SIZE 4

static void updateFromDataSlice(IEDEntity entity)
{	
	TerminalItem* termItem = entity->extInfo;	
	size_t bitCount = termItem->ce.bitCount;
	int* dsOffsets = termItem->ce.dsOffsets;
	uint8_t value = 0;

	size_t i;
	for(i = 0; i < bitCount; ++i)
	{
		value <<= 1;
		value |=  DataSlice_getBoolFastCurrDS(dsOffsets[i]);
	}

	if(entity->codedEnumValue == value)
	{
		entity->changed = TRGOP_NONE;
	}
	else
	{
		entity->changed = entity->trgOps;
		entity->codedEnumValue = value;
		IEDEntity_setTimeStamp(entity, dataSliceGetTimeStamp());
	}
}

static bool calcReadLen(IEDEntity entity, size_t* pLen )
{	
	*pLen = CODEDENUM_ENCODED_SIZE;
	return true;
}

static bool encodeRead(IEDEntity entity, BufferView* outBuf)
{
	TerminalItem* termItem = entity->extInfo;
	size_t bitCount = termItem->ce.bitCount;

	uint8_t* encodeBuf;

	if(!BufferView_alloc(outBuf,CODEDENUM_ENCODED_SIZE, &encodeBuf))
	{
		ERROR_REPORT("Unable to allocate buffer");
		return false;
	}

	// Возвращаемое значение не нужно,
	// потому что функция не возвращает ошибки, а размер известен заранее
	BerEncoder_encodeUcharBitString(ASN_TYPEDESCRIPTION_BIT_STRING,
									bitCount,  entity->codedEnumValue, encodeBuf, 0);

	outBuf->pos += CODEDENUM_ENCODED_SIZE;
	return true;
}

void IEDCodedEnum_init(IEDEntity entity)
{	
	TerminalItem* extInfo;	
	//accessInfo из бинарника модели
	CodedEnumAccessInfo* accessInfo;
	size_t bitCount;
	int* dsOffsets;
	size_t i;

	extInfo = entity->extInfo;
	accessInfo = extInfo->accessInfo;

	bitCount = accessInfo->bitCount;
	if(bitCount > 8)
	{
		ERROR_REPORT("CodedEnum more than 8 bits is not supported");
		bitCount = 8;
	}
	extInfo->ce.bitCount = bitCount;

	dsOffsets = extInfo->ce.dsOffsets;


	for (i = 0; i < bitCount; ++i)
	{
		dsOffsets[i] = DataSlice_getBoolOffset(accessInfo->valueOffsets[i]);
	}

	entity->updateFromDataSlice = updateFromDataSlice;
	entity->calcReadLen = calcReadLen;
	entity->encodeRead = encodeRead;

	IEDTree_addToCmpList(entity);
}
