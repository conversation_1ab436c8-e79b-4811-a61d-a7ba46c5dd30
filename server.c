#include "server.h"
#include "MmsConst.h"
#include "platform_socket.h"
#include <platform_socket_def.h>
#include <platform_critical_section.h>
#include <debug.h>

extern SOCKET listenSocket;

static int connCounter = 0;
static CriticalSection connCounterCS;


void handleIncomingConnections(TCPConnectionHandler handleConnection)
{
	SOCKADDR_IN_T fromAddr = { 0 };
	int fromAddrLen = sizeof(fromAddr);
	SOCKET connectionSocket;


	CriticalSection_Init(&connCounterCS);

	while (1)
	{                		
        if (!acceptConnection(&connectionSocket, (struct sockaddr *)&fromAddr, &fromAddrLen))
		{			
			ERROR_REPORT("'accept' function has returned an error");
			continue;
		}
		CriticalSection_Lock(&connCounterCS);
		if (connCounter == MAX_CONN_COUNT)
		{
			closesocket(connectionSocket);
			CriticalSection_Unlock(&connCounterCS);
			continue;
		}		
		connCounter++;
		CriticalSection_Unlock(&connCounterCS);

		handleConnection((SERVER_SOCKET)connectionSocket);
	}
}

void closeServerSocket(SERVER_SOCKET socket)
{
	CriticalSection_Lock(&connCounterCS);
	connCounter--;
	VERIFY(connCounter >= 0);
	CriticalSection_Unlock(&connCounterCS);
	closesocket((SOCKET)socket);	
}


