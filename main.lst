                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1s41.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=main.c -o gh_1s41.o -list=main.lst C:\Users\<USER>\AppData\Local\Temp\gh_1s41.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_1s41.s
Source File: main.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile main.c -o

                      10 ;		main.o

                      11 ;Source File:   main.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:55:52 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "main.h"


                      21 ;2: 


                      22 ;3: #include "MemoryManager.h"


                      23 ;4: #include "mms.h"


                      24 ;5: #include "iedmodel.h"


                      25 ;6: #include "pwin_access.h"


                      26 ;7: #include "DataSlice.h"


                      27 ;8: #include "out_buffers.h"


                      28 ;9: #include "reports.h"


                      29 ;10: #include "goose.h"


                      30 ;11: #include "file_system.h"


                      31 ;12: #include "server.h"


                      32 ;13: #include "timers.h"


                      33 ;14: #include "iedTree/iedTree.h"


                      34 ;15: #include "BusError.h"


                      35 ;16: 


                      36 ;17: #include <debug.h>


                      37 ;18: 


                      38 ;19: #include <stddef.h>


                      39 ;20: #include <Clib.h>


                      40 ;21: #include "netTools.h"


                      41 ;22: 


                      42 ;23: 


                      43 ;24: void serverMain(void)


                      44 	.text

                      45 	.align	4

                      46 serverMain::

00000000 e92d4000     47 	stmfd	[sp]!,{lr}

                      48 ;25: {


                      49 

00000004 e24dd004     50 	sub	sp,sp,4


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1s41.s
00000008 e3a00000     51 	mov	r0,0

0000000c e58d0000     52 	str	r0,[sp]

                      53 ;26:     int iedModelSize = 0;


                      54 

                      55 ;27:     uint8_t* pIedModel;


                      56 ;28: 


                      57 ;29:     debugStart();


                      58 

00000010 eb000000*    59 	bl	debugStart

                      60 ;30: 


                      61 ;31:     TRACE("Init memory manager");


                      62 ;32: 


                      63 ;33:     if (!MM_init())


                      64 

00000014 eb000000*    65 	bl	MM_init

00000018 e3500000     66 	cmp	r0,0

0000001c 0a000026     67 	beq	.L2

                      68 ;34:     {


                      69 

                      70 ;35:         return;


                      71 

                      72 ;36:     }


                      73 ;37: 


                      74 ;38:     TRACE("Init PWin");


                      75 ;39: 


                      76 ;40:     if(!initPWin())


                      77 

00000020 eb000000*    78 	bl	initPWin

00000024 e3500000     79 	cmp	r0,0

00000028 0a000023     80 	beq	.L2

                      81 ;41:     {


                      82 

                      83 ;42:         TRACE("Error");


                      84 ;43:         return;


                      85 

                      86 ;44:     }


                      87 ;45: 


                      88 ;46:     TRACE("dataSliceInit");


                      89 ;47:     // dataSliceInit придерживает инициализацию до прихода первого DataSlice


                      90 ;48:     // поэтому надо рассмотреть возможность переноса этого вызова пониже


                      91 ;49:     // чтобы ускорить инициализацию в целом.


                      92 ;50:     if(!dataSliceInit())


                      93 

0000002c eb000000*    94 	bl	dataSliceInit

00000030 e3500000     95 	cmp	r0,0

00000034 0a000020     96 	beq	.L2

                      97 ;51:     {


                      98 

                      99 ;52:         TRACE("Error");


                     100 ;53:         return;


                     101 

                     102 ;54:     }


                     103 ;55: 


                     104 ;56:     TRACE("fs_init");


                     105 ;57:     if(!fs_init())


                     106 

00000038 eb000000*   107 	bl	fs_init

0000003c e3500000    108 	cmp	r0,0

00000040 0a00001d    109 	beq	.L2

                     110 ;58:     {


                     111 


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1s41.s
                     112 ;59:         TRACE("Error");


                     113 ;60:         return;


                     114 

                     115 ;61:     }


                     116 ;62: 


                     117 ;63:     TRACE("NetTools_init");


                     118 ;64:     if( !NetTools_init())


                     119 

00000044 eb000000*   120 	bl	NetTools_init

00000048 e3500000    121 	cmp	r0,0

0000004c 0a00001a    122 	beq	.L2

                     123 ;65:     {


                     124 

                     125 ;66:         TRACE("Error");


                     126 ;67:         return;


                     127 

                     128 ;68:     }


                     129 ;69: 


                     130 ;70:     TRACE("BusError_init");


                     131 ;71:     BusError_init();


                     132 

00000050 eb000000*   133 	bl	BusError_init

                     134 ;72: 


                     135 ;73: 


                     136 ;74:     TRACE("Init sockets");


                     137 ;75: 


                     138 ;76:     if(!socketInit())


                     139 

00000054 eb000000*   140 	bl	socketInit

00000058 e3500000    141 	cmp	r0,0

0000005c 0a000016    142 	beq	.L2

                     143 ;77:     {


                     144 

                     145 ;78:         TRACE("Error");


                     146 ;79:         return;


                     147 

                     148 ;80:     }


                     149 ;81: 


                     150 ;82:     TRACE("Load IED model");


                     151 ;83: 


                     152 ;84:     // TODO Убедиться что правильно работает при отсутствии ROM-модуля


                     153 ;85:     pIedModel = loadIedModel(&iedModelSize);


                     154 

00000060 e1a0000d    155 	mov	r0,sp

00000064 eb000000*   156 	bl	loadIedModel

                     157 ;86: 


                     158 ;87:     //Если ROM-модуль не найден, останется информационная модель по умолчанию.


                     159 ;88:     if(pIedModel != NULL)


                     160 

00000068 e3500000    161 	cmp	r0,0

                     162 ;89:     {


                     163 

                     164 ;90:         TRACE("Set IED model");


                     165 ;91:         setIedModel(pIedModel, iedModelSize);


                     166 

0000006c 159d1000    167 	ldrne	r1,[sp]

00000070 1b000000*   168 	blne	setIedModel

                     169 ;92:     }


                     170 ;93: 


                     171 ;94:     // При инициализации в IEDTree элементов t (TimeStamp)


                     172 ;95:     // используется время из DataSlice, поэтому захватываем здесь



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1s41.s
                     173 ;96:     dataSliceCapture();


                     174 

00000074 eb000000*   175 	bl	dataSliceCapture

                     176 ;97: 


                     177 ;98:     if(!IEDTree_init(iedModel, iedModelSize))


                     178 

00000078 e59f2044*   179 	ldr	r2,.L159

0000007c e59d1000    180 	ldr	r1,[sp]

00000080 e5920000    181 	ldr	r0,[r2]

00000084 eb000000*   182 	bl	IEDTree_init

00000088 e3500000    183 	cmp	r0,0

0000008c 1a000001    184 	bne	.L24

                     185 ;99:     {


                     186 

                     187 ;100:         dataSliceRelease();


                     188 

00000090 eb000000*   189 	bl	dataSliceRelease

                     190 ;101:         ERROR_REPORT("IEDTree_init error");


                     191 ;102:         return;


                     192 

00000094 ea000008    193 	b	.L2

                     194 .L24:

                     195 ;103:     }


                     196 ;104: 


                     197 ;105:     //Инициализируем начальные значения из DataSlice.


                     198 ;106:     //При этом установятся флаги изменения DA, но они здесь не анализируются,


                     199 ;107:     //и ни на что не влияют


                     200 ;108:     IEDTree_updateFromDataSlice();


                     201 

00000098 eb000000*   202 	bl	IEDTree_updateFromDataSlice

                     203 ;109: 


                     204 ;110:     dataSliceRelease();


                     205 

0000009c eb000000*   206 	bl	dataSliceRelease

                     207 ;111: 


                     208 ;112:     initReports();


                     209 

000000a0 eb000000*   210 	bl	initReports

                     211 ;113:     GOOSE_init();


                     212 

000000a4 eb000000*   213 	bl	GOOSE_init

                     214 ;114:     Timers_init();


                     215 

000000a8 eb000000*   216 	bl	Timers_init

                     217 ;115: 


                     218 ;116:     TRACE("Allocated MM memory %d KB, free %d KB", 


                     219 ;117:         MM_getAllocated() / 1024, MM_getRemaining() / 1024);


                     220 ;118: 


                     221 ;119:     TRACE("Start listening");


                     222 ;120:     if(!startListening())


                     223 

000000ac eb000000*   224 	bl	startListening

000000b0 e3500000    225 	cmp	r0,0

000000b4 159f000c*   226 	ldrne	r0,.L160

                     227 ;121:     {


                     228 

                     229 ;122:         return;


                     230 

                     231 ;123:     }


                     232 ;124: 


                     233 ;125:     handleIncomingConnections(handleMMSConnection);



                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1s41.s
                     234 

000000b8 1b000000*   235 	blne	handleIncomingConnections

                     236 .L2:

000000bc e28dd004    237 	add	sp,sp,4

000000c0 e8bd8000    238 	ldmfd	[sp]!,{pc}

                     239 	.endf	serverMain

                     240 	.align	4

                     241 ;iedModelSize	[sp]	local

                     242 ;pIedModel	r1	local

                     243 

                     244 	.section ".bss","awb"

                     245 .L120:

                     246 	.data

                     247 	.text

                     248 

                     249 ;126: }


                     250 	.align	4

                     251 .L159:

000000c4 00000000*   252 	.data.w	iedModel

                     253 	.type	.L159,$object

                     254 	.size	.L159,4

                     255 

                     256 .L160:

000000c8 00000000*   257 	.data.w	handleMMSConnection

                     258 	.type	.L160,$object

                     259 	.size	.L160,4

                     260 

                     261 	.align	4

                     262 ;iedModel	iedModel	import

                     263 

                     264 	.data

                     265 	.ghsnote version,6

                     266 	.ghsnote tools,3

                     267 	.ghsnote options,0

                     268 	.text

                     269 	.align	4

