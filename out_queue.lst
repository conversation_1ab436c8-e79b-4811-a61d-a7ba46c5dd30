                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fvo1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=out_queue.c -o gh_fvo1.o -list=out_queue.lst C:\Users\<USER>\AppData\Local\Temp\gh_fvo1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_fvo1.s
Source File: out_queue.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile out_queue.c -o

                      10 ;		out_queue.o

                      11 ;Source File:   out_queue.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Oct 07 09:39:39 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "out_queue.h"


                      21 ;2: 


                      22 ;3: #include <platform_critical_section.h>


                      23 ;4: #include <stddef.h>


                      24 ;5: 


                      25 ;6: void OutQueue_init(OutQueue* self)


                      26 	.text

                      27 	.align	4

                      28 OutQueue_init::

00000000 e92d4010     29 	stmfd	[sp]!,{r4,lr}

                      30 ;7: {


                      31 

                      32 ;8:     CriticalSection_Init(&self->cs);


                      33 

00000004 e1a04000     34 	mov	r4,r0

00000008 eb000000*    35 	bl	CriticalSection_Init

                      36 ;9: 	self->head = -1;


                      37 

0000000c e3e00000     38 	mvn	r0,0

00000010 e5840004     39 	str	r0,[r4,4]

00000014 e8bd8010     40 	ldmfd	[sp]!,{r4,pc}

                      41 	.endf	OutQueue_init

                      42 	.align	4

                      43 

                      44 ;self	r4	param

                      45 

                      46 	.section ".bss","awb"

                      47 .L30:

                      48 	.data

                      49 	.text

                      50 


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fvo1.s
                      51 ;10: }


                      52 

                      53 ;11: 


                      54 ;12: void OutQueue_done(OutQueue* self)


                      55 	.align	4

                      56 	.align	4

                      57 OutQueue_done::

                      58 ;13: {


                      59 

                      60 ;14: 	CriticalSection_Done(&self->cs);	


                      61 

00000018 ea000000*    62 	b	CriticalSection_Done

                      63 	.endf	OutQueue_done

                      64 	.align	4

                      65 

                      66 ;self	none	param

                      67 

                      68 	.section ".bss","awb"

                      69 .L62:

                      70 	.data

                      71 	.text

                      72 

                      73 ;15: }


                      74 

                      75 ;16: 


                      76 ;17: bool OutQueue_isEmpty(OutQueue* self)


                      77 	.align	4

                      78 	.align	4

                      79 OutQueue_isEmpty::

                      80 ;18: {


                      81 

                      82 ;19: 	return self->head == -1;


                      83 

0000001c e5900004     84 	ldr	r0,[r0,4]

00000020 e3700001     85 	cmn	r0,1

00000024 03a00001     86 	moveq	r0,1

00000028 13a00000     87 	movne	r0,0

0000002c e12fff1e*    88 	ret	

                      89 	.endf	OutQueue_isEmpty

                      90 	.align	4

                      91 

                      92 ;self	r0	param

                      93 

                      94 	.section ".bss","awb"

                      95 .L97:

                      96 	.data

                      97 	.text

                      98 

                      99 ;20: };


                     100 

                     101 ;21: 


                     102 ;22: bool OutQueue_insert(OutQueue* self, void* item)


                     103 	.align	4

                     104 	.align	4

                     105 OutQueue_insert::

00000030 e92d4030    106 	stmfd	[sp]!,{r4-r5,lr}

                     107 ;23: {


                     108 

                     109 ;24: 	bool result = FALSE;


                     110 

                     111 ;25:     CriticalSection_Lock(&self->cs);



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fvo1.s
                     112 

00000034 e1a04001    113 	mov	r4,r1

00000038 e1a05000    114 	mov	r5,r0

0000003c eb000000*   115 	bl	CriticalSection_Lock

                     116 ;26: 	if (self->head == -1)


                     117 

00000040 e5950004    118 	ldr	r0,[r5,4]

00000044 e3700001    119 	cmn	r0,1

00000048 1a000006    120 	bne	.L106

                     121 ;27: 	{


                     122 

                     123 ;28: 		//Empty queue


                     124 ;29: 		self->tail = 0;


                     125 

0000004c e3a0c000    126 	mov	r12,0

                     127 ;30: 		self->queue[0] = item;


                     128 

00000050 e1a0e004    129 	mov	lr,r4

                     130 ;31: 		self->head = 1;


                     131 

00000054 e3a04001    132 	mov	r4,1

00000058 e9855010    133 	stmfa	[r5],{r4,r12,lr}

                     134 ;32: 		result = TRUE;


                     135 

                     136 ;47: 	}


                     137 ;48:     CriticalSection_Unlock(&self->cs);


                     138 

0000005c e1a00005    139 	mov	r0,r5

00000060 eb000000*   140 	bl	CriticalSection_Unlock

                     141 ;49:     return result;


                     142 

00000064 ea000012    143 	b	.L104

                     144 .L106:

                     145 ;33: 	}


                     146 ;34: 	else if (self->head == self->tail)


                     147 

00000068 e5951008    148 	ldr	r1,[r5,8]

0000006c e1500001    149 	cmp	r0,r1

00000070 1a000003    150 	bne	.L109

                     151 ;35: 	{


                     152 

                     153 ;36: 		//Overflow


                     154 ;37: 		result = FALSE;


                     155 

00000074 e3a04000    156 	mov	r4,0

                     157 ;47: 	}


                     158 ;48:     CriticalSection_Unlock(&self->cs);


                     159 

00000078 e1a00005    160 	mov	r0,r5

0000007c eb000000*   161 	bl	CriticalSection_Unlock

                     162 ;49:     return result;


                     163 

00000080 ea00000b    164 	b	.L104

                     165 .L109:

                     166 ;38: 	}


                     167 ;39: 	else


                     168 ;40: 	{


                     169 

                     170 ;41: 		self->queue[self->head++] = item;


                     171 

00000084 e5950004    172 	ldr	r0,[r5,4]


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fvo1.s
00000088 e2801001    173 	add	r1,r0,1

0000008c e5851004    174 	str	r1,[r5,4]

00000090 e0850100    175 	add	r0,r5,r0 lsl 2

00000094 e580400c    176 	str	r4,[r0,12]

                     177 ;42: 		if (self->head == OUT_QUEUE_SIZE)


                     178 

00000098 e5950004    179 	ldr	r0,[r5,4]

0000009c e3a04001    180 	mov	r4,1

                     181 ;47: 	}


                     182 ;48:     CriticalSection_Unlock(&self->cs);


                     183 

000000a0 e3500008    184 	cmp	r0,8

                     185 ;43: 		{


                     186 

                     187 ;44: 			self->head = 0;


                     188 

000000a4 03a00000    189 	moveq	r0,0

000000a8 05850004    190 	streq	r0,[r5,4]

                     191 ;45: 		}


                     192 ;46: 		result = TRUE;


                     193 

000000ac e1a00005    194 	mov	r0,r5

000000b0 eb000000*   195 	bl	CriticalSection_Unlock

                     196 ;49:     return result;


                     197 

                     198 .L104:

000000b4 e1a00004    199 	mov	r0,r4

000000b8 e8bd8030    200 	ldmfd	[sp]!,{r4-r5,pc}

                     201 	.endf	OutQueue_insert

                     202 	.align	4

                     203 ;result	r4	local

                     204 

                     205 ;self	r5	param

                     206 ;item	r4	param

                     207 

                     208 	.section ".bss","awb"

                     209 .L202:

                     210 	.data

                     211 	.text

                     212 

                     213 ;50: }


                     214 

                     215 ;51: 


                     216 ;52: void* OutQueue_get(OutQueue* self)


                     217 	.align	4

                     218 	.align	4

                     219 OutQueue_get::

000000bc e92d4030    220 	stmfd	[sp]!,{r4-r5,lr}

                     221 ;53: {


                     222 

                     223 ;54: 	void* result = NULL;


                     224 

                     225 ;55:     CriticalSection_Lock(&self->cs);


                     226 

000000c0 e1a05000    227 	mov	r5,r0

000000c4 eb000000*   228 	bl	CriticalSection_Lock

                     229 ;56: 	if (self->head == -1)


                     230 

000000c8 e5951004    231 	ldr	r1,[r5,4]

000000cc e3710001    232 	cmn	r1,1

000000d0 1a000003    233 	bne	.L229


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fvo1.s
                     234 ;57: 	{


                     235 

                     236 ;58: 		//Empty


                     237 ;59: 		result = NULL;


                     238 

000000d4 e3a04000    239 	mov	r4,0

                     240 ;71: 		}


                     241 ;72: 	}


                     242 ;73:     CriticalSection_Unlock(&self->cs);


                     243 

000000d8 e1a00005    244 	mov	r0,r5

000000dc eb000000*   245 	bl	CriticalSection_Unlock

                     246 ;74:     return result;


                     247 

000000e0 ea00000c    248 	b	.L227

                     249 .L229:

                     250 ;60: 	}


                     251 ;61: 	else


                     252 ;62: 	{


                     253 

                     254 ;63: 		result = self->queue[self->tail++];


                     255 

000000e4 e5952008    256 	ldr	r2,[r5,8]

000000e8 e2820001    257 	add	r0,r2,1

000000ec e0852102    258 	add	r2,r5,r2 lsl 2

000000f0 e5850008    259 	str	r0,[r5,8]

000000f4 e592400c    260 	ldr	r4,[r2,12]

                     261 ;64: 		if (self->tail == OUT_QUEUE_SIZE)


                     262 

000000f8 e3500008    263 	cmp	r0,8

                     264 ;65: 		{


                     265 

                     266 ;66: 			self->tail = 0;


                     267 

000000fc 03a00000    268 	moveq	r0,0

00000100 05850008    269 	streq	r0,[r5,8]

                     270 ;67: 		}


                     271 ;68: 		if (self->tail == self->head)


                     272 

00000104 e1500001    273 	cmp	r0,r1

                     274 ;69: 		{


                     275 

                     276 ;70: 			self->head = -1;


                     277 

00000108 03e00000    278 	mvneq	r0,0

0000010c 05850004    279 	streq	r0,[r5,4]

                     280 ;71: 		}


                     281 ;72: 	}


                     282 ;73:     CriticalSection_Unlock(&self->cs);


                     283 

00000110 e1a00005    284 	mov	r0,r5

00000114 eb000000*   285 	bl	CriticalSection_Unlock

                     286 ;74:     return result;


                     287 

                     288 .L227:

00000118 e1a00004    289 	mov	r0,r4

0000011c e8bd8030    290 	ldmfd	[sp]!,{r4-r5,pc}

                     291 	.endf	OutQueue_get

                     292 	.align	4

                     293 ;result	r4	local

                     294 


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_fvo1.s
                     295 ;self	r5	param

                     296 

                     297 	.section ".bss","awb"

                     298 .L300:

                     299 	.data

                     300 	.text

                     301 

                     302 ;75: }


                     303 	.align	4

                     304 

                     305 	.data

                     306 	.ghsnote version,6

                     307 	.ghsnote tools,3

                     308 	.ghsnote options,0

                     309 	.text

                     310 	.align	4

