                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1js1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=RptBool.c -o reportItems\gh_1js1.o -list=reportItems/RptBool.lst C:\Users\<USER>\AppData\Local\Temp\gh_1js1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_1js1.s
Source File: RptBool.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      10 ;		reportItems/RptBool.c -o reportItems/RptBool.o

                      11 ;Source File:   reportItems/RptBool.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:55:49 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: 


                      21 ;2: #include "RptBool.h"


                      22 ;3: 


                      23 ;4: #include <debug.h>


                      24 ;5: 


                      25 ;6: 


                      26 ;7: static void updateChanges(RptItem item)


                      27 	.text

                      28 	.align	4

                      29 updateChanges:

                      30 ;8: {        


                      31 

                      32 ;9:     item->newValue.boolValue = item->iedObj->boolValue;


                      33 

00000000 e5901008     34 	ldr	r1,[r0,8]

00000004 e5d11030     35 	ldrb	r1,[r1,48]

00000008 e5c01018     36 	strb	r1,[r0,24]

0000000c e12fff1e*    37 	ret	

                      38 	.endf	updateChanges

                      39 	.align	4

                      40 

                      41 ;item	r0	param

                      42 

                      43 	.section ".bss","awb"

                      44 .L33:

                      45 	.data

                      46 	.text

                      47 

                      48 ;10: }


                      49 

                      50 ;11: 



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1js1.s
                      51 ;12: 


                      52 ;13: static bool encodeRead(RptItem item, BufferView* outBuf)


                      53 	.align	4

                      54 	.align	4

                      55 encodeRead:

                      56 ;14: {			


                      57 

                      58 ;15:     //!!! Тут много чего надо написать


                      59 ;16: 


                      60 ;17:     item->changedOld = item->changedNew;


                      61 

00000010 e5901014     62 	ldr	r1,[r0,20]

00000014 e580101c     63 	str	r1,[r0,28]

                      64 ;18:     item->changedNew = TRGOP_NONE;


                      65 

00000018 e3a01000     66 	mov	r1,0

0000001c e5801014     67 	str	r1,[r0,20]

                      68 ;19:     return true;


                      69 

00000020 e3a00001     70 	mov	r0,1

00000024 e12fff1e*    71 	ret	

                      72 	.endf	encodeRead

                      73 	.align	4

                      74 

                      75 ;item	r0	param

                      76 ;outBuf	none	param

                      77 

                      78 	.section ".bss","awb"

                      79 .L62:

                      80 	.data

                      81 	.text

                      82 

                      83 ;20: }


                      84 

                      85 ;21: 


                      86 ;22: static bool calcReadLen(RptItem item, size_t* pLen )


                      87 	.align	4

                      88 	.align	4

                      89 calcReadLen:

                      90 ;23: {


                      91 

                      92 ;24: 	*pLen = 3; // BOOL encoded size


                      93 

00000028 e3a00003     94 	mov	r0,3

0000002c e5810000     95 	str	r0,[r1]

                      96 ;25: 	return true;


                      97 

00000030 e3a00001     98 	mov	r0,1

00000034 e12fff1e*    99 	ret	

                     100 	.endf	calcReadLen

                     101 	.align	4

                     102 

                     103 ;item	none	param

                     104 ;pLen	r1	param

                     105 

                     106 	.section ".bss","awb"

                     107 .L94:

                     108 	.data

                     109 	.text

                     110 

                     111 ;26: }



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1js1.s
                     112 

                     113 ;27: 


                     114 ;28: static void overwriteOld(RptItem item)


                     115 	.align	4

                     116 	.align	4

                     117 overwriteOld:

                     118 ;29: {


                     119 

                     120 ;30:     item->oldValue.boolValue = item->newValue.boolValue;    


                     121 

00000038 e5d01018    122 	ldrb	r1,[r0,24]

0000003c e5c01020    123 	strb	r1,[r0,32]

00000040 e12fff1e*   124 	ret	

                     125 	.endf	overwriteOld

                     126 	.align	4

                     127 

                     128 ;item	r0	param

                     129 

                     130 	.section ".bss","awb"

                     131 .L129:

                     132 	.data

                     133 	.text

                     134 

                     135 ;31: }


                     136 

                     137 ;32: 


                     138 ;33: static struct RptItemBehavior behavior = {


                     139 ;34:     updateChanges,


                     140 ;35:     encodeRead,


                     141 ;36:     calcReadLen,


                     142 ;37:     overwriteOld


                     143 ;38: };


                     144 ;39: 


                     145 ;40: 


                     146 ;41: void RptBool_init(RptItem item)


                     147 	.align	4

                     148 	.align	4

                     149 	.align	4

                     150 RptBool_init::

                     151 ;42: {


                     152 

                     153 ;43:     item->behaviour = &behavior;


                     154 

00000044 e59f1004*   155 	ldr	r1,.L166

00000048 e5801004    156 	str	r1,[r0,4]

0000004c e12fff1e*   157 	ret	

                     158 	.endf	RptBool_init

                     159 	.align	4

                     160 

                     161 ;item	r0	param

                     162 

                     163 	.data

                     164 .L158:

                     165 .L159:

00000000 00000000*   166 behavior:	.data.w	updateChanges

00000004 00000000*   167 	.data.w	encodeRead

00000008 00000000*   168 	.data.w	calcReadLen

0000000c 00000000*   169 	.data.w	overwriteOld

                     170 	.type	behavior,$object

                     171 	.size	behavior,16

                     172 	.text


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1js1.s
                     173 

                     174 ;44: 


                     175 ;45: }


                     176 	.align	4

                     177 .L166:

00000050 00000000*   178 	.data.w	.L159

                     179 	.type	.L166,$object

                     180 	.size	.L166,4

                     181 

                     182 	.align	4

                     183 ;behavior	.L159	static

                     184 

                     185 	.data

                     186 	.ghsnote version,6

                     187 	.ghsnote tools,3

                     188 	.ghsnote options,0

                     189 	.text

                     190 	.align	4

                     191 	.data

                     192 	.align	4

                     193 	.text

