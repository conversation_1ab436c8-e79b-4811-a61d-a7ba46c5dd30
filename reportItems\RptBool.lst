                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3uo1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=RptBool.c -o reportItems\gh_3uo1.o -list=reportItems/RptBool.lst C:\Users\<USER>\AppData\Local\Temp\gh_3uo1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_3uo1.s
Source File: RptBool.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      10 ;		reportItems/RptBool.c -o reportItems/RptBool.o

                      11 ;Source File:   reportItems/RptBool.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Oct 07 09:39:23 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: 


                      21 ;2: #include "RptBool.h"


                      22 ;3: 


                      23 ;4: #include <debug.h>


                      24 ;5: 


                      25 ;6: static void initValue(RptItem item)


                      26 	.text

                      27 	.align	4

                      28 initValue:

                      29 ;7: {


                      30 

                      31 ;8:     item->oldValue.boolValue = item->iedObj->boolValue;


                      32 

00000000 e5901008     33 	ldr	r1,[r0,8]

00000004 e5d11030     34 	ldrb	r1,[r1,48]

00000008 e5c01020     35 	strb	r1,[r0,32]

0000000c e12fff1e*    36 	ret	

                      37 	.endf	initValue

                      38 	.align	4

                      39 

                      40 ;item	r0	param

                      41 

                      42 	.section ".bss","awb"

                      43 .L33:

                      44 	.data

                      45 	.text

                      46 

                      47 ;9: }


                      48 

                      49 ;10: 


                      50 ;11: static void updateChanges(RptItem item)



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3uo1.s
                      51 	.align	4

                      52 	.align	4

                      53 updateChanges:

                      54 ;12: {        


                      55 

                      56 ;13:     item->newValue.boolValue = item->iedObj->boolValue;


                      57 

00000010 e5901008     58 	ldr	r1,[r0,8]

00000014 e5d11030     59 	ldrb	r1,[r1,48]

00000018 e5c01018     60 	strb	r1,[r0,24]

0000001c e12fff1e*    61 	ret	

                      62 	.endf	updateChanges

                      63 	.align	4

                      64 

                      65 ;item	r0	param

                      66 

                      67 	.section ".bss","awb"

                      68 .L65:

                      69 	.data

                      70 	.text

                      71 

                      72 ;14: }


                      73 

                      74 ;15: 


                      75 ;16: 


                      76 ;17: static bool encodeRead(RptItem item, BufferView* outBuf)


                      77 	.align	4

                      78 	.align	4

                      79 encodeRead:

                      80 ;18: {			


                      81 

                      82 ;19:     //!!! Тут много чего надо написать


                      83 ;20: 


                      84 ;21:     item->changedOld = item->changedNew;


                      85 

00000020 e5901014     86 	ldr	r1,[r0,20]

00000024 e580101c     87 	str	r1,[r0,28]

                      88 ;22:     item->changedNew = TRGOP_NONE;


                      89 

00000028 e3a01000     90 	mov	r1,0

0000002c e5801014     91 	str	r1,[r0,20]

                      92 ;23:     return true;


                      93 

00000030 e3a00001     94 	mov	r0,1

00000034 e12fff1e*    95 	ret	

                      96 	.endf	encodeRead

                      97 	.align	4

                      98 

                      99 ;item	r0	param

                     100 ;outBuf	none	param

                     101 

                     102 	.section ".bss","awb"

                     103 .L94:

                     104 	.data

                     105 	.text

                     106 

                     107 ;24: }


                     108 

                     109 ;25: 


                     110 ;26: static bool calcReadLen(RptItem item, size_t* pLen )


                     111 	.align	4


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3uo1.s
                     112 	.align	4

                     113 calcReadLen:

                     114 ;27: {


                     115 

                     116 ;28: 	*pLen = 3; // BOOL encoded size


                     117 

00000038 e3a00003    118 	mov	r0,3

0000003c e5810000    119 	str	r0,[r1]

                     120 ;29: 	return true;


                     121 

00000040 e3a00001    122 	mov	r0,1

00000044 e12fff1e*   123 	ret	

                     124 	.endf	calcReadLen

                     125 	.align	4

                     126 

                     127 ;item	none	param

                     128 ;pLen	r1	param

                     129 

                     130 	.section ".bss","awb"

                     131 .L126:

                     132 	.data

                     133 	.text

                     134 

                     135 ;30: }


                     136 

                     137 ;31: 


                     138 ;32: static void overwriteOld(RptItem item)


                     139 	.align	4

                     140 	.align	4

                     141 overwriteOld:

                     142 ;33: {


                     143 

                     144 ;34:     item->oldValue.boolValue = item->newValue.boolValue;    


                     145 

00000048 e5d01018    146 	ldrb	r1,[r0,24]

0000004c e5c01020    147 	strb	r1,[r0,32]

00000050 e12fff1e*   148 	ret	

                     149 	.endf	overwriteOld

                     150 	.align	4

                     151 

                     152 ;item	r0	param

                     153 

                     154 	.section ".bss","awb"

                     155 .L161:

                     156 	.data

                     157 	.text

                     158 

                     159 ;35: }


                     160 

                     161 ;36: 


                     162 ;37: static struct RptItemBehavior behavior = {


                     163 ;38:     initValue,


                     164 ;39:     updateChanges,


                     165 ;40:     encodeRead,


                     166 ;41:     calcReadLen,


                     167 ;42:     overwriteOld


                     168 ;43: };


                     169 ;44: 


                     170 ;45: 


                     171 ;46: void RptBool_init(RptItem item)


                     172 	.align	4


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3uo1.s
                     173 	.align	4

                     174 	.align	4

                     175 RptBool_init::

                     176 ;47: {


                     177 

                     178 ;48:     item->behaviour = &behavior;


                     179 

00000054 e59f1004*   180 	ldr	r1,.L198

00000058 e5801004    181 	str	r1,[r0,4]

0000005c e12fff1e*   182 	ret	

                     183 	.endf	RptBool_init

                     184 	.align	4

                     185 

                     186 ;item	r0	param

                     187 

                     188 	.data

                     189 .L190:

                     190 .L191:

00000000 00000000*   191 behavior:	.data.w	initValue

00000004 00000000*   192 	.data.w	updateChanges

00000008 00000000*   193 	.data.w	encodeRead

0000000c 00000000*   194 	.data.w	calcReadLen

00000010 00000000*   195 	.data.w	overwriteOld

                     196 	.type	behavior,$object

                     197 	.size	behavior,20

                     198 	.text

                     199 

                     200 ;49: 


                     201 ;50: }


                     202 	.align	4

                     203 .L198:

00000060 00000000*   204 	.data.w	.L191

                     205 	.type	.L198,$object

                     206 	.size	.L198,4

                     207 

                     208 	.align	4

                     209 ;behavior	.L191	static

                     210 

                     211 	.data

                     212 	.ghsnote version,6

                     213 	.ghsnote tools,3

                     214 	.ghsnote options,0

                     215 	.text

                     216 	.align	4

                     217 	.data

                     218 	.align	4

                     219 	.text

