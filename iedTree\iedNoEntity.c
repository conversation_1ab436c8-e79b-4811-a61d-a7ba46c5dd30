#include "iedNoEntity.h"

#include "../bufView.h"

#include <string.h>


static struct IEDEntityStruct noEntity;

static bool calcReadLen(IEDEntity entity, size_t* pLen )
{
    *pLen = 3;
    return true;
}

static bool encodeRead(IEDEntity entity, BufferView* outBuf)
{
    // При чтении создаёт код object-non-existent
    return BufferView_writeData(outBuf, "\x80\x01\x0A", 3);
}

static MmsDataAccessError write(IEDEntity entity, IsoConnection* isoConn,
                               BufferView* value)
{
    return DATA_ACCESS_ERROR_OBJECT_NONE_EXISTENT;
}


void IEDNoEntity_init(void)
{
    memset(&noEntity, 0, sizeof(noEntity));
    noEntity.type = IED_ENTITY_NONE;
    noEntity.encodeRead = encodeRead;
    noEntity.calcReadLen = calcReadLen;
    noEntity.write = write;
}

IEDEntity IEDNoEntity_get(void)
{
    return &noEntity;
}
