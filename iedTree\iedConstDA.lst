                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_egc1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=iedConstDA.c -o iedTree\gh_egc1.o -list=iedTree/iedConstDA.lst C:\Users\<USER>\AppData\Local\Temp\gh_egc1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_egc1.s
Source File: iedConstDA.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      10 ;		iedTree/iedConstDA.c -o iedTree/iedConstDA.o

                      11 ;Source File:   iedTree/iedConstDA.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Sep 30 09:55:12 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "iedConstDA.h"


                      21 ;2: 


                      22 ;3: 


                      23 ;4: #include "iedControlModel.h"


                      24 ;5: #include "../iedmodel.h"


                      25 ;6: 


                      26 ;7: 


                      27 ;8: bool IEDConstDA_init(IEDEntity entity, BufferView* ber)


                      28 	.text

                      29 	.align	4

                      30 IEDConstDA_init::

00000000 e92d4030     31 	stmfd	[sp]!,{r4-r5,lr}

                      32 ;9: {


                      33 

                      34 ;10:     // Некоторые константы на самом деле не константы,


                      35 ;11:     // а части модели управления


                      36 ;12:     if( StringView_cmpCStr(&entity->name, "orIdent") == 0 )


                      37 

00000004 e1a04000     38 	mov	r4,r0

00000008 e1a05001     39 	mov	r5,r1

0000000c e28f1000*    40 	adr	r1,.L93

00000010 e2840048     41 	add	r0,r4,72

00000014 eb000000*    42 	bl	StringView_cmpCStr

00000018 e3500000     43 	cmp	r0,0

                      44 ;13:     {


                      45 

                      46 ;14:         return IEDOrIdent_init(entity);


                      47 

0000001c 01a00004     48 	moveq	r0,r4

00000020 08bd4030     49 	ldmeqfd	[sp]!,{r4-r5,lr}

00000024 0a000000*    50 	beq	IEDOrIdent_init


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_egc1.s
                      51 ;15:     }


                      52 ;16:     else if(StringView_cmpCStr(&entity->name, "orCat") == 0)


                      53 

00000028 e28f1000*    54 	adr	r1,.L94

0000002c e2840048     55 	add	r0,r4,72

00000030 eb000000*    56 	bl	StringView_cmpCStr

00000034 e3500000     57 	cmp	r0,0

                      58 ;17:     {


                      59 

                      60 ;18:         return IEDOrCat_init(entity);


                      61 

00000038 01a00004     62 	moveq	r0,r4

0000003c 08bd4030     63 	ldmeqfd	[sp]!,{r4-r5,lr}

00000040 0a000000*    64 	beq	IEDOrCat_init

                      65 ;19:     }


                      66 ;20:     else if(StringView_cmpCStr(&entity->name, "ctlNum") == 0)


                      67 

00000044 e28f1000*    68 	adr	r1,.L95

00000048 e2840048     69 	add	r0,r4,72

0000004c eb000000*    70 	bl	StringView_cmpCStr

00000050 e3500000     71 	cmp	r0,0

                      72 ;21:     {


                      73 

                      74 ;22:         return IEDCtlNum_init(entity, ber);


                      75 

00000054 01a01005     76 	moveq	r1,r5

00000058 01a00004     77 	moveq	r0,r4

0000005c 08bd4030     78 	ldmeqfd	[sp]!,{r4-r5,lr}

00000060 0a000000*    79 	beq	IEDCtlNum_init

                      80 ;23:     }


                      81 ;24: 


                      82 ;25:     entity->type = IED_ENTITY_DA_CONST;    


                      83 

00000064 e3a00007     84 	mov	r0,7

00000068 e5840050     85 	str	r0,[r4,80]

                      86 ;26:     entity->readOnly = true;


                      87 

0000006c e3a00001     88 	mov	r0,1

00000070 e5c40022     89 	strb	r0,[r4,34]

                      90 ;27: 


                      91 ;28:     //Сохраняем позицию константы в информационной модели


                      92 ;29:     entity->extInfo = (void*)(ber->p + ber->pos - iedModel);


                      93 

00000074 e895000c     94 	ldmfd	[r5],{r2-r3}

00000078 e0831002     95 	add	r1,r3,r2

0000007c e59f3024*    96 	ldr	r3,.L96

00000080 e5932000     97 	ldr	r2,[r3]

00000084 e0411002     98 	sub	r1,r1,r2

00000088 e5841058     99 	str	r1,[r4,88]

                     100 ;30:     return true;


                     101 

0000008c e8bd8030    102 	ldmfd	[sp]!,{r4-r5,pc}

                     103 	.endf	IEDConstDA_init

                     104 	.align	4

                     105 ;.L64	.L69	static

                     106 ;.L65	.L70	static

                     107 ;.L66	.L71	static

                     108 

                     109 ;entity	r4	param

                     110 ;ber	r5	param

                     111 


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_egc1.s
                     112 	.section ".bss","awb"

                     113 .L63:

                     114 	.data

                     115 	.text

                     116 

                     117 ;31: }


                     118 	.align	4

                     119 .L93:

                     120 ;	"orIdent\000"

00000090 6449726f    121 	.data.b	111,114,73,100

00000094 00746e65    122 	.data.b	101,110,116,0

                     123 	.align 4

                     124 

                     125 	.type	.L93,$object

                     126 	.size	.L93,4

                     127 

                     128 .L94:

                     129 ;	"orCat\000"

00000098 6143726f    130 	.data.b	111,114,67,97

0000009c 0074       131 	.data.b	116,0

0000009e 0000       132 	.align 4

                     133 

                     134 	.type	.L94,$object

                     135 	.size	.L94,4

                     136 

                     137 .L95:

                     138 ;	"ctlNum\000"

000000a0 4e6c7463    139 	.data.b	99,116,108,78

000000a4 6d75       140 	.data.b	117,109

000000a6 00         141 	.data.b	0

000000a7 00         142 	.align 4

                     143 

                     144 	.type	.L95,$object

                     145 	.size	.L95,4

                     146 

                     147 .L96:

000000a8 00000000*   148 	.data.w	iedModel

                     149 	.type	.L96,$object

                     150 	.size	.L96,4

                     151 

                     152 	.align	4

                     153 ;iedModel	iedModel	import

                     154 

                     155 	.data

                     156 	.ghsnote version,6

                     157 	.ghsnote tools,3

                     158 	.ghsnote options,0

                     159 	.text

                     160 	.align	4

