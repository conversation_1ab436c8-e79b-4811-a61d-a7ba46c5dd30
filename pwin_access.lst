                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aas1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=pwin_access.c -o gh_aas1.o -list=pwin_access.lst C:\Users\<USER>\AppData\Local\Temp\gh_aas1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_aas1.s
Source File: pwin_access.c
Directory: F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		-I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi

                       4 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       5 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       6 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       7 ;		-I../../../../lwipport/include

                       8 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                       9 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile pwin_access.c

                      10 ;		-o pwin_access.o

                      11 ;Source File:   pwin_access.c

                      12 ;Directory:     F:\work\SAPR_RZA_clones\BufTm\IEC_61850\modules\MMS\MMSServer

                      13 ;Compile Date:  Tue Oct 07 09:39:38 2025

                      14 ;Host OS:       Win32

                      15 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      16 ;Release:       MULTI v4.2.3

                      17 ;Revision Date: Wed Mar 29 05:25:47 2006

                      18 ;Release Date:  Fri Mar 31 10:02:14 2006

                      19 

                      20 ;1: #include "pwin_access.h"


                      21 ;2: 


                      22 ;3: #include "../../../../AT91CORE/modules/pw/pwinlib/config.h"


                      23 ;4: #include "../../../../AT91CORE/modules/pw/pwinlib/pwinlib.h"


                      24 ;5: #include "../../../../AT91CORE/modules/pw/pwinlib/lasterr.h"


                      25 ;6: 


                      26 ;7: #include <debug.h>


                      27 ;8: #include <types.h>


                      28 ;9: #include <process.h>


                      29 ;10: 


                      30 ;11: #define PWIN_DEFAULT_TIMEOUT 1000


                      31 ;12: 


                      32 ;13: static _HANDLE pwlHandle;


                      33 ;14: static _PWL_GetAnalogValue pwlGetAnalogValue;


                      34 ;15: static _LoadRomFromAddr pwlLoadRomFromAddr;


                      35 ;16: static _PWL_initLibIf pwlInitLibIf;


                      36 ;17: 


                      37 ;18: static PWinLibIf pwinLibIf;


                      38 ;19: 


                      39 ;20: bool initPWin(void)


                      40 	.text

                      41 	.align	4

                      42 initPWin::

00000000 e92d4070     43 	stmfd	[sp]!,{r4-r6,lr}

                      44 ;21: {


                      45 

                      46 ;22:     _PWL_Open pwlOpen;


                      47 ;23:     _HANDLE hModule = _GetModuleHandle("PWINLIB");


                      48 

00000004 e28f0000*    49 	adr	r0,.L145

00000008 e59f4084*    50 	ldr	r4,.L146


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aas1.s
0000000c eb000000*    51 	bl	_GetModuleHandle

00000010 e1b06000     52 	movs	r6,r0

                      53 ;24:     if(! hModule)


                      54 

00000014 0a000015     55 	beq	.L17

                      56 ;25:     {


                      57 

                      58 ;26:         return FALSE;


                      59 

                      60 ;27:     }


                      61 ;28: 


                      62 ;29:     pwlOpen = (_PWL_Open)_GetProcAddress(hModule,(char*)PWL_OPEN);


                      63 

00000018 e3a01360     64 	mov	r1,0x80000001

0000001c eb000000*    65 	bl	_GetProcAddress

00000020 e1b05000     66 	movs	r5,r0

                      67 ;30:     if(! pwlOpen)


                      68 

00000024 0a000011     69 	beq	.L17

                      70 ;31:     {


                      71 

                      72 ;32:         return FALSE;


                      73 

                      74 ;33:     }


                      75 ;34: 


                      76 ;35:     pwlGetAnalogValue = (_PWL_GetAnalogValue)


                      77 

00000028 e3a01288     78 	mov	r1,0x80000008

0000002c e1a00006     79 	mov	r0,r6

00000030 eb000000*    80 	bl	_GetProcAddress

00000034 e5840004     81 	str	r0,[r4,4]

                      82 ;36:             _GetProcAddress(hModule,(char*)PWL_GET_ANALOG_VALUE);


                      83 ;37:     if(! pwlGetAnalogValue)


                      84 

00000038 e3500000     85 	cmp	r0,0

0000003c 0a00000b     86 	beq	.L17

                      87 ;38:     {


                      88 

                      89 ;39:         return FALSE;


                      90 

                      91 ;40:     }


                      92 ;41: 


                      93 ;42:     pwlLoadRomFromAddr = (_LoadRomFromAddr)


                      94 

00000040 e3a01268     95 	mov	r1,0x80000006

00000044 e1a00006     96 	mov	r0,r6

00000048 eb000000*    97 	bl	_GetProcAddress

0000004c e5840008     98 	str	r0,[r4,8]

                      99 ;43:             _GetProcAddress(hModule,(char*)PWL_LOAD_ROMM_FROM_ADDR);


                     100 ;44:     if(! pwlLoadRomFromAddr)


                     101 

00000050 e3500000    102 	cmp	r0,0

00000054 0a000005    103 	beq	.L17

                     104 ;45:     {


                     105 

                     106 ;46:         return FALSE;


                     107 

                     108 ;47:     }


                     109 ;48: 


                     110 ;49:     pwlInitLibIf = (_PWL_initLibIf)


                     111 


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aas1.s
00000058 e3a01480    112 	mov	r1,1<<31

0000005c e2811064    113 	add	r1,r1,100

00000060 e1a00006    114 	mov	r0,r6

00000064 eb000000*   115 	bl	_GetProcAddress

00000068 e1b0c000    116 	movs	r12,r0

0000006c e584c00c    117 	str	r12,[r4,12]

                     118 ;50:             _GetProcAddress(hModule,(char*)PWL_INIT_IF);


                     119 ;51:     if(! pwlInitLibIf)


                     120 

                     121 .L17:

                     122 ;52:     {


                     123 

                     124 ;53:         return FALSE;


                     125 

00000070 03a00000    126 	moveq	r0,0

00000074 0a000019    127 	beq	.L2

                     128 .L16:

                     129 ;54:     }


                     130 ;55:     pwlInitLibIf(&pwinLibIf,sizeof(PWinLibIf));


                     131 

00000078 e2840010    132 	add	r0,r4,16

0000007c e3a01074    133 	mov	r1,116

00000080 e1a0e00f    134 	mov	lr,pc

00000084 e12fff1c*   135 	bx	r12

                     136 ;56: 


                     137 ;57: 


                     138 ;58:     do


                     139 

                     140 .L21:

                     141 ;59:     {


                     142 

                     143 ;60:         Idle();


                     144 

00000088 ea000002    145 	b	.L147

                     146 	.align	4

                     147 .L145:

                     148 ;	"PWINLIB\000"

0000008c 4e495750    149 	.data.b	80,87,73,78

00000090 0042494c    150 	.data.b	76,73,66,0

                     151 	.align 4

                     152 

                     153 	.type	.L145,$object

                     154 	.size	.L145,4

                     155 

                     156 .L146:

00000094 00000000*   157 	.data.w	.L120

                     158 	.type	.L146,$object

                     159 	.size	.L146,4

                     160 

                     161 .L147:

                     162 

00000098 e6000010    163 	.word	0xE6000010

                     164 

                     165 ;61:         pwlHandle = pwlOpen();


                     166 

0000009c e1a0e00f    167 	mov	lr,pc

000000a0 e12fff15*   168 	bx	r5

000000a4 e5840000    169 	str	r0,[r4]

000000a8 e3500000    170 	cmp	r0,0

000000ac 0afffff5    171 	beq	.L21

                     172 ;62:     }



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aas1.s
                     173 ;63:     while(!pwlHandle);


                     174 ;64: 


                     175 ;65: 	pwinLibIf.configure(pwlHandle,PWL_CONFIGURE_WAIT_READY_BEFORE_OPERATION,0);


                     176 

000000b0 e594c064    177 	ldr	r12,[r4,100]

000000b4 e3a02000    178 	mov	r2,0

000000b8 e3a01002    179 	mov	r1,2

000000bc e1a0e00f    180 	mov	lr,pc

000000c0 e12fff1c*   181 	bx	r12

                     182 ;66: 	pwinLibIf.configure(pwlHandle,PWL_CONFIGURE_TIMEOUT,PWIN_DEFAULT_TIMEOUT);


                     183 

000000c4 e594c064    184 	ldr	r12,[r4,100]

000000c8 e5940000    185 	ldr	r0,[r4]

000000cc e3a02ffa    186 	mov	r2,0x03e8

000000d0 e3a01001    187 	mov	r1,1

000000d4 e1a0e00f    188 	mov	lr,pc

000000d8 e12fff1c*   189 	bx	r12

                     190 ;67: 


                     191 ;68:     return TRUE;


                     192 

000000dc e3a00001    193 	mov	r0,1

                     194 .L2:

000000e0 e8bd8070    195 	ldmfd	[sp]!,{r4-r6,pc}

                     196 	.endf	initPWin

                     197 	.align	4

                     198 ;pwlOpen	r5	local

                     199 ;hModule	r6	local

                     200 ;.L121	.L124	static

                     201 

                     202 	.section ".bss","awb"

                     203 .L120:

00000000 00000000    204 pwlHandle:	.space	4

00000004 00000000    205 pwlGetAnalogValue:	.space	4

00000008 00000000    206 pwlLoadRomFromAddr:	.space	4

0000000c 00000000    207 pwlInitLibIf:	.space	4

00000010 00000000    208 pwinLibIf:	.space	116

00000014 00000000 
00000018 00000000 
0000001c 00000000 
00000020 00000000 
00000024 00000000 
00000028 00000000 
0000002c 00000000 
00000030 00000000 
00000034 00000000 
00000038 00000000 
0000003c 00000000 
00000040 00000000 
00000044 00000000 
00000048 00000000 
0000004c 00000000 
00000050 00000000 
00000054 00000000 
00000058 00000000 
0000005c 00000000 
00000060 00000000 
00000064 00000000 
00000068 00000000 
0000006c 00000000 
00000070 00000000 
00000074 00000000 

                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aas1.s
00000078 00000000 
0000007c 00000000 
00000080 00000000 
                     209 	.data

                     210 	.text

                     211 

                     212 ;69: }


                     213 

                     214 ;70: 


                     215 ;71: bool loadRomModule(uint32_t signature, void** pRomModule, uint8_t** pRomModuleData,


                     216 	.align	4

                     217 	.align	4

                     218 loadRomModule::

000000e4 e92d4cf0    219 	stmfd	[sp]!,{r4-r7,r10-fp,lr}

                     220 ;72:     size_t* pRomModuleDataSize, __time32_t *time)


                     221 ;73: {


                     222 

                     223 ;74:     RMODULE* module;


                     224 ;75: 	


                     225 ;76: 	while (true)


                     226 

000000e8 e1a05000    227 	mov	r5,r0

000000ec e1a0b001    228 	mov	fp,r1

000000f0 e1a06002    229 	mov	r6,r2

000000f4 e1a07003    230 	mov	r7,r3

000000f8 e59da01c    231 	ldr	r10,[sp,28]

000000fc e59f43e8*   232 	ldr	r4,.L298

                     233 .L152:

                     234 ;77: 	{


                     235 

                     236 ;78: 		module = pwlLoadRomFromAddr(pwlHandle, 0, signature, TRUE);


                     237 

00000100 e594c008    238 	ldr	r12,[r4,8]

00000104 e1a02005    239 	mov	r2,r5

00000108 e5940000    240 	ldr	r0,[r4]

0000010c e3a03001    241 	mov	r3,1

00000110 e3a01000    242 	mov	r1,0

00000114 e1a0e00f    243 	mov	lr,pc

00000118 e12fff1c*   244 	bx	r12

                     245 ;79: 		// нашли модель


                     246 ;80: 		if (module)


                     247 

0000011c e3500000    248 	cmp	r0,0

00000120 1a000007    249 	bne	.L155

                     250 ;81: 		{


                     251 

                     252 ;82: 			break;


                     253 

                     254 ;83: 		}


                     255 ;84: 		


                     256 ;85: 		// ошибок нет, не найден роммодуль


                     257 ;86: 		if (pwinLibIf.getLastError(pwlHandle) == PWL_ERROR_ROMMODULE_NOT_FOUND)


                     258 

00000124 e5940000    259 	ldr	r0,[r4]

00000128 e594c070    260 	ldr	r12,[r4,112]

0000012c e1a0e00f    261 	mov	lr,pc

00000130 e12fff1c*   262 	bx	r12

00000134 e3500002    263 	cmp	r0,2

00000138 1afffff0    264 	bne	.L152

                     265 ;87: 			return FALSE;


                     266 


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aas1.s
0000013c e3a00000    267 	mov	r0,0

00000140 ea00000c    268 	b	.L148

                     269 .L155:

                     270 ;88: 		


                     271 ;89: 		// была ошибка по pwin - повторяем поиск


                     272 ;90: 	}


                     273 ;91: 	


                     274 ;92: 	if (pRomModule)


                     275 

00000144 e35b0000    276 	cmp	fp,0

                     277 ;93: 	{		


                     278 

                     279 ;94: 		*pRomModule = module;


                     280 

00000148 158b0000    281 	strne	r0,[fp]

                     282 ;95: 	}


                     283 ;96: 	


                     284 ;97: 	if (pRomModuleData)


                     285 

0000014c e3560000    286 	cmp	r6,0

                     287 ;98: 	{


                     288 

                     289 ;99: 	   *pRomModuleData = (uint8_t*)(module + 1);


                     290 

00000150 12801030    291 	addne	r1,r0,48

00000154 15861000    292 	strne	r1,[r6]

                     293 ;100: 	}


                     294 ;101: 	// размер


                     295 ;102: 	if (pRomModuleDataSize)


                     296 

00000158 e3570000    297 	cmp	r7,0

                     298 ;103: 	{


                     299 

                     300 ;104:         *pRomModuleDataSize = module->MSize - sizeof(RMODULE);


                     301 

0000015c 15901004    302 	ldrne	r1,[r0,4]

00000160 12411030    303 	subne	r1,r1,48

00000164 15871000    304 	strne	r1,[r7]

                     305 ;105: 	}


                     306 ;106: 	// время


                     307 ;107: 	if (time)


                     308 

00000168 e35a0000    309 	cmp	r10,0

                     310 ;108: 	{


                     311 

                     312 ;109: 		*time = module->DateTime;


                     313 

0000016c 1590002c    314 	ldrne	r0,[r0,44]

00000170 158a0000    315 	strne	r0,[r10]

                     316 ;110: 	}


                     317 ;111:     return TRUE;


                     318 

00000174 e3a00001    319 	mov	r0,1

                     320 .L148:

00000178 e8bd8cf0    321 	ldmfd	[sp]!,{r4-r7,r10-fp,pc}

                     322 	.endf	loadRomModule

                     323 	.align	4

                     324 ;module	r0	local

                     325 

                     326 ;signature	r5	param

                     327 ;pRomModule	fp	param


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aas1.s
                     328 ;pRomModuleData	r6	param

                     329 ;pRomModuleDataSize	r7	param

                     330 ;time	r10	param

                     331 

                     332 	.data

                     333 	.text

                     334 

                     335 ;112: }


                     336 

                     337 ;113: 


                     338 ;114: uint8_t* loadIedModel(int* pSize )


                     339 	.align	4

                     340 	.align	4

                     341 loadIedModel::

0000017c e92d4010    342 	stmfd	[sp]!,{r4,lr}

                     343 ;115: {


                     344 

00000180 e24dd008    345 	sub	sp,sp,8

00000184 e1a04000    346 	mov	r4,r0

00000188 e3a01000    347 	mov	r1,0

0000018c e1a00001    348 	mov	r0,r1

00000190 e88d0003    349 	stmea	[sp],{r0-r1}

                     350 ;116: 	


                     351 ;117: 	uint8_t *modelPtr = NULL;


                     352 

                     353 ;118: 	


                     354 ;119: 	// Если не удалось загрузить загрузить модель, изготовленную из CID-файла, 


                     355 ;120: 	// загружается модель по-умолчанию


                     356 ;121: 	loadRomModule('IED1',NULL,&modelPtr,(size_t*)pSize,NULL) || 


                     357 

00000194 e1a03004    358 	mov	r3,r4

00000198 e59f0350*   359 	ldr	r0,.L373

0000019c e28d2004    360 	add	r2,sp,4

000001a0 ebffffcf*   361 	bl	loadRomModule

000001a4 e3500000    362 	cmp	r0,0

000001a8 1a000005    363 	bne	.L302

000001ac e3a01000    364 	mov	r1,0

000001b0 e58d1000    365 	str	r1,[sp]

000001b4 e1a03004    366 	mov	r3,r4

000001b8 e59f0334*   367 	ldr	r0,.L374

000001bc e28d2004    368 	add	r2,sp,4

000001c0 ebffffc7*   369 	bl	loadRomModule

                     370 .L302:

                     371 ;122: 		loadRomModule('IED0',NULL,&modelPtr,(size_t*)pSize,NULL);


                     372 ;123: 	


                     373 ;124:     return modelPtr;


                     374 

000001c4 e59d0004    375 	ldr	r0,[sp,4]

000001c8 e28dd008    376 	add	sp,sp,8

000001cc e8bd8010    377 	ldmfd	[sp]!,{r4,pc}

                     378 	.endf	loadIedModel

                     379 	.align	4

                     380 ;modelPtr	[sp,4]	local

                     381 

                     382 ;pSize	r4	param

                     383 

                     384 	.section ".bss","awb"

                     385 .L362:

                     386 	.data

                     387 	.text

                     388 


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aas1.s
                     389 ;125: }


                     390 

                     391 ;126: 


                     392 ;127: int getFloatValue(short offset)


                     393 	.align	4

                     394 	.align	4

                     395 getFloatValue::

000001d0 e92d4010    396 	stmfd	[sp]!,{r4,lr}

                     397 ;128: {


                     398 

                     399 ;129:     long value;


                     400 ;130:     bool result = pwlGetAnalogValue(pwlHandle, offset, &value);


                     401 

000001d4 e24dd004    402 	sub	sp,sp,4

000001d8 e59fc30c*   403 	ldr	r12,.L298

000001dc e1a0200d    404 	mov	r2,sp

000001e0 e1a01800    405 	mov	r1,r0 lsl 16

000001e4 e1a01821    406 	mov	r1,r1 lsr 16

000001e8 e89c0011    407 	ldmfd	[r12],{r0,r4}

000001ec e1a0e00f    408 	mov	lr,pc

000001f0 e12fff14*   409 	bx	r4

                     410 ;131:     if( !result  )


                     411 

000001f4 e3500000    412 	cmp	r0,0

                     413 ;134:     }


                     414 ;135:     else


                     415 ;136:     {


                     416 

                     417 ;137:         return value;


                     418 

000001f8 159d0000    419 	ldrne	r0,[sp]

                     420 ;132:     {


                     421 

                     422 ;133:         return 0x7FFFFFFF;


                     423 

000001fc 03e00480    424 	mvneq	r0,1<<31

00000200 e28dd004    425 	add	sp,sp,4

00000204 e8bd8010    426 	ldmfd	[sp]!,{r4,pc}

                     427 	.endf	getFloatValue

                     428 	.align	4

                     429 ;value	[sp]	local

                     430 

                     431 ;offset	r0	param

                     432 

                     433 	.data

                     434 	.text

                     435 

                     436 ;138:     }


                     437 ;139: }


                     438 

                     439 ;140: 


                     440 ;141: void writeTele(uint32_t offset)


                     441 	.align	4

                     442 	.align	4

                     443 writeTele::

00000208 e92d4010    444 	stmfd	[sp]!,{r4,lr}

0000020c e59fc2d8*   445 	ldr	r12,.L298

                     446 ;142: {


                     447 

                     448 ;143:     pwinLibIf.sendTele(pwlHandle, offset);


                     449 


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aas1.s
00000210 e1a01800    450 	mov	r1,r0 lsl 16

00000214 e59c404c    451 	ldr	r4,[r12,76]

00000218 e59c0000    452 	ldr	r0,[r12]

0000021c e1a01821    453 	mov	r1,r1 lsr 16

00000220 e1a0e00f    454 	mov	lr,pc

00000224 e12fff14*   455 	bx	r4

00000228 e8bd8010    456 	ldmfd	[sp]!,{r4,pc}

                     457 	.endf	writeTele

                     458 	.align	4

                     459 

                     460 ;offset	r0	param

                     461 

                     462 	.data

                     463 	.text

                     464 

                     465 ;144: }


                     466 

                     467 ;145: 


                     468 ;146: //***************************************************************


                     469 ;147: // Этим функциям здесь не место, их нужно перенести в dataslice.c


                     470 ;148: // Оставлены только для проверки.


                     471 ;149: #include "../../../../DataSlice/DataSliceClient/datasliceif.h"


                     472 ;150: 


                     473 ;151: int getFloatSett(unsigned short offset)


                     474 	.align	4

                     475 	.align	4

                     476 getFloatSett::

0000022c e92d4000    477 	stmfd	[sp]!,{lr}

00000230 e59fc2c0*   478 	ldr	r12,.L514

00000234 e24dd004    479 	sub	sp,sp,4

00000238 e59c1000    480 	ldr	r1,[r12]

0000023c e591c014    481 	ldr	r12,[r1,20]

00000240 e1a0100d    482 	mov	r1,sp

00000244 e1a0e00f    483 	mov	lr,pc

00000248 e12fff1c*   484 	bx	r12

                     485 ;152: {


                     486 

                     487 ;153: 	extern DataSliceSetts* settsDataSlice;


                     488 ;154: 	int32_t result;


                     489 ;155: 	if (!settsDataSlice->getWordValue(offset, &result))


                     490 

0000024c e3500000    491 	cmp	r0,0

                     492 ;158: 	}


                     493 ;159: 	return result;


                     494 

00000250 159d0000    495 	ldrne	r0,[sp]

                     496 ;156: 	{


                     497 

                     498 ;157: 		return 0;


                     499 

00000254 e28dd004    500 	add	sp,sp,4

00000258 e8bd8000    501 	ldmfd	[sp]!,{pc}

                     502 	.endf	getFloatSett

                     503 	.align	4

                     504 ;result	[sp]	local

                     505 

                     506 ;offset	none	param

                     507 

                     508 	.section ".bss","awb"

                     509 .L501:

                     510 	.data


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aas1.s
                     511 	.text

                     512 

                     513 ;160: }


                     514 

                     515 ;161: 


                     516 ;162: float getRealSett(unsigned short offset)


                     517 	.align	4

                     518 	.align	4

                     519 getRealSett::

0000025c e92d4000    520 	stmfd	[sp]!,{lr}

                     521 ;163: {


                     522 

                     523 ;164: 	


                     524 ;165: 	extern DataSliceSetts* settsDataSlice;


                     525 ;166: 	float result;


                     526 ;167: 	if (!settsDataSlice->getWordValue(offset, (int32_t*)(void*)&result))


                     527 

00000260 e59fc290*   528 	ldr	r12,.L514

00000264 e24dd004    529 	sub	sp,sp,4

00000268 e59c1000    530 	ldr	r1,[r12]

0000026c e591c014    531 	ldr	r12,[r1,20]

00000270 e1a0100d    532 	mov	r1,sp

00000274 e1a0e00f    533 	mov	lr,pc

00000278 e12fff1c*   534 	bx	r12

0000027c e3500000    535 	cmp	r0,0

                     536 ;170: 	}


                     537 ;171: 	return result;


                     538 

00000280 159d0000    539 	ldrne	r0,[sp]

                     540 ;168: 	{


                     541 

                     542 ;169: 		return 0.f;


                     543 

00000284 e28dd004    544 	add	sp,sp,4

00000288 e8bd8000    545 	ldmfd	[sp]!,{pc}

                     546 	.endf	getRealSett

                     547 	.align	4

                     548 ;result	[sp]	local

                     549 

                     550 ;offset	none	param

                     551 

                     552 	.section ".bss","awb"

                     553 .L555:

                     554 	.data

                     555 	.text

                     556 

                     557 ;172: }


                     558 

                     559 ;173: 


                     560 ;174: 


                     561 ;175: int getIntSett(int offset)


                     562 	.align	4

                     563 	.align	4

                     564 getIntSett::

0000028c e92d4000    565 	stmfd	[sp]!,{lr}

00000290 e59fc260*   566 	ldr	r12,.L514

00000294 e24dd004    567 	sub	sp,sp,4

00000298 e59c1000    568 	ldr	r1,[r12]

0000029c e1a00800    569 	mov	r0,r0 lsl 16

000002a0 e591c014    570 	ldr	r12,[r1,20]

000002a4 e1a0100d    571 	mov	r1,sp


                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aas1.s
000002a8 e1a00820    572 	mov	r0,r0 lsr 16

000002ac e1a0e00f    573 	mov	lr,pc

000002b0 e12fff1c*   574 	bx	r12

                     575 ;176: {


                     576 

                     577 ;177: 	extern DataSliceSetts* settsDataSlice;


                     578 ;178: 	int32_t result;


                     579 ;179: 	if (!settsDataSlice->getWordValue(offset, &result))


                     580 

000002b4 e3500000    581 	cmp	r0,0

                     582 ;182: 	}


                     583 ;183: 	return result;


                     584 

000002b8 159d0000    585 	ldrne	r0,[sp]

                     586 ;180: 	{


                     587 

                     588 ;181: 		return 0;


                     589 

000002bc e28dd004    590 	add	sp,sp,4

000002c0 e8bd8000    591 	ldmfd	[sp]!,{pc}

                     592 	.endf	getIntSett

                     593 	.align	4

                     594 ;result	[sp]	local

                     595 

                     596 ;offset	r0	param

                     597 

                     598 	.section ".bss","awb"

                     599 .L613:

                     600 	.data

                     601 	.text

                     602 

                     603 ;184: }


                     604 

                     605 ;185: 


                     606 ;186: //****************************************************************


                     607 ;187: 


                     608 ;188: bool pwaWriteFloatSett(unsigned short offset, int value)


                     609 	.align	4

                     610 	.align	4

                     611 pwaWriteFloatSett::

000002c4 e92d44f0    612 	stmfd	[sp]!,{r4-r7,r10,lr}

000002c8 e59f521c*   613 	ldr	r5,.L298

                     614 ;189: {


                     615 

                     616 ;190:     return pwinLibIf.prepareSetts(pwlHandle, WRITE_TYPE_SETT) 


                     617 

000002cc e1a0a001    618 	mov	r10,r1

000002d0 e2856010    619 	add	r6,r5,16

000002d4 e3a04000    620 	mov	r4,0

000002d8 e595c038    621 	ldr	r12,[r5,56]

000002dc e1a07000    622 	mov	r7,r0

000002e0 e5950000    623 	ldr	r0,[r5]

000002e4 e1a01004    624 	mov	r1,r4

000002e8 e1a0e00f    625 	mov	lr,pc

000002ec e12fff1c*   626 	bx	r12

000002f0 e3500000    627 	cmp	r0,0

000002f4 0a00000e    628 	beq	.L629

000002f8 e595c048    629 	ldr	r12,[r5,72]

000002fc e1a0300a    630 	mov	r3,r10

00000300 e1a02007    631 	mov	r2,r7

00000304 e5950000    632 	ldr	r0,[r5]


                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aas1.s
00000308 e3a01000    633 	mov	r1,0

0000030c e1a0e00f    634 	mov	lr,pc

00000310 e12fff1c*   635 	bx	r12

00000314 e3500000    636 	cmp	r0,0

00000318 0a000005    637 	beq	.L629

0000031c e5950000    638 	ldr	r0,[r5]

00000320 e596c02c    639 	ldr	r12,[r6,44]

00000324 e1a0e00f    640 	mov	lr,pc

00000328 e12fff1c*   641 	bx	r12

0000032c e3500000    642 	cmp	r0,0

00000330 13a04001    643 	movne	r4,1

                     644 .L629:

00000334 e20400ff    645 	and	r0,r4,255

00000338 e8bd84f0    646 	ldmfd	[sp]!,{r4-r7,r10,pc}

                     647 	.endf	pwaWriteFloatSett

                     648 	.align	4

                     649 

                     650 ;offset	r7	param

                     651 ;value	r10	param

                     652 

                     653 	.data

                     654 	.text

                     655 

                     656 ;191:         && pwinLibIf.writeAnalogSett(pwlHandle, 0, offset, value) 


                     657 ;192:         && pwinLibIf.finalizeSetts(pwlHandle);    


                     658 ;193: }


                     659 

                     660 ;194: 


                     661 ;195: bool pwaWriteRealSett(unsigned short offset, float value)


                     662 	.align	4

                     663 	.align	4

                     664 pwaWriteRealSett::

0000033c e92d44f2    665 	stmfd	[sp]!,{r1,r4-r7,r10,lr}

                     666 ;196: {


                     667 

                     668 ;197:     int* pValue = (void*)&value;


                     669 

00000340 e59f61a4*   670 	ldr	r6,.L298

00000344 e1a07000    671 	mov	r7,r0

00000348 e1a0400d    672 	mov	r4,sp

                     673 ;198:     int intValue = *pValue;


                     674 

0000034c e2140003    675 	ands	r0,r4,3

00000350 1a000001    676 	bne	.L770

00000354 e594c000    677 	ldr	r12,[r4]

00000358 ea000005    678 	b	.L771

                     679 .L770:

                     680 

0000035c e0444000    681 	sub	r4,r4,r0

00000360 e1a00180    682 	mov	r0,r0 lsl 3

00000364 e8941020    683 	ldmfd	[r4],{r5,r12}

00000368 e1a05035    684 	mov	r5,r5 lsr r0

0000036c e2600020    685 	rsb	r0,r0,32

00000370 e185c01c    686 	orr	r12,r5,r12 lsl r0

                     687 .L771:

                     688 

00000374 e286a010    689 	add	r10,r6,16

00000378 e3a04000    690 	mov	r4,0

0000037c e1a0500c    691 	mov	r5,r12

                     692 ;199: 


                     693 ;200:     return pwinLibIf.prepareSetts(pwlHandle, WRITE_TYPE_SETT) 



                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aas1.s
                     694 

00000380 e596c038    695 	ldr	r12,[r6,56]

00000384 e5960000    696 	ldr	r0,[r6]

00000388 e1a01004    697 	mov	r1,r4

0000038c e1a0e00f    698 	mov	lr,pc

00000390 e12fff1c*   699 	bx	r12

00000394 e3500000    700 	cmp	r0,0

00000398 0a00000e    701 	beq	.L712

0000039c e596c048    702 	ldr	r12,[r6,72]

000003a0 e1a03005    703 	mov	r3,r5

000003a4 e1a02007    704 	mov	r2,r7

000003a8 e5960000    705 	ldr	r0,[r6]

000003ac e3a01000    706 	mov	r1,0

000003b0 e1a0e00f    707 	mov	lr,pc

000003b4 e12fff1c*   708 	bx	r12

000003b8 e3500000    709 	cmp	r0,0

000003bc 0a000005    710 	beq	.L712

000003c0 e5960000    711 	ldr	r0,[r6]

000003c4 e59ac02c    712 	ldr	r12,[r10,44]

000003c8 e1a0e00f    713 	mov	lr,pc

000003cc e12fff1c*   714 	bx	r12

000003d0 e3500000    715 	cmp	r0,0

000003d4 13a04001    716 	movne	r4,1

                     717 .L712:

000003d8 e20400ff    718 	and	r0,r4,255

000003dc e8bd84f2    719 	ldmfd	[sp]!,{r1,r4-r7,r10,pc}

                     720 	.endf	pwaWriteRealSett

                     721 	.align	4

                     722 ;pValue	r4	local

                     723 ;intValue	r5	local

                     724 

                     725 ;offset	r7	param

                     726 ;value	[sp]	param

                     727 

                     728 	.data

                     729 	.text

                     730 

                     731 ;201:         && pwinLibIf.writeAnalogSett(pwlHandle, 0, offset, intValue) 


                     732 ;202:         && pwinLibIf.finalizeSetts(pwlHandle);    


                     733 ;203: }


                     734 

                     735 ;204: 


                     736 ;205: 


                     737 ;206: bool pwaWriteIntSett(unsigned short offset, int value)


                     738 	.align	4

                     739 	.align	4

                     740 pwaWriteIntSett::

000003e0 e92d44f0    741 	stmfd	[sp]!,{r4-r7,r10,lr}

000003e4 e59f5100*   742 	ldr	r5,.L298

                     743 ;207: {        


                     744 

                     745 ;208:     return pwinLibIf.prepareSetts(pwlHandle, WRITE_TYPE_SETT) 


                     746 

000003e8 e1a0a001    747 	mov	r10,r1

000003ec e2856010    748 	add	r6,r5,16

000003f0 e3a04000    749 	mov	r4,0

000003f4 e595c038    750 	ldr	r12,[r5,56]

000003f8 e1a07000    751 	mov	r7,r0

000003fc e5950000    752 	ldr	r0,[r5]

00000400 e1a01004    753 	mov	r1,r4

00000404 e1a0e00f    754 	mov	lr,pc


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aas1.s
00000408 e12fff1c*   755 	bx	r12

0000040c e3500000    756 	cmp	r0,0

00000410 0a00000e    757 	beq	.L794

00000414 e595c044    758 	ldr	r12,[r5,68]

00000418 e1a0300a    759 	mov	r3,r10

0000041c e1a02007    760 	mov	r2,r7

00000420 e5950000    761 	ldr	r0,[r5]

00000424 e3a01000    762 	mov	r1,0

00000428 e1a0e00f    763 	mov	lr,pc

0000042c e12fff1c*   764 	bx	r12

00000430 e3500000    765 	cmp	r0,0

00000434 0a000005    766 	beq	.L794

00000438 e5950000    767 	ldr	r0,[r5]

0000043c e596c02c    768 	ldr	r12,[r6,44]

00000440 e1a0e00f    769 	mov	lr,pc

00000444 e12fff1c*   770 	bx	r12

00000448 e3500000    771 	cmp	r0,0

0000044c 13a04001    772 	movne	r4,1

                     773 .L794:

00000450 e20400ff    774 	and	r0,r4,255

00000454 e8bd84f0    775 	ldmfd	[sp]!,{r4-r7,r10,pc}

                     776 	.endf	pwaWriteIntSett

                     777 	.align	4

                     778 

                     779 ;offset	r7	param

                     780 ;value	r10	param

                     781 

                     782 	.data

                     783 	.text

                     784 

                     785 ;209:         && pwinLibIf.writeIntSett(pwlHandle, 0, offset, value) 


                     786 ;210:         && pwinLibIf.finalizeSetts(pwlHandle);   


                     787 ;211: }


                     788 

                     789 ;212: 


                     790 ;213: int pwaOscFindFirst(unsigned int oscNum, PWFileInfo *fileInfo)


                     791 	.align	4

                     792 	.align	4

                     793 pwaOscFindFirst::

00000458 e92d4010    794 	stmfd	[sp]!,{r4,lr}

                     795 ;214: {


                     796 

                     797 ;215:    return pwinLibIf.oscFindFirst(pwlHandle,oscNum,fileInfo);


                     798 

0000045c e59fc088*   799 	ldr	r12,.L298

00000460 e1a02001    800 	mov	r2,r1

00000464 e1a01000    801 	mov	r1,r0

00000468 e59c4050    802 	ldr	r4,[r12,80]

0000046c e59c0000    803 	ldr	r0,[r12]

00000470 e1a0e00f    804 	mov	lr,pc

00000474 e12fff14*   805 	bx	r4

00000478 e8bd8010    806 	ldmfd	[sp]!,{r4,pc}

                     807 	.endf	pwaOscFindFirst

                     808 	.align	4

                     809 

                     810 ;oscNum	r0	param

                     811 ;fileInfo	r1	param

                     812 

                     813 	.data

                     814 	.text

                     815 


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aas1.s
                     816 ;216: }


                     817 

                     818 ;217: 


                     819 ;218: int pwaOscFindNext(int findResult, PWFileInfo *fileInfo)


                     820 	.align	4

                     821 	.align	4

                     822 pwaOscFindNext::

0000047c e92d4010    823 	stmfd	[sp]!,{r4,lr}

                     824 ;219: {


                     825 

                     826 ;220:     return pwinLibIf.oscFindNext(pwlHandle,findResult,fileInfo);


                     827 

00000480 e59fc064*   828 	ldr	r12,.L298

00000484 e1a02001    829 	mov	r2,r1

00000488 e1a01000    830 	mov	r1,r0

0000048c e59c4054    831 	ldr	r4,[r12,84]

00000490 e59c0000    832 	ldr	r0,[r12]

00000494 e1a0e00f    833 	mov	lr,pc

00000498 e12fff14*   834 	bx	r4

0000049c e8bd8010    835 	ldmfd	[sp]!,{r4,pc}

                     836 	.endf	pwaOscFindNext

                     837 	.align	4

                     838 

                     839 ;findResult	r0	param

                     840 ;fileInfo	r1	param

                     841 

                     842 	.data

                     843 	.text

                     844 

                     845 ;221: }


                     846 

                     847 ;222: void pwaOscFindClose(void)


                     848 	.align	4

                     849 	.align	4

                     850 pwaOscFindClose::

000004a0 e92d4010    851 	stmfd	[sp]!,{r4,lr}

                     852 ;223: {


                     853 

                     854 ;224: 	pwinLibIf.oscFindClose(pwlHandle);


                     855 

000004a4 e59fc040*   856 	ldr	r12,.L298

000004a8 e59c4058    857 	ldr	r4,[r12,88]

000004ac e59c0000    858 	ldr	r0,[r12]

000004b0 e1a0e00f    859 	mov	lr,pc

000004b4 e12fff14*   860 	bx	r4

000004b8 e8bd8010    861 	ldmfd	[sp]!,{r4,pc}

                     862 	.endf	pwaOscFindClose

                     863 	.align	4

                     864 

                     865 	.data

                     866 	.text

                     867 

                     868 ;225: }


                     869 

                     870 ;226: 


                     871 ;227: int pwaOscOscRead(PWFileInfo *fileInfo, unsigned int offset, unsigned char *data, int maxDataSize)


                     872 	.align	4

                     873 	.align	4

                     874 pwaOscOscRead::

000004bc e92d4010    875 	stmfd	[sp]!,{r4,lr}

                     876 ;228: {



                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aas1.s
                     877 

                     878 ;229: 	return pwinLibIf.oscRead(pwlHandle,fileInfo,offset,data,maxDataSize);


                     879 

000004c0 e59fc024*   880 	ldr	r12,.L298

000004c4 e59c405c    881 	ldr	r4,[r12,92]

000004c8 e52d3004    882 	str	r3,[sp,-4]!

000004cc e1a03002    883 	mov	r3,r2

000004d0 e1a02001    884 	mov	r2,r1

000004d4 e1a01000    885 	mov	r1,r0

000004d8 e59c0000    886 	ldr	r0,[r12]

000004dc e1a0e00f    887 	mov	lr,pc

000004e0 e12fff14*   888 	bx	r4

000004e4 e28dd004    889 	add	sp,sp,4

000004e8 e8bd8010    890 	ldmfd	[sp]!,{r4,pc}

                     891 	.endf	pwaOscOscRead

                     892 	.align	4

                     893 

                     894 ;fileInfo	r0	param

                     895 ;offset	r1	param

                     896 ;data	r2	param

                     897 ;maxDataSize	r3	param

                     898 

                     899 	.data

                     900 	.text

                     901 

                     902 ;230: }


                     903 	.align	4

                     904 .L298:

000004ec 00000000*   905 	.data.w	pwlHandle

                     906 	.type	.L298,$object

                     907 	.size	.L298,4

                     908 

                     909 .L373:

000004f0 49454431    910 	.data.w	0x49454431

                     911 	.type	.L373,$object

                     912 	.size	.L373,4

                     913 

                     914 .L374:

000004f4 49454430    915 	.data.w	0x49454430

                     916 	.type	.L374,$object

                     917 	.size	.L374,4

                     918 

                     919 .L514:

000004f8 00000000*   920 	.data.w	settsDataSlice

                     921 	.type	.L514,$object

                     922 	.size	.L514,4

                     923 

                     924 	.align	4

                     925 ;pwlHandle	pwlHandle	static

                     926 ;pwlGetAnalogValue	pwlGetAnalogValue	static

                     927 ;pwlLoadRomFromAddr	pwlLoadRomFromAddr	static

                     928 ;pwlInitLibIf	pwlInitLibIf	static

                     929 ;pwinLibIf	pwinLibIf	static

                     930 ;settsDataSlice	settsDataSlice	import

                     931 

                     932 	.data

                     933 	.ghsnote version,6

                     934 	.ghsnote tools,1

                     935 	.ghsnote options,0

                     936 	.text

                     937 	.align	4


                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aas1.s
                     938 	.section ".bss","awb"

                     939 	.align	4

                     940 	.text

